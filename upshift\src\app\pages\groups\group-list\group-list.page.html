<!-- Exact HTML from Django template with Angular syntax -->
<div class="container">
    <header>
        <div class="logo">
            <img src="assets/images/upshift_icon_mini.svg" alt="Upshift">
            <span>Upshift</span>
        </div>
        <h1>Groups</h1>
    </header>



    <!-- Always show one of these sections -->
    <section class="join-requests" *ngIf="joinRequests && joinRequests.length > 0">
        <div class="card">
            <a [routerLink]="['/group-requests']" class="card-header link">
                <span class="icon">👥</span>
                <h2>Join Requests</h2>
                <span class="badge">{{ joinRequests.length }}</span>
            </a>
        </div>
    </section>



    <section class="group-actions">
        <a [routerLink]="['/create-group']" class="btn primary full-width">+ Create Group</a>

        <div class="join-group-section">
            <h3>Join a Group</h3>
            <p>Enter an invitation code to join a group:</p>
            <form (ngSubmit)="joinGroupByCode()" class="join-group-form">
                <input type="text" [(ngModel)]="invitationCode" name="invitation_code" placeholder="Enter invitation code" required>
                <button type="submit" class="btn secondary">Join Group</button>
            </form>
        </div>
    </section>

    <section class="your-groups">
        <div class="section-header">
            <h2>Your Groups</h2>
            <button (click)="goToLeaderboard()" id="leaderboard-btn" class="leaderboard-btn">Leaderboard</button>
        </div>
        <div *ngIf="groups && groups.length > 0; else noGroups" class="group-list">
            <div *ngFor="let group of groups" class="group-card">
                <div class="group-icon">{{ group.emoji }}</div>
                <div class="group-info">
                    <h3>{{ group.name }}</h3>
                    <a [routerLink]="['/groups', group.id]" class="group-link">Show all quests</a>
                </div>
            </div>
        </div>
        <ng-template #noGroups>
            <p>No groups yet.</p>
        </ng-template>
    </section>
</div>
