import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormGroup, FormBuilder, Validators } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { HttpClient } from '@angular/common/http';
import { DailySideQuestPool } from '../../models/sidequest.model';
import { GroupSideQuestPool } from '../../models/group-sidequest.model';
import { SupabaseService } from '../../services/supabase.service';
import { AdminService } from '../../services/admin.service';


@Component({
  selector: 'app-import-sidequests',
  templateUrl: './import-sidequests.page.html',
  styleUrls: ['./import-sidequests.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, ReactiveFormsModule]
})
export class ImportSideQuestsPage implements OnInit {
  importStatus: string = '';
  isImporting: boolean = false;
  isAdmin: boolean = false;

  // User side quests
  existingSideQuests: DailySideQuestPool[] = [];
  jsonSideQuests: DailySideQuestPool[] = [];
  showJsonImport: boolean = false;
  showAddForm: boolean = false;

  // Group side quests
  existingGroupSideQuests: GroupSideQuestPool[] = [];
  jsonGroupSideQuests: GroupSideQuestPool[] = [];
  showGroupJsonImport: boolean = false;
  showGroupAddForm: boolean = false;
  groupSideQuestForm: FormGroup;

  // Tab control
  activeTab: string = 'user'; // 'user' or 'group'

  // Form for adding a new sidequest
  sideQuestForm: FormGroup;

  // Categories and goal units for dropdown
  categories: string[] = ['money', 'health', 'strength', 'knowledge'];
  goalUnits: string[] = ['count', 'steps', 'm', 'km', 'sec', 'min', 'hr', 'Cal', 'g', 'mg', 'l', 'pages', 'books', '%', '€', '$', '£'];

  // Use inject instead of constructor injection
  private http = inject(HttpClient);
  private supabaseService = inject(SupabaseService);
  private adminService = inject(AdminService);
  private fb = inject(FormBuilder);

  constructor() {
    // Initialize the user side quest form
    this.sideQuestForm = this.fb.group({
      name: ['', [Validators.required]],
      description: ['', [Validators.required]],
      goal_value: [1, [Validators.required, Validators.min(1)]],
      category: ['health', [Validators.required]],
      goal_unit: ['count', [Validators.required]],
      emoji: ['🎯', [Validators.required]]
    });

    // Initialize the group side quest form
    this.groupSideQuestForm = this.fb.group({
      name: ['', [Validators.required]],
      description: ['', [Validators.required]],
      goal_value: [1, [Validators.required, Validators.min(1)]],
      category: ['health', [Validators.required]],
      goal_unit: ['count', [Validators.required]],
      emoji: ['🎯', [Validators.required]]
    });
  }

  ngOnInit() {
    // Check if user is admin
    this.adminService.isAdmin().subscribe(isAdmin => {
      this.isAdmin = isAdmin;
      if (isAdmin) {
        // Fetch existing side quests
        this.fetchExistingSideQuests();
        this.fetchExistingGroupSideQuests();
      } else {
        this.importStatus = 'You need admin privileges to manage side quests.';
      }
    });

    // Initialize forms with default values
    this.resetForm();
    this.resetGroupForm();
  }

  // Reset user side quest form to default values
  resetForm() {
    this.sideQuestForm.reset({
      name: '',
      description: '',
      goal_value: 1,
      category: 'health',
      goal_unit: 'count',
      emoji: '🎯'
    });
  }

  // Reset group side quest form to default values
  resetGroupForm() {
    this.groupSideQuestForm.reset({
      name: '',
      description: '',
      goal_value: 1,
      category: 'health',
      goal_unit: 'count',
      emoji: '🎯'
    });
  }

  // Switch between user and group tabs
  switchTab(tab: string) {
    this.activeTab = tab;
    this.importStatus = '';
  }

  openSupabaseConsole() {
    window.open('https://app.supabase.com/', '_blank');
  }

  async fetchExistingSideQuests() {
    this.importStatus = 'Fetching existing side quests from Supabase...';

    try {
      const { data, error } = await this.supabaseService.getClient()
        .from('daily_sidequest_pool')
        .select('*')
        .order('id', { ascending: true });

      if (error) {
        console.error('Error fetching existing side quests:', error);
        this.importStatus = `Error fetching existing side quests: ${error.message}`;
        return;
      }

      if (data && data.length > 0) {
        this.existingSideQuests = data;
        console.log(`Found ${data.length} existing side quests in Supabase`);
        this.importStatus = `Found ${data.length} existing side quests in Supabase.`;
      } else {
        console.log('No existing side quests found in Supabase');
        this.importStatus = 'No existing side quests found in Supabase.';
      }
    } catch (error: any) {
      console.error('Error fetching existing side quests:', error);
      this.importStatus = `Error fetching existing side quests: ${error.message}`;
    }
  }

  toggleJsonImport() {
    this.showJsonImport = !this.showJsonImport;
    if (this.showJsonImport) {
      this.fetchJsonSideQuests();
    }
  }

  toggleAddForm() {
    this.showAddForm = !this.showAddForm;
  }

  fetchJsonSideQuests() {
    this.importStatus = 'Fetching side quests from JSON...';

    this.http.get<DailySideQuestPool[]>('assets/data/sidequest-pool.json').subscribe({
      next: (data) => {
        if (!data || data.length === 0) {
          this.importStatus = 'No side quests found in JSON file';
          return;
        }

        this.jsonSideQuests = data;
        this.importStatus = `Found ${data.length} side quests in JSON file. Ready to import.`;
      },
      error: (error) => {
        this.importStatus = `Error fetching side quests JSON: ${error.message}`;
      }
    });
  }

  async importJsonSideQuests() {
    if (this.jsonSideQuests.length === 0) {
      this.importStatus = 'No side quests to import from JSON';
      return;
    }

    this.isImporting = true;
    this.importStatus = 'Importing side quests from JSON...';

    try {
      // Prepare side quests with active flag and remove any custom IDs
      const sideQuestsToImport = this.jsonSideQuests.map(quest => {
        // Create a new object without the id field
        const { id, ...questWithoutId } = quest;
        return {
          ...questWithoutId,
          active: true
        };
      });

      console.log('Side quests prepared for import:', sideQuestsToImport);

      // Insert into Supabase
      const { error } = await this.supabaseService.getClient()
        .from('daily_sidequest_pool')
        .insert(sideQuestsToImport);

      if (error) {
        console.error('Error importing side quests:', error);
        this.importStatus = `Error importing side quests: ${error.message}`;
        this.isImporting = false;
        return;
      }

      console.log('Side quests imported successfully');
      this.importStatus = 'Side quests imported successfully';
      this.isImporting = false;

      // Refresh the list of existing side quests
      this.fetchExistingSideQuests();
    } catch (error: any) {
      console.error('Error importing side quests:', error);
      this.importStatus = `Error importing side quests: ${error.message}`;
      this.isImporting = false;
    }
  }

  async addSideQuest() {
    if (!this.sideQuestForm.valid) {
      this.importStatus = 'Please fill out all required fields correctly';
      return;
    }

    this.isImporting = true;
    this.importStatus = 'Adding new side quest...';

    try {
      const newSideQuest = {
        ...this.sideQuestForm.value,
        active: true
      };

      console.log('New side quest to add:', newSideQuest);

      // Insert into Supabase
      const { error } = await this.supabaseService.getClient()
        .from('daily_sidequest_pool')
        .insert([newSideQuest]);

      if (error) {
        console.error('Error adding side quest:', error);
        this.importStatus = `Error adding side quest: ${error.message}`;
        this.isImporting = false;
        return;
      }

      console.log('Side quest added successfully');
      this.importStatus = 'Side quest added successfully';

      // Reset the form
      this.resetForm();

      // Refresh the list of existing side quests
      this.fetchExistingSideQuests();
    } catch (error: any) {
      console.error('Error adding side quest:', error);
      this.importStatus = `Error adding side quest: ${error.message}`;
    } finally {
      this.isImporting = false;
    }
  }

  async toggleSideQuestActive(quest: DailySideQuestPool) {
    try {
      const { error } = await this.supabaseService.getClient()
        .from('daily_sidequest_pool')
        .update({ active: !quest.active })
        .eq('id', quest.id);

      if (error) {
        console.error('Error updating side quest:', error);
        this.importStatus = `Error updating side quest: ${error.message}`;
        return;
      }

      console.log('Side quest updated successfully');
      this.importStatus = 'Side quest updated successfully';

      // Update the local state
      quest.active = !quest.active;
    } catch (error: any) {
      console.error('Error updating side quest:', error);
      this.importStatus = `Error updating side quest: ${error.message}`;
    }
  }

  async deleteSideQuest(questId: string | undefined) {
    if (!questId) {
      this.importStatus = 'Cannot delete quest: Invalid quest ID';
      return;
    }

    if (!confirm('Are you sure you want to delete this side quest?')) {
      return;
    }

    try {
      const { error } = await this.supabaseService.getClient()
        .from('daily_sidequest_pool')
        .delete()
        .eq('id', questId);

      if (error) {
        console.error('Error deleting side quest:', error);
        this.importStatus = `Error deleting side quest: ${error.message}`;
        return;
      }

      console.log('Side quest deleted successfully');
      this.importStatus = 'Side quest deleted successfully';

      // Refresh the list of existing side quests
      this.fetchExistingSideQuests();
    } catch (error: any) {
      console.error('Error deleting side quest:', error);
      this.importStatus = `Error deleting side quest: ${error.message}`;
    }
  }

  // Group sidequest methods

  // Fetch existing group side quests from Supabase
  async fetchExistingGroupSideQuests() {
    this.importStatus = 'Fetching existing group side quests from Supabase...';

    try {
      const { data, error } = await this.supabaseService.getClient()
        .from('group_sidequest_pool')
        .select('*')
        .order('id', { ascending: true });

      if (error) {
        console.error('Error fetching existing group side quests:', error);
        this.importStatus = `Error fetching existing group side quests: ${error.message}`;
        return;
      }

      if (data && data.length > 0) {
        this.existingGroupSideQuests = data;
        console.log(`Found ${data.length} existing group side quests in Supabase`);
        this.importStatus = `Found ${data.length} existing group side quests in Supabase.`;
      } else {
        console.log('No existing group side quests found in Supabase');
        this.importStatus = 'No existing group side quests found in Supabase.';
      }
    } catch (error: any) {
      console.error('Error fetching existing group side quests:', error);
      this.importStatus = `Error fetching existing group side quests: ${error.message}`;
    }
  }

  // Toggle group JSON import section
  toggleGroupJsonImport() {
    this.showGroupJsonImport = !this.showGroupJsonImport;
    if (this.showGroupJsonImport) {
      this.fetchJsonGroupSideQuests();
    }
  }

  // Toggle group add form
  toggleGroupAddForm() {
    this.showGroupAddForm = !this.showGroupAddForm;
  }

  // Fetch group side quests from JSON file
  fetchJsonGroupSideQuests() {
    this.importStatus = 'Fetching group side quests from JSON...';

    this.http.get<GroupSideQuestPool[]>('assets/data/group-sidequest-pool.json').subscribe({
      next: (data) => {
        if (!data || data.length === 0) {
          this.importStatus = 'No group side quests found in JSON file';
          return;
        }

        this.jsonGroupSideQuests = data;
        this.importStatus = `Found ${data.length} group side quests in JSON file. Ready to import.`;
      },
      error: (error) => {
        this.importStatus = `Error fetching group side quests JSON: ${error.message}`;
      }
    });
  }

  // Import group side quests from JSON to Supabase
  async importJsonGroupSideQuests() {
    if (this.jsonGroupSideQuests.length === 0) {
      this.importStatus = 'No group side quests to import from JSON';
      return;
    }

    this.isImporting = true;
    this.importStatus = 'Importing group side quests from JSON...';

    try {
      // Prepare group side quests with active flag and remove any custom IDs
      const groupSideQuestsToImport = this.jsonGroupSideQuests.map(quest => {
        // Create a new object without the id field
        const { id, ...questWithoutId } = quest;
        return {
          ...questWithoutId,
          active: true
        };
      });

      console.log('Group side quests prepared for import:', groupSideQuestsToImport);

      // Insert into Supabase
      const { error } = await this.supabaseService.getClient()
        .from('group_sidequest_pool')
        .insert(groupSideQuestsToImport);

      if (error) {
        console.error('Error importing group side quests:', error);
        this.importStatus = `Error importing group side quests: ${error.message}`;
        this.isImporting = false;
        return;
      }

      console.log('Group side quests imported successfully');
      this.importStatus = 'Group side quests imported successfully';
      this.isImporting = false;

      // Refresh the list of existing group side quests
      this.fetchExistingGroupSideQuests();
    } catch (error: any) {
      console.error('Error importing group side quests:', error);
      this.importStatus = `Error importing group side quests: ${error.message}`;
      this.isImporting = false;
    }
  }

  // Add a new group side quest
  async addGroupSideQuest() {
    if (!this.groupSideQuestForm.valid) {
      this.importStatus = 'Please fill out all required fields correctly';
      return;
    }

    this.isImporting = true;
    this.importStatus = 'Adding new group side quest...';

    try {
      const newGroupSideQuest = {
        ...this.groupSideQuestForm.value,
        active: true
      };

      console.log('New group side quest to add:', newGroupSideQuest);

      // Insert into Supabase
      const { error } = await this.supabaseService.getClient()
        .from('group_sidequest_pool')
        .insert([newGroupSideQuest]);

      if (error) {
        console.error('Error adding group side quest:', error);
        this.importStatus = `Error adding group side quest: ${error.message}`;
        this.isImporting = false;
        return;
      }

      console.log('Group side quest added successfully');
      this.importStatus = 'Group side quest added successfully';

      // Reset the form
      this.resetGroupForm();

      // Refresh the list of existing group side quests
      this.fetchExistingGroupSideQuests();
    } catch (error: any) {
      console.error('Error adding group side quest:', error);
      this.importStatus = `Error adding group side quest: ${error.message}`;
    } finally {
      this.isImporting = false;
    }
  }

  // Toggle active status of a group side quest
  async toggleGroupSideQuestActive(quest: GroupSideQuestPool) {
    try {
      const { error } = await this.supabaseService.getClient()
        .from('group_sidequest_pool')
        .update({ active: !quest.active })
        .eq('id', quest.id);

      if (error) {
        console.error('Error updating group side quest:', error);
        this.importStatus = `Error updating group side quest: ${error.message}`;
        return;
      }

      console.log('Group side quest updated successfully');
      this.importStatus = 'Group side quest updated successfully';

      // Update the local state
      quest.active = !quest.active;
    } catch (error: any) {
      console.error('Error updating group side quest:', error);
      this.importStatus = `Error updating group side quest: ${error.message}`;
    }
  }

  // Delete a group side quest
  async deleteGroupSideQuest(questId: string) {
    if (!questId) {
      this.importStatus = 'Cannot delete quest: Invalid quest ID';
      return;
    }

    if (!confirm('Are you sure you want to delete this group side quest?')) {
      return;
    }

    try {
      const { error } = await this.supabaseService.getClient()
        .from('group_sidequest_pool')
        .delete()
        .eq('id', questId);

      if (error) {
        console.error('Error deleting group side quest:', error);
        this.importStatus = `Error deleting group side quest: ${error.message}`;
        return;
      }

      console.log('Group side quest deleted successfully');
      this.importStatus = 'Group side quest deleted successfully';

      // Refresh the list of existing group side quests
      this.fetchExistingGroupSideQuests();
    } catch (error: any) {
      console.error('Error deleting group side quest:', error);
      this.importStatus = `Error deleting group side quest: ${error.message}`;
    }
  }
}
