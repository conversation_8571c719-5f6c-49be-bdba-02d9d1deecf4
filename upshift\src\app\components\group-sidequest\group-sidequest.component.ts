﻿import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { GroupSideQuestService } from '../../services/group-sidequest.service';
import { GroupDailyQuest, GroupSideQuestMemberStatus } from '../../models/group-sidequest.model';
import { take } from 'rxjs/operators';
import { from } from 'rxjs';
import { ToastController } from '@ionic/angular';
import { SupabaseService } from '../../services/supabase.service';

@Component({
  selector: 'app-group-sidequest',
  templateUrl: './group-sidequest.component.html',
  styleUrls: ['./group-sidequest.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule]
})
export class GroupSideQuestComponent implements OnInit, OnChanges {
  @Input() groupId: string = '';
  @Input() userId: string = '';
  @Input() joinedDate: string = '';
  @Input() isAdmin: boolean = false;
  @Input() enableSidequests: boolean = true;
  @Input() selectedDate: string = '';

  dailyQuest: GroupDailyQuest | null = null;
  memberStatus: GroupSideQuestMemberStatus | null = null;
  memberStatuses: GroupSideQuestMemberStatus[] = [];
  isBeforeJoinDate: boolean = false;
  isLoading: boolean = true;
  togglingSideQuest: boolean = false;
  isTodaySelected: boolean = false;

  constructor(
    private groupSideQuestService: GroupSideQuestService,
    private toastController: ToastController,
    private supabaseService: SupabaseService
  ) { }

  ngOnInit() {
    if (this.enableSidequests) {
      this.checkAndResetMemberStatuses();
    } else {
      this.isLoading = false;
    }
  }

  checkAndResetMemberStatuses() {
    this.groupSideQuestService.ensureGroupHasDailySideQuest(this.groupId).pipe(
      take(1)
    ).subscribe({
      next: (sideQuest) => {
        if (!sideQuest) {
          this.isLoading = false;
          return;
        }

        const today = new Date();
        const todayStr = today.toISOString().split('T')[0]; 

        if (sideQuest.date_assigned === todayStr) {
          this.loadSideQuest();
          return;
        }


        this.groupSideQuestService.getAllMemberStatuses(sideQuest.id).pipe(
          take(1)
        ).subscribe({
          next: (statuses) => {
            const needsReset = statuses.some(s => s.last_updated !== todayStr);

            if (needsReset) {

              from(
                this.supabaseService.getClient()
                  .rpc('reset_group_sidequest_member_statuses', { sidequest_id: sideQuest.id })
              ).subscribe({
                next: () => {
                  this.loadSideQuest();
                },
                error: (error) => {
                  this.loadSideQuest();
                }
              });
            } else {
              this.loadSideQuest();
            }
          },
          error: (error) => {
            this.loadSideQuest();
          }
        });
      },
      error: (error) => {
        this.isLoading = false;
      }
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['groupId'] || changes['userId'] || changes['enableSidequests'] || changes['selectedDate']) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const selectedDate = this.selectedDate ? new Date(this.selectedDate) : new Date();
      selectedDate.setHours(0, 0, 0, 0);
      this.isTodaySelected = selectedDate.getTime() === today.getTime();


      if (!this.enableSidequests) {
        this.dailyQuest = null;
        this.memberStatus = null;
        this.isLoading = false;
        return;
      }

      this.loadSideQuest();
    }
  }

  loadSideQuest() {

    if (!this.groupId || !this.userId) {
      this.isLoading = false;
      return;
    }

    if (!this.enableSidequests) {
      this.dailyQuest = null;
      this.memberStatus = null;
      this.isLoading = false;
      return;
    }

    this.isLoading = true;

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const joinedDate = new Date(this.joinedDate);
    joinedDate.setHours(0, 0, 0, 0);
    const eligibleDate = new Date(today);
    eligibleDate.setDate(today.getDate() - 1);
    this.isBeforeJoinDate = joinedDate > eligibleDate;


    this.groupSideQuestService.ensureGroupHasDailySideQuest(this.groupId).pipe(
      take(1)
    ).subscribe({
      next: (sideQuest) => {

        if (sideQuest) {
          this.dailyQuest = {
            id: sideQuest.id,
            group_id: sideQuest.group_id,
            streak: sideQuest.streak,
            completed: sideQuest.completed,
            value_achieved: sideQuest.value_achieved,
            date_assigned: sideQuest.date_assigned,
            last_completed_date: sideQuest.last_completed_date,
            category: sideQuest.category,
            current_quest: {
              id: sideQuest.current_quest?.id || '',
              name: sideQuest.current_quest?.name || '',
              description: sideQuest.current_quest?.description,
              goal_value: sideQuest.current_quest?.goal_value || 0,
              goal_unit: sideQuest.current_quest?.goal_unit || 'count',
              emoji: sideQuest.current_quest?.emoji || '🎯'
            },
            eligible_members_count: 0,
            completed_members_count: 0
          };
        } else {
          this.dailyQuest = null;
        }


        if (sideQuest) {
          this.loadMemberStatuses(sideQuest.id);

          this.loadMemberStatus(sideQuest.id, this.userId);
        }

        this.isLoading = false;
      },
      error: (error) => {
        this.isLoading = false;
      }
    });
  }

  loadMemberStatuses(sideQuestId: string) {
    this.groupSideQuestService.getAllMemberStatuses(sideQuestId).pipe(
      take(1)
    ).subscribe({
      next: (statuses) => {
        this.memberStatuses = statuses;

        if (this.dailyQuest) {
          const today = new Date();
          today.setHours(0, 0, 0, 0); 

          from(
            this.supabaseService.getClient()
              .from('group_members')
              .select('user_id, joined_date, nickname')
              .eq('group_id', this.groupId)
          ).subscribe(response => {
            if (response.error) {
              return;
            }

            const eligibleMembers = response.data.filter(member => {
              const joinedDate = new Date(member.joined_date);
              return joinedDate < today; 
            });

            const eligibleMemberIds = eligibleMembers.map(m => m.user_id);

            this.dailyQuest!.eligible_members_count = eligibleMembers.length;

            const todayStr = new Date().toISOString().split('T')[0];

            this.dailyQuest!.completed_members_count = statuses.filter(s =>
              s.completed && 
              eligibleMemberIds.includes(s.member_id) && 
              s.last_updated === todayStr 
            ).length;



            statuses.forEach(s => {
              const member = response.data.find(m => m.user_id === s.member_id);
              const isEligible = eligibleMemberIds.includes(s.member_id);
              const todayStr = new Date().toISOString().split('T')[0];
              const isUpdatedToday = s.last_updated === todayStr;
              console.log(`Member ${member?.nickname || s.member_id}:`, {
                member_id: s.member_id,
                completed: s.completed,
                last_updated: s.last_updated,
                is_eligible: isEligible,
                is_updated_today: isUpdatedToday,
                is_counted: s.completed && isEligible && isUpdatedToday
              });
            });
          });
        }
      },
      error: (error) => {
      }
    });
  }

  loadMemberStatus(sideQuestId: string, userId: string) {
    this.groupSideQuestService.getMemberStatus(sideQuestId, userId).pipe(
      take(1)
    ).subscribe({
      next: (status) => {
        if (status) {
          this.memberStatus = status;
        } else {
          this.createMemberStatus(sideQuestId, userId);
        }
      },
      error: (error) => {
      }
    });
  }

  createMemberStatus(sideQuestId: string, userId: string) {
    const today = new Date();
    const dateString = today.toISOString().split('T')[0];

    const newStatus = {
      group_quest_id: sideQuestId,
      member_id: userId,
      completed: false,
      value_achieved: 0,
      last_updated: dateString
    };

    from(
      this.supabaseService.getClient()
        .from('group_sidequest_member_status')
        .insert(newStatus)
        .select()
        .single()
    ).pipe(
      take(1)
    ).subscribe({
      next: (response) => {
        if (response.error) {
          return;
        }

        this.memberStatus = response.data as GroupSideQuestMemberStatus;

        this.loadMemberStatuses(sideQuestId);
      },
      error: (error) => {
      }
    });
  }

  toggleSideQuest() {
    if (!this.groupId || !this.userId || !this.dailyQuest || !this.memberStatus || this.isBeforeJoinDate || this.togglingSideQuest) {
      return;
    }

    this.togglingSideQuest = true;

    const newCompletionStatus = !this.memberStatus.completed;
    const newValueAchieved = newCompletionStatus ? this.dailyQuest.current_quest.goal_value : 0;
    const today = new Date().toISOString().split('T')[0]; 

    console.log('Toggling sidequest completion:', {
      previous_status: {
        completed: this.memberStatus.completed,
        value_achieved: this.memberStatus.value_achieved,
        last_updated: this.memberStatus.last_updated
      },
      new_status: {
        completed: newCompletionStatus,
        value_achieved: newValueAchieved,
        last_updated: today
      }
    });

    if (this.memberStatus) {
      this.memberStatus.completed = newCompletionStatus;
      this.memberStatus.value_achieved = newValueAchieved;
      this.memberStatus.last_updated = today; 
    }

    if (this.dailyQuest) {
      const today = new Date();
      today.setHours(0, 0, 0, 0); 

      from(
        this.supabaseService.getClient()
          .from('group_members')
          .select('joined_date')
          .eq('group_id', this.groupId)
          .eq('user_id', this.userId)
          .single()
      ).subscribe(response => {
        if (response.error) {
          return;
        }

        const joinedDate = new Date(response.data.joined_date);
        const isEligible = joinedDate < today; 

        const todayStr = new Date().toISOString().split('T')[0];

        const wasUpdatedToday = this.memberStatus?.last_updated === todayStr;

        if (isEligible) {
          if (newCompletionStatus) {
            this.dailyQuest!.completed_members_count = (this.dailyQuest!.completed_members_count || 0) + 1;
          } else {
            if (this.dailyQuest!.completed_members_count && this.dailyQuest!.completed_members_count > 0) {
              this.dailyQuest!.completed_members_count--;
            }
          }
        } else {
        }

      });
    }

    this.groupSideQuestService.toggleMemberCompletion(this.memberStatus.id, this.groupId).pipe(
      take(1)
    ).subscribe({
      next: (updatedStatus) => {

        console.log('Status comparison - Local vs Server:', {
          local: {
            completed: this.memberStatus?.completed,
            value_achieved: this.memberStatus?.value_achieved,
            last_updated: this.memberStatus?.last_updated
          },
          server: {
            completed: updatedStatus.completed,
            value_achieved: updatedStatus.value_achieved,
            last_updated: updatedStatus.last_updated
          }
        });

        this.memberStatus = updatedStatus;

        if (this.dailyQuest) {
          const completedCount = this.dailyQuest.completed_members_count || 0;
          const eligibleCount = this.dailyQuest.eligible_members_count || 0;
          const allCompleted = completedCount === eligibleCount && eligibleCount > 0;

          const wasCompleted = this.dailyQuest.completed;


          if (allCompleted && !wasCompleted) {
            this.dailyQuest.streak += 1;
            this.dailyQuest.completed = true;
          } else if (!allCompleted && wasCompleted) {
            this.dailyQuest.streak = Math.max(0, this.dailyQuest.streak - 1);
            this.dailyQuest.completed = false;
          }
        }

        this.togglingSideQuest = false;
      },
      error: (error) => {

        const previousLastUpdated = this.memberStatus?.last_updated || '';

        if (this.memberStatus) {
          this.memberStatus.completed = !newCompletionStatus;
          this.memberStatus.value_achieved = !newCompletionStatus ? this.dailyQuest!.current_quest.goal_value : 0;
          this.memberStatus.last_updated = previousLastUpdated; 
        }

        if (this.dailyQuest) {
          const today = new Date();
          today.setHours(0, 0, 0, 0);

          from(
            this.supabaseService.getClient()
              .from('group_members')
              .select('joined_date')
              .eq('group_id', this.groupId)
              .eq('user_id', this.userId)
              .single()
          ).subscribe(response => {
            if (response.error) return;

            const joinedDate = new Date(response.data.joined_date);
            const isEligible = joinedDate < today;

            const todayStr = new Date().toISOString().split('T')[0];
            const wasUpdatedToday = previousLastUpdated === todayStr;

            if (isEligible && this.dailyQuest) {
              if (!newCompletionStatus && wasUpdatedToday) {
                this.dailyQuest.completed_members_count = (this.dailyQuest.completed_members_count || 0) + 1;
              } else if (newCompletionStatus && !wasUpdatedToday) {
                if (this.dailyQuest.completed_members_count && this.dailyQuest.completed_members_count > 0) {
                  this.dailyQuest.completed_members_count--;
                }
              }
            }
          });
        }

        this.togglingSideQuest = false;
        this.showErrorToast('Error updating side quest. Please try again.');
      }
    });
  }

  async showErrorToast(message: string) {
    const toast = await this.toastController.create({
      message: message,
      duration: 3000,
      position: 'bottom',
      color: 'danger'
    });
    await toast.present();
  }

  getProgressPercentage(): number {
    if (!this.dailyQuest || !this.dailyQuest.eligible_members_count || this.dailyQuest.eligible_members_count === 0) {
      return 0;
    }

    return (this.dailyQuest.completed_members_count || 0) / this.dailyQuest.eligible_members_count * 100;
  }

  getMemberStatusText(): string {
    if (!this.dailyQuest || !this.dailyQuest.eligible_members_count) {
      return 'No members';
    }

    return `${this.dailyQuest.completed_members_count || 0}/${this.dailyQuest.eligible_members_count} completed`;
  }
}
