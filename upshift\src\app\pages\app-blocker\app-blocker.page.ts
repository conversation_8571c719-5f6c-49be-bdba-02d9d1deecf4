﻿import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { NavigationComponent } from '../../components/navigation/navigation.component';

interface BlockedItem {
  id: string;
  name: string;
  type: 'website' | 'app';
  icon: string;
}

interface WeekDay {
  label: string;
  value: string;
  selected: boolean;
}

@Component({
  selector: 'app-app-blocker',
  templateUrl: './app-blocker.page.html',
  styleUrls: ['./app-blocker.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule, NavigationComponent]
})
export class AppBlockerPage implements OnInit {
  blockerEnabled = false;
  strictMode = false;
  blockDuration = 'focus';
  startTime = '09:00';
  endTime = '17:00';
  
  weekDays: WeekDay[] = [
    { label: 'Mon', value: 'Monday', selected: true },
    { label: 'Tue', value: 'Tuesday', selected: true },
    { label: 'Wed', value: 'Wednesday', selected: true },
    { label: 'Thu', value: 'Thursday', selected: true },
    { label: 'Fri', value: 'Friday', selected: true },
    { label: 'Sat', value: 'Saturday', selected: false },
    { label: 'Sun', value: 'Sunday', selected: false }
  ];
  
  blockedItems: BlockedItem[] = [
    { id: '1', name: 'facebook.com', type: 'website', icon: '🌐' },
    { id: '2', name: 'twitter.com', type: 'website', icon: '🌐' },
    { id: '3', name: 'instagram.com', type: 'website', icon: '🌐' },
    { id: '4', name: 'Netflix', type: 'app', icon: '📱' }
  ];
  
  newItem = {
    type: 'website',
    name: ''
  };
  
  extensionConnected = false;

  constructor() {}

  ngOnInit() {
    this.loadSettings();
  }

  loadSettings() {
    const settings = localStorage.getItem('appBlockerSettings');
    if (settings) {
      const parsedSettings = JSON.parse(settings);
      this.blockerEnabled = parsedSettings.blockerEnabled || false;
      this.strictMode = parsedSettings.strictMode || false;
      this.blockDuration = parsedSettings.blockDuration || 'focus';
      this.startTime = parsedSettings.startTime || '09:00';
      this.endTime = parsedSettings.endTime || '17:00';
      this.weekDays = parsedSettings.weekDays || this.weekDays;
      this.blockedItems = parsedSettings.blockedItems || [];
      this.extensionConnected = parsedSettings.extensionConnected || false;
    }
  }

  saveSettings() {
    const settings = {
      blockerEnabled: this.blockerEnabled,
      strictMode: this.strictMode,
      blockDuration: this.blockDuration,
      startTime: this.startTime,
      endTime: this.endTime,
      weekDays: this.weekDays,
      blockedItems: this.blockedItems,
      extensionConnected: this.extensionConnected
    };
    
    localStorage.setItem('appBlockerSettings', JSON.stringify(settings));
    
    alert('Settings saved successfully!');
  }

  addBlockedItem() {
    if (!this.newItem.name.trim()) {
      alert('Please enter a name or URL');
      return;
    }
    
    const id = Math.random().toString(36).substring(2, 15);
    
    const icon = this.newItem.type === 'website' ? '🌐' : '📱';
    
    this.blockedItems.push({
      id,
      name: this.newItem.name.trim(),
      type: this.newItem.type as 'website' | 'app',
      icon
    });
    
    this.newItem = {
      type: 'website',
      name: ''
    };
  }

  removeBlockedItem(item: BlockedItem) {
    this.blockedItems = this.blockedItems.filter(i => i.id !== item.id);
  }

  installExtension() {
    if (this.extensionConnected) {
      alert('This would open the extension settings in a real app');
    } else {
      alert('This would redirect to the extension store in a real app');
      
      setTimeout(() => {
        this.extensionConnected = true;
      }, 1000);
    }
  }
}
