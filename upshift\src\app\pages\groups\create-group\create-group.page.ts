﻿import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule, Router } from '@angular/router';
import { GroupService } from '../../../services/group.service';
import { Group } from '../../../models/group.model';
import { SupabaseService } from '../../../services/supabase.service';
import { HttpClient } from '@angular/common/http';
import { debounceTime, distinctUntilChanged, Subject, Subscription } from 'rxjs';
import { Geolocation } from '@capacitor/geolocation';
import { EmojiInputDirective } from '../../../directives/emoji-input.directive';

@Component({
  selector: 'app-create-group',
  templateUrl: './create-group.page.html',
  styleUrls: ['./create-group.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule, EmojiInputDirective]
})
export class CreateGroupPage implements OnInit {
  userId: string | null = null;

  group: Partial<Group> = {
    name: '',
    emoji: '👥',
    timezone: 'UTC', 
    country: '',
    level: 0,
    strength_xp: 0,
    money_xp: 0,
    health_xp: 0,
    knowledge_xp: 0,
    enable_sidequests: true
  };

  timezones: { value: string, label: string }[] = [
    { value: 'UTC', label: '(UTC) Coordinated Universal Time' },

    { value: 'America/Anchorage', label: '(UTC-8) Anchorage' },
    { value: 'America/Los_Angeles', label: '(UTC-7) Los Angeles' },
    { value: 'America/Phoenix', label: '(UTC-7) Phoenix' },
    { value: 'America/Vancouver', label: '(UTC-7) Vancouver' },
    { value: 'America/Denver', label: '(UTC-6) Denver' },
    { value: 'America/Mexico_City', label: '(UTC-6) Mexico City' },
    { value: 'America/San_Salvador', label: '(UTC-6) San Salvador' },
    { value: 'America/Managua', label: '(UTC-6) Managua' },
    { value: 'America/Tegucigalpa', label: '(UTC-6) Tegucigalpa' },
    { value: 'America/Bogota', label: '(UTC-5) Bogota' },
    { value: 'America/Chicago', label: '(UTC-5) Chicago' },
    { value: 'America/Lima', label: '(UTC-5) Lima' },
    { value: 'America/Panama', label: '(UTC-5) Panama City' },
    { value: 'America/Caracas', label: '(UTC-4) Caracas' },
    { value: 'America/New_York', label: '(UTC-4) New York' },
    { value: 'America/Santiago', label: '(UTC-4) Santiago' },
    { value: 'America/Toronto', label: '(UTC-4) Toronto' },
    { value: 'America/Santo_Domingo', label: '(UTC-4) Santo Domingo' },
    { value: 'America/Port_of_Spain', label: '(UTC-4) Port of Spain' },
    { value: 'America/Argentina/Buenos_Aires', label: '(UTC-3) Buenos Aires' },
    { value: 'America/Sao_Paulo', label: '(UTC-3) Sao Paulo' },
    { value: 'America/Montevideo', label: '(UTC-3) Montevideo' },
    { value: 'America/Asuncion', label: '(UTC-3) Asuncion' },

    { value: 'Atlantic/Azores', label: '(UTC) Azores' },
    { value: 'Atlantic/Reykjavik', label: '(UTC) Reykjavik' },
    { value: 'Africa/Casablanca', label: '(UTC+1) Casablanca' },
    { value: 'Europe/Dublin', label: '(UTC+1) Dublin' },
    { value: 'Africa/Lagos', label: '(UTC+1) Lagos' },
    { value: 'Europe/Lisbon', label: '(UTC+1) Lisbon' },
    { value: 'Europe/London', label: '(UTC+1) London' },
    { value: 'Africa/Tunis', label: '(UTC+1) Tunis' },
    { value: 'Africa/Algiers', label: '(UTC+1) Algiers' },
    { value: 'Europe/Amsterdam', label: '(UTC+2) Amsterdam' },
    { value: 'Europe/Belgrade', label: '(UTC+2) Belgrade' },
    { value: 'Europe/Berlin', label: '(UTC+2) Berlin' },
    { value: 'Europe/Bratislava', label: '(UTC+2) Bratislava' },
    { value: 'Europe/Brussels', label: '(UTC+2) Brussels' },
    { value: 'Europe/Budapest', label: '(UTC+2) Budapest' },
    { value: 'Europe/Copenhagen', label: '(UTC+2) Copenhagen' },
    { value: 'Africa/Cairo', label: '(UTC+2) Cairo' },
    { value: 'Europe/Madrid', label: '(UTC+2) Madrid' },
    { value: 'Europe/Oslo', label: '(UTC+2) Oslo' },
    { value: 'Europe/Paris', label: '(UTC+2) Paris' },
    { value: 'Europe/Prague', label: '(UTC+2) Prague' },
    { value: 'Europe/Rome', label: '(UTC+2) Rome' },
    { value: 'Europe/Stockholm', label: '(UTC+2) Stockholm' },
    { value: 'Europe/Vienna', label: '(UTC+2) Vienna' },
    { value: 'Europe/Warsaw', label: '(UTC+2) Warsaw' },
    { value: 'Europe/Zurich', label: '(UTC+2) Zurich' },
    { value: 'Africa/Nairobi', label: '(UTC+3) Nairobi' },
    { value: 'Europe/Athens', label: '(UTC+3) Athens' },
    { value: 'Asia/Baghdad', label: '(UTC+3) Baghdad' },
    { value: 'Europe/Helsinki', label: '(UTC+3) Helsinki' },
    { value: 'Europe/Istanbul', label: '(UTC+3) Istanbul' },
    { value: 'Asia/Jerusalem', label: '(UTC+3) Jerusalem' },
    { value: 'Europe/Kiev', label: '(UTC+3) Kiev' },
    { value: 'Asia/Kuwait', label: '(UTC+3) Kuwait City' },
    { value: 'Europe/Moscow', label: '(UTC+3) Moscow' },
    { value: 'Asia/Riyadh', label: '(UTC+3) Riyadh' },
    { value: 'Asia/Qatar', label: '(UTC+3) Doha' },
    { value: 'Asia/Tehran', label: '(UTC+3:30) Tehran' },
    { value: 'Asia/Dubai', label: '(UTC+4) Dubai' },
    { value: 'Asia/Muscat', label: '(UTC+4) Muscat' },
    { value: 'Asia/Baku', label: '(UTC+4) Baku' },
    { value: 'Asia/Yerevan', label: '(UTC+4) Yerevan' },
    { value: 'Asia/Kabul', label: '(UTC+4:30) Kabul' },
    { value: 'Asia/Karachi', label: '(UTC+5) Karachi' },
    { value: 'Asia/Tashkent', label: '(UTC+5) Tashkent' },
    { value: 'Asia/Kolkata', label: '(UTC+5:30) New Delhi' },
    { value: 'Asia/Colombo', label: '(UTC+5:30) Colombo' },
    { value: 'Asia/Kathmandu', label: '(UTC+5:45) Kathmandu' },
    { value: 'Asia/Dhaka', label: '(UTC+6) Dhaka' },
    { value: 'Asia/Almaty', label: '(UTC+6) Almaty' },
    { value: 'Asia/Yangon', label: '(UTC+6:30) Yangon' },
    { value: 'Asia/Bangkok', label: '(UTC+7) Bangkok' },
    { value: 'Asia/Jakarta', label: '(UTC+7) Jakarta' },
    { value: 'Asia/Hanoi', label: '(UTC+7) Hanoi' },
    { value: 'Asia/Phnom_Penh', label: '(UTC+7) Phnom Penh' },
    { value: 'Asia/Vientiane', label: '(UTC+7) Vientiane' },
    { value: 'Asia/Hong_Kong', label: '(UTC+8) Hong Kong' },
    { value: 'Asia/Beijing', label: '(UTC+8) Beijing' },
    { value: 'Asia/Shanghai', label: '(UTC+8) Shanghai' },
    { value: 'Asia/Singapore', label: '(UTC+8) Singapore' },
    { value: 'Asia/Taipei', label: '(UTC+8) Taipei' },
    { value: 'Asia/Kuala_Lumpur', label: '(UTC+8) Kuala Lumpur' },
    { value: 'Asia/Manila', label: '(UTC+8) Manila' },
    { value: 'Asia/Ulaanbaatar', label: '(UTC+8) Ulaanbaatar' },
    { value: 'Asia/Seoul', label: '(UTC+9) Seoul' },
    { value: 'Asia/Tokyo', label: '(UTC+9) Tokyo' },
    { value: 'Asia/Pyongyang', label: '(UTC+9) Pyongyang' },
    { value: 'Australia/Darwin', label: '(UTC+9:30) Darwin' },
    { value: 'Australia/Adelaide', label: '(UTC+9:30) Adelaide' },
    { value: 'Australia/Brisbane', label: '(UTC+10) Brisbane' },
    { value: 'Australia/Canberra', label: '(UTC+10) Canberra' },
    { value: 'Australia/Melbourne', label: '(UTC+10) Melbourne' },
    { value: 'Australia/Sydney', label: '(UTC+10) Sydney' },
    { value: 'Pacific/Guam', label: '(UTC+10) Guam' },
    { value: 'Pacific/Port_Moresby', label: '(UTC+10) Port Moresby' },
    { value: 'Pacific/Noumea', label: '(UTC+11) Noumea' },
    { value: 'Pacific/Auckland', label: '(UTC+12) Auckland' },
    { value: 'Pacific/Fiji', label: '(UTC+12) Suva' }
  ];

  countries: { name: string, code: string }[] = [
    { name: 'Afghanistan', code: 'AF' },
    { name: 'Åland Islands', code: 'AX' },
    { name: 'Albania', code: 'AL' },
    { name: 'Algeria', code: 'DZ' },
    { name: 'American Samoa', code: 'AS' },
    { name: 'Andorra', code: 'AD' },
    { name: 'Angola', code: 'AO' },
    { name: 'Anguilla', code: 'AI' },
    { name: 'Antarctica', code: 'AQ' },
    { name: 'Antigua and Barbuda', code: 'AG' },
    { name: 'Argentina', code: 'AR' },
    { name: 'Armenia', code: 'AM' },
    { name: 'Aruba', code: 'AW' },
    { name: 'Australia', code: 'AU' },
    { name: 'Austria', code: 'AT' },
    { name: 'Azerbaijan', code: 'AZ' },
    { name: 'Bahamas', code: 'BS' },
    { name: 'Bahrain', code: 'BH' },
    { name: 'Bangladesh', code: 'BD' },
    { name: 'Barbados', code: 'BB' },
    { name: 'Belarus', code: 'BY' },
    { name: 'Belgium', code: 'BE' },
    { name: 'Belize', code: 'BZ' },
    { name: 'Benin', code: 'BJ' },
    { name: 'Bermuda', code: 'BM' },
    { name: 'Bhutan', code: 'BT' },
    { name: 'Bolivia', code: 'BO' },
    { name: 'Bosnia and Herzegovina', code: 'BA' },
    { name: 'Botswana', code: 'BW' },
    { name: 'Bouvet Island', code: 'BV' },
    { name: 'Brazil', code: 'BR' },
    { name: 'British Indian Ocean Territory', code: 'IO' },
    { name: 'Brunei Darussalam', code: 'BN' },
    { name: 'Bulgaria', code: 'BG' },
    { name: 'Burkina Faso', code: 'BF' },
    { name: 'Burundi', code: 'BI' },
    { name: 'Cambodia', code: 'KH' },
    { name: 'Cameroon', code: 'CM' },
    { name: 'Canada', code: 'CA' },
    { name: 'Cape Verde', code: 'CV' },
    { name: 'Cayman Islands', code: 'KY' },
    { name: 'Central African Republic', code: 'CF' },
    { name: 'Chad', code: 'TD' },
    { name: 'Chile', code: 'CL' },
    { name: 'China', code: 'CN' },
    { name: 'Christmas Island', code: 'CX' },
    { name: 'Cocos (Keeling) Islands', code: 'CC' },
    { name: 'Colombia', code: 'CO' },
    { name: 'Comoros', code: 'KM' },
    { name: 'Congo', code: 'CG' },
    { name: 'Congo, The Democratic Republic of the', code: 'CD' },
    { name: 'Cook Islands', code: 'CK' },
    { name: 'Costa Rica', code: 'CR' },
    { name: 'Cote D\'Ivoire', code: 'CI' },
    { name: 'Croatia', code: 'HR' },
    { name: 'Cuba', code: 'CU' },
    { name: 'Cyprus', code: 'CY' },
    { name: 'Czech Republic', code: 'CZ' },
    { name: 'Denmark', code: 'DK' },
    { name: 'Djibouti', code: 'DJ' },
    { name: 'Dominica', code: 'DM' },
    { name: 'Dominican Republic', code: 'DO' },
    { name: 'Ecuador', code: 'EC' },
    { name: 'Egypt', code: 'EG' },
    { name: 'El Salvador', code: 'SV' },
    { name: 'Equatorial Guinea', code: 'GQ' },
    { name: 'Eritrea', code: 'ER' },
    { name: 'Estonia', code: 'EE' },
    { name: 'Ethiopia', code: 'ET' },
    { name: 'Falkland Islands (Malvinas)', code: 'FK' },
    { name: 'Faroe Islands', code: 'FO' },
    { name: 'Fiji', code: 'FJ' },
    { name: 'Finland', code: 'FI' },
    { name: 'France', code: 'FR' },
    { name: 'French Guiana', code: 'GF' },
    { name: 'French Polynesia', code: 'PF' },
    { name: 'French Southern Territories', code: 'TF' },
    { name: 'Gabon', code: 'GA' },
    { name: 'Gambia', code: 'GM' },
    { name: 'Georgia', code: 'GE' },
    { name: 'Germany', code: 'DE' },
    { name: 'Ghana', code: 'GH' },
    { name: 'Gibraltar', code: 'GI' },
    { name: 'Greece', code: 'GR' },
    { name: 'Greenland', code: 'GL' },
    { name: 'Grenada', code: 'GD' },
    { name: 'Guadeloupe', code: 'GP' },
    { name: 'Guam', code: 'GU' },
    { name: 'Guatemala', code: 'GT' },
    { name: 'Guernsey', code: 'GG' },
    { name: 'Guinea', code: 'GN' },
    { name: 'Guinea-Bissau', code: 'GW' },
    { name: 'Guyana', code: 'GY' },
    { name: 'Haiti', code: 'HT' },
    { name: 'Heard Island and Mcdonald Islands', code: 'HM' },
    { name: 'Holy See (Vatican City State)', code: 'VA' },
    { name: 'Honduras', code: 'HN' },
    { name: 'Hong Kong', code: 'HK' },
    { name: 'Hungary', code: 'HU' },
    { name: 'Iceland', code: 'IS' },
    { name: 'India', code: 'IN' },
    { name: 'Indonesia', code: 'ID' },
    { name: 'Iran, Islamic Republic Of', code: 'IR' },
    { name: 'Iraq', code: 'IQ' },
    { name: 'Ireland', code: 'IE' },
    { name: 'Isle of Man', code: 'IM' },
    { name: 'Israel', code: 'IL' },
    { name: 'Italy', code: 'IT' },
    { name: 'Jamaica', code: 'JM' },
    { name: 'Japan', code: 'JP' },
    { name: 'Jersey', code: 'JE' },
    { name: 'Jordan', code: 'JO' },
    { name: 'Kazakhstan', code: 'KZ' },
    { name: 'Kenya', code: 'KE' },
    { name: 'Kiribati', code: 'KI' },
    { name: 'Korea, Democratic People\'s Republic of', code: 'KP' },
    { name: 'Korea, Republic of', code: 'KR' },
    { name: 'Kuwait', code: 'KW' },
    { name: 'Kyrgyzstan', code: 'KG' },
    { name: 'Lao People\'s Democratic Republic', code: 'LA' },
    { name: 'Latvia', code: 'LV' },
    { name: 'Lebanon', code: 'LB' },
    { name: 'Lesotho', code: 'LS' },
    { name: 'Liberia', code: 'LR' },
    { name: 'Libyan Arab Jamahiriya', code: 'LY' },
    { name: 'Liechtenstein', code: 'LI' },
    { name: 'Lithuania', code: 'LT' },
    { name: 'Luxembourg', code: 'LU' },
    { name: 'Macao', code: 'MO' },
    { name: 'Macedonia, The Former Yugoslav Republic of', code: 'MK' },
    { name: 'Madagascar', code: 'MG' },
    { name: 'Malawi', code: 'MW' },
    { name: 'Malaysia', code: 'MY' },
    { name: 'Maldives', code: 'MV' },
    { name: 'Mali', code: 'ML' },
    { name: 'Malta', code: 'MT' },
    { name: 'Marshall Islands', code: 'MH' },
    { name: 'Martinique', code: 'MQ' },
    { name: 'Mauritania', code: 'MR' },
    { name: 'Mauritius', code: 'MU' },
    { name: 'Mayotte', code: 'YT' },
    { name: 'Mexico', code: 'MX' },
    { name: 'Micronesia, Federated States of', code: 'FM' },
    { name: 'Moldova, Republic of', code: 'MD' },
    { name: 'Monaco', code: 'MC' },
    { name: 'Mongolia', code: 'MN' },
    { name: 'Montenegro', code: 'ME' },
    { name: 'Montserrat', code: 'MS' },
    { name: 'Morocco', code: 'MA' },
    { name: 'Mozambique', code: 'MZ' },
    { name: 'Myanmar', code: 'MM' },
    { name: 'Namibia', code: 'NA' },
    { name: 'Nauru', code: 'NR' },
    { name: 'Nepal', code: 'NP' },
    { name: 'Netherlands', code: 'NL' },
    { name: 'Netherlands Antilles', code: 'AN' },
    { name: 'New Caledonia', code: 'NC' },
    { name: 'New Zealand', code: 'NZ' },
    { name: 'Nicaragua', code: 'NI' },
    { name: 'Niger', code: 'NE' },
    { name: 'Nigeria', code: 'NG' },
    { name: 'Niue', code: 'NU' },
    { name: 'Norfolk Island', code: 'NF' },
    { name: 'Northern Mariana Islands', code: 'MP' },
    { name: 'Norway', code: 'NO' },
    { name: 'Oman', code: 'OM' },
    { name: 'Pakistan', code: 'PK' },
    { name: 'Palau', code: 'PW' },
    { name: 'Palestinian Territory, Occupied', code: 'PS' },
    { name: 'Panama', code: 'PA' },
    { name: 'Papua New Guinea', code: 'PG' },
    { name: 'Paraguay', code: 'PY' },
    { name: 'Peru', code: 'PE' },
    { name: 'Philippines', code: 'PH' },
    { name: 'Pitcairn', code: 'PN' },
    { name: 'Poland', code: 'PL' },
    { name: 'Portugal', code: 'PT' },
    { name: 'Puerto Rico', code: 'PR' },
    { name: 'Qatar', code: 'QA' },
    { name: 'Reunion', code: 'RE' },
    { name: 'Romania', code: 'RO' },
    { name: 'Russian Federation', code: 'RU' },
    { name: 'Rwanda', code: 'RW' },
    { name: 'Saint Helena', code: 'SH' },
    { name: 'Saint Kitts and Nevis', code: 'KN' },
    { name: 'Saint Lucia', code: 'LC' },
    { name: 'Saint Pierre and Miquelon', code: 'PM' },
    { name: 'Saint Vincent and the Grenadines', code: 'VC' },
    { name: 'Samoa', code: 'WS' },
    { name: 'San Marino', code: 'SM' },
    { name: 'Sao Tome and Principe', code: 'ST' },
    { name: 'Saudi Arabia', code: 'SA' },
    { name: 'Senegal', code: 'SN' },
    { name: 'Serbia', code: 'RS' },
    { name: 'Seychelles', code: 'SC' },
    { name: 'Sierra Leone', code: 'SL' },
    { name: 'Singapore', code: 'SG' },
    { name: 'Slovakia', code: 'SK' },
    { name: 'Slovenia', code: 'SI' },
    { name: 'Solomon Islands', code: 'SB' },
    { name: 'Somalia', code: 'SO' },
    { name: 'South Africa', code: 'ZA' },
    { name: 'South Georgia and the South Sandwich Islands', code: 'GS' },
    { name: 'Spain', code: 'ES' },
    { name: 'Sri Lanka', code: 'LK' },
    { name: 'Sudan', code: 'SD' },
    { name: 'Suriname', code: 'SR' },
    { name: 'Svalbard and Jan Mayen', code: 'SJ' },
    { name: 'Swaziland', code: 'SZ' },
    { name: 'Sweden', code: 'SE' },
    { name: 'Switzerland', code: 'CH' },
    { name: 'Syrian Arab Republic', code: 'SY' },
    { name: 'Taiwan', code: 'TW' },
    { name: 'Tajikistan', code: 'TJ' },
    { name: 'Tanzania, United Republic of', code: 'TZ' },
    { name: 'Thailand', code: 'TH' },
    { name: 'Timor-Leste', code: 'TL' },
    { name: 'Togo', code: 'TG' },
    { name: 'Tokelau', code: 'TK' },
    { name: 'Tonga', code: 'TO' },
    { name: 'Trinidad and Tobago', code: 'TT' },
    { name: 'Tunisia', code: 'TN' },
    { name: 'Turkey', code: 'TR' },
    { name: 'Turkmenistan', code: 'TM' },
    { name: 'Turks and Caicos Islands', code: 'TC' },
    { name: 'Tuvalu', code: 'TV' },
    { name: 'Uganda', code: 'UG' },
    { name: 'Ukraine', code: 'UA' },
    { name: 'United Arab Emirates', code: 'AE' },
    { name: 'United Kingdom', code: 'GB' },
    { name: 'United States', code: 'US' },
    { name: 'United States Minor Outlying Islands', code: 'UM' },
    { name: 'Uruguay', code: 'UY' },
    { name: 'Uzbekistan', code: 'UZ' },
    { name: 'Vanuatu', code: 'VU' },
    { name: 'Venezuela', code: 'VE' },
    { name: 'Vietnam', code: 'VN' },
    { name: 'Virgin Islands, British', code: 'VG' },
    { name: 'Virgin Islands, U.S.', code: 'VI' },
    { name: 'Wallis and Futuna', code: 'WF' },
    { name: 'Western Sahara', code: 'EH' },
    { name: 'Yemen', code: 'YE' },
    { name: 'Zambia', code: 'ZM' },
    { name: 'Zimbabwe', code: 'ZW' }
  ];

  isSubmitting = false;
  errorMessage = '';
  nameError = '';
  nameChecking = false;
  nameAvailable = false;
  isLoadingCountry = true;
  isLoadingTimezone = true;
  private nameCheckSubject = new Subject<string>();
  private nameCheckSubscription: Subscription | null = null;

  private supabaseService = inject(SupabaseService);
  private groupService = inject(GroupService);
  private router = inject(Router);
  private http = inject(HttpClient);

  constructor() {}

  ngOnInit() {
    this.supabaseService.currentUser$.subscribe(user => {
      if (user) {
        this.userId = user.id;
      } else {
        this.router.navigate(['/login']);
      }
    });

    this.nameCheckSubscription = this.nameCheckSubject.pipe(
      debounceTime(500), 
      distinctUntilChanged() 
    ).subscribe(name => {
      this.checkGroupNameExists(name);
    });

    this.countries.sort((a, b) => a.name.localeCompare(b.name));

    this.detectUserCountry();
  }

  ngOnDestroy() {
    if (this.nameCheckSubscription) {
      this.nameCheckSubscription.unsubscribe();
    }
  }

  async detectUserCountry() {
    this.isLoadingCountry = true;

    try {
      const position = await Geolocation.getCurrentPosition({
        enableHighAccuracy: true,
        timeout: 10000
      });


      if (position && position.coords) {
        const { latitude, longitude } = position.coords;

        this.userCoordinates = { latitude, longitude };

        this.http.get<any>(`https:
          .subscribe({
            next: (data) => {
              if (data && data.countryCode) {
                this.group.country = data.countryCode;

                const foundCountry = this.countries.find(c => c.code === data.countryCode);
                if (!foundCountry) {
                  this.group.country = 'US';
                }

                this.detectTimezone(latitude, longitude);
              } else {
                this.fallbackToIpGeolocation();
              }
              this.isLoadingCountry = false;
            },
            error: (error) => {
              this.fallbackToIpGeolocation();
            }
          });
      } else {
        this.fallbackToIpGeolocation();
      }
    } catch (error) {
      this.fallbackToIpGeolocation();
    }
  }

  private detectTimezone(latitude: number, longitude: number) {
    this.http.get<any>(`https:
      .subscribe({
        next: (data) => {
          if (data && data.status === 'OK') {

            const gmtOffset = data.gmtOffset / 3600; 

            if (data.zoneName) {

              const foundTimezone = this.timezones.find(tz => tz.value === data.zoneName);
              if (foundTimezone) {
                this.group.timezone = foundTimezone.value;
                return;
              }
            }

            if (data.zoneName) {
              const similarTimezone = this.findSimilarTimezone(data.zoneName);
              if (similarTimezone) {
                this.group.timezone = similarTimezone;
                return;
              }
            }

            this.findTimezoneByOffset(gmtOffset);
          }
        },
        error: (error) => {
          this.detectBrowserTimezone();
        }
      });
  }

  private findTimezoneByOffset(offsetHours: number): void {

    const offsetSign = offsetHours >= 0 ? '+' : '-';
    const offsetAbs = Math.abs(offsetHours);
    const offsetInt = Math.floor(offsetAbs);
    const offsetFraction = offsetAbs - offsetInt;

    let offsetStr: string;

    if (offsetFraction === 0) {
      if (offsetHours === 0) {
        offsetStr = 'UTC';
      } else {
        offsetStr = `UTC${offsetSign}${offsetInt}`;
      }
    } else if (offsetFraction === 0.5) {
      offsetStr = `UTC${offsetSign}${offsetInt}:30`;
    } else if (offsetFraction === 0.75) {
      offsetStr = `UTC${offsetSign}${offsetInt}:45`;
    } else if (offsetFraction === 0.25) {
      offsetStr = `UTC${offsetSign}${offsetInt}:15`;
    } else {
      offsetStr = `UTC${offsetSign}${Math.round(offsetAbs)}`;
    }


    const timezonesByOffset = this.timezones.filter(tz =>
      tz.label.includes(`(${offsetStr})`) ||
      tz.label.includes(`(${offsetStr.replace('+', ' +')})`)
    );

    if (timezonesByOffset.length > 0) {
      if (this.userCoordinates) {
        const nearestCity = this.findNearestCityInTimezones(this.userCoordinates.latitude, this.userCoordinates.longitude, timezonesByOffset);
        if (nearestCity) {
          this.group.timezone = nearestCity.value;
          return;
        }
      }

      const userCountry = this.group.country;

      if (userCountry) {
        const countryCityPatterns: { [key: string]: string[] } = {
          'US': ['New York', 'Los Angeles', 'Chicago', 'Denver', 'Phoenix', 'Anchorage'],
          'CA': ['Toronto', 'Vancouver'],
          'MX': ['Mexico City'],
          'SV': ['San Salvador'],
          'NI': ['Managua'],
          'HN': ['Tegucigalpa'],
          'CO': ['Bogota'],
          'PE': ['Lima'],
          'PA': ['Panama City'],
          'VE': ['Caracas'],
          'CL': ['Santiago'],
          'DO': ['Santo Domingo'],
          'TT': ['Port of Spain'],
          'AR': ['Buenos Aires'],
          'BR': ['Sao Paulo'],
          'UY': ['Montevideo'],
          'PY': ['Asuncion'],

          'PT': ['Lisbon', 'Azores'],
          'IS': ['Reykjavik'],
          'MA': ['Casablanca'],
          'IE': ['Dublin'],
          'NG': ['Lagos'],
          'GB': ['London'],
          'TN': ['Tunis'],
          'DZ': ['Algiers'],
          'NL': ['Amsterdam'],
          'RS': ['Belgrade'],
          'DE': ['Berlin'],
          'SK': ['Bratislava'],
          'BE': ['Brussels'],
          'HU': ['Budapest'],
          'DK': ['Copenhagen'],
          'ES': ['Madrid'],
          'NO': ['Oslo'],
          'FR': ['Paris'],
          'CZ': ['Prague'],
          'IT': ['Rome'],
          'SE': ['Stockholm'],
          'AT': ['Vienna'],
          'PL': ['Warsaw'],
          'CH': ['Zurich'],
          'KE': ['Nairobi'],
          'GR': ['Athens'],
          'IQ': ['Baghdad'],
          'FI': ['Helsinki'],
          'TR': ['Istanbul'],
          'IL': ['Jerusalem'],
          'UA': ['Kiev'],
          'KW': ['Kuwait City'],
          'RU': ['Moscow'],
          'SA': ['Riyadh'],
          'QA': ['Doha'],

          'IR': ['Tehran'],
          'AE': ['Dubai'],
          'OM': ['Muscat'],
          'AZ': ['Baku'],
          'AM': ['Yerevan'],
          'AF': ['Kabul'],
          'PK': ['Karachi'],
          'UZ': ['Tashkent'],
          'IN': ['New Delhi', 'Kolkata'],
          'LK': ['Colombo'],
          'NP': ['Kathmandu'],
          'BD': ['Dhaka'],
          'KZ': ['Almaty'],
          'MM': ['Yangon'],
          'TH': ['Bangkok'],
          'ID': ['Jakarta'],
          'VN': ['Hanoi', 'Ho Chi Minh'],
          'KH': ['Phnom Penh'],
          'LA': ['Vientiane'],
          'HK': ['Hong Kong'],
          'CN': ['Beijing', 'Shanghai'],
          'SG': ['Singapore'],
          'TW': ['Taipei'],
          'MY': ['Kuala Lumpur'],
          'PH': ['Manila'],
          'MN': ['Ulaanbaatar'],
          'KR': ['Seoul'],
          'JP': ['Tokyo'],
          'KP': ['Pyongyang'],

          'AU': ['Sydney', 'Melbourne', 'Brisbane', 'Perth', 'Adelaide', 'Darwin', 'Canberra'],
          'GU': ['Guam'],
          'PG': ['Port Moresby'],
          'NC': ['Noumea'],
          'NZ': ['Auckland'],
          'FJ': ['Suva'],

          'EG': ['Cairo'],
          'ZA': ['Johannesburg']
        };

        if (countryCityPatterns[userCountry]) {
          for (const cityPattern of countryCityPatterns[userCountry]) {
            const countryTimezone = timezonesByOffset.find(tz =>
              tz.label.toLowerCase().includes(cityPattern.toLowerCase())
            );

            if (countryTimezone) {
              this.group.timezone = countryTimezone.value;
              return;
            }
          }
        }
      }

      const majorCities = ['London', 'Berlin', 'Paris', 'New York', 'Tokyo', 'Sydney', 'Moscow',
                          'Dubai', 'Singapore', 'Hong Kong', 'Los Angeles', 'Chicago', 'Toronto'];

      for (const city of majorCities) {
        const cityTimezone = timezonesByOffset.find(tz =>
          tz.label.toLowerCase().includes(city.toLowerCase())
        );
        if (cityTimezone) {
          this.group.timezone = cityTimezone.value;
          return;
        }
      }

      this.group.timezone = timezonesByOffset[0].value;
    } else {
      this.group.timezone = 'UTC';
    }
  }

  private userCoordinates: { latitude: number, longitude: number } | null = null;

  private cityCoordinates: { [key: string]: { lat: number, lng: number } } = {
    'Anchorage': { lat: 61.2181, lng: -149.9003 },
    'Los Angeles': { lat: 34.0522, lng: -118.2437 },
    'Phoenix': { lat: 33.4484, lng: -112.0740 },
    'Vancouver': { lat: 49.2827, lng: -123.1207 },
    'Denver': { lat: 39.7392, lng: -104.9903 },
    'Mexico City': { lat: 19.4326, lng: -99.1332 },
    'San Salvador': { lat: 13.6929, lng: -89.2182 },
    'Managua': { lat: 12.1149, lng: -86.2362 },
    'Tegucigalpa': { lat: 14.0723, lng: -87.1921 },
    'Bogota': { lat: 4.7110, lng: -74.0721 },
    'Chicago': { lat: 41.8781, lng: -87.6298 },
    'Lima': { lat: 12.0464, lng: -77.0428 },
    'Panama City': { lat: 8.9824, lng: -79.5199 },
    'Caracas': { lat: 10.4806, lng: -66.9036 },
    'New York': { lat: 40.7128, lng: -74.0060 },
    'Santiago': { lat: -33.4489, lng: -70.6693 },
    'Toronto': { lat: 43.6532, lng: -79.3832 },
    'Santo Domingo': { lat: 18.4861, lng: -69.9312 },
    'Port of Spain': { lat: 10.6550, lng: -61.5020 },
    'Buenos Aires': { lat: -34.6037, lng: -58.3816 },
    'Sao Paulo': { lat: -23.5505, lng: -46.6333 },
    'Montevideo': { lat: -34.9011, lng: -56.1915 },
    'Asuncion': { lat: -25.2637, lng: -57.5759 },

    'Azores': { lat: 37.7412, lng: -25.6756 },
    'Reykjavik': { lat: 64.1466, lng: -21.9426 },
    'Casablanca': { lat: 33.5731, lng: -7.5898 },
    'Dublin': { lat: 53.3498, lng: -6.2603 },
    'Lagos': { lat: 6.5244, lng: 3.3792 },
    'Lisbon': { lat: 38.7223, lng: -9.1393 },
    'London': { lat: 51.5074, lng: -0.1278 },
    'Tunis': { lat: 36.8065, lng: 10.1815 },
    'Algiers': { lat: 36.7538, lng: 3.0588 },
    'Amsterdam': { lat: 52.3676, lng: 4.9041 },
    'Belgrade': { lat: 44.7866, lng: 20.4489 },
    'Berlin': { lat: 52.5200, lng: 13.4050 },
    'Bratislava': { lat: 48.1486, lng: 17.1077 },
    'Brussels': { lat: 50.8503, lng: 4.3517 },
    'Budapest': { lat: 47.4979, lng: 19.0402 },
    'Copenhagen': { lat: 55.6761, lng: 12.5683 },
    'Cairo': { lat: 30.0444, lng: 31.2357 },
    'Madrid': { lat: 40.4168, lng: -3.7038 },
    'Oslo': { lat: 59.9139, lng: 10.7522 },
    'Paris': { lat: 48.8566, lng: 2.3522 },
    'Prague': { lat: 50.0755, lng: 14.4378 },
    'Rome': { lat: 41.9028, lng: 12.4964 },
    'Stockholm': { lat: 59.3293, lng: 18.0686 },
    'Vienna': { lat: 48.2082, lng: 16.3738 },
    'Warsaw': { lat: 52.2297, lng: 21.0122 },
    'Zurich': { lat: 47.3769, lng: 8.5417 },
    'Nairobi': { lat: -1.2921, lng: 36.8219 },
    'Athens': { lat: 37.9838, lng: 23.7275 },
    'Baghdad': { lat: 33.3152, lng: 44.3661 },
    'Helsinki': { lat: 60.1699, lng: 24.9384 },
    'Istanbul': { lat: 41.0082, lng: 28.9784 },
    'Jerusalem': { lat: 31.7683, lng: 35.2137 },
    'Kiev': { lat: 50.4501, lng: 30.5234 },
    'Kuwait City': { lat: 29.3759, lng: 47.9774 },
    'Moscow': { lat: 55.7558, lng: 37.6173 },
    'Riyadh': { lat: 24.7136, lng: 46.6753 },
    'Doha': { lat: 25.2854, lng: 51.5310 },

    'Tehran': { lat: 35.6892, lng: 51.3890 },
    'Dubai': { lat: 25.2048, lng: 55.2708 },
    'Muscat': { lat: 23.5880, lng: 58.3829 },
    'Baku': { lat: 40.4093, lng: 49.8671 },
    'Yerevan': { lat: 40.1792, lng: 44.4991 },
    'Kabul': { lat: 34.5553, lng: 69.2075 },
    'Karachi': { lat: 24.8607, lng: 67.0011 },
    'Tashkent': { lat: 41.2995, lng: 69.2401 },
    'New Delhi': { lat: 28.6139, lng: 77.2090 },
    'Kolkata': { lat: 22.5726, lng: 88.3639 },
    'Colombo': { lat: 6.9271, lng: 79.8612 },
    'Kathmandu': { lat: 27.7172, lng: 85.3240 },
    'Dhaka': { lat: 23.8103, lng: 90.4125 },
    'Almaty': { lat: 43.2220, lng: 76.8512 },
    'Yangon': { lat: 16.8661, lng: 96.1951 },
    'Bangkok': { lat: 13.7563, lng: 100.5018 },
    'Jakarta': { lat: 6.2088, lng: 106.8456 },
    'Hanoi': { lat: 21.0278, lng: 105.8342 },
    'Ho Chi Minh': { lat: 10.8231, lng: 106.6297 },
    'Phnom Penh': { lat: 11.5564, lng: 104.9282 },
    'Vientiane': { lat: 17.9757, lng: 102.6331 },
    'Hong Kong': { lat: 22.3193, lng: 114.1694 },
    'Beijing': { lat: 39.9042, lng: 116.4074 },
    'Shanghai': { lat: 31.2304, lng: 121.4737 },
    'Singapore': { lat: 1.3521, lng: 103.8198 },
    'Taipei': { lat: 25.0330, lng: 121.5654 },
    'Kuala Lumpur': { lat: 3.1390, lng: 101.6869 },
    'Manila': { lat: 14.5995, lng: 120.9842 },
    'Ulaanbaatar': { lat: 47.8864, lng: 106.9057 },
    'Seoul': { lat: 37.5665, lng: 126.9780 },
    'Tokyo': { lat: 35.6762, lng: 139.6503 },
    'Pyongyang': { lat: 39.0392, lng: 125.7625 },

    'Darwin': { lat: -12.4634, lng: 130.8456 },
    'Adelaide': { lat: -34.9285, lng: 138.6007 },
    'Brisbane': { lat: -27.4698, lng: 153.0251 },
    'Canberra': { lat: -35.2809, lng: 149.1300 },
    'Melbourne': { lat: -37.8136, lng: 144.9631 },
    'Sydney': { lat: -33.8688, lng: 151.2093 },
    'Perth': { lat: -31.9505, lng: 115.8605 },
    'Guam': { lat: 13.4443, lng: 144.7937 },
    'Port Moresby': { lat: -9.4438, lng: 147.1803 },
    'Noumea': { lat: -22.2711, lng: 166.4416 },
    'Auckland': { lat: -36.8509, lng: 174.7645 },
    'Suva': { lat: -18.1416, lng: 178.4419 }
  };

  private findNearestCityInTimezones(lat: number, lng: number, timezones: { value: string, label: string }[]): { value: string, label: string } | null {
    if (!lat || !lng || timezones.length === 0) return null;

    let nearestCity: { value: string, label: string } | null = null;
    let shortestDistance = Number.MAX_VALUE;

    for (const timezone of timezones) {
      const cityMatch = timezone.label.match(/\)\s+(.+)$/);
      if (!cityMatch) continue;

      const cityName = cityMatch[1];

      let cityCoords: { lat: number, lng: number } | null = null;

      if (this.cityCoordinates[cityName]) {
        cityCoords = this.cityCoordinates[cityName];
      } else {
        for (const knownCity in this.cityCoordinates) {
          if (cityName.includes(knownCity) || knownCity.includes(cityName)) {
            cityCoords = this.cityCoordinates[knownCity];
            break;
          }
        }
      }

      if (cityCoords) {
        const distance = this.calculateDistance(lat, lng, cityCoords.lat, cityCoords.lng);

        if (distance < shortestDistance) {
          shortestDistance = distance;
          nearestCity = timezone;
        }
      }
    }

    return nearestCity;
  }

  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; 
    const dLat = this.deg2rad(lat2 - lat1);
    const dLon = this.deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c; 
    return distance;
  }

  private deg2rad(deg: number): number {
    return deg * (Math.PI/180);
  }

  private detectBrowserTimezone(): void {
    try {
      const offsetMinutes = new Date().getTimezoneOffset();
      const offsetHours = -offsetMinutes / 60;


      this.findTimezoneByOffset(offsetHours);
    } catch (error) {
      this.group.timezone = 'UTC';
    }
  }

  private findSimilarTimezone(zoneName: string): string | null {
    const parts = zoneName.split('/');
    if (parts.length >= 1) {
      const continent = parts[0];

      const similarTimezone = this.timezones.find(tz => tz.value.startsWith(continent + '/'));
      if (similarTimezone) {
        return similarTimezone.value;
      }
    }

    return null;
  }

  private fallbackToIpGeolocation() {

    this.http.get<any>('https://ipapi.co/json/').subscribe({
      next: (data) => {
        if (data && data.country_code) {
          this.group.country = data.country_code;

          const foundCountry = this.countries.find(c => c.code === data.country_code);
          if (!foundCountry) {
            this.group.country = 'US';
          }

          if (data.timezone) {

            const foundTimezone = this.timezones.find(tz => tz.value === data.timezone);
            if (foundTimezone) {
              this.group.timezone = foundTimezone.value;
            } else {
              const similarTimezone = this.findSimilarTimezone(data.timezone);
              if (similarTimezone) {
                this.group.timezone = similarTimezone;
              } else if (data.utc_offset) {
                const offsetHours = parseInt(data.utc_offset.substring(0, 3)) +
                                   (parseInt(data.utc_offset.substring(3, 5)) / 60);
                this.findTimezoneByOffset(offsetHours);
              } else {
                this.detectBrowserTimezone();
              }
            }
          } else {
            this.detectBrowserTimezone();
          }
        } else {
          this.group.country = 'US';
          this.detectBrowserTimezone();
        }
        this.isLoadingCountry = false;
      },
      error: (error) => {
        this.group.country = 'US';
        this.detectBrowserTimezone();
        this.isLoadingCountry = false;
      }
    });
  }

  checkGroupName() {
    const name = this.group.name?.trim();

    this.nameAvailable = false;

    if (!name) {
      this.nameError = '';
      this.nameChecking = false;
      return;
    }

    this.nameChecking = true;

    this.nameCheckSubject.next(name);
  }

  async checkGroupNameExists(name: string) {
    if (!name) {
      this.nameError = '';
      this.nameChecking = false;
      return;
    }

    try {
      const { data, error } = await this.supabaseService.getClient()
        .from('groups')
        .select('id')
        .eq('name', name)
        .limit(1);

      this.nameChecking = false;

      if (error) {
        this.nameError = 'Error checking group name availability';
        return;
      }

      if (data && data.length > 0) {
        this.nameError = 'This group name is already taken';
        this.nameAvailable = false;
      } else {
        this.nameError = '';
        this.nameAvailable = true;
      }
    } catch (error) {
      this.nameChecking = false;
      this.nameError = 'Error checking group name availability';
    }
  }


  async createGroup() {
    if (!this.userId) {
      this.errorMessage = 'You must be logged in to create a group';
      return;
    }

    if (!this.group.name || !this.group.emoji || !this.group.timezone || !this.group.country) {
      this.errorMessage = 'Please fill in all required fields';
      return;
    }

    if (this.nameError) {
      this.errorMessage = 'Please choose a different group name';
      return;
    }

    this.isSubmitting = true;
    this.errorMessage = '';

    try {
      const newGroup: Omit<Group, 'id' | 'created'> = {
        name: this.group.name,
        emoji: this.group.emoji,
        timezone: this.group.timezone,
        country: this.group.country,
        admin_id: this.userId,
        level: 0,
        strength_xp: 0,
        money_xp: 0,
        health_xp: 0,
        knowledge_xp: 0,
        enable_sidequests: true
      };

      const groupId = await this.groupService.createGroup(newGroup);

      this.router.navigate(['/groups', groupId]);
    } catch (error: any) {
      this.errorMessage = error.message || 'Failed to create group. Please try again.';
    } finally {
      this.isSubmitting = false;
    }
  }
}
