﻿import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { QuestService } from '../../services/quest.service';
import { SideQuestService } from '../../services/sidequest.service';
import { UserService } from '../../services/user.service';
import { SupabaseService } from '../../services/supabase.service';
import { Quest, QuestCategory, QuestGoalUnit, QuestPeriod, QuestPriority, QuestProgress, QuestType } from '../../models/quest.model';
import { User } from '../../models/user.model';
import { Observable, Subscription, forkJoin, map, of, switchMap, take, firstValueFrom } from 'rxjs';
import { CelebrationComponent } from '../../components/celebration/celebration.component';
import { Activated<PERSON>oute, Router } from '@angular/router';
import { PreferencesService } from '../../services/preferences.service';
import { EmojiInputDirective } from '../../directives/emoji-input.directive';
import { StreakCalculatorService } from '../../services/streak-calculator';


interface WeekDate {
  date: string; 
  day: number;
  is_today: boolean;
  is_selected: boolean;
  is_future: boolean;
  total_quests: number;
  completed_quests: number;
  completion_percentage: number;
}

interface DailyQuest {
  id: string;
  current_quest: {
    id: string;
    name: string;
    description: string;
    goal_value: number;
    goal_unit: string;
  };
  streak: number;
  completed: boolean;
  value_achieved: number;
  emoji: string;
}

interface QuestDisplay extends Quest {
  completed: boolean;
  value_achieved: number;
}

@Component({
  selector: 'app-today',
  templateUrl: './today.page.html',
  styleUrls: ['./today.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, CelebrationComponent, EmojiInputDirective]
})
export class TodayPage implements OnInit, OnDestroy {
  user$: Observable<User | null> = of(null);
  userId: string | null = null;
  userSubscription: Subscription | undefined;
  showSidequests = true;

  selectedDate: Date = new Date();
  weekDates: WeekDate[] = [];
  dayNames: string[] = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];
  headerText: string = 'Today';
  weekOffset: number = 0;

  quests: QuestDisplay[] = [];
  dailyQuest: DailyQuest | null = null;

  private questCache: { [dateKey: string]: QuestDisplay[] } = {};

  private isLoadingData = false;

  private loadDailySideQuest() {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const selectedDate = new Date(this.selectedDate);
    selectedDate.setHours(0, 0, 0, 0);
    const isTodaySelected = selectedDate.getTime() === today.getTime();

    if (!isTodaySelected) {
      this.dailyQuest = null;
      return;
    }

    if (this.showSidequests && isTodaySelected && this.userId) {
      this.sideQuestService.ensureUserHasDailySideQuests(this.userId!).pipe(
        take(1)
      ).subscribe({
        next: (sideQuests) => {
          if (sideQuests && sideQuests.length > 0) {
            const sideQuest = sideQuests[0];

            this.supabaseService.getClient()
              .from('daily_sidequest_pool')
              .select('*')
              .eq('id', sideQuest.current_quest_id)
              .single()
              .then(response => {
                if (response.error) {
                  return;
                }

                const questDetails = response.data;

                this.dailyQuest = {
                  id: sideQuest.id!,
                  current_quest: {
                    id: sideQuest.current_quest_id!,
                    name: questDetails.name || 'Daily Side Quest',
                    description: questDetails.description || 'Complete this daily side quest',
                    goal_value: questDetails.goal_value || 1,
                    goal_unit: questDetails.goal_unit || 'count'
                  },
                  streak: sideQuest.streak || 0,
                  completed: sideQuest.completed || false,
                  value_achieved: sideQuest.value_achieved || 0,
                  emoji: questDetails.emoji || '🎯'
                };
              });
          } else {
            this.dailyQuest = null;
          }
        },
        error: () => {
          this.dailyQuest = null;
        }
      });
    }
  }

  showAddQuestModal = false;
  newQuest = this.getEmptyQuest();
  hasHighPriorityQuest = false;

  showCelebration = false;
  currentUser: User | null = null;
  celebrationShownDates: string[] = [];

  weekDays = [
    { value: 'Sun', label: 'Su' },
    { value: 'Mon', label: 'Mo' },
    { value: 'Tue', label: 'Tu' },
    { value: 'Wed', label: 'We' },
    { value: 'Thu', label: 'Th' },
    { value: 'Fri', label: 'Fr' },
    { value: 'Sat', label: 'Sa' }
  ];
  monthDays = Array.from({ length: 31 }, (_, i) => i + 1);
  selectedDaysOfWeek: string[] = [];
  selectedDaysOfMonth: number[] = [];

  private questService = inject(QuestService);
  private sideQuestService = inject(SideQuestService);
  private userService = inject(UserService);
  private supabaseService = inject(SupabaseService);
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private preferencesService = inject(PreferencesService);
  private streakCalculator = inject(StreakCalculatorService);
  private isRedirecting = false; 

  constructor() {
    this.route.queryParams.subscribe(params => {
      const dateParam = params['date'];
      const weekOffsetParam = params['week_offset'];


      if (weekOffsetParam !== undefined) {
        try {
          this.weekOffset = parseInt(weekOffsetParam);
        } catch (error) {
          this.weekOffset = 0;
        }
      } else {
        this.weekOffset = 0;
      }

      if (dateParam) {
        try {
          if (/^\d{4}-\d{2}-\d{2}$/.test(dateParam)) {
            this.selectedDate = new Date(dateParam);
          } else {
            this.selectedDate = new Date(); 
          }
        } catch (error) {
          this.selectedDate = new Date(); 
        }
      } else {
        this.selectedDate = new Date(); 
      }

      this.generateWeekDates();

      this.updateHeaderText();

      if (this.userId) {
        this.loadData();
      }
    });

    this.userSubscription = this.supabaseService.currentUser$.subscribe(authUser => {


      if (!authUser) {
        return;
      }

      this.userId = authUser.id;


      this.userService.getUserById(authUser.id).subscribe(userData => {
        if (!userData) {
          return;
        }

        this.loadData();
      });
    });

    this.user$ = this.supabaseService.currentUser$.pipe(
      switchMap(authUser => {
        if (!authUser) {
          return of(null);
        }

        return this.userService.getUserById(authUser.id);
      })
    );

    const userDataSubscription = this.user$.subscribe({
      next: (user) => {
        if (user) {
          this.showSidequests = user.sidequests_switch;
          this.currentUser = user;
        }
      }
    });

    this.userSubscription = new Subscription();
    this.userSubscription.add(userDataSubscription);
  }

  ngOnInit() {
    this.generateWeekDates();

    setTimeout(() => {
      this.preloadWeekData();
    }, 0);

    try {
      const today = new Date();
      const todayStr = this.formatDate(today);

      const allKeys: string[] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
          allKeys.push(key);
        }
      }

      allKeys.forEach(key => {
        if (key.startsWith('celebration_shown_') && key !== `celebration_shown_${todayStr}`) {
          localStorage.removeItem(key);
        }
      });

      const todayCelebrationShown = localStorage.getItem(`celebration_shown_${todayStr}`);

      this.celebrationShownDates = [];
      if (todayCelebrationShown) {
        this.celebrationShownDates.push(todayStr);
      }
    } catch (error) {
      this.celebrationShownDates = [];
    }
  }

  async ionViewWillEnter() {
    const authUser = this.supabaseService._currentUser.value;

    if (!authUser) {
      return;
    }

    this.userService.ensureUserExists(authUser).subscribe(userData => {
      if (!userData) {
        this.router.navigateByUrl('/signup');
        return;
      }

      let endDate = userData.end_of_current_plan ? new Date(userData.end_of_current_plan) : null;
      const currentDate = new Date();

      let isValidPlan = false;
      if (endDate instanceof Date) {
        isValidPlan = endDate > currentDate;
      }

      if (!isValidPlan) {
        if (this.isRedirecting) return;
        this.isRedirecting = true;

        setTimeout(() => {
          this.router.navigateByUrl('/pricing');
          setTimeout(() => {
            this.isRedirecting = false;
          }, 2000);
        }, 500);
        return;
      }

      const dateKey = this.formatDate(this.selectedDate);
      if (this.questCache[dateKey]) {
        this.quests = this.questCache[dateKey];

        requestAnimationFrame(() => {
          this.initializeSliderBackgrounds();
        });

        this.loadDailySideQuest();
      } else {
        this.loadData();
      }
    });

    const route = this.router.url;
    const dateParam = this.formatDate(this.selectedDate);

    if (route === '/today') {
      this.router.navigate(['/today'], {
        queryParams: {
          date: dateParam,
          week_offset: this.weekOffset !== 0 ? this.weekOffset : null
        },
        replaceUrl: true
      });
    }
  }


  initializeSliderBackgrounds() {
    requestAnimationFrame(() => {
      const sliders = document.querySelectorAll('.progress-slider');
      if (sliders.length === 0) {
        return;
      }

      sliders.forEach(slider => {
        if (slider instanceof HTMLInputElement) {
          const sliderQuestId = slider.getAttribute('data-quest-id');
          if (!sliderQuestId) {
            return;
          }

          const sliderValue = parseInt(slider.value);
          const minValue = parseInt(slider.min);
          const maxValue = parseInt(slider.max);

          const percentage = maxValue > minValue ?
            ((sliderValue - minValue) / (maxValue - minValue)) * 100 : 0;

          slider.style.background =
            `linear-gradient(to right, #4169E1 0%, #4169E1 ${percentage}%, #2C2C2E ${percentage}%, #2C2C2E 100%)`;

          slider.setAttribute('data-current-value', slider.value);
        } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {
          const sliderQuestId = slider.getAttribute('data-quest-id');
          if (!sliderQuestId) {
            return;
          }

          const valueAttr = slider.getAttribute('value') || '0';
          const minAttr = slider.getAttribute('min') || '0';
          const maxAttr = slider.getAttribute('max') || '100';

          const sliderValue = parseInt(valueAttr);
          const minValue = parseInt(minAttr);
          const maxValue = parseInt(maxAttr);

          const percentage = maxValue > minValue ?
            ((sliderValue - minValue) / (maxValue - minValue)) * 100 : 0;

          slider.style.setProperty('--progress-value', `${percentage}%`);

          slider.setAttribute('data-current-value', sliderValue.toString());
        }
      });
    });
  }

  ionViewWillLeave() {
  }

  ngOnDestroy() {
    if (this.userSubscription) {
      this.userSubscription.unsubscribe();
    }
  }

  async loadData() {
    if (!this.userId) {
      return;
    }

    this.updateHeaderText();

    const dateKey = this.formatDate(this.selectedDate);
    if (this.questCache[dateKey]) {
      this.quests = this.questCache[dateKey];

      requestAnimationFrame(() => {
        this.initializeSliderBackgrounds();
      });

      this.loadDailySideQuest();

      return;
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const selectedDate = new Date(this.selectedDate);
    selectedDate.setHours(0, 0, 0, 0);
    const isTodaySelected = selectedDate.getTime() === today.getTime();

    if (isTodaySelected) {
      const todayDateString = this.formatDate(today);
      try {
        const { value: lastStreakCalculation } = await this.preferencesService.get('last_streak_calculation');

        if (lastStreakCalculation !== todayDateString) {

          await firstValueFrom(this.questService.getQuests(this.userId!).pipe(
            take(1),
            switchMap(async quests => {
              for (const quest of quests) {
                if (quest.id) {
                  await this.questService.checkMissedDays(quest.id);
                }
              }

              await this.questService.createQuitQuestProgressForToday();

              return quests;
            })
          ));

        } else {
        }
      } catch (error) {

      }

      if (this.showSidequests) {
        this.sideQuestService.recalculateSideQuestStreak(this.userId, this.selectedDate)
          .subscribe({
            error: (error) => {
            }
          });
      }
    }
    this.questService.getQuests(this.userId).pipe(
      take(1),
      switchMap(quests => {
        const filteredQuests = this.filterQuestsForDate(quests, this.selectedDate);

        if (filteredQuests.length === 0) {
          return of([]);
        }

        const sortedFilteredQuests = [...filteredQuests].sort((a, b) => {
          if (a.created_at && b.created_at) {
            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
          }
          return a.id && b.id ? a.id.localeCompare(b.id) : 0;
        });

        return this.questService.getQuestProgressForDate(this.userId!, this.selectedDate).pipe(
          take(1),
          switchMap(allProgress => {
            const progressLookup: { [questId: string]: QuestProgress } = {};
            allProgress.forEach(progress => {
              progressLookup[progress.quest_id] = progress;
            });

            if (isTodaySelected) {
              const todayDateString = this.formatDate(today);
              return this.preferencesService.get('last_streak_calculation').then(({ value: lastStreakCalculation }) => {
                if (lastStreakCalculation !== todayDateString) {

                  return this.streakCalculator.calculateStreaks(this.userId!,  sortedFilteredQuests).then(streaks => {
                    return sortedFilteredQuests.map(quest => {
                      const progress = progressLookup[quest.id!];
                      const calculatedStreak = streaks[quest.id!] || 0;

                      this.questService.updateQuestStreak(quest.id!, calculatedStreak).subscribe();

                      return {
                        ...quest,
                        completed: progress?.completed || false,
                        value_achieved: progress?.value_achieved || 0,
                        streak: calculatedStreak
                      } as QuestDisplay;
                    });
                  }).then(result => {
                    this.preferencesService.set('last_streak_calculation', todayDateString);
                    return result;
                  });
                } else {

                  return sortedFilteredQuests.map(quest => {
                    const progress = progressLookup[quest.id!];

                    return {
                      ...quest,
                      completed: progress?.completed || false,
                      value_achieved: progress?.value_achieved || 0,
                      streak: quest.streak || 0
                    } as QuestDisplay;
                  });
                }
              }).catch(error => {

                return sortedFilteredQuests.map(quest => {
                  const progress = progressLookup[quest.id!];

                  return {
                    ...quest,
                    completed: progress?.completed || false,
                    value_achieved: progress?.value_achieved || 0,
                    streak: quest.streak || 0
                  } as QuestDisplay;
                });
              });
            } else {
              return Promise.resolve(sortedFilteredQuests.map(quest => {
                const progress = progressLookup[quest.id!];

                return {
                  ...quest,
                  completed: progress?.completed || false,
                  value_achieved: progress?.value_achieved || 0,
                  streak: 0 
                } as QuestDisplay;
              }));
            }
          })
        );


      })
    ).subscribe({
      next: (questsWithProgress) => {
        const sortedQuests = [...questsWithProgress].sort((a, b) => {
          if (a.created_at && b.created_at) {
            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
          }
          return a.id && b.id ? a.id.localeCompare(b.id) : 0;
        });

        this.checkAllQuestsCompleted(sortedQuests);

        this.quests = sortedQuests;

        const dateKey = this.formatDate(this.selectedDate);
        this.questCache[dateKey] = sortedQuests;

        this.updateWeekDateProgress();

        requestAnimationFrame(() => {
          this.initializeSliderBackgrounds();
        });

        this.loadDailySideQuest();
      },
      error: (error) => {
      }
    });

  }

  generateWeekDates() {
    const today = new Date();

    const currentDay = today.getDay(); 
    const daysFromMonday = currentDay === 0 ? 6 : currentDay - 1; 
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - daysFromMonday + (7 * this.weekOffset));

    this.weekDates = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);

      const dateString = this.formatDate(date);
      const isToday = this.isSameDay(date, today);
      const isSelected = this.isSameDay(date, this.selectedDate);
      const isFuture = date > today;

      const dateKey = dateString;
      let totalQuests = 0;
      let completedQuests = 0;
      let completionPercentage = 0;

      if (this.weekProgressCache[dateKey]) {
        const cached = this.weekProgressCache[dateKey];
        totalQuests = cached.total;
        completedQuests = cached.completed;
        completionPercentage = totalQuests > 0
          ? Math.round((completedQuests / totalQuests) * 100)
          : 0;
      }

      this.weekDates.push({
        date: dateString,
        day: date.getDate(),
        is_today: isToday,
        is_selected: isSelected,
        is_future: isFuture,
        total_quests: totalQuests,
        completed_quests: completedQuests,
        completion_percentage: completionPercentage
      });
    }

    if (this.userId) {
      setTimeout(() => {
        this.preloadWeekData();
      }, 0);
    }
  }

  private weekProgressCache: { [dateKey: string]: { total: number, completed: number } } = {};

  updateWeekDateProgress() {
    if (!this.userId) return;

    this.weekDates.forEach((weekDate, index) => {
      if (weekDate.is_future) return;

      const date = new Date(weekDate.date);
      const dateKey = this.formatDate(date);

      if (this.weekProgressCache[dateKey]) {
        const cached = this.weekProgressCache[dateKey];
        this.weekDates[index].total_quests = cached.total;
        this.weekDates[index].completed_quests = cached.completed;
        this.weekDates[index].completion_percentage = cached.total > 0
          ? Math.round((cached.completed / cached.total) * 100)
          : 0;
        return;
      }

      if (this.questCache[dateKey]) {
        const cachedQuests = this.questCache[dateKey];
        const totalQuests = cachedQuests.length;
        const completedQuests = cachedQuests.filter(q => q.completed).length;

        this.weekProgressCache[dateKey] = {
          total: totalQuests,
          completed: completedQuests
        };

        this.weekDates[index].total_quests = totalQuests;
        this.weekDates[index].completed_quests = completedQuests;
        this.weekDates[index].completion_percentage = totalQuests > 0
          ? Math.round((completedQuests / totalQuests) * 100)
          : 0;
        return;
      }
    });

    this.preloadWeekData();
  }

  private filterQuestsForDate(quests: Quest[], date: Date): Quest[] {
    const dateObj = new Date(date);
    const dayOfWeek = dateObj.getDay(); 
    const djangoDayOfWeek = dayOfWeek === 0 ? 6 : dayOfWeek - 1; 
    const dayOfMonth = dateObj.getDate(); 


    const filteredQuests = quests.filter(quest => {

      if (!quest.active) {
        return false;
      }

      if (quest.created_at) {
        const createdDate = new Date(quest.created_at);
        createdDate.setHours(0, 0, 0, 0);
        dateObj.setHours(0, 0, 0, 0);

        if (dateObj < createdDate) {
          return false;
        }
      }

      if (quest.goal_period === 'day') {
        return true;
      }

      if (quest.goal_period === 'week') {
        if (!quest.task_days_of_week) {
          return true; 
        }

        let taskDays: any[] = [];
        if (typeof quest.task_days_of_week === 'string') {
          taskDays = quest.task_days_of_week.split(',').map(day => day.trim());
        } else if (Array.isArray(quest.task_days_of_week)) {
          taskDays = quest.task_days_of_week;
        }

        const dayNameShort = this.getDayNameShort(djangoDayOfWeek);
        const dayNameFull = this.getDayNameFull(djangoDayOfWeek);


        const isIncluded = taskDays.includes(djangoDayOfWeek) ||
                      taskDays.includes(djangoDayOfWeek.toString()) ||
                      taskDays.includes(dayNameShort) ||
                      taskDays.includes(dayNameFull);


        return isIncluded;
      }

      if (quest.goal_period === 'month') {
        if (!quest.task_days_of_month) return true; 

        let taskDays: any[] = [];
        if (typeof quest.task_days_of_month === 'string') {
          taskDays = quest.task_days_of_month.split(',').map(day => parseInt(day.trim()));
        } else if (Array.isArray(quest.task_days_of_month)) {
          taskDays = quest.task_days_of_month;
        }

        return taskDays.includes(dayOfMonth) ||
               taskDays.includes(dayOfMonth.toString());
      }

      return false;
    });
    return filteredQuests;
  }

  selectDate(dateString: string) {
    if (this.isLoadingData) {
      return;
    }

    this.isLoadingData = true;

    const date = new Date(dateString);
    this.selectedDate = date;

    this.weekDates.forEach(weekDate => {
      weekDate.is_selected = weekDate.date === dateString;
    });

    const formattedDate = this.formatDate(date);

    this.router.navigate(['/today'], {
      queryParams: {
        date: formattedDate,
        week_offset: this.weekOffset !== 0 ? this.weekOffset : null
      },
      replaceUrl: true
    });

    this.updateHeaderText();

    setTimeout(() => {
      this.loadData();
      this.isLoadingData = false;
    }, 10);
  }

  private isChangingWeek = false;

  changeWeek(direction: number) {
    if (this.isChangingWeek) {
      return;
    }

    this.isChangingWeek = true;

    this.weekOffset += direction;

    this.generateWeekDates();

    this.preloadWeekData();

    const dateParam = this.formatDate(this.selectedDate);
    this.router.navigate(['/today'], {
      queryParams: {
        date: dateParam,
        week_offset: this.weekOffset
      },
      replaceUrl: true
    });

    setTimeout(() => {
      this.isChangingWeek = false;
    }, 300);
  }

  private preloadWeekData() {
    if (!this.userId) return;

    this.questService.getQuests(this.userId!).pipe(
      take(1)
    ).subscribe(allQuests => {
      const dateObservables = this.weekDates
        .filter(weekDate => !weekDate.is_future)
        .map(weekDate => {
          const date = new Date(weekDate.date);
          const dateKey = this.formatDate(date);

          if (this.weekProgressCache[dateKey]) {
            return of({
              date: weekDate.date,
              progress: this.weekProgressCache[dateKey]
            });
          }

          const activeQuests = this.filterQuestsForDate(allQuests, date);

          if (activeQuests.length === 0) {
            const emptyProgress = { total: 0, completed: 0 };
            this.weekProgressCache[dateKey] = emptyProgress;
            return of({
              date: weekDate.date,
              progress: emptyProgress
            });
          }

          return this.questService.getQuestProgressForDate(this.userId!, date).pipe(
            take(1),
            map(progressList => {
              const questIds = activeQuests.map(q => q.id);
              const relevantProgress = progressList.filter(p => questIds.includes(p.quest_id));
              const completedQuests = relevantProgress.filter(p => p.completed).length;
              const totalQuests = activeQuests.length;

              const progress = {
                total: totalQuests,
                completed: completedQuests
              };

              this.weekProgressCache[dateKey] = progress;

              return {
                date: weekDate.date,
                progress
              };
            })
          );
        });

      forkJoin(dateObservables).subscribe(results => {
        results.forEach(result => {
          const index = this.weekDates.findIndex(wd => wd.date === result.date);
          if (index >= 0) {
            this.weekDates[index].total_quests = result.progress.total;
            this.weekDates[index].completed_quests = result.progress.completed;
            this.weekDates[index].completion_percentage = result.progress.total > 0
              ? Math.round((result.progress.completed / result.progress.total) * 100)
              : 0;
          }
        });
      });
    });
  }

  updateHeaderText() {
    const today = new Date();
    if (this.isSameDay(this.selectedDate, today)) {
      this.headerText = 'Today';
    } else if (this.isSameDay(this.selectedDate, new Date(today.setDate(today.getDate() - 1)))) {
      this.headerText = 'Yesterday';
    } else if (this.isSameDay(this.selectedDate, new Date(today.setDate(today.getDate() + 2)))) {
      this.headerText = 'Tomorrow';
    } else {
      this.headerText = this.selectedDate.toLocaleDateString('en-US', {
        weekday: 'short',
        day: 'numeric',
        month: 'short'
      });
    }
  }

  private togglingQuestIds: { [questId: string]: boolean } = {};

  async toggleQuest(quest: QuestDisplay) {
    if (!this.userId || !quest.id) return;

    if (this.togglingQuestIds[quest.id]) {
      return;
    }

    this.togglingQuestIds[quest.id] = true;

    try {
      delete this.togglingQuestIds[quest.id];
      return;
    } catch (error) {
      :`, error);
    } finally {
      delete this.togglingQuestIds[quest.id];
    }
  }

  private updatingQuestIds: { [questId: string]: boolean } = {};

  async updateQuestProgress(quest: QuestDisplay, event?: any) {
    if (!this.userId || !quest.id) return;

    if (this.updatingQuestIds[quest.id]) {
      return;
    }

    this.updatingQuestIds[quest.id] = true;

    try {
      const wasCompletedBefore = quest.completed;

      if (event) {
        const slider = event.target || (event.detail ? event.detail.value : null);
        this.updateSliderBackground(slider);

        const sliderQuestId = slider instanceof HTMLElement ? slider.getAttribute('data-quest-id') : null;
        if (sliderQuestId && sliderQuestId !== quest.id) {
          delete this.updatingQuestIds[quest.id];
          return;
        }

        let sliderValue = 0;
        if (event.detail && event.detail.value !== undefined) {
          sliderValue = event.detail.value;
        } else if (slider instanceof HTMLInputElement) {
          sliderValue = parseInt(slider.value);
        } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {
          const valueAttr = slider.getAttribute('value') || '0';
          sliderValue = parseInt(valueAttr);
        }

        quest.value_achieved = sliderValue;
        if (quest.quest_type === 'build') {
          quest.completed = sliderValue >= quest.goal_value;
        } else {
          quest.completed = sliderValue < quest.goal_value;
        }
      }

      const questCopy = { ...quest };

      const result = await this.questService.toggleQuestCompletion(
        this.userId,
        quest.id,
        this.selectedDate,
        quest.value_achieved,
        questCopy
      );

      quest.completed = result.completed;
      quest.value_achieved = result.value_achieved;

      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const selectedDate = new Date(this.selectedDate);
      selectedDate.setHours(0, 0, 0, 0);
      const isTodaySelected = selectedDate.getTime() === today.getTime();

      if (isTodaySelected) {

        let streak = result.streak;

        const isCompletedNow = quest.completed;

        if (wasCompletedBefore !== isCompletedNow) {
          if (isCompletedNow) {
            streak++;
          } else {
            streak = Math.max(0, streak - 1);
          }

          this.questService.updateQuestStreak(quest.id!, streak).subscribe({
            next: () => {

              const dateKey = this.formatDate(this.selectedDate);
              if (this.questCache[dateKey]) {
                const cachedQuestIndex = this.questCache[dateKey].findIndex(q => q.id === quest.id);
                if (cachedQuestIndex >= 0) {
                  this.questCache[dateKey][cachedQuestIndex].streak = streak;
                }
              }
            },
            error: (error: any) => 
          });
        }
      } else {

        this.questService.getQuest(quest.id!).subscribe(questDetails => {
          if (!questDetails) {
            return;
          }

          this.streakCalculator.calculateStreak(this.userId!, quest.id!)
            .then(calculatedStreak => {

              this.questService.updateQuestStreak(quest.id!, calculatedStreak).subscribe({
                next: () => {

                  const todayString = this.formatDate(today);
                  delete this.questCache[todayString];

                  const todayIndex = this.weekDates.findIndex(wd => wd.date === todayString);
                  if (todayIndex >= 0) {
                    delete this.weekProgressCache[todayString];
                    this.updateProgressRingForDate(todayString);
                  }
                },
                error: (error: any) => 
              });
            })
            .catch(error => {
            });
        });
      }

      this.updateQuestUI(quest);

      const dateKey = this.formatDate(this.selectedDate);
      if (this.questCache[dateKey]) {
        const cachedQuestIndex = this.questCache[dateKey].findIndex(q => q.id === quest.id);
        if (cachedQuestIndex >= 0) {
          this.questCache[dateKey][cachedQuestIndex] = { ...quest };
        }
      }

      delete this.weekProgressCache[dateKey];

      this.updateProgressRingForDate(dateKey);

      this.checkAllQuestsCompleted(this.quests);
    } catch (error) {
    } finally {
      delete this.updatingQuestIds[quest.id];
    }
  }

  private updateProgressRingForDate(dateKey: string) {
    const index = this.weekDates.findIndex(wd => wd.date === dateKey);
    if (index < 0) return;

    if (this.questCache[dateKey]) {
      const cachedQuests = this.questCache[dateKey];
      const totalQuests = cachedQuests.length;
      const completedQuests = cachedQuests.filter(q => q.completed).length;

      this.weekProgressCache[dateKey] = {
        total: totalQuests,
        completed: completedQuests
      };

      this.weekDates[index].total_quests = totalQuests;
      this.weekDates[index].completed_quests = completedQuests;
      this.weekDates[index].completion_percentage = totalQuests > 0
        ? Math.round((completedQuests / totalQuests) * 100)
        : 0;

      return;
    }

    if (this.userId) {
      const date = new Date(dateKey);

      this.questService.getQuestProgressForDate(this.userId, date).pipe(
        take(1)
      ).subscribe(progressList => {
        this.questService.getQuests(this.userId!).pipe(
          take(1)
        ).subscribe(quests => {
          const activeQuests = this.filterQuestsForDate(quests, date);

          const questIds = activeQuests.map(q => q.id);
          const relevantProgress = progressList.filter(p => questIds.includes(p.quest_id));
          const completedQuests = relevantProgress.filter(p => p.completed).length;
          const totalQuests = activeQuests.length;

          this.weekProgressCache[dateKey] = {
            total: totalQuests,
            completed: completedQuests
          };

          this.weekDates[index].total_quests = totalQuests;
          this.weekDates[index].completed_quests = completedQuests;
          this.weekDates[index].completion_percentage = totalQuests > 0
            ? Math.round((completedQuests / totalQuests) * 100)
            : 0;
        });
      });
    }
  }

  private updateQuestUI(quest: QuestDisplay) {
    const questElement = document.querySelector(`[data-quest-id="${quest.id}"]`);
    if (!questElement) {
      return;
    }

    if (quest.completed) {
      questElement.classList.add('completed');
    } else {
      questElement.classList.remove('completed');
    }

    const streakElements = questElement.querySelectorAll('.quest-streak');
    if (streakElements && streakElements.length > 0) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const selectedDate = new Date(this.selectedDate);
      selectedDate.setHours(0, 0, 0, 0);
      const isTodaySelected = selectedDate.getTime() === today.getTime();

      if (isTodaySelected) {
        const streakValue = quest.streak || 0;

        streakElements.forEach(element => {
          if (element.parentElement && element.parentElement.contains(element)) {
            (element as HTMLElement).style.display = 'block';
            element.textContent = `🔥${streakValue}d`;
          }
        });
      } else {
        streakElements.forEach(element => {
          if (element.parentElement && element.parentElement.contains(element)) {
            (element as HTMLElement).style.display = 'none';
            element.textContent = '';
          }
        });
      }
    }

    const progressText = questElement.querySelector('.progress-text');
    if (progressText) {
      const isTimeUnit = progressText.parentElement?.classList.contains('progress-time');
      const unitSuffix = isTimeUnit ? 'm' : '';
      const goalUnitSuffix = quest.goal_unit !== 'count' && !isTimeUnit ? ` ${quest.goal_unit}` : '';

      progressText.textContent = `${quest.value_achieved}${unitSuffix}/${quest.goal_value}${unitSuffix}${goalUnitSuffix}`;
    }
  }

  updateSliderBackground(slider: HTMLInputElement | EventTarget | null) {
    if (!slider) {
      return;
    }

    let sliderElement: HTMLElement;
    let sliderValue = 0;
    let minValue = 0;
    let maxValue = 100;
    let sliderQuestId = '';

    if (slider instanceof HTMLInputElement) {
      sliderElement = slider;
      sliderQuestId = slider.getAttribute('data-quest-id') || '';
      sliderValue = parseInt(slider.value);
      minValue = parseInt(slider.min);
      maxValue = parseInt(slider.max);
    } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {
      sliderElement = slider;
      sliderQuestId = slider.getAttribute('data-quest-id') || '';

      const valueAttr = slider.getAttribute('value') || '0';
      const minAttr = slider.getAttribute('min') || '0';
      const maxAttr = slider.getAttribute('max') || '100';

      sliderValue = parseInt(valueAttr);
      minValue = parseInt(minAttr);
      maxValue = parseInt(maxAttr);
    } else {
      return;
    }

    if (!sliderQuestId) {
      return;
    }

    const percentage = maxValue > minValue ?
      ((sliderValue - minValue) / (maxValue - minValue)) * 100 : 0;

    if (sliderElement.tagName === 'ION-RANGE') {
      sliderElement.style.setProperty('--progress-value', `${percentage}%`);
    } else {
      sliderElement.style.background =
        `linear-gradient(to right, #4169E1 0%, #4169E1 ${percentage}%, #2C2C2E ${percentage}%, #2C2C2E 100%)`;
    }

    sliderElement.setAttribute('data-current-value', sliderValue.toString());
  }

  private togglingSideQuestIds: { [questId: string]: boolean } = {};

  async toggleSideQuest(sideQuest: DailyQuest) {
    if (!this.userId || !sideQuest.id) return;

    if (this.togglingSideQuestIds[sideQuest.id]) {
      return;
    }

    this.togglingSideQuestIds[sideQuest.id] = true;

    try {
      const newValue = sideQuest.value_achieved === 0 ? sideQuest.current_quest.goal_value : 0;
      const newCompletedState = newValue === sideQuest.current_quest.goal_value;

      sideQuest.value_achieved = newValue;
      sideQuest.completed = newCompletedState;


      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const selectedDate = new Date(this.selectedDate);
      selectedDate.setHours(0, 0, 0, 0);
      const isToday = selectedDate.getTime() === today.getTime();

      if (!isToday) {
        delete this.togglingSideQuestIds[sideQuest.id];
        return;
      }

      this.updateSideQuestUI(sideQuest);

      try {
        const result = await this.sideQuestService.toggleSideQuestCompletion(
          sideQuest.id,
          this.userId,
          this.selectedDate 
        );


        sideQuest.completed = result.completed;
        sideQuest.value_achieved = result.value_achieved;
        sideQuest.streak = result.streak;

        this.updateSideQuestUI(sideQuest);

        const dateKey = this.formatDate(this.selectedDate);
        delete this.weekProgressCache[dateKey];

        this.updateProgressRingForDate(dateKey);

        this.checkAllQuestsCompleted(this.quests);

        delete this.togglingSideQuestIds[sideQuest.id];
      } catch (error) {

        sideQuest.value_achieved = sideQuest.value_achieved === 0 ? sideQuest.current_quest.goal_value : 0;
        sideQuest.completed = sideQuest.value_achieved === sideQuest.current_quest.goal_value;
        this.updateSideQuestUI(sideQuest);

        delete this.togglingSideQuestIds[sideQuest.id];
      }
    } catch (error) {
      delete this.togglingSideQuestIds[sideQuest.id];
    }
  }

  private updateSideQuestUI(sideQuest: DailyQuest) {
    const questElement = document.querySelector(`.daily-side-quest [data-quest-id="${sideQuest.id}"]`);
    if (!questElement) {
      return;
    }

    if (sideQuest.completed) {
      questElement.classList.add('completed');
    } else {
      questElement.classList.remove('completed');
    }

    const streakElements = questElement.querySelectorAll('.quest-streak');
    if (streakElements && streakElements.length > 0) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const selectedDate = new Date(this.selectedDate);
      selectedDate.setHours(0, 0, 0, 0);
      const isTodaySelected = selectedDate.getTime() === today.getTime();

      if (isTodaySelected) {
        const streakValue = sideQuest.streak || 0;

        streakElements.forEach(element => {
          if (element.parentElement && element.parentElement.contains(element)) {
            (element as HTMLElement).style.display = 'block';
            element.textContent = `🔥${streakValue}d`;
          }
        });
      } else {
        streakElements.forEach(element => {
          if (element.parentElement && element.parentElement.contains(element)) {
            (element as HTMLElement).style.display = 'none';
            element.textContent = '';
          }
        });
      }
    }

    const progressText = questElement.querySelector('.progress-text');
    if (progressText) {
      const goalUnit = sideQuest.current_quest.goal_unit !== 'count' ? ` ${sideQuest.current_quest.goal_unit}` : '';
      progressText.textContent = `${sideQuest.value_achieved}/${sideQuest.current_quest.goal_value}${goalUnit}`;
    }

    setTimeout(() => {
      if (questElement.parentElement) {
        const display = questElement.parentElement.style.display;
        questElement.parentElement.style.display = 'none';
        void questElement.parentElement.offsetHeight;
        questElement.parentElement.style.display = display;
      }
    }, 0);

  }

  openAddQuestModal(event: Event) {
    event.preventDefault();
    this.showAddQuestModal = true;
    this.newQuest = this.getEmptyQuest();
    this.selectedDaysOfWeek = [];
    this.selectedDaysOfMonth = [];
    this.hasHighPriorityQuest = false;
  }

  closeAddQuestModal() {
    this.showAddQuestModal = false;
    this.newQuest = this.getEmptyQuest();
    this.selectedDaysOfWeek = [];
    this.selectedDaysOfMonth = [];
    this.hasHighPriorityQuest = false;
  }

  async createQuest() {
    if (!this.userId || !this.newQuest.name || !this.newQuest.emoji || !this.newQuest.quest_type ||
        !this.newQuest.category || !this.newQuest.goal_value || !this.newQuest.goal_unit || !this.newQuest.goal_period) {
      return;
    }

    try {
      if (this.newQuest.goal_period === 'week' && this.selectedDaysOfWeek.length > 0) {
        this.newQuest.task_days_of_week = this.selectedDaysOfWeek.join(',');
      } else if (this.newQuest.goal_period === 'month' && this.selectedDaysOfMonth.length > 0) {
        this.newQuest.task_days_of_month = this.selectedDaysOfMonth.join(',');
      }

      const { data: userProfile, error: userError } = await this.supabaseService.getClient()
        .from('profiles')
        .select('id')
        .eq('id', this.userId)
        .single();

      if (userError || !userProfile) {
        throw new Error('User profile not found. Please ensure you are logged in.');
      }


      const questToCreate: Omit<Quest, 'id' | 'streak' | 'created_at'> = {
        name: this.newQuest.name || '',
        description: this.newQuest.description || '',
        quest_type: this.newQuest.quest_type || 'build',
        goal_value: this.newQuest.goal_value || 1,
        goal_unit: this.newQuest.goal_unit || 'count',
        goal_period: this.newQuest.goal_period || 'day',
        priority: this.newQuest.priority || 'basic',
        category: this.newQuest.category || 'strength',
        emoji: this.newQuest.emoji || '🎯',
        task_days_of_week: this.newQuest.task_days_of_week || '',
        task_days_of_month: this.newQuest.task_days_of_month || '',
        user_id: this.userId, 
        active: true
      };

      try {
        const questId = await this.questService.createQuest(questToCreate);

        if (this.newQuest.quest_type === 'quit') {

          await this.questService.toggleQuestCompletion(
            this.userId,
            questId,
            new Date(), 
            0, 
            { ...questToCreate, id: questId } as Quest
          );
        }

        const dateKey = this.formatDate(this.selectedDate);
        delete this.questCache[dateKey];
        delete this.weekProgressCache[dateKey];

        this.closeAddQuestModal();
        this.loadData();
      } catch (questError: any) {

        if (questError.message && questError.message.includes('foreign key constraint')) {
          alert('Database configuration issue detected. Please run the fix_quest_constraints.sql script in the Supabase SQL Editor to fix the foreign key constraints.');
        } else if (questError.message && questError.message.includes('fix_quest_constraints.sql')) {
          alert(questError.message);
        } else {
          alert(`Error creating quest: ${questError.message}`);
        }
      }
    } catch (error: any) {
      alert(`Error: ${error.message || 'Unknown error occurred'}`);
    }
  }

  updateDaysOfWeek(event: any, day: string) {
    let isChecked = false;

    if (event.detail !== undefined) {
      isChecked = event.detail.checked;
    } else if (event.target instanceof HTMLInputElement) {
      isChecked = event.target.checked;
    }

    if (isChecked) {
      this.selectedDaysOfWeek.push(day);
    } else {
      const index = this.selectedDaysOfWeek.indexOf(day);
      if (index !== -1) {
        this.selectedDaysOfWeek.splice(index, 1);
      }
    }
  }

  updateDaysOfMonth(event: any, day: number) {
    let isChecked = false;

    if (event.detail !== undefined) {
      isChecked = event.detail.checked;
    } else if (event.target instanceof HTMLInputElement) {
      isChecked = event.target.checked;
    }

    if (isChecked) {
      this.selectedDaysOfMonth.push(day);
    } else {
      const index = this.selectedDaysOfMonth.indexOf(day);
      if (index !== -1) {
        this.selectedDaysOfMonth.splice(index, 1);
      }
    }
  }

  updatePeriodDisplay() {
    this.selectedDaysOfWeek = [];
    this.selectedDaysOfMonth = [];
  }

  checkCategoryPriority(event?: any) {
    if (!this.userId || !this.newQuest.category) return;

    if (event && event.detail) {
      this.newQuest.category = event.detail.value;
    }

    this.questService.getQuests(this.userId).pipe(
      take(1),
      map(quests => {
        return quests.some(q =>
          q.category === this.newQuest.category &&
          q.priority === 'high' &&
          q.active
        );
      })
    ).subscribe({
      next: (hasHighPriority) => {
        this.hasHighPriorityQuest = hasHighPriority;

        if (hasHighPriority) {
          this.newQuest.priority = 'basic';
        }

      }
    });
  }

  checkAllQuestsCompleted(quests: QuestDisplay[]) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const selectedDate = new Date(this.selectedDate);
    selectedDate.setHours(0, 0, 0, 0);
    const isTodaySelected = selectedDate.getTime() === today.getTime();
    const todayStr = this.formatDate(today);

    if (!isTodaySelected || !this.currentUser) {
      return;
    }

    const celebrationShown = localStorage.getItem(`celebration_shown_${todayStr}`);
    if (celebrationShown) {
      return;
    }

    const allQuestsCompleted = quests.length > 0 && quests.every(quest => quest.completed);

    const sideQuestCompleted = !this.showSidequests || !this.dailyQuest || this.dailyQuest.completed;


    if (allQuestsCompleted && sideQuestCompleted && this.currentUser.show_celebration) {
      this.userService.getUserById(this.userId!).subscribe(userData => {
        if (userData) {
          this.currentUser = userData;
        }

        this.showCelebration = true;

        localStorage.setItem(`celebration_shown_${todayStr}`, 'true');

        if (!this.celebrationShownDates.includes(todayStr)) {
          this.celebrationShownDates.push(todayStr);
        }
      });
    }
  }

  closeCelebration() {
    this.showCelebration = false;
  }



  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  isSameDay(date1: Date, date2: Date): boolean {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  }

  getToday(): Date {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return today;
  }

  private getDayNameShort(djangoDayIndex: number): string {
    const dayMap = ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su'];
    return dayMap[djangoDayIndex];
  }

  private getDayNameFull(djangoDayIndex: number): string {
    const dayMap = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return dayMap[djangoDayIndex];
  }

  private getEmptyQuest(): Partial<Quest> {
    return {
      name: '',
      description: '',
      quest_type: 'build' as QuestType,
      goal_value: 1,
      goal_unit: 'count' as QuestGoalUnit,
      goal_period: 'day' as QuestPeriod,
      priority: 'basic' as QuestPriority, 
      category: '' as QuestCategory,
      emoji: '🎯'
    };
  }

}
