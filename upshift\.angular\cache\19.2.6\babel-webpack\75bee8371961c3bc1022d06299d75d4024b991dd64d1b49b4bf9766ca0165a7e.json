{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _SideQuestService;\nimport { map, of, from, switchMap, catchError, take } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./supabase.service\";\nimport * as i2 from \"./user.service\";\nimport * as i3 from \"@angular/common/http\";\nexport class SideQuestService {\n  constructor(supabaseService, userService, http) {\n    this.supabaseService = supabaseService;\n    this.userService = userService;\n    this.http = http;\n  }\n  getSideQuestPools() {\n    return from(this.supabaseService.getClient().from('daily_sidequest_pool').select('*').eq('active', true)).pipe(map(response => {\n      if (response.error) {\n        return [];\n      }\n      return response.data;\n    }), catchError(error => {\n      return of([]);\n    }));\n  }\n  getUserDailySideQuest(userId) {\n    return from(this.supabaseService.getClient().from('user_daily_sidequests').select('*').eq('user_id', userId)).pipe(map(response => {\n      if (response.error) {\n        return null;\n      }\n      return response.data.length > 0 ? response.data[0] : null;\n    }), catchError(error => {\n      return of(null);\n    }));\n  }\n  createUserDailySideQuest(userId) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          data: poolQuests,\n          error: poolError\n        } = yield _this.supabaseService.getClient().from('daily_sidequest_pool').select('*').eq('active', true);\n        if (poolError) {\n          throw new Error('Error getting side quest pool: ' + poolError.message);\n        }\n        if (!poolQuests || poolQuests.length === 0) {\n          throw new Error('No active side quests available');\n        }\n        const randomIndex = Math.floor(Math.random() * poolQuests.length);\n        const randomQuest = poolQuests[randomIndex];\n        const today = new Date();\n        const dateString = today.toISOString().split('T')[0];\n        const newSideQuest = {\n          user_id: userId,\n          current_quest_id: randomQuest.id,\n          streak: 0,\n          date_assigned: dateString,\n          completed: false,\n          value_achieved: 0,\n          category: randomQuest.category,\n          emoji: randomQuest.emoji,\n          last_completed_date: null\n        };\n        const {\n          data: insertData,\n          error: insertError\n        } = yield _this.supabaseService.getClient().from('user_daily_sidequests').insert(newSideQuest).select('id').single();\n        if (insertError) {\n          throw new Error('Error creating side quest: ' + insertError.message);\n        }\n        return insertData.id;\n      } catch (error) {\n        throw error;\n      }\n    })();\n  }\n  toggleSideQuestCompletion(sideQuestId, userId, selectedDate) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          data: sideQuest,\n          error: questError\n        } = yield _this2.supabaseService.getClient().from('user_daily_sidequests').select('*').eq('id', sideQuestId).single();\n        if (questError || !sideQuest) {\n          throw new Error('Side quest not found');\n        }\n        const wasCompleted = sideQuest.completed;\n        const {\n          data: questPool,\n          error: poolError\n        } = yield _this2.supabaseService.getClient().from('daily_sidequest_pool').select('*').eq('id', sideQuest.current_quest_id).single();\n        if (poolError || !questPool) {\n          throw new Error('Side quest pool item not found');\n        }\n        const isCompleted = !wasCompleted;\n        const today = selectedDate || new Date();\n        const yesterday = new Date(today);\n        yesterday.setDate(yesterday.getDate() - 1);\n        let newStreak = sideQuest.streak;\n        let newLastCompletedDate = sideQuest.last_completed_date;\n        let newValueAchieved = sideQuest.value_achieved;\n        if (isCompleted) {\n          newValueAchieved = questPool.goal_value;\n          if (sideQuest.last_completed_date) {\n            const lastCompletedDate = new Date(sideQuest.last_completed_date);\n            const lastCompletedDay = lastCompletedDate.getDate();\n            const yesterdayDay = yesterday.getDate();\n            if (lastCompletedDay === yesterdayDay) {\n              newStreak += 1;\n            } else {\n              newStreak = 1;\n            }\n          } else {\n            newStreak = 1;\n          }\n          newLastCompletedDate = today;\n        } else {\n          newValueAchieved = 0;\n          if (sideQuest.last_completed_date && new Date(sideQuest.last_completed_date).getDate() === today.getDate()) {\n            if (newStreak > 1) {\n              newStreak -= 1;\n              newLastCompletedDate = yesterday;\n            } else {\n              newStreak = 0;\n              newLastCompletedDate = yesterday;\n            }\n          }\n        }\n        const {\n          error: updateError\n        } = yield _this2.supabaseService.getClient().from('user_daily_sidequests').update({\n          completed: isCompleted,\n          value_achieved: newValueAchieved,\n          streak: newStreak,\n          last_completed_date: newLastCompletedDate\n        }).eq('id', sideQuestId);\n        if (updateError) {\n          throw new Error('Error updating side quest: ' + updateError.message);\n        }\n        if (isCompleted !== wasCompleted) {\n          yield _this2.updateUserXP(userId, sideQuest.category, isCompleted, wasCompleted);\n        }\n        if (newStreak >= 7) {\n          yield _this2.updateSideQuestBadges(userId, newStreak);\n        }\n        const actualToday = new Date();\n        actualToday.setHours(0, 0, 0, 0);\n        if (selectedDate && selectedDate < actualToday) {\n          const {\n            data: todaySideQuest,\n            error: todaySideQuestError\n          } = yield _this2.supabaseService.getClient().from('user_daily_sidequests').select('*').eq('user_id', userId).single();\n          if (todaySideQuestError || !todaySideQuest) {} else {\n            if (todaySideQuest.id !== sideQuestId) {\n              yield _this2.checkMissedDays(todaySideQuest.id);\n            }\n          }\n        }\n        return {\n          completed: isCompleted,\n          value_achieved: newValueAchieved,\n          streak: newStreak,\n          goal_value: questPool.goal_value,\n          goal_unit: questPool.goal_unit\n        };\n      } catch (error) {\n        throw error;\n      }\n    })();\n  }\n  checkMissedDays(sideQuestId) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!sideQuestId) {\n        return;\n      }\n      try {\n        const {\n          data: sideQuest,\n          error: questError\n        } = yield _this3.supabaseService.getClient().from('user_daily_sidequests').select('*').eq('id', sideQuestId).single();\n        if (questError || !sideQuest) {\n          return;\n        }\n        const today = new Date();\n        const yesterday = new Date(today);\n        yesterday.setDate(yesterday.getDate() - 1);\n        const lastCompletedDate = sideQuest.last_completed_date ? new Date(sideQuest.last_completed_date) : null;\n        if (lastCompletedDate) {\n          lastCompletedDate.setHours(0, 0, 0, 0);\n        }\n        yesterday.setHours(0, 0, 0, 0);\n        const missedDay = !lastCompletedDate || lastCompletedDate < yesterday && lastCompletedDate.toISOString().split('T')[0] !== yesterday.toISOString().split('T')[0];\n        if (missedDay) {\n          const {\n            error: updateError\n          } = yield _this3.supabaseService.getClient().from('user_daily_sidequests').update({\n            streak: 0,\n            last_completed_date: yesterday.toISOString().split('T')[0]\n          }).eq('id', sideQuestId);\n          if (updateError) {\n            return;\n          }\n        } else {}\n      } catch (error) {}\n    })();\n  }\n  assignNewQuest(sideQuestId) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (!sideQuestId) {\n        return;\n      }\n      try {\n        const {\n          data: sideQuest,\n          error: questError\n        } = yield _this4.supabaseService.getClient().from('user_daily_sidequests').select('*').eq('id', sideQuestId).single();\n        if (questError || !sideQuest) {\n          return;\n        }\n        const today = new Date();\n        if (new Date(sideQuest.date_assigned).getDate() === today.getDate()) {\n          return;\n        }\n        yield _this4.checkMissedDays(sideQuestId);\n        const {\n          data: poolQuests,\n          error: poolError\n        } = yield _this4.supabaseService.getClient().from('daily_sidequest_pool').select('*').eq('active', true).neq('id', sideQuest.current_quest_id);\n        if (poolError) {\n          return;\n        }\n        if (!poolQuests || poolQuests.length === 0) {\n          return;\n        }\n        const randomIndex = Math.floor(Math.random() * poolQuests.length);\n        const randomQuest = poolQuests[randomIndex];\n        const {\n          error: updateError\n        } = yield _this4.supabaseService.getClient().from('user_daily_sidequests').update({\n          current_quest_id: randomQuest.id,\n          date_assigned: today,\n          completed: false,\n          value_achieved: 0,\n          category: randomQuest.category,\n          emoji: randomQuest.emoji\n        }).eq('id', sideQuestId);\n        if (updateError) {\n          return;\n        }\n      } catch (error) {}\n    })();\n  }\n  updateUserXP(userId, category, isCompleted, wasCompleted) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (isCompleted === wasCompleted) {\n          return;\n        }\n        const xpField = `${category}_xp`;\n        const xpValue = 2;\n        try {\n          let updateData = {};\n          if (isCompleted && !wasCompleted) {\n            const {\n              data: userData,\n              error: userError\n            } = yield _this5.supabaseService.getClient().from('profiles').select(`${xpField}`).eq('id', userId).single();\n            if (userError) {\n              return;\n            }\n            const currentXP = userData[xpField] || 0;\n            const newXP = currentXP + xpValue;\n            updateData = {\n              [xpField]: newXP\n            };\n          } else if (!isCompleted && wasCompleted) {\n            const {\n              data: userData,\n              error: userError\n            } = yield _this5.supabaseService.getClient().from('profiles').select(`${xpField}`).eq('id', userId).single();\n            if (userError) {\n              return;\n            }\n            const currentXP = userData[xpField] || 0;\n            const newXP = Math.max(0, currentXP - xpValue);\n            updateData = {\n              [xpField]: newXP\n            };\n          }\n          if (Object.keys(updateData).length > 0) {\n            const {\n              error: updateError\n            } = yield _this5.supabaseService.getClient().from('profiles').update(updateData).eq('id', userId);\n            if (updateError) {\n              return;\n            }\n            yield _this5.userService.checkAndLevelUp(userId);\n          }\n        } catch (error) {}\n      } catch (error) {}\n    })();\n  }\n  updateSideQuestBadges(userId, streak) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          data: badges,\n          error: badgesError\n        } = yield _this6.supabaseService.getClient().from('user_badges').select('*').eq('user_id', userId);\n        if (badgesError) {\n          return;\n        }\n        if (!badges || badges.length === 0) {\n          return;\n        }\n        const badgeDoc = badges[0];\n        const updates = {};\n        if (streak >= 7) {\n          updates.badge_sidequest_streak_7_days = true;\n        }\n        if (streak >= 30) {\n          updates.badge_sidequest_streak_30_days = true;\n        }\n        if (streak >= 100) {\n          updates.badge_sidequest_streak_100_days = true;\n        }\n        if (streak >= 365) {\n          updates.badge_sidequest_streak_365_days = true;\n        }\n        if (Object.keys(updates).length > 0) {\n          const {\n            error: updateError\n          } = yield _this6.supabaseService.getClient().from('user_badges').update(updates).eq('id', badgeDoc.id);\n          if (updateError) {\n            return;\n          }\n        } else {}\n      } catch (error) {}\n    })();\n  }\n  isSameDay(date1, date2) {\n    return date1.getFullYear() === date2.getFullYear() && date1.getMonth() === date2.getMonth() && date1.getDate() === date2.getDate();\n  }\n  getOrCreateDailySideQuest(userId) {\n    const today = new Date();\n    return from(this.supabaseService.getClient().from('user_daily_sidequests').select('*').eq('user_id', userId)).pipe(take(1), map(response => {\n      if (response.error) {\n        return null;\n      }\n      return response.data.length > 0 ? response.data[0] : null;\n    }), switchMap(existingSideQuest => {\n      if (existingSideQuest) {\n        const assignedDate = new Date(existingSideQuest.date_assigned);\n        if (assignedDate.getDate() !== today.getDate() || assignedDate.getMonth() !== today.getMonth() || assignedDate.getFullYear() !== today.getFullYear()) {\n          return from(this.assignNewQuest(existingSideQuest.id)).pipe(switchMap(() => from(this.supabaseService.getClient().from('user_daily_sidequests').select('*').eq('user_id', userId)).pipe(take(1), map(response => {\n            if (response.error) {\n              return null;\n            }\n            return response.data.length > 0 ? response.data[0] : null;\n          }))));\n        }\n        return from(this.checkMissedDays(existingSideQuest.id)).pipe(switchMap(() => of(existingSideQuest)));\n      } else {\n        return from(this.createUserDailySideQuest(userId)).pipe(switchMap(() => from(this.supabaseService.getClient().from('user_daily_sidequests').select('*').eq('user_id', userId)).pipe(take(1), map(response => {\n          if (response.error) {\n            return null;\n          }\n          return response.data.length > 0 ? response.data[0] : null;\n        }))));\n      }\n    }), catchError(error => {\n      return of(null);\n    }));\n  }\n  recalculateSideQuestStreak(userId, _selectedDate) {\n    return from(this.supabaseService.getClient().from('user_daily_sidequests').select('*').eq('user_id', userId).order('date_assigned', {\n      ascending: false\n    }).limit(1)).pipe(map(response => {\n      if (response.error) {\n        return null;\n      }\n      return response.data.length > 0 ? response.data[0] : null;\n    }), switchMap(sideQuest => {\n      if (!sideQuest) {\n        return of(undefined);\n      }\n      return from(this.checkMissedDays(sideQuest.id)).pipe(map(() => undefined));\n    }), catchError(error => {\n      return of(undefined);\n    }));\n  }\n  ensureUserHasDailySideQuests(userId) {\n    const today = new Date();\n    return from(this.supabaseService.getClient().from('user_daily_sidequests').select('*, daily_sidequest_pool(*)').eq('user_id', userId).order('date_assigned', {\n      ascending: false\n    }).limit(1)).pipe(switchMap(response => {\n      if (response.error) {\n        return of([]);\n      }\n      if (response.data && response.data.length > 0) {\n        const sideQuest = response.data[0];\n        return from(this.checkMissedDays(sideQuest.id)).pipe(switchMap(() => {\n          const assignedDate = new Date(sideQuest.date_assigned);\n          if (assignedDate.getDate() !== today.getDate() || assignedDate.getMonth() !== today.getMonth() || assignedDate.getFullYear() !== today.getFullYear()) {\n            return from(this.assignNewQuest(sideQuest.id)).pipe(switchMap(() => from(this.supabaseService.getClient().from('user_daily_sidequests').select('*, daily_sidequest_pool(*)').eq('user_id', userId).order('date_assigned', {\n              ascending: false\n            }).limit(1)).pipe(map(updatedResponse => {\n              if (updatedResponse.error) {\n                return [];\n              }\n              return updatedResponse.data;\n            }))));\n          }\n          return of([sideQuest]);\n        }));\n      }\n      return from(this.createUserDailySideQuest(userId)).pipe(switchMap(newSideQuestId => {\n        return from(this.supabaseService.getClient().from('user_daily_sidequests').select('*, daily_sidequest_pool(*)').eq('user_id', userId).order('date_assigned', {\n          ascending: false\n        }).limit(1)).pipe(map(newResponse => {\n          if (newResponse.error) {\n            return [];\n          }\n          return newResponse.data;\n        }));\n      }), catchError(error => {\n        return of([]);\n      }));\n    }));\n  }\n  importSideQuestsFromJson() {\n    return this.http.get('assets/data/sidequest-pool.json').pipe(switchMap(sideQuests => {\n      if (!sideQuests || sideQuests.length === 0) {\n        return of(false);\n      }\n      return from(this.supabaseService.getClient().from('daily_sidequest_pool').select('id', {\n        count: 'exact'\n      })).pipe(switchMap(response => {\n        if (response.error) {\n          return of(false);\n        }\n        if (response.data && response.data.length > 0) {\n          return of(false);\n        }\n        const questsWithActive = sideQuests.map(quest => ({\n          ...quest,\n          active: true\n        }));\n        return from(this.supabaseService.getClient().from('daily_sidequest_pool').insert(questsWithActive)).pipe(map(insertResponse => {\n          if (insertResponse.error) {\n            return false;\n          }\n          return true;\n        }), catchError(error => {\n          return of(false);\n        }));\n      }));\n    }), catchError(error => {\n      return of(false);\n    }));\n  }\n}\n_SideQuestService = SideQuestService;\n_SideQuestService.ɵfac = function SideQuestService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _SideQuestService)(i0.ɵɵinject(i1.SupabaseService), i0.ɵɵinject(i2.UserService), i0.ɵɵinject(i3.HttpClient));\n};\n_SideQuestService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _SideQuestService,\n  factory: _SideQuestService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "names": ["map", "of", "from", "switchMap", "catchError", "take", "SideQuestService", "constructor", "supabaseService", "userService", "http", "getSideQuestPools", "getClient", "select", "eq", "pipe", "response", "error", "data", "getUserDailySideQuest", "userId", "length", "createUserDailySideQuest", "_this", "_asyncToGenerator", "poolQuests", "poolError", "Error", "message", "randomIndex", "Math", "floor", "random", "randomQuest", "today", "Date", "dateString", "toISOString", "split", "newSideQuest", "user_id", "current_quest_id", "id", "streak", "date_assigned", "completed", "value_achieved", "category", "emoji", "last_completed_date", "insertData", "insertError", "insert", "single", "toggleSideQuestCompletion", "sideQuestId", "selectedDate", "_this2", "sideQuest", "questError", "wasCompleted", "questPool", "isCompleted", "yesterday", "setDate", "getDate", "newStreak", "newLastCompletedDate", "newValueAchieved", "goal_value", "lastCompletedDate", "lastCompletedDay", "yesterdayDay", "updateError", "update", "updateUserXP", "updateSideQuestBadges", "actualToday", "setHours", "todaySideQuest", "todaySideQuestError", "checkMissedDays", "goal_unit", "_this3", "missedDay", "assignNewQuest", "_this4", "neq", "_this5", "xpField", "xpValue", "updateData", "userData", "userError", "currentXP", "newXP", "max", "Object", "keys", "checkAndLevelUp", "_this6", "badges", "badgesError", "badgeDoc", "updates", "badge_sidequest_streak_7_days", "badge_sidequest_streak_30_days", "badge_sidequest_streak_100_days", "badge_sidequest_streak_365_days", "isSameDay", "date1", "date2", "getFullYear", "getMonth", "getOrCreateDailySideQuest", "existingSideQuest", "assignedDate", "recalculateSideQuestStreak", "_selectedDate", "order", "ascending", "limit", "undefined", "ensureUserHasDailySideQuests", "updatedResponse", "newSideQuestId", "newResponse", "importSideQuestsFromJson", "get", "sideQuests", "count", "questsWithActive", "quest", "active", "insertResponse", "i0", "ɵɵinject", "i1", "SupabaseService", "i2", "UserService", "i3", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\services\\sidequest.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { DailySideQuestPool, UserDailySideQuest } from '../models/sidequest.model';\r\nimport { Observable, map, of, from, switchMap, catchError, take } from 'rxjs';\r\nimport { UserService } from './user.service';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { SupabaseService } from './supabase.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class SideQuestService {\r\n  constructor(\r\n    private supabaseService: SupabaseService,\r\n    private userService: UserService,\r\n    private http: HttpClient\r\n  ) {}\r\n\r\n  getSideQuestPools(): Observable<DailySideQuestPool[]> {\r\n\r\n\r\n    return from(\r\n      this.supabaseService.getClient()\r\n        .from('daily_sidequest_pool')\r\n        .select('*')\r\n        .eq('active', true)\r\n    ).pipe(\r\n      map(response => {\r\n        if (response.error) {\r\n          return [];\r\n        }\r\n\r\n\r\n        return response.data as DailySideQuestPool[];\r\n      }),\r\n      catchError(error => {\r\n        return of([]);\r\n      })\r\n    );\r\n  }\r\n\r\n  getUserDailySideQuest(userId: string): Observable<UserDailySideQuest | null> {\r\n\r\n\r\n    return from(\r\n      this.supabaseService.getClient()\r\n        .from('user_daily_sidequests')\r\n        .select('*')\r\n        .eq('user_id', userId)\r\n    ).pipe(\r\n      map(response => {\r\n        if (response.error) {\r\n          return null;\r\n        }\r\n\r\n\r\n        return response.data.length > 0 ? response.data[0] as UserDailySideQuest : null;\r\n      }),\r\n      catchError(error => {\r\n        return of(null);\r\n      })\r\n    );\r\n  }\r\n\r\n  async createUserDailySideQuest(userId: string): Promise<string> {\r\n\r\n\r\n    try {\r\n      const { data: poolQuests, error: poolError } = await this.supabaseService.getClient()\r\n        .from('daily_sidequest_pool')\r\n        .select('*')\r\n        .eq('active', true);\r\n\r\n      if (poolError) {\r\n\r\n        throw new Error('Error getting side quest pool: ' + poolError.message);\r\n      }\r\n\r\n      if (!poolQuests || poolQuests.length === 0) {\r\n        throw new Error('No active side quests available');\r\n      }\r\n\r\n\r\n\r\n      const randomIndex = Math.floor(Math.random() * poolQuests.length);\r\n      const randomQuest = poolQuests[randomIndex];\r\n\r\n\r\n      const today = new Date();\r\n      const dateString = today.toISOString().split('T')[0];\r\n\r\n      const newSideQuest: Omit<UserDailySideQuest, 'id'> = {\r\n        user_id: userId,\r\n        current_quest_id: randomQuest.id,\r\n        streak: 0,\r\n        date_assigned: dateString,\r\n        completed: false,\r\n        value_achieved: 0,\r\n        category: randomQuest.category,\r\n        emoji: randomQuest.emoji,\r\n        last_completed_date: null\r\n      };\r\n\r\n\r\n\r\n      const { data: insertData, error: insertError } = await this.supabaseService.getClient()\r\n        .from('user_daily_sidequests')\r\n        .insert(newSideQuest)\r\n        .select('id')\r\n        .single();\r\n\r\n      if (insertError) {\r\n        throw new Error('Error creating side quest: ' + insertError.message);\r\n      }\r\n\r\n\r\n      return insertData.id;\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async toggleSideQuestCompletion(sideQuestId: string, userId: string, selectedDate?: Date): Promise<{\r\n    completed: boolean;\r\n    value_achieved: number;\r\n    streak: number;\r\n    goal_value: number;\r\n    goal_unit: string;\r\n  }> {\r\n\r\n\r\n    try {\r\n      const { data: sideQuest, error: questError } = await this.supabaseService.getClient()\r\n        .from('user_daily_sidequests')\r\n        .select('*')\r\n        .eq('id', sideQuestId)\r\n        .single();\r\n\r\n      if (questError || !sideQuest) {\r\n        throw new Error('Side quest not found');\r\n      }\r\n\r\n      const wasCompleted = sideQuest.completed;\r\n\r\n      const { data: questPool, error: poolError } = await this.supabaseService.getClient()\r\n        .from('daily_sidequest_pool')\r\n        .select('*')\r\n        .eq('id', sideQuest.current_quest_id)\r\n        .single();\r\n\r\n      if (poolError || !questPool) {\r\n        throw new Error('Side quest pool item not found');\r\n      }\r\n\r\n\r\n\r\n      const isCompleted = !wasCompleted;\r\n      const today = selectedDate || new Date();\r\n      const yesterday = new Date(today);\r\n      yesterday.setDate(yesterday.getDate() - 1);\r\n\r\n      let newStreak = sideQuest.streak;\r\n      let newLastCompletedDate = sideQuest.last_completed_date;\r\n      let newValueAchieved = sideQuest.value_achieved;\r\n\r\n      if (isCompleted) {\r\n        newValueAchieved = questPool.goal_value;\r\n\r\n        if (sideQuest.last_completed_date) {\r\n          const lastCompletedDate = new Date(sideQuest.last_completed_date);\r\n          const lastCompletedDay = lastCompletedDate.getDate();\r\n          const yesterdayDay = yesterday.getDate();\r\n\r\n          if (lastCompletedDay === yesterdayDay) {\r\n            newStreak += 1;\r\n          } else {\r\n            newStreak = 1;\r\n          }\r\n        } else {\r\n          newStreak = 1;\r\n        }\r\n\r\n        newLastCompletedDate = today;\r\n      } else {\r\n        newValueAchieved = 0;\r\n\r\n        if (sideQuest.last_completed_date && new Date(sideQuest.last_completed_date).getDate() === today.getDate()) {\r\n          if (newStreak > 1) {\r\n            newStreak -= 1;\r\n            newLastCompletedDate = yesterday;\r\n          } else {\r\n            newStreak = 0;\r\n            newLastCompletedDate = yesterday;  \n          }\r\n        }\r\n      }\r\n\r\n      const { error: updateError } = await this.supabaseService.getClient()\r\n        .from('user_daily_sidequests')\r\n        .update({\r\n          completed: isCompleted,\r\n          value_achieved: newValueAchieved,\r\n          streak: newStreak,\r\n          last_completed_date: newLastCompletedDate\r\n        })\r\n        .eq('id', sideQuestId);\r\n\r\n      if (updateError) {\r\n        throw new Error('Error updating side quest: ' + updateError.message);\r\n      }\r\n\r\n\r\n      if (isCompleted !== wasCompleted) {\r\n        await this.updateUserXP(userId, sideQuest.category, isCompleted, wasCompleted);\r\n      }\r\n\r\n      if (newStreak >= 7) {\r\n        await this.updateSideQuestBadges(userId, newStreak);\r\n      }\r\n\r\n      const actualToday = new Date();\r\n      actualToday.setHours(0, 0, 0, 0);\r\n\r\n      if (selectedDate && selectedDate < actualToday) {\r\n\r\n        const { data: todaySideQuest, error: todaySideQuestError } = await this.supabaseService.getClient()\r\n          .from('user_daily_sidequests')\r\n          .select('*')\r\n          .eq('user_id', userId)\r\n          .single();\r\n\r\n        if (todaySideQuestError || !todaySideQuest) {\r\n        } else {\r\n          if (todaySideQuest.id !== sideQuestId) {\r\n\r\n            await this.checkMissedDays(todaySideQuest.id);\r\n          }\r\n        }\r\n      }\r\n\r\n      return {\r\n        completed: isCompleted,\r\n        value_achieved: newValueAchieved,\r\n        streak: newStreak,\r\n        goal_value: questPool.goal_value,\r\n        goal_unit: questPool.goal_unit\r\n      };\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async checkMissedDays(sideQuestId: string | undefined): Promise<void> {\r\n\r\n    if (!sideQuestId) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const { data: sideQuest, error: questError } = await this.supabaseService.getClient()\r\n        .from('user_daily_sidequests')\r\n        .select('*')\r\n        .eq('id', sideQuestId)\r\n        .single();\r\n\r\n      if (questError || !sideQuest) {\r\n        return;\r\n      }\r\n\r\n      const today = new Date();\r\n      const yesterday = new Date(today);\r\n      yesterday.setDate(yesterday.getDate() - 1);\r\n\r\n      const lastCompletedDate = sideQuest.last_completed_date ? new Date(sideQuest.last_completed_date) : null;\r\n\r\n      if (lastCompletedDate) {\r\n        lastCompletedDate.setHours(0, 0, 0, 0);\r\n      }\r\n      yesterday.setHours(0, 0, 0, 0);\r\n\r\n      const missedDay = !lastCompletedDate ||\r\n                       (lastCompletedDate < yesterday &&\r\n                        lastCompletedDate.toISOString().split('T')[0] !== yesterday.toISOString().split('T')[0]);\r\n\r\n\r\n      if (missedDay) {\r\n\r\n\r\n        const { error: updateError } = await this.supabaseService.getClient()\r\n          .from('user_daily_sidequests')\r\n          .update({\r\n            streak: 0,\r\n            last_completed_date: yesterday.toISOString().split('T')[0]  \n          })\r\n          .eq('id', sideQuestId);\r\n\r\n        if (updateError) {\r\n          return;\r\n        }\r\n\r\n      } else {\r\n      }\r\n    } catch (error) {\r\n    }\r\n  }\r\n\r\n  async assignNewQuest(sideQuestId: string | undefined): Promise<void> {\r\n\r\n    if (!sideQuestId) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const { data: sideQuest, error: questError } = await this.supabaseService.getClient()\r\n        .from('user_daily_sidequests')\r\n        .select('*')\r\n        .eq('id', sideQuestId)\r\n        .single();\r\n\r\n      if (questError || !sideQuest) {\r\n        return;\r\n      }\r\n\r\n      const today = new Date();\r\n\r\n      if (new Date(sideQuest.date_assigned).getDate() === today.getDate()) {\r\n        return;\r\n      }\r\n\r\n      await this.checkMissedDays(sideQuestId);\r\n\r\n      const { data: poolQuests, error: poolError } = await this.supabaseService.getClient()\r\n        .from('daily_sidequest_pool')\r\n        .select('*')\r\n        .eq('active', true)\r\n        .neq('id', sideQuest.current_quest_id);\r\n\r\n      if (poolError) {\r\n        return;\r\n      }\r\n\r\n      if (!poolQuests || poolQuests.length === 0) {\r\n        return;\r\n      }\r\n\r\n\r\n      const randomIndex = Math.floor(Math.random() * poolQuests.length);\r\n      const randomQuest = poolQuests[randomIndex];\r\n\r\n      const { error: updateError } = await this.supabaseService.getClient()\r\n        .from('user_daily_sidequests')\r\n        .update({\r\n          current_quest_id: randomQuest.id,\r\n          date_assigned: today,\r\n          completed: false,\r\n          value_achieved: 0,\r\n          category: randomQuest.category,\r\n          emoji: randomQuest.emoji\r\n        })\r\n        .eq('id', sideQuestId);\r\n\r\n      if (updateError) {\r\n        return;\r\n      }\r\n\r\n    } catch (error) {\r\n    }\r\n  }\r\n\r\n  private async updateUserXP(\r\n    userId: string,\r\n    category: string,\r\n    isCompleted: boolean,\r\n    wasCompleted: boolean\r\n  ): Promise<void> {\r\n\r\n    try {\r\n      if (isCompleted === wasCompleted) {\r\n        return;\r\n      }\r\n\r\n\r\n      const xpField = `${category}_xp`;\r\n\r\n      const xpValue = 2;\r\n\r\n      try {\r\n        let updateData = {};\r\n\r\n        if (isCompleted && !wasCompleted) {\r\n\r\n          const { data: userData, error: userError } = await this.supabaseService.getClient()\r\n            .from('profiles')\r\n            .select(`${xpField}`)\r\n            .eq('id', userId)\r\n            .single();\r\n\r\n          if (userError) {\r\n            return;\r\n          }\r\n          const currentXP = userData[xpField] || 0;\r\n          const newXP = currentXP + xpValue;\r\n\r\n          updateData = { [xpField]: newXP };\r\n\r\n        } else if (!isCompleted && wasCompleted) {\r\n\r\n          const { data: userData, error: userError } = await this.supabaseService.getClient()\r\n            .from('profiles')\r\n            .select(`${xpField}`)\r\n            .eq('id', userId)\r\n            .single();\r\n\r\n          if (userError) {\r\n            return;\r\n          }\r\n\r\n          const currentXP = userData[xpField] || 0;\r\n          const newXP = Math.max(0, currentXP - xpValue);\r\n\r\n          updateData = { [xpField]: newXP };\r\n        }\r\n\r\n        if (Object.keys(updateData).length > 0) {\r\n          const { error: updateError } = await this.supabaseService.getClient()\r\n            .from('profiles')\r\n            .update(updateData)\r\n            .eq('id', userId);\r\n\r\n          if (updateError) {\r\n            return;\r\n          }\r\n\r\n\r\n          await this.userService.checkAndLevelUp(userId);\r\n\r\n        }\r\n      } catch (error) {\r\n      }\r\n    } catch (error) {\r\n    }\r\n  }\r\n\r\n  private async updateSideQuestBadges(userId: string, streak: number): Promise<void> {\r\n\r\n    try {\r\n      const { data: badges, error: badgesError } = await this.supabaseService.getClient()\r\n        .from('user_badges')\r\n        .select('*')\r\n        .eq('user_id', userId);\r\n\r\n      if (badgesError) {\r\n        return;\r\n      }\r\n\r\n      if (!badges || badges.length === 0) {\r\n        return;\r\n      }\r\n\r\n      const badgeDoc = badges[0];\r\n\r\n      const updates: any = {};\r\n\r\n      if (streak >= 7) {\r\n        updates.badge_sidequest_streak_7_days = true;\r\n      }\r\n      if (streak >= 30) {\r\n        updates.badge_sidequest_streak_30_days = true;\r\n      }\r\n      if (streak >= 100) {\r\n        updates.badge_sidequest_streak_100_days = true;\r\n      }\r\n      if (streak >= 365) {\r\n        updates.badge_sidequest_streak_365_days = true;\r\n      }\r\n\r\n      if (Object.keys(updates).length > 0) {\r\n\r\n        const { error: updateError } = await this.supabaseService.getClient()\r\n          .from('user_badges')\r\n          .update(updates)\r\n          .eq('id', badgeDoc.id);\r\n\r\n        if (updateError) {\r\n          return;\r\n        }\r\n\r\n      } else {\r\n      }\r\n    } catch (error) {\r\n    }\r\n  }\r\n\r\n  private isSameDay(date1: Date, date2: Date): boolean {\r\n    return date1.getFullYear() === date2.getFullYear() &&\r\n           date1.getMonth() === date2.getMonth() &&\r\n           date1.getDate() === date2.getDate();\r\n  }\r\n\r\n  getOrCreateDailySideQuest(userId: string): Observable<UserDailySideQuest | null> {\r\n    const today = new Date();\r\n\r\n    return from(\r\n      this.supabaseService.getClient()\r\n        .from('user_daily_sidequests')\r\n        .select('*')\r\n        .eq('user_id', userId)\r\n    ).pipe(\r\n      take(1),\r\n      map(response => {\r\n        if (response.error) {\r\n          return null;\r\n        }\r\n\r\n        return response.data.length > 0 ? response.data[0] as UserDailySideQuest : null;\r\n      }),\r\n      switchMap(existingSideQuest => {\r\n        if (existingSideQuest) {\r\n\r\n          const assignedDate = new Date(existingSideQuest.date_assigned);\r\n          if (assignedDate.getDate() !== today.getDate() ||\r\n              assignedDate.getMonth() !== today.getMonth() ||\r\n              assignedDate.getFullYear() !== today.getFullYear()) {\r\n\r\n            return from(this.assignNewQuest(existingSideQuest.id)).pipe(\r\n              switchMap(() => from(\r\n                this.supabaseService.getClient()\r\n                  .from('user_daily_sidequests')\r\n                  .select('*')\r\n                  .eq('user_id', userId)\r\n              ).pipe(\r\n                take(1),\r\n                map(response => {\r\n                  if (response.error) {\r\n                    return null;\r\n                  }\r\n\r\n                  return response.data.length > 0 ? response.data[0] as UserDailySideQuest : null;\r\n                })\r\n              ))\r\n            );\r\n          }\r\n\r\n\r\n          return from(this.checkMissedDays(existingSideQuest.id)).pipe(\r\n            switchMap(() => of(existingSideQuest))\r\n          );\r\n        } else {\r\n\r\n          return from(this.createUserDailySideQuest(userId)).pipe(\r\n            switchMap(() => from(\r\n              this.supabaseService.getClient()\r\n                .from('user_daily_sidequests')\r\n                .select('*')\r\n                .eq('user_id', userId)\r\n            ).pipe(\r\n              take(1),\r\n              map(response => {\r\n                if (response.error) {\r\n                  return null;\r\n                }\r\n\r\n                return response.data.length > 0 ? response.data[0] as UserDailySideQuest : null;\r\n              })\r\n            ))\r\n          );\r\n        }\r\n      }),\r\n      catchError(error => {\r\n        return of(null);\r\n      })\r\n    );\r\n  }\r\n\r\n  recalculateSideQuestStreak(userId: string, _selectedDate?: Date): Observable<void> {\r\n\r\n    return from(\r\n      this.supabaseService.getClient()\r\n        .from('user_daily_sidequests')\r\n        .select('*')\r\n        .eq('user_id', userId)\r\n        .order('date_assigned', { ascending: false })\r\n        .limit(1)\r\n    ).pipe(\r\n      map(response => {\r\n        if (response.error) {\r\n          return null;\r\n        }\r\n        return response.data.length > 0 ? response.data[0] : null;\r\n      }),\r\n      switchMap(sideQuest => {\r\n        if (!sideQuest) {\r\n          return of(undefined);\r\n        }\r\n\r\n        return from(this.checkMissedDays(sideQuest.id)).pipe(\r\n          map(() => undefined)\r\n        );\r\n      }),\r\n      catchError(error => {\r\n        return of(undefined);\r\n      })\r\n    );\r\n  }\r\n\r\n  ensureUserHasDailySideQuests(userId: string): Observable<UserDailySideQuest[]> {\r\n\r\n    const today = new Date();\r\n\r\n    return from(\r\n      this.supabaseService.getClient()\r\n        .from('user_daily_sidequests')\r\n        .select('*, daily_sidequest_pool(*)')\r\n        .eq('user_id', userId)\r\n        .order('date_assigned', { ascending: false })\r\n        .limit(1)\r\n    ).pipe(\r\n      switchMap(response => {\r\n        if (response.error) {\r\n          return of([]);\r\n        }\r\n\r\n        if (response.data && response.data.length > 0) {\r\n\r\n          const sideQuest = response.data[0];\r\n\r\n          return from(this.checkMissedDays(sideQuest.id)).pipe(\r\n            switchMap(() => {\r\n\r\n              const assignedDate = new Date(sideQuest.date_assigned);\r\n              if (assignedDate.getDate() !== today.getDate() ||\r\n                  assignedDate.getMonth() !== today.getMonth() ||\r\n                  assignedDate.getFullYear() !== today.getFullYear()) {\r\n\r\n                return from(this.assignNewQuest(sideQuest.id)).pipe(\r\n                  switchMap(() => from(\r\n                    this.supabaseService.getClient()\r\n                      .from('user_daily_sidequests')\r\n                      .select('*, daily_sidequest_pool(*)')\r\n                      .eq('user_id', userId)\r\n                      .order('date_assigned', { ascending: false })\r\n                      .limit(1)\r\n                  ).pipe(\r\n                    map(updatedResponse => {\r\n                      if (updatedResponse.error) {\r\n                        return [];\r\n                      }\r\n                      return updatedResponse.data as UserDailySideQuest[];\r\n                    })\r\n                  ))\r\n                );\r\n              }\r\n\r\n              return of([sideQuest] as UserDailySideQuest[]);\r\n            })\r\n          );\r\n        }\r\n\r\n\r\n        return from(this.createUserDailySideQuest(userId)).pipe(\r\n          switchMap(newSideQuestId => {\r\n            return from(\r\n              this.supabaseService.getClient()\r\n                .from('user_daily_sidequests')\r\n                .select('*, daily_sidequest_pool(*)')\r\n                .eq('user_id', userId)\r\n                .order('date_assigned', { ascending: false })\r\n                .limit(1)\r\n            ).pipe(\r\n              map(newResponse => {\r\n                if (newResponse.error) {\r\n                  return [];\r\n                }\r\n                return newResponse.data as UserDailySideQuest[];\r\n              })\r\n            );\r\n          }),\r\n          catchError(error => {\r\n            return of([]);\r\n          })\r\n        );\r\n      })\r\n    );\r\n  }\r\n\r\n  importSideQuestsFromJson(): Observable<boolean> {\r\n\r\n    return this.http.get<DailySideQuestPool[]>('assets/data/sidequest-pool.json').pipe(\r\n      switchMap(sideQuests => {\r\n        if (!sideQuests || sideQuests.length === 0) {\r\n          return of(false);\r\n        }\r\n\r\n\r\n        return from(\r\n          this.supabaseService.getClient()\r\n            .from('daily_sidequest_pool')\r\n            .select('id', { count: 'exact' })\r\n        ).pipe(\r\n          switchMap(response => {\r\n            if (response.error) {\r\n              return of(false);\r\n            }\r\n\r\n            if (response.data && response.data.length > 0) {\r\n              return of(false);\r\n            }\r\n\r\n            const questsWithActive = sideQuests.map(quest => ({\r\n              ...quest,\r\n              active: true\r\n            }));\r\n\r\n            return from(\r\n              this.supabaseService.getClient()\r\n                .from('daily_sidequest_pool')\r\n                .insert(questsWithActive)\r\n            ).pipe(\r\n              map(insertResponse => {\r\n                if (insertResponse.error) {\r\n                  return false;\r\n                }\r\n\r\n                return true;\r\n              }),\r\n              catchError(error => {\r\n                return of(false);\r\n              })\r\n            );\r\n          })\r\n        );\r\n      }),\r\n      catchError(error => {\r\n        return of(false);\r\n      })\r\n    );\r\n  }\r\n}\r\n"], "mappings": ";;AAEA,SAAqBA,GAAG,EAAEC,EAAE,EAAEC,IAAI,EAAEC,SAAS,EAAEC,UAAU,EAAEC,IAAI,QAAQ,MAAM;;;;;AAQ7E,OAAM,MAAOC,gBAAgB;EAC3BC,YACUC,eAAgC,EAChCC,WAAwB,EACxBC,IAAgB;IAFhB,KAAAF,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,IAAI,GAAJA,IAAI;EACX;EAEHC,iBAAiBA,CAAA;IAGf,OAAOT,IAAI,CACT,IAAI,CAACM,eAAe,CAACI,SAAS,EAAE,CAC7BV,IAAI,CAAC,sBAAsB,CAAC,CAC5BW,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,CACtB,CAACC,IAAI,CACJf,GAAG,CAACgB,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,KAAK,EAAE;QAClB,OAAO,EAAE;MACX;MAGA,OAAOD,QAAQ,CAACE,IAA4B;IAC9C,CAAC,CAAC,EACFd,UAAU,CAACa,KAAK,IAAG;MACjB,OAAOhB,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH;EACH;EAEAkB,qBAAqBA,CAACC,MAAc;IAGlC,OAAOlB,IAAI,CACT,IAAI,CAACM,eAAe,CAACI,SAAS,EAAE,CAC7BV,IAAI,CAAC,uBAAuB,CAAC,CAC7BW,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAEM,MAAM,CAAC,CACzB,CAACL,IAAI,CACJf,GAAG,CAACgB,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,KAAK,EAAE;QAClB,OAAO,IAAI;MACb;MAGA,OAAOD,QAAQ,CAACE,IAAI,CAACG,MAAM,GAAG,CAAC,GAAGL,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAuB,GAAG,IAAI;IACjF,CAAC,CAAC,EACFd,UAAU,CAACa,KAAK,IAAG;MACjB,OAAOhB,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH;EACH;EAEMqB,wBAAwBA,CAACF,MAAc;IAAA,IAAAG,KAAA;IAAA,OAAAC,iBAAA;MAG3C,IAAI;QACF,MAAM;UAAEN,IAAI,EAAEO,UAAU;UAAER,KAAK,EAAES;QAAS,CAAE,SAASH,KAAI,CAACf,eAAe,CAACI,SAAS,EAAE,CAClFV,IAAI,CAAC,sBAAsB,CAAC,CAC5BW,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC;QAErB,IAAIY,SAAS,EAAE;UAEb,MAAM,IAAIC,KAAK,CAAC,iCAAiC,GAAGD,SAAS,CAACE,OAAO,CAAC;QACxE;QAEA,IAAI,CAACH,UAAU,IAAIA,UAAU,CAACJ,MAAM,KAAK,CAAC,EAAE;UAC1C,MAAM,IAAIM,KAAK,CAAC,iCAAiC,CAAC;QACpD;QAIA,MAAME,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAGP,UAAU,CAACJ,MAAM,CAAC;QACjE,MAAMY,WAAW,GAAGR,UAAU,CAACI,WAAW,CAAC;QAG3C,MAAMK,KAAK,GAAG,IAAIC,IAAI,EAAE;QACxB,MAAMC,UAAU,GAAGF,KAAK,CAACG,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAEpD,MAAMC,YAAY,GAAmC;UACnDC,OAAO,EAAEpB,MAAM;UACfqB,gBAAgB,EAAER,WAAW,CAACS,EAAE;UAChCC,MAAM,EAAE,CAAC;UACTC,aAAa,EAAER,UAAU;UACzBS,SAAS,EAAE,KAAK;UAChBC,cAAc,EAAE,CAAC;UACjBC,QAAQ,EAAEd,WAAW,CAACc,QAAQ;UAC9BC,KAAK,EAAEf,WAAW,CAACe,KAAK;UACxBC,mBAAmB,EAAE;SACtB;QAID,MAAM;UAAE/B,IAAI,EAAEgC,UAAU;UAAEjC,KAAK,EAAEkC;QAAW,CAAE,SAAS5B,KAAI,CAACf,eAAe,CAACI,SAAS,EAAE,CACpFV,IAAI,CAAC,uBAAuB,CAAC,CAC7BkD,MAAM,CAACb,YAAY,CAAC,CACpB1B,MAAM,CAAC,IAAI,CAAC,CACZwC,MAAM,EAAE;QAEX,IAAIF,WAAW,EAAE;UACf,MAAM,IAAIxB,KAAK,CAAC,6BAA6B,GAAGwB,WAAW,CAACvB,OAAO,CAAC;QACtE;QAGA,OAAOsB,UAAU,CAACR,EAAE;MACtB,CAAC,CAAC,OAAOzB,KAAK,EAAE;QACd,MAAMA,KAAK;MACb;IAAC;EACH;EAEMqC,yBAAyBA,CAACC,WAAmB,EAAEnC,MAAc,EAAEoC,YAAmB;IAAA,IAAAC,MAAA;IAAA,OAAAjC,iBAAA;MAStF,IAAI;QACF,MAAM;UAAEN,IAAI,EAAEwC,SAAS;UAAEzC,KAAK,EAAE0C;QAAU,CAAE,SAASF,MAAI,CAACjD,eAAe,CAACI,SAAS,EAAE,CAClFV,IAAI,CAAC,uBAAuB,CAAC,CAC7BW,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,IAAI,EAAEyC,WAAW,CAAC,CACrBF,MAAM,EAAE;QAEX,IAAIM,UAAU,IAAI,CAACD,SAAS,EAAE;UAC5B,MAAM,IAAI/B,KAAK,CAAC,sBAAsB,CAAC;QACzC;QAEA,MAAMiC,YAAY,GAAGF,SAAS,CAACb,SAAS;QAExC,MAAM;UAAE3B,IAAI,EAAE2C,SAAS;UAAE5C,KAAK,EAAES;QAAS,CAAE,SAAS+B,MAAI,CAACjD,eAAe,CAACI,SAAS,EAAE,CACjFV,IAAI,CAAC,sBAAsB,CAAC,CAC5BW,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,IAAI,EAAE4C,SAAS,CAACjB,gBAAgB,CAAC,CACpCY,MAAM,EAAE;QAEX,IAAI3B,SAAS,IAAI,CAACmC,SAAS,EAAE;UAC3B,MAAM,IAAIlC,KAAK,CAAC,gCAAgC,CAAC;QACnD;QAIA,MAAMmC,WAAW,GAAG,CAACF,YAAY;QACjC,MAAM1B,KAAK,GAAGsB,YAAY,IAAI,IAAIrB,IAAI,EAAE;QACxC,MAAM4B,SAAS,GAAG,IAAI5B,IAAI,CAACD,KAAK,CAAC;QACjC6B,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;QAE1C,IAAIC,SAAS,GAAGR,SAAS,CAACf,MAAM;QAChC,IAAIwB,oBAAoB,GAAGT,SAAS,CAACT,mBAAmB;QACxD,IAAImB,gBAAgB,GAAGV,SAAS,CAACZ,cAAc;QAE/C,IAAIgB,WAAW,EAAE;UACfM,gBAAgB,GAAGP,SAAS,CAACQ,UAAU;UAEvC,IAAIX,SAAS,CAACT,mBAAmB,EAAE;YACjC,MAAMqB,iBAAiB,GAAG,IAAInC,IAAI,CAACuB,SAAS,CAACT,mBAAmB,CAAC;YACjE,MAAMsB,gBAAgB,GAAGD,iBAAiB,CAACL,OAAO,EAAE;YACpD,MAAMO,YAAY,GAAGT,SAAS,CAACE,OAAO,EAAE;YAExC,IAAIM,gBAAgB,KAAKC,YAAY,EAAE;cACrCN,SAAS,IAAI,CAAC;YAChB,CAAC,MAAM;cACLA,SAAS,GAAG,CAAC;YACf;UACF,CAAC,MAAM;YACLA,SAAS,GAAG,CAAC;UACf;UAEAC,oBAAoB,GAAGjC,KAAK;QAC9B,CAAC,MAAM;UACLkC,gBAAgB,GAAG,CAAC;UAEpB,IAAIV,SAAS,CAACT,mBAAmB,IAAI,IAAId,IAAI,CAACuB,SAAS,CAACT,mBAAmB,CAAC,CAACgB,OAAO,EAAE,KAAK/B,KAAK,CAAC+B,OAAO,EAAE,EAAE;YAC1G,IAAIC,SAAS,GAAG,CAAC,EAAE;cACjBA,SAAS,IAAI,CAAC;cACdC,oBAAoB,GAAGJ,SAAS;YAClC,CAAC,MAAM;cACLG,SAAS,GAAG,CAAC;cACbC,oBAAoB,GAAGJ,SAAS;YAClC;UACF;QACF;QAEA,MAAM;UAAE9C,KAAK,EAAEwD;QAAW,CAAE,SAAShB,MAAI,CAACjD,eAAe,CAACI,SAAS,EAAE,CAClEV,IAAI,CAAC,uBAAuB,CAAC,CAC7BwE,MAAM,CAAC;UACN7B,SAAS,EAAEiB,WAAW;UACtBhB,cAAc,EAAEsB,gBAAgB;UAChCzB,MAAM,EAAEuB,SAAS;UACjBjB,mBAAmB,EAAEkB;SACtB,CAAC,CACDrD,EAAE,CAAC,IAAI,EAAEyC,WAAW,CAAC;QAExB,IAAIkB,WAAW,EAAE;UACf,MAAM,IAAI9C,KAAK,CAAC,6BAA6B,GAAG8C,WAAW,CAAC7C,OAAO,CAAC;QACtE;QAGA,IAAIkC,WAAW,KAAKF,YAAY,EAAE;UAChC,MAAMH,MAAI,CAACkB,YAAY,CAACvD,MAAM,EAAEsC,SAAS,CAACX,QAAQ,EAAEe,WAAW,EAAEF,YAAY,CAAC;QAChF;QAEA,IAAIM,SAAS,IAAI,CAAC,EAAE;UAClB,MAAMT,MAAI,CAACmB,qBAAqB,CAACxD,MAAM,EAAE8C,SAAS,CAAC;QACrD;QAEA,MAAMW,WAAW,GAAG,IAAI1C,IAAI,EAAE;QAC9B0C,WAAW,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAEhC,IAAItB,YAAY,IAAIA,YAAY,GAAGqB,WAAW,EAAE;UAE9C,MAAM;YAAE3D,IAAI,EAAE6D,cAAc;YAAE9D,KAAK,EAAE+D;UAAmB,CAAE,SAASvB,MAAI,CAACjD,eAAe,CAACI,SAAS,EAAE,CAChGV,IAAI,CAAC,uBAAuB,CAAC,CAC7BW,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAEM,MAAM,CAAC,CACrBiC,MAAM,EAAE;UAEX,IAAI2B,mBAAmB,IAAI,CAACD,cAAc,EAAE,CAC5C,CAAC,MAAM;YACL,IAAIA,cAAc,CAACrC,EAAE,KAAKa,WAAW,EAAE;cAErC,MAAME,MAAI,CAACwB,eAAe,CAACF,cAAc,CAACrC,EAAE,CAAC;YAC/C;UACF;QACF;QAEA,OAAO;UACLG,SAAS,EAAEiB,WAAW;UACtBhB,cAAc,EAAEsB,gBAAgB;UAChCzB,MAAM,EAAEuB,SAAS;UACjBG,UAAU,EAAER,SAAS,CAACQ,UAAU;UAChCa,SAAS,EAAErB,SAAS,CAACqB;SACtB;MACH,CAAC,CAAC,OAAOjE,KAAK,EAAE;QACd,MAAMA,KAAK;MACb;IAAC;EACH;EAEMgE,eAAeA,CAAC1B,WAA+B;IAAA,IAAA4B,MAAA;IAAA,OAAA3D,iBAAA;MAEnD,IAAI,CAAC+B,WAAW,EAAE;QAChB;MACF;MAEA,IAAI;QACF,MAAM;UAAErC,IAAI,EAAEwC,SAAS;UAAEzC,KAAK,EAAE0C;QAAU,CAAE,SAASwB,MAAI,CAAC3E,eAAe,CAACI,SAAS,EAAE,CAClFV,IAAI,CAAC,uBAAuB,CAAC,CAC7BW,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,IAAI,EAAEyC,WAAW,CAAC,CACrBF,MAAM,EAAE;QAEX,IAAIM,UAAU,IAAI,CAACD,SAAS,EAAE;UAC5B;QACF;QAEA,MAAMxB,KAAK,GAAG,IAAIC,IAAI,EAAE;QACxB,MAAM4B,SAAS,GAAG,IAAI5B,IAAI,CAACD,KAAK,CAAC;QACjC6B,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;QAE1C,MAAMK,iBAAiB,GAAGZ,SAAS,CAACT,mBAAmB,GAAG,IAAId,IAAI,CAACuB,SAAS,CAACT,mBAAmB,CAAC,GAAG,IAAI;QAExG,IAAIqB,iBAAiB,EAAE;UACrBA,iBAAiB,CAACQ,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxC;QACAf,SAAS,CAACe,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAE9B,MAAMM,SAAS,GAAG,CAACd,iBAAiB,IAClBA,iBAAiB,GAAGP,SAAS,IAC7BO,iBAAiB,CAACjC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAKyB,SAAS,CAAC1B,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE;QAG1G,IAAI8C,SAAS,EAAE;UAGb,MAAM;YAAEnE,KAAK,EAAEwD;UAAW,CAAE,SAASU,MAAI,CAAC3E,eAAe,CAACI,SAAS,EAAE,CAClEV,IAAI,CAAC,uBAAuB,CAAC,CAC7BwE,MAAM,CAAC;YACN/B,MAAM,EAAE,CAAC;YACTM,mBAAmB,EAAEc,SAAS,CAAC1B,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;WAC1D,CAAC,CACDxB,EAAE,CAAC,IAAI,EAAEyC,WAAW,CAAC;UAExB,IAAIkB,WAAW,EAAE;YACf;UACF;QAEF,CAAC,MAAM,CACP;MACF,CAAC,CAAC,OAAOxD,KAAK,EAAE,CAChB;IAAC;EACH;EAEMoE,cAAcA,CAAC9B,WAA+B;IAAA,IAAA+B,MAAA;IAAA,OAAA9D,iBAAA;MAElD,IAAI,CAAC+B,WAAW,EAAE;QAChB;MACF;MAEA,IAAI;QACF,MAAM;UAAErC,IAAI,EAAEwC,SAAS;UAAEzC,KAAK,EAAE0C;QAAU,CAAE,SAAS2B,MAAI,CAAC9E,eAAe,CAACI,SAAS,EAAE,CAClFV,IAAI,CAAC,uBAAuB,CAAC,CAC7BW,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,IAAI,EAAEyC,WAAW,CAAC,CACrBF,MAAM,EAAE;QAEX,IAAIM,UAAU,IAAI,CAACD,SAAS,EAAE;UAC5B;QACF;QAEA,MAAMxB,KAAK,GAAG,IAAIC,IAAI,EAAE;QAExB,IAAI,IAAIA,IAAI,CAACuB,SAAS,CAACd,aAAa,CAAC,CAACqB,OAAO,EAAE,KAAK/B,KAAK,CAAC+B,OAAO,EAAE,EAAE;UACnE;QACF;QAEA,MAAMqB,MAAI,CAACL,eAAe,CAAC1B,WAAW,CAAC;QAEvC,MAAM;UAAErC,IAAI,EAAEO,UAAU;UAAER,KAAK,EAAES;QAAS,CAAE,SAAS4D,MAAI,CAAC9E,eAAe,CAACI,SAAS,EAAE,CAClFV,IAAI,CAAC,sBAAsB,CAAC,CAC5BW,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,CAClByE,GAAG,CAAC,IAAI,EAAE7B,SAAS,CAACjB,gBAAgB,CAAC;QAExC,IAAIf,SAAS,EAAE;UACb;QACF;QAEA,IAAI,CAACD,UAAU,IAAIA,UAAU,CAACJ,MAAM,KAAK,CAAC,EAAE;UAC1C;QACF;QAGA,MAAMQ,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAGP,UAAU,CAACJ,MAAM,CAAC;QACjE,MAAMY,WAAW,GAAGR,UAAU,CAACI,WAAW,CAAC;QAE3C,MAAM;UAAEZ,KAAK,EAAEwD;QAAW,CAAE,SAASa,MAAI,CAAC9E,eAAe,CAACI,SAAS,EAAE,CAClEV,IAAI,CAAC,uBAAuB,CAAC,CAC7BwE,MAAM,CAAC;UACNjC,gBAAgB,EAAER,WAAW,CAACS,EAAE;UAChCE,aAAa,EAAEV,KAAK;UACpBW,SAAS,EAAE,KAAK;UAChBC,cAAc,EAAE,CAAC;UACjBC,QAAQ,EAAEd,WAAW,CAACc,QAAQ;UAC9BC,KAAK,EAAEf,WAAW,CAACe;SACpB,CAAC,CACDlC,EAAE,CAAC,IAAI,EAAEyC,WAAW,CAAC;QAExB,IAAIkB,WAAW,EAAE;UACf;QACF;MAEF,CAAC,CAAC,OAAOxD,KAAK,EAAE,CAChB;IAAC;EACH;EAEc0D,YAAYA,CACxBvD,MAAc,EACd2B,QAAgB,EAChBe,WAAoB,EACpBF,YAAqB;IAAA,IAAA4B,MAAA;IAAA,OAAAhE,iBAAA;MAGrB,IAAI;QACF,IAAIsC,WAAW,KAAKF,YAAY,EAAE;UAChC;QACF;QAGA,MAAM6B,OAAO,GAAG,GAAG1C,QAAQ,KAAK;QAEhC,MAAM2C,OAAO,GAAG,CAAC;QAEjB,IAAI;UACF,IAAIC,UAAU,GAAG,EAAE;UAEnB,IAAI7B,WAAW,IAAI,CAACF,YAAY,EAAE;YAEhC,MAAM;cAAE1C,IAAI,EAAE0E,QAAQ;cAAE3E,KAAK,EAAE4E;YAAS,CAAE,SAASL,MAAI,CAAChF,eAAe,CAACI,SAAS,EAAE,CAChFV,IAAI,CAAC,UAAU,CAAC,CAChBW,MAAM,CAAC,GAAG4E,OAAO,EAAE,CAAC,CACpB3E,EAAE,CAAC,IAAI,EAAEM,MAAM,CAAC,CAChBiC,MAAM,EAAE;YAEX,IAAIwC,SAAS,EAAE;cACb;YACF;YACA,MAAMC,SAAS,GAAGF,QAAQ,CAACH,OAAO,CAAC,IAAI,CAAC;YACxC,MAAMM,KAAK,GAAGD,SAAS,GAAGJ,OAAO;YAEjCC,UAAU,GAAG;cAAE,CAACF,OAAO,GAAGM;YAAK,CAAE;UAEnC,CAAC,MAAM,IAAI,CAACjC,WAAW,IAAIF,YAAY,EAAE;YAEvC,MAAM;cAAE1C,IAAI,EAAE0E,QAAQ;cAAE3E,KAAK,EAAE4E;YAAS,CAAE,SAASL,MAAI,CAAChF,eAAe,CAACI,SAAS,EAAE,CAChFV,IAAI,CAAC,UAAU,CAAC,CAChBW,MAAM,CAAC,GAAG4E,OAAO,EAAE,CAAC,CACpB3E,EAAE,CAAC,IAAI,EAAEM,MAAM,CAAC,CAChBiC,MAAM,EAAE;YAEX,IAAIwC,SAAS,EAAE;cACb;YACF;YAEA,MAAMC,SAAS,GAAGF,QAAQ,CAACH,OAAO,CAAC,IAAI,CAAC;YACxC,MAAMM,KAAK,GAAGjE,IAAI,CAACkE,GAAG,CAAC,CAAC,EAAEF,SAAS,GAAGJ,OAAO,CAAC;YAE9CC,UAAU,GAAG;cAAE,CAACF,OAAO,GAAGM;YAAK,CAAE;UACnC;UAEA,IAAIE,MAAM,CAACC,IAAI,CAACP,UAAU,CAAC,CAACtE,MAAM,GAAG,CAAC,EAAE;YACtC,MAAM;cAAEJ,KAAK,EAAEwD;YAAW,CAAE,SAASe,MAAI,CAAChF,eAAe,CAACI,SAAS,EAAE,CAClEV,IAAI,CAAC,UAAU,CAAC,CAChBwE,MAAM,CAACiB,UAAU,CAAC,CAClB7E,EAAE,CAAC,IAAI,EAAEM,MAAM,CAAC;YAEnB,IAAIqD,WAAW,EAAE;cACf;YACF;YAGA,MAAMe,MAAI,CAAC/E,WAAW,CAAC0F,eAAe,CAAC/E,MAAM,CAAC;UAEhD;QACF,CAAC,CAAC,OAAOH,KAAK,EAAE,CAChB;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE,CAChB;IAAC;EACH;EAEc2D,qBAAqBA,CAACxD,MAAc,EAAEuB,MAAc;IAAA,IAAAyD,MAAA;IAAA,OAAA5E,iBAAA;MAEhE,IAAI;QACF,MAAM;UAAEN,IAAI,EAAEmF,MAAM;UAAEpF,KAAK,EAAEqF;QAAW,CAAE,SAASF,MAAI,CAAC5F,eAAe,CAACI,SAAS,EAAE,CAChFV,IAAI,CAAC,aAAa,CAAC,CACnBW,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAEM,MAAM,CAAC;QAExB,IAAIkF,WAAW,EAAE;UACf;QACF;QAEA,IAAI,CAACD,MAAM,IAAIA,MAAM,CAAChF,MAAM,KAAK,CAAC,EAAE;UAClC;QACF;QAEA,MAAMkF,QAAQ,GAAGF,MAAM,CAAC,CAAC,CAAC;QAE1B,MAAMG,OAAO,GAAQ,EAAE;QAEvB,IAAI7D,MAAM,IAAI,CAAC,EAAE;UACf6D,OAAO,CAACC,6BAA6B,GAAG,IAAI;QAC9C;QACA,IAAI9D,MAAM,IAAI,EAAE,EAAE;UAChB6D,OAAO,CAACE,8BAA8B,GAAG,IAAI;QAC/C;QACA,IAAI/D,MAAM,IAAI,GAAG,EAAE;UACjB6D,OAAO,CAACG,+BAA+B,GAAG,IAAI;QAChD;QACA,IAAIhE,MAAM,IAAI,GAAG,EAAE;UACjB6D,OAAO,CAACI,+BAA+B,GAAG,IAAI;QAChD;QAEA,IAAIX,MAAM,CAACC,IAAI,CAACM,OAAO,CAAC,CAACnF,MAAM,GAAG,CAAC,EAAE;UAEnC,MAAM;YAAEJ,KAAK,EAAEwD;UAAW,CAAE,SAAS2B,MAAI,CAAC5F,eAAe,CAACI,SAAS,EAAE,CAClEV,IAAI,CAAC,aAAa,CAAC,CACnBwE,MAAM,CAAC8B,OAAO,CAAC,CACf1F,EAAE,CAAC,IAAI,EAAEyF,QAAQ,CAAC7D,EAAE,CAAC;UAExB,IAAI+B,WAAW,EAAE;YACf;UACF;QAEF,CAAC,MAAM,CACP;MACF,CAAC,CAAC,OAAOxD,KAAK,EAAE,CAChB;IAAC;EACH;EAEQ4F,SAASA,CAACC,KAAW,EAAEC,KAAW;IACxC,OAAOD,KAAK,CAACE,WAAW,EAAE,KAAKD,KAAK,CAACC,WAAW,EAAE,IAC3CF,KAAK,CAACG,QAAQ,EAAE,KAAKF,KAAK,CAACE,QAAQ,EAAE,IACrCH,KAAK,CAAC7C,OAAO,EAAE,KAAK8C,KAAK,CAAC9C,OAAO,EAAE;EAC5C;EAEAiD,yBAAyBA,CAAC9F,MAAc;IACtC,MAAMc,KAAK,GAAG,IAAIC,IAAI,EAAE;IAExB,OAAOjC,IAAI,CACT,IAAI,CAACM,eAAe,CAACI,SAAS,EAAE,CAC7BV,IAAI,CAAC,uBAAuB,CAAC,CAC7BW,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAEM,MAAM,CAAC,CACzB,CAACL,IAAI,CACJV,IAAI,CAAC,CAAC,CAAC,EACPL,GAAG,CAACgB,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,KAAK,EAAE;QAClB,OAAO,IAAI;MACb;MAEA,OAAOD,QAAQ,CAACE,IAAI,CAACG,MAAM,GAAG,CAAC,GAAGL,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAuB,GAAG,IAAI;IACjF,CAAC,CAAC,EACFf,SAAS,CAACgH,iBAAiB,IAAG;MAC5B,IAAIA,iBAAiB,EAAE;QAErB,MAAMC,YAAY,GAAG,IAAIjF,IAAI,CAACgF,iBAAiB,CAACvE,aAAa,CAAC;QAC9D,IAAIwE,YAAY,CAACnD,OAAO,EAAE,KAAK/B,KAAK,CAAC+B,OAAO,EAAE,IAC1CmD,YAAY,CAACH,QAAQ,EAAE,KAAK/E,KAAK,CAAC+E,QAAQ,EAAE,IAC5CG,YAAY,CAACJ,WAAW,EAAE,KAAK9E,KAAK,CAAC8E,WAAW,EAAE,EAAE;UAEtD,OAAO9G,IAAI,CAAC,IAAI,CAACmF,cAAc,CAAC8B,iBAAiB,CAACzE,EAAE,CAAC,CAAC,CAAC3B,IAAI,CACzDZ,SAAS,CAAC,MAAMD,IAAI,CAClB,IAAI,CAACM,eAAe,CAACI,SAAS,EAAE,CAC7BV,IAAI,CAAC,uBAAuB,CAAC,CAC7BW,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAEM,MAAM,CAAC,CACzB,CAACL,IAAI,CACJV,IAAI,CAAC,CAAC,CAAC,EACPL,GAAG,CAACgB,QAAQ,IAAG;YACb,IAAIA,QAAQ,CAACC,KAAK,EAAE;cAClB,OAAO,IAAI;YACb;YAEA,OAAOD,QAAQ,CAACE,IAAI,CAACG,MAAM,GAAG,CAAC,GAAGL,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAuB,GAAG,IAAI;UACjF,CAAC,CAAC,CACH,CAAC,CACH;QACH;QAGA,OAAOhB,IAAI,CAAC,IAAI,CAAC+E,eAAe,CAACkC,iBAAiB,CAACzE,EAAE,CAAC,CAAC,CAAC3B,IAAI,CAC1DZ,SAAS,CAAC,MAAMF,EAAE,CAACkH,iBAAiB,CAAC,CAAC,CACvC;MACH,CAAC,MAAM;QAEL,OAAOjH,IAAI,CAAC,IAAI,CAACoB,wBAAwB,CAACF,MAAM,CAAC,CAAC,CAACL,IAAI,CACrDZ,SAAS,CAAC,MAAMD,IAAI,CAClB,IAAI,CAACM,eAAe,CAACI,SAAS,EAAE,CAC7BV,IAAI,CAAC,uBAAuB,CAAC,CAC7BW,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAEM,MAAM,CAAC,CACzB,CAACL,IAAI,CACJV,IAAI,CAAC,CAAC,CAAC,EACPL,GAAG,CAACgB,QAAQ,IAAG;UACb,IAAIA,QAAQ,CAACC,KAAK,EAAE;YAClB,OAAO,IAAI;UACb;UAEA,OAAOD,QAAQ,CAACE,IAAI,CAACG,MAAM,GAAG,CAAC,GAAGL,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAuB,GAAG,IAAI;QACjF,CAAC,CAAC,CACH,CAAC,CACH;MACH;IACF,CAAC,CAAC,EACFd,UAAU,CAACa,KAAK,IAAG;MACjB,OAAOhB,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH;EACH;EAEAoH,0BAA0BA,CAACjG,MAAc,EAAEkG,aAAoB;IAE7D,OAAOpH,IAAI,CACT,IAAI,CAACM,eAAe,CAACI,SAAS,EAAE,CAC7BV,IAAI,CAAC,uBAAuB,CAAC,CAC7BW,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAEM,MAAM,CAAC,CACrBmG,KAAK,CAAC,eAAe,EAAE;MAAEC,SAAS,EAAE;IAAK,CAAE,CAAC,CAC5CC,KAAK,CAAC,CAAC,CAAC,CACZ,CAAC1G,IAAI,CACJf,GAAG,CAACgB,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,KAAK,EAAE;QAClB,OAAO,IAAI;MACb;MACA,OAAOD,QAAQ,CAACE,IAAI,CAACG,MAAM,GAAG,CAAC,GAAGL,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;IAC3D,CAAC,CAAC,EACFf,SAAS,CAACuD,SAAS,IAAG;MACpB,IAAI,CAACA,SAAS,EAAE;QACd,OAAOzD,EAAE,CAACyH,SAAS,CAAC;MACtB;MAEA,OAAOxH,IAAI,CAAC,IAAI,CAAC+E,eAAe,CAACvB,SAAS,CAAChB,EAAE,CAAC,CAAC,CAAC3B,IAAI,CAClDf,GAAG,CAAC,MAAM0H,SAAS,CAAC,CACrB;IACH,CAAC,CAAC,EACFtH,UAAU,CAACa,KAAK,IAAG;MACjB,OAAOhB,EAAE,CAACyH,SAAS,CAAC;IACtB,CAAC,CAAC,CACH;EACH;EAEAC,4BAA4BA,CAACvG,MAAc;IAEzC,MAAMc,KAAK,GAAG,IAAIC,IAAI,EAAE;IAExB,OAAOjC,IAAI,CACT,IAAI,CAACM,eAAe,CAACI,SAAS,EAAE,CAC7BV,IAAI,CAAC,uBAAuB,CAAC,CAC7BW,MAAM,CAAC,4BAA4B,CAAC,CACpCC,EAAE,CAAC,SAAS,EAAEM,MAAM,CAAC,CACrBmG,KAAK,CAAC,eAAe,EAAE;MAAEC,SAAS,EAAE;IAAK,CAAE,CAAC,CAC5CC,KAAK,CAAC,CAAC,CAAC,CACZ,CAAC1G,IAAI,CACJZ,SAAS,CAACa,QAAQ,IAAG;MACnB,IAAIA,QAAQ,CAACC,KAAK,EAAE;QAClB,OAAOhB,EAAE,CAAC,EAAE,CAAC;MACf;MAEA,IAAIe,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACG,MAAM,GAAG,CAAC,EAAE;QAE7C,MAAMqC,SAAS,GAAG1C,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC;QAElC,OAAOhB,IAAI,CAAC,IAAI,CAAC+E,eAAe,CAACvB,SAAS,CAAChB,EAAE,CAAC,CAAC,CAAC3B,IAAI,CAClDZ,SAAS,CAAC,MAAK;UAEb,MAAMiH,YAAY,GAAG,IAAIjF,IAAI,CAACuB,SAAS,CAACd,aAAa,CAAC;UACtD,IAAIwE,YAAY,CAACnD,OAAO,EAAE,KAAK/B,KAAK,CAAC+B,OAAO,EAAE,IAC1CmD,YAAY,CAACH,QAAQ,EAAE,KAAK/E,KAAK,CAAC+E,QAAQ,EAAE,IAC5CG,YAAY,CAACJ,WAAW,EAAE,KAAK9E,KAAK,CAAC8E,WAAW,EAAE,EAAE;YAEtD,OAAO9G,IAAI,CAAC,IAAI,CAACmF,cAAc,CAAC3B,SAAS,CAAChB,EAAE,CAAC,CAAC,CAAC3B,IAAI,CACjDZ,SAAS,CAAC,MAAMD,IAAI,CAClB,IAAI,CAACM,eAAe,CAACI,SAAS,EAAE,CAC7BV,IAAI,CAAC,uBAAuB,CAAC,CAC7BW,MAAM,CAAC,4BAA4B,CAAC,CACpCC,EAAE,CAAC,SAAS,EAAEM,MAAM,CAAC,CACrBmG,KAAK,CAAC,eAAe,EAAE;cAAEC,SAAS,EAAE;YAAK,CAAE,CAAC,CAC5CC,KAAK,CAAC,CAAC,CAAC,CACZ,CAAC1G,IAAI,CACJf,GAAG,CAAC4H,eAAe,IAAG;cACpB,IAAIA,eAAe,CAAC3G,KAAK,EAAE;gBACzB,OAAO,EAAE;cACX;cACA,OAAO2G,eAAe,CAAC1G,IAA4B;YACrD,CAAC,CAAC,CACH,CAAC,CACH;UACH;UAEA,OAAOjB,EAAE,CAAC,CAACyD,SAAS,CAAyB,CAAC;QAChD,CAAC,CAAC,CACH;MACH;MAGA,OAAOxD,IAAI,CAAC,IAAI,CAACoB,wBAAwB,CAACF,MAAM,CAAC,CAAC,CAACL,IAAI,CACrDZ,SAAS,CAAC0H,cAAc,IAAG;QACzB,OAAO3H,IAAI,CACT,IAAI,CAACM,eAAe,CAACI,SAAS,EAAE,CAC7BV,IAAI,CAAC,uBAAuB,CAAC,CAC7BW,MAAM,CAAC,4BAA4B,CAAC,CACpCC,EAAE,CAAC,SAAS,EAAEM,MAAM,CAAC,CACrBmG,KAAK,CAAC,eAAe,EAAE;UAAEC,SAAS,EAAE;QAAK,CAAE,CAAC,CAC5CC,KAAK,CAAC,CAAC,CAAC,CACZ,CAAC1G,IAAI,CACJf,GAAG,CAAC8H,WAAW,IAAG;UAChB,IAAIA,WAAW,CAAC7G,KAAK,EAAE;YACrB,OAAO,EAAE;UACX;UACA,OAAO6G,WAAW,CAAC5G,IAA4B;QACjD,CAAC,CAAC,CACH;MACH,CAAC,CAAC,EACFd,UAAU,CAACa,KAAK,IAAG;QACjB,OAAOhB,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH;EACH;EAEA8H,wBAAwBA,CAAA;IAEtB,OAAO,IAAI,CAACrH,IAAI,CAACsH,GAAG,CAAuB,iCAAiC,CAAC,CAACjH,IAAI,CAChFZ,SAAS,CAAC8H,UAAU,IAAG;MACrB,IAAI,CAACA,UAAU,IAAIA,UAAU,CAAC5G,MAAM,KAAK,CAAC,EAAE;QAC1C,OAAOpB,EAAE,CAAC,KAAK,CAAC;MAClB;MAGA,OAAOC,IAAI,CACT,IAAI,CAACM,eAAe,CAACI,SAAS,EAAE,CAC7BV,IAAI,CAAC,sBAAsB,CAAC,CAC5BW,MAAM,CAAC,IAAI,EAAE;QAAEqH,KAAK,EAAE;MAAO,CAAE,CAAC,CACpC,CAACnH,IAAI,CACJZ,SAAS,CAACa,QAAQ,IAAG;QACnB,IAAIA,QAAQ,CAACC,KAAK,EAAE;UAClB,OAAOhB,EAAE,CAAC,KAAK,CAAC;QAClB;QAEA,IAAIe,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACG,MAAM,GAAG,CAAC,EAAE;UAC7C,OAAOpB,EAAE,CAAC,KAAK,CAAC;QAClB;QAEA,MAAMkI,gBAAgB,GAAGF,UAAU,CAACjI,GAAG,CAACoI,KAAK,KAAK;UAChD,GAAGA,KAAK;UACRC,MAAM,EAAE;SACT,CAAC,CAAC;QAEH,OAAOnI,IAAI,CACT,IAAI,CAACM,eAAe,CAACI,SAAS,EAAE,CAC7BV,IAAI,CAAC,sBAAsB,CAAC,CAC5BkD,MAAM,CAAC+E,gBAAgB,CAAC,CAC5B,CAACpH,IAAI,CACJf,GAAG,CAACsI,cAAc,IAAG;UACnB,IAAIA,cAAc,CAACrH,KAAK,EAAE;YACxB,OAAO,KAAK;UACd;UAEA,OAAO,IAAI;QACb,CAAC,CAAC,EACFb,UAAU,CAACa,KAAK,IAAG;UACjB,OAAOhB,EAAE,CAAC,KAAK,CAAC;QAClB,CAAC,CAAC,CACH;MACH,CAAC,CAAC,CACH;IACH,CAAC,CAAC,EACFG,UAAU,CAACa,KAAK,IAAG;MACjB,OAAOhB,EAAE,CAAC,KAAK,CAAC;IAClB,CAAC,CAAC,CACH;EACH;;oBArtBWK,gBAAgB;;mCAAhBA,iBAAgB,EAAAiI,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,UAAA;AAAA;;SAAhBxI,iBAAgB;EAAAyI,OAAA,EAAhBzI,iBAAgB,CAAA0I,IAAA;EAAAC,UAAA,EAFf;AAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}