﻿
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://tobifepmbrrrvshpvrqa.supabase.co';
const supabaseServiceRoleKey = 'YOUR_SUPABASE_SERVICE_ROLE_KEY'; 

const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

async function setUserAsAdmin(userId: string) {
  try {
    const { data: user, error: userError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError) {
      return;
    }

    if (!user) {
      return;
    }


    const { data, error } = await supabase
      .from('profiles')
      .update({ username: 'admin' })
      .eq('id', userId);

    if (error) {
      return;
    }


    const { data: updatedUser, error: updatedError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (updatedError) {
      return;
    }

  } catch (error) {
  }
}




