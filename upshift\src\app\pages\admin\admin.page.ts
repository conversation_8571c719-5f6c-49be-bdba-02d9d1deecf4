﻿import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { AdminService } from '../../services/admin.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-admin',
  templateUrl: './admin.page.html',
  styleUrls: ['./admin.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule]
})
export class AdminPage implements OnInit {
  collections: string[] = [];
  isLoading = true;
  newCollectionName = '';

  constructor(
    private adminService: AdminService,
    private router: Router
  ) {}

  ngOnInit() {
    this.loadCollections();
  }

  loadCollections() {
    this.isLoading = true;
    this.adminService.getCollections().subscribe({
      next: (collections) => {
        this.collections = collections;
        this.isLoading = false;
      },
      error: (error) => {
        this.isLoading = false;
      }
    });
  }

  navigateToCollection(collection: string) {
    this.router.navigate(['/admin/collection', collection]);
  }

  addCollection() {
    if (!this.newCollectionName.trim()) return;

    this.isLoading = true;
    this.adminService.addCollection(this.newCollectionName.trim()).subscribe({
      next: () => {
        this.newCollectionName = '';
        this.loadCollections();
      },
      error: (error) => {
        this.isLoading = false;
      }
    });
  }
}
