import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule, Router } from '@angular/router';
import { UserService } from '../../../services/user.service';
import { User } from '../../../models/user.model';
import { Subscription, of, switchMap } from 'rxjs';
import { SupabaseService } from '../../../services/supabase.service';

@Component({
  selector: 'app-profile-settings',
  templateUrl: './profile-settings.page.html',
  styleUrls: ['./profile-settings.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule]
})
export class ProfileSettingsPage implements OnInit {
  // User data
  user: User | null = null;
  userSubscription: Subscription | null = null;

  // Form fields
  firstName = '';
  lastName = '';
  username = '';
  bio = '';
  sidequestsSwitch = true;
  showCelebration = true;
  celebrationName = '';
  celebrationDescription = '';
  celebrationEmoji = '';

  // Profile picture
  profilePicture: File | null = null;
  profilePicturePreview: string | null = null;
  fileStatus = 'No file selected';

  // Form state
  errors: { [key: string]: string } = {};
  successMessage = '';

  // Services
  private supabaseService = inject(SupabaseService);
  private userService = inject(UserService);
  private router = inject(Router);

  ngOnInit() {
    this.userSubscription = this.supabaseService.currentUser$.pipe(
      switchMap(authUser => {
        if (authUser) {
          return this.userService.getUser(authUser.id);
        }
        return of(null);
      })
    ).subscribe(user => {
      if (user) {
        this.user = user;
        this.initializeForm(user);
      } else {
        // Redirect to login if no user found
        this.router.navigate(['/login']);
      }
    });
  }

  ngOnDestroy() {
    if (this.userSubscription) {
      this.userSubscription.unsubscribe();
    }
  }

  initializeForm(user: User) {
    // Split name into first and last name
    const nameParts = (user.name || '').split(' ');
    this.firstName = nameParts[0] || '';
    this.lastName = nameParts.slice(1).join(' ') || '';

    this.username = user.username || '';
    this.bio = user.bio || '';
    this.sidequestsSwitch = user.sidequests_switch;
    this.showCelebration = user.show_celebration;
    this.celebrationName = user.celebration_name || '';
    this.celebrationDescription = user.celebration_description || '';
    this.celebrationEmoji = user.celebration_emoji || '';

    this.profilePicturePreview = user.profile_picture || null;
  }

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];

      // Validate file type
      const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
      if (!validTypes.includes(file.type)) {
        this.errors['profilePicture'] = 'Invalid file type. Please upload a JPG, PNG, or WebP image.';
        this.fileStatus = 'Invalid file type';
        this.profilePicture = null;
        return;
      }

      // Check if file is a GIF (we don't want to allow GIFs)
      if (file.type === 'image/gif') {
        this.errors['profilePicture'] = 'GIF files are not supported. Please select a static image file.';
        this.fileStatus = 'GIF not supported';
        this.profilePicture = null;
        return;
      }

      // Validate file size (max 2MB)
      if (file.size > 2 * 1024 * 1024) {
        this.errors['profilePicture'] = 'File size too large. Maximum size is 2MB.';
        this.fileStatus = 'File too large';
        this.profilePicture = null;
        return;
      }

      // Clear errors
      delete this.errors['profilePicture'];

      // Set file and preview
      this.profilePicture = file;
      this.fileStatus = 'Image selected ✓';

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        this.profilePicturePreview = e.target?.result as string;
      };
      reader.readAsDataURL(file);
    }
  }

  toggleCelebrationSettings() {
    // If celebration is turned off, reset the fields
    if (!this.showCelebration) {
      this.celebrationName = 'Another Day, Another W';
      this.celebrationDescription = "You've completed all your quests for today. Keep up the great work!";
      this.celebrationEmoji = '';
    }
  }

  updateEmojiPreview() {
    // Validate that only one emoji is entered
    if (this.celebrationEmoji) {
      const emojiRegex = /\p{Extended_Pictographic}/u;
      const graphemes = [...this.celebrationEmoji];

      // Check if last grapheme is emoji
      const lastEmoji = graphemes.reverse().find(char => emojiRegex.test(char));

      // Replace value with only that emoji (if valid), or clear
      this.celebrationEmoji = lastEmoji ? lastEmoji : '';
    }
  }

  async saveChanges() {
    if (!this.user || !this.user.id) return;

    // Reset errors and success message
    this.errors = {};
    this.successMessage = '';

    // Validate form
    let isValid = true;

    if (!this.username.trim()) {
      this.errors['username'] = 'Username is required';
      isValid = false;
    }

    if (!isValid) {
      return;
    }

    // Prepare update data
    const updateData: Partial<User> = {
      name: `${this.firstName.trim()} ${this.lastName.trim()}`.trim(),
      username: this.username.trim(),
      bio: this.bio.trim(),
      sidequests_switch: this.sidequestsSwitch,
      show_celebration: this.showCelebration,
      celebration_name: this.celebrationName.trim(),
      celebration_description: this.celebrationDescription.trim(),
      celebration_emoji: this.celebrationEmoji.trim()
    };

    try {
      // Upload profile picture if selected
      if (this.profilePicture) {
        // Create a unique file name
        const fileName = `${this.user.id}_${Date.now()}.${this.profilePicture.name.split('.').pop()}`;

        // Upload to Supabase Storage
        const { data, error } = await this.supabaseService.getClient()
          .storage
          .from('profile_pictures')
          .upload(fileName, this.profilePicture, {
            cacheControl: '3600',
            upsert: true
          });

        if (error) {
          throw new Error(`Error uploading profile picture: ${error.message}`);
        }

        // Get the public URL
        const { data: urlData } = this.supabaseService.getClient()
          .storage
          .from('profile_pictures')
          .getPublicUrl(fileName);

        updateData.profile_picture = urlData.publicUrl;
      }

      // Update user data
      await this.userService.updateUserProfile(this.user.id, updateData);

      // Show success message
      this.successMessage = 'Profile updated successfully';

      // Update local user object
      this.user = { ...this.user, ...updateData };
    } catch (error) {
      console.error('Error updating profile:', error);
      this.errors['general'] = 'An error occurred while updating your profile. Please try again.';
    }
  }

  async logout() {
    try {
      await this.supabaseService.signOut();
      this.router.navigate(['/signup/']);
    } catch (error) {
      console.error('Error signing out:', error);
    }
  }
}
