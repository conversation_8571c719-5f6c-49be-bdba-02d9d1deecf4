﻿import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule, Router } from '@angular/router';
import { UserService } from '../../../services/user.service';
import { User } from '../../../models/user.model';
import { Subscription, of, switchMap } from 'rxjs';
import { SupabaseService } from '../../../services/supabase.service';

@Component({
  selector: 'app-profile-settings',
  templateUrl: './profile-settings.page.html',
  styleUrls: ['./profile-settings.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule]
})
export class ProfileSettingsPage implements OnInit {
  user: User | null = null;
  userSubscription: Subscription | null = null;

  firstName = '';
  lastName = '';
  username = '';
  bio = '';
  sidequestsSwitch = true;
  showCelebration = true;
  celebrationName = '';
  celebrationDescription = '';
  celebrationEmoji = '';

  profilePicture: File | null = null;
  profilePicturePreview: string | null = null;
  fileStatus = 'No file selected';

  errors: { [key: string]: string } = {};
  successMessage = '';

  private supabaseService = inject(SupabaseService);
  private userService = inject(UserService);
  private router = inject(Router);

  ngOnInit() {
    this.userSubscription = this.supabaseService.currentUser$.pipe(
      switchMap(authUser => {
        if (authUser) {
          return this.userService.getUser(authUser.id);
        }
        return of(null);
      })
    ).subscribe(user => {
      if (user) {
        this.user = user;
        this.initializeForm(user);
      } else {
        this.router.navigate(['/login']);
      }
    });
  }

  ngOnDestroy() {
    if (this.userSubscription) {
      this.userSubscription.unsubscribe();
    }
  }

  initializeForm(user: User) {
    const nameParts = (user.name || '').split(' ');
    this.firstName = nameParts[0] || '';
    this.lastName = nameParts.slice(1).join(' ') || '';

    this.username = user.username || '';
    this.bio = user.bio || '';
    this.sidequestsSwitch = user.sidequests_switch;
    this.showCelebration = user.show_celebration;
    this.celebrationName = user.celebration_name || '';
    this.celebrationDescription = user.celebration_description || '';
    this.celebrationEmoji = user.celebration_emoji || '';

    this.profilePicturePreview = user.profile_picture || null;
  }

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];

      const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
      if (!validTypes.includes(file.type)) {
        this.errors['profilePicture'] = 'Invalid file type. Please upload a JPG, PNG, or WebP image.';
        this.fileStatus = 'Invalid file type';
        this.profilePicture = null;
        return;
      }

      if (file.type === 'image/gif') {
        this.errors['profilePicture'] = 'GIF files are not supported. Please select a static image file.';
        this.fileStatus = 'GIF not supported';
        this.profilePicture = null;
        return;
      }

      if (file.size > 2 * 1024 * 1024) {
        this.errors['profilePicture'] = 'File size too large. Maximum size is 2MB.';
        this.fileStatus = 'File too large';
        this.profilePicture = null;
        return;
      }

      delete this.errors['profilePicture'];

      this.profilePicture = file;
      this.fileStatus = 'Image selected ✓';

      const reader = new FileReader();
      reader.onload = (e) => {
        this.profilePicturePreview = e.target?.result as string;
      };
      reader.readAsDataURL(file);
    }
  }

  toggleCelebrationSettings() {
    if (!this.showCelebration) {
      this.celebrationName = 'Another Day, Another W';
      this.celebrationDescription = "You've completed all your quests for today. Keep up the great work!";
      this.celebrationEmoji = '';
    }
  }

  updateEmojiPreview() {
    if (this.celebrationEmoji) {
      const emojiRegex = /\p{Extended_Pictographic}/u;
      const graphemes = [...this.celebrationEmoji];

      const lastEmoji = graphemes.reverse().find(char => emojiRegex.test(char));

      this.celebrationEmoji = lastEmoji ? lastEmoji : '';
    }
  }

  async saveChanges() {
    if (!this.user || !this.user.id) return;

    this.errors = {};
    this.successMessage = '';

    let isValid = true;

    if (!this.username.trim()) {
      this.errors['username'] = 'Username is required';
      isValid = false;
    }

    if (!isValid) {
      return;
    }

    const updateData: Partial<User> = {
      name: `${this.firstName.trim()} ${this.lastName.trim()}`.trim(),
      username: this.username.trim(),
      bio: this.bio.trim(),
      sidequests_switch: this.sidequestsSwitch,
      show_celebration: this.showCelebration,
      celebration_name: this.celebrationName.trim(),
      celebration_description: this.celebrationDescription.trim(),
      celebration_emoji: this.celebrationEmoji.trim()
    };

    try {
      if (this.profilePicture) {
        const fileName = `${this.user.id}_${Date.now()}.${this.profilePicture.name.split('.').pop()}`;

        const { data, error } = await this.supabaseService.getClient()
          .storage
          .from('profile_pictures')
          .upload(fileName, this.profilePicture, {
            cacheControl: '3600',
            upsert: true
          });

        if (error) {
          throw new Error(`Error uploading profile picture: ${error.message}`);
        }

        const { data: urlData } = this.supabaseService.getClient()
          .storage
          .from('profile_pictures')
          .getPublicUrl(fileName);

        updateData.profile_picture = urlData.publicUrl;
      }

      await this.userService.updateUserProfile(this.user.id, updateData);

      this.successMessage = 'Profile updated successfully';

      this.user = { ...this.user, ...updateData };
    } catch (error) {
      this.errors['general'] = 'An error occurred while updating your profile. Please try again.';
    }
  }

  async logout() {
    try {
      await this.supabaseService.signOut();
      this.router.navigate(['/signup/']);
    } catch (error) {
    }
  }
}
