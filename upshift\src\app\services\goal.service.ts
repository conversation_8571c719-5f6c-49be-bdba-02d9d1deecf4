﻿import { Injectable, inject } from '@angular/core';
import { Goal, GoalJournalEntry, MicroGoal } from '../models/goal.model';
import { Observable, catchError, map, of, from } from 'rxjs';
import { SupabaseService } from './supabase.service';

@Injectable({
  providedIn: 'root'
})
export class GoalService {
  private supabaseService = inject(SupabaseService);

  getGoals(userId: string): Observable<Goal[]> {

    if (!userId) {
      return of([]);
    }

    try {
      return from(this.supabaseService.supabase
        .from('goals')
        .select('*')
        .eq('user_id', userId)
      ).pipe(
        map(response => {
          if (response.error) {
            return [];
          }
          return response.data as Goal[];
        }),
        catchError(error => {
          return of([]);
        })
      );
    } catch (error) {
      return of([]);
    }
  }

  getGoal(goalId: string): Observable<Goal | null> {

    if (!goalId) {
      return of(null);
    }

    try {
      return from(this.supabaseService.supabase
        .from('goals')
        .select('*')
        .eq('id', goalId)
        .single()
      ).pipe(
        map(response => {
          if (response.error) {
            return null;
          }
          return response.data as Goal || null;
        }),
        catchError(error => {
          return of(null);
        })
      );
    } catch (error) {
      return of(null);
    }
  }

  async createGoal(goal: Omit<Goal, 'id'>): Promise<string> {

    try {
      const { data, error } = await this.supabaseService.supabase
        .from('goals')
        .insert(goal)
        .select('id')
        .single();

      if (error) {
        return Promise.reject(error);
      }

      return data.id;
    } catch (error) {
      return Promise.reject(error);
    }
  }

  async updateGoal(goalId: string, data: Partial<Goal>): Promise<void> {

    try {
      const { error } = await this.supabaseService.supabase
        .from('goals')
        .update(data)
        .eq('id', goalId);

      if (error) {
        return Promise.reject(error);
      }

      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  }

  async deleteGoal(goalId: string): Promise<void> {

    try {
      const { error } = await this.supabaseService.supabase
        .from('goals')
        .delete()
        .eq('id', goalId);

      if (error) {
        return Promise.reject(error);
      }

      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  }

  getMicroGoals(goalId: string): Observable<MicroGoal[]> {

    if (!goalId) {
      return of([]);
    }

    try {
      return from(this.supabaseService.supabase
        .from('microgoals')
        .select('*')
        .eq('goal_id', goalId)
      ).pipe(
        map(response => {
          if (response.error) {
            return [];
          }
          return response.data as MicroGoal[];
        }),
        catchError(error => {
          return of([]);
        })
      );
    } catch (error) {
      return of([]);
    }
  }

  async createMicroGoal(microGoal: Omit<MicroGoal, 'id'>): Promise<string> {

    try {
      const { data, error } = await this.supabaseService.supabase
        .from('microgoals')
        .insert(microGoal)
        .select('id')
        .single();

      if (error) {
        return Promise.reject(error);
      }

      return data.id;
    } catch (error) {
      return Promise.reject(error);
    }
  }

  async toggleMicroGoalCompletion(microGoalId: string): Promise<void> {

    try {
      const { data: microGoal, error: fetchError } = await this.supabaseService.supabase
        .from('microgoals')
        .select('*')
        .eq('id', microGoalId)
        .single();

      if (fetchError) {
        throw fetchError;
      }

      if (!microGoal) {
        throw new Error('Micro goal not found');
      }

      const isCompleted = !microGoal.completed;

      try {
        const { error: updateError } = await this.supabaseService.supabase
          .from('microgoals')
          .update({
            completed: isCompleted,
            completed_at: isCompleted ? new Date() : null
          })
          .eq('id', microGoalId);

        if (updateError) {
          if (updateError.message && updateError.message.includes('completed_at')) {
            const { error: fallbackError } = await this.supabaseService.supabase
              .from('microgoals')
              .update({
                completed: isCompleted
              })
              .eq('id', microGoalId);

            if (fallbackError) {
              throw fallbackError;
            }
          } else {
            throw updateError;
          }
        }
      } catch (error) {
        throw error;
      }


    } catch (error) {
      throw error;
    }
  }

  async deleteMicroGoal(microGoalId: string): Promise<void> {

    try {
      const { error } = await this.supabaseService.supabase
        .from('microgoals')
        .delete()
        .eq('id', microGoalId);

      if (error) {
        return Promise.reject(error);
      }

      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  }

  getJournalEntries(goalId: string): Observable<GoalJournalEntry[]> {

    if (!goalId) {
      return of([]);
    }

    try {
      return from(this.supabaseService.supabase
        .from('goal_journal_entries')
        .select('*')
        .eq('goal_id', goalId)
        .order('milestone_percentage', { ascending: true })
      ).pipe(
        map(response => {
          if (response.error) {
            return [];
          }
          return response.data as GoalJournalEntry[];
        }),
        catchError(error => {
          return of([]);
        })
      );
    } catch (error) {
      return of([]);
    }
  }

  async createJournalEntry(entry: Omit<GoalJournalEntry, 'id' | 'created_at'>): Promise<string> {

    try {
      const newEntry: Omit<GoalJournalEntry, 'id'> = {
        ...entry,
        created_at: new Date()
      };

      try {
        const { data, error } = await this.supabaseService.supabase
          .from('goal_journal_entries')
          .insert(newEntry)
          .select('id')
          .single();

        if (error) {
          if (error.message && (error.message.includes('id does not exist') || error.message.includes('column goal_journal_entries.id'))) {
            throw new Error('id_column_missing');
          }
          return Promise.reject(error);
        }

        return data.id;
      } catch (err: any) {
        if (err.message === 'id_column_missing') {
          const { error } = await this.supabaseService.supabase
            .from('goal_journal_entries')
            .insert(newEntry);

          if (error) {
            return Promise.reject(error);
          }

          ');
          return 'unknown_id'; 
        } else {
          throw err; 
        }
      }
    } catch (error) {
      return Promise.reject(error);
    }
  }

  async updateJournalEntry(entryId: string, data: Partial<GoalJournalEntry>): Promise<void> {

    try {
      const { error } = await this.supabaseService.supabase
        .from('goal_journal_entries')
        .update(data)
        .eq('id', entryId);

      if (error) {
        return Promise.reject(error);
      }

      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  }

  async deleteJournalEntry(entryId: string): Promise<void> {

    try {
      const { error } = await this.supabaseService.supabase
        .from('goal_journal_entries')
        .delete()
        .eq('id', entryId);

      if (error) {
        return Promise.reject(error);
      }

      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  }
}
