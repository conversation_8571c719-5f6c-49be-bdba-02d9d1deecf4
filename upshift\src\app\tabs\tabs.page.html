﻿ <ion-tabs>
  <ion-tab-bar slot="bottom" class="main-navigation">
    <ion-tab-button tab="today" [routerLink]="['/today']" routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}">
      <ion-label>
        <span class="nav-icon">📋</span>
        <span class="nav-text">Quests</span>
      </ion-label>
    </ion-tab-button>
    <ion-tab-button tab="groups" [routerLink]="['/groups']" routerLinkActive="active">
      <ion-label>
        <span class="nav-icon">👥</span>
        <span class="nav-text">Groups</span>
      </ion-label>
    </ion-tab-button>
    <ion-tab-button tab="goals" [routerLink]="['/goals']" routerLinkActive="active">
      <ion-label>
        <span class="nav-icon">🎯</span>
        <span class="nav-text">Goals</span>
      </ion-label>
    </ion-tab-button>
    <ion-tab-button tab="time-tracker" [routerLink]="['/time-tracker']" routerLinkActive="active">
      <ion-label>
        <span class="nav-icon">⏰</span>
        <span class="nav-text">Time</span>
      </ion-label>
    </ion-tab-button>
    <ion-tab-button tab="focus" [routerLink]="['/focus']" routerLinkActive="active">
      <ion-label>
        <span class="nav-icon">🔥</span>
        <span class="nav-text">Focus</span>
      </ion-label>
    </ion-tab-button>
    <ion-tab-button tab="profile" [routerLink]="['/profile']" routerLinkActive="active">
      <ion-label>
        <span class="nav-icon">👤</span>
        <span class="nav-text">Profile</span>
      </ion-label>
    </ion-tab-button>
    <ion-tab-button tab="friends" [routerLink]="['/friends']" routerLinkActive="active">
      <ion-label>
        <span class="nav-icon">👥</span>
        <span class="nav-text">Friends</span>
      </ion-label>
    </ion-tab-button>
    <ion-tab-button *ngIf="isAdmin" tab="import-sidequests" [routerLink]="['/import-sidequests']" routerLinkActive="active">
      <ion-label>
        <span class="nav-icon">📥</span>
        <span class="nav-text">Import</span>
      </ion-label>
    </ion-tab-button>
    <ion-tab-button *ngIf="isAdmin" tab="admin" [routerLink]="['/admin']" routerLinkActive="active" class="admin-link">
      <ion-label>
        <span class="nav-icon">⚙️</span>
        <span class="nav-text">Admin</span>
      </ion-label>
    </ion-tab-button>
  </ion-tab-bar>
</ion-tabs>
