﻿import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { UserService } from '../../services/user.service';
import { FriendService } from '../../services/friend.service';
import { User } from '../../models/user.model';
import { Observable, Subscription, map, of, switchMap, take, catchError, from } from 'rxjs';
import { SupabaseService } from '../../services/supabase.service';
import { XpService, EntityType } from '../../services/xp.service';

interface CategoryDisplay {
  name: string;
  icon: string;
  color: string;
  current_xp: number;
  required_xp: number;
  progress: number;
}


@Component({
  selector: 'app-user-profile',
  templateUrl: './user-profile.page.html',
  styleUrls: ['./user-profile.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule]
})
export class UserProfilePage implements OnInit {
  currentUserId: string | null = null;
  user: User | null = null;
  userId: string | null = null;

  categories: CategoryDisplay[] = [];
  nextLevel = 0;

  private supabaseService = inject(SupabaseService);
  private userService = inject(UserService);
  private friendService = inject(FriendService);
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private xpService = inject(XpService);

  constructor() {}

  ngOnInit() {
    this.route.paramMap.pipe(
      take(1),
      switchMap(params => {
        this.userId = params.get('id');

        return this.supabaseService.currentUser$;
      }),
      switchMap(authUser => {
        if (authUser) {
          this.currentUserId = authUser.id;

          if (this.userId) {
            return this.userService.getUser(this.userId);
          }
        }
        return of(null);
      })
    ).subscribe(user => {
      if (user) {
        this.user = user;
        this.calculateXpProgress();
      } else {
        this.router.navigate(['/']);
      }
    });
  }

  calculateXpProgress() {
    if (!this.user) {
      return;
    }


    this.xpService.calculateXpProgress(this.user, EntityType.USER).subscribe(result => {
      if (result) {
        this.categories = result.categories;
        this.nextLevel = result.next_level;
      } else {
      }
    });
  }

  goBack() {
    window.history.back();
  }
}
