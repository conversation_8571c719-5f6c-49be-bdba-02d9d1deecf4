import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { UserService } from '../../services/user.service';
import { FriendService } from '../../services/friend.service';
import { User } from '../../models/user.model';
import { Observable, Subscription, map, of, switchMap, take, catchError, from } from 'rxjs';
import { SupabaseService } from '../../services/supabase.service';
import { XpService, EntityType } from '../../services/xp.service';

interface CategoryDisplay {
  name: string;
  icon: string;
  color: string;
  current_xp: number;
  required_xp: number;
  progress: number;
}


@Component({
  selector: 'app-user-profile',
  templateUrl: './user-profile.page.html',
  styleUrls: ['./user-profile.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule]
})
export class UserProfilePage implements OnInit {
  // User data
  currentUserId: string | null = null;
  user: User | null = null;
  userId: string | null = null;

  // XP categories
  categories: CategoryDisplay[] = [];
  nextLevel = 0;

  private supabaseService = inject(SupabaseService);
  private userService = inject(UserService);
  private friendService = inject(FriendService);
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private xpService = inject(XpService);

  constructor() {}

  ngOnInit() {
    // Get the user ID from the route
    this.route.paramMap.pipe(
      take(1),
      switchMap(params => {
        this.userId = params.get('id');
        console.log('UserProfilePage: User ID from route:', this.userId);

        // Get the current user
        return this.supabaseService.currentUser$;
      }),
      switchMap(authUser => {
        if (authUser) {
          this.currentUserId = authUser.id;
          console.log('UserProfilePage: Current user ID:', this.currentUserId);

          // Check if the user ID is valid
          if (this.userId) {
            console.log('UserProfilePage: Getting user profile for ID:', this.userId);
            // Get the user's profile - use getUser instead of getUserById
            return this.userService.getUser(this.userId);
          }
        }
        return of(null);
      })
    ).subscribe(user => {
      console.log('UserProfilePage: User data received:', user);
      if (user) {
        this.user = user;
        console.log('UserProfilePage: User data set:', this.user);
        this.calculateXpProgress();
      } else {
        console.log('UserProfilePage: User not found, navigating back');
        // User not found, navigate back
        this.router.navigate(['/']);
      }
    });
  }

  calculateXpProgress() {
    if (!this.user) {
      console.log('UserProfilePage: No user data, cannot calculate XP progress');
      return;
    }

    console.log('UserProfilePage: Calculating XP progress for user:', this.user.id);

    // Use the XP service to calculate the progress
    this.xpService.calculateXpProgress(this.user, EntityType.USER).subscribe(result => {
      if (result) {
        this.categories = result.categories;
        this.nextLevel = result.next_level;
        console.log('UserProfilePage: XP progress calculated:', this.categories);
        console.log('UserProfilePage: Next level:', this.nextLevel);
      } else {
        console.log('UserProfilePage: Failed to calculate XP progress');
      }
    });
  }

  goBack() {
    // Navigate back to the previous page
    window.history.back();
  }
}
