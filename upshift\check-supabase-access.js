﻿
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://tobifepmbrrrvshpvrqa.supabase.co';
const supabaseKey = 'YOUR_SUPABASE_KEY'; 

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkAccess() {

  try {
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);
    
    if (profilesError) {
    } else {
    }

    const { data: friends, error: friendsError } = await supabase
      .from('friends')
      .select('*')
      .limit(1);
    
    if (friendsError) {
    } else {
    }

    const { data: groups, error: groupsError } = await supabase
      .from('groups')
      .select('*')
      .limit(1);
    
    if (groupsError) {
    } else {
    }

    const { data: groupMembers, error: groupMembersError } = await supabase
      .from('group_members')
      .select('*')
      .limit(1);
    
    if (groupMembersError) {
    } else {
    }

    const { data: userBadges, error: userBadgesError } = await supabase
      .from('user_badges')
      .select('*')
      .limit(1);
    
    if (userBadgesError) {
    } else {
    }

    const { data: activities, error: activitiesError } = await supabase
      .from('activities')
      .select('*')
      .limit(1);
    
    if (activitiesError) {
    } else {
    }

    const { data: goals, error: goalsError } = await supabase
      .from('goals')
      .select('*')
      .limit(1);
    
    if (goalsError) {
    } else {
    }


  } catch (error) {
  }
}

checkAccess();
