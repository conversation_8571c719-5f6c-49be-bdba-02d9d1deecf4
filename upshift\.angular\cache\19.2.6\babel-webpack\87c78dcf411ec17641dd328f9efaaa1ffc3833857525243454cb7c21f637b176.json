{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _LoginComponent;\nimport { inject, EnvironmentInjector } from '@angular/core';\nimport { FormBuilder, ReactiveFormsModule } from '@angular/forms';\nimport { Router, RouterModule } from '@angular/router';\nimport { CommonModule } from '@angular/common';\nimport { IonicModule } from '@ionic/angular';\nimport { Preferences } from '@capacitor/preferences';\nimport { SupabaseService } from '../../services/supabase.service';\nimport { UserService } from '../../services/user.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nimport * as i2 from \"@angular/forms\";\nexport class LoginComponent {\n  constructor() {\n    this.fb = inject(FormBuilder);\n    this.supabaseService = inject(SupabaseService);\n    this.userService = inject(UserService);\n    this.router = inject(Router);\n    this.injector = inject(EnvironmentInjector);\n    this.form = this.fb.group({\n      email: [''],\n      password: ['']\n    });\n  }\n  login() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const {\n        email,\n        password\n      } = _this.form.value;\n      if (!email || !password) {\n        alert('Please enter both email and password');\n        return;\n      }\n      try {\n        const {\n          data,\n          error\n        } = yield _this.supabaseService.getClient().auth.signInWithPassword({\n          email,\n          password\n        });\n        if (error) {\n          alert('Login error: ' + error.message);\n          return;\n        }\n        yield _this.handlePostLogin(data.user.id);\n      } catch (error) {\n        alert('Login error: ' + error.message);\n      }\n    })();\n  }\n  signInWithGoogle() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          data,\n          error\n        } = yield _this2.supabaseService.getClient().auth.signInWithOAuth({\n          provider: 'google',\n          options: {\n            redirectTo: '/'\n          }\n        });\n        if (error) {\n          alert('Error signing in with Google: ' + error.message);\n          return;\n        }\n      } catch (error) {\n        alert('Error signing in with Google');\n      }\n    })();\n  }\n  signInWithApple() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          data,\n          error\n        } = yield _this3.supabaseService.getClient().auth.signInWithOAuth({\n          provider: 'apple',\n          options: {\n            redirectTo: '/'\n          }\n        });\n        if (error) {\n          alert('Error signing in with Apple: ' + error.message);\n          return;\n        }\n      } catch (error) {\n        alert('Error signing in with Apple');\n      }\n    })();\n  }\n  handlePostLogin(uid) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield Preferences.set({\n          key: 'uid',\n          value: uid\n        });\n        const {\n          data: userData,\n          error\n        } = yield _this4.supabaseService.getClient().from('profiles').select('*').eq('id', uid).single();\n        if (error && error.code !== 'PGRST116') {}\n        if (!userData) {\n          const authUser = _this4.supabaseService._currentUser.value;\n          if (authUser) {\n            yield _this4.createUserRecord(uid, authUser);\n          }\n        } else {\n          const {\n            error: updateError\n          } = yield _this4.supabaseService.getClient().from('profiles').update({\n            last_login: new Date()\n          }).eq('id', uid);\n          if (updateError) {}\n        }\n        _this4.router.navigateByUrl('/');\n      } catch (error) {}\n    })();\n  }\n}\n_LoginComponent = LoginComponent;\n_LoginComponent.ɵfac = function LoginComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _LoginComponent)();\n};\n_LoginComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _LoginComponent,\n  selectors: [[\"app-login\"]],\n  decls: 32,\n  vars: 1,\n  consts: [[1, \"ion-padding\"], [1, \"background-container\"], [1, \"gradient-bg\"], [1, \"celestial-body\"], [\"size\", \"12\", 1, \"ion-text-center\"], [1, \"upshift-title\"], [1, \"gradient-text\"], [\"size\", \"12\"], [3, \"ngSubmit\", \"formGroup\"], [\"formControlName\", \"email\", \"type\", \"email\", \"placeholder\", \"Email\", \"required\", \"\", 1, \"dark-input\", \"ion-margin-top\", \"ion-margin-bottom\"], [\"formControlName\", \"password\", \"type\", \"password\", \"placeholder\", \"Password\", \"required\", \"\", 1, \"dark-input\", \"ion-margin-top\", \"ion-margin-bottom\"], [\"expand\", \"block\", \"type\", \"submit\", 1, \"blue-button\"], [1, \"divider\"], [1, \"social-buttons\"], [1, \"social-button\", 3, \"click\"], [1, \"button-content\"], [\"name\", \"logo-google\"], [\"name\", \"logo-apple\"]],\n  template: function LoginComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ion-content\", 0)(1, \"div\", 1);\n      i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"ion-grid\")(5, \"ion-row\")(6, \"ion-col\", 4)(7, \"h1\", 5);\n      i0.ɵɵtext(8, \"Welcome back to \");\n      i0.ɵɵelementStart(9, \"span\", 6);\n      i0.ɵɵtext(10, \"Upshift\");\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(11, \"ion-row\")(12, \"ion-col\", 7)(13, \"form\", 8);\n      i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_13_listener() {\n        return ctx.login();\n      });\n      i0.ɵɵelement(14, \"ion-input\", 9)(15, \"ion-input\", 10);\n      i0.ɵɵelementStart(16, \"ion-button\", 11);\n      i0.ɵɵtext(17, \" Login \");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(18, \"div\", 12)(19, \"span\");\n      i0.ɵɵtext(20, \"or continue with\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(21, \"div\", 13)(22, \"ion-button\", 14);\n      i0.ɵɵlistener(\"click\", function LoginComponent_Template_ion_button_click_22_listener() {\n        return ctx.signInWithGoogle();\n      });\n      i0.ɵɵelementStart(23, \"div\", 15);\n      i0.ɵɵelement(24, \"ion-icon\", 16);\n      i0.ɵɵelementStart(25, \"span\");\n      i0.ɵɵtext(26, \"Login with Google\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(27, \"ion-button\", 14);\n      i0.ɵɵlistener(\"click\", function LoginComponent_Template_ion_button_click_27_listener() {\n        return ctx.signInWithApple();\n      });\n      i0.ɵɵelementStart(28, \"div\", 15);\n      i0.ɵɵelement(29, \"ion-icon\", 17);\n      i0.ɵɵelementStart(30, \"span\");\n      i0.ɵɵtext(31, \"Login with Apple\");\n      i0.ɵɵelementEnd()()()()()()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(13);\n      i0.ɵɵproperty(\"formGroup\", ctx.form);\n    }\n  },\n  dependencies: [CommonModule, IonicModule, i1.IonButton, i1.IonCol, i1.IonContent, i1.IonGrid, i1.IonIcon, i1.IonInput, i1.IonRow, i1.TextValueAccessor, ReactiveFormsModule, i2.ɵNgNoValidate, i2.NgControlStatus, i2.NgControlStatusGroup, i2.RequiredValidator, i2.FormGroupDirective, i2.FormControlName, RouterModule],\n  styles: [\"ion-content[_ngcontent-%COMP%] {\\n  --background: transparent;\\n}\\nion-content[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin: 20px 0;\\n  color: var(--text-secondary);\\n  font-size: 14px;\\n}\\nion-content[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]:before, ion-content[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  flex: 1;\\n  height: 1px;\\n  background: var(--border);\\n  opacity: 0.7;\\n}\\nion-content[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  padding: 0 16px;\\n}\\nion-content[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n  margin-bottom: 20px;\\n}\\nion-content[_ngcontent-%COMP%]   .background-container[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  overflow: hidden;\\n  z-index: -1;\\n}\\nion-content[_ngcontent-%COMP%]   .background-container[_ngcontent-%COMP%]   .gradient-bg[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: var(--bg);\\n}\\nion-content[_ngcontent-%COMP%]   .background-container[_ngcontent-%COMP%]   .celestial-body[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 250px;\\n  height: 250px;\\n  border-radius: 50%;\\n  filter: blur(25px);\\n  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 70%);\\n  animation: _ngcontent-%COMP%_celestial-cycle 30s ease-in-out infinite;\\n  opacity: 0.5;\\n  box-shadow: 0 0 60px rgba(255, 255, 255, 0.1);\\n}\\n@keyframes _ngcontent-%COMP%_celestial-cycle {\\n  0% {\\n    transform: translate(-150px, 120%);\\n    background: radial-gradient(circle, rgba(255, 190, 69, 0.4) 0%, rgba(255, 190, 69, 0) 70%);\\n    box-shadow: 0 0 80px rgba(255, 165, 0, 0.2);\\n  }\\n  25% {\\n    transform: translate(25%, 50%);\\n    background: radial-gradient(circle, rgba(255, 190, 69, 0.5) 0%, rgba(255, 190, 69, 0) 70%);\\n    box-shadow: 0 0 100px rgba(255, 165, 0, 0.3);\\n  }\\n  50% {\\n    transform: translate(120%, -150px);\\n    background: radial-gradient(circle, rgba(210, 235, 255, 0.4) 0%, rgba(210, 235, 255, 0) 70%);\\n    box-shadow: 0 0 80px rgba(173, 216, 230, 0.2);\\n  }\\n  75% {\\n    transform: translate(75%, 50%);\\n    background: radial-gradient(circle, rgba(210, 235, 255, 0.5) 0%, rgba(210, 235, 255, 0) 70%);\\n    box-shadow: 0 0 100px rgba(173, 216, 230, 0.3);\\n  }\\n  100% {\\n    transform: translate(-150px, 120%);\\n    background: radial-gradient(circle, rgba(255, 190, 69, 0.4) 0%, rgba(255, 190, 69, 0) 70%);\\n    box-shadow: 0 0 80px rgba(255, 165, 0, 0.2);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_gradient {\\n  0% {\\n    background-position: 0% 50%;\\n  }\\n  50% {\\n    background-position: 100% 50%;\\n  }\\n  100% {\\n    background-position: 0% 50%;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["inject", "EnvironmentInjector", "FormBuilder", "ReactiveFormsModule", "Router", "RouterModule", "CommonModule", "IonicModule", "Preferences", "SupabaseService", "UserService", "LoginComponent", "constructor", "fb", "supabaseService", "userService", "router", "injector", "form", "group", "email", "password", "login", "_this", "_asyncToGenerator", "value", "alert", "data", "error", "getClient", "auth", "signInWithPassword", "message", "handlePostLogin", "user", "id", "signInWithGoogle", "_this2", "signInWithOAuth", "provider", "options", "redirectTo", "signInWithApple", "_this3", "uid", "_this4", "set", "key", "userData", "from", "select", "eq", "single", "code", "authUser", "_currentUser", "createUserRecord", "updateError", "update", "last_login", "Date", "navigateByUrl", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_13_listener", "LoginComponent_Template_ion_button_click_22_listener", "LoginComponent_Template_ion_button_click_27_listener", "ɵɵadvance", "ɵɵproperty", "i1", "IonButton", "IonCol", "IonContent", "IonGrid", "IonIcon", "IonInput", "IonRow", "TextValueAccessor", "i2", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\auth\\login\\login.component.ts", "C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\auth\\login\\login.component.html"], "sourcesContent": ["import { Component, inject, EnvironmentInjector, runInInjectionContext } from '@angular/core';\r\nimport { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';\r\nimport { Router, RouterModule } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { Preferences } from '@capacitor/preferences';\r\nimport { SupabaseService } from '../../services/supabase.service';\r\nimport { UserService } from '../../services/user.service';\r\n\r\n@Component({\r\n  selector: 'app-login',\r\n  standalone: true,\r\n  templateUrl: './login.component.html',\r\n  styleUrls: ['./login.component.scss'],\r\n  imports: [CommonModule, IonicModule, ReactiveFormsModule, RouterModule],\r\n})\r\nexport class LoginComponent {\r\n  form: FormGroup;\r\n  segment: 'login';\r\n  private fb = inject(FormBuilder);\r\n  private supabaseService = inject(SupabaseService);\r\n  private userService = inject(UserService);\r\n  private router = inject(Router);\r\n  private injector = inject(EnvironmentInjector);\r\n\r\n  constructor() {\r\n    this.form = this.fb.group({\r\n      email: [''],\r\n      password: [''],\r\n    });\r\n  }\r\n\r\n\r\n  async login() {\r\n    const { email, password } = this.form.value;\r\n\r\n    if (!email || !password) {\r\n      alert('Please enter both email and password');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const { data, error } = await this.supabaseService.getClient().auth.signInWithPassword({\r\n        email,\r\n        password\r\n      });\r\n\r\n      if (error) {\r\n        alert('Login error: ' + error.message);\r\n        return;\r\n      }\r\n\r\n      await this.handlePostLogin(data.user.id);\r\n    } catch (error: any) {\r\n      alert('Login error: ' + error.message);\r\n    }\r\n  }\r\n\r\n  async signInWithGoogle() {\r\n    try {\r\n      const { data, error } = await this.supabaseService.getClient().auth.signInWithOAuth({\r\n        provider: 'google',\r\n        options: {\r\n          redirectTo: '/'\r\n        }\r\n      });\r\n\r\n      if (error) {\r\n        alert('Error signing in with Google: ' + error.message);\r\n        return;\r\n      }\r\n\r\n    } catch (error: any) {\r\n      alert('Error signing in with Google');\r\n    }\r\n  }\r\n\r\n  async signInWithApple() {\r\n    try {\r\n      const { data, error } = await this.supabaseService.getClient().auth.signInWithOAuth({\r\n        provider: 'apple',\r\n        options: {\r\n          redirectTo: '/'\r\n        }\r\n      });\r\n\r\n      if (error) {\r\n        alert('Error signing in with Apple: ' + error.message);\r\n        return;\r\n      }\r\n\r\n    } catch (error: any) {\r\n      alert('Error signing in with Apple');\r\n    }\r\n  }\r\n\r\n  private async handlePostLogin(uid: string) {\r\n    try {\r\n      await Preferences.set({ key: 'uid', value: uid });\r\n\r\n      const { data: userData, error } = await this.supabaseService.getClient()\r\n        .from('profiles')\r\n        .select('*')\r\n        .eq('id', uid)\r\n        .single();\r\n\r\n      if (error && error.code !== 'PGRST116') { \n      }\r\n\r\n      if (!userData) {\r\n        const authUser = this.supabaseService._currentUser.value;\r\n        if (authUser) {\r\n          await this.createUserRecord(uid, authUser);\r\n        }\r\n      } else {\r\n        const { error: updateError } = await this.supabaseService.getClient()\r\n          .from('profiles')\r\n          .update({ last_login: new Date() })\r\n          .eq('id', uid);\r\n\r\n        if (updateError) {\r\n        }\r\n      }\r\n\r\n      this.router.navigateByUrl('/');\r\n    } catch (error) {\r\n    }\r\n  }\r\n}", "<ion-content class=\"ion-padding\">\r\n    <div class=\"background-container\">\r\n      <div class=\"gradient-bg\"></div>\r\n      <div class=\"celestial-body\"></div>\r\n    </div>\r\n\r\n    <ion-grid>\r\n      <ion-row>\r\n        <ion-col size=\"12\" class=\"ion-text-center\">\r\n          <h1 class=\"upshift-title\">Welcome back to <span class=\"gradient-text\">Upshift</span></h1>\r\n        </ion-col>\r\n      </ion-row>\r\n\r\n      <ion-row>\r\n        <ion-col size=\"12\">\r\n          <form [formGroup]=\"form\" (ngSubmit)=\"login()\">\r\n            <ion-input\r\n              formControlName=\"email\"\r\n              type=\"email\"\r\n              placeholder=\"Email\"\r\n              required\r\n              class=\"dark-input ion-margin-top ion-margin-bottom\"\r\n            ></ion-input>\r\n\r\n            <ion-input  \r\n              formControlName=\"password\"\r\n              type=\"password\"\r\n              placeholder=\"Password\"\r\n              required\r\n              class=\"dark-input ion-margin-top ion-margin-bottom\"\r\n            ></ion-input>\r\n\r\n            <ion-button expand=\"block\" type=\"submit\" class=\"blue-button\">\r\n              Login\r\n            </ion-button>\r\n          </form>\r\n\r\n          <div class=\"divider\">\r\n            <span>or continue with</span>\r\n          </div>\r\n\r\n          <div class=\"social-buttons\">\r\n            <ion-button class=\"social-button\" (click)=\"signInWithGoogle()\">\r\n              <div class=\"button-content\">\r\n                <ion-icon name=\"logo-google\"></ion-icon>\r\n                <span>Login with Google</span>\r\n              </div>\r\n            </ion-button>\r\n\r\n            <ion-button class=\"social-button\" (click)=\"signInWithApple()\">\r\n              <div class=\"button-content\">\r\n                <ion-icon name=\"logo-apple\"></ion-icon>\r\n                <span>Login with Apple</span>\r\n              </div>\r\n            </ion-button>\r\n          </div>\r\n        </ion-col>\r\n      </ion-row>\r\n    </ion-grid>\r\n  </ion-content>"], "mappings": ";;AAAA,SAAoBA,MAAM,EAAEC,mBAAmB,QAA+B,eAAe;AAC7F,SAASC,WAAW,EAAaC,mBAAmB,QAAQ,gBAAgB;AAC5E,SAASC,MAAM,EAAEC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,WAAW,QAAQ,6BAA6B;;;;AASzD,OAAM,MAAOC,cAAc;EASzBC,YAAA;IANQ,KAAAC,EAAE,GAAGb,MAAM,CAACE,WAAW,CAAC;IACxB,KAAAY,eAAe,GAAGd,MAAM,CAACS,eAAe,CAAC;IACzC,KAAAM,WAAW,GAAGf,MAAM,CAACU,WAAW,CAAC;IACjC,KAAAM,MAAM,GAAGhB,MAAM,CAACI,MAAM,CAAC;IACvB,KAAAa,QAAQ,GAAGjB,MAAM,CAACC,mBAAmB,CAAC;IAG5C,IAAI,CAACiB,IAAI,GAAG,IAAI,CAACL,EAAE,CAACM,KAAK,CAAC;MACxBC,KAAK,EAAE,CAAC,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC,EAAE;KACd,CAAC;EACJ;EAGMC,KAAKA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACT,MAAM;QAAEJ,KAAK;QAAEC;MAAQ,CAAE,GAAGE,KAAI,CAACL,IAAI,CAACO,KAAK;MAE3C,IAAI,CAACL,KAAK,IAAI,CAACC,QAAQ,EAAE;QACvBK,KAAK,CAAC,sCAAsC,CAAC;QAC7C;MACF;MAEA,IAAI;QACF,MAAM;UAAEC,IAAI;UAAEC;QAAK,CAAE,SAASL,KAAI,CAACT,eAAe,CAACe,SAAS,EAAE,CAACC,IAAI,CAACC,kBAAkB,CAAC;UACrFX,KAAK;UACLC;SACD,CAAC;QAEF,IAAIO,KAAK,EAAE;UACTF,KAAK,CAAC,eAAe,GAAGE,KAAK,CAACI,OAAO,CAAC;UACtC;QACF;QAEA,MAAMT,KAAI,CAACU,eAAe,CAACN,IAAI,CAACO,IAAI,CAACC,EAAE,CAAC;MAC1C,CAAC,CAAC,OAAOP,KAAU,EAAE;QACnBF,KAAK,CAAC,eAAe,GAAGE,KAAK,CAACI,OAAO,CAAC;MACxC;IAAC;EACH;EAEMI,gBAAgBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAb,iBAAA;MACpB,IAAI;QACF,MAAM;UAAEG,IAAI;UAAEC;QAAK,CAAE,SAASS,MAAI,CAACvB,eAAe,CAACe,SAAS,EAAE,CAACC,IAAI,CAACQ,eAAe,CAAC;UAClFC,QAAQ,EAAE,QAAQ;UAClBC,OAAO,EAAE;YACPC,UAAU,EAAE;;SAEf,CAAC;QAEF,IAAIb,KAAK,EAAE;UACTF,KAAK,CAAC,gCAAgC,GAAGE,KAAK,CAACI,OAAO,CAAC;UACvD;QACF;MAEF,CAAC,CAAC,OAAOJ,KAAU,EAAE;QACnBF,KAAK,CAAC,8BAA8B,CAAC;MACvC;IAAC;EACH;EAEMgB,eAAeA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAnB,iBAAA;MACnB,IAAI;QACF,MAAM;UAAEG,IAAI;UAAEC;QAAK,CAAE,SAASe,MAAI,CAAC7B,eAAe,CAACe,SAAS,EAAE,CAACC,IAAI,CAACQ,eAAe,CAAC;UAClFC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE;YACPC,UAAU,EAAE;;SAEf,CAAC;QAEF,IAAIb,KAAK,EAAE;UACTF,KAAK,CAAC,+BAA+B,GAAGE,KAAK,CAACI,OAAO,CAAC;UACtD;QACF;MAEF,CAAC,CAAC,OAAOJ,KAAU,EAAE;QACnBF,KAAK,CAAC,6BAA6B,CAAC;MACtC;IAAC;EACH;EAEcO,eAAeA,CAACW,GAAW;IAAA,IAAAC,MAAA;IAAA,OAAArB,iBAAA;MACvC,IAAI;QACF,MAAMhB,WAAW,CAACsC,GAAG,CAAC;UAAEC,GAAG,EAAE,KAAK;UAAEtB,KAAK,EAAEmB;QAAG,CAAE,CAAC;QAEjD,MAAM;UAAEjB,IAAI,EAAEqB,QAAQ;UAAEpB;QAAK,CAAE,SAASiB,MAAI,CAAC/B,eAAe,CAACe,SAAS,EAAE,CACrEoB,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,IAAI,EAAEP,GAAG,CAAC,CACbQ,MAAM,EAAE;QAEX,IAAIxB,KAAK,IAAIA,KAAK,CAACyB,IAAI,KAAK,UAAU,EAAE,CACxC;QAEA,IAAI,CAACL,QAAQ,EAAE;UACb,MAAMM,QAAQ,GAAGT,MAAI,CAAC/B,eAAe,CAACyC,YAAY,CAAC9B,KAAK;UACxD,IAAI6B,QAAQ,EAAE;YACZ,MAAMT,MAAI,CAACW,gBAAgB,CAACZ,GAAG,EAAEU,QAAQ,CAAC;UAC5C;QACF,CAAC,MAAM;UACL,MAAM;YAAE1B,KAAK,EAAE6B;UAAW,CAAE,SAASZ,MAAI,CAAC/B,eAAe,CAACe,SAAS,EAAE,CAClEoB,IAAI,CAAC,UAAU,CAAC,CAChBS,MAAM,CAAC;YAAEC,UAAU,EAAE,IAAIC,IAAI;UAAE,CAAE,CAAC,CAClCT,EAAE,CAAC,IAAI,EAAEP,GAAG,CAAC;UAEhB,IAAIa,WAAW,EAAE,CACjB;QACF;QAEAZ,MAAI,CAAC7B,MAAM,CAAC6C,aAAa,CAAC,GAAG,CAAC;MAChC,CAAC,CAAC,OAAOjC,KAAK,EAAE,CAChB;IAAC;EACH;;kBA/GWjB,cAAc;;mCAAdA,eAAc;AAAA;;QAAdA,eAAc;EAAAmD,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCfvBE,EADJ,CAAAC,cAAA,qBAAiC,aACK;MAEhCD,EADA,CAAAE,SAAA,aAA+B,aACG;MACpCF,EAAA,CAAAG,YAAA,EAAM;MAKAH,EAHN,CAAAC,cAAA,eAAU,cACC,iBACoC,YACf;MAAAD,EAAA,CAAAI,MAAA,uBAAgB;MAAAJ,EAAA,CAAAC,cAAA,cAA4B;MAAAD,EAAA,CAAAI,MAAA,eAAO;MAEjFJ,EAFiF,CAAAG,YAAA,EAAO,EAAK,EACjF,EACF;MAINH,EAFJ,CAAAC,cAAA,eAAS,kBACY,eAC6B;MAArBD,EAAA,CAAAK,UAAA,sBAAAC,kDAAA;QAAA,OAAYP,GAAA,CAAA/C,KAAA,EAAO;MAAA,EAAC;MAS3CgD,EARA,CAAAE,SAAA,oBAMa,qBAQA;MAEbF,EAAA,CAAAC,cAAA,sBAA6D;MAC3DD,EAAA,CAAAI,MAAA,eACF;MACFJ,EADE,CAAAG,YAAA,EAAa,EACR;MAGLH,EADF,CAAAC,cAAA,eAAqB,YACb;MAAAD,EAAA,CAAAI,MAAA,wBAAgB;MACxBJ,EADwB,CAAAG,YAAA,EAAO,EACzB;MAGJH,EADF,CAAAC,cAAA,eAA4B,sBACqC;MAA7BD,EAAA,CAAAK,UAAA,mBAAAE,qDAAA;QAAA,OAASR,GAAA,CAAAjC,gBAAA,EAAkB;MAAA,EAAC;MAC5DkC,EAAA,CAAAC,cAAA,eAA4B;MAC1BD,EAAA,CAAAE,SAAA,oBAAwC;MACxCF,EAAA,CAAAC,cAAA,YAAM;MAAAD,EAAA,CAAAI,MAAA,yBAAiB;MAE3BJ,EAF2B,CAAAG,YAAA,EAAO,EAC1B,EACK;MAEbH,EAAA,CAAAC,cAAA,sBAA8D;MAA5BD,EAAA,CAAAK,UAAA,mBAAAG,qDAAA;QAAA,OAAST,GAAA,CAAA3B,eAAA,EAAiB;MAAA,EAAC;MAC3D4B,EAAA,CAAAC,cAAA,eAA4B;MAC1BD,EAAA,CAAAE,SAAA,oBAAuC;MACvCF,EAAA,CAAAC,cAAA,YAAM;MAAAD,EAAA,CAAAI,MAAA,wBAAgB;MAOpCJ,EAPoC,CAAAG,YAAA,EAAO,EACzB,EACK,EACT,EACE,EACF,EACD,EACC;;;MA5CAH,EAAA,CAAAS,SAAA,IAAkB;MAAlBT,EAAA,CAAAU,UAAA,cAAAX,GAAA,CAAAnD,IAAA,CAAkB;;;iBDDtBZ,YAAY,EAAEC,WAAW,EAAA0E,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,MAAA,EAAAF,EAAA,CAAAG,UAAA,EAAAH,EAAA,CAAAI,OAAA,EAAAJ,EAAA,CAAAK,OAAA,EAAAL,EAAA,CAAAM,QAAA,EAAAN,EAAA,CAAAO,MAAA,EAAAP,EAAA,CAAAQ,iBAAA,EAAEtF,mBAAmB,EAAAuF,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,oBAAA,EAAAH,EAAA,CAAAI,iBAAA,EAAAJ,EAAA,CAAAK,kBAAA,EAAAL,EAAA,CAAAM,eAAA,EAAE3F,YAAY;EAAA4F,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}