{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _BadgeService;\nimport { inject } from '@angular/core';\nimport { from, of } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { SupabaseService } from './supabase.service';\nimport * as i0 from \"@angular/core\";\nexport class BadgeService {\n  constructor() {\n    this.supabaseService = inject(SupabaseService);\n  }\n  getUserBadges(userId) {\n    return from(this.supabaseService.supabase.from('user_badges').select('*').eq('user_id', userId)).pipe(map(response => {\n      if (response.error) {\n        if (response.error.code === '42501' || response.error.message.includes('permission') || response.error.message.includes('policy')) {\n          throw new Error(`Permission denied: ${response.error.message}. Please check Supabase RLS policies for user_badges table.`);\n        }\n        return undefined;\n      }\n      if (response.data && response.data.length > 0) {\n        return response.data[0];\n      } else {\n        return undefined;\n      }\n    }), catchError(error => {\n      if (error.message && error.message.includes('Permission denied')) {\n        throw error;\n      }\n      return of(undefined);\n    }));\n  }\n  createUserBadges(userId) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          data: existingBadges,\n          error: checkError\n        } = yield _this.supabaseService.supabase.from('user_badges').select('id').eq('user_id', userId);\n        if (checkError) {\n          if (checkError.code === '42501' || checkError.message.includes('permission') || checkError.message.includes('policy')) {\n            throw new Error(`Permission denied: ${checkError.message}. Please check Supabase RLS policies for user_badges table.`);\n          }\n          throw new Error(checkError.message);\n        }\n        if (existingBadges && existingBadges.length > 0) {\n          return existingBadges[0].id;\n        }\n        const newBadges = {\n          user_id: userId,\n          badge_newbie: false,\n          badge_warrior: false,\n          badge_hardcore: false,\n          badge_peak_performer: false,\n          badge_indestructible: false,\n          badge_professional: false,\n          badge_streak_7_days: false,\n          badge_streak_30_days: false,\n          badge_streak_100_days: false,\n          badge_streak_365_days: false,\n          badge_sidequest_streak_7_days: false,\n          badge_sidequest_streak_30_days: false,\n          badge_sidequest_streak_100_days: false,\n          badge_sidequest_streak_365_days: false,\n          badge_friends_5: false,\n          badge_friends_10: false,\n          badge_strength_master: false,\n          badge_money_master: false,\n          badge_health_master: false,\n          badge_knowledge_master: false,\n          created_at: new Date(),\n          updated_at: new Date()\n        };\n        const {\n          data,\n          error\n        } = yield _this.supabaseService.supabase.from('user_badges').insert(newBadges).select('id').single();\n        if (error) {\n          if (error.code === '42501' || error.message.includes('permission') || error.message.includes('policy')) {\n            throw new Error(`Permission denied: ${error.message}. Please check Supabase RLS policies for user_badges table.`);\n          }\n          throw new Error(error.message);\n        }\n        return data.id;\n      } catch (error) {\n        throw error;\n      }\n    })();\n  }\n  updateUserBadges(badgeId, data) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const updates = {\n          ...data,\n          updated_at: new Date()\n        };\n        const {\n          error\n        } = yield _this2.supabaseService.supabase.from('user_badges').update(updates).eq('id', badgeId);\n        if (error) {\n          if (error.code === '42501' || error.message.includes('permission') || error.message.includes('policy')) {\n            throw new Error(`Permission denied: ${error.message}. Please check Supabase RLS policies for user_badges table.`);\n          }\n          throw new Error(error.message);\n        }\n      } catch (error) {\n        throw error;\n      }\n    })();\n  }\n  updateCategoryBadges(userId, category) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          data: badges,\n          error: findError\n        } = yield _this3.supabaseService.supabase.from('user_badges').select('id').eq('user_id', userId);\n        if (findError) {\n          if (findError.code === '42501' || findError.message.includes('permission') || findError.message.includes('policy')) {\n            throw new Error(`Permission denied: ${findError.message}. Please check Supabase RLS policies for user_badges table.`);\n          }\n          throw new Error(findError.message);\n        }\n        if (!badges || badges.length === 0) {\n          yield _this3.createUserBadges(userId);\n          return _this3.updateCategoryBadges(userId, category);\n        }\n        const badgeId = badges[0].id;\n        const badgeField = `badge_${category}_master`;\n        const {\n          error: updateError\n        } = yield _this3.supabaseService.supabase.from('user_badges').update({\n          [badgeField]: true,\n          updated_at: new Date()\n        }).eq('id', badgeId);\n        if (updateError) {\n          if (updateError.code === '42501' || updateError.message.includes('permission') || updateError.message.includes('policy')) {\n            throw new Error(`Permission denied: ${updateError.message}. Please check Supabase RLS policies for user_badges table.`);\n          }\n          throw new Error(updateError.message);\n        }\n      } catch (error) {\n        throw error;\n      }\n    })();\n  }\n}\n_BadgeService = BadgeService;\n_BadgeService.ɵfac = function BadgeService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _BadgeService)();\n};\n_BadgeService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _BadgeService,\n  factory: _BadgeService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "names": ["inject", "from", "of", "map", "catchError", "SupabaseService", "BadgeService", "constructor", "supabaseService", "getUserBadges", "userId", "supabase", "select", "eq", "pipe", "response", "error", "code", "message", "includes", "Error", "undefined", "data", "length", "createUserBadges", "_this", "_asyncToGenerator", "existingBadges", "checkError", "id", "newBadges", "user_id", "badge_newbie", "badge_warrior", "badge_hardcore", "badge_peak_performer", "badge_indestructible", "badge_professional", "badge_streak_7_days", "badge_streak_30_days", "badge_streak_100_days", "badge_streak_365_days", "badge_sidequest_streak_7_days", "badge_sidequest_streak_30_days", "badge_sidequest_streak_100_days", "badge_sidequest_streak_365_days", "badge_friends_5", "badge_friends_10", "badge_strength_master", "badge_money_master", "badge_health_master", "badge_knowledge_master", "created_at", "Date", "updated_at", "insert", "single", "updateUserBadges", "badgeId", "_this2", "updates", "update", "updateCategoryBadges", "category", "_this3", "badges", "findError", "badgeField", "updateError", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\services\\badge.service.ts"], "sourcesContent": ["import { Injectable, inject } from '@angular/core';\r\nimport { UserBadges } from '../models/friend.model';\r\nimport { Observable, from, of } from 'rxjs';\r\nimport { map, catchError } from 'rxjs/operators';\r\nimport { SupabaseService } from './supabase.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class BadgeService {\r\n  private supabaseService = inject(SupabaseService);\r\n\r\n  constructor() {}\r\n\r\n  getUserBadges(userId: string): Observable<UserBadges | undefined> {\r\n\r\n    return from(\r\n      this.supabaseService.supabase\r\n        .from('user_badges')\r\n        .select('*')\r\n        .eq('user_id', userId)\r\n    ).pipe(\r\n      map(response => {\r\n        if (response.error) {\r\n\r\n          if (response.error.code === '42501' ||\r\n              response.error.message.includes('permission') ||\r\n              response.error.message.includes('policy')) {\r\n            throw new Error(`Permission denied: ${response.error.message}. Please check Supabase RLS policies for user_badges table.`);\r\n          }\r\n\r\n          return undefined;\r\n        }\r\n\r\n        if (response.data && response.data.length > 0) {\r\n          return response.data[0] as UserBadges;\r\n        } else {\r\n          return undefined;\r\n        }\r\n      }),\r\n      catchError(error => {\r\n        if (error.message && error.message.includes('Permission denied')) {\r\n          throw error;\r\n        }\r\n        return of(undefined);\r\n      })\r\n    );\r\n  }\r\n\r\n  async createUserBadges(userId: string): Promise<string> {\r\n\r\n    try {\r\n      const { data: existingBadges, error: checkError } = await this.supabaseService.supabase\r\n        .from('user_badges')\r\n        .select('id')\r\n        .eq('user_id', userId);\r\n\r\n      if (checkError) {\r\n\r\n        if (checkError.code === '42501' || checkError.message.includes('permission') || checkError.message.includes('policy')) {\r\n          throw new Error(`Permission denied: ${checkError.message}. Please check Supabase RLS policies for user_badges table.`);\r\n        }\r\n\r\n        throw new Error(checkError.message);\r\n      }\r\n\r\n      if (existingBadges && existingBadges.length > 0) {\r\n        return existingBadges[0].id;\r\n      }\r\n\r\n      const newBadges: UserBadges = {\r\n        user_id: userId,\r\n        badge_newbie: false,\r\n        badge_warrior: false,\r\n        badge_hardcore: false,\r\n        badge_peak_performer: false,\r\n        badge_indestructible: false,\r\n        badge_professional: false,\r\n        badge_streak_7_days: false,\r\n        badge_streak_30_days: false,\r\n        badge_streak_100_days: false,\r\n        badge_streak_365_days: false,\r\n        badge_sidequest_streak_7_days: false,\r\n        badge_sidequest_streak_30_days: false,\r\n        badge_sidequest_streak_100_days: false,\r\n        badge_sidequest_streak_365_days: false,\r\n        badge_friends_5: false,\r\n        badge_friends_10: false,\r\n        badge_strength_master: false,\r\n        badge_money_master: false,\r\n        badge_health_master: false,\r\n        badge_knowledge_master: false,\r\n        created_at: new Date(),\r\n        updated_at: new Date()\r\n      };\r\n\r\n\r\n      const { data, error } = await this.supabaseService.supabase\r\n        .from('user_badges')\r\n        .insert(newBadges)\r\n        .select('id')\r\n        .single();\r\n\r\n      if (error) {\r\n\r\n        if (error.code === '42501' || error.message.includes('permission') || error.message.includes('policy')) {\r\n          throw new Error(`Permission denied: ${error.message}. Please check Supabase RLS policies for user_badges table.`);\r\n        }\r\n\r\n        throw new Error(error.message);\r\n      }\r\n\r\n      return data.id;\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async updateUserBadges(badgeId: string, data: Partial<UserBadges>): Promise<void> {\r\n\r\n    try {\r\n      const updates = {\r\n        ...data,\r\n        updated_at: new Date()\r\n      };\r\n\r\n      const { error } = await this.supabaseService.supabase\r\n        .from('user_badges')\r\n        .update(updates)\r\n        .eq('id', badgeId);\r\n\r\n      if (error) {\r\n\r\n        if (error.code === '42501' || error.message.includes('permission') || error.message.includes('policy')) {\r\n          throw new Error(`Permission denied: ${error.message}. Please check Supabase RLS policies for user_badges table.`);\r\n        }\r\n\r\n        throw new Error(error.message);\r\n      }\r\n\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async updateCategoryBadges(userId: string, category: string): Promise<void> {\r\n\r\n    try {\r\n      const { data: badges, error: findError } = await this.supabaseService.supabase\r\n        .from('user_badges')\r\n        .select('id')\r\n        .eq('user_id', userId);\r\n\r\n      if (findError) {\r\n\r\n        if (findError.code === '42501' || findError.message.includes('permission') || findError.message.includes('policy')) {\r\n          throw new Error(`Permission denied: ${findError.message}. Please check Supabase RLS policies for user_badges table.`);\r\n        }\r\n\r\n        throw new Error(findError.message);\r\n      }\r\n\r\n      if (!badges || badges.length === 0) {\r\n        await this.createUserBadges(userId);\r\n        return this.updateCategoryBadges(userId, category); \n      }\r\n\r\n      const badgeId = badges[0].id;\r\n      const badgeField = `badge_${category}_master`;\r\n\r\n\r\n      const { error: updateError } = await this.supabaseService.supabase\r\n        .from('user_badges')\r\n        .update({\r\n          [badgeField]: true,\r\n          updated_at: new Date()\r\n        })\r\n        .eq('id', badgeId);\r\n\r\n      if (updateError) {\r\n\r\n        if (updateError.code === '42501' || updateError.message.includes('permission') || updateError.message.includes('policy')) {\r\n          throw new Error(`Permission denied: ${updateError.message}. Please check Supabase RLS policies for user_badges table.`);\r\n        }\r\n\r\n        throw new Error(updateError.message);\r\n      }\r\n\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\n"], "mappings": ";;AAAA,SAAqBA,MAAM,QAAQ,eAAe;AAElD,SAAqBC,IAAI,EAAEC,EAAE,QAAQ,MAAM;AAC3C,SAASC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;AAChD,SAASC,eAAe,QAAQ,oBAAoB;;AAKpD,OAAM,MAAOC,YAAY;EAGvBC,YAAA;IAFQ,KAAAC,eAAe,GAAGR,MAAM,CAACK,eAAe,CAAC;EAElC;EAEfI,aAAaA,CAACC,MAAc;IAE1B,OAAOT,IAAI,CACT,IAAI,CAACO,eAAe,CAACG,QAAQ,CAC1BV,IAAI,CAAC,aAAa,CAAC,CACnBW,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAEH,MAAM,CAAC,CACzB,CAACI,IAAI,CACJX,GAAG,CAACY,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,KAAK,EAAE;QAElB,IAAID,QAAQ,CAACC,KAAK,CAACC,IAAI,KAAK,OAAO,IAC/BF,QAAQ,CAACC,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC,YAAY,CAAC,IAC7CJ,QAAQ,CAACC,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;UAC7C,MAAM,IAAIC,KAAK,CAAC,sBAAsBL,QAAQ,CAACC,KAAK,CAACE,OAAO,6DAA6D,CAAC;QAC5H;QAEA,OAAOG,SAAS;MAClB;MAEA,IAAIN,QAAQ,CAACO,IAAI,IAAIP,QAAQ,CAACO,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QAC7C,OAAOR,QAAQ,CAACO,IAAI,CAAC,CAAC,CAAe;MACvC,CAAC,MAAM;QACL,OAAOD,SAAS;MAClB;IACF,CAAC,CAAC,EACFjB,UAAU,CAACY,KAAK,IAAG;MACjB,IAAIA,KAAK,CAACE,OAAO,IAAIF,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;QAChE,MAAMH,KAAK;MACb;MACA,OAAOd,EAAE,CAACmB,SAAS,CAAC;IACtB,CAAC,CAAC,CACH;EACH;EAEMG,gBAAgBA,CAACd,MAAc;IAAA,IAAAe,KAAA;IAAA,OAAAC,iBAAA;MAEnC,IAAI;QACF,MAAM;UAAEJ,IAAI,EAAEK,cAAc;UAAEX,KAAK,EAAEY;QAAU,CAAE,SAASH,KAAI,CAACjB,eAAe,CAACG,QAAQ,CACpFV,IAAI,CAAC,aAAa,CAAC,CACnBW,MAAM,CAAC,IAAI,CAAC,CACZC,EAAE,CAAC,SAAS,EAAEH,MAAM,CAAC;QAExB,IAAIkB,UAAU,EAAE;UAEd,IAAIA,UAAU,CAACX,IAAI,KAAK,OAAO,IAAIW,UAAU,CAACV,OAAO,CAACC,QAAQ,CAAC,YAAY,CAAC,IAAIS,UAAU,CAACV,OAAO,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACrH,MAAM,IAAIC,KAAK,CAAC,sBAAsBQ,UAAU,CAACV,OAAO,6DAA6D,CAAC;UACxH;UAEA,MAAM,IAAIE,KAAK,CAACQ,UAAU,CAACV,OAAO,CAAC;QACrC;QAEA,IAAIS,cAAc,IAAIA,cAAc,CAACJ,MAAM,GAAG,CAAC,EAAE;UAC/C,OAAOI,cAAc,CAAC,CAAC,CAAC,CAACE,EAAE;QAC7B;QAEA,MAAMC,SAAS,GAAe;UAC5BC,OAAO,EAAErB,MAAM;UACfsB,YAAY,EAAE,KAAK;UACnBC,aAAa,EAAE,KAAK;UACpBC,cAAc,EAAE,KAAK;UACrBC,oBAAoB,EAAE,KAAK;UAC3BC,oBAAoB,EAAE,KAAK;UAC3BC,kBAAkB,EAAE,KAAK;UACzBC,mBAAmB,EAAE,KAAK;UAC1BC,oBAAoB,EAAE,KAAK;UAC3BC,qBAAqB,EAAE,KAAK;UAC5BC,qBAAqB,EAAE,KAAK;UAC5BC,6BAA6B,EAAE,KAAK;UACpCC,8BAA8B,EAAE,KAAK;UACrCC,+BAA+B,EAAE,KAAK;UACtCC,+BAA+B,EAAE,KAAK;UACtCC,eAAe,EAAE,KAAK;UACtBC,gBAAgB,EAAE,KAAK;UACvBC,qBAAqB,EAAE,KAAK;UAC5BC,kBAAkB,EAAE,KAAK;UACzBC,mBAAmB,EAAE,KAAK;UAC1BC,sBAAsB,EAAE,KAAK;UAC7BC,UAAU,EAAE,IAAIC,IAAI,EAAE;UACtBC,UAAU,EAAE,IAAID,IAAI;SACrB;QAGD,MAAM;UAAE/B,IAAI;UAAEN;QAAK,CAAE,SAASS,KAAI,CAACjB,eAAe,CAACG,QAAQ,CACxDV,IAAI,CAAC,aAAa,CAAC,CACnBsD,MAAM,CAACzB,SAAS,CAAC,CACjBlB,MAAM,CAAC,IAAI,CAAC,CACZ4C,MAAM,EAAE;QAEX,IAAIxC,KAAK,EAAE;UAET,IAAIA,KAAK,CAACC,IAAI,KAAK,OAAO,IAAID,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC,YAAY,CAAC,IAAIH,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACtG,MAAM,IAAIC,KAAK,CAAC,sBAAsBJ,KAAK,CAACE,OAAO,6DAA6D,CAAC;UACnH;UAEA,MAAM,IAAIE,KAAK,CAACJ,KAAK,CAACE,OAAO,CAAC;QAChC;QAEA,OAAOI,IAAI,CAACO,EAAE;MAChB,CAAC,CAAC,OAAOb,KAAK,EAAE;QACd,MAAMA,KAAK;MACb;IAAC;EACH;EAEMyC,gBAAgBA,CAACC,OAAe,EAAEpC,IAAyB;IAAA,IAAAqC,MAAA;IAAA,OAAAjC,iBAAA;MAE/D,IAAI;QACF,MAAMkC,OAAO,GAAG;UACd,GAAGtC,IAAI;UACPgC,UAAU,EAAE,IAAID,IAAI;SACrB;QAED,MAAM;UAAErC;QAAK,CAAE,SAAS2C,MAAI,CAACnD,eAAe,CAACG,QAAQ,CAClDV,IAAI,CAAC,aAAa,CAAC,CACnB4D,MAAM,CAACD,OAAO,CAAC,CACf/C,EAAE,CAAC,IAAI,EAAE6C,OAAO,CAAC;QAEpB,IAAI1C,KAAK,EAAE;UAET,IAAIA,KAAK,CAACC,IAAI,KAAK,OAAO,IAAID,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC,YAAY,CAAC,IAAIH,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACtG,MAAM,IAAIC,KAAK,CAAC,sBAAsBJ,KAAK,CAACE,OAAO,6DAA6D,CAAC;UACnH;UAEA,MAAM,IAAIE,KAAK,CAACJ,KAAK,CAACE,OAAO,CAAC;QAChC;MAEF,CAAC,CAAC,OAAOF,KAAK,EAAE;QACd,MAAMA,KAAK;MACb;IAAC;EACH;EAEM8C,oBAAoBA,CAACpD,MAAc,EAAEqD,QAAgB;IAAA,IAAAC,MAAA;IAAA,OAAAtC,iBAAA;MAEzD,IAAI;QACF,MAAM;UAAEJ,IAAI,EAAE2C,MAAM;UAAEjD,KAAK,EAAEkD;QAAS,CAAE,SAASF,MAAI,CAACxD,eAAe,CAACG,QAAQ,CAC3EV,IAAI,CAAC,aAAa,CAAC,CACnBW,MAAM,CAAC,IAAI,CAAC,CACZC,EAAE,CAAC,SAAS,EAAEH,MAAM,CAAC;QAExB,IAAIwD,SAAS,EAAE;UAEb,IAAIA,SAAS,CAACjD,IAAI,KAAK,OAAO,IAAIiD,SAAS,CAAChD,OAAO,CAACC,QAAQ,CAAC,YAAY,CAAC,IAAI+C,SAAS,CAAChD,OAAO,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YAClH,MAAM,IAAIC,KAAK,CAAC,sBAAsB8C,SAAS,CAAChD,OAAO,6DAA6D,CAAC;UACvH;UAEA,MAAM,IAAIE,KAAK,CAAC8C,SAAS,CAAChD,OAAO,CAAC;QACpC;QAEA,IAAI,CAAC+C,MAAM,IAAIA,MAAM,CAAC1C,MAAM,KAAK,CAAC,EAAE;UAClC,MAAMyC,MAAI,CAACxC,gBAAgB,CAACd,MAAM,CAAC;UACnC,OAAOsD,MAAI,CAACF,oBAAoB,CAACpD,MAAM,EAAEqD,QAAQ,CAAC;QACpD;QAEA,MAAML,OAAO,GAAGO,MAAM,CAAC,CAAC,CAAC,CAACpC,EAAE;QAC5B,MAAMsC,UAAU,GAAG,SAASJ,QAAQ,SAAS;QAG7C,MAAM;UAAE/C,KAAK,EAAEoD;QAAW,CAAE,SAASJ,MAAI,CAACxD,eAAe,CAACG,QAAQ,CAC/DV,IAAI,CAAC,aAAa,CAAC,CACnB4D,MAAM,CAAC;UACN,CAACM,UAAU,GAAG,IAAI;UAClBb,UAAU,EAAE,IAAID,IAAI;SACrB,CAAC,CACDxC,EAAE,CAAC,IAAI,EAAE6C,OAAO,CAAC;QAEpB,IAAIU,WAAW,EAAE;UAEf,IAAIA,WAAW,CAACnD,IAAI,KAAK,OAAO,IAAImD,WAAW,CAAClD,OAAO,CAACC,QAAQ,CAAC,YAAY,CAAC,IAAIiD,WAAW,CAAClD,OAAO,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACxH,MAAM,IAAIC,KAAK,CAAC,sBAAsBgD,WAAW,CAAClD,OAAO,6DAA6D,CAAC;UACzH;UAEA,MAAM,IAAIE,KAAK,CAACgD,WAAW,CAAClD,OAAO,CAAC;QACtC;MAEF,CAAC,CAAC,OAAOF,KAAK,EAAE;QACd,MAAMA,KAAK;MACb;IAAC;EACH;;gBAtLWV,YAAY;;mCAAZA,aAAY;AAAA;;SAAZA,aAAY;EAAA+D,OAAA,EAAZ/D,aAAY,CAAAgE,IAAA;EAAAC,UAAA,EAFX;AAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}