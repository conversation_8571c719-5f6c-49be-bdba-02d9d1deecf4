﻿export type GoalUnit = 'count' | 'steps' | 'm' | 'km' | 'sec' | 'min' | 'hr' | 'Cal' | 'g' | 'mg' | 'drink' | 'pages' | 'books' | '%' | '€' | '$' | '£' | 'days' | 'weeks' | 'months' | 'years';

export interface Goal {
  id?: string; 
  user_id: string;
  name: string;
  description: string;
  emoji: string;
  start_date: Date;
  end_date?: Date;
  
  goal_value: number;
  current_value: number;
  goal_unit: GoalUnit;
  
  before_photo?: string;
  after_photo?: string;
}

export interface MicroGoal {
  id?: string; 
  goal_id: string;
  title: string;
  completed: boolean;
  completed_at?: Date;
}

export interface GoalJournalEntry {
  id?: string; 
  goal_id: string;
  milestone_percentage: number;
  content: string;
  created_at: Date;
}
