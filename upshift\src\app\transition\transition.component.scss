﻿.transition-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #000;
    z-index: 9999;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
  
    .video-wrapper {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
    }
  
    .transition-video {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.05s linear; 
      transform-origin: center center; 
    }
  }
  
  .flash-effect {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: white;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.15s ease-in-out;
    pointer-events: none;
  }