{"ast": null, "code": "var _ComponentsModule;\nimport { CommonModule } from '@angular/common';\nimport { IonicModule } from '@ionic/angular';\nimport { FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { GroupSideQuestComponent } from './group-sidequest/group-sidequest.component';\nimport * as i0 from \"@angular/core\";\nexport class ComponentsModule {}\n_ComponentsModule = ComponentsModule;\n_ComponentsModule.ɵfac = function ComponentsModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _ComponentsModule)();\n};\n_ComponentsModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n  type: _ComponentsModule\n});\n_ComponentsModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n  imports: [CommonModule, IonicModule, FormsModule, RouterModule, GroupSideQuestComponent]\n});\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ComponentsModule, {\n    imports: [CommonModule, IonicModule, FormsModule, RouterModule, GroupSideQuestComponent],\n    exports: [GroupSideQuestComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "IonicModule", "FormsModule", "RouterModule", "GroupSideQuestComponent", "ComponentsModule", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\components\\components.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { RouterModule } from '@angular/router';\r\n\r\nimport { GroupSideQuestComponent } from './group-sidequest/group-sidequest.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    IonicModule,\r\n    FormsModule,\r\n    RouterModule,\r\n    GroupSideQuestComponent\r\n  ],\r\n  exports: [\r\n    GroupSideQuestComponent\r\n  ]\r\n})\r\nexport class ComponentsModule { }\r\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,uBAAuB,QAAQ,6CAA6C;;AAgBrF,OAAM,MAAOC,gBAAgB;oBAAhBA,gBAAgB;;mCAAhBA,iBAAgB;AAAA;;QAAhBA;AAAgB;;YAVzBL,YAAY,EACZC,WAAW,EACXC,WAAW,EACXC,YAAY,EACZC,uBAAuB;AAAA;;2EAMdC,gBAAgB;IAAAC,OAAA,GAVzBN,YAAY,EACZC,WAAW,EACXC,WAAW,EACXC,YAAY,EACZC,uBAAuB;IAAAG,OAAA,GAGvBH,uBAAuB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}