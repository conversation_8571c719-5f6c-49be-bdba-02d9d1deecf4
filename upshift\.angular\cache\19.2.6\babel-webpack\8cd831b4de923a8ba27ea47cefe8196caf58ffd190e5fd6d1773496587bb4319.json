{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _TimeTrackerUnifiedService;\nimport { inject } from '@angular/core';\nimport { from, of, catchError, map, switchMap } from 'rxjs';\nimport { SupabaseService } from './supabase.service';\nimport * as i0 from \"@angular/core\";\nexport class TimeTrackerUnifiedService {\n  constructor() {\n    this.supabaseService = inject(SupabaseService);\n  }\n  getActivityTypes() {\n    return from(this.supabaseService.getClient().from('activity_types').select('*').eq('is_active', true).order('order', {\n      ascending: true\n    })).pipe(map(response => {\n      if (response.error) {\n        return [];\n      }\n      return response.data;\n    }), catchError(error => {\n      return of([]);\n    }));\n  }\n  getDayTracking(userId, date) {\n    if (!userId || !date) {\n      return of({\n        id: '',\n        user_id: userId,\n        date: date\n      });\n    }\n    return from(this.supabaseService.getClient().from('day_tracking').select('*').eq('user_id', userId).eq('date', date).single()).pipe(switchMap(response => {\n      if (response.error && response.error.code === 'PGRST116') {\n        return from(this.supabaseService.getClient().from('day_tracking').insert({\n          user_id: userId,\n          date: date\n        }).select().single()).pipe(map(insertResponse => {\n          if (insertResponse.error) {\n            return {\n              id: '',\n              user_id: userId,\n              date: date\n            };\n          }\n          return insertResponse.data;\n        }), catchError(error => {\n          return of({\n            id: '',\n            user_id: userId,\n            date: date\n          });\n        }));\n      }\n      if (response.error) {\n        return of({\n          id: '',\n          user_id: userId,\n          date: date\n        });\n      }\n      return of(response.data);\n    }), catchError(error => {\n      return of({\n        id: '',\n        user_id: userId,\n        date: date\n      });\n    }));\n  }\n  getActivitiesForDayTracking(dayTrackingId) {\n    if (!dayTrackingId) {\n      return of([]);\n    }\n    return from(this.supabaseService.getClient().from('activities').select('*').eq('day_tracking_id', dayTrackingId).order('name')).pipe(map(response => {\n      if (response.error) {\n        return [];\n      }\n      return response.data;\n    }), catchError(error => {\n      return of([]);\n    }));\n  }\n  getActivities(userId, date) {\n    if (!userId || !date) {\n      return of([]);\n    }\n    return this.getDayTracking(userId, date).pipe(switchMap(dayTracking => {\n      if (!dayTracking.id) {\n        return of([]);\n      }\n      return this.getActivitiesForDayTracking(dayTracking.id);\n    }));\n  }\n  createActivity(userId, date, name, emoji, hours, minutes, isCustom) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          data: dayTracking,\n          error: dayTrackingError\n        } = yield _this.supabaseService.getClient().from('day_tracking').select('id').eq('user_id', userId).eq('date', date).single();\n        let dayTrackingId;\n        if (dayTrackingError && dayTrackingError.code === 'PGRST116') {\n          const {\n            data: newDayTracking,\n            error: newDayTrackingError\n          } = yield _this.supabaseService.getClient().from('day_tracking').insert({\n            user_id: userId,\n            date: date\n          }).select('id').single();\n          if (newDayTrackingError) {\n            return Promise.reject(newDayTrackingError);\n          }\n          dayTrackingId = newDayTracking.id;\n        } else if (dayTrackingError) {\n          return Promise.reject(dayTrackingError);\n        } else {\n          dayTrackingId = dayTracking.id;\n        }\n        const {\n          data: newActivity,\n          error: activityError\n        } = yield _this.supabaseService.getClient().from('activities').insert({\n          day_tracking_id: dayTrackingId,\n          name: name,\n          emoji: emoji,\n          hours: hours,\n          minutes: minutes,\n          is_custom: isCustom\n        }).select('id').single();\n        if (activityError) {\n          return Promise.reject(activityError);\n        }\n        const {\n          data: activities,\n          error: activitiesError\n        } = yield _this.supabaseService.getClient().from('activities').select('hours, minutes').eq('day_tracking_id', dayTrackingId);\n        if (activitiesError) {\n          return Promise.reject(activitiesError);\n        }\n        const totalMinutes = activities.reduce((total, act) => {\n          return total + act.hours * 60 + act.minutes;\n        }, 0);\n        const totalHours = totalMinutes / 60;\n        const remainingHours = Math.max(0, 24 - totalHours);\n        return {\n          id: newActivity.id,\n          total_hours: totalHours.toFixed(1),\n          remaining_hours: remainingHours.toFixed(1)\n        };\n      } catch (error) {\n        return Promise.reject(error);\n      }\n    })();\n  }\n  updateActivity(activityId, hours, minutes) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          data: activity,\n          error: activityError\n        } = yield _this2.supabaseService.getClient().from('activities').select('day_tracking_id').eq('id', activityId).single();\n        if (activityError) {\n          return Promise.reject(activityError);\n        }\n        const dayTrackingId = activity.day_tracking_id;\n        const {\n          error: updateError\n        } = yield _this2.supabaseService.getClient().from('activities').update({\n          hours,\n          minutes\n        }).eq('id', activityId);\n        if (updateError) {\n          return Promise.reject(updateError);\n        }\n        const {\n          data: activities,\n          error: activitiesError\n        } = yield _this2.supabaseService.getClient().from('activities').select('hours, minutes').eq('day_tracking_id', dayTrackingId);\n        if (activitiesError) {\n          return Promise.reject(activitiesError);\n        }\n        const totalMinutes = activities.reduce((total, act) => {\n          return total + act.hours * 60 + act.minutes;\n        }, 0);\n        const totalHours = totalMinutes / 60;\n        const remainingHours = Math.max(0, 24 - totalHours);\n        return {\n          total_hours: totalHours.toFixed(1),\n          remaining_hours: remainingHours.toFixed(1)\n        };\n      } catch (error) {\n        return Promise.reject(error);\n      }\n    })();\n  }\n  deleteActivity(activityId) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          data: activity,\n          error: activityError\n        } = yield _this3.supabaseService.getClient().from('activities').select('day_tracking_id').eq('id', activityId).single();\n        if (activityError) {\n          return Promise.reject(activityError);\n        }\n        const dayTrackingId = activity.day_tracking_id;\n        const {\n          error: deleteError\n        } = yield _this3.supabaseService.getClient().from('activities').delete().eq('id', activityId);\n        if (deleteError) {\n          return Promise.reject(deleteError);\n        }\n        const {\n          data: activities,\n          error: activitiesError\n        } = yield _this3.supabaseService.getClient().from('activities').select('hours, minutes').eq('day_tracking_id', dayTrackingId);\n        if (activitiesError) {\n          return Promise.reject(activitiesError);\n        }\n        const totalMinutes = activities.reduce((total, act) => {\n          return total + act.hours * 60 + act.minutes;\n        }, 0);\n        const totalHours = totalMinutes / 60;\n        const remainingHours = Math.max(0, 24 - totalHours);\n        return {\n          total_hours: totalHours.toFixed(1),\n          remaining_hours: remainingHours.toFixed(1)\n        };\n      } catch (error) {\n        return Promise.reject(error);\n      }\n    })();\n  }\n  getTotalTime(dayTrackingId) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          data: activities,\n          error: activitiesError\n        } = yield _this4.supabaseService.getClient().from('activities').select('hours, minutes').eq('day_tracking_id', dayTrackingId);\n        if (activitiesError) {\n          return Promise.reject(activitiesError);\n        }\n        const totalMinutes = activities.reduce((total, act) => {\n          return total + act.hours * 60 + act.minutes;\n        }, 0);\n        const totalHours = totalMinutes / 60;\n        const remainingHours = Math.max(0, 24 - totalHours);\n        return {\n          total_hours: totalHours,\n          remaining_hours: remainingHours\n        };\n      } catch (error) {\n        return Promise.reject(error);\n      }\n    })();\n  }\n  createActivityType(activityType) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          data,\n          error\n        } = yield _this5.supabaseService.getClient().from('activity_types').insert(activityType).select('id').single();\n        if (error) {\n          return Promise.reject(error);\n        }\n        return data.id;\n      } catch (error) {\n        return Promise.reject(error);\n      }\n    })();\n  }\n}\n_TimeTrackerUnifiedService = TimeTrackerUnifiedService;\n_TimeTrackerUnifiedService.ɵfac = function TimeTrackerUnifiedService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _TimeTrackerUnifiedService)();\n};\n_TimeTrackerUnifiedService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _TimeTrackerUnifiedService,\n  factory: _TimeTrackerUnifiedService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "names": ["inject", "from", "of", "catchError", "map", "switchMap", "SupabaseService", "TimeTrackerUnifiedService", "constructor", "supabaseService", "getActivityTypes", "getClient", "select", "eq", "order", "ascending", "pipe", "response", "error", "data", "getDayTracking", "userId", "date", "id", "user_id", "single", "code", "insert", "insertResponse", "getActivitiesForDayTracking", "dayTrackingId", "getActivities", "dayTracking", "createActivity", "name", "emoji", "hours", "minutes", "isCustom", "_this", "_asyncToGenerator", "dayTrackingError", "newDayTracking", "newDayTrackingError", "Promise", "reject", "newActivity", "activityError", "day_tracking_id", "is_custom", "activities", "activitiesError", "totalMinutes", "reduce", "total", "act", "totalHours", "remainingHours", "Math", "max", "total_hours", "toFixed", "remaining_hours", "updateActivity", "activityId", "_this2", "activity", "updateError", "update", "deleteActivity", "_this3", "deleteError", "delete", "getTotalTime", "_this4", "createActivityType", "activityType", "_this5", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\services\\time-tracker-unified.service.ts"], "sourcesContent": ["import { Injectable, inject } from '@angular/core';\r\nimport { Activity, ActivityType, DayTracking } from '../models/activity.model';\r\nimport { Observable, from, of, catchError, map, switchMap } from 'rxjs';\r\nimport { SupabaseService } from './supabase.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class TimeTrackerUnifiedService {\r\n  private supabaseService = inject(SupabaseService);\r\n\r\n  getActivityTypes(): Observable<ActivityType[]> {\r\n\r\n    return from(\r\n      this.supabaseService.getClient()\r\n        .from('activity_types')\r\n        .select('*')\r\n        .eq('is_active', true)\r\n        .order('order', { ascending: true })\r\n    ).pipe(\r\n      map(response => {\r\n        if (response.error) {\r\n          return [];\r\n        }\r\n\r\n        return response.data as ActivityType[];\r\n      }),\r\n      catchError(error => {\r\n        return of([]);\r\n      })\r\n    );\r\n  }\r\n\r\n  getDayTracking(userId: string, date: string): Observable<DayTracking> {\r\n\r\n    if (!userId || !date) {\r\n      return of({ id: '', user_id: userId, date: date });\r\n    }\r\n\r\n    return from(\r\n      this.supabaseService.getClient()\r\n        .from('day_tracking')\r\n        .select('*')\r\n        .eq('user_id', userId)\r\n        .eq('date', date)\r\n        .single()\r\n    ).pipe(\r\n      switchMap(response => {\r\n        if (response.error && response.error.code === 'PGRST116') { \n          return from(\r\n            this.supabaseService.getClient()\r\n              .from('day_tracking')\r\n              .insert({ user_id: userId, date: date })\r\n              .select()\r\n              .single()\r\n          ).pipe(\r\n            map(insertResponse => {\r\n              if (insertResponse.error) {\r\n                return { id: '', user_id: userId, date: date };\r\n              }\r\n\r\n              return insertResponse.data as DayTracking;\r\n            }),\r\n            catchError(error => {\r\n              return of({ id: '', user_id: userId, date: date });\r\n            })\r\n          );\r\n        }\r\n\r\n        if (response.error) {\r\n          return of({ id: '', user_id: userId, date: date });\r\n        }\r\n\r\n        return of(response.data as DayTracking);\r\n      }),\r\n      catchError(error => {\r\n        return of({ id: '', user_id: userId, date: date });\r\n      })\r\n    );\r\n  }\r\n\r\n  getActivitiesForDayTracking(dayTrackingId: string): Observable<Activity[]> {\r\n\r\n    if (!dayTrackingId) {\r\n      return of([]);\r\n    }\r\n\r\n    return from(\r\n      this.supabaseService.getClient()\r\n        .from('activities')\r\n        .select('*')\r\n        .eq('day_tracking_id', dayTrackingId)\r\n        .order('name')\r\n    ).pipe(\r\n      map(response => {\r\n        if (response.error) {\r\n          return [];\r\n        }\r\n\r\n        return response.data as Activity[];\r\n      }),\r\n      catchError(error => {\r\n        return of([]);\r\n      })\r\n    );\r\n  }\r\n\r\n  getActivities(userId: string, date: string): Observable<Activity[]> {\r\n\r\n    if (!userId || !date) {\r\n      return of([]);\r\n    }\r\n\r\n    return this.getDayTracking(userId, date).pipe(\r\n      switchMap(dayTracking => {\r\n        if (!dayTracking.id) {\r\n          return of([]);\r\n        }\r\n\r\n        return this.getActivitiesForDayTracking(dayTracking.id);\r\n      })\r\n    );\r\n  }\r\n\r\n  async createActivity(\r\n    userId: string,\r\n    date: string,\r\n    name: string,\r\n    emoji: string,\r\n    hours: number,\r\n    minutes: number,\r\n    isCustom: boolean\r\n  ): Promise<{ id: string, total_hours: string, remaining_hours: string }> {\r\n\r\n    try {\r\n      const { data: dayTracking, error: dayTrackingError } = await this.supabaseService.getClient()\r\n        .from('day_tracking')\r\n        .select('id')\r\n        .eq('user_id', userId)\r\n        .eq('date', date)\r\n        .single();\r\n\r\n      let dayTrackingId: string;\r\n\r\n      if (dayTrackingError && dayTrackingError.code === 'PGRST116') { \n        const { data: newDayTracking, error: newDayTrackingError } = await this.supabaseService.getClient()\r\n          .from('day_tracking')\r\n          .insert({ user_id: userId, date: date })\r\n          .select('id')\r\n          .single();\r\n\r\n        if (newDayTrackingError) {\r\n          return Promise.reject(newDayTrackingError);\r\n        }\r\n\r\n        dayTrackingId = newDayTracking.id;\r\n      } else if (dayTrackingError) {\r\n        return Promise.reject(dayTrackingError);\r\n      } else {\r\n        dayTrackingId = dayTracking.id;\r\n      }\r\n\r\n      const { data: newActivity, error: activityError } = await this.supabaseService.getClient()\r\n        .from('activities')\r\n        .insert({\r\n          day_tracking_id: dayTrackingId,\r\n          name: name,\r\n          emoji: emoji,\r\n          hours: hours,\r\n          minutes: minutes,\r\n          is_custom: isCustom\r\n        })\r\n        .select('id')\r\n        .single();\r\n\r\n      if (activityError) {\r\n        return Promise.reject(activityError);\r\n      }\r\n\r\n\r\n      const { data: activities, error: activitiesError } = await this.supabaseService.getClient()\r\n        .from('activities')\r\n        .select('hours, minutes')\r\n        .eq('day_tracking_id', dayTrackingId);\r\n\r\n      if (activitiesError) {\r\n        return Promise.reject(activitiesError);\r\n      }\r\n\r\n      const totalMinutes = activities.reduce((total, act) => {\r\n        return total + (act.hours * 60) + act.minutes;\r\n      }, 0);\r\n\r\n      const totalHours = totalMinutes / 60;\r\n      const remainingHours = Math.max(0, 24 - totalHours);\r\n\r\n\r\n      return {\r\n        id: newActivity.id,\r\n        total_hours: totalHours.toFixed(1),\r\n        remaining_hours: remainingHours.toFixed(1)\r\n      };\r\n    } catch (error) {\r\n      return Promise.reject(error);\r\n    }\r\n  }\r\n\r\n  async updateActivity(activityId: string, hours: number, minutes: number): Promise<{ total_hours: string, remaining_hours: string }> {\r\n\r\n    try {\r\n      const { data: activity, error: activityError } = await this.supabaseService.getClient()\r\n        .from('activities')\r\n        .select('day_tracking_id')\r\n        .eq('id', activityId)\r\n        .single();\r\n\r\n      if (activityError) {\r\n        return Promise.reject(activityError);\r\n      }\r\n\r\n      const dayTrackingId = activity.day_tracking_id;\r\n\r\n      const { error: updateError } = await this.supabaseService.getClient()\r\n        .from('activities')\r\n        .update({ hours, minutes })\r\n        .eq('id', activityId);\r\n\r\n      if (updateError) {\r\n        return Promise.reject(updateError);\r\n      }\r\n\r\n\r\n      const { data: activities, error: activitiesError } = await this.supabaseService.getClient()\r\n        .from('activities')\r\n        .select('hours, minutes')\r\n        .eq('day_tracking_id', dayTrackingId);\r\n\r\n      if (activitiesError) {\r\n        return Promise.reject(activitiesError);\r\n      }\r\n\r\n      const totalMinutes = activities.reduce((total, act) => {\r\n        return total + (act.hours * 60) + act.minutes;\r\n      }, 0);\r\n\r\n      const totalHours = totalMinutes / 60;\r\n      const remainingHours = Math.max(0, 24 - totalHours);\r\n\r\n\r\n      return {\r\n        total_hours: totalHours.toFixed(1),\r\n        remaining_hours: remainingHours.toFixed(1)\r\n      };\r\n    } catch (error) {\r\n      return Promise.reject(error);\r\n    }\r\n  }\r\n\r\n  async deleteActivity(activityId: string): Promise<{ total_hours: string, remaining_hours: string }> {\r\n\r\n    try {\r\n      const { data: activity, error: activityError } = await this.supabaseService.getClient()\r\n        .from('activities')\r\n        .select('day_tracking_id')\r\n        .eq('id', activityId)\r\n        .single();\r\n\r\n      if (activityError) {\r\n        return Promise.reject(activityError);\r\n      }\r\n\r\n      const dayTrackingId = activity.day_tracking_id;\r\n\r\n      const { error: deleteError } = await this.supabaseService.getClient()\r\n        .from('activities')\r\n        .delete()\r\n        .eq('id', activityId);\r\n\r\n      if (deleteError) {\r\n        return Promise.reject(deleteError);\r\n      }\r\n\r\n\r\n      const { data: activities, error: activitiesError } = await this.supabaseService.getClient()\r\n        .from('activities')\r\n        .select('hours, minutes')\r\n        .eq('day_tracking_id', dayTrackingId);\r\n\r\n      if (activitiesError) {\r\n        return Promise.reject(activitiesError);\r\n      }\r\n\r\n      const totalMinutes = activities.reduce((total, act) => {\r\n        return total + (act.hours * 60) + act.minutes;\r\n      }, 0);\r\n\r\n      const totalHours = totalMinutes / 60;\r\n      const remainingHours = Math.max(0, 24 - totalHours);\r\n\r\n\r\n      return {\r\n        total_hours: totalHours.toFixed(1),\r\n        remaining_hours: remainingHours.toFixed(1)\r\n      };\r\n    } catch (error) {\r\n      return Promise.reject(error);\r\n    }\r\n  }\r\n\r\n  async getTotalTime(dayTrackingId: string): Promise<{ total_hours: number, remaining_hours: number }> {\r\n\r\n    try {\r\n      const { data: activities, error: activitiesError } = await this.supabaseService.getClient()\r\n        .from('activities')\r\n        .select('hours, minutes')\r\n        .eq('day_tracking_id', dayTrackingId);\r\n\r\n      if (activitiesError) {\r\n        return Promise.reject(activitiesError);\r\n      }\r\n\r\n      const totalMinutes = activities.reduce((total, act) => {\r\n        return total + (act.hours * 60) + act.minutes;\r\n      }, 0);\r\n\r\n      const totalHours = totalMinutes / 60;\r\n      const remainingHours = Math.max(0, 24 - totalHours);\r\n\r\n      return {\r\n        total_hours: totalHours,\r\n        remaining_hours: remainingHours\r\n      };\r\n    } catch (error) {\r\n      return Promise.reject(error);\r\n    }\r\n  }\r\n\r\n  async createActivityType(activityType: Omit<ActivityType, 'id'>): Promise<string> {\r\n\r\n    try {\r\n      const { data, error } = await this.supabaseService.getClient()\r\n        .from('activity_types')\r\n        .insert(activityType)\r\n        .select('id')\r\n        .single();\r\n\r\n      if (error) {\r\n        return Promise.reject(error);\r\n      }\r\n\r\n      return data.id;\r\n    } catch (error) {\r\n      return Promise.reject(error);\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAAqBA,MAAM,QAAQ,eAAe;AAElD,SAAqBC,IAAI,EAAEC,EAAE,EAAEC,UAAU,EAAEC,GAAG,EAAEC,SAAS,QAAQ,MAAM;AACvE,SAASC,eAAe,QAAQ,oBAAoB;;AAKpD,OAAM,MAAOC,yBAAyB;EAHtCC,YAAA;IAIU,KAAAC,eAAe,GAAGT,MAAM,CAACM,eAAe,CAAC;;EAEjDI,gBAAgBA,CAAA;IAEd,OAAOT,IAAI,CACT,IAAI,CAACQ,eAAe,CAACE,SAAS,EAAE,CAC7BV,IAAI,CAAC,gBAAgB,CAAC,CACtBW,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,CACrBC,KAAK,CAAC,OAAO,EAAE;MAAEC,SAAS,EAAE;IAAI,CAAE,CAAC,CACvC,CAACC,IAAI,CACJZ,GAAG,CAACa,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,KAAK,EAAE;QAClB,OAAO,EAAE;MACX;MAEA,OAAOD,QAAQ,CAACE,IAAsB;IACxC,CAAC,CAAC,EACFhB,UAAU,CAACe,KAAK,IAAG;MACjB,OAAOhB,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH;EACH;EAEAkB,cAAcA,CAACC,MAAc,EAAEC,IAAY;IAEzC,IAAI,CAACD,MAAM,IAAI,CAACC,IAAI,EAAE;MACpB,OAAOpB,EAAE,CAAC;QAAEqB,EAAE,EAAE,EAAE;QAAEC,OAAO,EAAEH,MAAM;QAAEC,IAAI,EAAEA;MAAI,CAAE,CAAC;IACpD;IAEA,OAAOrB,IAAI,CACT,IAAI,CAACQ,eAAe,CAACE,SAAS,EAAE,CAC7BV,IAAI,CAAC,cAAc,CAAC,CACpBW,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAEQ,MAAM,CAAC,CACrBR,EAAE,CAAC,MAAM,EAAES,IAAI,CAAC,CAChBG,MAAM,EAAE,CACZ,CAACT,IAAI,CACJX,SAAS,CAACY,QAAQ,IAAG;MACnB,IAAIA,QAAQ,CAACC,KAAK,IAAID,QAAQ,CAACC,KAAK,CAACQ,IAAI,KAAK,UAAU,EAAE;QACxD,OAAOzB,IAAI,CACT,IAAI,CAACQ,eAAe,CAACE,SAAS,EAAE,CAC7BV,IAAI,CAAC,cAAc,CAAC,CACpB0B,MAAM,CAAC;UAAEH,OAAO,EAAEH,MAAM;UAAEC,IAAI,EAAEA;QAAI,CAAE,CAAC,CACvCV,MAAM,EAAE,CACRa,MAAM,EAAE,CACZ,CAACT,IAAI,CACJZ,GAAG,CAACwB,cAAc,IAAG;UACnB,IAAIA,cAAc,CAACV,KAAK,EAAE;YACxB,OAAO;cAAEK,EAAE,EAAE,EAAE;cAAEC,OAAO,EAAEH,MAAM;cAAEC,IAAI,EAAEA;YAAI,CAAE;UAChD;UAEA,OAAOM,cAAc,CAACT,IAAmB;QAC3C,CAAC,CAAC,EACFhB,UAAU,CAACe,KAAK,IAAG;UACjB,OAAOhB,EAAE,CAAC;YAAEqB,EAAE,EAAE,EAAE;YAAEC,OAAO,EAAEH,MAAM;YAAEC,IAAI,EAAEA;UAAI,CAAE,CAAC;QACpD,CAAC,CAAC,CACH;MACH;MAEA,IAAIL,QAAQ,CAACC,KAAK,EAAE;QAClB,OAAOhB,EAAE,CAAC;UAAEqB,EAAE,EAAE,EAAE;UAAEC,OAAO,EAAEH,MAAM;UAAEC,IAAI,EAAEA;QAAI,CAAE,CAAC;MACpD;MAEA,OAAOpB,EAAE,CAACe,QAAQ,CAACE,IAAmB,CAAC;IACzC,CAAC,CAAC,EACFhB,UAAU,CAACe,KAAK,IAAG;MACjB,OAAOhB,EAAE,CAAC;QAAEqB,EAAE,EAAE,EAAE;QAAEC,OAAO,EAAEH,MAAM;QAAEC,IAAI,EAAEA;MAAI,CAAE,CAAC;IACpD,CAAC,CAAC,CACH;EACH;EAEAO,2BAA2BA,CAACC,aAAqB;IAE/C,IAAI,CAACA,aAAa,EAAE;MAClB,OAAO5B,EAAE,CAAC,EAAE,CAAC;IACf;IAEA,OAAOD,IAAI,CACT,IAAI,CAACQ,eAAe,CAACE,SAAS,EAAE,CAC7BV,IAAI,CAAC,YAAY,CAAC,CAClBW,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,iBAAiB,EAAEiB,aAAa,CAAC,CACpChB,KAAK,CAAC,MAAM,CAAC,CACjB,CAACE,IAAI,CACJZ,GAAG,CAACa,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,KAAK,EAAE;QAClB,OAAO,EAAE;MACX;MAEA,OAAOD,QAAQ,CAACE,IAAkB;IACpC,CAAC,CAAC,EACFhB,UAAU,CAACe,KAAK,IAAG;MACjB,OAAOhB,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH;EACH;EAEA6B,aAAaA,CAACV,MAAc,EAAEC,IAAY;IAExC,IAAI,CAACD,MAAM,IAAI,CAACC,IAAI,EAAE;MACpB,OAAOpB,EAAE,CAAC,EAAE,CAAC;IACf;IAEA,OAAO,IAAI,CAACkB,cAAc,CAACC,MAAM,EAAEC,IAAI,CAAC,CAACN,IAAI,CAC3CX,SAAS,CAAC2B,WAAW,IAAG;MACtB,IAAI,CAACA,WAAW,CAACT,EAAE,EAAE;QACnB,OAAOrB,EAAE,CAAC,EAAE,CAAC;MACf;MAEA,OAAO,IAAI,CAAC2B,2BAA2B,CAACG,WAAW,CAACT,EAAE,CAAC;IACzD,CAAC,CAAC,CACH;EACH;EAEMU,cAAcA,CAClBZ,MAAc,EACdC,IAAY,EACZY,IAAY,EACZC,KAAa,EACbC,KAAa,EACbC,OAAe,EACfC,QAAiB;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAGjB,IAAI;QACF,MAAM;UAAErB,IAAI,EAAEa,WAAW;UAAEd,KAAK,EAAEuB;QAAgB,CAAE,SAASF,KAAI,CAAC9B,eAAe,CAACE,SAAS,EAAE,CAC1FV,IAAI,CAAC,cAAc,CAAC,CACpBW,MAAM,CAAC,IAAI,CAAC,CACZC,EAAE,CAAC,SAAS,EAAEQ,MAAM,CAAC,CACrBR,EAAE,CAAC,MAAM,EAAES,IAAI,CAAC,CAChBG,MAAM,EAAE;QAEX,IAAIK,aAAqB;QAEzB,IAAIW,gBAAgB,IAAIA,gBAAgB,CAACf,IAAI,KAAK,UAAU,EAAE;UAC5D,MAAM;YAAEP,IAAI,EAAEuB,cAAc;YAAExB,KAAK,EAAEyB;UAAmB,CAAE,SAASJ,KAAI,CAAC9B,eAAe,CAACE,SAAS,EAAE,CAChGV,IAAI,CAAC,cAAc,CAAC,CACpB0B,MAAM,CAAC;YAAEH,OAAO,EAAEH,MAAM;YAAEC,IAAI,EAAEA;UAAI,CAAE,CAAC,CACvCV,MAAM,CAAC,IAAI,CAAC,CACZa,MAAM,EAAE;UAEX,IAAIkB,mBAAmB,EAAE;YACvB,OAAOC,OAAO,CAACC,MAAM,CAACF,mBAAmB,CAAC;UAC5C;UAEAb,aAAa,GAAGY,cAAc,CAACnB,EAAE;QACnC,CAAC,MAAM,IAAIkB,gBAAgB,EAAE;UAC3B,OAAOG,OAAO,CAACC,MAAM,CAACJ,gBAAgB,CAAC;QACzC,CAAC,MAAM;UACLX,aAAa,GAAGE,WAAW,CAACT,EAAE;QAChC;QAEA,MAAM;UAAEJ,IAAI,EAAE2B,WAAW;UAAE5B,KAAK,EAAE6B;QAAa,CAAE,SAASR,KAAI,CAAC9B,eAAe,CAACE,SAAS,EAAE,CACvFV,IAAI,CAAC,YAAY,CAAC,CAClB0B,MAAM,CAAC;UACNqB,eAAe,EAAElB,aAAa;UAC9BI,IAAI,EAAEA,IAAI;UACVC,KAAK,EAAEA,KAAK;UACZC,KAAK,EAAEA,KAAK;UACZC,OAAO,EAAEA,OAAO;UAChBY,SAAS,EAAEX;SACZ,CAAC,CACD1B,MAAM,CAAC,IAAI,CAAC,CACZa,MAAM,EAAE;QAEX,IAAIsB,aAAa,EAAE;UACjB,OAAOH,OAAO,CAACC,MAAM,CAACE,aAAa,CAAC;QACtC;QAGA,MAAM;UAAE5B,IAAI,EAAE+B,UAAU;UAAEhC,KAAK,EAAEiC;QAAe,CAAE,SAASZ,KAAI,CAAC9B,eAAe,CAACE,SAAS,EAAE,CACxFV,IAAI,CAAC,YAAY,CAAC,CAClBW,MAAM,CAAC,gBAAgB,CAAC,CACxBC,EAAE,CAAC,iBAAiB,EAAEiB,aAAa,CAAC;QAEvC,IAAIqB,eAAe,EAAE;UACnB,OAAOP,OAAO,CAACC,MAAM,CAACM,eAAe,CAAC;QACxC;QAEA,MAAMC,YAAY,GAAGF,UAAU,CAACG,MAAM,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAI;UACpD,OAAOD,KAAK,GAAIC,GAAG,CAACnB,KAAK,GAAG,EAAG,GAAGmB,GAAG,CAAClB,OAAO;QAC/C,CAAC,EAAE,CAAC,CAAC;QAEL,MAAMmB,UAAU,GAAGJ,YAAY,GAAG,EAAE;QACpC,MAAMK,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAGH,UAAU,CAAC;QAGnD,OAAO;UACLjC,EAAE,EAAEuB,WAAW,CAACvB,EAAE;UAClBqC,WAAW,EAAEJ,UAAU,CAACK,OAAO,CAAC,CAAC,CAAC;UAClCC,eAAe,EAAEL,cAAc,CAACI,OAAO,CAAC,CAAC;SAC1C;MACH,CAAC,CAAC,OAAO3C,KAAK,EAAE;QACd,OAAO0B,OAAO,CAACC,MAAM,CAAC3B,KAAK,CAAC;MAC9B;IAAC;EACH;EAEM6C,cAAcA,CAACC,UAAkB,EAAE5B,KAAa,EAAEC,OAAe;IAAA,IAAA4B,MAAA;IAAA,OAAAzB,iBAAA;MAErE,IAAI;QACF,MAAM;UAAErB,IAAI,EAAE+C,QAAQ;UAAEhD,KAAK,EAAE6B;QAAa,CAAE,SAASkB,MAAI,CAACxD,eAAe,CAACE,SAAS,EAAE,CACpFV,IAAI,CAAC,YAAY,CAAC,CAClBW,MAAM,CAAC,iBAAiB,CAAC,CACzBC,EAAE,CAAC,IAAI,EAAEmD,UAAU,CAAC,CACpBvC,MAAM,EAAE;QAEX,IAAIsB,aAAa,EAAE;UACjB,OAAOH,OAAO,CAACC,MAAM,CAACE,aAAa,CAAC;QACtC;QAEA,MAAMjB,aAAa,GAAGoC,QAAQ,CAAClB,eAAe;QAE9C,MAAM;UAAE9B,KAAK,EAAEiD;QAAW,CAAE,SAASF,MAAI,CAACxD,eAAe,CAACE,SAAS,EAAE,CAClEV,IAAI,CAAC,YAAY,CAAC,CAClBmE,MAAM,CAAC;UAAEhC,KAAK;UAAEC;QAAO,CAAE,CAAC,CAC1BxB,EAAE,CAAC,IAAI,EAAEmD,UAAU,CAAC;QAEvB,IAAIG,WAAW,EAAE;UACf,OAAOvB,OAAO,CAACC,MAAM,CAACsB,WAAW,CAAC;QACpC;QAGA,MAAM;UAAEhD,IAAI,EAAE+B,UAAU;UAAEhC,KAAK,EAAEiC;QAAe,CAAE,SAASc,MAAI,CAACxD,eAAe,CAACE,SAAS,EAAE,CACxFV,IAAI,CAAC,YAAY,CAAC,CAClBW,MAAM,CAAC,gBAAgB,CAAC,CACxBC,EAAE,CAAC,iBAAiB,EAAEiB,aAAa,CAAC;QAEvC,IAAIqB,eAAe,EAAE;UACnB,OAAOP,OAAO,CAACC,MAAM,CAACM,eAAe,CAAC;QACxC;QAEA,MAAMC,YAAY,GAAGF,UAAU,CAACG,MAAM,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAI;UACpD,OAAOD,KAAK,GAAIC,GAAG,CAACnB,KAAK,GAAG,EAAG,GAAGmB,GAAG,CAAClB,OAAO;QAC/C,CAAC,EAAE,CAAC,CAAC;QAEL,MAAMmB,UAAU,GAAGJ,YAAY,GAAG,EAAE;QACpC,MAAMK,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAGH,UAAU,CAAC;QAGnD,OAAO;UACLI,WAAW,EAAEJ,UAAU,CAACK,OAAO,CAAC,CAAC,CAAC;UAClCC,eAAe,EAAEL,cAAc,CAACI,OAAO,CAAC,CAAC;SAC1C;MACH,CAAC,CAAC,OAAO3C,KAAK,EAAE;QACd,OAAO0B,OAAO,CAACC,MAAM,CAAC3B,KAAK,CAAC;MAC9B;IAAC;EACH;EAEMmD,cAAcA,CAACL,UAAkB;IAAA,IAAAM,MAAA;IAAA,OAAA9B,iBAAA;MAErC,IAAI;QACF,MAAM;UAAErB,IAAI,EAAE+C,QAAQ;UAAEhD,KAAK,EAAE6B;QAAa,CAAE,SAASuB,MAAI,CAAC7D,eAAe,CAACE,SAAS,EAAE,CACpFV,IAAI,CAAC,YAAY,CAAC,CAClBW,MAAM,CAAC,iBAAiB,CAAC,CACzBC,EAAE,CAAC,IAAI,EAAEmD,UAAU,CAAC,CACpBvC,MAAM,EAAE;QAEX,IAAIsB,aAAa,EAAE;UACjB,OAAOH,OAAO,CAACC,MAAM,CAACE,aAAa,CAAC;QACtC;QAEA,MAAMjB,aAAa,GAAGoC,QAAQ,CAAClB,eAAe;QAE9C,MAAM;UAAE9B,KAAK,EAAEqD;QAAW,CAAE,SAASD,MAAI,CAAC7D,eAAe,CAACE,SAAS,EAAE,CAClEV,IAAI,CAAC,YAAY,CAAC,CAClBuE,MAAM,EAAE,CACR3D,EAAE,CAAC,IAAI,EAAEmD,UAAU,CAAC;QAEvB,IAAIO,WAAW,EAAE;UACf,OAAO3B,OAAO,CAACC,MAAM,CAAC0B,WAAW,CAAC;QACpC;QAGA,MAAM;UAAEpD,IAAI,EAAE+B,UAAU;UAAEhC,KAAK,EAAEiC;QAAe,CAAE,SAASmB,MAAI,CAAC7D,eAAe,CAACE,SAAS,EAAE,CACxFV,IAAI,CAAC,YAAY,CAAC,CAClBW,MAAM,CAAC,gBAAgB,CAAC,CACxBC,EAAE,CAAC,iBAAiB,EAAEiB,aAAa,CAAC;QAEvC,IAAIqB,eAAe,EAAE;UACnB,OAAOP,OAAO,CAACC,MAAM,CAACM,eAAe,CAAC;QACxC;QAEA,MAAMC,YAAY,GAAGF,UAAU,CAACG,MAAM,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAI;UACpD,OAAOD,KAAK,GAAIC,GAAG,CAACnB,KAAK,GAAG,EAAG,GAAGmB,GAAG,CAAClB,OAAO;QAC/C,CAAC,EAAE,CAAC,CAAC;QAEL,MAAMmB,UAAU,GAAGJ,YAAY,GAAG,EAAE;QACpC,MAAMK,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAGH,UAAU,CAAC;QAGnD,OAAO;UACLI,WAAW,EAAEJ,UAAU,CAACK,OAAO,CAAC,CAAC,CAAC;UAClCC,eAAe,EAAEL,cAAc,CAACI,OAAO,CAAC,CAAC;SAC1C;MACH,CAAC,CAAC,OAAO3C,KAAK,EAAE;QACd,OAAO0B,OAAO,CAACC,MAAM,CAAC3B,KAAK,CAAC;MAC9B;IAAC;EACH;EAEMuD,YAAYA,CAAC3C,aAAqB;IAAA,IAAA4C,MAAA;IAAA,OAAAlC,iBAAA;MAEtC,IAAI;QACF,MAAM;UAAErB,IAAI,EAAE+B,UAAU;UAAEhC,KAAK,EAAEiC;QAAe,CAAE,SAASuB,MAAI,CAACjE,eAAe,CAACE,SAAS,EAAE,CACxFV,IAAI,CAAC,YAAY,CAAC,CAClBW,MAAM,CAAC,gBAAgB,CAAC,CACxBC,EAAE,CAAC,iBAAiB,EAAEiB,aAAa,CAAC;QAEvC,IAAIqB,eAAe,EAAE;UACnB,OAAOP,OAAO,CAACC,MAAM,CAACM,eAAe,CAAC;QACxC;QAEA,MAAMC,YAAY,GAAGF,UAAU,CAACG,MAAM,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAI;UACpD,OAAOD,KAAK,GAAIC,GAAG,CAACnB,KAAK,GAAG,EAAG,GAAGmB,GAAG,CAAClB,OAAO;QAC/C,CAAC,EAAE,CAAC,CAAC;QAEL,MAAMmB,UAAU,GAAGJ,YAAY,GAAG,EAAE;QACpC,MAAMK,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAGH,UAAU,CAAC;QAEnD,OAAO;UACLI,WAAW,EAAEJ,UAAU;UACvBM,eAAe,EAAEL;SAClB;MACH,CAAC,CAAC,OAAOvC,KAAK,EAAE;QACd,OAAO0B,OAAO,CAACC,MAAM,CAAC3B,KAAK,CAAC;MAC9B;IAAC;EACH;EAEMyD,kBAAkBA,CAACC,YAAsC;IAAA,IAAAC,MAAA;IAAA,OAAArC,iBAAA;MAE7D,IAAI;QACF,MAAM;UAAErB,IAAI;UAAED;QAAK,CAAE,SAAS2D,MAAI,CAACpE,eAAe,CAACE,SAAS,EAAE,CAC3DV,IAAI,CAAC,gBAAgB,CAAC,CACtB0B,MAAM,CAACiD,YAAY,CAAC,CACpBhE,MAAM,CAAC,IAAI,CAAC,CACZa,MAAM,EAAE;QAEX,IAAIP,KAAK,EAAE;UACT,OAAO0B,OAAO,CAACC,MAAM,CAAC3B,KAAK,CAAC;QAC9B;QAEA,OAAOC,IAAI,CAACI,EAAE;MAChB,CAAC,CAAC,OAAOL,KAAK,EAAE;QACd,OAAO0B,OAAO,CAACC,MAAM,CAAC3B,KAAK,CAAC;MAC9B;IAAC;EACH;;6BA1VWX,yBAAyB;;mCAAzBA,0BAAyB;AAAA;;SAAzBA,0BAAyB;EAAAuE,OAAA,EAAzBvE,0BAAyB,CAAAwE,IAAA;EAAAC,UAAA,EAFxB;AAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}