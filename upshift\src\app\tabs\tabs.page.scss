﻿ion-tab-bar.main-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  z-index: 1000;
  padding: 8px 0;
  display: flex;
  justify-content: center;
  max-width: 100vw;
}

ion-tab-bar.main-navigation {
  .tab-button {
    flex: 1 1 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #888;
    padding: 5px 0;
    transition: color 0.2s ease;
    background: transparent;
    border: none;
    min-width: 0;
    max-width: 120px;
    position: relative;
  }

  .tab-button:hover {
    color: #fff;
  }

  .tab-button.tab-selected,
  .tab-button.active {
    color: #4D7BFF;
  }

  .tab-button.tab-selected::after,
  .tab-button.active::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background-color: #4D7BFF;
    border-radius: 50%;
  }

  .tab-button .nav-icon {
    font-size: 18px;
    margin-bottom: 4px;
    display: block;
  }

  .tab-button .nav-text {
    font-size: 12px;
    font-weight: 500;
    display: block;
  }

   Admin link styling */
