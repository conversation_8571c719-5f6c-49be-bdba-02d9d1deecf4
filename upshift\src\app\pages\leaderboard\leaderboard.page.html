﻿<!-- Leaderboard Page -->
<div class="container">
  <header>
    <div class="logo">
      <img src="assets/images/upshift_icon_mini.svg" alt="Upshift">
      <span>Upshift</span>
    </div>
    <h1>Leaderboard</h1>
  </header>

  <div class="leaderboard-container">
    <a href="javascript:void(0)" (click)="goBackToGroups()" class="back-link">← Back to Groups</a>

    <div class="leaderboard-tabs">
      <button (click)="setActiveTab('groups')" class="leaderboard-tab" [class.active]="activeTab === 'groups'">Top Groups</button>
      <button (click)="setActiveTab('users')" class="leaderboard-tab" [class.active]="activeTab === 'users'">Top Users</button>
    </div>

    <!-- Groups Tab -->
    <div id="groups-tab" class="leaderboard-content" [class.active]="activeTab === 'groups'">
      <div *ngIf="topGroups.length > 0; else noGroups">
        <div *ngFor="let group of topGroups; let i = index" class="leaderboard-item">
          <div class="rank" [class.top-1]="i === 0" [class.top-2]="i === 1" [class.top-3]="i === 2">{{ i + 1 }}</div>
          <div class="entity-icon">{{ group.emoji }}</div>
          <div class="entity-info">
            <div class="entity-name">{{ group.name }}</div>
            <div class="entity-level">Level {{ group.level }}</div>
            <div class="entity-xp">
              <span class="xp-category">💪 {{ group.strength_xp }}</span>
              <span class="xp-category">💰 {{ group.money_xp }}</span>
              <span class="xp-category">❤️ {{ group.health_xp }}</span>
              <span class="xp-category">📚 {{ group.knowledge_xp }}</span>
            </div>
          </div>
        </div>
      </div>
      <ng-template #noGroups>
        <div class="no-results">
          <p>No groups found. Create a group to see it on the leaderboard!</p>
        </div>
      </ng-template>
    </div>

    <!-- Users Tab -->
    <div id="users-tab" class="leaderboard-content" [class.active]="activeTab === 'users'">
      <div *ngIf="topUsers.length > 0; else noUsers">
        <div *ngFor="let user of topUsers; let i = index" class="leaderboard-item"
             [class.clickable-user]="user.id !== currentUserId"
             [class.current-user]="user.id === currentUserId"
             (click)="viewUserProfile(user.id)">
          <div class="rank" [class.top-1]="i === 0" [class.top-2]="i === 1" [class.top-3]="i === 2">{{ i + 1 }}</div>
          <div class="entity-info">
            <div class="entity-name">
              {{ user.username }}
            </div>
            <div class="entity-level">Level {{ user.level }} - {{ user.title }}</div>
            <div class="entity-xp">
              <span class="xp-category">💪 {{ user.strength_xp }}</span>
              <span class="xp-category">💰 {{ user.money_xp }}</span>
              <span class="xp-category">❤️ {{ user.health_xp }}</span>
              <span class="xp-category">📚 {{ user.knowledge_xp }}</span>
            </div>
          </div>
        </div>
      </div>
      <ng-template #noUsers>
        <div class="no-results">
          <p>No users found. Be the first to climb the leaderboard!</p>
        </div>
      </ng-template>
    </div>
  </div>
</div>
