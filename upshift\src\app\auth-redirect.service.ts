﻿import { Injectable, inject } from '@angular/core';
import { Router, Resolve, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Preferences } from '@capacitor/preferences';
import { SupabaseService } from './services/supabase.service';
import { UserService } from './services/user.service';

import { Observable, Subscription, from }  from 'rxjs';

@Injectable({ providedIn: 'root' })
export class AuthRedirectService implements Resolve<boolean> {
  private supabaseService = inject(SupabaseService);
  private userService = inject(UserService);
  private router = inject(Router);
  private authSubscription: Subscription | undefined;

  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    return from(this.handleRedirect());
  }

  async handleRedirect(): Promise<boolean> {
    const user = this.supabaseService._currentUser.value;

    if (!user) {
      this.router.navigateByUrl('/signup', { replaceUrl: true });
      return true;
    }

    try {
      const onboardingSeen = await Preferences.get({ key: 'onboarding_complete' });
      if (!onboardingSeen.value) {
        this.router.navigateByUrl('/onboarding', { replaceUrl: true });
        return true;
      }
    } catch (error) {
      this.router.navigateByUrl('/onboarding', { replaceUrl: true });
      return true;
    }

    if (!user.id) {
      this.router.navigateByUrl('/signup', { replaceUrl: true });
      return true;
    }

    try {
      const { data: userData, error } = await this.supabaseService.getClient()
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        this.router.navigateByUrl('/signup', { replaceUrl: true });
        return true;
      }

      if (!userData) {
        this.router.navigateByUrl('/signup', { replaceUrl: true });
        return true;
      }

      const endDate = userData.end_of_current_plan ? new Date(userData.end_of_current_plan) : null;
      const username = userData.username;

      if (endDate && endDate >= new Date()) {
        if (username) {
          this.router.navigateByUrl('-tabs/today', { replaceUrl: true });
        } else {
          this.router.navigateByUrl('/signup-step3', { replaceUrl: true });
        }
      } else {
        this.router.navigateByUrl('/pricing', { replaceUrl: true });
      }
      return true;
    } catch (error) {
      this.router.navigateByUrl('/signup', { replaceUrl: true });
      return true;
    }
  }

  initAuthSubscription() {
    this.authSubscription = this.supabaseService.currentUser$.subscribe(async (user) => {
      this.handleRedirect().then(() => {
      }).catch(error => {
      });
    });
  }

  cleanup() {
    if (this.authSubscription) {
      this.authSubscription.unsubscribe();
    }
  }
}
