﻿

.quest-message {
    color: #FF9500;
    font-weight: 500;
    font-size: small;
}

 Add Quest Button */
.add-quest-modal {
    --width: 90%;
    --max-width: 480px;
    --height: auto;
    --max-height: 90%;
    --border-radius: 8px;
    --background: transparent;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
        background-color: rgba(0, 0, 0, 0.9);
}

 Modal backdrop */
ion-input::part(native) {
    color: white;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #CCCCCC;
    font-size: 14px;
    font-weight: 500;
}

.form-group .priority-warning {
    margin-top: 4px;
    color: #FF9500;
    font-size: 12px;
}

.form-group ion-select {
    --background: #1C1C1E;
    --color: white;
    --padding-start: 12px;
    --padding-end: 12px;
    --border-radius: 4px;
    --border-color: #2C2C2E;
    --border-style: solid;
    --border-width: 1px;
    --placeholder-color: #8E8E93;
    width: 100%;
    height: 40px;
}

 Styling for the select interface */
ion-select::part(icon) {
    color: white;
    opacity: 1;
}

.form-group ion-textarea {
    --background: #1C1C1E;
    --color: white;
    --padding-start: 12px;
    --padding-end: 12px;
    --border-radius: 4px;
    --border-color: #2C2C2E;
    --border-style: solid;
    --border-width: 1px;
    --placeholder-color: #8E8E93;
    height: 80px;
}

 Styling for textarea text */
.days-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 8px;
}

.day-checkbox {
    display: flex;
    align-items: center;
    gap: 6px;
}

.day-checkbox ion-checkbox {
    --size: 18px;
    --background-checked: #4169E1;
    --border-color: #3C3C3E;
    --border-color-checked: #4169E1;
    --checkmark-color: white;
}

.day-checkbox label {
    font-size: 13px;
    cursor: pointer;
    margin-bottom: 0;
    color: white;
}

.month-days-selector {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
    margin-top: 8px;
    margin-bottom: 8px;
}

.month-days-selector .day-checkbox {
    justify-content: center;
}

.month-days-selector .day-checkbox label {
    min-width: 20px;
    text-align: center;
}

.quests, .side-quests {
    margin-bottom: 32px;
}


.quest-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.quest-item {
    background-color: #1C1C1E;
    border: 1px solid #2C2C2E;
    border-radius: 8px;
    padding: 12px;
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.quest-item:active {
    transform: scale(0.98);
}

.quest-item.completed {
    border-color: #4169E1;
}

.quest-item.completed .quest-title {
    color: #4169E1;
}

.quest-item.completed::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(65, 105, 225, 0.05);
    pointer-events: none;
}

.quest-icon {
    font-size: 20px;
    min-width: 24px;
    text-align: center;
}

.quest-info {
    flex-grow: 1;
}

.quest-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 2px;
    color: #4169E1;
}

.quest-description {
    color: #8E8E93;
    font-size: 12px;
    margin-bottom: 4px;
}


.progress, .progress-time {
    color: var(--secondary-text);
    font-size: 12px;
}


.quest-streak {
    font-size: 12px;
    white-space: nowrap;
}


 Add a purple line between main quests and side quests */
}


.progress-slider {
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    height: 4px;
    background: #2C2C2E;  Use hardcoded color */
}


.progress-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #4169E1;
    cursor: pointer;
    position: relative;
    margin-top: -4px;  Only use margin-top for vertical centering */
}


.progress-slider::-webkit-slider-runnable-track {
    height: 4px;
    border-radius: 2px;
}


.progress-slider::-moz-range-track {
    height: 4px;
    border-radius: 2px;
}


 Custom progress bar fill */
    background: var(--inactive-date);
}

 Add dynamic styling for the progress slider */
}

input[type="range"].progress-slider::-webkit-slider-runnable-track {
    height: 4px;
    border-radius: 2px;
    background: transparent;
}

input[type="range"].progress-slider::-moz-range-track {
    height: 4px;
    border-radius: 2px;
    background: transparent;
}

 Style the filled part of the slider */
    cursor: pointer;
    margin-top: -4px;  Adjust to center the thumb */
    cursor: pointer;
    border: none;
    position: relative;
    margin-top: 0;  Firefox handles vertical centering differently */
}


.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo img {
    height: 24px;
}

.logo span {
    font-size: 20px;
    font-weight: 600;
}

.page-title {
    font-size: 20px;
    font-weight: 600;
}


.week-calendar {
    margin-bottom: 32px;
}


.days, .dates {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    text-align: center;
    gap: 8px;
}


.days {
    margin-bottom: 8px;
}


.day-name {
    color: var(--secondary-text);
    font-size: 14px;
}


.date {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin: 0 auto;
    font-size: 14px;
    background-color: #2C2C2E;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
}

.date-progress {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    pointer-events: none;
    z-index: 0;
}

.date-progress circle {
    fill: transparent;
    stroke-width: 2.5;
    stroke-linecap: round;
    transform-origin: center;
    transform: rotate(-90deg);
    transition: stroke-dasharray 0.3s ease;
}

 Different colors for different completion levels */
.date.selected .date-progress .progress-circle {
    stroke: #78a8f3;
    stroke-opacity: 0.7;
}

.date.active .date-progress .progress-circle {
    stroke: #78a8f3;
     #4287f5; */
    stroke-opacity: 0.7;
}

.date-content {
    position: relative;
    z-index: 1;
}


.date.active {
    background-color: #4169E1;
    color: white;
}

.date.selected {
    background-color: #4169E1;
    color: white;
}


.date.selected::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background-color: #4169E1;
    border-radius: 50%;
}


.date:hover:not(.disabled) {
    background-color: rgba(255, 255, 255, 0.1);
}


.date.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}


h2 {
    font-size: 20px;
    margin-bottom: 16px;
}


.side-quests::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    height: 1px;
    background: linear-gradient(to right, transparent, #4B0082, transparent);
}


.calendar {
    margin: 20px 0;
    padding: 10px;
    background: var(--bg-secondary);
    border-radius: 8px;
}


.calendar-nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
}


.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
    text-align: center;
}


.day-name {
    color: #8E8E93;
    font-size: 12px;
    font-weight: 500;
}


.day-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    text-decoration: none;
    color: var(--text-primary);
    margin: 0 auto;
}


.day-number:hover {
    background: var(--bg-hover);
}


.day-number.selected {
    background: var(--primary-color);
    color: white;
}


.day-number.today {
    border: 2px solid var(--primary-color);
}


.nav-arrow {
    --background: transparent;
    --color: #FFFFFF;
    --border-radius: 50%;
    --padding-start: 0;
    --padding-end: 0;
    width: 32px;
    height: 32px;
    margin: 0;
    font-size: 18px;
    cursor: pointer;
}

.nav-arrow:hover {
    --background: rgba(255, 255, 255, 0.1);
}


.time-display {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-color);
    margin-right: 16px;
}


 Make number input spinners less prominent */
ion-range.progress-slider {
    --bar-height: 6px;
    --bar-border-radius: 3px;
    --knob-size: 16px;
    --bar-background: #2C2C2E;
    --bar-background-active: #4169E1;
    --knob-background: #4169E1;
    --pin-background: #4169E1;
    --pin-color: #FFFFFF;
    --step: 1;
    --tick-height: 0;
    --tick-width: 0;
    --tick-background: transparent;
    --tick-background-active: transparent;
    margin: 0;
    padding: 0;
}

 For standard HTML sliders (fallback) */
ion-range.progress-slider::part(tick) {
    display: none !important;
}

ion-range.progress-slider::part(tick-active) {
    display: none !important;
}

.progress-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #4169E1;
    cursor: pointer;
    margin-top: -5px;
}

.progress-slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #4169E1;
    cursor: pointer;
    margin-top: 0;
    border: none;
}

.daily-side-quest h2{
    padding-bottom: 20px;
}
@media (max-width: 480px) {
    .container {
        width: auto;
    }
}
