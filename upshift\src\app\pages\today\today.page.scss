

.quest-message {
    color: #FF9500;
    font-weight: 500;
    font-size: small;
}

/* Add Quest Button */
.add-quest-btn {
    --background: rgba(65, 105, 225, 0.1);
    --color: #4169E1;
    --border-radius: 6px;
    --padding-start: 12px;
    --padding-end: 12px;
    font-size: 14px;
    font-weight: 500;
    height: 32px;
    margin: 0;
}

.add-quest-btn:hover {
    --background: rgba(65, 105, 225, 0.2);
}

/* Modal Styles */
.add-quest-modal {
    --width: 90%;
    --max-width: 480px;
    --height: auto;
    --max-height: 90%;
    --border-radius: 8px;
    --background: transparent;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
        background-color: rgba(0, 0, 0, 0.9);
}

/* Modal backdrop */
ion-modal::part(backdrop) {
    background: #000000;
    opacity: 1;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #1C1C1E;
    position: sticky;
    top: 0;

    z-index: 10;
}

.modal-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
}

.close-button {
    --padding-start: 8px;
    --padding-end: 8px;
    --color: #8E8E93;
    --background: transparent;
    --background-hover: transparent;
    --background-activated: transparent;
    --background-focused: transparent;
    font-size: 20px;
    margin: 0;
}

.modal-content {
    padding: 16px;
    color: white;
    max-height: 70vh;
    overflow-y: auto;
}

ion-modal form {
    color: white;
}

.quest-name-row {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.emoji-input {
    width: 60px;
}

.emoji-input ion-input {
    --background: #1C1C1E;
    --color: white;
    --padding-start: 8px;
    --padding-end: 8px;
    --border-radius: 4px;
    --border-color: #2C2C2E;
    --border-style: solid;
    --border-width: 1px;
    text-align: center;
    font-size: 24px;
    height: 40px;
    cursor: pointer;
}

.name-input {
    flex: 1;
}

.name-input ion-input {
    --background: #1C1C1E;
    --color: white;
    --padding-start: 12px;
    --padding-end: 12px;
    --border-radius: 4px;
    --border-color: #2C2C2E;
    --border-style: solid;
    --border-width: 1px;
    height: 40px;
}

/* Styling for input text */
ion-input::part(native) {
    color: white;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #CCCCCC;
    font-size: 14px;
    font-weight: 500;
}

.form-group .priority-warning {
    margin-top: 4px;
    color: #FF9500;
    font-size: 12px;
}

.form-group ion-select {
    --background: #1C1C1E;
    --color: white;
    --padding-start: 12px;
    --padding-end: 12px;
    --border-radius: 4px;
    --border-color: #2C2C2E;
    --border-style: solid;
    --border-width: 1px;
    --placeholder-color: #8E8E93;
    width: 100%;
    height: 40px;
}

/* Styling for the select interface */
ion-select::part(placeholder),
ion-select::part(text) {
    color: white;
}

/* Styling for the select arrow icon */
ion-select::part(icon) {
    color: white;
    opacity: 1;
}

.form-group ion-textarea {
    --background: #1C1C1E;
    --color: white;
    --padding-start: 12px;
    --padding-end: 12px;
    --border-radius: 4px;
    --border-color: #2C2C2E;
    --border-style: solid;
    --border-width: 1px;
    --placeholder-color: #8E8E93;
    height: 80px;
}

/* Styling for textarea text */
ion-textarea::part(native) {
    color: white;
}

.warning-note {
    color: #FF9500;
    font-size: 12px;
    margin-top: 4px;
}

.goal-inputs {
    display: flex;
    gap: 12px;
}

.goal-inputs ion-input {
    --background: #1C1C1E;
    --color: white;
    --padding-start: 12px;
    --padding-end: 12px;
    --border-radius: 4px;
    --border-color: #2C2C2E;
    --border-style: solid;
    --border-width: 1px;
    width: 80px;
    height: 40px;
}

.goal-inputs ion-select {
    flex: 1;
}

.create-quest-btn {
    --background: #4169E1;
    --color: white;
    --border-radius: 4px;
    --padding-top: 12px;
    --padding-bottom: 12px;
    margin-top: 24px;
    margin-bottom: 0;
    font-weight: 600;
    letter-spacing: 0.5px;
    font-size: 14px;
    text-transform: none;
    position: sticky;
    bottom: 0;
    z-index: 10;
    box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.3);
    padding-bottom: 8px;
    background-color: var(--background-color);
}

/* Day selectors */
.days-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 8px;
}

.day-checkbox {
    display: flex;
    align-items: center;
    gap: 6px;
}

.day-checkbox ion-checkbox {
    --size: 18px;
    --background-checked: #4169E1;
    --border-color: #3C3C3E;
    --border-color-checked: #4169E1;
    --checkmark-color: white;
}

.day-checkbox label {
    font-size: 13px;
    cursor: pointer;
    margin-bottom: 0;
    color: white;
}

.month-days-selector {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
    margin-top: 8px;
    margin-bottom: 8px;
}

.month-days-selector .day-checkbox {
    justify-content: center;
}

.month-days-selector .day-checkbox label {
    min-width: 20px;
    text-align: center;
}

.quests, .side-quests {
    margin-bottom: 32px;
}


.quest-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.quest-item {
    background-color: #1C1C1E;
    border: 1px solid #2C2C2E;
    border-radius: 8px;
    padding: 12px;
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.quest-item:active {
    transform: scale(0.98);
}

.quest-item.completed {
    border-color: #4169E1;
}

.quest-item.completed .quest-title {
    color: #4169E1;
}

.quest-item.completed::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(65, 105, 225, 0.05);
    pointer-events: none;
}

.quest-icon {
    font-size: 20px;
    min-width: 24px;
    text-align: center;
}

.quest-info {
    flex-grow: 1;
}

.quest-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 2px;
    color: #4169E1;
}

.quest-description {
    color: #8E8E93;
    font-size: 12px;
    margin-bottom: 4px;
}


.progress, .progress-time {
    color: var(--secondary-text);
    font-size: 12px;
}


.quest-streak {
    font-size: 12px;
    white-space: nowrap;
}


/* Add a purple line between main quests and side quests */
.side-quests {
    position: relative;
    padding-top: 32px;
}


.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.quests h2, .daily-side-quest h2 {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
}


.progress-container {
    width: 100%;
    // margin: 8px 0;
    /* Add a bit more vertical space for the slider */
}


.progress-slider {
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    height: 4px;
    background: #2C2C2E; /* Use hardcoded color */
    outline: none;
    position: relative;
    /* Remove top and transform properties that cause vertical alignment issues */
}


.progress-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #4169E1;
    cursor: pointer;
    position: relative;
    margin-top: -4px; /* Only use margin-top for vertical centering */
}


.progress-slider::-moz-range-thumb {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #4169E1;
    cursor: pointer;
    border: none;
    position: relative;
    margin-top: 0; /* Firefox handles vertical centering differently */
}


.progress-slider::-webkit-slider-runnable-track {
    height: 4px;
    border-radius: 2px;
}


.progress-slider::-moz-range-track {
    height: 4px;
    border-radius: 2px;
}


/* Custom progress bar fill */
.progress-slider {
    /* Remove the static background gradient */
    background: var(--inactive-date);
}

/* Add dynamic styling for the progress slider */
input[type="range"].progress-slider {
    -webkit-appearance: none;
    appearance: none;
    height: 4px;
    border-radius: 2px;
    outline: none;
    position: relative;
    z-index: 1;
    /* Background will be set inline via JavaScript */
}

input[type="range"].progress-slider::-webkit-slider-runnable-track {
    height: 4px;
    border-radius: 2px;
    background: transparent;
}

input[type="range"].progress-slider::-moz-range-track {
    height: 4px;
    border-radius: 2px;
    background: transparent;
}

/* Style the filled part of the slider */
input[type="range"].progress-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #4169E1; /* Use hardcoded color */
    cursor: pointer;
    margin-top: -4px; /* Adjust to center the thumb */
    position: relative;
    z-index: 2;
}

input[type="range"].progress-slider::-moz-range-thumb {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #4169E1; /* Use hardcoded color */
    cursor: pointer;
    border: none;
    position: relative;
    margin-top: 0; /* Firefox handles vertical centering differently */
    z-index: 2;
}


.progress-text {
    font-size: 12px;
    color: var(--secondary-text);
    margin-top: 2px;
}



.container {
    width: 480px;
    margin: 0 auto;
    padding: 20px;
    overflow-y: auto;
    scrollbar-width: none;
    height: 100%;
    padding-bottom: 74px;
}
.container::-webkit-scrollbar {
    display: none; /* Chrome/Safari */
}


.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo img {
    height: 24px;
}

.logo span {
    font-size: 20px;
    font-weight: 600;
}

.page-title {
    font-size: 20px;
    font-weight: 600;
}


.week-calendar {
    margin-bottom: 32px;
}


.days, .dates {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    text-align: center;
    gap: 8px;
}


.days {
    margin-bottom: 8px;
}


.day-name {
    color: var(--secondary-text);
    font-size: 14px;
}


.date {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin: 0 auto;
    font-size: 14px;
    background-color: #2C2C2E;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
}

.date-progress {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    pointer-events: none;
    z-index: 0;
}

.date-progress circle {
    fill: transparent;
    stroke-width: 2.5;
    stroke-linecap: round;
    transform-origin: center;
    transform: rotate(-90deg);
    transition: stroke-dasharray 0.3s ease;
}

/* Different colors for different completion levels */
.date-progress .progress-circle {
    stroke: #4169E1;
    stroke-opacity: 0.9;
}

.date-progress .progress-circle.low {
    stroke: #FF9500;
}

.date-progress circle {
    stroke-width: 3;
}

/* Special handling for selected days */
.date.selected .date-progress .progress-circle {
    stroke: #78a8f3;
    stroke-opacity: 0.7;
}

.date.active .date-progress .progress-circle {
    stroke: #78a8f3;
    /* #4287f5; */
    /* 6da5fe */
    stroke-opacity: 0.7;
}

.date-content {
    position: relative;
    z-index: 1;
}


.date.active {
    background-color: #4169E1;
    color: white;
}

.date.selected {
    background-color: #4169E1;
    color: white;
}


.date.selected::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background-color: #4169E1;
    border-radius: 50%;
}


.date:hover:not(.disabled) {
    background-color: rgba(255, 255, 255, 0.1);
}


.date.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}


h2 {
    font-size: 20px;
    margin-bottom: 16px;
}


.side-quests::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    height: 1px;
    background: linear-gradient(to right, transparent, #4B0082, transparent);
}


.calendar {
    margin: 20px 0;
    padding: 10px;
    background: var(--bg-secondary);
    border-radius: 8px;
}


.calendar-nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
}


.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
    text-align: center;
}


.day-name {
    color: #8E8E93;
    font-size: 12px;
    font-weight: 500;
}


.day-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    text-decoration: none;
    color: var(--text-primary);
    margin: 0 auto;
}


.day-number:hover {
    background: var(--bg-hover);
}


.day-number.selected {
    background: var(--primary-color);
    color: white;
}


.day-number.today {
    border: 2px solid var(--primary-color);
}


.nav-arrow {
    --background: transparent;
    --color: #FFFFFF;
    --border-radius: 50%;
    --padding-start: 0;
    --padding-end: 0;
    width: 32px;
    height: 32px;
    margin: 0;
    font-size: 18px;
    cursor: pointer;
}

.nav-arrow:hover {
    --background: rgba(255, 255, 255, 0.1);
}


.time-display {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-color);
    margin-right: 16px;
}


/* Make number input spinners less prominent */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    opacity: 0.3;
}

/* Progress slider styling */
ion-range.progress-slider {
    --bar-height: 6px;
    --bar-border-radius: 3px;
    --knob-size: 16px;
    --bar-background: #2C2C2E;
    --bar-background-active: #4169E1;
    --knob-background: #4169E1;
    --pin-background: #4169E1;
    --pin-color: #FFFFFF;
    --step: 1;
    --tick-height: 0;
    --tick-width: 0;
    --tick-background: transparent;
    --tick-background-active: transparent;
    margin: 0;
    padding: 0;
}

/* For standard HTML sliders (fallback) */
.progress-slider {
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    height: 6px;
    border-radius: 3px;
    outline: none;
    background: linear-gradient(to right, #4169E1 0%, #4169E1 var(--progress-value), #2C2C2E var(--progress-value), #2C2C2E 100%);
}

/* Hide tick marks completely */
ion-range.progress-slider::part(tick) {
    display: none !important;
}

ion-range.progress-slider::part(tick-active) {
    display: none !important;
}

.progress-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #4169E1;
    cursor: pointer;
    margin-top: -5px;
}

.progress-slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #4169E1;
    cursor: pointer;
    margin-top: 0;
    border: none;
}

.daily-side-quest h2{
    padding-bottom: 20px;
}
@media (max-width: 480px) {
    .container {
        width: auto;
    }
}
