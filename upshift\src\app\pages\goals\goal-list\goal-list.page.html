﻿<!-- Exact HTML from Django template with Angular syntax -->
<div class="container">
    <header>
        <div class="logo">
            <img src="assets/images/upshift_icon_mini.svg" alt="Upshift">
            <span>Upshift</span>
        </div>
        <h1>Goals</h1>
    </header>

    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
        <h2>My Goals</h2>
        <a href="#" id="add-goal-btn" class="add-quest-link" (click)="openAddGoalModal($event)">
            <span class="add-quest-icon">+</span> Add Goal
        </a>
    </div>

    <ng-container *ngIf="goals.length > 0; else noGoals">
        <a *ngFor="let goal of goals" [routerLink]="['/goals', goal.id]" class="goal-card-link">
            <div class="goal-card">
                <div class="goal-header">
                    <div class="goal-title">
                        <span class="goal-emoji">{{ goal.emoji }}</span>
                        <span class="goal-name">{{ goal.name }}</span>
                    </div>
                    <span class="goal-percent">{{ goal.progressPercent }}%</span>
                </div>

                <div class="progress-bar">
                    <div class="progress-fill" [style.width.%]="goal.progressPercent"></div>
                </div>

                <p class="goal-value">
                    <span>{{ goal.current_value }}/{{ goal.goal_value }} {{ goal.goal_unit }} </span>
                    <span *ngIf="goal.totalMicrogoals > 0">Micro goals: {{ goal.completedMicrogoals }}/{{ goal.totalMicrogoals }} </span>
                </p>
            </div>
        </a>
    </ng-container>

    <ng-template #noGoals>
        <p class="no-goals">You have no goals yet.</p>
    </ng-template>
</div>

<!-- Add Goal Modal -->
<div id="add-goal-modal" class="modal" [style.display]="showAddGoalModal ? 'block' : 'none'">
    <div class="modal-content">
        <span class="close-modal" (click)="closeAddGoalModal()">&times;</span>
        <h2>Add New Goal</h2>
        <form (ngSubmit)="createGoal()" #goalForm="ngForm">
            <div style="display: flex;gap: 10px;">
                <div class="form-group">
                    <input type="text" id="emoji" name="emoji" [(ngModel)]="newGoal.emoji" value="🎯" appEmojiInput>
                </div>
                <div class="form-group">
                    <input type="text" id="name" name="name" [(ngModel)]="newGoal.name" placeholder="Enter goal name" required>
                </div>
            </div>
            <div class="form-group">
                <label for="description">Description</label>
                <textarea id="description" name="description" [(ngModel)]="newGoal.description" placeholder="Enter goal description"></textarea>
            </div>
            <div class="form-group goal-settings">
                <label>Goal Target</label>
                <div class="goal-inputs">
                    <input type="number" id="goal_value" name="goal_value" [(ngModel)]="newGoal.goal_value" value="100" min="1">
                    <select id="goal_unit" name="goal_unit" [(ngModel)]="newGoal.goal_unit">
                        <option value="count">count</option>
                        <option value="steps">steps</option>
                        <option value="m">meters</option>
                        <option value="km">kilometers</option>
                        <option value="sec">seconds</option>
                        <option value="min">minutes</option>
                        <option value="hr">hours</option>
                        <option value="days">days</option>
                        <option value="weeks">weeks</option>
                        <option value="months">months</option>
                        <option value="years">years</option>
                        <option value="Cal">calories</option>
                        <option value="g">grams</option>
                        <option value="mg">milligrams</option>
                        <option value="pages">pages</option>
                        <option value="books">books</option>
                        <option value="%">percent</option>
                        <option value="€">euros</option>
                        <option value="$">dollars</option>
                        <option value="£">pounds</option>
                    </select>
                </div>
            </div>
            <button type="submit" class="submit-btn">Create Goal</button>
        </form>
    </div>
</div>
