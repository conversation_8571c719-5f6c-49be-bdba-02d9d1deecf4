﻿import { Component, inject, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { RouterModule, Router } from '@angular/router';
import { Preferences } from '@capacitor/preferences';
import { IonicModule } from '@ionic/angular';
import { SharedModule } from "../shared/shared.module";
import { OnboardingService, Question } from "../services/onboarding.service";
import { Subscription } from "rxjs";
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { PreferencesService } from '../services/preferences.service';
import { SupabaseService } from '../services/supabase.service';
@Component({
  selector: 'app-onboarding',
  templateUrl: './onboarding.page.html',
  styleUrls: ['./onboarding.page.scss'],
  standalone: true,
  imports: [CommonModule,
    FormsModule,
    IonicModule,
    RouterModule,
    SharedModule]
})
export class OnboardingPage implements OnInit, OnDestroy {
  currentQuestion: Question | null = null;
  progress: number = 0;
  quizEnded: boolean = false;
  referralCode: string = '';
  appliedUsername: string = '';
  isCheckingUsername: boolean = false;
  usernameError: string = '';
  private subscriptions: Subscription[] = [];

  private preferencesService = inject(PreferencesService);
  private supabaseService = inject(SupabaseService);

  constructor(
    private onboardingService: OnboardingService,
    private router: Router
  ) {
  }

  async ngOnInit() {
    this.referralCode = '';
    this.appliedUsername = '';
    this.isCheckingUsername = false;

    this.subscriptions.push(
      this.onboardingService.currentIndex$.subscribe(() => {
          this.currentQuestion = this.onboardingService.getCurrentQuestion();
          this.progress = this.onboardingService.getProgress();
        }
      ));

    this.subscriptions.push(
      this.onboardingService.quizEnded$.subscribe(ended => {
          this.quizEnded = ended;
        }
      ));

    try {
      const savedCodeObj = await this.preferencesService.get('affiliate_code_used');

      if (savedCodeObj && savedCodeObj.value) {
        this.appliedUsername = savedCodeObj.value;
      }
    } catch (error) {
    }
  }

  public clickedAnswer(answer: string) {
    this.onboardingService.submitAnswer(answer);
  }

  public goBack() {
    this.onboardingService.goToPreviousQuestion();
  }

  public animateButton(event: MouseEvent) {
    const button = event.currentTarget as HTMLElement;

    button.classList.add('btn-flash-animation');

    setTimeout(() => {
      button.classList.remove('btn-flash-animation');
    }, 600); 
  }

  public skipReferral(event: MouseEvent) {
    this.animateButton(event);
    this.navigateToPricing();
  }

  public async applyReferral(event: MouseEvent) {
    this.animateButton(event);

    if (this.appliedUsername) {
      await this.preferencesService.remove('affiliate_code_used');
      this.appliedUsername = '';
    }

    if (!this.referralCode.trim()) {
      return;
    }

    this.isCheckingUsername = true;
    this.usernameError = '';

    try {
      const enteredUsername = this.referralCode.trim();

      try {
        const { data: allProfiles, error: profilesError } = await this.supabaseService.supabase
          .from('profiles')
          .select('username');

        if (profilesError) {
          this.checkAgainstHardcodedUsernames();
          return;
        }

        let foundMatch = false;
        if (allProfiles && allProfiles.length > 0) {
          foundMatch = allProfiles.some(profile =>
            profile.username?.toLowerCase() === enteredUsername.toLowerCase()
          );
        }

        if (foundMatch) {
          await this.preferencesService.set('affiliate_code_used', enteredUsername);
          this.appliedUsername = enteredUsername;
          this.referralCode = ''; 
        } else {
          this.checkAgainstHardcodedUsernames();
        }
      } catch (error) {
        this.checkAgainstHardcodedUsernames();
      }
    } catch (error) {
      this.usernameError = 'Error checking referral code';
    } finally {
      this.isCheckingUsername = false;
    }
  }

  private async checkAgainstHardcodedUsernames() {
    const knownUsernames = ['test', 'admin', 'test2', 'test3'];
    const enteredUsername = this.referralCode.trim().toLowerCase();

    const foundMatch = knownUsernames.includes(enteredUsername);

    if (foundMatch) {
      await this.preferencesService.set('affiliate_code_used', this.referralCode.trim());
      this.appliedUsername = this.referralCode.trim();
      this.referralCode = ''; 
    } else {
      this.usernameError = 'Invalid referral code';
    }
  }

  public async removeReferral(event: MouseEvent) {
    this.animateButton(event);

    try {
      await this.preferencesService.remove('affiliate_code_used');
      this.appliedUsername = '';
    } catch (error) {
    }
  }

  public continueToPrice(event: MouseEvent) {
    this.animateButton(event);
    this.navigateToPricing();
  }

  public onReferralCodeChange(value: string) {
    this.usernameError = '';
  }

  public onInputChange(event: Event) {
    const inputElement = event.target as HTMLInputElement;
    this.referralCode = inputElement.value;
    this.usernameError = '';
  }

  private navigateToPricing() {
    setTimeout(() => {
      this.router.navigate(['/pricing']);
    }, 300);
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }
}
