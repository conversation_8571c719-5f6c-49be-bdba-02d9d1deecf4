import { Component, inject, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { RouterModule, Router } from '@angular/router';
import { Preferences } from '@capacitor/preferences';
import { IonicModule } from '@ionic/angular';
import { SharedModule } from "../shared/shared.module";
import { OnboardingService, Question } from "../services/onboarding.service";
import { Subscription } from "rxjs";
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { PreferencesService } from '../services/preferences.service';
import { SupabaseService } from '../services/supabase.service';
@Component({
  selector: 'app-onboarding',
  templateUrl: './onboarding.page.html',
  styleUrls: ['./onboarding.page.scss'],
  standalone: true,
  imports: [CommonModule,
    FormsModule,
    IonicModule,
    RouterModule,
    SharedModule]
})
export class OnboardingPage implements OnInit, OnDestroy {
  currentQuestion: Question | null = null;
  progress: number = 0;
  quizEnded: boolean = false;
  referralCode: string = '';
  appliedUsername: string = '';
  isCheckingUsername: boolean = false;
  usernameError: string = '';
  private subscriptions: Subscription[] = [];

  private preferencesService = inject(PreferencesService);
  private supabaseService = inject(SupabaseService);

  constructor(
    private onboardingService: OnboardingService,
    private router: Router
  ) {
  }

  async ngOnInit() {
    // Initialize variables
    this.referralCode = '';
    this.appliedUsername = '';
    this.isCheckingUsername = false;

    this.subscriptions.push(
      this.onboardingService.currentIndex$.subscribe(() => {
          this.currentQuestion = this.onboardingService.getCurrentQuestion();
          this.progress = this.onboardingService.getProgress();
        }
      ));

    this.subscriptions.push(
      this.onboardingService.quizEnded$.subscribe(ended => {
          this.quizEnded = ended;
        }
      ));

    // Check if we already have a referral code saved
    try {
      const savedCodeObj = await this.preferencesService.get('affiliate_code_used');

      if (savedCodeObj && savedCodeObj.value) {
        this.appliedUsername = savedCodeObj.value;
        // Don't set referralCode to the saved value, keep it empty for new input
      }
    } catch (error) {
      console.error('Error getting saved referral code:', error);
    }
  }

  public clickedAnswer(answer: string) {
    this.onboardingService.submitAnswer(answer);
  }

  public goBack() {
    this.onboardingService.goToPreviousQuestion();
  }

  public animateButton(event: MouseEvent) {
    // Get the button element that was clicked
    const button = event.currentTarget as HTMLElement;

    // Add the animation class
    button.classList.add('btn-flash-animation');

    // Remove the animation class after the animation completes
    setTimeout(() => {
      button.classList.remove('btn-flash-animation');
    }, 600); // Animation duration + a little extra
  }

  public skipReferral(event: MouseEvent) {
    this.animateButton(event);
    // Navigate to pricing page without saving a referral code
    this.navigateToPricing();
  }

  public async applyReferral(event: MouseEvent) {
    this.animateButton(event);

    // If there's already an applied username, clear it first
    if (this.appliedUsername) {
      await this.preferencesService.remove('affiliate_code_used');
      this.appliedUsername = '';
    }

    if (!this.referralCode.trim()) {
      return;
    }

    this.isCheckingUsername = true;
    this.usernameError = '';

    try {
      const enteredUsername = this.referralCode.trim();

      try {
        // Get all profiles
        const { data: allProfiles, error: profilesError } = await this.supabaseService.supabase
          .from('profiles')
          .select('username');

        if (profilesError) {
          console.error('Error fetching profiles:', profilesError);
          // Fall back to hardcoded usernames
          this.checkAgainstHardcodedUsernames();
          return;
        }

        // Check if the username exists in the fetched profiles
        let foundMatch = false;
        if (allProfiles && allProfiles.length > 0) {
          foundMatch = allProfiles.some(profile =>
            profile.username?.toLowerCase() === enteredUsername.toLowerCase()
          );
        }

        if (foundMatch) {
          // Username exists, save it as the affiliate code
          await this.preferencesService.set('affiliate_code_used', enteredUsername);
          this.appliedUsername = enteredUsername;
          this.referralCode = ''; // Clear the input
        } else {
          // If not found in database, check hardcoded list as fallback
          this.checkAgainstHardcodedUsernames();
        }
      } catch (error) {
        console.error('Error accessing profiles table:', error);
        // Fall back to hardcoded usernames
        this.checkAgainstHardcodedUsernames();
      }
    } catch (error) {
      console.error('Error in applyReferral:', error);
      this.usernameError = 'Error checking referral code';
    } finally {
      this.isCheckingUsername = false;
    }
  }

  /**
   * Fallback method to check against hardcoded usernames
   * when we can't access the profiles table
   */
  private async checkAgainstHardcodedUsernames() {
    const knownUsernames = ['test', 'admin', 'test2', 'test3'];
    const enteredUsername = this.referralCode.trim().toLowerCase();

    // Check if the username exists in our hardcoded list
    const foundMatch = knownUsernames.includes(enteredUsername);

    if (foundMatch) {
      // Username exists, save it as the affiliate code
      await this.preferencesService.set('affiliate_code_used', this.referralCode.trim());
      this.appliedUsername = this.referralCode.trim();
      this.referralCode = ''; // Clear the input
    } else {
      // Username doesn't exist
      this.usernameError = 'Invalid referral code';
    }
  }

  public async removeReferral(event: MouseEvent) {
    this.animateButton(event);

    try {
      // Remove the affiliate code from preferences
      await this.preferencesService.remove('affiliate_code_used');
      this.appliedUsername = '';
    } catch (error) {
      console.error('Error removing referral code:', error);
    }
  }

  public continueToPrice(event: MouseEvent) {
    this.animateButton(event);
    this.navigateToPricing();
  }

  public onReferralCodeChange(value: string) {
    // Clear any error message when the user types
    this.usernameError = '';
  }

  public onInputChange(event: Event) {
    const inputElement = event.target as HTMLInputElement;
    this.referralCode = inputElement.value;
    this.usernameError = '';
  }

  private navigateToPricing() {
    // Navigate to pricing page
    setTimeout(() => {
      this.router.navigate(['/pricing']);
    }, 300);
  }

  ngOnDestroy() {
    // Clean up subscriptions to prevent memory leaks
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }
}
