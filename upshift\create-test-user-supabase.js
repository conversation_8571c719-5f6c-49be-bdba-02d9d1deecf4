﻿
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://tobifepmbrrrvshpvrqa.supabase.co';
const supabaseServiceRoleKey = 'YOUR_SUPABASE_SERVICE_ROLE_KEY'; 

const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

const email = '<EMAIL>';
const password = 'Test123!';
const username = 'testuser';
const name = 'Test User';

async function createTestUser() {
  try {
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      user_metadata: {
        name
      }
    });

    if (authError) {
      return;
    }


    const expiryDate = new Date();
    expiryDate.setMonth(expiryDate.getMonth() + 1);

    const { data: userData, error: userError } = await supabase
      .from('profiles')
      .insert({
        id: authData.user.id,
        email,
        username,
        name,
        registration_date: new Date(),
        last_login: new Date(),
        plan: 'premium',
        plan_expiry: expiryDate,
        level: 1,
        strength_xp: 0,
        money_xp: 0,
        health_xp: 0,
        knowledge_xp: 0
      })
      .select()
      .single();

    if (userError) {
      await supabase.auth.admin.deleteUser(authData.user.id);
      return;
    }

    );
  } catch (error) {
  }
}

createTestUser();
