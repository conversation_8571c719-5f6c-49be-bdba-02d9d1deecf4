{"ast": null, "code": "var _GoalListPage;\nimport { inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { RouterModule } from '@angular/router';\nimport { GoalService } from '../../../services/goal.service';\nimport { Goal } from '../../../models/goal.model';\nimport { combineLatest, map, of, switchMap, take } from 'rxjs';\nimport { SupabaseService } from '../../../services/supabase.service';\nimport { EmojiInputDirective } from '../../../directives/emoji-input.directive';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/router\";\nconst _c0 = a0 => [\"/goals\", a0];\nfunction GoalListPage_ng_container_15_a_1_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const goal_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"Micro goals: \", goal_r2.completedMicrogoals, \"/\", goal_r2.totalMicrogoals, \" \");\n  }\n}\nfunction GoalListPage_ng_container_15_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 45)(1, \"div\", 46)(2, \"div\", 47)(3, \"div\", 48)(4, \"span\", 49);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 50);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"span\", 51);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 52);\n    i0.ɵɵelement(11, \"div\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 54)(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, GoalListPage_ng_container_15_a_1_span_15_Template, 2, 2, \"span\", 55);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const goal_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(10, _c0, goal_r2.id));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(goal_r2.emoji);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(goal_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", goal_r2.progressPercent, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", goal_r2.progressPercent, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\"\", goal_r2.current_value, \"/\", goal_r2.goal_value, \" \", goal_r2.goal_unit, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", goal_r2.totalMicrogoals > 0);\n  }\n}\nfunction GoalListPage_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, GoalListPage_ng_container_15_a_1_Template, 16, 12, \"a\", 44);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.goals);\n  }\n}\nfunction GoalListPage_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 56);\n    i0.ɵɵtext(1, \"You have no goals yet.\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class GoalListPage {\n  constructor() {\n    this.userId = null;\n    this.goals = [];\n    this.showAddGoalModal = false;\n    this.newGoal = this.getEmptyGoal();\n    this.userSubscription = null;\n    this.supabaseService = inject(SupabaseService);\n    this.goalService = inject(GoalService);\n  }\n  ngOnInit() {\n    this.userSubscription = this.supabaseService.currentUser$.pipe(take(1)).subscribe(user => {\n      if (user) {\n        this.userId = user.id;\n        this.loadGoals();\n      }\n    });\n  }\n  ionViewWillEnter() {\n    if (this.userId) {\n      this.loadGoals();\n    }\n  }\n  ngOnDestroy() {\n    if (this.userSubscription) {\n      this.userSubscription.unsubscribe();\n    }\n  }\n  loadGoals() {\n    if (!this.userId) {\n      return;\n    }\n    this.goals = [];\n    this.goalService.getGoals(this.userId).pipe(switchMap(goals => {\n      if (goals.length === 0) {\n        return of([]);\n      }\n      const goalObservables = goals.map(goal => this.goalService.getMicroGoals(goal.id).pipe(map(microgoals => {\n        const totalMicrogoals = microgoals.length;\n        const completedMicrogoals = microgoals.filter(m => m.completed).length;\n        const progressPercent = goal.goal_value > 0 ? Math.min(100, Math.round(goal.current_value / goal.goal_value * 100)) : 0;\n        return {\n          ...goal,\n          progressPercent,\n          totalMicrogoals,\n          completedMicrogoals\n        };\n      })));\n      return combineLatest(goalObservables);\n    })).subscribe({\n      next: goalsWithMicrogoals => {\n        this.goals = goalsWithMicrogoals;\n      },\n      error: error => {\n        this.goals = [];\n      }\n    });\n  }\n  openAddGoalModal(event) {\n    event.preventDefault();\n    this.showAddGoalModal = true;\n    this.newGoal = this.getEmptyGoal();\n  }\n  closeAddGoalModal() {\n    this.showAddGoalModal = false;\n  }\n  createGoal() {\n    if (!this.userId) {\n      return;\n    }\n    if (!this.newGoal.name) {\n      return;\n    }\n    const goalToCreate = {\n      name: this.newGoal.name,\n      description: this.newGoal.description || '',\n      emoji: this.newGoal.emoji || '🎯',\n      goal_value: this.newGoal.goal_value || 0,\n      goal_unit: this.newGoal.goal_unit || 'count',\n      user_id: this.userId,\n      start_date: new Date(),\n      current_value: 0\n    };\n    this.goalService.createGoal(goalToCreate).then(goalId => {\n      this.closeAddGoalModal();\n      this.loadGoals();\n    }).catch(error => {});\n  }\n  getEmptyGoal() {\n    return {\n      name: '',\n      description: '',\n      emoji: '🎯',\n      goal_value: 100,\n      goal_unit: 'count'\n    };\n  }\n}\n_GoalListPage = GoalListPage;\n_GoalListPage.ɵfac = function GoalListPage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _GoalListPage)();\n};\n_GoalListPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _GoalListPage,\n  selectors: [[\"app-goal-list\"]],\n  decls: 83,\n  vars: 9,\n  consts: [[\"noGoals\", \"\"], [\"goalForm\", \"ngForm\"], [1, \"container\"], [1, \"logo\"], [\"src\", \"assets/images/upshift_icon_mini.svg\", \"alt\", \"Upshift\"], [2, \"display\", \"flex\", \"justify-content\", \"space-between\", \"align-items\", \"center\", \"margin-bottom\", \"20px\"], [\"href\", \"#\", \"id\", \"add-goal-btn\", 1, \"add-quest-link\", 3, \"click\"], [1, \"add-quest-icon\"], [4, \"ngIf\", \"ngIfElse\"], [\"id\", \"add-goal-modal\", 1, \"modal\"], [1, \"modal-content\"], [1, \"close-modal\", 3, \"click\"], [3, \"ngSubmit\"], [2, \"display\", \"flex\", \"gap\", \"10px\"], [1, \"form-group\"], [\"type\", \"text\", \"id\", \"emoji\", \"name\", \"emoji\", \"value\", \"\\uD83C\\uDFAF\", \"appEmojiInput\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"id\", \"name\", \"name\", \"name\", \"placeholder\", \"Enter goal name\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"description\"], [\"id\", \"description\", \"name\", \"description\", \"placeholder\", \"Enter goal description\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"goal-settings\"], [1, \"goal-inputs\"], [\"type\", \"number\", \"id\", \"goal_value\", \"name\", \"goal_value\", \"value\", \"100\", \"min\", \"1\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"goal_unit\", \"name\", \"goal_unit\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"count\"], [\"value\", \"steps\"], [\"value\", \"m\"], [\"value\", \"km\"], [\"value\", \"sec\"], [\"value\", \"min\"], [\"value\", \"hr\"], [\"value\", \"days\"], [\"value\", \"weeks\"], [\"value\", \"months\"], [\"value\", \"years\"], [\"value\", \"Cal\"], [\"value\", \"g\"], [\"value\", \"mg\"], [\"value\", \"pages\"], [\"value\", \"books\"], [\"value\", \"%\"], [\"value\", \"\\u20AC\"], [\"value\", \"$\"], [\"value\", \"\\u00A3\"], [\"type\", \"submit\", 1, \"submit-btn\"], [\"class\", \"goal-card-link\", 3, \"routerLink\", 4, \"ngFor\", \"ngForOf\"], [1, \"goal-card-link\", 3, \"routerLink\"], [1, \"goal-card\"], [1, \"goal-header\"], [1, \"goal-title\"], [1, \"goal-emoji\"], [1, \"goal-name\"], [1, \"goal-percent\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"goal-value\"], [4, \"ngIf\"], [1, \"no-goals\"]],\n  template: function GoalListPage_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r1 = i0.ɵɵgetCurrentView();\n      i0.ɵɵelementStart(0, \"div\", 2)(1, \"header\")(2, \"div\", 3);\n      i0.ɵɵelement(3, \"img\", 4);\n      i0.ɵɵelementStart(4, \"span\");\n      i0.ɵɵtext(5, \"Upshift\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(6, \"h1\");\n      i0.ɵɵtext(7, \"Goals\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(8, \"div\", 5)(9, \"h2\");\n      i0.ɵɵtext(10, \"My Goals\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(11, \"a\", 6);\n      i0.ɵɵlistener(\"click\", function GoalListPage_Template_a_click_11_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.openAddGoalModal($event));\n      });\n      i0.ɵɵelementStart(12, \"span\", 7);\n      i0.ɵɵtext(13, \"+\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtext(14, \" Add Goal \");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(15, GoalListPage_ng_container_15_Template, 2, 1, \"ng-container\", 8)(16, GoalListPage_ng_template_16_Template, 2, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(18, \"div\", 9)(19, \"div\", 10)(20, \"span\", 11);\n      i0.ɵɵlistener(\"click\", function GoalListPage_Template_span_click_20_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.closeAddGoalModal());\n      });\n      i0.ɵɵtext(21, \"\\u00D7\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(22, \"h2\");\n      i0.ɵɵtext(23, \"Add New Goal\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(24, \"form\", 12, 1);\n      i0.ɵɵlistener(\"ngSubmit\", function GoalListPage_Template_form_ngSubmit_24_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.createGoal());\n      });\n      i0.ɵɵelementStart(26, \"div\", 13)(27, \"div\", 14)(28, \"input\", 15);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function GoalListPage_Template_input_ngModelChange_28_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        i0.ɵɵtwoWayBindingSet(ctx.newGoal.emoji, $event) || (ctx.newGoal.emoji = $event);\n        return i0.ɵɵresetView($event);\n      });\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(29, \"div\", 14)(30, \"input\", 16);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function GoalListPage_Template_input_ngModelChange_30_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        i0.ɵɵtwoWayBindingSet(ctx.newGoal.name, $event) || (ctx.newGoal.name = $event);\n        return i0.ɵɵresetView($event);\n      });\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(31, \"div\", 14)(32, \"label\", 17);\n      i0.ɵɵtext(33, \"Description\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(34, \"textarea\", 18);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function GoalListPage_Template_textarea_ngModelChange_34_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        i0.ɵɵtwoWayBindingSet(ctx.newGoal.description, $event) || (ctx.newGoal.description = $event);\n        return i0.ɵɵresetView($event);\n      });\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(35, \"div\", 19)(36, \"label\");\n      i0.ɵɵtext(37, \"Goal Target\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(38, \"div\", 20)(39, \"input\", 21);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function GoalListPage_Template_input_ngModelChange_39_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        i0.ɵɵtwoWayBindingSet(ctx.newGoal.goal_value, $event) || (ctx.newGoal.goal_value = $event);\n        return i0.ɵɵresetView($event);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(40, \"select\", 22);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function GoalListPage_Template_select_ngModelChange_40_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        i0.ɵɵtwoWayBindingSet(ctx.newGoal.goal_unit, $event) || (ctx.newGoal.goal_unit = $event);\n        return i0.ɵɵresetView($event);\n      });\n      i0.ɵɵelementStart(41, \"option\", 23);\n      i0.ɵɵtext(42, \"count\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(43, \"option\", 24);\n      i0.ɵɵtext(44, \"steps\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(45, \"option\", 25);\n      i0.ɵɵtext(46, \"meters\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(47, \"option\", 26);\n      i0.ɵɵtext(48, \"kilometers\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(49, \"option\", 27);\n      i0.ɵɵtext(50, \"seconds\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(51, \"option\", 28);\n      i0.ɵɵtext(52, \"minutes\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(53, \"option\", 29);\n      i0.ɵɵtext(54, \"hours\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(55, \"option\", 30);\n      i0.ɵɵtext(56, \"days\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(57, \"option\", 31);\n      i0.ɵɵtext(58, \"weeks\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(59, \"option\", 32);\n      i0.ɵɵtext(60, \"months\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(61, \"option\", 33);\n      i0.ɵɵtext(62, \"years\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(63, \"option\", 34);\n      i0.ɵɵtext(64, \"calories\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(65, \"option\", 35);\n      i0.ɵɵtext(66, \"grams\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(67, \"option\", 36);\n      i0.ɵɵtext(68, \"milligrams\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(69, \"option\", 37);\n      i0.ɵɵtext(70, \"pages\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(71, \"option\", 38);\n      i0.ɵɵtext(72, \"books\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(73, \"option\", 39);\n      i0.ɵɵtext(74, \"percent\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(75, \"option\", 40);\n      i0.ɵɵtext(76, \"euros\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(77, \"option\", 41);\n      i0.ɵɵtext(78, \"dollars\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(79, \"option\", 42);\n      i0.ɵɵtext(80, \"pounds\");\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(81, \"button\", 43);\n      i0.ɵɵtext(82, \"Create Goal\");\n      i0.ɵɵelementEnd()()()();\n    }\n    if (rf & 2) {\n      const noGoals_r4 = i0.ɵɵreference(17);\n      i0.ɵɵadvance(15);\n      i0.ɵɵproperty(\"ngIf\", ctx.goals.length > 0)(\"ngIfElse\", noGoals_r4);\n      i0.ɵɵadvance(3);\n      i0.ɵɵstyleProp(\"display\", ctx.showAddGoalModal ? \"block\" : \"none\");\n      i0.ɵɵadvance(10);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newGoal.emoji);\n      i0.ɵɵadvance(2);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newGoal.name);\n      i0.ɵɵadvance(4);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newGoal.description);\n      i0.ɵɵadvance(5);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newGoal.goal_value);\n      i0.ɵɵadvance();\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newGoal.goal_unit);\n    }\n  },\n  dependencies: [IonicModule, i1.RouterLinkWithHrefDelegate, CommonModule, i2.NgForOf, i2.NgIf, FormsModule, i3.ɵNgNoValidate, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.DefaultValueAccessor, i3.NumberValueAccessor, i3.SelectControlValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.MinValidator, i3.NgModel, i3.NgForm, RouterModule, i4.RouterLink, EmojiInputDirective],\n  styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%];\\n\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\tvar __webpack_modules__ = ({\\n\\n\\n 588:\\n\\n\\n\\n\\n\\n (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/sass-loader/dist/cjs.js):\\\\nexpected selector.\\\\n    \\u2577\\\\n556 \\u2502  Add Goal Button */\\\\r\\\\n    \\u2502                   ^\\\\n    \\u2575\\\\n  src\\\\\\\\app\\\\\\\\pages\\\\\\\\goals\\\\\\\\goal-list\\\\\\\\goal-list.page.scss 556:19  root stylesheet\\\");\\n\\n\\n })\\n\\n\\n \\t});\\n\\n\\n\\n \\t\\n\\n \\t// startup\\n\\n \\t// Load entry module and return exports\\n\\n \\t// This entry module doesn't tell about it's top-level declarations so it can't be inlined\\n\\n \\tvar __webpack_exports__ = {};\\n\\n \\t__webpack_modules__[588]();\\n\\n \\tresource = __webpack_exports__;\\n\\n \\t\\n\\n })()\\n;\"]\n});", "map": {"version": 3, "names": ["inject", "CommonModule", "FormsModule", "IonicModule", "RouterModule", "GoalService", "Goal", "combineLatest", "map", "of", "switchMap", "take", "SupabaseService", "EmojiInputDirective", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate2", "goal_r2", "completedMicrogoals", "totalMicrogoals", "ɵɵelement", "ɵɵtemplate", "GoalListPage_ng_container_15_a_1_span_15_Template", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "id", "ɵɵtextInterpolate", "emoji", "name", "ɵɵtextInterpolate1", "progressPercent", "ɵɵstyleProp", "ɵɵtextInterpolate3", "current_value", "goal_value", "goal_unit", "ɵɵelementContainerStart", "GoalListPage_ng_container_15_a_1_Template", "ctx_r2", "goals", "GoalListPage", "constructor", "userId", "showAddGoalModal", "newGoal", "getEmptyGoal", "userSubscription", "supabaseService", "goalService", "ngOnInit", "currentUser$", "pipe", "subscribe", "user", "loadGoals", "ionViewWillEnter", "ngOnDestroy", "unsubscribe", "getGoals", "length", "goalObservables", "goal", "getMicroGoals", "microgoals", "filter", "m", "completed", "Math", "min", "round", "next", "goalsWithMicrogoals", "error", "openAddGoalModal", "event", "preventDefault", "closeAddGoalModal", "createGoal", "goalToCreate", "description", "user_id", "start_date", "Date", "then", "goalId", "catch", "selectors", "decls", "vars", "consts", "template", "GoalListPage_Template", "rf", "ctx", "ɵɵlistener", "GoalListPage_Template_a_click_11_listener", "$event", "ɵɵrestoreView", "_r1", "ɵɵresetView", "GoalListPage_ng_container_15_Template", "GoalListPage_ng_template_16_Template", "ɵɵtemplateRefExtractor", "GoalListPage_Template_span_click_20_listener", "GoalListPage_Template_form_ngSubmit_24_listener", "ɵɵtwoWayListener", "GoalListPage_Template_input_ngModelChange_28_listener", "ɵɵtwoWayBindingSet", "GoalListPage_Template_input_ngModelChange_30_listener", "GoalListPage_Template_textarea_ngModelChange_34_listener", "GoalListPage_Template_input_ngModelChange_39_listener", "GoalListPage_Template_select_ngModelChange_40_listener", "noGoals_r4", "ɵɵtwoWayProperty", "i1", "RouterLinkWithHrefDelegate", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "ɵNgNoValidate", "NgSelectOption", "ɵNgSelectMultipleOption", "DefaultValueAccessor", "NumberValueAccessor", "SelectControlValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "MinValidator", "NgModel", "NgForm", "i4", "RouterLink", "styles"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\goals\\goal-list\\goal-list.page.ts", "C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\goals\\goal-list\\goal-list.page.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy, inject } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { RouterModule } from '@angular/router';\r\nimport { GoalService } from '../../../services/goal.service';\r\nimport { Goal, GoalUnit } from '../../../models/goal.model';\r\nimport { Subscription, combineLatest, map, of, switchMap, take } from 'rxjs';\r\nimport { SupabaseService } from '../../../services/supabase.service';\r\nimport { EmojiInputDirective } from '../../../directives/emoji-input.directive';\r\n\r\ninterface GoalDisplay extends Goal {\r\n  progressPercent: number;\r\n  totalMicrogoals: number;\r\n  completedMicrogoals: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-goal-list',\r\n  templateUrl: './goal-list.page.html',\r\n  styleUrls: ['./goal-list.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule, FormsModule, RouterModule, EmojiInputDirective]\r\n})\r\nexport class GoalListPage implements OnInit, OnDestroy {\r\n  userId: string | null = null;\r\n\r\n  goals: GoalDisplay[] = [];\r\n\r\n  showAddGoalModal = false;\r\n  newGoal = this.getEmptyGoal();\r\n\r\n  private userSubscription: Subscription | null = null;\r\n\r\n  private supabaseService = inject(SupabaseService);\r\n  private goalService = inject(GoalService);\r\n\r\n  constructor() {\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.userSubscription = this.supabaseService.currentUser$.pipe(\r\n      take(1)\r\n    ).subscribe(user => {\r\n      if (user) {\r\n        this.userId = user.id;\r\n        this.loadGoals();\r\n      }\r\n    });\r\n  }\r\n\r\n  ionViewWillEnter() {\r\n    if (this.userId) {\r\n      this.loadGoals();\r\n    }\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    if (this.userSubscription) {\r\n      this.userSubscription.unsubscribe();\r\n    }\r\n  }\r\n\r\n  loadGoals() {\r\n    if (!this.userId) {\r\n      return;\r\n    }\r\n\r\n\r\n    this.goals = [];\r\n\r\n    this.goalService.getGoals(this.userId).pipe(\r\n      switchMap(goals => {\r\n\r\n        if (goals.length === 0) {\r\n          return of([]);\r\n        }\r\n\r\n        const goalObservables = goals.map(goal =>\r\n          this.goalService.getMicroGoals(goal.id!).pipe(\r\n            map(microgoals => {\r\n\r\n              const totalMicrogoals = microgoals.length;\r\n              const completedMicrogoals = microgoals.filter(m => m.completed).length;\r\n              const progressPercent = goal.goal_value > 0\r\n                ? Math.min(100, Math.round((goal.current_value / goal.goal_value) * 100))\r\n                : 0;\r\n\r\n              return {\r\n                ...goal,\r\n                progressPercent,\r\n                totalMicrogoals,\r\n                completedMicrogoals\r\n              } as GoalDisplay;\r\n            })\r\n          )\r\n        );\r\n\r\n        return combineLatest(goalObservables);\r\n      })\r\n    ).subscribe({\r\n      next: goalsWithMicrogoals => {\r\n        this.goals = goalsWithMicrogoals;\r\n      },\r\n      error: error => {\r\n        this.goals = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  openAddGoalModal(event: Event) {\r\n    event.preventDefault();\r\n    this.showAddGoalModal = true;\r\n    this.newGoal = this.getEmptyGoal();\r\n  }\r\n\r\n  closeAddGoalModal() {\r\n    this.showAddGoalModal = false;\r\n  }\r\n\r\n  createGoal() {\r\n    if (!this.userId) {\r\n      return;\r\n    }\r\n\r\n    if (!this.newGoal.name) {\r\n      return;\r\n    }\r\n\r\n    const goalToCreate: Goal = {\r\n      name: this.newGoal.name,\r\n      description: this.newGoal.description || '',\r\n      emoji: this.newGoal.emoji || '🎯',\r\n      goal_value: this.newGoal.goal_value || 0,\r\n      goal_unit: this.newGoal.goal_unit || 'count',\r\n      user_id: this.userId,\r\n      start_date: new Date(),\r\n      current_value: 0\r\n    };\r\n\r\n\r\n    this.goalService.createGoal(goalToCreate)\r\n      .then((goalId) => {\r\n        this.closeAddGoalModal();\r\n        this.loadGoals();\r\n      })\r\n      .catch(error => {\r\n      });\r\n  }\r\n\r\n  private getEmptyGoal(): Partial<Goal> {\r\n    return {\r\n      name: '',\r\n      description: '',\r\n      emoji: '🎯',\r\n      goal_value: 100,\r\n      goal_unit: 'count' as GoalUnit\r\n    };\r\n  }\r\n}\r\n", "<!-- Exact HTML from Django template with Angular syntax -->\r\n<div class=\"container\">\r\n    <header>\r\n        <div class=\"logo\">\r\n            <img src=\"assets/images/upshift_icon_mini.svg\" alt=\"Upshift\">\r\n            <span>Upshift</span>\r\n        </div>\r\n        <h1>Goals</h1>\r\n    </header>\r\n\r\n    <div style=\"display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;\">\r\n        <h2>My Goals</h2>\r\n        <a href=\"#\" id=\"add-goal-btn\" class=\"add-quest-link\" (click)=\"openAddGoalModal($event)\">\r\n            <span class=\"add-quest-icon\">+</span> Add Goal\r\n        </a>\r\n    </div>\r\n\r\n    <ng-container *ngIf=\"goals.length > 0; else noGoals\">\r\n        <a *ngFor=\"let goal of goals\" [routerLink]=\"['/goals', goal.id]\" class=\"goal-card-link\">\r\n            <div class=\"goal-card\">\r\n                <div class=\"goal-header\">\r\n                    <div class=\"goal-title\">\r\n                        <span class=\"goal-emoji\">{{ goal.emoji }}</span>\r\n                        <span class=\"goal-name\">{{ goal.name }}</span>\r\n                    </div>\r\n                    <span class=\"goal-percent\">{{ goal.progressPercent }}%</span>\r\n                </div>\r\n\r\n                <div class=\"progress-bar\">\r\n                    <div class=\"progress-fill\" [style.width.%]=\"goal.progressPercent\"></div>\r\n                </div>\r\n\r\n                <p class=\"goal-value\">\r\n                    <span>{{ goal.current_value }}/{{ goal.goal_value }} {{ goal.goal_unit }} </span>\r\n                    <span *ngIf=\"goal.totalMicrogoals > 0\">Micro goals: {{ goal.completedMicrogoals }}/{{ goal.totalMicrogoals }} </span>\r\n                </p>\r\n            </div>\r\n        </a>\r\n    </ng-container>\r\n\r\n    <ng-template #noGoals>\r\n        <p class=\"no-goals\">You have no goals yet.</p>\r\n    </ng-template>\r\n</div>\r\n\r\n<!-- Add Goal Modal -->\r\n<div id=\"add-goal-modal\" class=\"modal\" [style.display]=\"showAddGoalModal ? 'block' : 'none'\">\r\n    <div class=\"modal-content\">\r\n        <span class=\"close-modal\" (click)=\"closeAddGoalModal()\">&times;</span>\r\n        <h2>Add New Goal</h2>\r\n        <form (ngSubmit)=\"createGoal()\" #goalForm=\"ngForm\">\r\n            <div style=\"display: flex;gap: 10px;\">\r\n                <div class=\"form-group\">\r\n                    <input type=\"text\" id=\"emoji\" name=\"emoji\" [(ngModel)]=\"newGoal.emoji\" value=\"🎯\" appEmojiInput>\r\n                </div>\r\n                <div class=\"form-group\">\r\n                    <input type=\"text\" id=\"name\" name=\"name\" [(ngModel)]=\"newGoal.name\" placeholder=\"Enter goal name\" required>\r\n                </div>\r\n            </div>\r\n            <div class=\"form-group\">\r\n                <label for=\"description\">Description</label>\r\n                <textarea id=\"description\" name=\"description\" [(ngModel)]=\"newGoal.description\" placeholder=\"Enter goal description\"></textarea>\r\n            </div>\r\n            <div class=\"form-group goal-settings\">\r\n                <label>Goal Target</label>\r\n                <div class=\"goal-inputs\">\r\n                    <input type=\"number\" id=\"goal_value\" name=\"goal_value\" [(ngModel)]=\"newGoal.goal_value\" value=\"100\" min=\"1\">\r\n                    <select id=\"goal_unit\" name=\"goal_unit\" [(ngModel)]=\"newGoal.goal_unit\">\r\n                        <option value=\"count\">count</option>\r\n                        <option value=\"steps\">steps</option>\r\n                        <option value=\"m\">meters</option>\r\n                        <option value=\"km\">kilometers</option>\r\n                        <option value=\"sec\">seconds</option>\r\n                        <option value=\"min\">minutes</option>\r\n                        <option value=\"hr\">hours</option>\r\n                        <option value=\"days\">days</option>\r\n                        <option value=\"weeks\">weeks</option>\r\n                        <option value=\"months\">months</option>\r\n                        <option value=\"years\">years</option>\r\n                        <option value=\"Cal\">calories</option>\r\n                        <option value=\"g\">grams</option>\r\n                        <option value=\"mg\">milligrams</option>\r\n                        <option value=\"pages\">pages</option>\r\n                        <option value=\"books\">books</option>\r\n                        <option value=\"%\">percent</option>\r\n                        <option value=\"€\">euros</option>\r\n                        <option value=\"$\">dollars</option>\r\n                        <option value=\"£\">pounds</option>\r\n                    </select>\r\n                </div>\r\n            </div>\r\n            <button type=\"submit\" class=\"submit-btn\">Create Goal</button>\r\n        </form>\r\n    </div>\r\n</div>\r\n"], "mappings": ";AAAA,SAAuCA,MAAM,QAAQ,eAAe;AACpE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,SAASC,IAAI,QAAkB,4BAA4B;AAC3D,SAAuBC,aAAa,EAAEC,GAAG,EAAEC,EAAE,EAAEC,SAAS,EAAEC,IAAI,QAAQ,MAAM;AAC5E,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,mBAAmB,QAAQ,2CAA2C;;;;;;;;;ICyB3DC,EAAA,CAAAC,cAAA,WAAuC;IAAAD,EAAA,CAAAE,MAAA,GAAuE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA9EH,EAAA,CAAAI,SAAA,EAAuE;IAAvEJ,EAAA,CAAAK,kBAAA,kBAAAC,OAAA,CAAAC,mBAAA,OAAAD,OAAA,CAAAE,eAAA,MAAuE;;;;;IAZ1GR,EAJhB,CAAAC,cAAA,YAAwF,cAC7D,cACM,cACG,eACK;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChDH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAC3CF,EAD2C,CAAAG,YAAA,EAAO,EAC5C;IACNH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAC1DF,EAD0D,CAAAG,YAAA,EAAO,EAC3D;IAENH,EAAA,CAAAC,cAAA,eAA0B;IACtBD,EAAA,CAAAS,SAAA,eAAwE;IAC5ET,EAAA,CAAAG,YAAA,EAAM;IAGFH,EADJ,CAAAC,cAAA,aAAsB,YACZ;IAAAD,EAAA,CAAAE,MAAA,IAAoE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjFH,EAAA,CAAAU,UAAA,KAAAC,iDAAA,mBAAuC;IAGnDX,EAFQ,CAAAG,YAAA,EAAI,EACF,EACN;;;;IAnB0BH,EAAA,CAAAY,UAAA,eAAAZ,EAAA,CAAAa,eAAA,KAAAC,GAAA,EAAAR,OAAA,CAAAS,EAAA,EAAkC;IAIvBf,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAgB,iBAAA,CAAAV,OAAA,CAAAW,KAAA,CAAgB;IACjBjB,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAgB,iBAAA,CAAAV,OAAA,CAAAY,IAAA,CAAe;IAEhBlB,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAmB,kBAAA,KAAAb,OAAA,CAAAc,eAAA,MAA2B;IAI3BpB,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAqB,WAAA,UAAAf,OAAA,CAAAc,eAAA,MAAsC;IAI3DpB,EAAA,CAAAI,SAAA,GAAoE;IAApEJ,EAAA,CAAAsB,kBAAA,KAAAhB,OAAA,CAAAiB,aAAA,OAAAjB,OAAA,CAAAkB,UAAA,OAAAlB,OAAA,CAAAmB,SAAA,MAAoE;IACnEzB,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAY,UAAA,SAAAN,OAAA,CAAAE,eAAA,KAA8B;;;;;IAjBrDR,EAAA,CAAA0B,uBAAA,GAAqD;IACjD1B,EAAA,CAAAU,UAAA,IAAAiB,yCAAA,kBAAwF;;;;;IAApE3B,EAAA,CAAAI,SAAA,EAAQ;IAARJ,EAAA,CAAAY,UAAA,YAAAgB,MAAA,CAAAC,KAAA,CAAQ;;;;;IAuB5B7B,EAAA,CAAAC,cAAA,YAAoB;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;ADjBtD,OAAM,MAAO2B,YAAY;EAavBC,YAAA;IAZA,KAAAC,MAAM,GAAkB,IAAI;IAE5B,KAAAH,KAAK,GAAkB,EAAE;IAEzB,KAAAI,gBAAgB,GAAG,KAAK;IACxB,KAAAC,OAAO,GAAG,IAAI,CAACC,YAAY,EAAE;IAErB,KAAAC,gBAAgB,GAAwB,IAAI;IAE5C,KAAAC,eAAe,GAAGnD,MAAM,CAACY,eAAe,CAAC;IACzC,KAAAwC,WAAW,GAAGpD,MAAM,CAACK,WAAW,CAAC;EAGzC;EAEAgD,QAAQA,CAAA;IACN,IAAI,CAACH,gBAAgB,GAAG,IAAI,CAACC,eAAe,CAACG,YAAY,CAACC,IAAI,CAC5D5C,IAAI,CAAC,CAAC,CAAC,CACR,CAAC6C,SAAS,CAACC,IAAI,IAAG;MACjB,IAAIA,IAAI,EAAE;QACR,IAAI,CAACX,MAAM,GAAGW,IAAI,CAAC5B,EAAE;QACrB,IAAI,CAAC6B,SAAS,EAAE;MAClB;IACF,CAAC,CAAC;EACJ;EAEAC,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACb,MAAM,EAAE;MACf,IAAI,CAACY,SAAS,EAAE;IAClB;EACF;EAEAE,WAAWA,CAAA;IACT,IAAI,IAAI,CAACV,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACW,WAAW,EAAE;IACrC;EACF;EAEAH,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAACZ,MAAM,EAAE;MAChB;IACF;IAGA,IAAI,CAACH,KAAK,GAAG,EAAE;IAEf,IAAI,CAACS,WAAW,CAACU,QAAQ,CAAC,IAAI,CAAChB,MAAM,CAAC,CAACS,IAAI,CACzC7C,SAAS,CAACiC,KAAK,IAAG;MAEhB,IAAIA,KAAK,CAACoB,MAAM,KAAK,CAAC,EAAE;QACtB,OAAOtD,EAAE,CAAC,EAAE,CAAC;MACf;MAEA,MAAMuD,eAAe,GAAGrB,KAAK,CAACnC,GAAG,CAACyD,IAAI,IACpC,IAAI,CAACb,WAAW,CAACc,aAAa,CAACD,IAAI,CAACpC,EAAG,CAAC,CAAC0B,IAAI,CAC3C/C,GAAG,CAAC2D,UAAU,IAAG;QAEf,MAAM7C,eAAe,GAAG6C,UAAU,CAACJ,MAAM;QACzC,MAAM1C,mBAAmB,GAAG8C,UAAU,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,CAAC,CAACP,MAAM;QACtE,MAAM7B,eAAe,GAAG+B,IAAI,CAAC3B,UAAU,GAAG,CAAC,GACvCiC,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,KAAK,CAAER,IAAI,CAAC5B,aAAa,GAAG4B,IAAI,CAAC3B,UAAU,GAAI,GAAG,CAAC,CAAC,GACvE,CAAC;QAEL,OAAO;UACL,GAAG2B,IAAI;UACP/B,eAAe;UACfZ,eAAe;UACfD;SACc;MAClB,CAAC,CAAC,CACH,CACF;MAED,OAAOd,aAAa,CAACyD,eAAe,CAAC;IACvC,CAAC,CAAC,CACH,CAACR,SAAS,CAAC;MACVkB,IAAI,EAAEC,mBAAmB,IAAG;QAC1B,IAAI,CAAChC,KAAK,GAAGgC,mBAAmB;MAClC,CAAC;MACDC,KAAK,EAAEA,KAAK,IAAG;QACb,IAAI,CAACjC,KAAK,GAAG,EAAE;MACjB;KACD,CAAC;EACJ;EAEAkC,gBAAgBA,CAACC,KAAY;IAC3BA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAAChC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,YAAY,EAAE;EACpC;EAEA+B,iBAAiBA,CAAA;IACf,IAAI,CAACjC,gBAAgB,GAAG,KAAK;EAC/B;EAEAkC,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACnC,MAAM,EAAE;MAChB;IACF;IAEA,IAAI,CAAC,IAAI,CAACE,OAAO,CAAChB,IAAI,EAAE;MACtB;IACF;IAEA,MAAMkD,YAAY,GAAS;MACzBlD,IAAI,EAAE,IAAI,CAACgB,OAAO,CAAChB,IAAI;MACvBmD,WAAW,EAAE,IAAI,CAACnC,OAAO,CAACmC,WAAW,IAAI,EAAE;MAC3CpD,KAAK,EAAE,IAAI,CAACiB,OAAO,CAACjB,KAAK,IAAI,IAAI;MACjCO,UAAU,EAAE,IAAI,CAACU,OAAO,CAACV,UAAU,IAAI,CAAC;MACxCC,SAAS,EAAE,IAAI,CAACS,OAAO,CAACT,SAAS,IAAI,OAAO;MAC5C6C,OAAO,EAAE,IAAI,CAACtC,MAAM;MACpBuC,UAAU,EAAE,IAAIC,IAAI,EAAE;MACtBjD,aAAa,EAAE;KAChB;IAGD,IAAI,CAACe,WAAW,CAAC6B,UAAU,CAACC,YAAY,CAAC,CACtCK,IAAI,CAAEC,MAAM,IAAI;MACf,IAAI,CAACR,iBAAiB,EAAE;MACxB,IAAI,CAACtB,SAAS,EAAE;IAClB,CAAC,CAAC,CACD+B,KAAK,CAACb,KAAK,IAAG,CACf,CAAC,CAAC;EACN;EAEQ3B,YAAYA,CAAA;IAClB,OAAO;MACLjB,IAAI,EAAE,EAAE;MACRmD,WAAW,EAAE,EAAE;MACfpD,KAAK,EAAE,IAAI;MACXO,UAAU,EAAE,GAAG;MACfC,SAAS,EAAE;KACZ;EACH;;gBAtIWK,YAAY;;mCAAZA,aAAY;AAAA;;QAAZA,aAAY;EAAA8C,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;;MCrBjBlF,EAFR,CAAAC,cAAA,aAAuB,aACX,aACc;MACdD,EAAA,CAAAS,SAAA,aAA6D;MAC7DT,EAAA,CAAAC,cAAA,WAAM;MAAAD,EAAA,CAAAE,MAAA,cAAO;MACjBF,EADiB,CAAAG,YAAA,EAAO,EAClB;MACNH,EAAA,CAAAC,cAAA,SAAI;MAAAD,EAAA,CAAAE,MAAA,YAAK;MACbF,EADa,CAAAG,YAAA,EAAK,EACT;MAGLH,EADJ,CAAAC,cAAA,aAAsG,SAC9F;MAAAD,EAAA,CAAAE,MAAA,gBAAQ;MAAAF,EAAA,CAAAG,YAAA,EAAK;MACjBH,EAAA,CAAAC,cAAA,YAAwF;MAAnCD,EAAA,CAAAoF,UAAA,mBAAAC,0CAAAC,MAAA;QAAAtF,EAAA,CAAAuF,aAAA,CAAAC,GAAA;QAAA,OAAAxF,EAAA,CAAAyF,WAAA,CAASN,GAAA,CAAApB,gBAAA,CAAAuB,MAAA,CAAwB;MAAA,EAAC;MACnFtF,EAAA,CAAAC,cAAA,eAA6B;MAAAD,EAAA,CAAAE,MAAA,SAAC;MAAAF,EAAA,CAAAG,YAAA,EAAO;MAACH,EAAA,CAAAE,MAAA,kBAC1C;MACJF,EADI,CAAAG,YAAA,EAAI,EACF;MAyBNH,EAvBA,CAAAU,UAAA,KAAAgF,qCAAA,0BAAqD,KAAAC,oCAAA,gCAAA3F,EAAA,CAAA4F,sBAAA,CAuB/B;MAG1B5F,EAAA,CAAAG,YAAA,EAAM;MAKEH,EAFR,CAAAC,cAAA,cAA6F,eAC9D,gBACiC;MAA9BD,EAAA,CAAAoF,UAAA,mBAAAS,6CAAA;QAAA7F,EAAA,CAAAuF,aAAA,CAAAC,GAAA;QAAA,OAAAxF,EAAA,CAAAyF,WAAA,CAASN,GAAA,CAAAjB,iBAAA,EAAmB;MAAA,EAAC;MAAClE,EAAA,CAAAE,MAAA,cAAO;MAAAF,EAAA,CAAAG,YAAA,EAAO;MACtEH,EAAA,CAAAC,cAAA,UAAI;MAAAD,EAAA,CAAAE,MAAA,oBAAY;MAAAF,EAAA,CAAAG,YAAA,EAAK;MACrBH,EAAA,CAAAC,cAAA,mBAAmD;MAA7CD,EAAA,CAAAoF,UAAA,sBAAAU,gDAAA;QAAA9F,EAAA,CAAAuF,aAAA,CAAAC,GAAA;QAAA,OAAAxF,EAAA,CAAAyF,WAAA,CAAYN,GAAA,CAAAhB,UAAA,EAAY;MAAA,EAAC;MAGnBnE,EAFR,CAAAC,cAAA,eAAsC,eACV,iBAC4E;MAArDD,EAAA,CAAA+F,gBAAA,2BAAAC,sDAAAV,MAAA;QAAAtF,EAAA,CAAAuF,aAAA,CAAAC,GAAA;QAAAxF,EAAA,CAAAiG,kBAAA,CAAAd,GAAA,CAAAjD,OAAA,CAAAjB,KAAA,EAAAqE,MAAA,MAAAH,GAAA,CAAAjD,OAAA,CAAAjB,KAAA,GAAAqE,MAAA;QAAA,OAAAtF,EAAA,CAAAyF,WAAA,CAAAH,MAAA;MAAA,EAA2B;MAC1EtF,EADI,CAAAG,YAAA,EAAgG,EAC9F;MAEFH,EADJ,CAAAC,cAAA,eAAwB,iBACuF;MAAlED,EAAA,CAAA+F,gBAAA,2BAAAG,sDAAAZ,MAAA;QAAAtF,EAAA,CAAAuF,aAAA,CAAAC,GAAA;QAAAxF,EAAA,CAAAiG,kBAAA,CAAAd,GAAA,CAAAjD,OAAA,CAAAhB,IAAA,EAAAoE,MAAA,MAAAH,GAAA,CAAAjD,OAAA,CAAAhB,IAAA,GAAAoE,MAAA;QAAA,OAAAtF,EAAA,CAAAyF,WAAA,CAAAH,MAAA;MAAA,EAA0B;MAE3EtF,EAFQ,CAAAG,YAAA,EAA2G,EACzG,EACJ;MAEFH,EADJ,CAAAC,cAAA,eAAwB,iBACK;MAAAD,EAAA,CAAAE,MAAA,mBAAW;MAAAF,EAAA,CAAAG,YAAA,EAAQ;MAC5CH,EAAA,CAAAC,cAAA,oBAAqH;MAAvED,EAAA,CAAA+F,gBAAA,2BAAAI,yDAAAb,MAAA;QAAAtF,EAAA,CAAAuF,aAAA,CAAAC,GAAA;QAAAxF,EAAA,CAAAiG,kBAAA,CAAAd,GAAA,CAAAjD,OAAA,CAAAmC,WAAA,EAAAiB,MAAA,MAAAH,GAAA,CAAAjD,OAAA,CAAAmC,WAAA,GAAAiB,MAAA;QAAA,OAAAtF,EAAA,CAAAyF,WAAA,CAAAH,MAAA;MAAA,EAAiC;MACnFtF,EADyH,CAAAG,YAAA,EAAW,EAC9H;MAEFH,EADJ,CAAAC,cAAA,eAAsC,aAC3B;MAAAD,EAAA,CAAAE,MAAA,mBAAW;MAAAF,EAAA,CAAAG,YAAA,EAAQ;MAEtBH,EADJ,CAAAC,cAAA,eAAyB,iBACuF;MAArDD,EAAA,CAAA+F,gBAAA,2BAAAK,sDAAAd,MAAA;QAAAtF,EAAA,CAAAuF,aAAA,CAAAC,GAAA;QAAAxF,EAAA,CAAAiG,kBAAA,CAAAd,GAAA,CAAAjD,OAAA,CAAAV,UAAA,EAAA8D,MAAA,MAAAH,GAAA,CAAAjD,OAAA,CAAAV,UAAA,GAAA8D,MAAA;QAAA,OAAAtF,EAAA,CAAAyF,WAAA,CAAAH,MAAA;MAAA,EAAgC;MAAvFtF,EAAA,CAAAG,YAAA,EAA4G;MAC5GH,EAAA,CAAAC,cAAA,kBAAwE;MAAhCD,EAAA,CAAA+F,gBAAA,2BAAAM,uDAAAf,MAAA;QAAAtF,EAAA,CAAAuF,aAAA,CAAAC,GAAA;QAAAxF,EAAA,CAAAiG,kBAAA,CAAAd,GAAA,CAAAjD,OAAA,CAAAT,SAAA,EAAA6D,MAAA,MAAAH,GAAA,CAAAjD,OAAA,CAAAT,SAAA,GAAA6D,MAAA;QAAA,OAAAtF,EAAA,CAAAyF,WAAA,CAAAH,MAAA;MAAA,EAA+B;MACnEtF,EAAA,CAAAC,cAAA,kBAAsB;MAAAD,EAAA,CAAAE,MAAA,aAAK;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACpCH,EAAA,CAAAC,cAAA,kBAAsB;MAAAD,EAAA,CAAAE,MAAA,aAAK;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACpCH,EAAA,CAAAC,cAAA,kBAAkB;MAAAD,EAAA,CAAAE,MAAA,cAAM;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACjCH,EAAA,CAAAC,cAAA,kBAAmB;MAAAD,EAAA,CAAAE,MAAA,kBAAU;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACtCH,EAAA,CAAAC,cAAA,kBAAoB;MAAAD,EAAA,CAAAE,MAAA,eAAO;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACpCH,EAAA,CAAAC,cAAA,kBAAoB;MAAAD,EAAA,CAAAE,MAAA,eAAO;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACpCH,EAAA,CAAAC,cAAA,kBAAmB;MAAAD,EAAA,CAAAE,MAAA,aAAK;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACjCH,EAAA,CAAAC,cAAA,kBAAqB;MAAAD,EAAA,CAAAE,MAAA,YAAI;MAAAF,EAAA,CAAAG,YAAA,EAAS;MAClCH,EAAA,CAAAC,cAAA,kBAAsB;MAAAD,EAAA,CAAAE,MAAA,aAAK;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACpCH,EAAA,CAAAC,cAAA,kBAAuB;MAAAD,EAAA,CAAAE,MAAA,cAAM;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACtCH,EAAA,CAAAC,cAAA,kBAAsB;MAAAD,EAAA,CAAAE,MAAA,aAAK;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACpCH,EAAA,CAAAC,cAAA,kBAAoB;MAAAD,EAAA,CAAAE,MAAA,gBAAQ;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACrCH,EAAA,CAAAC,cAAA,kBAAkB;MAAAD,EAAA,CAAAE,MAAA,aAAK;MAAAF,EAAA,CAAAG,YAAA,EAAS;MAChCH,EAAA,CAAAC,cAAA,kBAAmB;MAAAD,EAAA,CAAAE,MAAA,kBAAU;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACtCH,EAAA,CAAAC,cAAA,kBAAsB;MAAAD,EAAA,CAAAE,MAAA,aAAK;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACpCH,EAAA,CAAAC,cAAA,kBAAsB;MAAAD,EAAA,CAAAE,MAAA,aAAK;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACpCH,EAAA,CAAAC,cAAA,kBAAkB;MAAAD,EAAA,CAAAE,MAAA,eAAO;MAAAF,EAAA,CAAAG,YAAA,EAAS;MAClCH,EAAA,CAAAC,cAAA,kBAAkB;MAAAD,EAAA,CAAAE,MAAA,aAAK;MAAAF,EAAA,CAAAG,YAAA,EAAS;MAChCH,EAAA,CAAAC,cAAA,kBAAkB;MAAAD,EAAA,CAAAE,MAAA,eAAO;MAAAF,EAAA,CAAAG,YAAA,EAAS;MAClCH,EAAA,CAAAC,cAAA,kBAAkB;MAAAD,EAAA,CAAAE,MAAA,cAAM;MAGpCF,EAHoC,CAAAG,YAAA,EAAS,EAC5B,EACP,EACJ;MACNH,EAAA,CAAAC,cAAA,kBAAyC;MAAAD,EAAA,CAAAE,MAAA,mBAAW;MAGhEF,EAHgE,CAAAG,YAAA,EAAS,EAC1D,EACL,EACJ;;;;MA7EaH,EAAA,CAAAI,SAAA,IAAwB;MAAAJ,EAAxB,CAAAY,UAAA,SAAAuE,GAAA,CAAAtD,KAAA,CAAAoB,MAAA,KAAwB,aAAAqD,UAAA,CAAY;MA6BhBtG,EAAA,CAAAI,SAAA,GAAqD;MAArDJ,EAAA,CAAAqB,WAAA,YAAA8D,GAAA,CAAAlD,gBAAA,oBAAqD;MAO7BjC,EAAA,CAAAI,SAAA,IAA2B;MAA3BJ,EAAA,CAAAuG,gBAAA,YAAApB,GAAA,CAAAjD,OAAA,CAAAjB,KAAA,CAA2B;MAG7BjB,EAAA,CAAAI,SAAA,GAA0B;MAA1BJ,EAAA,CAAAuG,gBAAA,YAAApB,GAAA,CAAAjD,OAAA,CAAAhB,IAAA,CAA0B;MAKzBlB,EAAA,CAAAI,SAAA,GAAiC;MAAjCJ,EAAA,CAAAuG,gBAAA,YAAApB,GAAA,CAAAjD,OAAA,CAAAmC,WAAA,CAAiC;MAKpBrE,EAAA,CAAAI,SAAA,GAAgC;MAAhCJ,EAAA,CAAAuG,gBAAA,YAAApB,GAAA,CAAAjD,OAAA,CAAAV,UAAA,CAAgC;MAC/CxB,EAAA,CAAAI,SAAA,EAA+B;MAA/BJ,EAAA,CAAAuG,gBAAA,YAAApB,GAAA,CAAAjD,OAAA,CAAAT,SAAA,CAA+B;;;iBD7C/EpC,WAAW,EAAAmH,EAAA,CAAAC,0BAAA,EAAEtH,YAAY,EAAAuH,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAExH,WAAW,EAAAyH,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,uBAAA,EAAAH,EAAA,CAAAI,oBAAA,EAAAJ,EAAA,CAAAK,mBAAA,EAAAL,EAAA,CAAAM,0BAAA,EAAAN,EAAA,CAAAO,eAAA,EAAAP,EAAA,CAAAQ,oBAAA,EAAAR,EAAA,CAAAS,iBAAA,EAAAT,EAAA,CAAAU,YAAA,EAAAV,EAAA,CAAAW,OAAA,EAAAX,EAAA,CAAAY,MAAA,EAAEnI,YAAY,EAAAoI,EAAA,CAAAC,UAAA,EAAE5H,mBAAmB;EAAA6H,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}