﻿import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';

interface WeekDate {
  date: string; 
  day: number;
  is_today: boolean;
  is_selected: boolean;
  is_future: boolean;
  total_quests: number;
  completed_quests: number;
  completion_percentage: number;
}

@Component({
  selector: 'app-focus',
  templateUrl: './focus.page.html',
  styleUrls: ['./focus.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule]
})
export class FocusPage implements OnInit, OnDestroy {
  selectedDate: Date = new Date();
  weekDates: WeekDate[] = [];
  dayNames: string[] = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  headerText: string = 'Focus';

  focusTime = 25;
  breakTime = 5;
  longBreakTime = 15;
  sessionsBeforeLongBreak = 4;

  isRunning = false;
  isPaused = false;
  isBreak = false;
  isLongBreak = false;
  currentSession = 1;

  timerMinutes = 25;
  timerSeconds = 0;
  timerInterval: any = null;

  completedSessions = 0;
  totalFocusTime = 0;
  currentStreak = 0;

  constructor() {}

  ngOnInit() {
    this.loadSettings();

    this.loadStats();

    this.resetTimer();

    this.generateWeekDates();
  }

  ngOnDestroy() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }
  }

  loadSettings() {
    const settings = localStorage.getItem('focusSettings');
    if (settings) {
      const parsedSettings = JSON.parse(settings);
      this.focusTime = parsedSettings.focusTime || 25;
      this.breakTime = parsedSettings.breakTime || 5;
      this.longBreakTime = parsedSettings.longBreakTime || 15;
      this.sessionsBeforeLongBreak = parsedSettings.sessionsBeforeLongBreak || 4;
    }
  }

  saveSettings() {
    const settings = {
      focusTime: this.focusTime,
      breakTime: this.breakTime,
      longBreakTime: this.longBreakTime,
      sessionsBeforeLongBreak: this.sessionsBeforeLongBreak
    };

    localStorage.setItem('focusSettings', JSON.stringify(settings));

    this.resetTimer();
  }

  loadStats() {
    const stats = localStorage.getItem('focusStats');
    if (stats) {
      const parsedStats = JSON.parse(stats);
      this.completedSessions = parsedStats.completedSessions || 0;
      this.totalFocusTime = parsedStats.totalFocusTime || 0;
      this.currentStreak = parsedStats.currentStreak || 0;
    }
  }

  saveStats() {
    const stats = {
      completedSessions: this.completedSessions,
      totalFocusTime: this.totalFocusTime,
      currentStreak: this.currentStreak,
      lastSessionDate: new Date().toISOString().split('T')[0]
    };

    localStorage.setItem('focusStats', JSON.stringify(stats));
  }

  startTimer() {
    if (this.isRunning && !this.isPaused) return;

    if (this.isPaused) {
      this.isPaused = false;
    } else {
      this.isRunning = true;
    }

    this.timerInterval = setInterval(() => {
      if (this.timerSeconds === 0) {
        if (this.timerMinutes === 0) {
          this.timerCompleted();
        } else {
          this.timerMinutes--;
          this.timerSeconds = 59;
        }
      } else {
        this.timerSeconds--;
      }
    }, 1000);
  }

  pauseTimer() {
    if (!this.isRunning) return;

    this.isPaused = true;
    clearInterval(this.timerInterval);
  }

  resetTimer() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }

    this.isRunning = false;
    this.isPaused = false;

    if (this.isBreak) {
      if (this.isLongBreak) {
        this.timerMinutes = this.longBreakTime;
      } else {
        this.timerMinutes = this.breakTime;
      }
    } else {
      this.timerMinutes = this.focusTime;
    }

    this.timerSeconds = 0;
  }

  timerCompleted() {
    clearInterval(this.timerInterval);

    this.playNotificationSound();

    if (this.isBreak) {
      this.isBreak = false;
      this.timerMinutes = this.focusTime;
    } else {
      this.completedSessions++;
      this.totalFocusTime += this.focusTime;

      this.updateStreak();

      this.saveStats();

      if (this.currentSession % this.sessionsBeforeLongBreak === 0) {
        this.isLongBreak = true;
        this.timerMinutes = this.longBreakTime;
      } else {
        this.isLongBreak = false;
        this.timerMinutes = this.breakTime;
      }

      this.isBreak = true;
      this.currentSession++;
    }

    this.timerSeconds = 0;
    this.isRunning = false;
  }

  playNotificationSound() {
    const audio = new Audio('assets/sounds/notification.mp3');
    audio.play().catch(error => {
    });
  }

  updateStreak() {
    const stats = localStorage.getItem('focusStats');
    if (stats) {
      const parsedStats = JSON.parse(stats);
      const lastSessionDate = parsedStats.lastSessionDate;
      const today = new Date().toISOString().split('T')[0];
      const yesterday = new Date(Date.now() - 86400000).toISOString().split('T')[0];

      if (lastSessionDate === yesterday) {
        this.currentStreak++;
      } else if (lastSessionDate !== today) {
        this.currentStreak = 1;
      }
    } else {
      this.currentStreak = 1;
    }
  }

  generateWeekDates() {
    const today = new Date();
    const currentDay = today.getDay() || 7; 
    const startDate = new Date(today);
    startDate.setDate(today.getDate() - currentDay + 1); 

    this.weekDates = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);

      const dateString = this.formatDate(date);
      const isToday = this.isSameDay(date, today);
      const isSelected = this.isSameDay(date, this.selectedDate);
      const isFuture = date > today;

      this.weekDates.push({
        date: dateString,
        day: date.getDate(),
        is_today: isToday,
        is_selected: isSelected,
        is_future: isFuture,
        total_quests: 0,
        completed_quests: 0,
        completion_percentage: 0
      });
    }
  }

  selectDate(dateString: string) {
    const date = new Date(dateString);
    this.selectedDate = date;

    this.weekDates.forEach(weekDate => {
      weekDate.is_selected = weekDate.date === dateString;
    });

    this.updateHeaderText();
  }

  changeWeek(direction: number) {
    const firstDate = new Date(this.weekDates[0].date);
    firstDate.setDate(firstDate.getDate() + (direction * 7));

    const currentDay = firstDate.getDay() || 7; 
    const startDate = new Date(firstDate);
    startDate.setDate(firstDate.getDate() - currentDay + 1); 

    this.weekDates = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);

      const dateString = this.formatDate(date);
      const today = new Date();
      const isToday = this.isSameDay(date, today);
      const isSelected = this.isSameDay(date, this.selectedDate);
      const isFuture = date > today;

      this.weekDates.push({
        date: dateString,
        day: date.getDate(),
        is_today: isToday,
        is_selected: isSelected,
        is_future: isFuture,
        total_quests: 0,
        completed_quests: 0,
        completion_percentage: 0
      });
    }
  }

  updateHeaderText() {
    const today = new Date();
    if (this.isSameDay(this.selectedDate, today)) {
      this.headerText = 'Focus';
    } else {
      this.headerText = this.selectedDate.toLocaleDateString('en-US', {
        weekday: 'short',
        day: 'numeric',
        month: 'short'
      }) + ' - Focus';
    }
  }

  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  isSameDay(date1: Date, date2: Date): boolean {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  }
}
