<!-- User Badges Page -->
<ion-content [fullscreen]="true" [scrollY]="true">
  <div class="container">
    <header>
      <div class="logo">
        <img src="assets/images/upshift_icon_mini.svg" alt="Upshift">
        <span>Upshift</span>
      </div>
      <h1 *ngIf="isCurrentUser">My Badges</h1>
      <h1 *ngIf="!isCurrentUser && username">{{ username }}'s Badges</h1>
      <h1 *ngIf="!isCurrentUser && !username">User Badges</h1>
    </header>

    <div class="badges-container" *ngIf="userBadges">
      <a (click)="goBack()" class="back-button" style="cursor: pointer;">
        <span>←</span> Back
      </a>

      <!-- Title Badges Section -->
      <div class="badge-section">
        <h2 class="section-title">Titles</h2>
        <div class="badges-grid">
          <div *ngFor="let badge of titleBadges" class="badge-item title-badge" [class.locked]="!badge.unlocked">
            <div class="badge-icon">{{ badge.emoji }}</div>
            <div class="badge-name">{{ badge.name }}</div>
            <div class="badge-description">{{ badge.description }}</div>
            <div class="locked-overlay" *ngIf="!badge.unlocked">🔒</div>
          </div>
        </div>
      </div>

      <!-- Streak Badges Section -->
      <div class="badge-section">
        <h2 class="section-title">Streak</h2>
        <div class="badges-grid">
          <div *ngFor="let badge of streakBadges" class="badge-item streak-badge" [class.locked]="!badge.unlocked">
            <div class="badge-icon">🔥</div>
            <div class="badge-name">{{ badge.name }}</div>
            <div class="badge-description">{{ badge.description }}</div>
            <div class="locked-overlay" *ngIf="!badge.unlocked">🔒</div>
          </div>
        </div>
      </div>

      <!-- Side Quest Badges Section -->
      <div class="badge-section">
        <h2 class="section-title">Side Quests</h2>
        <div class="badges-grid">
          <div *ngFor="let badge of sidequestBadges" class="badge-item sidequest-badge" [class.locked]="!badge.unlocked">
            <div class="badge-icon">🎯</div>
            <div class="badge-name">{{ badge.name }}</div>
            <div class="badge-description">{{ badge.description }}</div>
            <div class="locked-overlay" *ngIf="!badge.unlocked">🔒</div>
          </div>
        </div>
      </div>

      <!-- Friend Badges Section -->
      <div class="badge-section">
        <h2 class="section-title">Friends</h2>
        <div class="badges-grid">
          <div *ngFor="let badge of friendBadges" class="badge-item friend-badge" [class.locked]="!badge.unlocked">
            <div class="badge-icon">👥</div>
            <div class="badge-name">{{ badge.name }}</div>
            <div class="badge-description">{{ badge.description }}</div>
            <div class="locked-overlay" *ngIf="!badge.unlocked">🔒</div>
          </div>
        </div>
      </div>

      <!-- Category Badges Section -->
      <div class="badge-section">
        <h2 class="section-title">Categories</h2>
        <div class="badges-grid">
          <div *ngFor="let badge of categoryBadges" class="badge-item category-badge" [class.locked]="!badge.unlocked">
            <div class="badge-icon">
              <ng-container [ngSwitch]="true">
                <ng-container *ngSwitchCase="badge.name.includes('First Rep')">💪</ng-container>
                <ng-container *ngSwitchCase="badge.name.includes('Cha-Ching')">💰</ng-container>
                <ng-container *ngSwitchCase="badge.name.includes('Vital Spark')">❤️</ng-container>
                <ng-container *ngSwitchCase="badge.name.includes('Smart Start')">📚</ng-container>
              </ng-container>
            </div>
            <div class="badge-name">{{ badge.name }}</div>
            <div class="badge-description">{{ badge.description }}</div>
            <div class="locked-overlay" *ngIf="!badge.unlocked">🔒</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading state -->
    <div class="loading-container" *ngIf="!userBadges">
      <div class="loading-spinner"></div>
      <p>Loading badges...</p>
    </div>
  </div>
</ion-content>
