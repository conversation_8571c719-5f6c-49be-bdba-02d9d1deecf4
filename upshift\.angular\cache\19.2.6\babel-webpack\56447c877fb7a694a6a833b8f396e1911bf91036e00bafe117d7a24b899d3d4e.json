{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _PreferencesService;\nimport { Preferences as CapacitorPreferences } from '@capacitor/preferences';\nimport * as i0 from \"@angular/core\";\nexport class PreferencesService {\n  constructor() {\n    this.useLocalStorage = false;\n    this.checkCapacitorAvailability();\n  }\n  checkCapacitorAvailability() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield CapacitorPreferences.get({\n          key: 'test_key'\n        });\n        _this.useLocalStorage = false;\n      } catch (error) {\n        _this.useLocalStorage = true;\n      }\n    })();\n  }\n  get(key) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (_this2.useLocalStorage) {\n          const value = localStorage.getItem(key);\n          return {\n            value\n          };\n        } else {\n          return yield CapacitorPreferences.get({\n            key\n          });\n        }\n      } catch (error) {\n        const value = localStorage.getItem(key);\n        return {\n          value\n        };\n      }\n    })();\n  }\n  set(key, value) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (_this3.useLocalStorage) {\n          localStorage.setItem(key, value);\n        } else {\n          yield CapacitorPreferences.set({\n            key,\n            value\n          });\n        }\n      } catch (error) {\n        localStorage.setItem(key, value);\n      }\n    })();\n  }\n  remove(key) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (_this4.useLocalStorage) {\n          localStorage.removeItem(key);\n        } else {\n          yield CapacitorPreferences.remove({\n            key\n          });\n        }\n      } catch (error) {\n        localStorage.removeItem(key);\n      }\n    })();\n  }\n  clear() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (_this5.useLocalStorage) {\n          localStorage.clear();\n        } else {\n          yield CapacitorPreferences.clear();\n        }\n      } catch (error) {\n        localStorage.clear();\n      }\n    })();\n  }\n}\n_PreferencesService = PreferencesService;\n_PreferencesService.ɵfac = function PreferencesService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _PreferencesService)();\n};\n_PreferencesService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _PreferencesService,\n  factory: _PreferencesService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "names": ["Preferences", "CapacitorPreferences", "PreferencesService", "constructor", "useLocalStorage", "checkCapacitorAvailability", "_this", "_asyncToGenerator", "get", "key", "error", "_this2", "value", "localStorage", "getItem", "set", "_this3", "setItem", "remove", "_this4", "removeItem", "clear", "_this5", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\services\\preferences.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Preferences as CapacitorPreferences } from '@capacitor/preferences';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class PreferencesService {\r\n  private useLocalStorage = false;\r\n\r\n  constructor() {\r\n    this.checkCapacitorAvailability();\r\n  }\r\n\r\n  private async checkCapacitorAvailability(): Promise<void> {\r\n    try {\r\n      await CapacitorPreferences.get({ key: 'test_key' });\r\n      this.useLocalStorage = false;\r\n    } catch (error) {\r\n      this.useLocalStorage = true;\r\n    }\r\n  }\r\n\r\n  async get(key: string): Promise<{ value: string | null }> {\r\n    try {\r\n      if (this.useLocalStorage) {\r\n        const value = localStorage.getItem(key);\r\n        return { value };\r\n      } else {\r\n        return await CapacitorPreferences.get({ key });\r\n      }\r\n    } catch (error) {\r\n      const value = localStorage.getItem(key);\r\n      return { value };\r\n    }\r\n  }\r\n\r\n  async set(key: string, value: string): Promise<void> {\r\n    try {\r\n      if (this.useLocalStorage) {\r\n        localStorage.setItem(key, value);\r\n      } else {\r\n        await CapacitorPreferences.set({ key, value });\r\n      }\r\n    } catch (error) {\r\n      localStorage.setItem(key, value);\r\n    }\r\n  }\r\n\r\n  async remove(key: string): Promise<void> {\r\n    try {\r\n      if (this.useLocalStorage) {\r\n        localStorage.removeItem(key);\r\n      } else {\r\n        await CapacitorPreferences.remove({ key });\r\n      }\r\n    } catch (error) {\r\n      localStorage.removeItem(key);\r\n    }\r\n  }\r\n\r\n  async clear(): Promise<void> {\r\n    try {\r\n      if (this.useLocalStorage) {\r\n        localStorage.clear();\r\n      } else {\r\n        await CapacitorPreferences.clear();\r\n      }\r\n    } catch (error) {\r\n      localStorage.clear();\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;AACA,SAASA,WAAW,IAAIC,oBAAoB,QAAQ,wBAAwB;;AAK5E,OAAM,MAAOC,kBAAkB;EAG7BC,YAAA;IAFQ,KAAAC,eAAe,GAAG,KAAK;IAG7B,IAAI,CAACC,0BAA0B,EAAE;EACnC;EAEcA,0BAA0BA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtC,IAAI;QACF,MAAMN,oBAAoB,CAACO,GAAG,CAAC;UAAEC,GAAG,EAAE;QAAU,CAAE,CAAC;QACnDH,KAAI,CAACF,eAAe,GAAG,KAAK;MAC9B,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdJ,KAAI,CAACF,eAAe,GAAG,IAAI;MAC7B;IAAC;EACH;EAEMI,GAAGA,CAACC,GAAW;IAAA,IAAAE,MAAA;IAAA,OAAAJ,iBAAA;MACnB,IAAI;QACF,IAAII,MAAI,CAACP,eAAe,EAAE;UACxB,MAAMQ,KAAK,GAAGC,YAAY,CAACC,OAAO,CAACL,GAAG,CAAC;UACvC,OAAO;YAAEG;UAAK,CAAE;QAClB,CAAC,MAAM;UACL,aAAaX,oBAAoB,CAACO,GAAG,CAAC;YAAEC;UAAG,CAAE,CAAC;QAChD;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACd,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAACL,GAAG,CAAC;QACvC,OAAO;UAAEG;QAAK,CAAE;MAClB;IAAC;EACH;EAEMG,GAAGA,CAACN,GAAW,EAAEG,KAAa;IAAA,IAAAI,MAAA;IAAA,OAAAT,iBAAA;MAClC,IAAI;QACF,IAAIS,MAAI,CAACZ,eAAe,EAAE;UACxBS,YAAY,CAACI,OAAO,CAACR,GAAG,EAAEG,KAAK,CAAC;QAClC,CAAC,MAAM;UACL,MAAMX,oBAAoB,CAACc,GAAG,CAAC;YAAEN,GAAG;YAAEG;UAAK,CAAE,CAAC;QAChD;MACF,CAAC,CAAC,OAAOF,KAAK,EAAE;QACdG,YAAY,CAACI,OAAO,CAACR,GAAG,EAAEG,KAAK,CAAC;MAClC;IAAC;EACH;EAEMM,MAAMA,CAACT,GAAW;IAAA,IAAAU,MAAA;IAAA,OAAAZ,iBAAA;MACtB,IAAI;QACF,IAAIY,MAAI,CAACf,eAAe,EAAE;UACxBS,YAAY,CAACO,UAAU,CAACX,GAAG,CAAC;QAC9B,CAAC,MAAM;UACL,MAAMR,oBAAoB,CAACiB,MAAM,CAAC;YAAET;UAAG,CAAE,CAAC;QAC5C;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdG,YAAY,CAACO,UAAU,CAACX,GAAG,CAAC;MAC9B;IAAC;EACH;EAEMY,KAAKA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAf,iBAAA;MACT,IAAI;QACF,IAAIe,MAAI,CAAClB,eAAe,EAAE;UACxBS,YAAY,CAACQ,KAAK,EAAE;QACtB,CAAC,MAAM;UACL,MAAMpB,oBAAoB,CAACoB,KAAK,EAAE;QACpC;MACF,CAAC,CAAC,OAAOX,KAAK,EAAE;QACdG,YAAY,CAACQ,KAAK,EAAE;MACtB;IAAC;EACH;;sBAhEWnB,kBAAkB;;mCAAlBA,mBAAkB;AAAA;;SAAlBA,mBAAkB;EAAAqB,OAAA,EAAlBrB,mBAAkB,CAAAsB,IAAA;EAAAC,UAAA,EAFjB;AAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}