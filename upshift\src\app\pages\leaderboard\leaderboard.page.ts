﻿import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';
import { UserService } from '../../services/user.service';
import { GroupService } from '../../services/group.service';
import { FriendService } from '../../services/friend.service';
import { User } from '../../models/user.model';
import { Group } from '../../models/group.model';
import { Subscription, of, switchMap, take, map } from 'rxjs';
import { SupabaseService } from '../../services/supabase.service';

@Component({
  selector: 'app-leaderboard',
  templateUrl: './leaderboard.page.html',
  styleUrls: ['./leaderboard.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, RouterModule]
})
export class LeaderboardPage implements OnInit {
  activeTab: 'groups' | 'users' = 'groups';

  topGroups: Group[] = [];
  topUsers: User[] = [];
  currentUserId: string | null = null;

  groupsSubscription: Subscription | null = null;
  usersSubscription: Subscription | null = null;

  private supabaseService = inject(SupabaseService);
  private userService = inject(UserService);
  private groupService = inject(GroupService);
  private friendService = inject(FriendService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);

  constructor() {}

  ngOnInit() {

    this.supabaseService.currentUser$.subscribe(user => {
      if (user) {
        this.currentUserId = user.id;
      }
    });

    const url = this.router.url;

    if (url.includes('/leaderboard/users')) {
      this.activeTab = 'users';
    } else {
      this.activeTab = 'groups';
    }


    this.loadTopGroups();
    this.loadTopUsers();
  }

  ngOnDestroy() {
    if (this.groupsSubscription) {
      this.groupsSubscription.unsubscribe();
    }

    if (this.usersSubscription) {
      this.usersSubscription.unsubscribe();
    }
  }

  setActiveTab(tab: 'groups' | 'users') {
    this.activeTab = tab;

    window.location.href = `/leaderboard/${tab}`;
  }

  loadTopGroups() {
    this.groupsSubscription = this.groupService.getTopGroups(10).subscribe({
      next: (groups) => {
        this.topGroups = groups;
      },
      error: (error) => {
      },
      complete: () => {
      }
    });
  }

  loadTopUsers() {
    this.usersSubscription = this.userService.getTopUsers(10).subscribe({
      next: (users) => {
        this.topUsers = users;
      },
      error: (error) => {
      },
      complete: () => {
      }
    });
  }

  viewUserProfile(userId: string | undefined) {
    if (!userId) return;

    if (userId === this.currentUserId) {
      return;
    }

    this.friendService.getFriends(this.currentUserId || '').pipe(
      take(1),
      map(friends => {
        const isFriend = friends.some(friend =>
          (friend.user_id === this.currentUserId && friend.friend_id === userId) ||
          (friend.user_id === userId && friend.friend_id === this.currentUserId)
        );

        if (isFriend) {
          this.router.navigate(['/friends', userId]);
        } else {
          this.router.navigate(['/user-profile', userId]);
        }
      })
    ).subscribe();
  }

  goBackToGroups() {
    window.location.href = '/groups';
  }
}
