{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\models\\goal.model.ts"], "sourcesContent": ["export type GoalUnit = 'count' | 'steps' | 'm' | 'km' | 'sec' | 'min' | 'hr' | 'Cal' | 'g' | 'mg' | 'drink' | 'pages' | 'books' | '%' | '€' | '$' | '£' | 'days' | 'weeks' | 'months' | 'years';\r\n\r\nexport interface Goal {\r\n  id?: string; \n  user_id: string;\r\n  name: string;\r\n  description: string;\r\n  emoji: string;\r\n  start_date: Date;\r\n  end_date?: Date;\r\n  \r\n  goal_value: number;\r\n  current_value: number;\r\n  goal_unit: GoalUnit;\r\n  \r\n  before_photo?: string;\r\n  after_photo?: string;\r\n}\r\n\r\nexport interface MicroGoal {\r\n  id?: string; \n  goal_id: string;\r\n  title: string;\r\n  completed: boolean;\r\n  completed_at?: Date;\r\n}\r\n\r\nexport interface GoalJournalEntry {\r\n  id?: string; \n  goal_id: string;\r\n  milestone_percentage: number;\r\n  content: string;\r\n  created_at: Date;\r\n}\r\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}