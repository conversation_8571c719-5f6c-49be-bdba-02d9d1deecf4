{"ast": null, "code": "var _EmojiInputDirective;\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nexport class EmojiInputDirective {\n  constructor(el, ngControl) {\n    this.el = el;\n    this.ngControl = ngControl;\n    this.isProcessing = false;\n  }\n  onInput(event) {\n    if (this.isProcessing) {\n      return;\n    }\n    this.isProcessing = true;\n    try {\n      const input = event.target;\n      const value = input.value;\n      const emojiRegex = /[\\xA9\\xAE\\u203C\\u2049\\u2122\\u2139\\u2194-\\u2199\\u21A9\\u21AA\\u231A\\u231B\\u2328\\u2388\\u23CF\\u23E9-\\u23F3\\u23F8-\\u23FA\\u24C2\\u25AA\\u25AB\\u25B6\\u25C0\\u25FB-\\u25FE\\u2600-\\u2605\\u2607-\\u2612\\u2614-\\u2685\\u2690-\\u2705\\u2708-\\u2712\\u2714\\u2716\\u271D\\u2721\\u2728\\u2733\\u2734\\u2744\\u2747\\u274C\\u274E\\u2753-\\u2755\\u2757\\u2763-\\u2767\\u2795-\\u2797\\u27A1\\u27B0\\u27BF\\u2934\\u2935\\u2B05-\\u2B07\\u2B1B\\u2B1C\\u2B50\\u2B55\\u3030\\u303D\\u3297\\u3299\\u{1F000}-\\u{1F0FF}\\u{1F10D}-\\u{1F10F}\\u{1F12F}\\u{1F16C}-\\u{1F171}\\u{1F17E}\\u{1F17F}\\u{1F18E}\\u{1F191}-\\u{1F19A}\\u{1F1AD}-\\u{1F1E5}\\u{1F201}-\\u{1F20F}\\u{1F21A}\\u{1F22F}\\u{1F232}-\\u{1F23A}\\u{1F23C}-\\u{1F23F}\\u{1F249}-\\u{1F3FA}\\u{1F400}-\\u{1F53D}\\u{1F546}-\\u{1F64F}\\u{1F680}-\\u{1F6FF}\\u{1F774}-\\u{1F77F}\\u{1F7D5}-\\u{1F7FF}\\u{1F80C}-\\u{1F80F}\\u{1F848}-\\u{1F84F}\\u{1F85A}-\\u{1F85F}\\u{1F888}-\\u{1F88F}\\u{1F8AE}-\\u{1F8FF}\\u{1F90C}-\\u{1F93A}\\u{1F93C}-\\u{1F945}\\u{1F947}-\\u{1FAFF}\\u{1FC00}-\\u{1FFFD}]/u;\n      const graphemes = [...value];\n      const lastEmoji = graphemes.reverse().find(char => emojiRegex.test(char));\n      const newValue = lastEmoji ? lastEmoji : '';\n      input.value = newValue;\n      if (this.ngControl && this.ngControl.control) {\n        this.ngControl.control.setValue(newValue, {\n          emitEvent: false\n        });\n        this.ngControl.control.markAsDirty();\n        this.ngControl.control.markAsTouched();\n        this.ngControl.control.updateValueAndValidity();\n      }\n    } finally {\n      this.isProcessing = false;\n    }\n  }\n}\n_EmojiInputDirective = EmojiInputDirective;\n_EmojiInputDirective.ɵfac = function EmojiInputDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _EmojiInputDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.NgControl, 8));\n};\n_EmojiInputDirective.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n  type: _EmojiInputDirective,\n  selectors: [[\"\", \"appEmojiInput\", \"\"]],\n  hostBindings: function EmojiInputDirective_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"input\", function EmojiInputDirective_input_HostBindingHandler($event) {\n        return ctx.onInput($event);\n      });\n    }\n  }\n});", "map": {"version": 3, "names": ["EmojiInputDirective", "constructor", "el", "ngControl", "isProcessing", "onInput", "event", "input", "target", "value", "emojiRegex", "graphemes", "<PERSON><PERSON><PERSON><PERSON>", "reverse", "find", "char", "test", "newValue", "control", "setValue", "emitEvent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>ched", "updateValueAndValidity", "i0", "ɵɵdirectiveInject", "ElementRef", "i1", "NgControl", "selectors", "hostBindings", "EmojiInputDirective_HostBindings", "rf", "ctx", "ɵɵlistener", "EmojiInputDirective_input_HostBindingHandler", "$event"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\directives\\emoji-input.directive.ts"], "sourcesContent": ["import { Directive, ElementRef, HostListener, Optional } from '@angular/core';\r\nimport { NgControl } from '@angular/forms';\r\n\r\n@Directive({\r\n  selector: '[appEmojiInput]',\r\n  standalone: true\r\n})\r\nexport class EmojiInputDirective {\r\n  constructor(\r\n    private el: ElementRef,\r\n    @Optional() private ngControl: NgControl\r\n  ) {}\r\n\r\n  private isProcessing = false;\r\n\r\n  @HostListener('input', ['$event'])\r\n  onInput(event: Event) {\r\n    if (this.isProcessing) {\r\n      return;\r\n    }\r\n\r\n    this.isProcessing = true;\r\n\r\n    try {\r\n      const input = event.target as HTMLInputElement;\r\n      const value = input.value;\r\n\r\n      const emojiRegex = /\\p{Extended_Pictographic}/u;\r\n      const graphemes = [...value];\r\n\r\n      const lastEmoji = graphemes.reverse().find(char => emojiRegex.test(char));\r\n\r\n      const newValue = lastEmoji ? lastEmoji : '';\r\n\r\n      input.value = newValue;\r\n\r\n      if (this.ngControl && this.ngControl.control) {\r\n        this.ngControl.control.setValue(newValue, { emitEvent: false });\r\n\r\n        this.ngControl.control.markAsDirty();\r\n        this.ngControl.control.markAsTouched();\r\n\r\n        this.ngControl.control.updateValueAndValidity();\r\n      }\r\n\r\n    } finally {\r\n      this.isProcessing = false;\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;AAOA,OAAM,MAAOA,mBAAmB;EAC9BC,YACUC,EAAc,EACFC,SAAoB;IADhC,KAAAD,EAAE,GAAFA,EAAE;IACU,KAAAC,SAAS,GAATA,SAAS;IAGvB,KAAAC,YAAY,GAAG,KAAK;EAFzB;EAKHC,OAAOA,CAACC,KAAY;IAClB,IAAI,IAAI,CAACF,YAAY,EAAE;MACrB;IACF;IAEA,IAAI,CAACA,YAAY,GAAG,IAAI;IAExB,IAAI;MACF,MAAMG,KAAK,GAAGD,KAAK,CAACE,MAA0B;MAC9C,MAAMC,KAAK,GAAGF,KAAK,CAACE,KAAK;MAEzB,MAAMC,UAAU,GAAG,u5BAA4B;MAC/C,MAAMC,SAAS,GAAG,CAAC,GAAGF,KAAK,CAAC;MAE5B,MAAMG,SAAS,GAAGD,SAAS,CAACE,OAAO,EAAE,CAACC,IAAI,CAACC,IAAI,IAAIL,UAAU,CAACM,IAAI,CAACD,IAAI,CAAC,CAAC;MAEzE,MAAME,QAAQ,GAAGL,SAAS,GAAGA,SAAS,GAAG,EAAE;MAE3CL,KAAK,CAACE,KAAK,GAAGQ,QAAQ;MAEtB,IAAI,IAAI,CAACd,SAAS,IAAI,IAAI,CAACA,SAAS,CAACe,OAAO,EAAE;QAC5C,IAAI,CAACf,SAAS,CAACe,OAAO,CAACC,QAAQ,CAACF,QAAQ,EAAE;UAAEG,SAAS,EAAE;QAAK,CAAE,CAAC;QAE/D,IAAI,CAACjB,SAAS,CAACe,OAAO,CAACG,WAAW,EAAE;QACpC,IAAI,CAAClB,SAAS,CAACe,OAAO,CAACI,aAAa,EAAE;QAEtC,IAAI,CAACnB,SAAS,CAACe,OAAO,CAACK,sBAAsB,EAAE;MACjD;IAEF,CAAC,SAAS;MACR,IAAI,CAACnB,YAAY,GAAG,KAAK;IAC3B;EACF;;uBAzCWJ,mBAAmB;;mCAAnBA,oBAAmB,EAAAwB,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,UAAA,GAAAF,EAAA,CAAAC,iBAAA,CAAAE,EAAA,CAAAC,SAAA;AAAA;;QAAnB5B,oBAAmB;EAAA6B,SAAA;EAAAC,YAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAnBR,EAAA,CAAAU,UAAA,mBAAAC,6CAAAC,MAAA;QAAA,OAAAH,GAAA,CAAA5B,OAAA,CAAA+B,MAAA,CAAe;MAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}