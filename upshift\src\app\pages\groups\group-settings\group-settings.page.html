﻿<div class="settings-container">
  <div class="settings-header">
    <a [routerLink]="['/groups', groupId]" class="back-link">
      <span class="back-arrow">←</span> Back to group
    </a>
    <h1>Settings</h1>
    <div class="group-level-badge" *ngIf="group">Level {{ group.level }}</div>
  </div>

  <!-- Messages -->
  <div class="messages" *ngIf="successMessage || errorMessage">
    <div class="message success" *ngIf="successMessage">{{ successMessage }}</div>
    <div class="message error" *ngIf="errorMessage">{{ errorMessage }}</div>
  </div>

  <!-- XP Section -->
  <div class="settings-section" *ngIf="group">
    <div class="section-header">
      <span class="section-icon">📊</span>
      <h2>Group XP</h2>
    </div>
    <div class="xp-section">
      <div class="category-card">
        <div class="category-header">
          <div class="category-icon">💪</div>
          <div class="category-name">Strength</div>
        </div>
        <div class="xp-progress">
          <div class="xp-value">{{ group.strength_xp }}/{{ requiredXp }} XP</div>
          <div class="progress-bar">
            <div class="progress-fill" [style.width.%]="getProgressPercentage(group.strength_xp)"></div>
          </div>
        </div>
      </div>

      <div class="category-card">
        <div class="category-header">
          <div class="category-icon">💰</div>
          <div class="category-name">Money</div>
        </div>
        <div class="xp-progress">
          <div class="xp-value">{{ group.money_xp }}/{{ requiredXp }} XP</div>
          <div class="progress-bar">
            <div class="progress-fill" [style.width.%]="getProgressPercentage(group.money_xp)"></div>
          </div>
        </div>
      </div>

      <div class="category-card">
        <div class="category-header">
          <div class="category-icon">❤️</div>
          <div class="category-name">Health</div>
        </div>
        <div class="xp-progress">
          <div class="xp-value">{{ group.health_xp }}/{{ requiredXp }} XP</div>
          <div class="progress-bar">
            <div class="progress-fill" [style.width.%]="getProgressPercentage(group.health_xp)"></div>
          </div>
        </div>
      </div>

      <div class="category-card">
        <div class="category-header">
          <div class="category-icon">🧠</div>
          <div class="category-name">Knowledge</div>
        </div>
        <div class="xp-progress">
          <div class="xp-value">{{ group.knowledge_xp }}/{{ requiredXp }} XP</div>
          <div class="progress-bar">
            <div class="progress-fill" [style.width.%]="getProgressPercentage(group.knowledge_xp)"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Group Information Section (Admin only) -->
  <div class="settings-section" *ngIf="isAdmin && group">
    <div class="section-header">
      <span class="section-icon">✏️</span>
      <h2>Group Information</h2>
    </div>
    <div class="group-info-section">
      <form (ngSubmit)="updateGroupInfo()" id="edit-group-form">
        <div style="display: flex;gap: 10px;">
          <div class="form-group">
            <input type="text" name="emoji" id="emoji" [(ngModel)]="groupInfo.emoji" appEmojiInput>
          </div>
          <div class="form-group" style="width: 100%;">
            <input type="text" name="name" id="name" [(ngModel)]="groupInfo.name" (input)="checkGroupName()" required>
            <div *ngIf="nameError" class="error-message">{{ nameError }}</div>
            <div *ngIf="nameChecking" class="checking-message">Checking availability...</div>
            <div *ngIf="nameAvailable" class="success-message">Group name is available!</div>
          </div>
          <button type="submit" class="save-btn">Save</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Members Section -->
  <div class="settings-section" *ngIf="members.length > 0">
    <div class="section-header">
      <span class="section-icon">👥</span>
      <h2>Members</h2>
    </div>
    <div class="members-list">
      <div class="member-item" *ngFor="let member of members">
        <div class="member-info">
          <ng-container *ngIf="member.user_id !== userId">
            <a (click)="navigateToProfile(member.user_id)" class="member-avatar-link" style="cursor: pointer;">
              <div class="member-avatar">
                <div class="default-avatar" *ngIf="!member.profile_picture">{{ member.nickname.charAt(0).toUpperCase() }}</div>
                <img *ngIf="member.profile_picture" [src]="member.profile_picture" [alt]="member.nickname">
              </div>
            </a>
          </ng-container>
          <ng-container *ngIf="member.user_id === userId">
            <div class="member-avatar">
              <div class="default-avatar" *ngIf="!member.profile_picture">{{ member.nickname.charAt(0).toUpperCase() }}</div>
              <img *ngIf="member.profile_picture" [src]="member.profile_picture" [alt]="member.nickname">
            </div>
          </ng-container>
          <div class="member-details">
            <ng-container *ngIf="member.user_id !== userId">
              <a (click)="navigateToProfile(member.user_id)" class="member-name-link" style="cursor: pointer;">
                <span class="member-name">{{ member.nickname }}</span>
              </a>
            </ng-container>
            <ng-container *ngIf="member.user_id === userId">
              <span class="member-name">{{ member.nickname }}</span>
            </ng-container>
            <!-- <span class="member-username" *ngIf="member.username && member.username !== member.nickname">({{ member.username }})</span> -->
            <span class="admin-badge" *ngIf="member.is_admin">Admin</span>
            <span class="member-joined">Joined {{ member.joined_date | date:'MMM d, y' }}</span>
          </div>
        </div>
        <div class="member-actions">
          <button *ngIf="member.user_id === userId" class="leave-btn" (click)="leaveGroup()">Leave Group</button>
          <ng-container *ngIf="isAdmin && member.user_id !== userId">
            <button class="edit-nickname-btn" (click)="editMemberNickname(member)">Edit</button>
            <button class="remove-btn" (click)="removeMember(member)">Remove</button>
          </ng-container>
        </div>
      </div>
    </div>
  </div>

  <!-- Invite Section (Admin only) -->
  <div class="settings-section" *ngIf="isAdmin">
    <div class="section-header">
      <span class="section-icon">➕</span>
      <h2>Invite</h2>
    </div>
    <div class="invite-section">
      <div class="invite-tabs">
        <button class="invite-tab" [class.active]="activeTab === 'friends'" (click)="setActiveTab('friends')">Invite Friends</button>
        <button class="invite-tab" [class.active]="activeTab === 'code'" (click)="setActiveTab('code')">Generate Code</button>
      </div>

      <div class="invite-tab-content" id="friends-tab" *ngIf="activeTab === 'friends'">
        <p>Invite your friends to join this group:</p>
        <form (ngSubmit)="inviteFriend()" id="invite-form" #inviteForm="ngForm">
          <div class="invite-input-container">
            <input
              type="text"
              name="username"
              id="username"
              [(ngModel)]="inviteUsername"
              (input)="onUsernameInput()"
              placeholder="Username to invite"
              pattern="^[\w.@+-]+$"
              title="Username should only contain letters, numbers, and &#64;/./+/-/_ characters"
              required
              #usernameInput="ngModel"
              autocomplete="off">
            <div id="username-suggestions" class="username-suggestions" *ngIf="usernameSuggestions.length > 0">
              <div class="suggestion-item" *ngFor="let suggestion of usernameSuggestions" (click)="selectUsername(suggestion)">
                {{ suggestion }}
              </div>
            </div>
            <div *ngIf="(usernameInput.invalid && (usernameInput.dirty || usernameInput.touched)) ||
                     (usernameInput.valid && inviteUsername.trim() &&
                      (alreadyInvitedUsernames.includes(inviteUsername.trim()) ||
                       !validFriendUsernames.includes(inviteUsername.trim())))"
                 class="validation-error">
              <div *ngIf="usernameInput.errors?.['pattern']">
                Username should only contain letters, numbers, and &#64;/./+/-/_ characters
              </div>
              <div *ngIf="usernameInput.errors?.['required']">
                Username is required
              </div>
              <div *ngIf="usernameInput.valid && inviteUsername.trim() && alreadyInvitedUsernames.includes(inviteUsername.trim())">
                An invitation has already been sent to this user
              </div>
              <div *ngIf="usernameInput.valid && inviteUsername.trim() &&
                          !alreadyInvitedUsernames.includes(inviteUsername.trim()) &&
                          !validFriendUsernames.includes(inviteUsername.trim())">
                You can only invite users from your friends list
              </div>
            </div>
          </div>
          <button type="submit" class="invite-btn" [disabled]="inviteForm.invalid ||
                                                  (inviteUsername.trim() &&
                                                   (alreadyInvitedUsernames.includes(inviteUsername.trim()) ||
                                                    !validFriendUsernames.includes(inviteUsername.trim())))">Invite Friend</button>
        </form>
        <div *ngIf="invitationSentMessage" class="invitation-sent-message">
          <div class="message success">{{ invitationSentMessage }}</div>
        </div>
        <div class="invite-note">Note: You can only invite users from your friends list.</div>
      </div>

      <div class="invite-tab-content" id="code-tab" *ngIf="activeTab === 'code'">
        <div class="code-header">
          <div class="section-header">
            <span class="section-icon">🔑</span>
            <h3 style="margin: 0; font-size: 18px;">Group Code</h3>
          </div>
          <p>Generate a code that anyone can use to join this group. The code will be valid for 24 hours.</p>
        </div>
        <div class="code-actions" style="display: flex; flex-direction: column; gap: 15px;">
          <ng-container *ngIf="invitationCode && codeIsValid; else generateCodeBlock">
            <div class="code-display">{{ invitationCode }}</div>
            <div class="code-info">This code is valid for 24 hours. Share it with friends to connect.</div>
          </ng-container>
          <ng-template #generateCodeBlock>
            <button (click)="generateInvitationCode()" class="invite-btn" style="width: 100%;">Generate Group Code</button>
          </ng-template>
        </div>
      </div>
    </div>
  </div>

  <!-- Side Quest Settings (Admin only) -->
  <div class="settings-section" *ngIf="isAdmin && group">
    <div class="section-header">
      <span class="section-icon">⚙️</span>
      <h2>Side Quest Settings</h2>
    </div>
    <form (ngSubmit)="updateSideQuestSettings()" class="sidequest-settings-form">
      <div class="setting-item">
        <label class="toggle-switch">
          <input type="checkbox" name="enable_sidequests" id="enable_sidequests" [(ngModel)]="group.enable_sidequests">
          <span class="toggle-slider"></span>
        </label>
        <span class="setting-label">Enable Daily Side Quests</span>
      </div>
      <button type="submit" class="save-settings-btn">Save Settings</button>
    </form>
  </div>

  <!-- Delete Group Section (Admin only) -->
  <div class="settings-section danger-section" *ngIf="isAdmin">
    <div class="section-header">
      <span class="section-icon">⚠️</span>
      <h2>Danger Zone</h2>
    </div>
    <div class="danger-actions">
      <button class="delete-btn" (click)="confirmDeleteGroup()">Delete Group</button>
      <div class="danger-note">This action cannot be undone. All group data will be permanently deleted.</div>
    </div>
  </div>
</div>

<!-- Edit Nickname Modal -->
<div id="edit-nickname-modal" class="modal" [style.display]="showEditNicknameModal ? 'block' : 'none'">
  <div class="modal-content">
    <span class="close-modal" (click)="showEditNicknameModal = false">&times;</span>
    <h3>Edit Nickname</h3>
    <form (ngSubmit)="updateMemberNickname()">
      <input type="text" id="edit-nickname-input" name="nickname" [(ngModel)]="editingMember.nickname" required>
      <button type="submit" class="save-nickname-btn">Save</button>
    </form>
  </div>
</div>
