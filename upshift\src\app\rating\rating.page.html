<ion-content [fullscreen]="true" class="ion-padding">
  <div class="background-container">
    <div class="gradient-bg"></div>
    <div class="celestial-body"></div>
  </div>
  <ion-grid>
    <ion-row>
      <ion-col>
        <h1 class="upshift-title ion-text-center">
          <span class="gradient-text">Upshift</span> was made for people like
          <span class="gradient-text">you!</span>
        </h1>
      </ion-col>
    </ion-row>
    <ion-row class="users-circle-container">
      <ion-col
        *ngFor="let user of fakeUsers; let i = index"
        [ngClass]="{
          'user-col': true,
          'middle': i === 1,
          'first': i === 0,
          'last': i === 2
        }"
        size="auto">
        <ion-avatar class="user-circle">
          <ion-img [src]="user.picture"></ion-img>
        </ion-avatar>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col size="12" class="ion-text-center">
        <ion-text class="users-count">+2M Upshift users</ion-text>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col size="12" *ngFor="let testimonial of fakeTestimonials">
        <ion-card class="default-card testimonial">
          <ion-card-header>
            <div class="header-content">
              <ion-avatar >
                <ion-img [src]="testimonial.profilePic"></ion-img>
              </ion-avatar>
              <ion-label class="user-info">
                <h3>{{ testimonial.name }}</h3>
                <div class="star-rating">
                  <ion-icon *ngFor="let star of [1,2,3,4,5]" name="star" class="gradient-star"></ion-icon>
                </div>
              </ion-label>
            </div>
          </ion-card-header>
          <ion-card-content>
            <ion-text class="dark-text" style="font-size: 14px">
              {{ testimonial.text }}
            </ion-text>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>

<ion-footer>
  <ion-button class="blue-button">Continue</ion-button>
</ion-footer>
