﻿import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { User } from '../../models/user.model';

interface Particle {
  style: {
    left: string;
    top: string;
    width: string;
    height: string;
    backgroundColor: string;
    animation: string;
    '--tx': string;
    '--ty': string;
  };
}

interface Hexagon {
  style: {
    left: string;
    top: string;
    animation?: string; 
  };
}

@Component({
  selector: 'app-celebration',
  templateUrl: './celebration.component.html',
  styleUrls: ['./celebration.component.scss'],
  standalone: true,
  imports: [CommonModule]
})
export class CelebrationComponent implements OnInit {
  @Input() user: User | null = null;
  @Input() date: string = '';
  @Output() close = new EventEmitter<void>();

  particles: Particle[] = [];
  hexagons: Hexagon[] = [];

  constructor() { }

  ngOnInit() {
    this.createParticles();

    this.createHexagonGrid();

    this.playAchievementSound();

    setTimeout(() => {
      this.closeModal();
    }, 15000);
  }

  createParticles() {
    const particleCount = 150; 
    const colors = ['#4d7bff', '#3a68e0', '#5ac8fa', '#34c759', '#ffcc00'];

    for (let i = 0; i < particleCount; i++) {
      setTimeout(() => {
        this.addParticle(colors);
      }, Math.random() * 2000);
    }

    let particleInterval = setInterval(() => {
      for (let i = 0; i < 8; i++) { 
        this.addParticle(colors);
      }
    }, 250); 

    setTimeout(() => {
      clearInterval(particleInterval);
    }, 8000);
  }

  addParticle(colors: string[]) {
    const angle = Math.random() * Math.PI * 2;
    const distance = 50 + Math.random() * 350; 
    const tx = Math.cos(angle) * distance;
    const ty = Math.sin(angle) * distance;
    const size = 3 + Math.random() * 5; 
    const color = colors[Math.floor(Math.random() * colors.length)];
    const duration = 1.2 + Math.random() * 2.5; 

    const particle: Particle = {
      style: {
        left: '50%',
        top: '50%',
        width: `${size}px`,
        height: `${size}px`,
        backgroundColor: color,
        animation: `particle-move ${duration}s ease-out forwards`,
        '--tx': `${tx}px`,
        '--ty': `${ty}px`
      }
    };

    this.particles.push(particle);

    setTimeout(() => {
      const index = this.particles.indexOf(particle);
      if (index !== -1) {
        this.particles.splice(index, 1);
      }
    }, duration * 1000 + 100);
  }

  createHexagonGrid() {
    const gridSize = 15;
    const hexSize = 40;
    const centerX = window.innerWidth / 2;
    const centerY = window.innerHeight / 2;

    this.hexagons = [];

    for (let row = -gridSize; row <= gridSize; row++) {
      for (let col = -gridSize; col <= gridSize; col++) {
        const x = centerX + col * hexSize * 1.5;
        const y = centerY + (row * hexSize * 1.732) + (col % 2) * (hexSize * 0.866);

        const distance = Math.sqrt(Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2));
        if (distance > 500) continue;

        const delay = distance / 500; 

        const hex: Hexagon = {
          style: {
            left: `${x}px`,
            top: `${y}px`,
          }
        };

        this.hexagons.push(hex);
      }
    }

  }

  playAchievementSound() {
    try {
      const AudioContext = window.AudioContext || (window as any).webkitAudioContext;
      if (!AudioContext) return;

      const audioCtx = new AudioContext();

      const masterGain = audioCtx.createGain();
      masterGain.gain.value = 0.3; 
      masterGain.connect(audioCtx.destination);

      const compressor = audioCtx.createDynamicsCompressor();
      compressor.threshold.value = -24;
      compressor.knee.value = 30;
      compressor.ratio.value = 12;
      compressor.attack.value = 0.003;
      compressor.release.value = 0.25;
      compressor.connect(masterGain);

      const reverbGain = audioCtx.createGain();
      reverbGain.gain.value = 0.2; 
      reverbGain.connect(masterGain);

      const notes = [
        { note: 'G4', time: 0.0, duration: 0.15 },
        { note: 'C5', time: 0.15, duration: 0.15 },
        { note: 'E5', time: 0.3, duration: 0.15 },
        { note: 'G5', time: 0.45, duration: 0.4 }
      ];

      const noteFrequency: {[key: string]: number} = {
        'G4': 392.00,
        'C5': 523.25,
        'E5': 659.25,
        'G5': 783.99
      };

      notes.forEach(note => {
        const mainOsc = audioCtx.createOscillator();
        const subOsc = audioCtx.createOscillator();

        const noteGain = audioCtx.createGain();
        noteGain.gain.value = 0;

        mainOsc.connect(noteGain);
        subOsc.connect(noteGain);
        noteGain.connect(compressor);

        noteGain.connect(reverbGain);

        mainOsc.type = 'sine';
        subOsc.type = 'triangle';

        const freq = noteFrequency[note.note];
        mainOsc.frequency.value = freq;
        subOsc.frequency.value = freq;

        const startTime = audioCtx.currentTime + note.time;
        const stopTime = startTime + note.duration;

        noteGain.gain.setValueAtTime(0, startTime);
        noteGain.gain.linearRampToValueAtTime(0.7, startTime + 0.02);

        noteGain.gain.linearRampToValueAtTime(0.5, startTime + 0.05);

        noteGain.gain.setValueAtTime(0.5, stopTime - 0.05);

        noteGain.gain.linearRampToValueAtTime(0, stopTime);

        mainOsc.start(startTime);
        subOsc.start(startTime);
        mainOsc.stop(stopTime + 0.1); 
        subOsc.stop(stopTime + 0.1);
      });

      const chordTime = audioCtx.currentTime + 0.85;
      const chordDuration = 0.8;

      const chordNotes = [
        { freq: 392.00, type: 'sine' },     
        { freq: 493.88, type: 'sine' },     
        { freq: 587.33, type: 'triangle' }, 
        { freq: 783.99, type: 'triangle' }  
      ];

      const chordGain = audioCtx.createGain();
      chordGain.gain.setValueAtTime(0, chordTime);
      chordGain.gain.linearRampToValueAtTime(0.2, chordTime + 0.1);
      chordGain.gain.setValueAtTime(0.2, chordTime + chordDuration - 0.3);
      chordGain.gain.linearRampToValueAtTime(0, chordTime + chordDuration);
      chordGain.connect(compressor);
      chordGain.connect(reverbGain);

      chordNotes.forEach(noteData => {
        const osc = audioCtx.createOscillator();
        osc.type = noteData.type as OscillatorType;
        osc.frequency.value = noteData.freq;
        osc.connect(chordGain);
        osc.start(chordTime);
        osc.stop(chordTime + chordDuration);
      });
    } catch (e) {
    }
  }

  closeModal() {
    this.close.emit();
  }
}
