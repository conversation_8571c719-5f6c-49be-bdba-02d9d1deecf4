﻿<ion-content [fullscreen]="true" class="ion-padding">
  <div class="background-container">
    <div class="gradient-bg"></div>
    <div class="celestial-body"></div>
  </div>
  <ion-grid>
    <ion-row *ngFor="let content of pageContent; let isLast = last" class="ion-padding-top">
      <ion-col size="3" class="icon-col">
        <div class="vertical-bar" [class.last-bar]="isLast"></div>
        <ion-badge class="icon-badge">
          <ion-icon [name]="content.icon"></ion-icon>
        </ion-badge>
      </ion-col>
      <ion-col class="text-col">
        <h2>{{ content.title }}</h2>
        <ion-text class="dark-text">
          {{ content.text }}
        </ion-text>
      </ion-col>
    </ion-row>
    <ion-row class="price-row ion-margin-top ion-padding-top" style="flex-wrap: nowrap;">
      <ion-col
        *ngFor="let key of ['monthly', 'yearly']"
        size="5"
        class="price"
      >
        <ion-badge *ngIf="getPricingValue(key).badge">
          {{ getPricingValue(key).badgeText }}
        </ion-badge>

        <div class="price-content">
          <h3>{{ getPricingValue(key).label }}</h3>
          <ion-text>{{ getPricingValue(key).displayPrice }}</ion-text>
        </div>
      </ion-col>
    </ion-row>
    <ion-row *ngIf="hasAffiliateDiscount" class="affiliate-discount-row ion-margin-top">
      <ion-col>
        <ion-card class="affiliate-discount-card">
          <ion-card-content>
            <ion-icon name="gift-outline"></ion-icon>
            <p>You're getting a <strong>{{ discountPercentage }}% discount</strong><br> thanks to referral code <strong>{{ affiliateCode }}</strong>!</p>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>

<ion-footer class="ion-padding">
  <ion-button class="blue-button" (click)="processPayment()">Process to payment</ion-button>
</ion-footer>
