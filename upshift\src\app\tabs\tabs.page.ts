﻿import { Component, inject } from '@angular/core';
import { RouterModule, Router } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import {AdminService} from "../services/admin.service";
import {catchError, of} from "rxjs";
import { OnInit } from "@angular/core";

@Component({
  selector: 'app-tabs',
  standalone: true,
  templateUrl: 'tabs.page.html',
  styleUrls: ['tabs.page.scss'],
  imports: [IonicModule, CommonModule,  RouterModule],
})
export class TabsPage implements OnInit {
  isAdmin = false;

  constructor(private adminService: AdminService) {}

  ngOnInit() {
    this.adminService.isAdmin()
      .pipe(
        catchError(error => {
          return of(false);
        })
      )
      .subscribe({
        next: (isAdmin) => {
          this.isAdmin = isAdmin;
        }
      });
  }
}
