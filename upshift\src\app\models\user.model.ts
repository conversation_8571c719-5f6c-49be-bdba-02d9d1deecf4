﻿export type PlanType = 'yearly' | 'monthly' | 'none';
export type SubscriptionStatus = 'email marketing' | 'active' | 'cancelled' | 'paused' | 'expired';

export interface User {
  id?: string; 
  email: string;
  username: string;
  name?: string;
  profile_picture?: string;
  registration_date: Date;
  last_login: Date;
  active: boolean;

  strength_xp: number;
  money_xp: number;
  health_xp: number;
  knowledge_xp: number;
  level: number;
  title: string;

  bio: string;
  affiliate_code_used: string;
  timezone: string;

  friend_code?: string;
  friend_code_expiry?: Date;

  plan?: PlanType;
  start_of_current_plan?: Date;
  end_of_current_plan?: Date;
  auto_renew: boolean;

  start_of_sick_days?: Date;
  end_of_sick_days?: Date;
  sidequests_switch: boolean;

  show_celebration: boolean;
  celebration_name: string;
  celebration_description: string;
  celebration_emoji: string;

  subscription_status: SubscriptionStatus;
}
