{"ast": null, "code": "var _AdminPage;\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/admin.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ionic/angular\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction AdminPage_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"ion-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading collections...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AdminPage_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"p\");\n    i0.ɵɵtext(2, \"No collections found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AdminPage_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵlistener(\"click\", function AdminPage_div_15_div_1_Template_div_click_0_listener() {\n      const collection_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigateToCollection(collection_r2));\n    });\n    i0.ɵɵelementStart(1, \"h3\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const collection_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(collection_r2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Manage \", collection_r2, \"\");\n  }\n}\nfunction AdminPage_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtemplate(1, AdminPage_div_15_div_1_Template, 5, 2, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.collections);\n  }\n}\nexport class AdminPage {\n  constructor(adminService, router) {\n    this.adminService = adminService;\n    this.router = router;\n    this.collections = [];\n    this.isLoading = true;\n    this.newCollectionName = '';\n  }\n  ngOnInit() {\n    this.loadCollections();\n  }\n  loadCollections() {\n    this.isLoading = true;\n    this.adminService.getCollections().subscribe({\n      next: collections => {\n        this.collections = collections;\n        this.isLoading = false;\n      },\n      error: error => {\n        this.isLoading = false;\n      }\n    });\n  }\n  navigateToCollection(collection) {\n    this.router.navigate(['/admin/collection', collection]);\n  }\n  addCollection() {\n    if (!this.newCollectionName.trim()) return;\n    this.isLoading = true;\n    this.adminService.addCollection(this.newCollectionName.trim()).subscribe({\n      next: () => {\n        this.newCollectionName = '';\n        this.loadCollections();\n      },\n      error: error => {\n        this.isLoading = false;\n      }\n    });\n  }\n}\n_AdminPage = AdminPage;\n_AdminPage.ɵfac = function AdminPage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _AdminPage)(i0.ɵɵdirectiveInject(i1.AdminService), i0.ɵɵdirectiveInject(i2.Router));\n};\n_AdminPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _AdminPage,\n  selectors: [[\"app-admin\"]],\n  decls: 23,\n  vars: 4,\n  consts: [[1, \"container\"], [1, \"logo\"], [\"src\", \"assets/images/upshift_icon_mini.svg\", \"alt\", \"Upshift\"], [1, \"admin-content\"], [1, \"collections-list\"], [\"class\", \"loading\", 4, \"ngIf\"], [\"class\", \"no-data\", 4, \"ngIf\"], [\"class\", \"collection-grid\", 4, \"ngIf\"], [1, \"add-collection\"], [1, \"add-collection-form\"], [\"type\", \"text\", \"placeholder\", \"Collection name\", 3, \"ngModelChange\", \"ngModel\"], [3, \"click\"], [1, \"loading\"], [1, \"no-data\"], [1, \"collection-grid\"], [\"class\", \"collection-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"collection-card\", 3, \"click\"]],\n  template: function AdminPage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ion-content\")(1, \"div\", 0)(2, \"header\")(3, \"div\", 1);\n      i0.ɵɵelement(4, \"img\", 2);\n      i0.ɵɵelementStart(5, \"span\");\n      i0.ɵɵtext(6, \"Upshift\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(7, \"h1\");\n      i0.ɵɵtext(8, \"Admin Dashboard\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(9, \"div\", 3)(10, \"div\", 4)(11, \"h2\");\n      i0.ɵɵtext(12, \"Collections\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(13, AdminPage_div_13_Template, 4, 0, \"div\", 5)(14, AdminPage_div_14_Template, 3, 0, \"div\", 6)(15, AdminPage_div_15_Template, 2, 1, \"div\", 7);\n      i0.ɵɵelementStart(16, \"div\", 8)(17, \"h3\");\n      i0.ɵɵtext(18, \"Add New Collection\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(19, \"div\", 9)(20, \"input\", 10);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function AdminPage_Template_input_ngModelChange_20_listener($event) {\n        i0.ɵɵtwoWayBindingSet(ctx.newCollectionName, $event) || (ctx.newCollectionName = $event);\n        return $event;\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(21, \"button\", 11);\n      i0.ɵɵlistener(\"click\", function AdminPage_Template_button_click_21_listener() {\n        return ctx.addCollection();\n      });\n      i0.ɵɵtext(22, \"Add\");\n      i0.ɵɵelementEnd()()()()()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(13);\n      i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.collections.length === 0);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.collections.length > 0);\n      i0.ɵɵadvance(5);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newCollectionName);\n    }\n  },\n  dependencies: [IonicModule, i3.IonContent, i3.IonSpinner, CommonModule, i4.NgForOf, i4.NgIf, FormsModule, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel],\n  styles: [\".container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 20px;\\n  color: white;\\n  min-height: 100vh;\\n  padding-bottom: 80px;\\n  overflow-y: scroll;\\n}\\n\\nheader[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 30px;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  font-size: 1.5rem;\\n  font-weight: bold;\\n}\\n\\n.logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 30px;\\n}\\n\\n.admin-content[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n  border-radius: 10px;\\n  padding: 20px;\\n}\\n\\n.collections-list[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  color: #FF9500;\\n}\\n\\n.loading[_ngcontent-%COMP%], .no-data[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 0;\\n}\\n\\n.collection-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\\n  gap: 20px;\\n  margin-bottom: 30px;\\n}\\n\\n.collection-card[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n  border-radius: 8px;\\n  padding: 20px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n.collection-card[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.2);\\n  transform: translateY(-3px);\\n}\\n\\n.collection-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 10px 0;\\n  color: #FF9500;\\n}\\n\\n.collection-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.9rem;\\n  opacity: 0.8;\\n}\\n\\n.add-collection[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.05);\\n  border-radius: 8px;\\n  padding: 20px;\\n  margin-top: 20px;\\n}\\n\\n.add-collection[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 15px 0;\\n  color: #FF9500;\\n}\\n\\n.add-collection-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n\\n.add-collection-form[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 10px;\\n  border-radius: 4px;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  background-color: rgba(0, 0, 0, 0.2);\\n  color: white;\\n}\\n\\n.add-collection-form[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 10px 20px;\\n  border-radius: 4px;\\n  border: none;\\n  background-color: #FF9500;\\n  color: white;\\n  cursor: pointer;\\n  transition: background-color 0.3s ease;\\n}\\n\\n.add-collection-form[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background-color: #e68600;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "IonicModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "AdminPage_div_15_div_1_Template_div_click_0_listener", "collection_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "navigateToCollection", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵtextInterpolate1", "ɵɵtemplate", "AdminPage_div_15_div_1_Template", "ɵɵproperty", "collections", "AdminPage", "constructor", "adminService", "router", "isLoading", "newCollectionName", "ngOnInit", "loadCollections", "getCollections", "subscribe", "next", "error", "collection", "navigate", "addCollection", "trim", "ɵɵdirectiveInject", "i1", "AdminService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "AdminPage_Template", "rf", "ctx", "AdminPage_div_13_Template", "AdminPage_div_14_Template", "AdminPage_div_15_Template", "ɵɵtwoWayListener", "AdminPage_Template_input_ngModelChange_20_listener", "$event", "ɵɵtwoWayBindingSet", "AdminPage_Template_button_click_21_listener", "length", "ɵɵtwoWayProperty", "i3", "IonContent", "Ion<PERSON><PERSON><PERSON>", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\admin\\admin.page.ts", "C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\admin\\admin.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { AdminService } from '../../services/admin.service';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-admin',\r\n  templateUrl: './admin.page.html',\r\n  styleUrls: ['./admin.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule, FormsModule]\r\n})\r\nexport class AdminPage implements OnInit {\r\n  collections: string[] = [];\r\n  isLoading = true;\r\n  newCollectionName = '';\r\n\r\n  constructor(\r\n    private adminService: AdminService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.loadCollections();\r\n  }\r\n\r\n  loadCollections() {\r\n    this.isLoading = true;\r\n    this.adminService.getCollections().subscribe({\r\n      next: (collections) => {\r\n        this.collections = collections;\r\n        this.isLoading = false;\r\n      },\r\n      error: (error) => {\r\n        this.isLoading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  navigateToCollection(collection: string) {\r\n    this.router.navigate(['/admin/collection', collection]);\r\n  }\r\n\r\n  addCollection() {\r\n    if (!this.newCollectionName.trim()) return;\r\n\r\n    this.isLoading = true;\r\n    this.adminService.addCollection(this.newCollectionName.trim()).subscribe({\r\n      next: () => {\r\n        this.newCollectionName = '';\r\n        this.loadCollections();\r\n      },\r\n      error: (error) => {\r\n        this.isLoading = false;\r\n      }\r\n    });\r\n  }\r\n}\r\n", "<ion-content>\r\n  <div class=\"container\">\r\n    <header>\r\n      <div class=\"logo\">\r\n        <img src=\"assets/images/upshift_icon_mini.svg\" alt=\"Upshift\">\r\n        <span>Upshift</span>\r\n      </div>\r\n      <h1>Admin Dashboard</h1>\r\n    </header>\r\n\r\n    <div class=\"admin-content\">\r\n      <div class=\"collections-list\">\r\n        <h2>Collections</h2>\r\n\r\n        <div *ngIf=\"isLoading\" class=\"loading\">\r\n          <ion-spinner></ion-spinner>\r\n          <p>Loading collections...</p>\r\n        </div>\r\n\r\n        <div *ngIf=\"!isLoading && collections.length === 0\" class=\"no-data\">\r\n          <p>No collections found.</p>\r\n        </div>\r\n\r\n        <div *ngIf=\"!isLoading && collections.length > 0\" class=\"collection-grid\">\r\n          <div *ngFor=\"let collection of collections\" class=\"collection-card\" (click)=\"navigateToCollection(collection)\">\r\n            <h3>{{ collection }}</h3>\r\n            <p>Manage {{ collection }}</p>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"add-collection\">\r\n          <h3>Add New Collection</h3>\r\n          <div class=\"add-collection-form\">\r\n            <input type=\"text\" [(ngModel)]=\"newCollectionName\" placeholder=\"Collection name\">\r\n            <button (click)=\"addCollection()\">Add</button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</ion-content>\r\n\r\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;ICWpCC,EAAA,CAAAC,cAAA,cAAuC;IACrCD,EAAA,CAAAE,SAAA,kBAA2B;IAC3BF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,6BAAsB;IAC3BH,EAD2B,CAAAI,YAAA,EAAI,EACzB;;;;;IAGJJ,EADF,CAAAC,cAAA,cAAoE,QAC/D;IAAAD,EAAA,CAAAG,MAAA,4BAAqB;IAC1BH,EAD0B,CAAAI,YAAA,EAAI,EACxB;;;;;;IAGJJ,EAAA,CAAAC,cAAA,cAA+G;IAA3CD,EAAA,CAAAK,UAAA,mBAAAC,qDAAA;MAAA,MAAAC,aAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,oBAAA,CAAAP,aAAA,CAAgC;IAAA,EAAC;IAC5GP,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACzBJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAuB;IAC5BH,EAD4B,CAAAI,YAAA,EAAI,EAC1B;;;;IAFAJ,EAAA,CAAAe,SAAA,GAAgB;IAAhBf,EAAA,CAAAgB,iBAAA,CAAAT,aAAA,CAAgB;IACjBP,EAAA,CAAAe,SAAA,GAAuB;IAAvBf,EAAA,CAAAiB,kBAAA,YAAAV,aAAA,KAAuB;;;;;IAH9BP,EAAA,CAAAC,cAAA,cAA0E;IACxED,EAAA,CAAAkB,UAAA,IAAAC,+BAAA,kBAA+G;IAIjHnB,EAAA,CAAAI,YAAA,EAAM;;;;IAJwBJ,EAAA,CAAAe,SAAA,EAAc;IAAdf,EAAA,CAAAoB,UAAA,YAAAT,MAAA,CAAAU,WAAA,CAAc;;;ADVpD,OAAM,MAAOC,SAAS;EAKpBC,YACUC,YAA0B,EAC1BC,MAAc;IADd,KAAAD,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IANhB,KAAAJ,WAAW,GAAa,EAAE;IAC1B,KAAAK,SAAS,GAAG,IAAI;IAChB,KAAAC,iBAAiB,GAAG,EAAE;EAKnB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAA,eAAeA,CAAA;IACb,IAAI,CAACH,SAAS,GAAG,IAAI;IACrB,IAAI,CAACF,YAAY,CAACM,cAAc,EAAE,CAACC,SAAS,CAAC;MAC3CC,IAAI,EAAGX,WAAW,IAAI;QACpB,IAAI,CAACA,WAAW,GAAGA,WAAW;QAC9B,IAAI,CAACK,SAAS,GAAG,KAAK;MACxB,CAAC;MACDO,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACP,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEAZ,oBAAoBA,CAACoB,UAAkB;IACrC,IAAI,CAACT,MAAM,CAACU,QAAQ,CAAC,CAAC,mBAAmB,EAAED,UAAU,CAAC,CAAC;EACzD;EAEAE,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAACT,iBAAiB,CAACU,IAAI,EAAE,EAAE;IAEpC,IAAI,CAACX,SAAS,GAAG,IAAI;IACrB,IAAI,CAACF,YAAY,CAACY,aAAa,CAAC,IAAI,CAACT,iBAAiB,CAACU,IAAI,EAAE,CAAC,CAACN,SAAS,CAAC;MACvEC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACL,iBAAiB,GAAG,EAAE;QAC3B,IAAI,CAACE,eAAe,EAAE;MACxB,CAAC;MACDI,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACP,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;;aA5CWJ,SAAS;;mCAATA,UAAS,EAAAtB,EAAA,CAAAsC,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAxC,EAAA,CAAAsC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;AAAA;;QAATpB,UAAS;EAAAqB,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,mBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCXhBjD,EAHN,CAAAC,cAAA,kBAAa,aACY,aACb,aACY;MAChBD,EAAA,CAAAE,SAAA,aAA6D;MAC7DF,EAAA,CAAAC,cAAA,WAAM;MAAAD,EAAA,CAAAG,MAAA,cAAO;MACfH,EADe,CAAAI,YAAA,EAAO,EAChB;MACNJ,EAAA,CAAAC,cAAA,SAAI;MAAAD,EAAA,CAAAG,MAAA,sBAAe;MACrBH,EADqB,CAAAI,YAAA,EAAK,EACjB;MAILJ,EAFJ,CAAAC,cAAA,aAA2B,cACK,UACxB;MAAAD,EAAA,CAAAG,MAAA,mBAAW;MAAAH,EAAA,CAAAI,YAAA,EAAK;MAWpBJ,EATA,CAAAkB,UAAA,KAAAiC,yBAAA,iBAAuC,KAAAC,yBAAA,iBAK6B,KAAAC,yBAAA,iBAIM;MAQxErD,EADF,CAAAC,cAAA,cAA4B,UACtB;MAAAD,EAAA,CAAAG,MAAA,0BAAkB;MAAAH,EAAA,CAAAI,YAAA,EAAK;MAEzBJ,EADF,CAAAC,cAAA,cAAiC,iBACkD;MAA9DD,EAAA,CAAAsD,gBAAA,2BAAAC,mDAAAC,MAAA;QAAAxD,EAAA,CAAAyD,kBAAA,CAAAP,GAAA,CAAAvB,iBAAA,EAAA6B,MAAA,MAAAN,GAAA,CAAAvB,iBAAA,GAAA6B,MAAA;QAAA,OAAAA,MAAA;MAAA,EAA+B;MAAlDxD,EAAA,CAAAI,YAAA,EAAiF;MACjFJ,EAAA,CAAAC,cAAA,kBAAkC;MAA1BD,EAAA,CAAAK,UAAA,mBAAAqD,4CAAA;QAAA,OAASR,GAAA,CAAAd,aAAA,EAAe;MAAA,EAAC;MAACpC,EAAA,CAAAG,MAAA,WAAG;MAMjDH,EANiD,CAAAI,YAAA,EAAS,EAC1C,EACF,EACF,EACF,EACF,EACM;;;MA1BAJ,EAAA,CAAAe,SAAA,IAAe;MAAff,EAAA,CAAAoB,UAAA,SAAA8B,GAAA,CAAAxB,SAAA,CAAe;MAKf1B,EAAA,CAAAe,SAAA,EAA4C;MAA5Cf,EAAA,CAAAoB,UAAA,UAAA8B,GAAA,CAAAxB,SAAA,IAAAwB,GAAA,CAAA7B,WAAA,CAAAsC,MAAA,OAA4C;MAI5C3D,EAAA,CAAAe,SAAA,EAA0C;MAA1Cf,EAAA,CAAAoB,UAAA,UAAA8B,GAAA,CAAAxB,SAAA,IAAAwB,GAAA,CAAA7B,WAAA,CAAAsC,MAAA,KAA0C;MAUzB3D,EAAA,CAAAe,SAAA,GAA+B;MAA/Bf,EAAA,CAAA4D,gBAAA,YAAAV,GAAA,CAAAvB,iBAAA,CAA+B;;;iBDrBlD5B,WAAW,EAAA8D,EAAA,CAAAC,UAAA,EAAAD,EAAA,CAAAE,UAAA,EAAElE,YAAY,EAAAmE,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEpE,WAAW,EAAAqE,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;EAAAC,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}