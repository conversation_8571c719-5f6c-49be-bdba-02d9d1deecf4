﻿ Exact CSS from Django template */
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo img {
    height: 24px;
}

.logo span {
    font-size: 20px;
    font-weight: 600;
}

h1 {
    font-size: 20px;
    font-weight: 600;
}

.friends-container {
    padding: 20px;
    margin-bottom: 80px;
}

.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.section-icon {
    font-size: 24px;
    margin-right: 10px;
}

.section-title {
    font-size: 20px;
    font-weight: 600;
}

.friend-card {
    background-color: var(--card-bg);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.friend-info {
    display: flex;
    align-items: center;
}

.friend-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--accent-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin-right: 15px;
    overflow: hidden;
}

.friend-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.friend-name {
    font-size: 16px;
    font-weight: 500;
}

.friend-actions a {
    color: var(--danger-color);
    text-decoration: none;
}

.code-section, .affiliate-rewards-section {
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 30px;
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
}

.code-header {
    margin-bottom: 15px;
}

.code-actions {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.code-display {
    background-color: var(--bg-color);
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    font-size: 24px;
    font-weight: 600;
    letter-spacing: 2px;
    margin-bottom: 15px;
}

.code-info {
    font-size: 14px;
    color: var(--secondary-text);
    text-align: center;
}

.add-code-form {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.add-code-form input {
    padding: 12px;
    border-radius: 8px;
    border: none;
    background-color: var(--bg-color);
    color: var(--text-color);
    font-size: 16px;
}

.add-code-form button {
    padding: 12px;
    border-radius: 8px;
    border: none;
    background-color: var(--accent-color);
    color: white;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
}

.generate-code-btn, .affiliate-rewards-btn {
    width: 100%;
    padding: 12px;
    border-radius: 8px;
    border: none;
    background-color: var(--accent-color);
    color: white;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
}

.no-friends {
    text-align: center;
    padding: 20px;
    color: var(--secondary-text);
}

.affiliate-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.affiliate-count {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--bg-color);
    padding: 15px 15px;
    border-radius: 8px;
    margin-bottom: 5px;
    margin-top: 5px;
}

.count-label {
    font-size: 16px;
    font-weight: 500;
}

.count-value {
    font-size: 18px;
    font-weight: 600;
    color: var(--accent-color);
}

.friends-list {
    margin-bottom: 30px;
}
.friend-requests {
    margin-bottom: 30px;
}

.request-card {
    background-color: var(--card-bg);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 15px;
}

.request-info {
    margin-bottom: 10px;
}

.request-actions {
    display: flex;
    gap: 10px;
}

.request-actions button {
    flex: 1;
    padding: 8px;
    border-radius: 8px;
    border: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
}

.accept-btn {
    background-color: var(--accent-color);
    color: white;
}

.reject-btn {
    background-color: var(--danger-color);
    color: white;
}

 Leaderboard styles */
    color: white;
}

.user-info {
    display: flex;
    align-items: center;
    flex-grow: 1;
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--accent-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin-right: 15px;
    overflow: hidden;
    flex-shrink: 0;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-size: 16px;
    font-weight: 600;
}

.user-level {
    font-size: 14px;
    color: var(--secondary-text);
}

.user-info-link {
    text-decoration: none;
    color: inherit;
    display: flex;
    flex-grow: 1;
}

.message {
    padding: 10px;
    border-radius: 8px;
    margin-bottom: 15px;
}

.message.success {
    background-color: rgba(0, 128, 0, 0.1);
    border: 1px solid green;
    color: green;
}

.message.error {
    background-color: rgba(255, 0, 0, 0.1);
    border: 1px solid red;
    color: red;
}

 Navigation Styles */
.container {
    padding-bottom: 120px !important;
}

@media screen and (max-width: 480px) {
    
    .container {
       width:auto;
    }

   
}