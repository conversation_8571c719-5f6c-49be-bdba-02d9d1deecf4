{"ast": null, "code": "var _XpService;\nimport { of } from 'rxjs';\nimport { map, catchError, shareReplay } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport var EntityType;\n(function (EntityType) {\n  EntityType[\"USER\"] = \"user\";\n  EntityType[\"GROUP\"] = \"group\";\n})(EntityType || (EntityType = {}));\nexport class XpService {\n  constructor(http) {\n    this.http = http;\n    this.userLevelDataCache$ = null;\n    this.groupLevelDataCache$ = null;\n  }\n  getLevelData(entityType = EntityType.USER) {\n    if (entityType === EntityType.USER) {\n      if (!this.userLevelDataCache$) {\n        this.userLevelDataCache$ = this.http.get('assets/data/levelsAndXp.json').pipe(catchError(error => {\n          return of([]);\n        }), shareReplay(1));\n      }\n      return this.userLevelDataCache$;\n    } else {\n      if (!this.groupLevelDataCache$) {\n        this.groupLevelDataCache$ = this.http.get('assets/data/groupLevelsAndXp.json').pipe(catchError(error => {\n          return of([]);\n        }), shareReplay(1));\n      }\n      return this.groupLevelDataCache$;\n    }\n  }\n  getRequiredXpForNextLevel(currentLevel, entityType = EntityType.USER) {\n    return this.getLevelData(entityType).pipe(map(levelData => {\n      if (currentLevel >= 100) {\n        return 0;\n      }\n      const nextLevelData = levelData.find(item => item.Level === currentLevel + 1);\n      return nextLevelData ? nextLevelData.XP_per_category : 0;\n    }));\n  }\n  calculateXpProgress(entity, entityType = EntityType.USER) {\n    if (!entity) {\n      return of(null);\n    }\n    return this.getRequiredXpForNextLevel(entity.level, entityType).pipe(map(requiredXp => {\n      const categories = [{\n        name: 'Strength',\n        field: 'strength_xp',\n        icon: '💪',\n        color: '#FF5733'\n      }, {\n        name: 'Money',\n        field: 'money_xp',\n        icon: '💰',\n        color: '#33FF57'\n      }, {\n        name: 'Health',\n        field: 'health_xp',\n        icon: '❤️',\n        color: '#3357FF'\n      }, {\n        name: 'Knowledge',\n        field: 'knowledge_xp',\n        icon: '🧠',\n        color: '#F033FF'\n      }];\n      for (const category of categories) {\n        const currentXp = entity[category.field] || 0;\n        category['current_xp'] = currentXp;\n        category['required_xp'] = requiredXp;\n        if (requiredXp > 0) {\n          category['progress'] = Math.min(100, Math.round(currentXp / requiredXp * 100));\n        } else {\n          category['progress'] = 100;\n        }\n      }\n      return {\n        categories,\n        next_level: entity.level < 100 ? entity.level + 1 : 'Max'\n      };\n    }));\n  }\n  getUserTitle(level) {\n    if (level >= 100) return 'Professional Upshifter';\n    if (level >= 90) return 'Ultra Human';\n    if (level >= 75) return 'Indestructible';\n    if (level >= 60) return 'Elite Operator';\n    if (level >= 50) return 'Peak Performer';\n    if (level >= 40) return 'Master of Consistency';\n    if (level >= 35) return 'High Performer';\n    if (level >= 30) return 'Disciplined Machine';\n    if (level >= 25) return 'Hardcore';\n    if (level >= 20) return 'Nonchalant';\n    if (level >= 15) return 'Monk';\n    if (level >= 10) return 'Warrior';\n    if (level >= 5) return 'Newbie';\n    return 'Beginner';\n  }\n}\n_XpService = XpService;\n_XpService.ɵfac = function XpService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _XpService)(i0.ɵɵinject(i1.HttpClient));\n};\n_XpService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _XpService,\n  factory: _XpService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "names": ["of", "map", "catchError", "shareReplay", "EntityType", "XpService", "constructor", "http", "userLevelDataCache$", "groupLevelDataCache$", "getLevelData", "entityType", "USER", "get", "pipe", "error", "getRequiredXpForNextLevel", "currentLevel", "levelData", "nextLevelData", "find", "item", "Level", "XP_per_category", "calculateXpProgress", "entity", "level", "requiredXp", "categories", "name", "field", "icon", "color", "category", "currentXp", "Math", "min", "round", "next_level", "getUserTitle", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\services\\xp.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Observable, of, forkJoin } from 'rxjs';\r\nimport { map, catchError, shareReplay } from 'rxjs/operators';\r\n\r\nexport interface LevelData {\r\n  Level: number;\r\n  XP_per_category: number;\r\n}\r\n\r\nexport enum EntityType {\r\n  USER = 'user',\r\n  GROUP = 'group'\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class XpService {\r\n  private userLevelDataCache$: Observable<LevelData[]> | null = null;\r\n  private groupLevelDataCache$: Observable<LevelData[]> | null = null;\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  getLevelData(entityType: EntityType = EntityType.USER): Observable<LevelData[]> {\r\n    if (entityType === EntityType.USER) {\r\n      if (!this.userLevelDataCache$) {\r\n        this.userLevelDataCache$ = this.http.get<LevelData[]>('assets/data/levelsAndXp.json').pipe(\r\n          catchError(error => {\r\n            return of([]);\r\n          }),\r\n          shareReplay(1)\r\n        );\r\n      }\r\n      return this.userLevelDataCache$;\r\n    } else {\r\n      if (!this.groupLevelDataCache$) {\r\n        this.groupLevelDataCache$ = this.http.get<LevelData[]>('assets/data/groupLevelsAndXp.json').pipe(\r\n          catchError(error => {\r\n            return of([]);\r\n          }),\r\n          shareReplay(1)\r\n        );\r\n      }\r\n      return this.groupLevelDataCache$;\r\n    }\r\n  }\r\n\r\n  getRequiredXpForNextLevel(currentLevel: number, entityType: EntityType = EntityType.USER): Observable<number> {\r\n    return this.getLevelData(entityType).pipe(\r\n      map(levelData => {\r\n        if (currentLevel >= 100) {\r\n          return 0; \n        }\r\n\r\n        const nextLevelData = levelData.find(item => item.Level === currentLevel + 1);\r\n        return nextLevelData ? nextLevelData.XP_per_category : 0;\r\n      })\r\n    );\r\n  }\r\n\r\n  calculateXpProgress(entity: any, entityType: EntityType = EntityType.USER): Observable<any> {\r\n    if (!entity) {\r\n      return of(null);\r\n    }\r\n\r\n    return this.getRequiredXpForNextLevel(entity.level, entityType).pipe(\r\n      map(requiredXp => {\r\n        const categories = [\r\n          { name: 'Strength', field: 'strength_xp', icon: '💪', color: '#FF5733' },\r\n          { name: 'Money', field: 'money_xp', icon: '💰', color: '#33FF57' },\r\n          { name: 'Health', field: 'health_xp', icon: '❤️', color: '#3357FF' },\r\n          { name: 'Knowledge', field: 'knowledge_xp', icon: '🧠', color: '#F033FF' }\r\n        ];\r\n\r\n        for (const category of categories) {\r\n          const currentXp = entity[category.field] || 0;\r\n          category['current_xp'] = currentXp;\r\n          category['required_xp'] = requiredXp;\r\n\r\n          if (requiredXp > 0) {\r\n            category['progress'] = Math.min(100, Math.round((currentXp / requiredXp) * 100));\r\n          } else {\r\n            category['progress'] = 100;\r\n          }\r\n        }\r\n\r\n        return {\r\n          categories,\r\n          next_level: entity.level < 100 ? entity.level + 1 : 'Max'\r\n        };\r\n      })\r\n    );\r\n  }\r\n\r\n  getUserTitle(level: number): string {\r\n    if (level >= 100) return 'Professional Upshifter';\r\n    if (level >= 90) return 'Ultra Human';\r\n    if (level >= 75) return 'Indestructible';\r\n    if (level >= 60) return 'Elite Operator';\r\n    if (level >= 50) return 'Peak Performer';\r\n    if (level >= 40) return 'Master of Consistency';\r\n    if (level >= 35) return 'High Performer';\r\n    if (level >= 30) return 'Disciplined Machine';\r\n    if (level >= 25) return 'Hardcore';\r\n    if (level >= 20) return 'Nonchalant';\r\n    if (level >= 15) return 'Monk';\r\n    if (level >= 10) return 'Warrior';\r\n    if (level >= 5) return 'Newbie';\r\n    return 'Beginner';\r\n  }\r\n}\r\n"], "mappings": ";AAEA,SAAqBA,EAAE,QAAkB,MAAM;AAC/C,SAASC,GAAG,EAAEC,UAAU,EAAEC,WAAW,QAAQ,gBAAgB;;;AAO7D,WAAYC,UAGX;AAHD,WAAYA,UAAU;EACpBA,UAAA,iBAAa;EACbA,UAAA,mBAAe;AACjB,CAAC,EAHWA,UAAU,KAAVA,UAAU;AAQtB,OAAM,MAAOC,SAAS;EAIpBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHhB,KAAAC,mBAAmB,GAAmC,IAAI;IAC1D,KAAAC,oBAAoB,GAAmC,IAAI;EAE3B;EAExCC,YAAYA,CAACC,UAAA,GAAyBP,UAAU,CAACQ,IAAI;IACnD,IAAID,UAAU,KAAKP,UAAU,CAACQ,IAAI,EAAE;MAClC,IAAI,CAAC,IAAI,CAACJ,mBAAmB,EAAE;QAC7B,IAAI,CAACA,mBAAmB,GAAG,IAAI,CAACD,IAAI,CAACM,GAAG,CAAc,8BAA8B,CAAC,CAACC,IAAI,CACxFZ,UAAU,CAACa,KAAK,IAAG;UACjB,OAAOf,EAAE,CAAC,EAAE,CAAC;QACf,CAAC,CAAC,EACFG,WAAW,CAAC,CAAC,CAAC,CACf;MACH;MACA,OAAO,IAAI,CAACK,mBAAmB;IACjC,CAAC,MAAM;MACL,IAAI,CAAC,IAAI,CAACC,oBAAoB,EAAE;QAC9B,IAAI,CAACA,oBAAoB,GAAG,IAAI,CAACF,IAAI,CAACM,GAAG,CAAc,mCAAmC,CAAC,CAACC,IAAI,CAC9FZ,UAAU,CAACa,KAAK,IAAG;UACjB,OAAOf,EAAE,CAAC,EAAE,CAAC;QACf,CAAC,CAAC,EACFG,WAAW,CAAC,CAAC,CAAC,CACf;MACH;MACA,OAAO,IAAI,CAACM,oBAAoB;IAClC;EACF;EAEAO,yBAAyBA,CAACC,YAAoB,EAAEN,UAAA,GAAyBP,UAAU,CAACQ,IAAI;IACtF,OAAO,IAAI,CAACF,YAAY,CAACC,UAAU,CAAC,CAACG,IAAI,CACvCb,GAAG,CAACiB,SAAS,IAAG;MACd,IAAID,YAAY,IAAI,GAAG,EAAE;QACvB,OAAO,CAAC;MACV;MAEA,MAAME,aAAa,GAAGD,SAAS,CAACE,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,KAAK,KAAKL,YAAY,GAAG,CAAC,CAAC;MAC7E,OAAOE,aAAa,GAAGA,aAAa,CAACI,eAAe,GAAG,CAAC;IAC1D,CAAC,CAAC,CACH;EACH;EAEAC,mBAAmBA,CAACC,MAAW,EAAEd,UAAA,GAAyBP,UAAU,CAACQ,IAAI;IACvE,IAAI,CAACa,MAAM,EAAE;MACX,OAAOzB,EAAE,CAAC,IAAI,CAAC;IACjB;IAEA,OAAO,IAAI,CAACgB,yBAAyB,CAACS,MAAM,CAACC,KAAK,EAAEf,UAAU,CAAC,CAACG,IAAI,CAClEb,GAAG,CAAC0B,UAAU,IAAG;MACf,MAAMC,UAAU,GAAG,CACjB;QAAEC,IAAI,EAAE,UAAU;QAAEC,KAAK,EAAE,aAAa;QAAEC,IAAI,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAS,CAAE,EACxE;QAAEH,IAAI,EAAE,OAAO;QAAEC,KAAK,EAAE,UAAU;QAAEC,IAAI,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAS,CAAE,EAClE;QAAEH,IAAI,EAAE,QAAQ;QAAEC,KAAK,EAAE,WAAW;QAAEC,IAAI,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAS,CAAE,EACpE;QAAEH,IAAI,EAAE,WAAW;QAAEC,KAAK,EAAE,cAAc;QAAEC,IAAI,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAS,CAAE,CAC3E;MAED,KAAK,MAAMC,QAAQ,IAAIL,UAAU,EAAE;QACjC,MAAMM,SAAS,GAAGT,MAAM,CAACQ,QAAQ,CAACH,KAAK,CAAC,IAAI,CAAC;QAC7CG,QAAQ,CAAC,YAAY,CAAC,GAAGC,SAAS;QAClCD,QAAQ,CAAC,aAAa,CAAC,GAAGN,UAAU;QAEpC,IAAIA,UAAU,GAAG,CAAC,EAAE;UAClBM,QAAQ,CAAC,UAAU,CAAC,GAAGE,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,KAAK,CAAEH,SAAS,GAAGP,UAAU,GAAI,GAAG,CAAC,CAAC;QAClF,CAAC,MAAM;UACLM,QAAQ,CAAC,UAAU,CAAC,GAAG,GAAG;QAC5B;MACF;MAEA,OAAO;QACLL,UAAU;QACVU,UAAU,EAAEb,MAAM,CAACC,KAAK,GAAG,GAAG,GAAGD,MAAM,CAACC,KAAK,GAAG,CAAC,GAAG;OACrD;IACH,CAAC,CAAC,CACH;EACH;EAEAa,YAAYA,CAACb,KAAa;IACxB,IAAIA,KAAK,IAAI,GAAG,EAAE,OAAO,wBAAwB;IACjD,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,aAAa;IACrC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,gBAAgB;IACxC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,gBAAgB;IACxC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,gBAAgB;IACxC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,uBAAuB;IAC/C,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,gBAAgB;IACxC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,qBAAqB;IAC7C,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,UAAU;IAClC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,YAAY;IACpC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,MAAM;IAC9B,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS;IACjC,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO,QAAQ;IAC/B,OAAO,UAAU;EACnB;;aA5FWrB,SAAS;;mCAATA,UAAS,EAAAmC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;AAAA;;SAATtC,UAAS;EAAAuC,OAAA,EAATvC,UAAS,CAAAwC,IAAA;EAAAC,UAAA,EAFR;AAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}