{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _FriendProfilePage;\nimport { inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { RouterModule, ActivatedRoute, Router } from '@angular/router';\nimport { FriendService } from '../../../services/friend.service';\nimport { UserService } from '../../../services/user.service';\nimport { of, switchMap, take } from 'rxjs';\nimport { SupabaseService } from '../../../services/supabase.service';\nimport { XpService, EntityType } from '../../../services/xp.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nconst _c0 = a0 => [\"/badges\", a0];\nfunction FriendProfilePage_div_10_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 27);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.friend.profile_picture, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.friend.username);\n  }\n}\nfunction FriendProfilePage_div_10_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \"\\uD83D\\uDC64\");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction FriendProfilePage_div_10_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.friend.bio);\n  }\n}\nfunction FriendProfilePage_div_10_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtext(1, \"No bio provided\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FriendProfilePage_div_10_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 30)(2, \"div\", 31);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 32);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 33);\n    i0.ɵɵelement(7, \"div\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 35)(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const category_r3 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(category_r3.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r3.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", category_r3.progress, \"%\")(\"background-color\", category_r3.color);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", category_r3.current_xp, \" XP\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", category_r3.required_xp, \" XP needed\");\n  }\n}\nfunction FriendProfilePage_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"div\", 7);\n    i0.ɵɵtemplate(3, FriendProfilePage_div_10_img_3_Template, 1, 2, \"img\", 8)(4, FriendProfilePage_div_10_ng_container_4_Template, 2, 0, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 10)(6, \"div\", 11);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 12);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 13)(11, \"div\", 14);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 15);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"div\", 16);\n    i0.ɵɵtemplate(16, FriendProfilePage_div_10_div_16_Template, 2, 1, \"div\", 17)(17, FriendProfilePage_div_10_div_17_Template, 2, 0, \"div\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 18)(19, \"h2\");\n    i0.ɵɵtext(20, \"XP Progress\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, FriendProfilePage_div_10_div_21_Template, 13, 8, \"div\", 19);\n    i0.ɵɵelementStart(22, \"div\", 20)(23, \"div\", 21);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 22);\n    i0.ɵɵtext(26, \" Reach required XP in all categories to level up \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"div\", 23)(28, \"a\", 24)(29, \"span\", 25);\n    i0.ɵɵtext(30, \"\\uD83C\\uDFC6\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(31, \" View Badges \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"a\", 26);\n    i0.ɵɵlistener(\"click\", function FriendProfilePage_div_10_Template_a_click_32_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.removeFriend());\n    });\n    i0.ɵɵtext(33, \"Remove Friend\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.friend.profile_picture);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.friend.profile_picture);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.friend.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"@\", ctx_r1.friend.username, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Level \", ctx_r1.friend.level, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.friend.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.friend.bio);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.friend.bio);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.categories);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Next Level: \", ctx_r1.nextLevel, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(11, _c0, ctx_r1.friend.id));\n  }\n}\nexport class FriendProfilePage {\n  constructor() {\n    this.currentUserId = null;\n    this.friend = null;\n    this.friendId = null;\n    this.categories = [];\n    this.nextLevel = 0;\n    this.supabaseService = inject(SupabaseService);\n    this.friendService = inject(FriendService);\n    this.userService = inject(UserService);\n    this.route = inject(ActivatedRoute);\n    this.router = inject(Router);\n    this.xpService = inject(XpService);\n  }\n  ngOnInit() {\n    this.route.paramMap.pipe(take(1), switchMap(params => {\n      this.friendId = params.get('id');\n      return this.supabaseService.currentUser$;\n    }), switchMap(authUser => {\n      if (authUser) {\n        this.currentUserId = authUser.id;\n        if (this.friendId) {\n          return this.userService.getUser(this.friendId);\n        }\n      }\n      return of(null);\n    })).subscribe(friend => {\n      if (friend) {\n        this.friend = friend;\n        this.calculateXpProgress();\n      } else {\n        this.router.navigate(['/friends']);\n      }\n    });\n  }\n  calculateXpProgress() {\n    if (!this.friend) return;\n    this.xpService.calculateXpProgress(this.friend, EntityType.USER).subscribe(result => {\n      if (result) {\n        this.categories = result.categories;\n        this.nextLevel = result.next_level;\n      }\n    });\n  }\n  removeFriend() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this.currentUserId || !_this.friendId) return;\n      if (confirm('Are you sure you want to remove this friend?')) {\n        try {\n          yield _this.friendService.removeFriend(_this.currentUserId, _this.friendId);\n          _this.router.navigate(['/friends']);\n        } catch (error) {\n          alert('Failed to remove friend. Please try again.');\n        }\n      }\n    })();\n  }\n  goBack() {\n    window.history.back();\n  }\n}\n_FriendProfilePage = FriendProfilePage;\n_FriendProfilePage.ɵfac = function FriendProfilePage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _FriendProfilePage)();\n};\n_FriendProfilePage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _FriendProfilePage,\n  selectors: [[\"app-friend-profile\"]],\n  decls: 11,\n  vars: 1,\n  consts: [[1, \"container\"], [1, \"logo\"], [\"src\", \"assets/images/upshift_icon_mini.svg\", \"alt\", \"Upshift\"], [\"href\", \"javascript:void(0)\", 1, \"back-link\", 3, \"click\"], [\"class\", \"profile-container\", 4, \"ngIf\"], [1, \"profile-container\"], [1, \"profile-header\"], [1, \"profile-picture\"], [3, \"src\", \"alt\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"profile-info\"], [1, \"profile-name\"], [1, \"profile-username\"], [1, \"profile-level\"], [1, \"level-badge\"], [1, \"profile-title\"], [1, \"profile-bio-container\", 2, \"padding-left\", \"100px\"], [\"class\", \"profile-bio\", 4, \"ngIf\"], [1, \"xp-section\"], [\"class\", \"category-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"next-level-info\"], [1, \"next-level-text\"], [1, \"next-level-requirements\"], [1, \"button-container\"], [2, \"margin-top\", \"15px\", \"display\", \"inline-block\", \"margin-right\", \"10px\", \"padding\", \"8px 16px\", \"background-color\", \"#1c1c1e\", \"color\", \"white\", \"border\", \"1px solid #4d7bff\", \"border-radius\", \"20px\", \"text-decoration\", \"none\", \"font-size\", \"14px\", \"font-weight\", \"600\", \"transition\", \"all 0.3s ease\", 3, \"routerLink\"], [2, \"margin-right\", \"5px\"], [1, \"remove-button\", 3, \"click\"], [3, \"src\", \"alt\"], [1, \"profile-bio\"], [1, \"category-card\"], [1, \"category-header\"], [1, \"category-icon\"], [1, \"category-name\"], [1, \"progress-container\"], [1, \"progress-bar\"], [1, \"xp-text\"]],\n  template: function FriendProfilePage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\")(2, \"div\", 1);\n      i0.ɵɵelement(3, \"img\", 2);\n      i0.ɵɵelementStart(4, \"span\");\n      i0.ɵɵtext(5, \"Upshift\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(6, \"h1\");\n      i0.ɵɵtext(7, \"Friend Profile\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(8, \"a\", 3);\n      i0.ɵɵlistener(\"click\", function FriendProfilePage_Template_a_click_8_listener() {\n        return ctx.goBack();\n      });\n      i0.ɵɵtext(9, \"\\u2190 Go back\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(10, FriendProfilePage_div_10_Template, 34, 13, \"div\", 4);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(10);\n      i0.ɵɵproperty(\"ngIf\", ctx.friend);\n    }\n  },\n  dependencies: [IonicModule, i1.RouterLinkWithHrefDelegate, CommonModule, i2.NgForOf, i2.NgIf, FormsModule, RouterModule, i3.RouterLink],\n  styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%];\\n\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\tvar __webpack_modules__ = ({\\n\\n\\n 87:\\n\\n\\n\\n\\n\\n (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/sass-loader/dist/cjs.js):\\\\nexpected \\\\\\\"}\\\\\\\".\\\\n    \\u2577\\\\n452 \\u2502 }\\\\n    \\u2502  ^\\\\n    \\u2575\\\\n  src\\\\\\\\app\\\\\\\\pages\\\\\\\\friends\\\\\\\\friend-profile\\\\\\\\friend-profile.page.scss 452:2  root stylesheet\\\");\\n\\n\\n })\\n\\n\\n \\t})[_ngcontent-%COMP%];\\n\\n\\n\\n \\t\\n\\n \\t//[_ngcontent-%COMP%]   startup\\n\\n[_ngcontent-%COMP%]   //[_ngcontent-%COMP%]   Load[_ngcontent-%COMP%]   entry[_ngcontent-%COMP%]   module[_ngcontent-%COMP%]   and[_ngcontent-%COMP%]   return[_ngcontent-%COMP%]   exports\\n\\n[_ngcontent-%COMP%]   //[_ngcontent-%COMP%]   This[_ngcontent-%COMP%]   entry[_ngcontent-%COMP%]   module[_ngcontent-%COMP%]   doesn't[_ngcontent-%COMP%]   tell[_ngcontent-%COMP%]   about[_ngcontent-%COMP%]   it's[_ngcontent-%COMP%]   top-level[_ngcontent-%COMP%]   declarations[_ngcontent-%COMP%]   so[_ngcontent-%COMP%]   it[_ngcontent-%COMP%]   can't[_ngcontent-%COMP%]   be[_ngcontent-%COMP%]   inlined\\n\\n[_ngcontent-%COMP%]   var[_ngcontent-%COMP%]   __webpack_exports__[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] {};\\n\\n \\t__webpack_modules__[87]();\\n\\n \\tresource = __webpack_exports__;\\n\\n \\t\\n\\n })()\\n;\"]\n});", "map": {"version": 3, "names": ["inject", "CommonModule", "FormsModule", "IonicModule", "RouterModule", "ActivatedRoute", "Router", "FriendService", "UserService", "of", "switchMap", "take", "SupabaseService", "XpService", "EntityType", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r1", "friend", "profile_picture", "ɵɵsanitizeUrl", "username", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "bio", "category_r3", "icon", "name", "ɵɵstyleProp", "progress", "color", "ɵɵtextInterpolate1", "current_xp", "required_xp", "ɵɵtemplate", "FriendProfilePage_div_10_img_3_Template", "FriendProfilePage_div_10_ng_container_4_Template", "FriendProfilePage_div_10_div_16_Template", "FriendProfilePage_div_10_div_17_Template", "FriendProfilePage_div_10_div_21_Template", "ɵɵlistener", "FriendProfilePage_div_10_Template_a_click_32_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "removeFriend", "level", "title", "categories", "nextLevel", "ɵɵpureFunction1", "_c0", "id", "FriendProfilePage", "constructor", "currentUserId", "friendId", "supabaseService", "friendService", "userService", "route", "router", "xpService", "ngOnInit", "paramMap", "pipe", "params", "get", "currentUser$", "authUser", "getUser", "subscribe", "calculateXpProgress", "navigate", "USER", "result", "next_level", "_this", "_asyncToGenerator", "confirm", "error", "alert", "goBack", "window", "history", "back", "selectors", "decls", "vars", "consts", "template", "FriendProfilePage_Template", "rf", "ctx", "FriendProfilePage_Template_a_click_8_listener", "FriendProfilePage_div_10_Template", "i1", "RouterLinkWithHrefDelegate", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "RouterLink", "styles"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\friends\\friend-profile\\friend-profile.page.ts", "C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\friends\\friend-profile\\friend-profile.page.html"], "sourcesContent": ["import { Component, OnInit, inject } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { RouterModule, ActivatedRoute, Router } from '@angular/router';\r\nimport { FriendService } from '../../../services/friend.service';\r\nimport { UserService } from '../../../services/user.service';\r\nimport { User } from '../../../models/user.model';\r\nimport { Observable, Subscription, map, of, switchMap, take, firstValueFrom } from 'rxjs';\r\nimport { SupabaseService } from '../../../services/supabase.service';\r\nimport { XpService, EntityType } from '../../../services/xp.service';\r\n\r\ninterface CategoryDisplay {\r\n  name: string;\r\n  icon: string;\r\n  color: string;\r\n  current_xp: number;\r\n  required_xp: number;\r\n  progress: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-friend-profile',\r\n  templateUrl: './friend-profile.page.html',\r\n  styleUrls: ['./friend-profile.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule, FormsModule, RouterModule]\r\n})\r\nexport class FriendProfilePage implements OnInit {\r\n  currentUserId: string | null = null;\r\n  friend: User | null = null;\r\n  friendId: string | null = null;\r\n\r\n  categories: CategoryDisplay[] = [];\r\n  nextLevel = 0;\r\n\r\n  private supabaseService = inject(SupabaseService);\r\n  private friendService = inject(FriendService);\r\n  private userService = inject(UserService);\r\n  private route = inject(ActivatedRoute);\r\n  private router = inject(Router);\r\n  private xpService = inject(XpService);\r\n\r\n  constructor() {}\r\n\r\n  ngOnInit() {\r\n    this.route.paramMap.pipe(\r\n      take(1),\r\n      switchMap(params => {\r\n        this.friendId = params.get('id');\r\n\r\n        return this.supabaseService.currentUser$;\r\n      }),\r\n      switchMap(authUser => {\r\n        if (authUser) {\r\n          this.currentUserId = authUser.id;\r\n\r\n          if (this.friendId) {\r\n            return this.userService.getUser(this.friendId);\r\n          }\r\n        }\r\n        return of(null);\r\n      })\r\n    ).subscribe(friend => {\r\n      if (friend) {\r\n        this.friend = friend;\r\n        this.calculateXpProgress();\r\n      } else {\r\n        this.router.navigate(['/friends']);\r\n      }\r\n    });\r\n  }\r\n\r\n  calculateXpProgress() {\r\n    if (!this.friend) return;\r\n\r\n    this.xpService.calculateXpProgress(this.friend, EntityType.USER).subscribe(result => {\r\n      if (result) {\r\n        this.categories = result.categories;\r\n        this.nextLevel = result.next_level;\r\n      }\r\n    });\r\n  }\r\n\r\n  async removeFriend() {\r\n    if (!this.currentUserId || !this.friendId) return;\r\n\r\n    if (confirm('Are you sure you want to remove this friend?')) {\r\n      try {\r\n        await this.friendService.removeFriend(this.currentUserId, this.friendId);\r\n        this.router.navigate(['/friends']);\r\n      } catch (error) {\r\n        alert('Failed to remove friend. Please try again.');\r\n      }\r\n    }\r\n  }\r\n  goBack() {\r\n    window.history.back();\r\n  }\r\n}\r\n", "<!-- Exact HTML from Django template with Angular syntax -->\r\n<div class=\"container\">\r\n    <header>\r\n        <div class=\"logo\">\r\n            <img src=\"assets/images/upshift_icon_mini.svg\" alt=\"Upshift\">\r\n            <span>Upshift</span>\r\n        </div>\r\n        <h1>Friend Profile</h1>\r\n    </header>\r\n\r\n    <a href=\"javascript:void(0)\" (click)=\"goBack()\"  class=\"back-link\">&larr; Go back</a>\r\n\r\n    <div class=\"profile-container\" *ngIf=\"friend\">\r\n        <div class=\"profile-header\">\r\n            <div class=\"profile-picture\">\r\n                <img *ngIf=\"friend.profile_picture\" [src]=\"friend.profile_picture\" [alt]=\"friend.username\">\r\n                <ng-container *ngIf=\"!friend.profile_picture\">👤</ng-container>\r\n            </div>\r\n            <div class=\"profile-info\">\r\n                <div class=\"profile-name\">{{ friend.name }}</div>\r\n                <div class=\"profile-username\">&#64;{{ friend.username }}</div>\r\n\r\n                <div class=\"profile-level\">\r\n                    <div class=\"level-badge\">Level {{ friend.level }}</div>\r\n                    <div class=\"profile-title\">{{ friend.title }}</div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"profile-bio-container\" style=\"padding-left: 100px;\">\r\n            <div class=\"profile-bio\" *ngIf=\"friend.bio\">{{ friend.bio }}</div>\r\n            <div class=\"profile-bio\" *ngIf=\"!friend.bio\">No bio provided</div>\r\n        </div>\r\n\r\n        <div class=\"xp-section\">\r\n            <h2>XP Progress</h2>\r\n\r\n            <div class=\"category-card\" *ngFor=\"let category of categories\">\r\n                <div class=\"category-header\">\r\n                    <div class=\"category-icon\">{{ category.icon }}</div>\r\n                    <div class=\"category-name\">{{ category.name }}</div>\r\n                </div>\r\n                <div class=\"progress-container\">\r\n                    <div class=\"progress-bar\" [style.width.%]=\"category.progress\" [style.background-color]=\"category.color\"></div>\r\n                </div>\r\n                <div class=\"xp-text\">\r\n                    <span>{{ category.current_xp }} XP</span>\r\n                    <span>{{ category.required_xp }} XP needed</span>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"next-level-info\">\r\n                <div class=\"next-level-text\">Next Level: {{ nextLevel }}</div>\r\n                <div class=\"next-level-requirements\">\r\n                    Reach required XP in all categories to level up\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"button-container\">\r\n            <a [routerLink]=\"['/badges', friend.id]\" style=\"margin-top: 15px; display: inline-block; margin-right: 10px; padding: 8px 16px; background-color: #1c1c1e; color: white; border: 1px solid #4d7bff; border-radius: 20px; text-decoration: none; font-size: 14px; font-weight: 600; transition: all 0.3s ease;\">\r\n                <span style=\"margin-right: 5px;\">🏆</span> View Badges\r\n            </a>\r\n            <a (click)=\"removeFriend()\" class=\"remove-button\">Remove Friend</a>\r\n        </div>\r\n    </div>\r\n</div>\r\n"], "mappings": ";;AAAA,SAA4BA,MAAM,QAAQ,eAAe;AACzD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,EAAEC,cAAc,EAAEC,MAAM,QAAQ,iBAAiB;AACtE,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,WAAW,QAAQ,gCAAgC;AAE5D,SAAwCC,EAAE,EAAEC,SAAS,EAAEC,IAAI,QAAwB,MAAM;AACzF,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,SAAS,EAAEC,UAAU,QAAQ,8BAA8B;;;;;;;;ICKpDC,EAAA,CAAAC,SAAA,cAA2F;;;;IAAxBD,EAA/B,CAAAE,UAAA,QAAAC,MAAA,CAAAC,MAAA,CAAAC,eAAA,EAAAL,EAAA,CAAAM,aAAA,CAA8B,QAAAH,MAAA,CAAAC,MAAA,CAAAG,QAAA,CAAwB;;;;;IAC1FP,EAAA,CAAAQ,uBAAA,GAA8C;IAAAR,EAAA,CAAAS,MAAA,mBAAE;;;;;;IAapDT,EAAA,CAAAU,cAAA,cAA4C;IAAAV,EAAA,CAAAS,MAAA,GAAgB;IAAAT,EAAA,CAAAW,YAAA,EAAM;;;;IAAtBX,EAAA,CAAAY,SAAA,EAAgB;IAAhBZ,EAAA,CAAAa,iBAAA,CAAAV,MAAA,CAAAC,MAAA,CAAAU,GAAA,CAAgB;;;;;IAC5Dd,EAAA,CAAAU,cAAA,cAA6C;IAAAV,EAAA,CAAAS,MAAA,sBAAe;IAAAT,EAAA,CAAAW,YAAA,EAAM;;;;;IAQ1DX,EAFR,CAAAU,cAAA,cAA+D,cAC9B,cACE;IAAAV,EAAA,CAAAS,MAAA,GAAmB;IAAAT,EAAA,CAAAW,YAAA,EAAM;IACpDX,EAAA,CAAAU,cAAA,cAA2B;IAAAV,EAAA,CAAAS,MAAA,GAAmB;IAClDT,EADkD,CAAAW,YAAA,EAAM,EAClD;IACNX,EAAA,CAAAU,cAAA,cAAgC;IAC5BV,EAAA,CAAAC,SAAA,cAA8G;IAClHD,EAAA,CAAAW,YAAA,EAAM;IAEFX,EADJ,CAAAU,cAAA,cAAqB,WACX;IAAAV,EAAA,CAAAS,MAAA,IAA4B;IAAAT,EAAA,CAAAW,YAAA,EAAO;IACzCX,EAAA,CAAAU,cAAA,YAAM;IAAAV,EAAA,CAAAS,MAAA,IAAoC;IAElDT,EAFkD,CAAAW,YAAA,EAAO,EAC/C,EACJ;;;;IAV6BX,EAAA,CAAAY,SAAA,GAAmB;IAAnBZ,EAAA,CAAAa,iBAAA,CAAAE,WAAA,CAAAC,IAAA,CAAmB;IACnBhB,EAAA,CAAAY,SAAA,GAAmB;IAAnBZ,EAAA,CAAAa,iBAAA,CAAAE,WAAA,CAAAE,IAAA,CAAmB;IAGpBjB,EAAA,CAAAY,SAAA,GAAmC;IAACZ,EAApC,CAAAkB,WAAA,UAAAH,WAAA,CAAAI,QAAA,MAAmC,qBAAAJ,WAAA,CAAAK,KAAA,CAA0C;IAGjGpB,EAAA,CAAAY,SAAA,GAA4B;IAA5BZ,EAAA,CAAAqB,kBAAA,KAAAN,WAAA,CAAAO,UAAA,QAA4B;IAC5BtB,EAAA,CAAAY,SAAA,GAAoC;IAApCZ,EAAA,CAAAqB,kBAAA,KAAAN,WAAA,CAAAQ,WAAA,eAAoC;;;;;;IAhClDvB,EAFR,CAAAU,cAAA,aAA8C,aACd,aACK;IAEzBV,EADA,CAAAwB,UAAA,IAAAC,uCAAA,iBAA2F,IAAAC,gDAAA,0BAC7C;IAClD1B,EAAA,CAAAW,YAAA,EAAM;IAEFX,EADJ,CAAAU,cAAA,cAA0B,cACI;IAAAV,EAAA,CAAAS,MAAA,GAAiB;IAAAT,EAAA,CAAAW,YAAA,EAAM;IACjDX,EAAA,CAAAU,cAAA,cAA8B;IAAAV,EAAA,CAAAS,MAAA,GAA0B;IAAAT,EAAA,CAAAW,YAAA,EAAM;IAG1DX,EADJ,CAAAU,cAAA,eAA2B,eACE;IAAAV,EAAA,CAAAS,MAAA,IAAwB;IAAAT,EAAA,CAAAW,YAAA,EAAM;IACvDX,EAAA,CAAAU,cAAA,eAA2B;IAAAV,EAAA,CAAAS,MAAA,IAAkB;IAGzDT,EAHyD,CAAAW,YAAA,EAAM,EACjD,EACJ,EACJ;IACNX,EAAA,CAAAU,cAAA,eAAgE;IAE5DV,EADA,CAAAwB,UAAA,KAAAG,wCAAA,kBAA4C,KAAAC,wCAAA,kBACC;IACjD5B,EAAA,CAAAW,YAAA,EAAM;IAGFX,EADJ,CAAAU,cAAA,eAAwB,UAChB;IAAAV,EAAA,CAAAS,MAAA,mBAAW;IAAAT,EAAA,CAAAW,YAAA,EAAK;IAEpBX,EAAA,CAAAwB,UAAA,KAAAK,wCAAA,mBAA+D;IAe3D7B,EADJ,CAAAU,cAAA,eAA6B,eACI;IAAAV,EAAA,CAAAS,MAAA,IAA2B;IAAAT,EAAA,CAAAW,YAAA,EAAM;IAC9DX,EAAA,CAAAU,cAAA,eAAqC;IACjCV,EAAA,CAAAS,MAAA,yDACJ;IAERT,EAFQ,CAAAW,YAAA,EAAM,EACJ,EACJ;IAIEX,EAFR,CAAAU,cAAA,eAA8B,aACqR,gBAC1Q;IAAAV,EAAA,CAAAS,MAAA,oBAAE;IAAAT,EAAA,CAAAW,YAAA,EAAO;IAACX,EAAA,CAAAS,MAAA,qBAC/C;IAAAT,EAAA,CAAAW,YAAA,EAAI;IACJX,EAAA,CAAAU,cAAA,aAAkD;IAA/CV,EAAA,CAAA8B,UAAA,mBAAAC,sDAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAAC,GAAA;MAAA,MAAA9B,MAAA,GAAAH,EAAA,CAAAkC,aAAA;MAAA,OAAAlC,EAAA,CAAAmC,WAAA,CAAShC,MAAA,CAAAiC,YAAA,EAAc;IAAA,EAAC;IAAuBpC,EAAA,CAAAS,MAAA,qBAAa;IAEvET,EAFuE,CAAAW,YAAA,EAAI,EACjE,EACJ;;;;IAjDYX,EAAA,CAAAY,SAAA,GAA4B;IAA5BZ,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAC,MAAA,CAAAC,eAAA,CAA4B;IACnBL,EAAA,CAAAY,SAAA,EAA6B;IAA7BZ,EAAA,CAAAE,UAAA,UAAAC,MAAA,CAAAC,MAAA,CAAAC,eAAA,CAA6B;IAGlBL,EAAA,CAAAY,SAAA,GAAiB;IAAjBZ,EAAA,CAAAa,iBAAA,CAAAV,MAAA,CAAAC,MAAA,CAAAa,IAAA,CAAiB;IACbjB,EAAA,CAAAY,SAAA,GAA0B;IAA1BZ,EAAA,CAAAqB,kBAAA,MAAAlB,MAAA,CAAAC,MAAA,CAAAG,QAAA,KAA0B;IAG3BP,EAAA,CAAAY,SAAA,GAAwB;IAAxBZ,EAAA,CAAAqB,kBAAA,WAAAlB,MAAA,CAAAC,MAAA,CAAAiC,KAAA,KAAwB;IACtBrC,EAAA,CAAAY,SAAA,GAAkB;IAAlBZ,EAAA,CAAAa,iBAAA,CAAAV,MAAA,CAAAC,MAAA,CAAAkC,KAAA,CAAkB;IAK3BtC,EAAA,CAAAY,SAAA,GAAgB;IAAhBZ,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAC,MAAA,CAAAU,GAAA,CAAgB;IAChBd,EAAA,CAAAY,SAAA,EAAiB;IAAjBZ,EAAA,CAAAE,UAAA,UAAAC,MAAA,CAAAC,MAAA,CAAAU,GAAA,CAAiB;IAMKd,EAAA,CAAAY,SAAA,GAAa;IAAbZ,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAoC,UAAA,CAAa;IAe5BvC,EAAA,CAAAY,SAAA,GAA2B;IAA3BZ,EAAA,CAAAqB,kBAAA,iBAAAlB,MAAA,CAAAqC,SAAA,KAA2B;IAQzDxC,EAAA,CAAAY,SAAA,GAAqC;IAArCZ,EAAA,CAAAE,UAAA,eAAAF,EAAA,CAAAyC,eAAA,KAAAC,GAAA,EAAAvC,MAAA,CAAAC,MAAA,CAAAuC,EAAA,EAAqC;;;AD/BpD,OAAM,MAAOC,iBAAiB;EAe5BC,YAAA;IAdA,KAAAC,aAAa,GAAkB,IAAI;IACnC,KAAA1C,MAAM,GAAgB,IAAI;IAC1B,KAAA2C,QAAQ,GAAkB,IAAI;IAE9B,KAAAR,UAAU,GAAsB,EAAE;IAClC,KAAAC,SAAS,GAAG,CAAC;IAEL,KAAAQ,eAAe,GAAG/D,MAAM,CAACY,eAAe,CAAC;IACzC,KAAAoD,aAAa,GAAGhE,MAAM,CAACO,aAAa,CAAC;IACrC,KAAA0D,WAAW,GAAGjE,MAAM,CAACQ,WAAW,CAAC;IACjC,KAAA0D,KAAK,GAAGlE,MAAM,CAACK,cAAc,CAAC;IAC9B,KAAA8D,MAAM,GAAGnE,MAAM,CAACM,MAAM,CAAC;IACvB,KAAA8D,SAAS,GAAGpE,MAAM,CAACa,SAAS,CAAC;EAEtB;EAEfwD,QAAQA,CAAA;IACN,IAAI,CAACH,KAAK,CAACI,QAAQ,CAACC,IAAI,CACtB5D,IAAI,CAAC,CAAC,CAAC,EACPD,SAAS,CAAC8D,MAAM,IAAG;MACjB,IAAI,CAACV,QAAQ,GAAGU,MAAM,CAACC,GAAG,CAAC,IAAI,CAAC;MAEhC,OAAO,IAAI,CAACV,eAAe,CAACW,YAAY;IAC1C,CAAC,CAAC,EACFhE,SAAS,CAACiE,QAAQ,IAAG;MACnB,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAACd,aAAa,GAAGc,QAAQ,CAACjB,EAAE;QAEhC,IAAI,IAAI,CAACI,QAAQ,EAAE;UACjB,OAAO,IAAI,CAACG,WAAW,CAACW,OAAO,CAAC,IAAI,CAACd,QAAQ,CAAC;QAChD;MACF;MACA,OAAOrD,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH,CAACoE,SAAS,CAAC1D,MAAM,IAAG;MACnB,IAAIA,MAAM,EAAE;QACV,IAAI,CAACA,MAAM,GAAGA,MAAM;QACpB,IAAI,CAAC2D,mBAAmB,EAAE;MAC5B,CAAC,MAAM;QACL,IAAI,CAACX,MAAM,CAACY,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;MACpC;IACF,CAAC,CAAC;EACJ;EAEAD,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAAC3D,MAAM,EAAE;IAElB,IAAI,CAACiD,SAAS,CAACU,mBAAmB,CAAC,IAAI,CAAC3D,MAAM,EAAEL,UAAU,CAACkE,IAAI,CAAC,CAACH,SAAS,CAACI,MAAM,IAAG;MAClF,IAAIA,MAAM,EAAE;QACV,IAAI,CAAC3B,UAAU,GAAG2B,MAAM,CAAC3B,UAAU;QACnC,IAAI,CAACC,SAAS,GAAG0B,MAAM,CAACC,UAAU;MACpC;IACF,CAAC,CAAC;EACJ;EAEM/B,YAAYA,CAAA;IAAA,IAAAgC,KAAA;IAAA,OAAAC,iBAAA;MAChB,IAAI,CAACD,KAAI,CAACtB,aAAa,IAAI,CAACsB,KAAI,CAACrB,QAAQ,EAAE;MAE3C,IAAIuB,OAAO,CAAC,8CAA8C,CAAC,EAAE;QAC3D,IAAI;UACF,MAAMF,KAAI,CAACnB,aAAa,CAACb,YAAY,CAACgC,KAAI,CAACtB,aAAa,EAAEsB,KAAI,CAACrB,QAAQ,CAAC;UACxEqB,KAAI,CAAChB,MAAM,CAACY,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;QACpC,CAAC,CAAC,OAAOO,KAAK,EAAE;UACdC,KAAK,CAAC,4CAA4C,CAAC;QACrD;MACF;IAAC;EACH;EACAC,MAAMA,CAAA;IACJC,MAAM,CAACC,OAAO,CAACC,IAAI,EAAE;EACvB;;qBAtEWhC,iBAAiB;;mCAAjBA,kBAAiB;AAAA;;QAAjBA,kBAAiB;EAAAiC,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCzBtBnF,EAFR,CAAAU,cAAA,aAAuB,aACX,aACc;MACdV,EAAA,CAAAC,SAAA,aAA6D;MAC7DD,EAAA,CAAAU,cAAA,WAAM;MAAAV,EAAA,CAAAS,MAAA,cAAO;MACjBT,EADiB,CAAAW,YAAA,EAAO,EAClB;MACNX,EAAA,CAAAU,cAAA,SAAI;MAAAV,EAAA,CAAAS,MAAA,qBAAc;MACtBT,EADsB,CAAAW,YAAA,EAAK,EAClB;MAETX,EAAA,CAAAU,cAAA,WAAmE;MAAtCV,EAAA,CAAA8B,UAAA,mBAAAuD,8CAAA;QAAA,OAASD,GAAA,CAAAX,MAAA,EAAQ;MAAA,EAAC;MAAoBzE,EAAA,CAAAS,MAAA,qBAAc;MAAAT,EAAA,CAAAW,YAAA,EAAI;MAErFX,EAAA,CAAAwB,UAAA,KAAA8D,iCAAA,mBAA8C;MAqDlDtF,EAAA,CAAAW,YAAA,EAAM;;;MArD8BX,EAAA,CAAAY,SAAA,IAAY;MAAZZ,EAAA,CAAAE,UAAA,SAAAkF,GAAA,CAAAhF,MAAA,CAAY;;;iBDcpChB,WAAW,EAAAmG,EAAA,CAAAC,0BAAA,EAAEtG,YAAY,EAAAuG,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAExG,WAAW,EAAEE,YAAY,EAAAuG,EAAA,CAAAC,UAAA;EAAAC,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}