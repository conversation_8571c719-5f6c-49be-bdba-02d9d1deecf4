﻿
async fetchExistingGroupSideQuests() {
  this.importStatus = 'Fetching existing group side quests from Supabase...';

  try {
    const { data, error } = await this.supabaseService.getClient()
      .from('group_sidequest_pool')
      .select('*')
      .order('id', { ascending: true });

    if (error) {
      this.importStatus = `Error fetching existing group side quests: ${error.message}`;
      return;
    }

    if (data && data.length > 0) {
      this.existingGroupSideQuests = data;
      this.importStatus = `Found ${data.length} existing group side quests in Supabase.`;
    } else {
      this.importStatus = 'No existing group side quests found in Supabase.';
    }
  } catch (error: any) {
    this.importStatus = `Error fetching existing group side quests: ${error.message}`;
  }
}

toggleGroupJsonImport() {
  this.showGroupJsonImport = !this.showGroupJsonImport;
  if (this.showGroupJsonImport) {
    this.fetchJsonGroupSideQuests();
  }
}

toggleGroupAddForm() {
  this.showGroupAddForm = !this.showGroupAddForm;
}

fetchJsonGroupSideQuests() {
  this.importStatus = 'Fetching group side quests from JSON...';

  this.http.get<GroupSideQuestPool[]>('assets/data/group-sidequest-pool.json').subscribe({
    next: (data) => {
      if (!data || data.length === 0) {
        this.importStatus = 'No group side quests found in JSON file';
        return;
      }

      this.jsonGroupSideQuests = data;
      this.importStatus = `Found ${data.length} group side quests in JSON file. Ready to import.`;
    },
    error: (error) => {
      this.importStatus = `Error fetching group side quests JSON: ${error.message}`;
    }
  });
}

async importJsonGroupSideQuests() {
  if (this.jsonGroupSideQuests.length === 0) {
    this.importStatus = 'No group side quests to import from JSON';
    return;
  }

  this.isImporting = true;
  this.importStatus = 'Importing group side quests from JSON...';

  try {
    const groupSideQuestsToImport = this.jsonGroupSideQuests.map(quest => {
      const { id, ...questWithoutId } = quest;
      return {
        ...questWithoutId,
        active: true
      };
    });


    const { error } = await this.supabaseService.getClient()
      .from('group_sidequest_pool')
      .insert(groupSideQuestsToImport);

    if (error) {
      this.importStatus = `Error importing group side quests: ${error.message}`;
      this.isImporting = false;
      return;
    }

    this.importStatus = 'Group side quests imported successfully';
    this.isImporting = false;

    this.fetchExistingGroupSideQuests();
  } catch (error: any) {
    this.importStatus = `Error importing group side quests: ${error.message}`;
    this.isImporting = false;
  }
}

async addGroupSideQuest() {
  if (!this.groupSideQuestForm.valid) {
    this.importStatus = 'Please fill out all required fields correctly';
    return;
  }

  this.isImporting = true;
  this.importStatus = 'Adding new group side quest...';

  try {
    const newGroupSideQuest = {
      ...this.groupSideQuestForm.value,
      active: true
    };


    const { error } = await this.supabaseService.getClient()
      .from('group_sidequest_pool')
      .insert([newGroupSideQuest]);

    if (error) {
      this.importStatus = `Error adding group side quest: ${error.message}`;
      this.isImporting = false;
      return;
    }

    this.importStatus = 'Group side quest added successfully';

    this.resetGroupForm();

    this.fetchExistingGroupSideQuests();
  } catch (error: any) {
    this.importStatus = `Error adding group side quest: ${error.message}`;
  } finally {
    this.isImporting = false;
  }
}

async toggleGroupSideQuestActive(quest: GroupSideQuestPool) {
  try {
    const { error } = await this.supabaseService.getClient()
      .from('group_sidequest_pool')
      .update({ active: !quest.active })
      .eq('id', quest.id);

    if (error) {
      this.importStatus = `Error updating group side quest: ${error.message}`;
      return;
    }

    this.importStatus = 'Group side quest updated successfully';

    quest.active = !quest.active;
  } catch (error: any) {
    this.importStatus = `Error updating group side quest: ${error.message}`;
  }
}

async deleteGroupSideQuest(questId: string) {
  if (!questId) {
    this.importStatus = 'Cannot delete quest: Invalid quest ID';
    return;
  }

  if (!confirm('Are you sure you want to delete this group side quest?')) {
    return;
  }

  try {
    const { error } = await this.supabaseService.getClient()
      .from('group_sidequest_pool')
      .delete()
      .eq('id', questId);

    if (error) {
      this.importStatus = `Error deleting group side quest: ${error.message}`;
      return;
    }

    this.importStatus = 'Group side quest deleted successfully';

    this.fetchExistingGroupSideQuests();
  } catch (error: any) {
    this.importStatus = `Error deleting group side quest: ${error.message}`;
  }
}
