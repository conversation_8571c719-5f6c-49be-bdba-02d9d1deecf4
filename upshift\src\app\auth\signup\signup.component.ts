﻿import { Component, inject } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { SupabaseService } from '../../services/supabase.service';
import {RouterLink} from "@angular/router";

@Component({
  selector: 'app-signup',
  standalone: true,
  imports: [IonicModule, CommonModule, ReactiveFormsModule, RouterLink],
  templateUrl: './signup.component.html',
  styleUrls: ['signup.component.scss'],
})
export class SignupComponent {
  loading = false;
  error: string | null = null;

  private supabaseService = inject(SupabaseService);

  constructor() {
  }

  async signInWithGoogle() {
    this.loading = true;
    this.error = null;
    try {
      const { error } = await this.supabaseService.getClient().auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: '/',
        }
      });
      if (error) throw error;
    } catch (err: any) {
      this.error = err.message || 'Google login failed';
    } finally {
      this.loading = false;
    }
  }

  async signInWithApple() {
    this.loading = true;
    this.error = null;
    try {
      const { error } = await this.supabaseService.getClient().auth.signInWithOAuth({
        provider: 'apple',
        options: {
          redirectTo: '/', 
        }
      });
      if (error) throw error;
    } catch (err: any) {
      this.error = err.message || 'Apple login failed';
    } finally {
      this.loading = false;
    }
  }
}
