﻿import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { GoalService } from '../../../services/goal.service';
import { Goal, GoalJournalEntry, MicroGoal } from '../../../models/goal.model';
import { take } from 'rxjs';
import { SupabaseService } from '../../../services/supabase.service';

@Component({
  selector: 'app-goal-detail',
  templateUrl: './goal-detail.page.html',
  styleUrls: ['./goal-detail.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule]
})
export class GoalDetailPage implements OnInit {
  userId: string | null = null;

  goalId: string | null = null;
  goal: Goal | null = null;
  microgoals: MicroGoal[] = [];
  journalEntries: GoalJournalEntry[] = [];

  showDescriptionForm = false;
  editedDescription = '';
  currentValue = 0;
  progressPercent = 0;
  showJournalModal = false;
  nextMilestone: number | null = null;
  journalContent = '';

  newMicrogoalTitle = '';

  private supabaseService = inject(SupabaseService);
  private goalService = inject(GoalService);
  private route = inject(ActivatedRoute);
  private router = inject(Router);

  constructor() {}

  ngOnInit() {
    this.supabaseService.currentUser$.pipe(
      take(1)
    ).subscribe(user => {
      if (user) {
        this.userId = user.id;

        this.route.paramMap.pipe(
          take(1)
        ).subscribe(params => {
          this.goalId = params.get('id');
          if (this.goalId) {
            this.loadGoal();
            this.loadMicrogoals();
            this.loadJournalEntries();
          }
        });
      }
    });
  }

  loadGoal() {
    if (!this.goalId) return;

    this.goalService.getGoal(this.goalId).pipe(
      take(1)
    ).subscribe(goal => {
      if (goal) {
        this.goal = goal;
        this.editedDescription = goal.description;
        this.currentValue = goal.current_value;
        this.calculateProgress();
        this.checkForMilestone();
      } else {
      }
    });
  }

  loadMicrogoals() {
    if (!this.goalId) return;

    this.goalService.getMicroGoals(this.goalId).subscribe(microgoals => {
      this.microgoals = microgoals;
    });
  }

  loadJournalEntries() {
    if (!this.goalId) return;

    this.goalService.getJournalEntries(this.goalId).subscribe(entries => {
      this.journalEntries = entries;
      this.checkForMilestone();
    });
  }

  calculateProgress() {
    if (!this.goal) return;

    this.progressPercent = this.goal.goal_value > 0
      ? Math.min(100, Math.round((this.goal.current_value / this.goal.goal_value) * 100))
      : 0;
  }

  checkForMilestone() {
    if (!this.goal) return;

    const currentPercent = this.progressPercent;

    const milestones = [20, 40, 60, 80, 100];

    const existingMilestones = this.journalEntries.map(entry => entry.milestone_percentage);

    const reachedMilestones = milestones.filter(milestone =>
      currentPercent >= milestone &&
      !existingMilestones.includes(milestone)
    );


    if (reachedMilestones.length > 0) {
      this.nextMilestone = reachedMilestones[0];
      this.showJournalModal = true;
    } else {
      this.nextMilestone = null;
      this.showJournalModal = false;
    }
  }

  updateDescription() {
    if (!this.goalId || !this.goal) return;

    this.goalService.updateGoal(this.goalId, {
      description: this.editedDescription
    }).then(() => {
      if (this.goal) {
        this.goal.description = this.editedDescription;
      }
      this.showDescriptionForm = false;
    });
  }

  cancelEditDescription() {
    if (this.goal) {
      this.editedDescription = this.goal.description;
    }
    this.showDescriptionForm = false;
  }

  updateProgress() {
    if (!this.goalId || !this.goal) return;


    this.goalService.updateGoal(this.goalId, {
      current_value: this.currentValue
    }).then(() => {
      if (this.goal) {
        this.goal.current_value = this.currentValue;
        this.calculateProgress();

        this.loadJournalEntries();
      }
    }).catch(error => {
    });
  }

  toggleMicrogoal(microgoal: MicroGoal) {
    if (!microgoal.id) return;

    this.goalService.toggleMicroGoalCompletion(microgoal.id).then(() => {
      microgoal.completed = !microgoal.completed;
      microgoal.completed_at = microgoal.completed ? new Date() : undefined;
    });
  }

  deleteMicrogoal(microgoal: MicroGoal) {
    if (!microgoal.id) return;

    this.goalService.deleteMicroGoal(microgoal.id).then(() => {
      this.microgoals = this.microgoals.filter(m => m.id !== microgoal.id);
    });
  }

  addMicrogoal() {
    if (!this.goalId || !this.newMicrogoalTitle.trim()) return;

    const newMicrogoal: Omit<MicroGoal, 'id'> = {
      goal_id: this.goalId,
      title: this.newMicrogoalTitle.trim(),
      completed: false
    };

    this.goalService.createMicroGoal(newMicrogoal).then(id => {
      this.microgoals.push({
        ...newMicrogoal,
        id
      });

      this.newMicrogoalTitle = '';
    });
  }

  addJournalEntry() {
    if (!this.goalId || !this.nextMilestone || !this.journalContent.trim()) return;

    const newEntry: Omit<GoalJournalEntry, 'id' | 'created_at'> = {
      goal_id: this.goalId,
      milestone_percentage: this.nextMilestone,
      content: this.journalContent.trim()
    };

    this.goalService.createJournalEntry(newEntry).then(id => {

      this.showJournalModal = false;
      this.journalContent = '';

      this.loadJournalEntries();
    }).catch(error => {
    });
  }

  confirmDeleteGoal() {
    if (!this.goalId) return;

    if (confirm('Are you sure you want to delete this goal? This action cannot be undone.')) {
      this.goalService.deleteGoal(this.goalId).then(() => {
        this.router.navigate(['/goals']);
      }).catch(error => {
      });
    }
  }
}
