{"ast": null, "code": "var _TimeTrackerPage;\nimport { inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { RouterModule } from '@angular/router';\nimport { TimeTrackerUnifiedService } from '../../services/time-tracker-unified.service';\nimport { take } from 'rxjs';\nimport { Chart, registerables } from 'chart.js';\nimport { SupabaseService } from '../../services/supabase.service';\nimport { EmojiInputDirective } from '../../directives/emoji-input.directive';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nconst _c0 = [\"timeChart\"];\nfunction TimeTrackerPage_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dayName_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(dayName_r1);\n  }\n}\nfunction TimeTrackerPage_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵlistener(\"click\", function TimeTrackerPage_div_17_Template_div_click_0_listener() {\n      const date_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(!date_r3.is_future && ctx_r3.selectDate(date_r3.date));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const date_r3 = ctx.$implicit;\n    i0.ɵɵclassProp(\"active\", date_r3.is_today)(\"selected\", date_r3.is_selected)(\"disabled\", date_r3.is_future);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", date_r3.day, \" \");\n  }\n}\nfunction TimeTrackerPage_option_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r5.id);\n    i0.ɵɵattribute(\"data-emoji\", type_r5.emoji)(\"data-name\", type_r5.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", type_r5.emoji, \" \", type_r5.name, \" \");\n  }\n}\nfunction TimeTrackerPage_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41)(2, \"div\", 42);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 43)(7, \"div\", 44)(8, \"input\", 45);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TimeTrackerPage_div_63_Template_input_ngModelChange_8_listener($event) {\n      const activity_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      i0.ɵɵtwoWayBindingSet(activity_r7.hours, $event) || (activity_r7.hours = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function TimeTrackerPage_div_63_Template_input_change_8_listener() {\n      const activity_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.updateActivity(activity_r7));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10, \"h\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"input\", 46);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TimeTrackerPage_div_63_Template_input_ngModelChange_11_listener($event) {\n      const activity_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      i0.ɵɵtwoWayBindingSet(activity_r7.minutes, $event) || (activity_r7.minutes = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function TimeTrackerPage_div_63_Template_input_change_11_listener() {\n      const activity_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.updateActivity(activity_r7));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13, \"m\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function TimeTrackerPage_div_63_Template_button_click_14_listener() {\n      const activity_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.deleteActivity(activity_r7.id));\n    });\n    i0.ɵɵtext(15, \"\\u00D7\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const activity_r7 = ctx.$implicit;\n    i0.ɵɵattribute(\"data-id\", activity_r7.id);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(activity_r7.emoji);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r7.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", activity_r7.hours);\n    i0.ɵɵattribute(\"data-activity-id\", activity_r7.id);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", activity_r7.minutes);\n    i0.ɵɵattribute(\"data-activity-id\", activity_r7.id);\n  }\n}\nChart.register(...registerables);\nexport class TimeTrackerPage {\n  constructor() {\n    this.userId = null;\n    this.dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];\n    this.weekDates = [];\n    this.weekOffset = 0;\n    this.selectedDate = new Date();\n    this.activityTypes = [];\n    this.activities = [];\n    this.selectedActivityId = '';\n    this.hoursInput = 0;\n    this.minutesInput = 0;\n    this.customActivityName = '';\n    this.customEmoji = '⚡';\n    this.customHoursInput = 0;\n    this.customMinutesInput = 0;\n    this.showStandardInput = false;\n    this.showCustomForm = false;\n    this.totalTrackedHours = '0.0';\n    this.remainingHours = '24.0';\n    this.timeChart = null;\n    this.supabaseService = inject(SupabaseService);\n    this.timeTrackerService = inject(TimeTrackerUnifiedService);\n  }\n  ngOnInit() {\n    this.supabaseService.currentUser$.pipe(take(1)).subscribe(authUser => {\n      if (authUser) {\n        this.userId = authUser.id;\n        this.loadActivityTypes();\n        this.generateWeekDates();\n        this.loadActivities();\n      }\n    });\n  }\n  ngAfterViewInit() {\n    setTimeout(() => {\n      this.initializeChart();\n    }, 500);\n  }\n  loadActivityTypes() {\n    this.timeTrackerService.getActivityTypes().subscribe(types => {\n      this.activityTypes = types;\n    });\n  }\n  generateWeekDates() {\n    const today = new Date();\n    const currentDay = today.getDay();\n    const mondayOffset = currentDay === 0 ? -6 : 1;\n    const startOfWeek = new Date(today);\n    startOfWeek.setDate(today.getDate() - currentDay + mondayOffset + 7 * this.weekOffset);\n    this.weekDates = [];\n    for (let i = 0; i < 7; i++) {\n      const date = new Date(startOfWeek);\n      date.setDate(startOfWeek.getDate() + i);\n      const dateStr = this.formatDate(date);\n      const isToday = this.isSameDay(date, today);\n      const isSelected = this.isSameDay(date, this.selectedDate);\n      const isFuture = date > today;\n      this.weekDates.push({\n        date: dateStr,\n        day: date.getDate(),\n        is_today: isToday,\n        is_selected: isSelected,\n        is_future: isFuture\n      });\n    }\n  }\n  formatDate(date) {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n  isSameDay(date1, date2) {\n    return date1.getFullYear() === date2.getFullYear() && date1.getMonth() === date2.getMonth() && date1.getDate() === date2.getDate();\n  }\n  changeWeek(offset) {\n    this.weekOffset += offset;\n    this.generateWeekDates();\n  }\n  selectDate(dateStr) {\n    this.selectedDate = new Date(dateStr);\n    this.generateWeekDates();\n    this.loadActivities();\n  }\n  loadActivities() {\n    if (!this.userId) return;\n    const dateStr = this.formatDate(this.selectedDate);\n    const userId = this.userId;\n    this.timeTrackerService.getDayTracking(userId, dateStr).subscribe(() => {\n      this.timeTrackerService.getActivities(userId, dateStr).subscribe(activities => {\n        this.activities = activities;\n        this.calculateTotals();\n        this.updateChart();\n      });\n    });\n  }\n  calculateTotals() {\n    const totalHours = this.activities.reduce((total, activity) => {\n      return total + activity.hours + activity.minutes / 60;\n    }, 0);\n    this.totalTrackedHours = totalHours.toFixed(1);\n    const remainingHours = Math.max(0, 24 - totalHours);\n    this.remainingHours = remainingHours.toFixed(1);\n  }\n  handleActivitySelection(event) {\n    const select = event.target;\n    const value = select.value;\n    this.selectedActivityId = value;\n    this.showStandardInput = value !== '' && value !== 'custom';\n    this.showCustomForm = value === 'custom';\n  }\n  addActivity() {\n    if (!this.userId || !this.selectedActivityId || this.selectedActivityId === 'custom') return;\n    if (this.hoursInput === 0 && this.minutesInput === 0) {\n      alert('Please enter a time greater than 0');\n      return;\n    }\n    if (!this.validateTotalTime(this.hoursInput, this.minutesInput)) {\n      alert('⏱️ Total time cannot exceed 24 hours');\n      return;\n    }\n    const activityType = this.activityTypes.find(type => type.id === this.selectedActivityId);\n    if (!activityType) return;\n    const existingActivity = this.activities.find(activity => activity.name === activityType.name);\n    if (existingActivity) {\n      const updatedHours = existingActivity.hours + this.hoursInput;\n      const updatedMinutes = existingActivity.minutes + this.minutesInput;\n      let finalHours = updatedHours + Math.floor(updatedMinutes / 60);\n      let finalMinutes = updatedMinutes % 60;\n      if (finalHours > 23) {\n        finalHours = 23;\n        finalMinutes = 59;\n      }\n      this.timeTrackerService.updateActivity(existingActivity.id, finalHours, finalMinutes).then(() => {\n        existingActivity.hours = finalHours;\n        existingActivity.minutes = finalMinutes;\n        this.resetInputs();\n        this.calculateTotals();\n        this.updateChart();\n      }).catch(error => {\n        alert('Error updating activity. Please try again.');\n      });\n    } else {\n      const userId = this.userId;\n      const dateStr = this.formatDate(this.selectedDate);\n      this.timeTrackerService.createActivity(userId, dateStr, activityType.name, activityType.emoji, this.hoursInput, this.minutesInput, false).then(result => {\n        this.activities.push({\n          id: result.id,\n          day_tracking_id: '',\n          name: activityType.name,\n          emoji: activityType.emoji,\n          hours: this.hoursInput,\n          minutes: this.minutesInput,\n          is_custom: false\n        });\n        this.resetInputs();\n        this.calculateTotals();\n        this.updateChart();\n      }).catch(error => {\n        alert('Error creating activity. Please try again.');\n      });\n    }\n  }\n  addCustomActivity() {\n    if (!this.userId || !this.customActivityName.trim()) {\n      alert('Please enter an activity name');\n      return;\n    }\n    if (this.customHoursInput === 0 && this.customMinutesInput === 0) {\n      alert('Please enter a time greater than 0');\n      return;\n    }\n    if (!this.validateTotalTime(this.customHoursInput, this.customMinutesInput)) {\n      alert('⏱️ Total time cannot exceed 24 hours');\n      return;\n    }\n    const existingActivity = this.activities.find(activity => activity.name.toLowerCase() === this.customActivityName.trim().toLowerCase());\n    if (existingActivity) {\n      const updatedHours = existingActivity.hours + this.customHoursInput;\n      const updatedMinutes = existingActivity.minutes + this.customMinutesInput;\n      let finalHours = updatedHours + Math.floor(updatedMinutes / 60);\n      let finalMinutes = updatedMinutes % 60;\n      if (finalHours > 23) {\n        finalHours = 23;\n        finalMinutes = 59;\n      }\n      this.timeTrackerService.updateActivity(existingActivity.id, finalHours, finalMinutes).then(() => {\n        existingActivity.hours = finalHours;\n        existingActivity.minutes = finalMinutes;\n        this.resetInputs();\n        this.calculateTotals();\n        this.updateChart();\n      }).catch(error => {\n        alert('Error updating activity. Please try again.');\n      });\n    } else {\n      const userId = this.userId;\n      const dateStr = this.formatDate(this.selectedDate);\n      this.timeTrackerService.createActivity(userId, dateStr, this.customActivityName.trim(), this.customEmoji, this.customHoursInput, this.customMinutesInput, true).then(result => {\n        this.activities.push({\n          id: result.id,\n          day_tracking_id: '',\n          name: this.customActivityName.trim(),\n          emoji: this.customEmoji,\n          hours: this.customHoursInput,\n          minutes: this.customMinutesInput,\n          is_custom: true\n        });\n        this.resetInputs();\n        this.calculateTotals();\n        this.updateChart();\n      }).catch(error => {\n        alert('Error creating activity. Please try again.');\n      });\n    }\n  }\n  updateActivity(activity) {\n    if (!activity.id) return;\n    if (activity.hours < 0) activity.hours = 0;\n    if (activity.minutes < 0) activity.minutes = 0;\n    if (activity.hours > 23) activity.hours = 23;\n    if (activity.minutes > 59) activity.minutes = 59;\n    const currentHours = activity.hours;\n    const currentMinutes = activity.minutes;\n    const otherActivitiesTime = this.activities.filter(a => a.id !== activity.id).reduce((total, a) => total + a.hours + a.minutes / 60, 0);\n    const totalTime = otherActivitiesTime + currentHours + currentMinutes / 60;\n    if (totalTime > 24) {\n      alert('Total time cannot exceed 24 hours');\n      this.loadActivities();\n      return;\n    }\n    this.timeTrackerService.updateActivity(activity.id, activity.hours, activity.minutes).then(() => {\n      this.calculateTotals();\n      this.updateChart();\n    }).catch(error => {\n      alert('Error updating activity. Please try again.');\n      this.loadActivities();\n    });\n  }\n  deleteActivity(activityId) {\n    if (!activityId) return;\n    this.timeTrackerService.deleteActivity(activityId).then(() => {\n      this.activities = this.activities.filter(a => a.id !== activityId);\n      this.calculateTotals();\n      this.updateChart();\n    }).catch(error => {\n      alert('Error deleting activity. Please try again.');\n    });\n  }\n  resetInputs() {\n    const currentEmoji = this.customEmoji;\n    this.selectedActivityId = '';\n    this.hoursInput = 0;\n    this.minutesInput = 0;\n    this.customActivityName = '';\n    this.customEmoji = currentEmoji || '⚡';\n    this.customHoursInput = 0;\n    this.customMinutesInput = 0;\n    this.showStandardInput = false;\n    this.showCustomForm = false;\n    setTimeout(() => {\n      const selectElement = document.getElementById('activitySelect');\n      if (selectElement) {\n        selectElement.value = '';\n        this.selectedActivityId = '';\n        this.showStandardInput = false;\n        this.showCustomForm = false;\n      }\n    }, 0);\n  }\n  updateTotals() {}\n  validateTotalTime(hours, minutes) {\n    const existingTime = this.activities.reduce((total, activity) => {\n      return total + activity.hours + activity.minutes / 60;\n    }, 0);\n    const totalTime = existingTime + hours + minutes / 60;\n    return totalTime <= 24;\n  }\n  initializeChart() {\n    const canvas = document.getElementById('timeChart');\n    if (!canvas) return;\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n    if (this.timeChart) {\n      this.timeChart.destroy();\n      this.timeChart = null;\n    }\n    Chart.register({\n      id: 'centerText',\n      beforeDraw: chart => {\n        const ctx = chart.ctx;\n        const width = chart.width;\n        const height = chart.height;\n        ctx.restore();\n        ctx.font = 'bold 20px Inter';\n        ctx.textAlign = 'center';\n        ctx.textBaseline = 'middle';\n        ctx.fillStyle = '#FFFFFF';\n        ctx.fillText('24h', width / 2, height / 2);\n        ctx.save();\n      }\n    });\n    try {\n      this.timeChart = new Chart(ctx, {\n        type: 'doughnut',\n        data: {\n          labels: [],\n          datasets: [{\n            data: [],\n            backgroundColor: [],\n            borderWidth: 0,\n            borderRadius: 5,\n            spacing: 2\n          }]\n        },\n        options: {\n          cutout: '75%',\n          radius: '90%',\n          plugins: {\n            legend: {\n              display: false\n            },\n            tooltip: {\n              enabled: true,\n              callbacks: {\n                label: function (context) {\n                  const value = context.raw;\n                  const hours = Math.floor(value);\n                  const minutes = Math.round((value - hours) * 60);\n                  return `${hours}h ${minutes}m`;\n                }\n              }\n            }\n          },\n          animation: {\n            animateRotate: true,\n            animateScale: true\n          }\n        }\n      });\n      this.updateChart();\n    } catch (error) {}\n  }\n  updateChart() {\n    if (!this.timeChart) {\n      this.initializeChart();\n      return;\n    }\n    try {\n      const data = this.processActivitiesForChart();\n      this.timeChart.data.labels = data.labels;\n      this.timeChart.data.datasets[0].data = data.values;\n      this.timeChart.data.datasets[0].backgroundColor = this.generateColors(data.labels.length);\n      this.timeChart.update();\n    } catch (error) {\n      this.timeChart = null;\n      setTimeout(() => {\n        this.initializeChart();\n      }, 100);\n    }\n  }\n  processActivitiesForChart() {\n    const labels = [];\n    const values = [];\n    this.activities.forEach(activity => {\n      labels.push(`${activity.emoji} ${activity.name}`);\n      values.push(activity.hours + activity.minutes / 60);\n    });\n    const totalHours = values.reduce((a, b) => a + b, 0);\n    if (totalHours < 24) {\n      labels.push('Remaining');\n      values.push(24 - totalHours);\n    }\n    return {\n      labels,\n      values\n    };\n  }\n  generateColors(count) {\n    const colors = [];\n    this.activities.forEach(activity => {\n      const activityType = this.activityTypes.find(type => type.name === activity.name);\n      if (activityType && activityType.color) {\n        colors.push(activityType.color);\n      } else if (activity.is_custom) {\n        colors.push('#2C3E50');\n      } else {\n        colors.push('#2C3E50');\n      }\n    });\n    if (count > colors.length) {\n      colors.push('#1C1C1E');\n    }\n    return colors;\n  }\n}\n_TimeTrackerPage = TimeTrackerPage;\n_TimeTrackerPage.ɵfac = function TimeTrackerPage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _TimeTrackerPage)();\n};\n_TimeTrackerPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _TimeTrackerPage,\n  selectors: [[\"app-time-tracker\"]],\n  viewQuery: function TimeTrackerPage_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chartCanvas = _t.first);\n    }\n  },\n  decls: 64,\n  vars: 16,\n  consts: [[1, \"container\"], [1, \"logo\"], [\"src\", \"assets/images/upshift_icon_mini.svg\", \"alt\", \"Upshift\"], [1, \"week-calendar\"], [1, \"calendar-nav\"], [1, \"nav-arrow\", \"prev\", 3, \"click\"], [1, \"days\"], [\"class\", \"day-name\", 4, \"ngFor\", \"ngForOf\"], [1, \"nav-arrow\", \"next\", 3, \"click\"], [1, \"dates\"], [\"class\", \"date\", 3, \"active\", \"selected\", \"disabled\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"time-tracking\"], [1, \"activity-input\"], [\"id\", \"activitySelect\", 3, \"change\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"value\", \"custom\"], [1, \"time-input-container\"], [1, \"time-inputs\"], [\"type\", \"number\", \"id\", \"hoursInput\", \"min\", \"0\", \"max\", \"23\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"id\", \"minutesInput\", \"min\", \"0\", \"max\", \"59\", 3, \"ngModelChange\", \"ngModel\"], [3, \"click\"], [\"id\", \"customActivityForm\", 1, \"custom-inline-form\"], [1, \"custom-row\"], [\"type\", \"text\", \"name\", \"emoji\", \"id\", \"emoji\", \"appEmojiInput\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"id\", \"customActivityName\", \"placeholder\", \"Activity name\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"id\", \"customHoursInput\", \"min\", \"0\", \"max\", \"23\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"id\", \"customMinutesInput\", \"min\", \"0\", \"max\", \"59\", 3, \"ngModelChange\", \"ngModel\"], [1, \"time-visualization\"], [\"id\", \"timeChart\"], [1, \"time-summary\"], [1, \"total-time\"], [\"id\", \"totalTracked\"], [1, \"remaining-time\"], [\"id\", \"remainingTime\"], [1, \"activity-list\"], [\"class\", \"activity-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"day-name\"], [1, \"date\", 3, \"click\"], [3, \"value\"], [1, \"activity-item\"], [1, \"activity-name-emoji\"], [1, \"activity-icon\"], [1, \"activity-info\"], [1, \"time-input\"], [\"type\", \"number\", \"min\", \"0\", \"max\", \"23\", 1, \"hours\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [\"type\", \"number\", \"min\", \"0\", \"max\", \"59\", 1, \"minutes\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [1, \"delete-activity\", 3, \"click\"]],\n  template: function TimeTrackerPage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\")(2, \"div\", 1);\n      i0.ɵɵelement(3, \"img\", 2);\n      i0.ɵɵelementStart(4, \"span\");\n      i0.ɵɵtext(5, \"Upshift\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(6, \"h1\");\n      i0.ɵɵtext(7, \"Time Tracker\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(8, \"div\", 3)(9, \"div\", 4)(10, \"button\", 5);\n      i0.ɵɵlistener(\"click\", function TimeTrackerPage_Template_button_click_10_listener() {\n        return ctx.changeWeek(-1);\n      });\n      i0.ɵɵtext(11, \"\\u2190\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(12, \"div\", 6);\n      i0.ɵɵtemplate(13, TimeTrackerPage_div_13_Template, 2, 1, \"div\", 7);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(14, \"button\", 8);\n      i0.ɵɵlistener(\"click\", function TimeTrackerPage_Template_button_click_14_listener() {\n        return ctx.changeWeek(1);\n      });\n      i0.ɵɵtext(15, \"\\u2192\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(16, \"div\", 9);\n      i0.ɵɵtemplate(17, TimeTrackerPage_div_17_Template, 2, 7, \"div\", 10);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(18, \"section\", 11)(19, \"h2\");\n      i0.ɵɵtext(20, \"Activities\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(21, \"div\", 12)(22, \"select\", 13);\n      i0.ɵɵlistener(\"change\", function TimeTrackerPage_Template_select_change_22_listener($event) {\n        return ctx.handleActivitySelection($event);\n      });\n      i0.ɵɵelementStart(23, \"option\", 14);\n      i0.ɵɵtext(24, \"Select Activity\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(25, TimeTrackerPage_option_25_Template, 2, 5, \"option\", 15);\n      i0.ɵɵelementStart(26, \"option\", 16);\n      i0.ɵɵtext(27, \"\\u2795 Custom Activity\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(28, \"div\", 17)(29, \"div\", 18)(30, \"input\", 19);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function TimeTrackerPage_Template_input_ngModelChange_30_listener($event) {\n        i0.ɵɵtwoWayBindingSet(ctx.hoursInput, $event) || (ctx.hoursInput = $event);\n        return $event;\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(31, \"span\");\n      i0.ɵɵtext(32, \"h\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(33, \"input\", 20);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function TimeTrackerPage_Template_input_ngModelChange_33_listener($event) {\n        i0.ɵɵtwoWayBindingSet(ctx.minutesInput, $event) || (ctx.minutesInput = $event);\n        return $event;\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(34, \"span\");\n      i0.ɵɵtext(35, \"m\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(36, \"button\", 21);\n      i0.ɵɵlistener(\"click\", function TimeTrackerPage_Template_button_click_36_listener() {\n        return ctx.addActivity();\n      });\n      i0.ɵɵtext(37, \"Add\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(38, \"div\", 22)(39, \"div\", 23)(40, \"input\", 24);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function TimeTrackerPage_Template_input_ngModelChange_40_listener($event) {\n        i0.ɵɵtwoWayBindingSet(ctx.customEmoji, $event) || (ctx.customEmoji = $event);\n        return $event;\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(41, \"input\", 25);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function TimeTrackerPage_Template_input_ngModelChange_41_listener($event) {\n        i0.ɵɵtwoWayBindingSet(ctx.customActivityName, $event) || (ctx.customActivityName = $event);\n        return $event;\n      });\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(42, \"div\", 18)(43, \"input\", 26);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function TimeTrackerPage_Template_input_ngModelChange_43_listener($event) {\n        i0.ɵɵtwoWayBindingSet(ctx.customHoursInput, $event) || (ctx.customHoursInput = $event);\n        return $event;\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(44, \"span\");\n      i0.ɵɵtext(45, \"h\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(46, \"input\", 27);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function TimeTrackerPage_Template_input_ngModelChange_46_listener($event) {\n        i0.ɵɵtwoWayBindingSet(ctx.customMinutesInput, $event) || (ctx.customMinutesInput = $event);\n        return $event;\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(47, \"span\");\n      i0.ɵɵtext(48, \"m\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(49, \"button\", 21);\n      i0.ɵɵlistener(\"click\", function TimeTrackerPage_Template_button_click_49_listener() {\n        return ctx.addCustomActivity();\n      });\n      i0.ɵɵtext(50, \"Add\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(51, \"div\", 28);\n      i0.ɵɵelement(52, \"canvas\", 29);\n      i0.ɵɵelementStart(53, \"div\", 30)(54, \"div\", 31);\n      i0.ɵɵtext(55, \" Total tracked: \");\n      i0.ɵɵelementStart(56, \"span\", 32);\n      i0.ɵɵtext(57);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(58, \"div\", 33);\n      i0.ɵɵtext(59, \" Remaining: \");\n      i0.ɵɵelementStart(60, \"span\", 34);\n      i0.ɵɵtext(61);\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(62, \"div\", 35);\n      i0.ɵɵtemplate(63, TimeTrackerPage_div_63_Template, 16, 7, \"div\", 36);\n      i0.ɵɵelementEnd()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(13);\n      i0.ɵɵproperty(\"ngForOf\", ctx.dayNames);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngForOf\", ctx.weekDates);\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"ngForOf\", ctx.activityTypes);\n      i0.ɵɵadvance(3);\n      i0.ɵɵstyleProp(\"display\", ctx.showStandardInput ? \"flex\" : \"none\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.hoursInput);\n      i0.ɵɵadvance(3);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.minutesInput);\n      i0.ɵɵadvance(5);\n      i0.ɵɵstyleProp(\"display\", ctx.showCustomForm ? \"flex\" : \"none\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.customEmoji);\n      i0.ɵɵadvance();\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.customActivityName);\n      i0.ɵɵadvance(2);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.customHoursInput);\n      i0.ɵɵadvance(3);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.customMinutesInput);\n      i0.ɵɵadvance(11);\n      i0.ɵɵtextInterpolate1(\"\", ctx.totalTrackedHours, \"h\");\n      i0.ɵɵadvance(4);\n      i0.ɵɵtextInterpolate1(\"\", ctx.remainingHours, \"h\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", ctx.activities);\n    }\n  },\n  dependencies: [IonicModule, CommonModule, i1.NgForOf, FormsModule, i2.NgSelectOption, i2.ɵNgSelectMultipleOption, i2.DefaultValueAccessor, i2.NumberValueAccessor, i2.NgControlStatus, i2.MinValidator, i2.MaxValidator, i2.NgModel, RouterModule, EmojiInputDirective],\n  styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%];\\n\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\tvar __webpack_modules__ = ({\\n\\n\\n 441:\\n\\n\\n\\n\\n\\n (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/sass-loader/dist/cjs.js):\\\\nexpected \\\\\\\"{\\\\\\\".\\\\n   \\u2577\\\\n22 \\u2502     padding: 12px;\\\\r\\\\n   \\u2502                  ^\\\\n   \\u2575\\\\n  src\\\\\\\\app\\\\\\\\pages\\\\\\\\time-tracker\\\\\\\\time-tracker.page.scss 22:18  root stylesheet\\\");\\n\\n\\n })\\n\\n\\n \\t});\\n\\n\\n\\n \\t\\n\\n \\t// startup\\n\\n \\t// Load entry module and return exports\\n\\n \\t// This entry module doesn't tell about it's top-level declarations so it can't be inlined\\n\\n \\tvar __webpack_exports__ = {};\\n\\n \\t__webpack_modules__[441]();\\n\\n \\tresource = __webpack_exports__;\\n\\n \\t\\n\\n })()\\n;\"]\n});", "map": {"version": 3, "names": ["inject", "CommonModule", "FormsModule", "IonicModule", "RouterModule", "TimeTrackerUnifiedService", "take", "Chart", "registerables", "SupabaseService", "EmojiInputDirective", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "dayName_r1", "ɵɵlistener", "TimeTrackerPage_div_17_Template_div_click_0_listener", "date_r3", "ɵɵrestoreView", "_r2", "$implicit", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "is_future", "selectDate", "date", "ɵɵclassProp", "is_today", "is_selected", "ɵɵtextInterpolate1", "day", "ɵɵproperty", "type_r5", "id", "ɵɵtextInterpolate2", "emoji", "name", "ɵɵtwoWayListener", "TimeTrackerPage_div_63_Template_input_ngModelChange_8_listener", "$event", "activity_r7", "_r6", "ɵɵtwoWayBindingSet", "hours", "TimeTrackerPage_div_63_Template_input_change_8_listener", "updateActivity", "TimeTrackerPage_div_63_Template_input_ngModelChange_11_listener", "minutes", "TimeTrackerPage_div_63_Template_input_change_11_listener", "TimeTrackerPage_div_63_Template_button_click_14_listener", "deleteActivity", "ɵɵtwoWayProperty", "register", "TimeTrackerPage", "constructor", "userId", "dayNames", "weekDates", "weekOffset", "selectedDate", "Date", "activityTypes", "activities", "selectedActivityId", "hoursInput", "minutesInput", "customActivityName", "customEmoji", "customHoursInput", "customMinutesInput", "showStandardInput", "showCustomForm", "totalTrackedHours", "remainingHours", "timeChart", "supabaseService", "timeTrackerService", "ngOnInit", "currentUser$", "pipe", "subscribe", "authUser", "loadActivityTypes", "generateWeekDates", "loadActivities", "ngAfterViewInit", "setTimeout", "initializeChart", "getActivityTypes", "types", "today", "currentDay", "getDay", "mondayOffset", "startOfWeek", "setDate", "getDate", "i", "dateStr", "formatDate", "isToday", "isSameDay", "isSelected", "isFuture", "push", "year", "getFullYear", "month", "String", "getMonth", "padStart", "date1", "date2", "changeWeek", "offset", "getDayTracking", "getActivities", "calculateTotals", "updateChart", "totalHours", "reduce", "total", "activity", "toFixed", "Math", "max", "handleActivitySelection", "event", "select", "target", "value", "addActivity", "alert", "validateTotalTime", "activityType", "find", "type", "existingActivity", "updatedHours", "updatedMinutes", "finalHours", "floor", "finalMinutes", "then", "resetInputs", "catch", "error", "createActivity", "result", "day_tracking_id", "is_custom", "addCustomActivity", "trim", "toLowerCase", "currentHours", "currentMinutes", "otherActivitiesTime", "filter", "a", "totalTime", "activityId", "<PERSON><PERSON><PERSON><PERSON>", "selectElement", "document", "getElementById", "updateTotals", "existingTime", "canvas", "ctx", "getContext", "destroy", "beforeDraw", "chart", "width", "height", "restore", "font", "textAlign", "textBaseline", "fillStyle", "fillText", "save", "data", "labels", "datasets", "backgroundColor", "borderWidth", "borderRadius", "spacing", "options", "cutout", "radius", "plugins", "legend", "display", "tooltip", "enabled", "callbacks", "label", "context", "raw", "round", "animation", "animateRotate", "animateScale", "processActivitiesForChart", "values", "generateColors", "length", "update", "for<PERSON>ach", "b", "count", "colors", "color", "selectors", "viewQuery", "TimeTrackerPage_Query", "rf", "ɵɵelement", "TimeTrackerPage_Template_button_click_10_listener", "ɵɵtemplate", "TimeTrackerPage_div_13_Template", "TimeTrackerPage_Template_button_click_14_listener", "TimeTrackerPage_div_17_Template", "TimeTrackerPage_Template_select_change_22_listener", "TimeTrackerPage_option_25_Template", "TimeTrackerPage_Template_input_ngModelChange_30_listener", "TimeTrackerPage_Template_input_ngModelChange_33_listener", "TimeTrackerPage_Template_button_click_36_listener", "TimeTrackerPage_Template_input_ngModelChange_40_listener", "TimeTrackerPage_Template_input_ngModelChange_41_listener", "TimeTrackerPage_Template_input_ngModelChange_43_listener", "TimeTrackerPage_Template_input_ngModelChange_46_listener", "TimeTrackerPage_Template_button_click_49_listener", "TimeTrackerPage_div_63_Template", "ɵɵstyleProp", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i2", "NgSelectOption", "ɵNgSelectMultipleOption", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "MinValidator", "MaxValidator", "NgModel", "styles"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\time-tracker\\time-tracker.page.ts", "C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\time-tracker\\time-tracker.page.html"], "sourcesContent": ["import { Component, OnInit, AfterViewInit, ViewChild, ElementRef, inject } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { RouterModule } from '@angular/router';\r\nimport { Activity, ActivityType } from '../../models/activity.model';\r\nimport { TimeTrackerUnifiedService } from '../../services/time-tracker-unified.service';\r\nimport { take } from 'rxjs';\r\nimport { Chart, registerables } from 'chart.js';\r\nimport { SupabaseService } from '../../services/supabase.service';\r\nimport { EmojiInputDirective } from '../../directives/emoji-input.directive';\r\n\r\nChart.register(...registerables);\r\n\r\ninterface DateDisplay {\r\n  date: string;\r\n  day: number;\r\n  is_today: boolean;\r\n  is_selected: boolean;\r\n  is_future: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-time-tracker',\r\n  templateUrl: './time-tracker.page.html',\r\n  styleUrls: ['./time-tracker.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule, FormsModule, RouterModule, EmojiInputDirective]\r\n})\r\nexport class TimeTrackerPage implements OnInit, AfterViewInit {\r\n  userId: string | null = null;\r\n\r\n  dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];\r\n  weekDates: DateDisplay[] = [];\r\n  weekOffset = 0;\r\n  selectedDate: Date = new Date();\r\n\r\n  activityTypes: ActivityType[] = [];\r\n  activities: Activity[] = [];\r\n\r\n  selectedActivityId = '';\r\n  hoursInput = 0;\r\n  minutesInput = 0;\r\n  customActivityName = '';\r\n  customEmoji = '⚡';\r\n  customHoursInput = 0;\r\n  customMinutesInput = 0;\r\n\r\n  showStandardInput = false;\r\n  showCustomForm = false;\r\n\r\n  totalTrackedHours = '0.0';\r\n  remainingHours = '24.0';\r\n\r\n  timeChart: Chart | null = null;\r\n\r\n  @ViewChild('timeChart') chartCanvas: ElementRef | undefined;\r\n\r\n  private supabaseService = inject(SupabaseService);\r\n  private timeTrackerService = inject(TimeTrackerUnifiedService);\r\n\r\n  constructor() {}\r\n\r\n  ngOnInit() {\r\n    this.supabaseService.currentUser$.pipe(\r\n      take(1)\r\n    ).subscribe(authUser => {\r\n      if (authUser) {\r\n        this.userId = authUser.id;\r\n        this.loadActivityTypes();\r\n        this.generateWeekDates();\r\n        this.loadActivities();\r\n      }\r\n    });\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    setTimeout(() => {\r\n      this.initializeChart();\r\n    }, 500);\r\n  }\r\n\r\n  loadActivityTypes() {\r\n    this.timeTrackerService.getActivityTypes().subscribe((types: ActivityType[]) => {\r\n      this.activityTypes = types;\r\n    });\r\n  }\r\n\r\n  generateWeekDates() {\r\n    const today = new Date();\r\n    const currentDay = today.getDay(); \n\r\n    const mondayOffset = currentDay === 0 ? -6 : 1; \n    const startOfWeek = new Date(today);\r\n    startOfWeek.setDate(today.getDate() - currentDay + mondayOffset + (7 * this.weekOffset));\r\n\r\n    this.weekDates = [];\r\n\r\n    for (let i = 0; i < 7; i++) {\r\n      const date = new Date(startOfWeek);\r\n      date.setDate(startOfWeek.getDate() + i);\r\n\r\n      const dateStr = this.formatDate(date);\r\n      const isToday = this.isSameDay(date, today);\r\n      const isSelected = this.isSameDay(date, this.selectedDate);\r\n      const isFuture = date > today;\r\n\r\n      this.weekDates.push({\r\n        date: dateStr,\r\n        day: date.getDate(),\r\n        is_today: isToday,\r\n        is_selected: isSelected,\r\n        is_future: isFuture\r\n      });\r\n    }\r\n  }\r\n\r\n  formatDate(date: Date): string {\r\n    const year = date.getFullYear();\r\n    const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n    return `${year}-${month}-${day}`;\r\n  }\r\n\r\n  isSameDay(date1: Date, date2: Date): boolean {\r\n    return date1.getFullYear() === date2.getFullYear() &&\r\n           date1.getMonth() === date2.getMonth() &&\r\n           date1.getDate() === date2.getDate();\r\n  }\r\n\r\n  changeWeek(offset: number) {\r\n    this.weekOffset += offset;\r\n    this.generateWeekDates();\r\n  }\r\n\r\n  selectDate(dateStr: string) {\r\n    this.selectedDate = new Date(dateStr);\r\n    this.generateWeekDates();\r\n    this.loadActivities();\r\n  }\r\n\r\n  loadActivities() {\r\n    if (!this.userId) return;\r\n\r\n    const dateStr = this.formatDate(this.selectedDate);\r\n    const userId = this.userId; \n\r\n    this.timeTrackerService.getDayTracking(userId, dateStr).subscribe(() => {\r\n      this.timeTrackerService.getActivities(userId, dateStr).subscribe((activities: Activity[]) => {\r\n        this.activities = activities;\r\n\r\n        this.calculateTotals();\r\n\r\n        this.updateChart();\r\n      });\r\n    });\r\n  }\r\n\r\n  calculateTotals() {\r\n    const totalHours = this.activities.reduce((total, activity) => {\r\n      return total + activity.hours + (activity.minutes / 60);\r\n    }, 0);\r\n\r\n    this.totalTrackedHours = totalHours.toFixed(1);\r\n\r\n    const remainingHours = Math.max(0, 24 - totalHours);\r\n    this.remainingHours = remainingHours.toFixed(1);\r\n\r\n  }\r\n\r\n  handleActivitySelection(event: Event) {\r\n    const select = event.target as HTMLSelectElement;\r\n    const value = select.value;\r\n\r\n    this.selectedActivityId = value;\r\n    this.showStandardInput = value !== '' && value !== 'custom';\r\n    this.showCustomForm = value === 'custom';\r\n  }\r\n\r\n  addActivity() {\r\n    if (!this.userId || !this.selectedActivityId || this.selectedActivityId === 'custom') return;\r\n    if (this.hoursInput === 0 && this.minutesInput === 0) {\r\n      alert('Please enter a time greater than 0');\r\n      return;\r\n    }\r\n\r\n    if (!this.validateTotalTime(this.hoursInput, this.minutesInput)) {\r\n      alert('⏱️ Total time cannot exceed 24 hours');\r\n      return;\r\n    }\r\n\r\n    const activityType = this.activityTypes.find(type => type.id === this.selectedActivityId);\r\n    if (!activityType) return;\r\n\r\n    const existingActivity = this.activities.find(activity => activity.name === activityType.name);\r\n\r\n    if (existingActivity) {\r\n      const updatedHours = existingActivity.hours + this.hoursInput;\r\n      const updatedMinutes = existingActivity.minutes + this.minutesInput;\r\n\r\n      let finalHours = updatedHours + Math.floor(updatedMinutes / 60);\r\n      let finalMinutes = updatedMinutes % 60;\r\n\r\n      if (finalHours > 23) {\r\n        finalHours = 23;\r\n        finalMinutes = 59;\r\n      }\r\n\r\n      this.timeTrackerService.updateActivity(existingActivity.id, finalHours, finalMinutes).then(() => {\r\n        existingActivity.hours = finalHours;\r\n        existingActivity.minutes = finalMinutes;\r\n\r\n        this.resetInputs();\r\n\r\n        this.calculateTotals();\r\n\r\n        this.updateChart();\r\n      }).catch(error => {\r\n        alert('Error updating activity. Please try again.');\r\n      });\r\n    } else {\r\n      const userId = this.userId as string; \n      const dateStr = this.formatDate(this.selectedDate);\r\n\r\n      this.timeTrackerService.createActivity(\r\n        userId,\r\n        dateStr,\r\n        activityType.name,\r\n        activityType.emoji,\r\n        this.hoursInput,\r\n        this.minutesInput,\r\n        false\r\n      ).then((result) => {\r\n        this.activities.push({\r\n          id: result.id,\r\n          day_tracking_id: '', \n          name: activityType.name,\r\n          emoji: activityType.emoji,\r\n          hours: this.hoursInput,\r\n          minutes: this.minutesInput,\r\n          is_custom: false\r\n        });\r\n\r\n        this.resetInputs();\r\n\r\n        this.calculateTotals();\r\n\r\n        this.updateChart();\r\n      }).catch(error => {\r\n        alert('Error creating activity. Please try again.');\r\n      });\r\n    }\r\n  }\r\n\r\n  addCustomActivity() {\r\n    if (!this.userId || !this.customActivityName.trim()) {\r\n      alert('Please enter an activity name');\r\n      return;\r\n    }\r\n\r\n    if (this.customHoursInput === 0 && this.customMinutesInput === 0) {\r\n      alert('Please enter a time greater than 0');\r\n      return;\r\n    }\r\n\r\n    if (!this.validateTotalTime(this.customHoursInput, this.customMinutesInput)) {\r\n      alert('⏱️ Total time cannot exceed 24 hours');\r\n      return;\r\n    }\r\n\r\n    const existingActivity = this.activities.find(activity =>\r\n      activity.name.toLowerCase() === this.customActivityName.trim().toLowerCase()\r\n    );\r\n\r\n    if (existingActivity) {\r\n      const updatedHours = existingActivity.hours + this.customHoursInput;\r\n      const updatedMinutes = existingActivity.minutes + this.customMinutesInput;\r\n\r\n      let finalHours = updatedHours + Math.floor(updatedMinutes / 60);\r\n      let finalMinutes = updatedMinutes % 60;\r\n\r\n      if (finalHours > 23) {\r\n        finalHours = 23;\r\n        finalMinutes = 59;\r\n      }\r\n\r\n      this.timeTrackerService.updateActivity(existingActivity.id, finalHours, finalMinutes).then(() => {\r\n        existingActivity.hours = finalHours;\r\n        existingActivity.minutes = finalMinutes;\r\n\r\n        this.resetInputs();\r\n\r\n        this.calculateTotals();\r\n\r\n        this.updateChart();\r\n      }).catch(error => {\r\n        alert('Error updating activity. Please try again.');\r\n      });\r\n    } else {\r\n      const userId = this.userId as string; \n      const dateStr = this.formatDate(this.selectedDate);\r\n\r\n      this.timeTrackerService.createActivity(\r\n        userId,\r\n        dateStr,\r\n        this.customActivityName.trim(),\r\n        this.customEmoji,\r\n        this.customHoursInput,\r\n        this.customMinutesInput,\r\n        true\r\n      ).then((result) => {\r\n        this.activities.push({\r\n          id: result.id,\r\n          day_tracking_id: '', \n          name: this.customActivityName.trim(),\r\n          emoji: this.customEmoji,\r\n          hours: this.customHoursInput,\r\n          minutes: this.customMinutesInput,\r\n          is_custom: true\r\n        });\r\n\r\n        this.resetInputs();\r\n\r\n        this.calculateTotals();\r\n\r\n        this.updateChart();\r\n      }).catch(error => {\r\n        alert('Error creating activity. Please try again.');\r\n      });\r\n    }\r\n  }\r\n\r\n  updateActivity(activity: Activity) {\r\n    if (!activity.id) return;\r\n\r\n    if (activity.hours < 0) activity.hours = 0;\r\n    if (activity.minutes < 0) activity.minutes = 0;\r\n    if (activity.hours > 23) activity.hours = 23;\r\n    if (activity.minutes > 59) activity.minutes = 59;\r\n\r\n    const currentHours = activity.hours;\r\n    const currentMinutes = activity.minutes;\r\n\r\n    const otherActivitiesTime = this.activities\r\n      .filter(a => a.id !== activity.id)\r\n      .reduce((total, a) => total + a.hours + (a.minutes / 60), 0);\r\n\r\n    const totalTime = otherActivitiesTime + currentHours + (currentMinutes / 60);\r\n\r\n    if (totalTime > 24) {\r\n      alert('Total time cannot exceed 24 hours');\r\n      this.loadActivities();\r\n      return;\r\n    }\r\n\r\n    this.timeTrackerService.updateActivity(activity.id, activity.hours, activity.minutes).then(() => {\r\n      this.calculateTotals();\r\n\r\n      this.updateChart();\r\n    }).catch(error => {\r\n      alert('Error updating activity. Please try again.');\r\n      this.loadActivities(); \n    });\r\n  }\r\n\r\n  deleteActivity(activityId: string) {\r\n    if (!activityId) return;\r\n\r\n    this.timeTrackerService.deleteActivity(activityId).then(() => {\r\n      this.activities = this.activities.filter(a => a.id !== activityId);\r\n\r\n      this.calculateTotals();\r\n\r\n      this.updateChart();\r\n    }).catch(error => {\r\n      alert('Error deleting activity. Please try again.');\r\n    });\r\n  }\r\n\r\n  resetInputs() {\r\n    const currentEmoji = this.customEmoji;\r\n\r\n    this.selectedActivityId = '';\r\n    this.hoursInput = 0;\r\n    this.minutesInput = 0;\r\n    this.customActivityName = '';\r\n    this.customEmoji = currentEmoji || '⚡';\r\n    this.customHoursInput = 0;\r\n    this.customMinutesInput = 0;\r\n    this.showStandardInput = false;\r\n    this.showCustomForm = false;\r\n\r\n    setTimeout(() => {\r\n      const selectElement = document.getElementById('activitySelect') as HTMLSelectElement;\r\n      if (selectElement) {\r\n        selectElement.value = '';\r\n\r\n        this.selectedActivityId = '';\r\n        this.showStandardInput = false;\r\n        this.showCustomForm = false;\r\n      }\r\n    }, 0);\r\n  }\r\n\r\n  updateTotals() {\r\n  }\r\n\r\n  validateTotalTime(hours: number, minutes: number): boolean {\r\n    const existingTime = this.activities.reduce((total, activity) => {\r\n      return total + activity.hours + (activity.minutes / 60);\r\n    }, 0);\r\n\r\n    const totalTime = existingTime + hours + (minutes / 60);\r\n\r\n    return totalTime <= 24;\r\n  }\r\n\r\n  initializeChart() {\r\n    const canvas = document.getElementById('timeChart') as HTMLCanvasElement;\r\n    if (!canvas) return;\r\n\r\n    const ctx = canvas.getContext('2d');\r\n    if (!ctx) return;\r\n\r\n    if (this.timeChart) {\r\n      this.timeChart.destroy();\r\n      this.timeChart = null;\r\n    }\r\n\r\n    Chart.register({\r\n      id: 'centerText',\r\n      beforeDraw: (chart: any) => {\r\n        const ctx = chart.ctx;\r\n        const width = chart.width;\r\n        const height = chart.height;\r\n\r\n        ctx.restore();\r\n        ctx.font = 'bold 20px Inter';\r\n        ctx.textAlign = 'center';\r\n        ctx.textBaseline = 'middle';\r\n\r\n        ctx.fillStyle = '#FFFFFF';\r\n        ctx.fillText('24h', width / 2, height / 2);\r\n        ctx.save();\r\n      }\r\n    });\r\n\r\n    try {\r\n      this.timeChart = new Chart(ctx, {\r\n        type: 'doughnut',\r\n        data: {\r\n          labels: [],\r\n          datasets: [{\r\n            data: [],\r\n            backgroundColor: [],\r\n            borderWidth: 0,\r\n            borderRadius: 5,\r\n            spacing: 2\r\n          }]\r\n        },\r\n        options: {\r\n          cutout: '75%',\r\n          radius: '90%',\r\n          plugins: {\r\n            legend: {\r\n              display: false\r\n            },\r\n            tooltip: {\r\n              enabled: true,\r\n              callbacks: {\r\n                label: function(context: any) {\r\n                  const value = context.raw as number;\r\n                  const hours = Math.floor(value);\r\n                  const minutes = Math.round((value - hours) * 60);\r\n                  return `${hours}h ${minutes}m`;\r\n                }\r\n              }\r\n            }\r\n          },\r\n          animation: {\r\n            animateRotate: true,\r\n            animateScale: true\r\n          }\r\n        }\r\n      });\r\n\r\n      this.updateChart();\r\n    } catch (error) {\r\n    }\r\n  }\r\n\r\n  updateChart() {\r\n    if (!this.timeChart) {\r\n      this.initializeChart();\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const data = this.processActivitiesForChart();\r\n\r\n      this.timeChart.data.labels = data.labels;\r\n      this.timeChart.data.datasets[0].data = data.values;\r\n      this.timeChart.data.datasets[0].backgroundColor = this.generateColors(data.labels.length);\r\n\r\n      this.timeChart.update();\r\n    } catch (error) {\r\n      this.timeChart = null;\r\n      setTimeout(() => {\r\n        this.initializeChart();\r\n      }, 100);\r\n    }\r\n  }\r\n\r\n  processActivitiesForChart() {\r\n    const labels: string[] = [];\r\n    const values: number[] = [];\r\n\r\n    this.activities.forEach(activity => {\r\n      labels.push(`${activity.emoji} ${activity.name}`);\r\n      values.push(activity.hours + (activity.minutes / 60));\r\n    });\r\n\r\n    const totalHours = values.reduce((a, b) => a + b, 0);\r\n    if (totalHours < 24) {\r\n      labels.push('Remaining');\r\n      values.push(24 - totalHours);\r\n    }\r\n\r\n    return { labels, values };\r\n  }\r\n\r\n  generateColors(count: number) {\r\n    const colors: string[] = [];\r\n\r\n    this.activities.forEach(activity => {\r\n      const activityType = this.activityTypes.find(type => type.name === activity.name);\r\n\r\n      if (activityType && activityType.color) {\r\n        colors.push(activityType.color);\r\n      } else if (activity.is_custom) {\r\n        colors.push('#2C3E50'); \n      } else {\r\n        colors.push('#2C3E50');\r\n      }\r\n    });\r\n\r\n    if (count > colors.length) {\r\n      colors.push('#1C1C1E');\r\n    }\r\n\r\n    return colors;\r\n  }\r\n}\r\n", "<!-- Exact HTML from Django template with Angular syntax -->\r\n<div class=\"container\">\r\n    <header>\r\n        <div class=\"logo\">\r\n            <img src=\"assets/images/upshift_icon_mini.svg\" alt=\"Upshift\">\r\n            <span>Upshift</span>\r\n        </div>\r\n        <h1>Time Tracker</h1>\r\n    </header>\r\n\r\n    <div class=\"week-calendar\">\r\n        <div class=\"calendar-nav\">\r\n            <button class=\"nav-arrow prev\" (click)=\"changeWeek(-1)\">←</button>\r\n            <div class=\"days\">\r\n                <div class=\"day-name\" *ngFor=\"let dayName of dayNames\">{{ dayName }}</div>\r\n            </div>\r\n            <button class=\"nav-arrow next\" (click)=\"changeWeek(1)\">→</button>\r\n        </div>\r\n        <div class=\"dates\">\r\n            <div *ngFor=\"let date of weekDates\"\r\n                 class=\"date\"\r\n                 [class.active]=\"date.is_today\"\r\n                 [class.selected]=\"date.is_selected\"\r\n                 [class.disabled]=\"date.is_future\"\r\n                 (click)=\"!date.is_future && selectDate(date.date)\">\r\n                {{ date.day }}\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <section class=\"time-tracking\">\r\n        <h2>Activities</h2>\r\n\r\n        <div class=\"activity-input\">\r\n            <select id=\"activitySelect\" (change)=\"handleActivitySelection($event)\">\r\n                <option value=\"\">Select Activity</option>\r\n                <option *ngFor=\"let type of activityTypes\" [value]=\"type.id\" [attr.data-emoji]=\"type.emoji\" [attr.data-name]=\"type.name\">\r\n                    {{ type.emoji }} {{ type.name }}\r\n                </option>\r\n                <option value=\"custom\">➕ Custom Activity</option>\r\n            </select>\r\n\r\n            <!-- Standard time input -->\r\n            <div class=\"time-input-container\" [style.display]=\"showStandardInput ? 'flex' : 'none'\">\r\n                <div class=\"time-inputs\">\r\n                    <input type=\"number\" id=\"hoursInput\" min=\"0\" max=\"23\" [(ngModel)]=\"hoursInput\">\r\n                    <span>h</span>\r\n                    <input type=\"number\" id=\"minutesInput\" min=\"0\" max=\"59\" [(ngModel)]=\"minutesInput\">\r\n                    <span>m</span>\r\n                </div>\r\n                <button (click)=\"addActivity()\">Add</button>\r\n            </div>\r\n\r\n            <!-- Custom activity input -->\r\n            <div id=\"customActivityForm\" class=\"custom-inline-form\" [style.display]=\"showCustomForm ? 'flex' : 'none'\">\r\n                <div class=\"custom-row\">\r\n                    <input type=\"text\" name=\"emoji\" id=\"emoji\" [(ngModel)]=\"customEmoji\" appEmojiInput>\r\n                    <input type=\"text\" id=\"customActivityName\" placeholder=\"Activity name\" [(ngModel)]=\"customActivityName\">\r\n                </div>\r\n                <div class=\"time-inputs\">\r\n                    <input type=\"number\" id=\"customHoursInput\" min=\"0\" max=\"23\" [(ngModel)]=\"customHoursInput\">\r\n                    <span>h</span>\r\n                    <input type=\"number\" id=\"customMinutesInput\" min=\"0\" max=\"59\" [(ngModel)]=\"customMinutesInput\">\r\n                    <span>m</span>\r\n                </div>\r\n                <button (click)=\"addCustomActivity()\">Add</button>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"time-visualization\">\r\n            <canvas id=\"timeChart\"></canvas>\r\n            <div class=\"time-summary\">\r\n                <div class=\"total-time\">\r\n                    Total tracked: <span id=\"totalTracked\">{{ totalTrackedHours }}h</span>\r\n                </div>\r\n                <div class=\"remaining-time\">\r\n                    Remaining: <span id=\"remainingTime\">{{ remainingHours }}h</span>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"activity-list\">\r\n            <div *ngFor=\"let activity of activities\" class=\"activity-item\" [attr.data-id]=\"activity.id\" >\r\n                <div class=\"activity-name-emoji\" >\r\n                    <div class=\"activity-icon\">{{ activity.emoji }}</div>\r\n                    <h3 >{{ activity.name }}</h3>\r\n                </div>\r\n                <div class=\"activity-info\">\r\n\r\n                    <div class=\"time-input\">\r\n                        <input type=\"number\" min=\"0\" max=\"23\" [(ngModel)]=\"activity.hours\"\r\n                               class=\"hours\" [attr.data-activity-id]=\"activity.id\" (change)=\"updateActivity(activity)\">\r\n                        <span>h</span>\r\n                        <input type=\"number\" min=\"0\" max=\"59\" [(ngModel)]=\"activity.minutes\"\r\n                               class=\"minutes\" [attr.data-activity-id]=\"activity.id\" (change)=\"updateActivity(activity)\">\r\n                        <span>m</span>\r\n                    </div>\r\n                    <button class=\"delete-activity\" (click)=\"deleteActivity(activity.id)\" >×</button>\r\n                </div>\r\n\r\n            </div>\r\n        </div>\r\n    </section>\r\n</div>\r\n"], "mappings": ";AAAA,SAAkEA,MAAM,QAAQ,eAAe;AAC/F,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,yBAAyB,QAAQ,6CAA6C;AACvF,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,KAAK,EAAEC,aAAa,QAAQ,UAAU;AAC/C,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,mBAAmB,QAAQ,wCAAwC;;;;;;;ICI5DC,EAAA,CAAAC,cAAA,cAAuD;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAnBH,EAAA,CAAAI,SAAA,EAAa;IAAbJ,EAAA,CAAAK,iBAAA,CAAAC,UAAA,CAAa;;;;;;IAKxEN,EAAA,CAAAC,cAAA,cAKwD;IAAnDD,EAAA,CAAAO,UAAA,mBAAAC,qDAAA;MAAA,MAAAC,OAAA,GAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,EAAAN,OAAA,CAAAO,SAAA,IAA4BH,MAAA,CAAAI,UAAA,CAAAR,OAAA,CAAAS,IAAA,CAAqB;IAAA,EAAC;IACnDlB,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAHDH,EAFA,CAAAmB,WAAA,WAAAV,OAAA,CAAAW,QAAA,CAA8B,aAAAX,OAAA,CAAAY,WAAA,CACK,aAAAZ,OAAA,CAAAO,SAAA,CACF;IAElChB,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAsB,kBAAA,MAAAb,OAAA,CAAAc,GAAA,MACJ;;;;;IAUIvB,EAAA,CAAAC,cAAA,iBAAyH;IACrHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFkCH,EAAA,CAAAwB,UAAA,UAAAC,OAAA,CAAAC,EAAA,CAAiB;;IACxD1B,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAA2B,kBAAA,MAAAF,OAAA,CAAAG,KAAA,OAAAH,OAAA,CAAAI,IAAA,MACJ;;;;;;IA8CI7B,EAFR,CAAAC,cAAA,cAA6F,cACvD,cACH;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACrDH,EAAA,CAAAC,cAAA,SAAK;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAC5BF,EAD4B,CAAAG,YAAA,EAAK,EAC3B;IAIEH,EAHR,CAAAC,cAAA,cAA2B,cAEC,gBAE2E;IADzDD,EAAA,CAAA8B,gBAAA,2BAAAC,+DAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAjC,EAAA,CAAAU,aAAA,CAAAwB,GAAA,EAAAtB,SAAA;MAAAZ,EAAA,CAAAmC,kBAAA,CAAAF,WAAA,CAAAG,KAAA,EAAAJ,MAAA,MAAAC,WAAA,CAAAG,KAAA,GAAAJ,MAAA;MAAA,OAAAhC,EAAA,CAAAe,WAAA,CAAAiB,MAAA;IAAA,EAA4B;IACPhC,EAAA,CAAAO,UAAA,oBAAA8B,wDAAA;MAAA,MAAAJ,WAAA,GAAAjC,EAAA,CAAAU,aAAA,CAAAwB,GAAA,EAAAtB,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAUF,MAAA,CAAAyB,cAAA,CAAAL,WAAA,CAAwB;IAAA,EAAC;IAD9FjC,EAAA,CAAAG,YAAA,EAC+F;IAC/FH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACdH,EAAA,CAAAC,cAAA,iBACiG;IAD3DD,EAAA,CAAA8B,gBAAA,2BAAAS,gEAAAP,MAAA;MAAA,MAAAC,WAAA,GAAAjC,EAAA,CAAAU,aAAA,CAAAwB,GAAA,EAAAtB,SAAA;MAAAZ,EAAA,CAAAmC,kBAAA,CAAAF,WAAA,CAAAO,OAAA,EAAAR,MAAA,MAAAC,WAAA,CAAAO,OAAA,GAAAR,MAAA;MAAA,OAAAhC,EAAA,CAAAe,WAAA,CAAAiB,MAAA;IAAA,EAA8B;IACPhC,EAAA,CAAAO,UAAA,oBAAAkC,yDAAA;MAAA,MAAAR,WAAA,GAAAjC,EAAA,CAAAU,aAAA,CAAAwB,GAAA,EAAAtB,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAUF,MAAA,CAAAyB,cAAA,CAAAL,WAAA,CAAwB;IAAA,EAAC;IADhGjC,EAAA,CAAAG,YAAA,EACiG;IACjGH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,SAAC;IACXF,EADW,CAAAG,YAAA,EAAO,EACZ;IACNH,EAAA,CAAAC,cAAA,kBAAuE;IAAvCD,EAAA,CAAAO,UAAA,mBAAAmC,yDAAA;MAAA,MAAAT,WAAA,GAAAjC,EAAA,CAAAU,aAAA,CAAAwB,GAAA,EAAAtB,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAA8B,cAAA,CAAAV,WAAA,CAAAP,EAAA,CAA2B;IAAA,EAAC;IAAE1B,EAAA,CAAAE,MAAA,cAAC;IAGhFF,EAHgF,CAAAG,YAAA,EAAS,EAC/E,EAEJ;;;;;IAhB6BH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAK,iBAAA,CAAA4B,WAAA,CAAAL,KAAA,CAAoB;IAC1C5B,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAA4B,WAAA,CAAAJ,IAAA,CAAmB;IAKkB7B,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAA4C,gBAAA,YAAAX,WAAA,CAAAG,KAAA,CAA4B;;IAG5BpC,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAA4C,gBAAA,YAAAX,WAAA,CAAAO,OAAA,CAA8B;;;;ADjF5F5C,KAAK,CAACiD,QAAQ,CAAC,GAAGhD,aAAa,CAAC;AAiBhC,OAAM,MAAOiD,eAAe;EAgC1BC,YAAA;IA/BA,KAAAC,MAAM,GAAkB,IAAI;IAE5B,KAAAC,QAAQ,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC5D,KAAAC,SAAS,GAAkB,EAAE;IAC7B,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAS,IAAIC,IAAI,EAAE;IAE/B,KAAAC,aAAa,GAAmB,EAAE;IAClC,KAAAC,UAAU,GAAe,EAAE;IAE3B,KAAAC,kBAAkB,GAAG,EAAE;IACvB,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,kBAAkB,GAAG,EAAE;IACvB,KAAAC,WAAW,GAAG,GAAG;IACjB,KAAAC,gBAAgB,GAAG,CAAC;IACpB,KAAAC,kBAAkB,GAAG,CAAC;IAEtB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,cAAc,GAAG,KAAK;IAEtB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,cAAc,GAAG,MAAM;IAEvB,KAAAC,SAAS,GAAiB,IAAI;IAItB,KAAAC,eAAe,GAAG/E,MAAM,CAACS,eAAe,CAAC;IACzC,KAAAuE,kBAAkB,GAAGhF,MAAM,CAACK,yBAAyB,CAAC;EAE/C;EAEf4E,QAAQA,CAAA;IACN,IAAI,CAACF,eAAe,CAACG,YAAY,CAACC,IAAI,CACpC7E,IAAI,CAAC,CAAC,CAAC,CACR,CAAC8E,SAAS,CAACC,QAAQ,IAAG;MACrB,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAAC1B,MAAM,GAAG0B,QAAQ,CAAChD,EAAE;QACzB,IAAI,CAACiD,iBAAiB,EAAE;QACxB,IAAI,CAACC,iBAAiB,EAAE;QACxB,IAAI,CAACC,cAAc,EAAE;MACvB;IACF,CAAC,CAAC;EACJ;EAEAC,eAAeA,CAAA;IACbC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,eAAe,EAAE;IACxB,CAAC,EAAE,GAAG,CAAC;EACT;EAEAL,iBAAiBA,CAAA;IACf,IAAI,CAACN,kBAAkB,CAACY,gBAAgB,EAAE,CAACR,SAAS,CAAES,KAAqB,IAAI;MAC7E,IAAI,CAAC5B,aAAa,GAAG4B,KAAK;IAC5B,CAAC,CAAC;EACJ;EAEAN,iBAAiBA,CAAA;IACf,MAAMO,KAAK,GAAG,IAAI9B,IAAI,EAAE;IACxB,MAAM+B,UAAU,GAAGD,KAAK,CAACE,MAAM,EAAE;IAEjC,MAAMC,YAAY,GAAGF,UAAU,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAC9C,MAAMG,WAAW,GAAG,IAAIlC,IAAI,CAAC8B,KAAK,CAAC;IACnCI,WAAW,CAACC,OAAO,CAACL,KAAK,CAACM,OAAO,EAAE,GAAGL,UAAU,GAAGE,YAAY,GAAI,CAAC,GAAG,IAAI,CAACnC,UAAW,CAAC;IAExF,IAAI,CAACD,SAAS,GAAG,EAAE;IAEnB,KAAK,IAAIwC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B,MAAMxE,IAAI,GAAG,IAAImC,IAAI,CAACkC,WAAW,CAAC;MAClCrE,IAAI,CAACsE,OAAO,CAACD,WAAW,CAACE,OAAO,EAAE,GAAGC,CAAC,CAAC;MAEvC,MAAMC,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC1E,IAAI,CAAC;MACrC,MAAM2E,OAAO,GAAG,IAAI,CAACC,SAAS,CAAC5E,IAAI,EAAEiE,KAAK,CAAC;MAC3C,MAAMY,UAAU,GAAG,IAAI,CAACD,SAAS,CAAC5E,IAAI,EAAE,IAAI,CAACkC,YAAY,CAAC;MAC1D,MAAM4C,QAAQ,GAAG9E,IAAI,GAAGiE,KAAK;MAE7B,IAAI,CAACjC,SAAS,CAAC+C,IAAI,CAAC;QAClB/E,IAAI,EAAEyE,OAAO;QACbpE,GAAG,EAAEL,IAAI,CAACuE,OAAO,EAAE;QACnBrE,QAAQ,EAAEyE,OAAO;QACjBxE,WAAW,EAAE0E,UAAU;QACvB/E,SAAS,EAAEgF;OACZ,CAAC;IACJ;EACF;EAEAJ,UAAUA,CAAC1E,IAAU;IACnB,MAAMgF,IAAI,GAAGhF,IAAI,CAACiF,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACnF,IAAI,CAACoF,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMhF,GAAG,GAAG8E,MAAM,CAACnF,IAAI,CAACuE,OAAO,EAAE,CAAC,CAACc,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAI7E,GAAG,EAAE;EAClC;EAEAuE,SAASA,CAACU,KAAW,EAAEC,KAAW;IAChC,OAAOD,KAAK,CAACL,WAAW,EAAE,KAAKM,KAAK,CAACN,WAAW,EAAE,IAC3CK,KAAK,CAACF,QAAQ,EAAE,KAAKG,KAAK,CAACH,QAAQ,EAAE,IACrCE,KAAK,CAACf,OAAO,EAAE,KAAKgB,KAAK,CAAChB,OAAO,EAAE;EAC5C;EAEAiB,UAAUA,CAACC,MAAc;IACvB,IAAI,CAACxD,UAAU,IAAIwD,MAAM;IACzB,IAAI,CAAC/B,iBAAiB,EAAE;EAC1B;EAEA3D,UAAUA,CAAC0E,OAAe;IACxB,IAAI,CAACvC,YAAY,GAAG,IAAIC,IAAI,CAACsC,OAAO,CAAC;IACrC,IAAI,CAACf,iBAAiB,EAAE;IACxB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAA,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC7B,MAAM,EAAE;IAElB,MAAM2C,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,IAAI,CAACxC,YAAY,CAAC;IAClD,MAAMJ,MAAM,GAAG,IAAI,CAACA,MAAM;IAE1B,IAAI,CAACqB,kBAAkB,CAACuC,cAAc,CAAC5D,MAAM,EAAE2C,OAAO,CAAC,CAAClB,SAAS,CAAC,MAAK;MACrE,IAAI,CAACJ,kBAAkB,CAACwC,aAAa,CAAC7D,MAAM,EAAE2C,OAAO,CAAC,CAAClB,SAAS,CAAElB,UAAsB,IAAI;QAC1F,IAAI,CAACA,UAAU,GAAGA,UAAU;QAE5B,IAAI,CAACuD,eAAe,EAAE;QAEtB,IAAI,CAACC,WAAW,EAAE;MACpB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAD,eAAeA,CAAA;IACb,MAAME,UAAU,GAAG,IAAI,CAACzD,UAAU,CAAC0D,MAAM,CAAC,CAACC,KAAK,EAAEC,QAAQ,KAAI;MAC5D,OAAOD,KAAK,GAAGC,QAAQ,CAAC/E,KAAK,GAAI+E,QAAQ,CAAC3E,OAAO,GAAG,EAAG;IACzD,CAAC,EAAE,CAAC,CAAC;IAEL,IAAI,CAACyB,iBAAiB,GAAG+C,UAAU,CAACI,OAAO,CAAC,CAAC,CAAC;IAE9C,MAAMlD,cAAc,GAAGmD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAGN,UAAU,CAAC;IACnD,IAAI,CAAC9C,cAAc,GAAGA,cAAc,CAACkD,OAAO,CAAC,CAAC,CAAC;EAEjD;EAEAG,uBAAuBA,CAACC,KAAY;IAClC,MAAMC,MAAM,GAAGD,KAAK,CAACE,MAA2B;IAChD,MAAMC,KAAK,GAAGF,MAAM,CAACE,KAAK;IAE1B,IAAI,CAACnE,kBAAkB,GAAGmE,KAAK;IAC/B,IAAI,CAAC5D,iBAAiB,GAAG4D,KAAK,KAAK,EAAE,IAAIA,KAAK,KAAK,QAAQ;IAC3D,IAAI,CAAC3D,cAAc,GAAG2D,KAAK,KAAK,QAAQ;EAC1C;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAC5E,MAAM,IAAI,CAAC,IAAI,CAACQ,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,KAAK,QAAQ,EAAE;IACtF,IAAI,IAAI,CAACC,UAAU,KAAK,CAAC,IAAI,IAAI,CAACC,YAAY,KAAK,CAAC,EAAE;MACpDmE,KAAK,CAAC,oCAAoC,CAAC;MAC3C;IACF;IAEA,IAAI,CAAC,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACrE,UAAU,EAAE,IAAI,CAACC,YAAY,CAAC,EAAE;MAC/DmE,KAAK,CAAC,sCAAsC,CAAC;MAC7C;IACF;IAEA,MAAME,YAAY,GAAG,IAAI,CAACzE,aAAa,CAAC0E,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACvG,EAAE,KAAK,IAAI,CAAC8B,kBAAkB,CAAC;IACzF,IAAI,CAACuE,YAAY,EAAE;IAEnB,MAAMG,gBAAgB,GAAG,IAAI,CAAC3E,UAAU,CAACyE,IAAI,CAACb,QAAQ,IAAIA,QAAQ,CAACtF,IAAI,KAAKkG,YAAY,CAAClG,IAAI,CAAC;IAE9F,IAAIqG,gBAAgB,EAAE;MACpB,MAAMC,YAAY,GAAGD,gBAAgB,CAAC9F,KAAK,GAAG,IAAI,CAACqB,UAAU;MAC7D,MAAM2E,cAAc,GAAGF,gBAAgB,CAAC1F,OAAO,GAAG,IAAI,CAACkB,YAAY;MAEnE,IAAI2E,UAAU,GAAGF,YAAY,GAAGd,IAAI,CAACiB,KAAK,CAACF,cAAc,GAAG,EAAE,CAAC;MAC/D,IAAIG,YAAY,GAAGH,cAAc,GAAG,EAAE;MAEtC,IAAIC,UAAU,GAAG,EAAE,EAAE;QACnBA,UAAU,GAAG,EAAE;QACfE,YAAY,GAAG,EAAE;MACnB;MAEA,IAAI,CAAClE,kBAAkB,CAAC/B,cAAc,CAAC4F,gBAAgB,CAACxG,EAAE,EAAE2G,UAAU,EAAEE,YAAY,CAAC,CAACC,IAAI,CAAC,MAAK;QAC9FN,gBAAgB,CAAC9F,KAAK,GAAGiG,UAAU;QACnCH,gBAAgB,CAAC1F,OAAO,GAAG+F,YAAY;QAEvC,IAAI,CAACE,WAAW,EAAE;QAElB,IAAI,CAAC3B,eAAe,EAAE;QAEtB,IAAI,CAACC,WAAW,EAAE;MACpB,CAAC,CAAC,CAAC2B,KAAK,CAACC,KAAK,IAAG;QACfd,KAAK,CAAC,4CAA4C,CAAC;MACrD,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,MAAM7E,MAAM,GAAG,IAAI,CAACA,MAAgB;MACpC,MAAM2C,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,IAAI,CAACxC,YAAY,CAAC;MAElD,IAAI,CAACiB,kBAAkB,CAACuE,cAAc,CACpC5F,MAAM,EACN2C,OAAO,EACPoC,YAAY,CAAClG,IAAI,EACjBkG,YAAY,CAACnG,KAAK,EAClB,IAAI,CAAC6B,UAAU,EACf,IAAI,CAACC,YAAY,EACjB,KAAK,CACN,CAAC8E,IAAI,CAAEK,MAAM,IAAI;QAChB,IAAI,CAACtF,UAAU,CAAC0C,IAAI,CAAC;UACnBvE,EAAE,EAAEmH,MAAM,CAACnH,EAAE;UACboH,eAAe,EAAE,EAAE;UACnBjH,IAAI,EAAEkG,YAAY,CAAClG,IAAI;UACvBD,KAAK,EAAEmG,YAAY,CAACnG,KAAK;UACzBQ,KAAK,EAAE,IAAI,CAACqB,UAAU;UACtBjB,OAAO,EAAE,IAAI,CAACkB,YAAY;UAC1BqF,SAAS,EAAE;SACZ,CAAC;QAEF,IAAI,CAACN,WAAW,EAAE;QAElB,IAAI,CAAC3B,eAAe,EAAE;QAEtB,IAAI,CAACC,WAAW,EAAE;MACpB,CAAC,CAAC,CAAC2B,KAAK,CAACC,KAAK,IAAG;QACfd,KAAK,CAAC,4CAA4C,CAAC;MACrD,CAAC,CAAC;IACJ;EACF;EAEAmB,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAAChG,MAAM,IAAI,CAAC,IAAI,CAACW,kBAAkB,CAACsF,IAAI,EAAE,EAAE;MACnDpB,KAAK,CAAC,+BAA+B,CAAC;MACtC;IACF;IAEA,IAAI,IAAI,CAAChE,gBAAgB,KAAK,CAAC,IAAI,IAAI,CAACC,kBAAkB,KAAK,CAAC,EAAE;MAChE+D,KAAK,CAAC,oCAAoC,CAAC;MAC3C;IACF;IAEA,IAAI,CAAC,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACjE,gBAAgB,EAAE,IAAI,CAACC,kBAAkB,CAAC,EAAE;MAC3E+D,KAAK,CAAC,sCAAsC,CAAC;MAC7C;IACF;IAEA,MAAMK,gBAAgB,GAAG,IAAI,CAAC3E,UAAU,CAACyE,IAAI,CAACb,QAAQ,IACpDA,QAAQ,CAACtF,IAAI,CAACqH,WAAW,EAAE,KAAK,IAAI,CAACvF,kBAAkB,CAACsF,IAAI,EAAE,CAACC,WAAW,EAAE,CAC7E;IAED,IAAIhB,gBAAgB,EAAE;MACpB,MAAMC,YAAY,GAAGD,gBAAgB,CAAC9F,KAAK,GAAG,IAAI,CAACyB,gBAAgB;MACnE,MAAMuE,cAAc,GAAGF,gBAAgB,CAAC1F,OAAO,GAAG,IAAI,CAACsB,kBAAkB;MAEzE,IAAIuE,UAAU,GAAGF,YAAY,GAAGd,IAAI,CAACiB,KAAK,CAACF,cAAc,GAAG,EAAE,CAAC;MAC/D,IAAIG,YAAY,GAAGH,cAAc,GAAG,EAAE;MAEtC,IAAIC,UAAU,GAAG,EAAE,EAAE;QACnBA,UAAU,GAAG,EAAE;QACfE,YAAY,GAAG,EAAE;MACnB;MAEA,IAAI,CAAClE,kBAAkB,CAAC/B,cAAc,CAAC4F,gBAAgB,CAACxG,EAAE,EAAE2G,UAAU,EAAEE,YAAY,CAAC,CAACC,IAAI,CAAC,MAAK;QAC9FN,gBAAgB,CAAC9F,KAAK,GAAGiG,UAAU;QACnCH,gBAAgB,CAAC1F,OAAO,GAAG+F,YAAY;QAEvC,IAAI,CAACE,WAAW,EAAE;QAElB,IAAI,CAAC3B,eAAe,EAAE;QAEtB,IAAI,CAACC,WAAW,EAAE;MACpB,CAAC,CAAC,CAAC2B,KAAK,CAACC,KAAK,IAAG;QACfd,KAAK,CAAC,4CAA4C,CAAC;MACrD,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,MAAM7E,MAAM,GAAG,IAAI,CAACA,MAAgB;MACpC,MAAM2C,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,IAAI,CAACxC,YAAY,CAAC;MAElD,IAAI,CAACiB,kBAAkB,CAACuE,cAAc,CACpC5F,MAAM,EACN2C,OAAO,EACP,IAAI,CAAChC,kBAAkB,CAACsF,IAAI,EAAE,EAC9B,IAAI,CAACrF,WAAW,EAChB,IAAI,CAACC,gBAAgB,EACrB,IAAI,CAACC,kBAAkB,EACvB,IAAI,CACL,CAAC0E,IAAI,CAAEK,MAAM,IAAI;QAChB,IAAI,CAACtF,UAAU,CAAC0C,IAAI,CAAC;UACnBvE,EAAE,EAAEmH,MAAM,CAACnH,EAAE;UACboH,eAAe,EAAE,EAAE;UACnBjH,IAAI,EAAE,IAAI,CAAC8B,kBAAkB,CAACsF,IAAI,EAAE;UACpCrH,KAAK,EAAE,IAAI,CAACgC,WAAW;UACvBxB,KAAK,EAAE,IAAI,CAACyB,gBAAgB;UAC5BrB,OAAO,EAAE,IAAI,CAACsB,kBAAkB;UAChCiF,SAAS,EAAE;SACZ,CAAC;QAEF,IAAI,CAACN,WAAW,EAAE;QAElB,IAAI,CAAC3B,eAAe,EAAE;QAEtB,IAAI,CAACC,WAAW,EAAE;MACpB,CAAC,CAAC,CAAC2B,KAAK,CAACC,KAAK,IAAG;QACfd,KAAK,CAAC,4CAA4C,CAAC;MACrD,CAAC,CAAC;IACJ;EACF;EAEAvF,cAAcA,CAAC6E,QAAkB;IAC/B,IAAI,CAACA,QAAQ,CAACzF,EAAE,EAAE;IAElB,IAAIyF,QAAQ,CAAC/E,KAAK,GAAG,CAAC,EAAE+E,QAAQ,CAAC/E,KAAK,GAAG,CAAC;IAC1C,IAAI+E,QAAQ,CAAC3E,OAAO,GAAG,CAAC,EAAE2E,QAAQ,CAAC3E,OAAO,GAAG,CAAC;IAC9C,IAAI2E,QAAQ,CAAC/E,KAAK,GAAG,EAAE,EAAE+E,QAAQ,CAAC/E,KAAK,GAAG,EAAE;IAC5C,IAAI+E,QAAQ,CAAC3E,OAAO,GAAG,EAAE,EAAE2E,QAAQ,CAAC3E,OAAO,GAAG,EAAE;IAEhD,MAAM2G,YAAY,GAAGhC,QAAQ,CAAC/E,KAAK;IACnC,MAAMgH,cAAc,GAAGjC,QAAQ,CAAC3E,OAAO;IAEvC,MAAM6G,mBAAmB,GAAG,IAAI,CAAC9F,UAAU,CACxC+F,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7H,EAAE,KAAKyF,QAAQ,CAACzF,EAAE,CAAC,CACjCuF,MAAM,CAAC,CAACC,KAAK,EAAEqC,CAAC,KAAKrC,KAAK,GAAGqC,CAAC,CAACnH,KAAK,GAAImH,CAAC,CAAC/G,OAAO,GAAG,EAAG,EAAE,CAAC,CAAC;IAE9D,MAAMgH,SAAS,GAAGH,mBAAmB,GAAGF,YAAY,GAAIC,cAAc,GAAG,EAAG;IAE5E,IAAII,SAAS,GAAG,EAAE,EAAE;MAClB3B,KAAK,CAAC,mCAAmC,CAAC;MAC1C,IAAI,CAAChD,cAAc,EAAE;MACrB;IACF;IAEA,IAAI,CAACR,kBAAkB,CAAC/B,cAAc,CAAC6E,QAAQ,CAACzF,EAAE,EAAEyF,QAAQ,CAAC/E,KAAK,EAAE+E,QAAQ,CAAC3E,OAAO,CAAC,CAACgG,IAAI,CAAC,MAAK;MAC9F,IAAI,CAAC1B,eAAe,EAAE;MAEtB,IAAI,CAACC,WAAW,EAAE;IACpB,CAAC,CAAC,CAAC2B,KAAK,CAACC,KAAK,IAAG;MACfd,KAAK,CAAC,4CAA4C,CAAC;MACnD,IAAI,CAAChD,cAAc,EAAE;IACvB,CAAC,CAAC;EACJ;EAEAlC,cAAcA,CAAC8G,UAAkB;IAC/B,IAAI,CAACA,UAAU,EAAE;IAEjB,IAAI,CAACpF,kBAAkB,CAAC1B,cAAc,CAAC8G,UAAU,CAAC,CAACjB,IAAI,CAAC,MAAK;MAC3D,IAAI,CAACjF,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC+F,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7H,EAAE,KAAK+H,UAAU,CAAC;MAElE,IAAI,CAAC3C,eAAe,EAAE;MAEtB,IAAI,CAACC,WAAW,EAAE;IACpB,CAAC,CAAC,CAAC2B,KAAK,CAACC,KAAK,IAAG;MACfd,KAAK,CAAC,4CAA4C,CAAC;IACrD,CAAC,CAAC;EACJ;EAEAY,WAAWA,CAAA;IACT,MAAMiB,YAAY,GAAG,IAAI,CAAC9F,WAAW;IAErC,IAAI,CAACJ,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,WAAW,GAAG8F,YAAY,IAAI,GAAG;IACtC,IAAI,CAAC7F,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACC,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,cAAc,GAAG,KAAK;IAE3Be,UAAU,CAAC,MAAK;MACd,MAAM4E,aAAa,GAAGC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAsB;MACpF,IAAIF,aAAa,EAAE;QACjBA,aAAa,CAAChC,KAAK,GAAG,EAAE;QAExB,IAAI,CAACnE,kBAAkB,GAAG,EAAE;QAC5B,IAAI,CAACO,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACC,cAAc,GAAG,KAAK;MAC7B;IACF,CAAC,EAAE,CAAC,CAAC;EACP;EAEA8F,YAAYA,CAAA,GACZ;EAEAhC,iBAAiBA,CAAC1F,KAAa,EAAEI,OAAe;IAC9C,MAAMuH,YAAY,GAAG,IAAI,CAACxG,UAAU,CAAC0D,MAAM,CAAC,CAACC,KAAK,EAAEC,QAAQ,KAAI;MAC9D,OAAOD,KAAK,GAAGC,QAAQ,CAAC/E,KAAK,GAAI+E,QAAQ,CAAC3E,OAAO,GAAG,EAAG;IACzD,CAAC,EAAE,CAAC,CAAC;IAEL,MAAMgH,SAAS,GAAGO,YAAY,GAAG3H,KAAK,GAAII,OAAO,GAAG,EAAG;IAEvD,OAAOgH,SAAS,IAAI,EAAE;EACxB;EAEAxE,eAAeA,CAAA;IACb,MAAMgF,MAAM,GAAGJ,QAAQ,CAACC,cAAc,CAAC,WAAW,CAAsB;IACxE,IAAI,CAACG,MAAM,EAAE;IAEb,MAAMC,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;IACnC,IAAI,CAACD,GAAG,EAAE;IAEV,IAAI,IAAI,CAAC9F,SAAS,EAAE;MAClB,IAAI,CAACA,SAAS,CAACgG,OAAO,EAAE;MACxB,IAAI,CAAChG,SAAS,GAAG,IAAI;IACvB;IAEAvE,KAAK,CAACiD,QAAQ,CAAC;MACbnB,EAAE,EAAE,YAAY;MAChB0I,UAAU,EAAGC,KAAU,IAAI;QACzB,MAAMJ,GAAG,GAAGI,KAAK,CAACJ,GAAG;QACrB,MAAMK,KAAK,GAAGD,KAAK,CAACC,KAAK;QACzB,MAAMC,MAAM,GAAGF,KAAK,CAACE,MAAM;QAE3BN,GAAG,CAACO,OAAO,EAAE;QACbP,GAAG,CAACQ,IAAI,GAAG,iBAAiB;QAC5BR,GAAG,CAACS,SAAS,GAAG,QAAQ;QACxBT,GAAG,CAACU,YAAY,GAAG,QAAQ;QAE3BV,GAAG,CAACW,SAAS,GAAG,SAAS;QACzBX,GAAG,CAACY,QAAQ,CAAC,KAAK,EAAEP,KAAK,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,CAAC;QAC1CN,GAAG,CAACa,IAAI,EAAE;MACZ;KACD,CAAC;IAEF,IAAI;MACF,IAAI,CAAC3G,SAAS,GAAG,IAAIvE,KAAK,CAACqK,GAAG,EAAE;QAC9BhC,IAAI,EAAE,UAAU;QAChB8C,IAAI,EAAE;UACJC,MAAM,EAAE,EAAE;UACVC,QAAQ,EAAE,CAAC;YACTF,IAAI,EAAE,EAAE;YACRG,eAAe,EAAE,EAAE;YACnBC,WAAW,EAAE,CAAC;YACdC,YAAY,EAAE,CAAC;YACfC,OAAO,EAAE;WACV;SACF;QACDC,OAAO,EAAE;UACPC,MAAM,EAAE,KAAK;UACbC,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACPC,MAAM,EAAE;cACNC,OAAO,EAAE;aACV;YACDC,OAAO,EAAE;cACPC,OAAO,EAAE,IAAI;cACbC,SAAS,EAAE;gBACTC,KAAK,EAAE,SAAAA,CAASC,OAAY;kBAC1B,MAAMrE,KAAK,GAAGqE,OAAO,CAACC,GAAa;kBACnC,MAAM7J,KAAK,GAAGiF,IAAI,CAACiB,KAAK,CAACX,KAAK,CAAC;kBAC/B,MAAMnF,OAAO,GAAG6E,IAAI,CAAC6E,KAAK,CAAC,CAACvE,KAAK,GAAGvF,KAAK,IAAI,EAAE,CAAC;kBAChD,OAAO,GAAGA,KAAK,KAAKI,OAAO,GAAG;gBAChC;;;WAGL;UACD2J,SAAS,EAAE;YACTC,aAAa,EAAE,IAAI;YACnBC,YAAY,EAAE;;;OAGnB,CAAC;MAEF,IAAI,CAACtF,WAAW,EAAE;IACpB,CAAC,CAAC,OAAO4B,KAAK,EAAE,CAChB;EACF;EAEA5B,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAC5C,SAAS,EAAE;MACnB,IAAI,CAACa,eAAe,EAAE;MACtB;IACF;IAEA,IAAI;MACF,MAAM+F,IAAI,GAAG,IAAI,CAACuB,yBAAyB,EAAE;MAE7C,IAAI,CAACnI,SAAS,CAAC4G,IAAI,CAACC,MAAM,GAAGD,IAAI,CAACC,MAAM;MACxC,IAAI,CAAC7G,SAAS,CAAC4G,IAAI,CAACE,QAAQ,CAAC,CAAC,CAAC,CAACF,IAAI,GAAGA,IAAI,CAACwB,MAAM;MAClD,IAAI,CAACpI,SAAS,CAAC4G,IAAI,CAACE,QAAQ,CAAC,CAAC,CAAC,CAACC,eAAe,GAAG,IAAI,CAACsB,cAAc,CAACzB,IAAI,CAACC,MAAM,CAACyB,MAAM,CAAC;MAEzF,IAAI,CAACtI,SAAS,CAACuI,MAAM,EAAE;IACzB,CAAC,CAAC,OAAO/D,KAAK,EAAE;MACd,IAAI,CAACxE,SAAS,GAAG,IAAI;MACrBY,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,eAAe,EAAE;MACxB,CAAC,EAAE,GAAG,CAAC;IACT;EACF;EAEAsH,yBAAyBA,CAAA;IACvB,MAAMtB,MAAM,GAAa,EAAE;IAC3B,MAAMuB,MAAM,GAAa,EAAE;IAE3B,IAAI,CAAChJ,UAAU,CAACoJ,OAAO,CAACxF,QAAQ,IAAG;MACjC6D,MAAM,CAAC/E,IAAI,CAAC,GAAGkB,QAAQ,CAACvF,KAAK,IAAIuF,QAAQ,CAACtF,IAAI,EAAE,CAAC;MACjD0K,MAAM,CAACtG,IAAI,CAACkB,QAAQ,CAAC/E,KAAK,GAAI+E,QAAQ,CAAC3E,OAAO,GAAG,EAAG,CAAC;IACvD,CAAC,CAAC;IAEF,MAAMwE,UAAU,GAAGuF,MAAM,CAACtF,MAAM,CAAC,CAACsC,CAAC,EAAEqD,CAAC,KAAKrD,CAAC,GAAGqD,CAAC,EAAE,CAAC,CAAC;IACpD,IAAI5F,UAAU,GAAG,EAAE,EAAE;MACnBgE,MAAM,CAAC/E,IAAI,CAAC,WAAW,CAAC;MACxBsG,MAAM,CAACtG,IAAI,CAAC,EAAE,GAAGe,UAAU,CAAC;IAC9B;IAEA,OAAO;MAAEgE,MAAM;MAAEuB;IAAM,CAAE;EAC3B;EAEAC,cAAcA,CAACK,KAAa;IAC1B,MAAMC,MAAM,GAAa,EAAE;IAE3B,IAAI,CAACvJ,UAAU,CAACoJ,OAAO,CAACxF,QAAQ,IAAG;MACjC,MAAMY,YAAY,GAAG,IAAI,CAACzE,aAAa,CAAC0E,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACpG,IAAI,KAAKsF,QAAQ,CAACtF,IAAI,CAAC;MAEjF,IAAIkG,YAAY,IAAIA,YAAY,CAACgF,KAAK,EAAE;QACtCD,MAAM,CAAC7G,IAAI,CAAC8B,YAAY,CAACgF,KAAK,CAAC;MACjC,CAAC,MAAM,IAAI5F,QAAQ,CAAC4B,SAAS,EAAE;QAC7B+D,MAAM,CAAC7G,IAAI,CAAC,SAAS,CAAC;MACxB,CAAC,MAAM;QACL6G,MAAM,CAAC7G,IAAI,CAAC,SAAS,CAAC;MACxB;IACF,CAAC,CAAC;IAEF,IAAI4G,KAAK,GAAGC,MAAM,CAACL,MAAM,EAAE;MACzBK,MAAM,CAAC7G,IAAI,CAAC,SAAS,CAAC;IACxB;IAEA,OAAO6G,MAAM;EACf;;mBA1gBWhK,eAAe;;mCAAfA,gBAAe;AAAA;;QAAfA,gBAAe;EAAAkK,SAAA;EAAAC,SAAA,WAAAC,sBAAAC,EAAA,EAAAlD,GAAA;IAAA,IAAAkD,EAAA;;;;;;;;;;;;;MC1BpBnN,EAFR,CAAAC,cAAA,aAAuB,aACX,aACc;MACdD,EAAA,CAAAoN,SAAA,aAA6D;MAC7DpN,EAAA,CAAAC,cAAA,WAAM;MAAAD,EAAA,CAAAE,MAAA,cAAO;MACjBF,EADiB,CAAAG,YAAA,EAAO,EAClB;MACNH,EAAA,CAAAC,cAAA,SAAI;MAAAD,EAAA,CAAAE,MAAA,mBAAY;MACpBF,EADoB,CAAAG,YAAA,EAAK,EAChB;MAIDH,EAFR,CAAAC,cAAA,aAA2B,aACG,iBACkC;MAAzBD,EAAA,CAAAO,UAAA,mBAAA8M,kDAAA;QAAA,OAASpD,GAAA,CAAAvD,UAAA,EAAY,CAAC,CAAC;MAAA,EAAC;MAAC1G,EAAA,CAAAE,MAAA,cAAC;MAAAF,EAAA,CAAAG,YAAA,EAAS;MAClEH,EAAA,CAAAC,cAAA,cAAkB;MACdD,EAAA,CAAAsN,UAAA,KAAAC,+BAAA,iBAAuD;MAC3DvN,EAAA,CAAAG,YAAA,EAAM;MACNH,EAAA,CAAAC,cAAA,iBAAuD;MAAxBD,EAAA,CAAAO,UAAA,mBAAAiN,kDAAA;QAAA,OAASvD,GAAA,CAAAvD,UAAA,CAAW,CAAC,CAAC;MAAA,EAAC;MAAC1G,EAAA,CAAAE,MAAA,cAAC;MAC5DF,EAD4D,CAAAG,YAAA,EAAS,EAC/D;MACNH,EAAA,CAAAC,cAAA,cAAmB;MACfD,EAAA,CAAAsN,UAAA,KAAAG,+BAAA,kBAKwD;MAIhEzN,EADI,CAAAG,YAAA,EAAM,EACJ;MAGFH,EADJ,CAAAC,cAAA,mBAA+B,UACvB;MAAAD,EAAA,CAAAE,MAAA,kBAAU;MAAAF,EAAA,CAAAG,YAAA,EAAK;MAGfH,EADJ,CAAAC,cAAA,eAA4B,kBAC+C;MAA3CD,EAAA,CAAAO,UAAA,oBAAAmN,mDAAA1L,MAAA;QAAA,OAAUiI,GAAA,CAAA1C,uBAAA,CAAAvF,MAAA,CAA+B;MAAA,EAAC;MAClEhC,EAAA,CAAAC,cAAA,kBAAiB;MAAAD,EAAA,CAAAE,MAAA,uBAAe;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACzCH,EAAA,CAAAsN,UAAA,KAAAK,kCAAA,qBAAyH;MAGzH3N,EAAA,CAAAC,cAAA,kBAAuB;MAAAD,EAAA,CAAAE,MAAA,8BAAiB;MAC5CF,EAD4C,CAAAG,YAAA,EAAS,EAC5C;MAKDH,EAFR,CAAAC,cAAA,eAAwF,eAC3D,iBAC0D;MAAzBD,EAAA,CAAA8B,gBAAA,2BAAA8L,yDAAA5L,MAAA;QAAAhC,EAAA,CAAAmC,kBAAA,CAAA8H,GAAA,CAAAxG,UAAA,EAAAzB,MAAA,MAAAiI,GAAA,CAAAxG,UAAA,GAAAzB,MAAA;QAAA,OAAAA,MAAA;MAAA,EAAwB;MAA9EhC,EAAA,CAAAG,YAAA,EAA+E;MAC/EH,EAAA,CAAAC,cAAA,YAAM;MAAAD,EAAA,CAAAE,MAAA,SAAC;MAAAF,EAAA,CAAAG,YAAA,EAAO;MACdH,EAAA,CAAAC,cAAA,iBAAmF;MAA3BD,EAAA,CAAA8B,gBAAA,2BAAA+L,yDAAA7L,MAAA;QAAAhC,EAAA,CAAAmC,kBAAA,CAAA8H,GAAA,CAAAvG,YAAA,EAAA1B,MAAA,MAAAiI,GAAA,CAAAvG,YAAA,GAAA1B,MAAA;QAAA,OAAAA,MAAA;MAAA,EAA0B;MAAlFhC,EAAA,CAAAG,YAAA,EAAmF;MACnFH,EAAA,CAAAC,cAAA,YAAM;MAAAD,EAAA,CAAAE,MAAA,SAAC;MACXF,EADW,CAAAG,YAAA,EAAO,EACZ;MACNH,EAAA,CAAAC,cAAA,kBAAgC;MAAxBD,EAAA,CAAAO,UAAA,mBAAAuN,kDAAA;QAAA,OAAS7D,GAAA,CAAArC,WAAA,EAAa;MAAA,EAAC;MAAC5H,EAAA,CAAAE,MAAA,WAAG;MACvCF,EADuC,CAAAG,YAAA,EAAS,EAC1C;MAKEH,EAFR,CAAAC,cAAA,eAA2G,eAC/E,iBAC+D;MAAxCD,EAAA,CAAA8B,gBAAA,2BAAAiM,yDAAA/L,MAAA;QAAAhC,EAAA,CAAAmC,kBAAA,CAAA8H,GAAA,CAAArG,WAAA,EAAA5B,MAAA,MAAAiI,GAAA,CAAArG,WAAA,GAAA5B,MAAA;QAAA,OAAAA,MAAA;MAAA,EAAyB;MAApEhC,EAAA,CAAAG,YAAA,EAAmF;MACnFH,EAAA,CAAAC,cAAA,iBAAwG;MAAjCD,EAAA,CAAA8B,gBAAA,2BAAAkM,yDAAAhM,MAAA;QAAAhC,EAAA,CAAAmC,kBAAA,CAAA8H,GAAA,CAAAtG,kBAAA,EAAA3B,MAAA,MAAAiI,GAAA,CAAAtG,kBAAA,GAAA3B,MAAA;QAAA,OAAAA,MAAA;MAAA,EAAgC;MAC3GhC,EADI,CAAAG,YAAA,EAAwG,EACtG;MAEFH,EADJ,CAAAC,cAAA,eAAyB,iBACsE;MAA/BD,EAAA,CAAA8B,gBAAA,2BAAAmM,yDAAAjM,MAAA;QAAAhC,EAAA,CAAAmC,kBAAA,CAAA8H,GAAA,CAAApG,gBAAA,EAAA7B,MAAA,MAAAiI,GAAA,CAAApG,gBAAA,GAAA7B,MAAA;QAAA,OAAAA,MAAA;MAAA,EAA8B;MAA1FhC,EAAA,CAAAG,YAAA,EAA2F;MAC3FH,EAAA,CAAAC,cAAA,YAAM;MAAAD,EAAA,CAAAE,MAAA,SAAC;MAAAF,EAAA,CAAAG,YAAA,EAAO;MACdH,EAAA,CAAAC,cAAA,iBAA+F;MAAjCD,EAAA,CAAA8B,gBAAA,2BAAAoM,yDAAAlM,MAAA;QAAAhC,EAAA,CAAAmC,kBAAA,CAAA8H,GAAA,CAAAnG,kBAAA,EAAA9B,MAAA,MAAAiI,GAAA,CAAAnG,kBAAA,GAAA9B,MAAA;QAAA,OAAAA,MAAA;MAAA,EAAgC;MAA9FhC,EAAA,CAAAG,YAAA,EAA+F;MAC/FH,EAAA,CAAAC,cAAA,YAAM;MAAAD,EAAA,CAAAE,MAAA,SAAC;MACXF,EADW,CAAAG,YAAA,EAAO,EACZ;MACNH,EAAA,CAAAC,cAAA,kBAAsC;MAA9BD,EAAA,CAAAO,UAAA,mBAAA4N,kDAAA;QAAA,OAASlE,GAAA,CAAAjB,iBAAA,EAAmB;MAAA,EAAC;MAAChJ,EAAA,CAAAE,MAAA,WAAG;MAEjDF,EAFiD,CAAAG,YAAA,EAAS,EAChD,EACJ;MAENH,EAAA,CAAAC,cAAA,eAAgC;MAC5BD,EAAA,CAAAoN,SAAA,kBAAgC;MAE5BpN,EADJ,CAAAC,cAAA,eAA0B,eACE;MACpBD,EAAA,CAAAE,MAAA,wBAAe;MAAAF,EAAA,CAAAC,cAAA,gBAAwB;MAAAD,EAAA,CAAAE,MAAA,IAAwB;MACnEF,EADmE,CAAAG,YAAA,EAAO,EACpE;MACNH,EAAA,CAAAC,cAAA,eAA4B;MACxBD,EAAA,CAAAE,MAAA,oBAAW;MAAAF,EAAA,CAAAC,cAAA,gBAAyB;MAAAD,EAAA,CAAAE,MAAA,IAAqB;MAGrEF,EAHqE,CAAAG,YAAA,EAAO,EAC9D,EACJ,EACJ;MAENH,EAAA,CAAAC,cAAA,eAA2B;MACvBD,EAAA,CAAAsN,UAAA,KAAAc,+BAAA,mBAA6F;MAqBzGpO,EAFQ,CAAAG,YAAA,EAAM,EACA,EACR;;;MAzFoDH,EAAA,CAAAI,SAAA,IAAW;MAAXJ,EAAA,CAAAwB,UAAA,YAAAyI,GAAA,CAAAhH,QAAA,CAAW;MAKnCjD,EAAA,CAAAI,SAAA,GAAY;MAAZJ,EAAA,CAAAwB,UAAA,YAAAyI,GAAA,CAAA/G,SAAA,CAAY;MAiBLlD,EAAA,CAAAI,SAAA,GAAgB;MAAhBJ,EAAA,CAAAwB,UAAA,YAAAyI,GAAA,CAAA3G,aAAA,CAAgB;MAOXtD,EAAA,CAAAI,SAAA,GAAqD;MAArDJ,EAAA,CAAAqO,WAAA,YAAApE,GAAA,CAAAlG,iBAAA,mBAAqD;MAEzB/D,EAAA,CAAAI,SAAA,GAAwB;MAAxBJ,EAAA,CAAA4C,gBAAA,YAAAqH,GAAA,CAAAxG,UAAA,CAAwB;MAEtBzD,EAAA,CAAAI,SAAA,GAA0B;MAA1BJ,EAAA,CAAA4C,gBAAA,YAAAqH,GAAA,CAAAvG,YAAA,CAA0B;MAOlC1D,EAAA,CAAAI,SAAA,GAAkD;MAAlDJ,EAAA,CAAAqO,WAAA,YAAApE,GAAA,CAAAjG,cAAA,mBAAkD;MAEvDhE,EAAA,CAAAI,SAAA,GAAyB;MAAzBJ,EAAA,CAAA4C,gBAAA,YAAAqH,GAAA,CAAArG,WAAA,CAAyB;MACG5D,EAAA,CAAAI,SAAA,EAAgC;MAAhCJ,EAAA,CAAA4C,gBAAA,YAAAqH,GAAA,CAAAtG,kBAAA,CAAgC;MAG3C3D,EAAA,CAAAI,SAAA,GAA8B;MAA9BJ,EAAA,CAAA4C,gBAAA,YAAAqH,GAAA,CAAApG,gBAAA,CAA8B;MAE5B7D,EAAA,CAAAI,SAAA,GAAgC;MAAhCJ,EAAA,CAAA4C,gBAAA,YAAAqH,GAAA,CAAAnG,kBAAA,CAAgC;MAWvD9D,EAAA,CAAAI,SAAA,IAAwB;MAAxBJ,EAAA,CAAAsB,kBAAA,KAAA2I,GAAA,CAAAhG,iBAAA,MAAwB;MAG3BjE,EAAA,CAAAI,SAAA,GAAqB;MAArBJ,EAAA,CAAAsB,kBAAA,KAAA2I,GAAA,CAAA/F,cAAA,MAAqB;MAMvClE,EAAA,CAAAI,SAAA,GAAa;MAAbJ,EAAA,CAAAwB,UAAA,YAAAyI,GAAA,CAAA1G,UAAA,CAAa;;;iBDvDvC/D,WAAW,EAAEF,YAAY,EAAAgP,EAAA,CAAAC,OAAA,EAAEhP,WAAW,EAAAiP,EAAA,CAAAC,cAAA,EAAAD,EAAA,CAAAE,uBAAA,EAAAF,EAAA,CAAAG,oBAAA,EAAAH,EAAA,CAAAI,mBAAA,EAAAJ,EAAA,CAAAK,eAAA,EAAAL,EAAA,CAAAM,YAAA,EAAAN,EAAA,CAAAO,YAAA,EAAAP,EAAA,CAAAQ,OAAA,EAAEvP,YAAY,EAAEM,mBAAmB;EAAAkP,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}