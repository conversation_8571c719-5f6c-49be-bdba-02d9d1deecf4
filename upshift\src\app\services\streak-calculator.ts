﻿import { Injectable } from '@angular/core';
import { SupabaseService } from './supabase.service';
import { Quest, QuestProgress } from '../models/quest.model';

@Injectable({
  providedIn: 'root'
})
export class StreakCalculatorService {
  constructor(private supabaseService: SupabaseService) {}

  async calculateStreaks(userId: string,  quests: Quest[]): Promise<{ [questId: string]: number }> {
    const dayNameMapping: { [key: string]: string } = {
      'Monday': 'Mon',
      'Tuesday': 'Tue',
      'Wednesday': 'Wed',
      'Thursday': 'Thu',
      'Friday': 'Fri',
      'Saturday': 'Sat',
      'Sunday': 'Sun'
    };

    const today = new Date();
    const dateString = today.toLocaleDateString('en-CA');

    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    const yesterdayString = yesterday.toISOString().split('T')[0];

    const { data: allProgress, error: progressError } = await this.supabaseService.getClient()
      .from('quest_progress')
      .select('*')
      .eq('user_id', userId);

    if (progressError) {
      return {};
    }

    if (!allProgress || allProgress.length === 0) {
      return {};
    }

    const progressByDate: { [date: string]: { [questId: string]: QuestProgress } } = {};

    allProgress.forEach(progress => {
      if (!progressByDate[progress.date]) {
        progressByDate[progress.date] = {};
      }
      progressByDate[progress.date][progress.quest_id] = progress;
    });

    const streaks: { [questId: string]: number } = {};

    for (const quest of quests) {
      if (!quest.id) {
        continue;
      }

      const questId = quest.id;
      const dateProgress = progressByDate[dateString] && progressByDate[dateString][questId]
        ? progressByDate[dateString][questId]
        : null;

      const yesterdayProgress = progressByDate[yesterdayString] && progressByDate[yesterdayString][questId]
        ? progressByDate[yesterdayString][questId]
        : null;

      const createdDate = new Date(quest.created_at);
      const createdDateString = createdDate.toISOString().split('T')[0];

      let displayStreak = 0;

      let currentDate = new Date(yesterday);
      let streakCount = 0;

      while (true) {
        if (currentDate < createdDate) {
          break;
        }

        let shouldCount = true;

        if (quest.goal_period === 'week' && quest.task_days_of_week) {
          const dayName = currentDate.toLocaleDateString('en-US', { weekday: 'long' });
          const currentDay = dayNameMapping[dayName];
          const taskDays = quest.task_days_of_week.split(',').map(day => day.trim());

          if (!taskDays.includes(currentDay)) {
            shouldCount = false;
          }
        } else if (quest.goal_period === 'month' && quest.task_days_of_month) {
          const taskDays = quest.task_days_of_month.split(',').map(day => parseInt(day.trim()));

          if (!taskDays.includes(currentDate.getDate())) {
            shouldCount = false;
          }
        }

        if (shouldCount) {
          const currentDateString = currentDate.toISOString().split('T')[0];

          if (currentDate < createdDate) {
            break;
          }

          const progress = progressByDate[currentDateString] && progressByDate[currentDateString][questId]
            ? progressByDate[currentDateString][questId]
            : null;

          if (!progress || !progress.completed) {
            break;
          }

          streakCount++;
        } else {
        }

        currentDate.setDate(currentDate.getDate() - 1);
      }

      if (quest.goal_period === 'week' && quest.task_days_of_week) {
        const dayName = today.toLocaleDateString('en-US', { weekday: 'long' });
        const currentDay = dayNameMapping[dayName];
        const taskDays = quest.task_days_of_week.split(',').map(day => day.trim());

        if (taskDays.includes(currentDay)) {
          if (dateProgress?.completed) {
            displayStreak = streakCount + 1;
          } else {
            displayStreak = streakCount;
          }
        } else {
          displayStreak = streakCount;
        }
      } else if (quest.goal_period === 'month' && quest.task_days_of_month) {
        const taskDays = quest.task_days_of_month.split(',').map(day => parseInt(day.trim()));

        if (taskDays.includes(today.getDate())) {
          if (dateProgress?.completed) {
            displayStreak = streakCount + 1;
          } else {
            displayStreak = streakCount;
          }
        } else {
          displayStreak = streakCount;
        }
      } else {
        if (yesterdayProgress && yesterdayProgress.completed) {
          displayStreak = streakCount;

          if (dateProgress?.completed) {
            displayStreak += 1;
          }
        } else {
          if (dateProgress?.completed) {
            displayStreak = 1;
          } else {
            displayStreak = 0;
          }
        }
      }

      streaks[questId] = displayStreak;
    }

    return streaks;
  }

  async calculateStreak(userId: string, questId: string): Promise<number> {
    const { data: quest, error: questError } = await this.supabaseService.getClient()
      .from('quests')
      .select('*')
      .eq('id', questId)
      .maybeSingle();

    if (questError || !quest) {
      return 0;
    }

    const streaks = await this.calculateStreaks(userId, [quest]);
    return streaks[questId] || 0;
  }
}
