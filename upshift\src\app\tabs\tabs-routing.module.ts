import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { TabsPage } from "./tabs.page";
import { AuthGuard } from "../guards/auth.guard";

const routes: Routes = [
  {
    path: 'tabs',
    component: TabsPage,
    canActivate: [AuthGuard],
    children: [
      {
        path: 'home',
        loadChildren: () => import('../pages/today/today.module').then(m => m.TodayPageModule),
      },
      {
        path: 'groups',
        loadComponent: () => import('../pages/groups/group-list/group-list.page').then(m => m.GroupListPage),
        children: [
          {
            path: 'create-group',
            loadComponent: () => import('../pages/groups/create-group/create-group.page').then(m => m.CreateGroupPage),
          },
          {
            path: 'group-requests',
            loadComponent: () => import('../pages/groups/group-requests/group-requests.page').then(m => m.GroupRequestsPage),
          },
          {
            path: ':id',
            loadComponent: () => import('../pages/groups/group-detail/group-detail.page').then(m => m.GroupDetailPage),
            children: [
              {
                path: 'settings',
                loadComponent: () => import('../pages/groups/group-settings/group-settings.page').then(m => m.GroupSettingsPage),
              }
            ]
          }
        ]
      },
      {
        path: 'leaderboards',
        children: [
          {
            path: '',
            redirectTo: 'groups',
            pathMatch: 'full'
          },
          {
            path: 'groups',
            loadComponent: () => import('../pages/leaderboard/leaderboard.page').then(m => m.LeaderboardPage),
          },
          {
            path: 'users',
            loadComponent: () => import('../pages/leaderboard/leaderboard.page').then(m => m.LeaderboardPage),
          }
        ]
      },
      {
        path: 'goals',
        loadComponent: () => import('../pages/goals/goal-list/goal-list.page').then(m => m.GoalListPage),
        children: [
          {
            path: 'time-tracker',
            loadComponent: () => import('../pages/time-tracker/time-tracker.page').then(m => m.TimeTrackerPage),
          },
          {
            path: 'focus',
            loadComponent: () => import('../pages/focus/focus.page').then(m => m.FocusPage),
          },
          {
            path: ':id',
            loadComponent: () => import('../pages/goals/goal-detail/goal-detail.page').then(m => m.GoalDetailPage),
          }
        ]
      },
      {
        path: 'profile',
        loadComponent: () => import('../pages/profile/profile.page').then(m => m.ProfilePage),
        children: [
          {
            path: '',
            redirectTo: 'badges',
            pathMatch: 'full'
          },
          {
            path: 'badges',
            loadComponent: () => import('../pages/badges/user-badges.page').then(m => m.UserBadgesPage),
          },
          {
            path: 'badges/:id',
            loadComponent: () => import('../pages/badges/user-badges.page').then(m => m.UserBadgesPage),
          },
          {
            path: 'profile-settings',
            loadComponent: () => import('../pages/profile-settings/profile-settings.page').then(m => m.ProfileSettingsPage),
          },
          {
            path: 'friends',
            loadComponent: () => import('../pages/friends/friends.page').then(m => m.FriendsPage),
            children: [
              {
                path: ':id',
                loadComponent: () => import('../pages/friends/friend-profile/friend-profile.page').then(m => m.FriendProfilePage),
              },
              {
                path: 'affiliates',
                loadComponent: () => import('../pages/affiliates/affiliates.page').then(m => m.AffiliatesPage),
              }
            ]
          }
        ]
      },
      {
        path: 'import-sidequests',
        loadComponent: () => import('../pages/import-sidequests/import-sidequests.page').then(m => m.ImportSideQuestsPage),
      },
      {
        path: 'admin',
        loadComponent: () => import('../pages/admin/admin.page').then(m => m.AdminPage),
        children: [
          {
            path: 'collection/:name',
            loadComponent: () => import('../pages/admin/collection-detail/collection-detail.page').then(m => m.CollectionDetailPage),
          }
        ]
      },
      {
        path: 'user-profile/:id',
        loadComponent: () => import('../pages/user-profile/user-profile.page').then(m => m.UserProfilePage),
      },
      {
        path: '',
        redirectTo: '/tabs/home',
        pathMatch: 'full'
      }
    ]
  },
  {
    path: '',
    redirectTo: '/tabs/home',
    pathMatch: 'full'
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class TabsRoutingModule { }
