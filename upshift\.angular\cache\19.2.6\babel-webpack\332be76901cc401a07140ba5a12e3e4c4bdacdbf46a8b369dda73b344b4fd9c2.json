{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _AffiliatesPage;\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { Subscription } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/supabase.service\";\nimport * as i2 from \"@ionic/angular\";\nimport * as i3 from \"@angular/common\";\nfunction AffiliatesPage_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵelement(1, \"ion-spinner\", 17);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading offers...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AffiliatesPage_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵelement(1, \"ion-icon\", 19);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"No affiliate offers available at the moment.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 20);\n    i0.ɵɵtext(5, \"Check back later for exciting rewards!\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AffiliatesPage_div_27_ion_card_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵelement(1, \"ion-icon\", 45);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Claimed\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AffiliatesPage_div_27_ion_card_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵelement(1, \"ion-icon\", 47);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Available\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AffiliatesPage_div_27_ion_card_7_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵelement(1, \"img\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const offer_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"alt\", offer_r2.name);\n    i0.ɵɵproperty(\"src\", offer_r2.image_cover, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction AffiliatesPage_div_27_ion_card_7_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵelement(1, \"ion-icon\", 51);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AffiliatesPage_div_27_ion_card_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-card\", 26);\n    i0.ɵɵlistener(\"click\", function AffiliatesPage_div_27_ion_card_7_Template_ion_card_click_0_listener() {\n      const offer_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.viewOfferDetails(offer_r2));\n    });\n    i0.ɵɵtemplate(1, AffiliatesPage_div_27_ion_card_7_div_1_Template, 4, 0, \"div\", 27)(2, AffiliatesPage_div_27_ion_card_7_div_2_Template, 4, 0, \"div\", 28)(3, AffiliatesPage_div_27_ion_card_7_div_3_Template, 2, 2, \"div\", 29)(4, AffiliatesPage_div_27_ion_card_7_div_4_Template, 2, 0, \"div\", 30);\n    i0.ɵɵelementStart(5, \"ion-card-content\")(6, \"h3\", 31);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 32);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 33)(11, \"div\", 34);\n    i0.ɵɵelement(12, \"ion-icon\", 35);\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 36);\n    i0.ɵɵelement(16, \"ion-icon\", 37);\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 38)(20, \"div\", 39);\n    i0.ɵɵelement(21, \"div\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 41)(23, \"ion-button\", 42);\n    i0.ɵɵtext(24, \" View Details \");\n    i0.ɵɵelement(25, \"ion-icon\", 43);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const offer_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"claimed\", ctx_r2.isOfferClaimed(offer_r2.id))(\"available\", ctx_r2.canClaimOffer(offer_r2));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isOfferClaimed(offer_r2.id));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.canClaimOffer(offer_r2) && !ctx_r2.isOfferClaimed(offer_r2.id));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", offer_r2.image_cover);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !offer_r2.image_cover);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(offer_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(offer_r2.description);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.formatPrice(offer_r2.price_value));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"met\", ctx_r2.userAffiliateCount >= offer_r2.number_of_invites_required);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.userAffiliateCount, \"/\", offer_r2.number_of_invites_required, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"width\", ctx_r2.userAffiliateCount / offer_r2.number_of_invites_required * 100 + \"%\");\n    i0.ɵɵclassProp(\"complete\", ctx_r2.userAffiliateCount >= offer_r2.number_of_invites_required);\n  }\n}\nfunction AffiliatesPage_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"h2\");\n    i0.ɵɵtext(3, \"Available Rewards\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 23);\n    i0.ɵɵtext(5, \"Claim these rewards when you reach the required number of affiliates\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 24);\n    i0.ɵɵtemplate(7, AffiliatesPage_div_27_ion_card_7_Template, 26, 19, \"ion-card\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.affiliateOffers);\n  }\n}\nfunction AffiliatesPage_ng_template_29_div_8_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"img\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r2.selectedOffer.image_cover, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r2.selectedOffer.name);\n  }\n}\nfunction AffiliatesPage_ng_template_29_div_8_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵelement(1, \"ion-icon\", 19);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AffiliatesPage_ng_template_29_div_8_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵelement(1, \"ion-icon\", 74);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"You need \", ctx_r2.selectedOffer.number_of_invites_required - ctx_r2.userAffiliateCount, \" more affiliate(s) to claim this reward\");\n  }\n}\nfunction AffiliatesPage_ng_template_29_div_8_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 75);\n    i0.ɵɵelement(2, \"ion-icon\", 45);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Reward Claimed\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AffiliatesPage_ng_template_29_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"div\", 58);\n    i0.ɵɵtemplate(2, AffiliatesPage_ng_template_29_div_8_div_2_Template, 2, 2, \"div\", 59)(3, AffiliatesPage_ng_template_29_div_8_div_3_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementStart(4, \"div\", 61)(5, \"h1\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 62);\n    i0.ɵɵelement(8, \"ion-icon\", 35);\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"div\", 63)(12, \"h3\");\n    i0.ɵɵtext(13, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 63)(17, \"h3\");\n    i0.ɵɵtext(18, \"Requirements\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 64)(20, \"div\", 39);\n    i0.ɵɵelement(21, \"div\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 65);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(24, AffiliatesPage_ng_template_29_div_8_div_24_Template, 4, 1, \"div\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, AffiliatesPage_ng_template_29_div_8_div_25_Template, 5, 0, \"div\", 67);\n    i0.ɵɵelementStart(26, \"div\", 68)(27, \"ion-button\", 69);\n    i0.ɵɵlistener(\"click\", function AffiliatesPage_ng_template_29_div_8_Template_ion_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.claimOffer(ctx_r2.selectedOffer));\n    });\n    i0.ɵɵelement(28, \"ion-icon\", 70);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedOffer.image_cover);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.selectedOffer.image_cover);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedOffer.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"You save \", ctx_r2.formatPrice(ctx_r2.selectedOffer.price_value, true), \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedOffer.description);\n    i0.ɵɵadvance(6);\n    i0.ɵɵstyleProp(\"width\", ctx_r2.userAffiliateCount / ctx_r2.selectedOffer.number_of_invites_required * 100 + \"%\");\n    i0.ɵɵclassProp(\"complete\", ctx_r2.userAffiliateCount >= ctx_r2.selectedOffer.number_of_invites_required);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"complete\", ctx_r2.userAffiliateCount >= ctx_r2.selectedOffer.number_of_invites_required);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", ctx_r2.userAffiliateCount, \"/\", ctx_r2.selectedOffer.number_of_invites_required, \" affiliates \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isOfferClaimed(ctx_r2.selectedOffer.id) && ctx_r2.userAffiliateCount < ctx_r2.selectedOffer.number_of_invites_required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isOfferClaimed(ctx_r2.selectedOffer.id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.canClaimOffer(ctx_r2.selectedOffer))(\"color\", ctx_r2.canClaimOffer(ctx_r2.selectedOffer) ? \"primary\" : \"medium\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r2.isOfferClaimed(ctx_r2.selectedOffer.id) ? \"checkmark-circle-outline\" : \"gift-outline\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isOfferClaimed(ctx_r2.selectedOffer.id) ? \"Already Claimed\" : \"Claim Reward\", \" \");\n  }\n}\nfunction AffiliatesPage_ng_template_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-header\", 0)(1, \"ion-toolbar\")(2, \"ion-title\");\n    i0.ɵɵtext(3, \"Reward Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ion-buttons\", 52)(5, \"ion-button\", 53);\n    i0.ɵɵlistener(\"click\", function AffiliatesPage_ng_template_29_Template_ion_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.closeOfferDetails());\n    });\n    i0.ɵɵelement(6, \"ion-icon\", 54);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(7, \"ion-content\", 55);\n    i0.ɵɵtemplate(8, AffiliatesPage_ng_template_29_div_8_Template, 30, 19, \"div\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedOffer);\n  }\n}\nexport class AffiliatesPage {\n  constructor(supabaseService, toastController) {\n    this.supabaseService = supabaseService;\n    this.toastController = toastController;\n    this.userId = null;\n    this.affiliateOffers = [];\n    this.userAffiliateCount = 0;\n    this.selectedOffer = null;\n    this.showOfferDetails = false;\n    this.userSubscription = new Subscription();\n    this.isLoading = true;\n    this.userRewards = [];\n  }\n  ngOnInit() {\n    this.isLoading = true;\n    const userDataSubscription = this.supabaseService.currentUser$.subscribe(user => {\n      if (user) {\n        this.userId = user.id;\n        this.loadUserData();\n        this.loadAffiliateOffers();\n        this.loadUserRewards();\n      } else {\n        this.userId = null;\n        this.isLoading = false;\n      }\n    });\n    this.userSubscription.add(userDataSubscription);\n  }\n  ngOnDestroy() {\n    this.userSubscription.unsubscribe();\n  }\n  loadUserData() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this.userId) return;\n      try {\n        const {\n          data,\n          error\n        } = yield _this.supabaseService.getClient().from('profiles').select('*').eq('id', _this.userId).single();\n        if (data && !error) {\n          _this.userAffiliateCount = data.number_of_affiliates || 0;\n        }\n      } catch (error) {}\n    })();\n  }\n  loadAffiliateOffers() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.userId) return;\n      try {\n        const {\n          data,\n          error\n        } = yield _this2.supabaseService.getClient().from('affiliate_offers').select('*').order('number_of_invites_required', {\n          ascending: true\n        });\n        if (error) {\n          throw error;\n        }\n        _this2.affiliateOffers = data || [];\n        _this2.affiliateOffers = _this2.affiliateOffers.map(offer => {\n          if (offer.image_cover && !offer.image_cover.includes('://')) {\n            try {\n              const {\n                data: urlData\n              } = _this2.supabaseService.getClient().storage.from('affiliate-offer').getPublicUrl(offer.image_cover);\n              return {\n                ...offer,\n                image_cover: urlData.publicUrl\n              };\n            } catch (err) {\n              const error = err;\n              return offer;\n            }\n          }\n          return offer;\n        });\n        _this2.isLoading = false;\n      } catch (error) {\n        _this2.isLoading = false;\n      }\n    })();\n  }\n  loadUserRewards() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.userId) return;\n      try {\n        const {\n          data,\n          error\n        } = yield _this3.supabaseService.getClient().from('affiliate_rewards_history').select('*').eq('user_id', _this3.userId);\n        if (error) {\n          throw error;\n        }\n        _this3.userRewards = data || [];\n      } catch (error) {}\n    })();\n  }\n  viewOfferDetails(offer) {\n    this.selectedOffer = offer;\n    this.showOfferDetails = true;\n  }\n  closeOfferDetails() {\n    this.showOfferDetails = false;\n    this.selectedOffer = null;\n  }\n  isOfferClaimed(offerId) {\n    return this.userRewards.some(reward => reward.affiliate_offer === offerId);\n  }\n  canClaimOffer(offer) {\n    return this.userAffiliateCount >= offer.number_of_invites_required && !this.isOfferClaimed(offer.id);\n  }\n  claimOffer(offer) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this4.userId || !_this4.canClaimOffer(offer)) return;\n      try {\n        const {\n          error\n        } = yield _this4.supabaseService.getClient().from('affiliate_rewards_history').insert([{\n          user_id: _this4.userId,\n          affiliate_offer: offer.id,\n          completed: true,\n          subscription_status: 'claimed',\n          affiliate_code_used: '',\n          number_of_affiliates: _this4.userAffiliateCount\n        }]);\n        if (error) {\n          throw error;\n        }\n        _this4.loadUserRewards();\n        const toast = yield _this4.toastController.create({\n          message: `Successfully claimed ${offer.name}! You saved ${_this4.formatPrice(offer.price_value)}.`,\n          duration: 3000,\n          position: 'bottom',\n          color: 'success',\n          buttons: [{\n            text: 'OK',\n            role: 'cancel'\n          }]\n        });\n        yield toast.present();\n        _this4.closeOfferDetails();\n      } catch (error) {\n        const toast = yield _this4.toastController.create({\n          message: 'Failed to claim offer. Please try again.',\n          duration: 3000,\n          position: 'bottom',\n          color: 'danger',\n          buttons: [{\n            text: 'OK',\n            role: 'cancel'\n          }]\n        });\n        yield toast.present();\n      }\n    })();\n  }\n  formatPrice(value, showCode = false) {\n    if (value === undefined || value === null) return '';\n    try {\n      return showCode ? `${value.toFixed(2)} USD` : `$${value.toFixed(2)}`;\n    } catch (error) {\n      return `$${value}`;\n    }\n  }\n}\n_AffiliatesPage = AffiliatesPage;\n_AffiliatesPage.ɵfac = function AffiliatesPage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _AffiliatesPage)(i0.ɵɵdirectiveInject(i1.SupabaseService), i0.ɵɵdirectiveInject(i2.ToastController));\n};\n_AffiliatesPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _AffiliatesPage,\n  selectors: [[\"app-affiliates\"]],\n  decls: 30,\n  vars: 7,\n  consts: [[1, \"ion-no-border\"], [\"slot\", \"start\"], [1, \"logo\"], [\"src\", \"assets/images/upshift_icon_mini.svg\", \"alt\", \"Upshift\"], [1, \"page-container\"], [1, \"header-section\"], [1, \"header-content\"], [1, \"subtitle\"], [1, \"affiliate-counter\"], [1, \"counter-value\"], [1, \"counter-label\"], [1, \"header-description\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"rewards-section\", 4, \"ngIf\"], [3, \"ionModalDidDismiss\", \"isOpen\", \"backdropDismiss\", \"cssClass\"], [1, \"loading-container\"], [\"name\", \"crescent\"], [1, \"empty-state\"], [\"name\", \"gift-outline\", \"size\", \"large\"], [1, \"empty-subtitle\"], [1, \"rewards-section\"], [1, \"section-header\"], [1, \"section-subtitle\"], [1, \"rewards-grid\"], [\"class\", \"reward-card\", 3, \"claimed\", \"available\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"reward-card\", 3, \"click\"], [\"class\", \"reward-status\", 4, \"ngIf\"], [\"class\", \"reward-status available\", 4, \"ngIf\"], [\"class\", \"reward-image\", 4, \"ngIf\"], [\"class\", \"reward-image placeholder\", 4, \"ngIf\"], [1, \"reward-title\"], [1, \"reward-description\"], [1, \"reward-details\"], [1, \"reward-value\"], [\"name\", \"cash-outline\"], [1, \"reward-requirement\"], [\"name\", \"people-outline\"], [1, \"reward-progress\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"reward-action\"], [\"expand\", \"block\", \"fill\", \"clear\", \"size\", \"small\"], [\"name\", \"chevron-forward-outline\", \"slot\", \"end\"], [1, \"reward-status\"], [\"name\", \"checkmark-circle\"], [1, \"reward-status\", \"available\"], [\"name\", \"gift\"], [1, \"reward-image\"], [3, \"src\", \"alt\"], [1, \"reward-image\", \"placeholder\"], [\"name\", \"gift-outline\"], [\"slot\", \"end\"], [3, \"click\"], [\"name\", \"close-outline\", \"size\", \"large\"], [1, \"ion-padding\"], [\"class\", \"modal-content\", 4, \"ngIf\"], [1, \"modal-content\"], [1, \"reward-detail-header\"], [\"class\", \"reward-detail-image\", 4, \"ngIf\"], [\"class\", \"reward-detail-image placeholder\", 4, \"ngIf\"], [1, \"reward-detail-title\"], [1, \"reward-detail-value\"], [1, \"reward-detail-section\"], [1, \"progress-container\"], [1, \"progress-text\"], [\"class\", \"requirement-message\", 4, \"ngIf\"], [\"class\", \"reward-detail-section\", 4, \"ngIf\"], [1, \"reward-detail-action\"], [\"expand\", \"block\", 3, \"click\", \"disabled\", \"color\"], [\"slot\", \"start\", 3, \"name\"], [1, \"reward-detail-image\"], [1, \"reward-detail-image\", \"placeholder\"], [1, \"requirement-message\"], [\"name\", \"information-circle-outline\"], [1, \"claimed-badge\"]],\n  template: function AffiliatesPage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ion-header\", 0)(1, \"ion-toolbar\")(2, \"ion-buttons\", 1)(3, \"div\", 2);\n      i0.ɵɵelement(4, \"img\", 3);\n      i0.ɵɵelementStart(5, \"span\");\n      i0.ɵɵtext(6, \"Upshift\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(7, \"ion-title\");\n      i0.ɵɵtext(8, \"Affiliate Rewards\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(9, \"ion-content\")(10, \"div\", 4)(11, \"div\", 5)(12, \"div\", 6)(13, \"h1\");\n      i0.ɵɵtext(14, \"Affiliate Rewards\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(15, \"p\", 7);\n      i0.ɵɵtext(16, \"Invite friends and earn exclusive rewards\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(17, \"div\", 8)(18, \"div\", 9);\n      i0.ɵɵtext(19);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(20, \"div\", 10);\n      i0.ɵɵtext(21, \"Your Affiliates\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(22, \"div\", 11)(23, \"p\");\n      i0.ɵɵtext(24, \"Invite friends to join Upshift and unlock valuable rewards. The more friends you invite, the better rewards you can claim!\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵtemplate(25, AffiliatesPage_div_25_Template, 4, 0, \"div\", 12)(26, AffiliatesPage_div_26_Template, 6, 0, \"div\", 13)(27, AffiliatesPage_div_27_Template, 8, 1, \"div\", 14);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(28, \"ion-modal\", 15);\n      i0.ɵɵlistener(\"ionModalDidDismiss\", function AffiliatesPage_Template_ion_modal_ionModalDidDismiss_28_listener() {\n        return ctx.closeOfferDetails();\n      });\n      i0.ɵɵtemplate(29, AffiliatesPage_ng_template_29_Template, 9, 1, \"ng-template\");\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(19);\n      i0.ɵɵtextInterpolate(ctx.userAffiliateCount);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.affiliateOffers.length === 0);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.affiliateOffers.length > 0);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"isOpen\", ctx.showOfferDetails)(\"backdropDismiss\", true)(\"cssClass\", \"affiliate-offer-modal\");\n    }\n  },\n  dependencies: [IonicModule, i2.IonButton, i2.IonButtons, i2.IonCard, i2.IonCardContent, i2.IonContent, i2.IonHeader, i2.IonIcon, i2.IonSpinner, i2.IonTitle, i2.IonToolbar, i2.IonModal, CommonModule, i3.NgForOf, i3.NgIf, FormsModule],\n  styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%];\\n\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\tvar __webpack_modules__ = ({\\n\\n\\n 321:\\n\\n\\n\\n\\n\\n (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/sass-loader/dist/cjs.js):\\\\nexpected \\\\\\\"{\\\\\\\".\\\\n  \\u2577\\\\n3 \\u2502   --surface-alt: #1e1f25;      Slight variation for subtle contrast */\\\\r\\\\n  \\u2502                         ^\\\\n  \\u2575\\\\n  src\\\\\\\\app\\\\\\\\pages\\\\\\\\affiliates\\\\\\\\affiliates.page.scss 3:25  root stylesheet\\\");\\n\\n\\n })\\n\\n\\n \\t});\\n\\n\\n\\n \\t\\n\\n \\t// startup\\n\\n \\t// Load entry module and return exports\\n\\n \\t// This entry module doesn't tell about it's top-level declarations so it can't be inlined\\n\\n \\tvar __webpack_exports__ = {};\\n\\n \\t__webpack_modules__[321]();\\n\\n \\tresource = __webpack_exports__;\\n\\n \\t\\n\\n })()\\n;\"]\n});", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "IonicModule", "Subscription", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵpropertyInterpolate", "offer_r2", "name", "ɵɵproperty", "image_cover", "ɵɵsanitizeUrl", "ɵɵlistener", "AffiliatesPage_div_27_ion_card_7_Template_ion_card_click_0_listener", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "viewOfferDetails", "ɵɵtemplate", "AffiliatesPage_div_27_ion_card_7_div_1_Template", "AffiliatesPage_div_27_ion_card_7_div_2_Template", "AffiliatesPage_div_27_ion_card_7_div_3_Template", "AffiliatesPage_div_27_ion_card_7_div_4_Template", "ɵɵclassProp", "isOfferClaimed", "id", "canClaimOffer", "ɵɵtextInterpolate", "description", "formatPrice", "price_value", "userAffiliateCount", "number_of_invites_required", "ɵɵtextInterpolate2", "ɵɵstyleProp", "AffiliatesPage_div_27_ion_card_7_Template", "affiliateOffers", "<PERSON><PERSON><PERSON>", "ɵɵtextInterpolate1", "AffiliatesPage_ng_template_29_div_8_div_2_Template", "AffiliatesPage_ng_template_29_div_8_div_3_Template", "AffiliatesPage_ng_template_29_div_8_div_24_Template", "AffiliatesPage_ng_template_29_div_8_div_25_Template", "AffiliatesPage_ng_template_29_div_8_Template_ion_button_click_27_listener", "_r5", "claimOffer", "AffiliatesPage_ng_template_29_Template_ion_button_click_5_listener", "_r4", "closeOfferDetails", "AffiliatesPage_ng_template_29_div_8_Template", "AffiliatesPage", "constructor", "supabaseService", "toastController", "userId", "showOfferDetails", "userSubscription", "isLoading", "userRewards", "ngOnInit", "userDataSubscription", "currentUser$", "subscribe", "user", "loadUserData", "loadAffiliateOffers", "loadUserRewards", "add", "ngOnDestroy", "unsubscribe", "_this", "_asyncToGenerator", "data", "error", "getClient", "from", "select", "eq", "single", "number_of_affiliates", "_this2", "order", "ascending", "map", "offer", "includes", "urlData", "storage", "getPublicUrl", "publicUrl", "err", "_this3", "offerId", "some", "reward", "affiliate_offer", "_this4", "insert", "user_id", "completed", "subscription_status", "affiliate_code_used", "toast", "create", "message", "duration", "position", "color", "buttons", "text", "role", "present", "value", "showCode", "undefined", "toFixed", "ɵɵdirectiveInject", "i1", "SupabaseService", "i2", "ToastController", "selectors", "decls", "vars", "consts", "template", "AffiliatesPage_Template", "rf", "ctx", "AffiliatesPage_div_25_Template", "AffiliatesPage_div_26_Template", "AffiliatesPage_div_27_Template", "AffiliatesPage_Template_ion_modal_ionModalDidDismiss_28_listener", "AffiliatesPage_ng_template_29_Template", "length", "IonButton", "IonButtons", "IonCard", "IonCardContent", "IonContent", "IonHeader", "IonIcon", "Ion<PERSON><PERSON><PERSON>", "IonTitle", "IonToolbar", "IonModal", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\affiliates\\affiliates.page.ts", "C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\affiliates\\affiliates.page.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IonicModule, ToastController } from '@ionic/angular';\r\nimport { SupabaseService } from '../../services/supabase.service';\r\nimport { Subscription } from 'rxjs';\r\n\r\n\r\ninterface AffiliateOffer {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  number_of_invites_required: number;\r\n  image_cover: string;\r\n  price_value: number;\r\n}\r\n\r\ninterface AffiliateReward {\r\n  id: string;\r\n  created_at: string;\r\n  user_id: string;\r\n  affiliate_offer: string;\r\n  completed: boolean;\r\n  subscription_status: string;\r\n  affiliate_code_used: string;\r\n  number_of_affiliates: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-affiliates',\r\n  templateUrl: './affiliates.page.html',\r\n  styleUrls: ['./affiliates.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule, FormsModule]\r\n})\r\nexport class AffiliatesPage implements OnInit, OnDestroy {\r\n  userId: string | null = null;\r\n  affiliateOffers: AffiliateOffer[] = [];\r\n  userAffiliateCount: number = 0;\r\n  selectedOffer: AffiliateOffer | null = null;\r\n  showOfferDetails: boolean = false;\r\n  userSubscription: Subscription = new Subscription();\r\n  isLoading: boolean = true;\r\n  userRewards: AffiliateReward[] = [];\r\n\r\n  constructor(\r\n    private supabaseService: SupabaseService,\r\n    private toastController: ToastController\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.isLoading = true;\r\n\r\n    const userDataSubscription = this.supabaseService.currentUser$.subscribe(user => {\r\n      if (user) {\r\n        this.userId = user.id;\r\n        this.loadUserData();\r\n        this.loadAffiliateOffers();\r\n        this.loadUserRewards();\r\n      } else {\r\n        this.userId = null;\r\n        this.isLoading = false;\r\n      }\r\n    });\r\n\r\n    this.userSubscription.add(userDataSubscription);\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.userSubscription.unsubscribe();\r\n  }\r\n\r\n  async loadUserData() {\r\n    if (!this.userId) return;\r\n\r\n    try {\r\n      const { data, error } = await this.supabaseService.getClient()\r\n        .from('profiles')\r\n        .select('*')\r\n        .eq('id', this.userId)\r\n        .single();\r\n\r\n      if (data && !error) {\r\n        this.userAffiliateCount = data.number_of_affiliates || 0;\r\n      }\r\n    } catch (error) {\r\n    }\r\n  }\r\n\r\n  async loadAffiliateOffers() {\r\n    if (!this.userId) return;\r\n\r\n    try {\r\n      const { data, error } = await this.supabaseService.getClient()\r\n        .from('affiliate_offers')\r\n        .select('*')\r\n        .order('number_of_invites_required', { ascending: true });\r\n\r\n      if (error) {\r\n        throw error;\r\n      }\r\n\r\n      this.affiliateOffers = data || [];\r\n\r\n      this.affiliateOffers = this.affiliateOffers.map(offer => {\r\n        if (offer.image_cover && !offer.image_cover.includes('://')) {\r\n          try {\r\n            const { data: urlData } = this.supabaseService.getClient()\r\n              .storage\r\n              .from('affiliate-offer')\r\n              .getPublicUrl(offer.image_cover);\r\n\r\n\r\n            return {\r\n              ...offer,\r\n              image_cover: urlData.publicUrl\r\n            };\r\n          } catch (err) {\r\n            const error = err as any;\r\n            return offer;\r\n          }\r\n        }\r\n        return offer;\r\n      });\r\n\r\n      this.isLoading = false;\r\n    } catch (error) {\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  async loadUserRewards() {\r\n    if (!this.userId) return;\r\n\r\n    try {\r\n      const { data, error } = await this.supabaseService.getClient()\r\n        .from('affiliate_rewards_history')\r\n        .select('*')\r\n        .eq('user_id', this.userId);\r\n\r\n      if (error) {\r\n        throw error;\r\n      }\r\n\r\n      this.userRewards = data || [];\r\n    } catch (error) {\r\n    }\r\n  }\r\n\r\n  viewOfferDetails(offer: AffiliateOffer) {\r\n    this.selectedOffer = offer;\r\n    this.showOfferDetails = true;\r\n  }\r\n\r\n  closeOfferDetails() {\r\n    this.showOfferDetails = false;\r\n    this.selectedOffer = null;\r\n  }\r\n\r\n  isOfferClaimed(offerId: string): boolean {\r\n    return this.userRewards.some(reward => reward.affiliate_offer === offerId);\r\n  }\r\n\r\n  canClaimOffer(offer: AffiliateOffer): boolean {\r\n    return this.userAffiliateCount >= offer.number_of_invites_required && !this.isOfferClaimed(offer.id);\r\n  }\r\n\r\n  async claimOffer(offer: AffiliateOffer) {\r\n    if (!this.userId || !this.canClaimOffer(offer)) return;\r\n\r\n    try {\r\n      const { error } = await this.supabaseService.getClient()\r\n        .from('affiliate_rewards_history')\r\n        .insert([\r\n          {\r\n            user_id: this.userId,\r\n            affiliate_offer: offer.id,\r\n            completed: true,\r\n            subscription_status: 'claimed',\r\n            affiliate_code_used: '',\r\n            number_of_affiliates: this.userAffiliateCount\r\n          }\r\n        ]);\r\n\r\n      if (error) {\r\n        throw error;\r\n      }\r\n\r\n      this.loadUserRewards();\r\n\r\n      const toast = await this.toastController.create({\r\n        message: `Successfully claimed ${offer.name}! You saved ${this.formatPrice(offer.price_value)}.`,\r\n        duration: 3000,\r\n        position: 'bottom',\r\n        color: 'success',\r\n        buttons: [\r\n          {\r\n            text: 'OK',\r\n            role: 'cancel'\r\n          }\r\n        ]\r\n      });\r\n      await toast.present();\r\n\r\n      this.closeOfferDetails();\r\n    } catch (error) {\r\n\r\n      const toast = await this.toastController.create({\r\n        message: 'Failed to claim offer. Please try again.',\r\n        duration: 3000,\r\n        position: 'bottom',\r\n        color: 'danger',\r\n        buttons: [\r\n          {\r\n            text: 'OK',\r\n            role: 'cancel'\r\n          }\r\n        ]\r\n      });\r\n      await toast.present();\r\n    }\r\n  }\r\n\r\n  formatPrice(value: number, showCode = false): string {\r\n    if (value === undefined || value === null) return '';\r\n\r\n    try {\r\n      return showCode ? `${value.toFixed(2)} USD` : `$${value.toFixed(2)}`;\r\n    } catch (error) {\r\n      return `$${value}`;\r\n    }\r\n  }\r\n}\r\n", "<ion-header class=\"ion-no-border\">\r\n  <ion-toolbar>\r\n    <ion-buttons slot=\"start\">\r\n      <div class=\"logo\">\r\n        <img src=\"assets/images/upshift_icon_mini.svg\" alt=\"Upshift\">\r\n        <span>Upshift</span>\r\n      </div>\r\n    </ion-buttons>\r\n    <ion-title>Affiliate Rewards</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content>\r\n  <div class=\"page-container\">\r\n    <div class=\"header-section\">\r\n      <div class=\"header-content\">\r\n        <h1>Affiliate Rewards</h1>\r\n        <p class=\"subtitle\">Invite friends and earn exclusive rewards</p>\r\n      </div>\r\n\r\n      <div class=\"affiliate-counter\">\r\n        <div class=\"counter-value\">{{ userAffiliateCount }}</div>\r\n        <div class=\"counter-label\">Your Affiliates</div>\r\n      </div>\r\n\r\n      <div class=\"header-description\">\r\n        <p>Invite friends to join Upshift and unlock valuable rewards. The more friends you invite, the better rewards you can claim!</p>\r\n      </div>\r\n    </div>\r\n\r\n    <div *ngIf=\"isLoading\" class=\"loading-container\">\r\n      <ion-spinner name=\"crescent\"></ion-spinner>\r\n      <p>Loading offers...</p>\r\n    </div>\r\n\r\n    <div *ngIf=\"!isLoading && affiliateOffers.length === 0\" class=\"empty-state\">\r\n      <ion-icon name=\"gift-outline\" size=\"large\"></ion-icon>\r\n      <p>No affiliate offers available at the moment.</p>\r\n      <p class=\"empty-subtitle\">Check back later for exciting rewards!</p>\r\n    </div>\r\n\r\n    <div class=\"rewards-section\" *ngIf=\"!isLoading && affiliateOffers.length > 0\">\r\n      <div class=\"section-header\">\r\n        <h2>Available Rewards</h2>\r\n        <p class=\"section-subtitle\">Claim these rewards when you reach the required number of affiliates</p>\r\n      </div>\r\n\r\n      <div class=\"rewards-grid\">\r\n        <ion-card class=\"reward-card\"\r\n             *ngFor=\"let offer of affiliateOffers\"\r\n             [class.claimed]=\"isOfferClaimed(offer.id)\"\r\n             [class.available]=\"canClaimOffer(offer)\"\r\n             (click)=\"viewOfferDetails(offer)\">\r\n          <div class=\"reward-status\" *ngIf=\"isOfferClaimed(offer.id)\">\r\n            <ion-icon name=\"checkmark-circle\"></ion-icon>\r\n            <span>Claimed</span>\r\n          </div>\r\n          <div class=\"reward-status available\" *ngIf=\"canClaimOffer(offer) && !isOfferClaimed(offer.id)\">\r\n            <ion-icon name=\"gift\"></ion-icon>\r\n            <span>Available</span>\r\n          </div>\r\n\r\n          <div class=\"reward-image\" *ngIf=\"offer.image_cover\">\r\n            <img [src]=\"offer.image_cover\" alt=\"{{ offer.name }}\">\r\n          </div>\r\n          <div class=\"reward-image placeholder\" *ngIf=\"!offer.image_cover\">\r\n            <ion-icon name=\"gift-outline\"></ion-icon>\r\n          </div>\r\n\r\n          <ion-card-content>\r\n            <h3 class=\"reward-title\">{{ offer.name }}</h3>\r\n            <p class=\"reward-description\">{{ offer.description }}</p>\r\n\r\n            <div class=\"reward-details\">\r\n              <div class=\"reward-value\">\r\n                <ion-icon name=\"cash-outline\"></ion-icon>\r\n                <span>{{ formatPrice(offer.price_value) }}</span>\r\n              </div>\r\n              <div class=\"reward-requirement\" [class.met]=\"userAffiliateCount >= offer.number_of_invites_required\">\r\n                <ion-icon name=\"people-outline\"></ion-icon>\r\n                <span>{{ userAffiliateCount }}/{{ offer.number_of_invites_required }}</span>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"reward-progress\">\r\n              <div class=\"progress-bar\">\r\n                <div class=\"progress-fill\"\r\n                     [style.width]=\"(userAffiliateCount / offer.number_of_invites_required) * 100 + '%'\"\r\n                     [class.complete]=\"userAffiliateCount >= offer.number_of_invites_required\"></div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"reward-action\">\r\n              <ion-button expand=\"block\" fill=\"clear\" size=\"small\">\r\n                View Details\r\n                <ion-icon name=\"chevron-forward-outline\" slot=\"end\"></ion-icon>\r\n              </ion-button>\r\n            </div>\r\n          </ion-card-content>\r\n        </ion-card>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Inline modal for offer details -->\r\n  <ion-modal [isOpen]=\"showOfferDetails\" [backdropDismiss]=\"true\" [cssClass]=\"'affiliate-offer-modal'\" (ionModalDidDismiss)=\"closeOfferDetails()\">\r\n    <ng-template>\r\n      <ion-header class=\"ion-no-border\">\r\n        <ion-toolbar>\r\n          <ion-title>Reward Details</ion-title>\r\n          <ion-buttons slot=\"end\">\r\n            <ion-button (click)=\"closeOfferDetails()\">\r\n              <ion-icon name=\"close-outline\" size=\"large\"></ion-icon>\r\n            </ion-button>\r\n          </ion-buttons>\r\n        </ion-toolbar>\r\n      </ion-header>\r\n\r\n      <ion-content class=\"ion-padding\">\r\n        <div class=\"modal-content\" *ngIf=\"selectedOffer\">\r\n          <div class=\"reward-detail-header\">\r\n            <div class=\"reward-detail-image\" *ngIf=\"selectedOffer.image_cover\">\r\n              <img [src]=\"selectedOffer.image_cover\" [alt]=\"selectedOffer.name\">\r\n            </div>\r\n            <div class=\"reward-detail-image placeholder\" *ngIf=\"!selectedOffer.image_cover\">\r\n              <ion-icon name=\"gift-outline\" size=\"large\"></ion-icon>\r\n            </div>\r\n\r\n            <div class=\"reward-detail-title\">\r\n              <h1>{{ selectedOffer.name }}</h1>\r\n              <div class=\"reward-detail-value\">\r\n                <ion-icon name=\"cash-outline\"></ion-icon>\r\n                <span>You save {{ formatPrice(selectedOffer.price_value, true) }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"reward-detail-section\">\r\n            <h3>Description</h3>\r\n            <p>{{ selectedOffer.description }}</p>\r\n          </div>\r\n\r\n          <div class=\"reward-detail-section\">\r\n            <h3>Requirements</h3>\r\n            <div class=\"progress-container\">\r\n              <div class=\"progress-bar\">\r\n                <div class=\"progress-fill\"\r\n                     [style.width]=\"((userAffiliateCount / selectedOffer.number_of_invites_required) * 100) + '%'\"\r\n                     [class.complete]=\"userAffiliateCount >= selectedOffer.number_of_invites_required\"></div>\r\n              </div>\r\n              <div class=\"progress-text\"\r\n                   [class.complete]=\"userAffiliateCount >= selectedOffer.number_of_invites_required\">\r\n                {{ userAffiliateCount }}/{{ selectedOffer.number_of_invites_required }} affiliates\r\n              </div>\r\n            </div>\r\n\r\n            <div *ngIf=\"!isOfferClaimed(selectedOffer.id) && userAffiliateCount < selectedOffer.number_of_invites_required\"\r\n                 class=\"requirement-message\">\r\n              <ion-icon name=\"information-circle-outline\"></ion-icon>\r\n              <span>You need {{ selectedOffer.number_of_invites_required - userAffiliateCount }} more affiliate(s) to claim this reward</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"reward-detail-section\" *ngIf=\"isOfferClaimed(selectedOffer.id)\">\r\n            <div class=\"claimed-badge\">\r\n              <ion-icon name=\"checkmark-circle\"></ion-icon>\r\n              <span>Reward Claimed</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"reward-detail-action\">\r\n            <ion-button expand=\"block\"\r\n                      [disabled]=\"!canClaimOffer(selectedOffer)\"\r\n                      (click)=\"claimOffer(selectedOffer)\"\r\n                      [color]=\"canClaimOffer(selectedOffer) ? 'primary' : 'medium'\">\r\n              <ion-icon [name]=\"isOfferClaimed(selectedOffer.id) ? 'checkmark-circle-outline' : 'gift-outline'\" slot=\"start\"></ion-icon>\r\n              {{ isOfferClaimed(selectedOffer.id) ? 'Already Claimed' : 'Claim Reward' }}\r\n            </ion-button>\r\n          </div>\r\n        </div>\r\n      </ion-content>\r\n    </ng-template>\r\n  </ion-modal>\r\n</ion-content>\r\n"], "mappings": ";;AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAyB,gBAAgB;AAE7D,SAASC,YAAY,QAAQ,MAAM;;;;;;;ICyB/BC,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAE,SAAA,sBAA2C;IAC3CF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,wBAAiB;IACtBH,EADsB,CAAAI,YAAA,EAAI,EACpB;;;;;IAENJ,EAAA,CAAAC,cAAA,cAA4E;IAC1ED,EAAA,CAAAE,SAAA,mBAAsD;IACtDF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,mDAA4C;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACnDJ,EAAA,CAAAC,cAAA,YAA0B;IAAAD,EAAA,CAAAG,MAAA,6CAAsC;IAClEH,EADkE,CAAAI,YAAA,EAAI,EAChE;;;;;IAcAJ,EAAA,CAAAC,cAAA,cAA4D;IAC1DD,EAAA,CAAAE,SAAA,mBAA6C;IAC7CF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,cAAO;IACfH,EADe,CAAAI,YAAA,EAAO,EAChB;;;;;IACNJ,EAAA,CAAAC,cAAA,cAA+F;IAC7FD,EAAA,CAAAE,SAAA,mBAAiC;IACjCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IACjBH,EADiB,CAAAI,YAAA,EAAO,EAClB;;;;;IAENJ,EAAA,CAAAC,cAAA,cAAoD;IAClDD,EAAA,CAAAE,SAAA,cAAsD;IACxDF,EAAA,CAAAI,YAAA,EAAM;;;;IAD2BJ,EAAA,CAAAK,SAAA,EAAsB;IAAtBL,EAAA,CAAAM,qBAAA,QAAAC,QAAA,CAAAC,IAAA,CAAsB;IAAhDR,EAAA,CAAAS,UAAA,QAAAF,QAAA,CAAAG,WAAA,EAAAV,EAAA,CAAAW,aAAA,CAAyB;;;;;IAEhCX,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAE,SAAA,mBAAyC;IAC3CF,EAAA,CAAAI,YAAA,EAAM;;;;;;IAnBRJ,EAAA,CAAAC,cAAA,mBAIuC;IAAlCD,EAAA,CAAAY,UAAA,mBAAAC,oEAAA;MAAA,MAAAN,QAAA,GAAAP,EAAA,CAAAc,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAG,gBAAA,CAAAb,QAAA,CAAuB;IAAA,EAAC;IAapCP,EAZA,CAAAqB,UAAA,IAAAC,+CAAA,kBAA4D,IAAAC,+CAAA,kBAImC,IAAAC,+CAAA,kBAK3C,IAAAC,+CAAA,kBAGa;IAK/DzB,EADF,CAAAC,cAAA,uBAAkB,aACS;IAAAD,EAAA,CAAAG,MAAA,GAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC9CJ,EAAA,CAAAC,cAAA,YAA8B;IAAAD,EAAA,CAAAG,MAAA,GAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAGvDJ,EADF,CAAAC,cAAA,eAA4B,eACA;IACxBD,EAAA,CAAAE,SAAA,oBAAyC;IACzCF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAAoC;IAC5CH,EAD4C,CAAAI,YAAA,EAAO,EAC7C;IACNJ,EAAA,CAAAC,cAAA,eAAqG;IACnGD,EAAA,CAAAE,SAAA,oBAA2C;IAC3CF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAA+D;IAEzEH,EAFyE,CAAAI,YAAA,EAAO,EACxE,EACF;IAGJJ,EADF,CAAAC,cAAA,eAA6B,eACD;IACxBD,EAAA,CAAAE,SAAA,eAEqF;IAEzFF,EADE,CAAAI,YAAA,EAAM,EACF;IAGJJ,EADF,CAAAC,cAAA,eAA2B,sBAC4B;IACnDD,EAAA,CAAAG,MAAA,sBACA;IAAAH,EAAA,CAAAE,SAAA,oBAA+D;IAIvEF,EAHM,CAAAI,YAAA,EAAa,EACT,EACW,EACV;;;;;IAhDNJ,EADA,CAAA0B,WAAA,YAAAT,MAAA,CAAAU,cAAA,CAAApB,QAAA,CAAAqB,EAAA,EAA0C,cAAAX,MAAA,CAAAY,aAAA,CAAAtB,QAAA,EACF;IAEfP,EAAA,CAAAK,SAAA,EAA8B;IAA9BL,EAAA,CAAAS,UAAA,SAAAQ,MAAA,CAAAU,cAAA,CAAApB,QAAA,CAAAqB,EAAA,EAA8B;IAIpB5B,EAAA,CAAAK,SAAA,EAAuD;IAAvDL,EAAA,CAAAS,UAAA,SAAAQ,MAAA,CAAAY,aAAA,CAAAtB,QAAA,MAAAU,MAAA,CAAAU,cAAA,CAAApB,QAAA,CAAAqB,EAAA,EAAuD;IAKlE5B,EAAA,CAAAK,SAAA,EAAuB;IAAvBL,EAAA,CAAAS,UAAA,SAAAF,QAAA,CAAAG,WAAA,CAAuB;IAGXV,EAAA,CAAAK,SAAA,EAAwB;IAAxBL,EAAA,CAAAS,UAAA,UAAAF,QAAA,CAAAG,WAAA,CAAwB;IAKpCV,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAA8B,iBAAA,CAAAvB,QAAA,CAAAC,IAAA,CAAgB;IACXR,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAA8B,iBAAA,CAAAvB,QAAA,CAAAwB,WAAA,CAAuB;IAK3C/B,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAA8B,iBAAA,CAAAb,MAAA,CAAAe,WAAA,CAAAzB,QAAA,CAAA0B,WAAA,EAAoC;IAEZjC,EAAA,CAAAK,SAAA,EAAoE;IAApEL,EAAA,CAAA0B,WAAA,QAAAT,MAAA,CAAAiB,kBAAA,IAAA3B,QAAA,CAAA4B,0BAAA,CAAoE;IAE5FnC,EAAA,CAAAK,SAAA,GAA+D;IAA/DL,EAAA,CAAAoC,kBAAA,KAAAnB,MAAA,CAAAiB,kBAAA,OAAA3B,QAAA,CAAA4B,0BAAA,KAA+D;IAOhEnC,EAAA,CAAAK,SAAA,GAAmF;IAAnFL,EAAA,CAAAqC,WAAA,UAAApB,MAAA,CAAAiB,kBAAA,GAAA3B,QAAA,CAAA4B,0BAAA,aAAmF;IACnFnC,EAAA,CAAA0B,WAAA,aAAAT,MAAA,CAAAiB,kBAAA,IAAA3B,QAAA,CAAA4B,0BAAA,CAAyE;;;;;IA7CtFnC,EAFJ,CAAAC,cAAA,cAA8E,cAChD,SACtB;IAAAD,EAAA,CAAAG,MAAA,wBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC1BJ,EAAA,CAAAC,cAAA,YAA4B;IAAAD,EAAA,CAAAG,MAAA,2EAAoE;IAClGH,EADkG,CAAAI,YAAA,EAAI,EAChG;IAENJ,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAqB,UAAA,IAAAiB,yCAAA,yBAIuC;IAiD3CtC,EADE,CAAAI,YAAA,EAAM,EACF;;;;IApDqBJ,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAS,UAAA,YAAAQ,MAAA,CAAAsB,eAAA,CAAkB;;;;;IAwErCvC,EAAA,CAAAC,cAAA,cAAmE;IACjED,EAAA,CAAAE,SAAA,cAAkE;IACpEF,EAAA,CAAAI,YAAA,EAAM;;;;IADCJ,EAAA,CAAAK,SAAA,EAAiC;IAACL,EAAlC,CAAAS,UAAA,QAAAQ,MAAA,CAAAuB,aAAA,CAAA9B,WAAA,EAAAV,EAAA,CAAAW,aAAA,CAAiC,QAAAM,MAAA,CAAAuB,aAAA,CAAAhC,IAAA,CAA2B;;;;;IAEnER,EAAA,CAAAC,cAAA,cAAgF;IAC9ED,EAAA,CAAAE,SAAA,mBAAsD;IACxDF,EAAA,CAAAI,YAAA,EAAM;;;;;IA8BNJ,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAE,SAAA,mBAAuD;IACvDF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAAmH;IAC3HH,EAD2H,CAAAI,YAAA,EAAO,EAC5H;;;;IADEJ,EAAA,CAAAK,SAAA,GAAmH;IAAnHL,EAAA,CAAAyC,kBAAA,cAAAxB,MAAA,CAAAuB,aAAA,CAAAL,0BAAA,GAAAlB,MAAA,CAAAiB,kBAAA,4CAAmH;;;;;IAK3HlC,EADF,CAAAC,cAAA,cAA4E,cAC/C;IACzBD,EAAA,CAAAE,SAAA,mBAA6C;IAC7CF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,qBAAc;IAExBH,EAFwB,CAAAI,YAAA,EAAO,EACvB,EACF;;;;;;IAhDNJ,EADF,CAAAC,cAAA,cAAiD,cACb;IAIhCD,EAHA,CAAAqB,UAAA,IAAAqB,kDAAA,kBAAmE,IAAAC,kDAAA,kBAGa;IAK9E3C,EADF,CAAAC,cAAA,cAAiC,SAC3B;IAAAD,EAAA,CAAAG,MAAA,GAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACjCJ,EAAA,CAAAC,cAAA,cAAiC;IAC/BD,EAAA,CAAAE,SAAA,mBAAyC;IACzCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,IAA2D;IAGvEH,EAHuE,CAAAI,YAAA,EAAO,EACpE,EACF,EACF;IAGJJ,EADF,CAAAC,cAAA,eAAmC,UAC7B;IAAAD,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACpBJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,IAA+B;IACpCH,EADoC,CAAAI,YAAA,EAAI,EAClC;IAGJJ,EADF,CAAAC,cAAA,eAAmC,UAC7B;IAAAD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAEnBJ,EADF,CAAAC,cAAA,eAAgC,eACJ;IACxBD,EAAA,CAAAE,SAAA,eAE6F;IAC/FF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eACuF;IACrFD,EAAA,CAAAG,MAAA,IACF;IACFH,EADE,CAAAI,YAAA,EAAM,EACF;IAENJ,EAAA,CAAAqB,UAAA,KAAAuB,mDAAA,kBACiC;IAInC5C,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAqB,UAAA,KAAAwB,mDAAA,kBAA4E;IAQ1E7C,EADF,CAAAC,cAAA,eAAkC,sBAIwC;IAD9DD,EAAA,CAAAY,UAAA,mBAAAkC,0EAAA;MAAA9C,EAAA,CAAAc,aAAA,CAAAiC,GAAA;MAAA,MAAA9B,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAA+B,UAAA,CAAA/B,MAAA,CAAAuB,aAAA,CAAyB;IAAA,EAAC;IAE3CxC,EAAA,CAAAE,SAAA,oBAA0H;IAC1HF,EAAA,CAAAG,MAAA,IACF;IAEJH,EAFI,CAAAI,YAAA,EAAa,EACT,EACF;;;;IA1DgCJ,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAS,UAAA,SAAAQ,MAAA,CAAAuB,aAAA,CAAA9B,WAAA,CAA+B;IAGnBV,EAAA,CAAAK,SAAA,EAAgC;IAAhCL,EAAA,CAAAS,UAAA,UAAAQ,MAAA,CAAAuB,aAAA,CAAA9B,WAAA,CAAgC;IAKxEV,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAA8B,iBAAA,CAAAb,MAAA,CAAAuB,aAAA,CAAAhC,IAAA,CAAwB;IAGpBR,EAAA,CAAAK,SAAA,GAA2D;IAA3DL,EAAA,CAAAyC,kBAAA,cAAAxB,MAAA,CAAAe,WAAA,CAAAf,MAAA,CAAAuB,aAAA,CAAAP,WAAA,YAA2D;IAOlEjC,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAA8B,iBAAA,CAAAb,MAAA,CAAAuB,aAAA,CAAAT,WAAA,CAA+B;IAQzB/B,EAAA,CAAAK,SAAA,GAA6F;IAA7FL,EAAA,CAAAqC,WAAA,UAAApB,MAAA,CAAAiB,kBAAA,GAAAjB,MAAA,CAAAuB,aAAA,CAAAL,0BAAA,aAA6F;IAC7FnC,EAAA,CAAA0B,WAAA,aAAAT,MAAA,CAAAiB,kBAAA,IAAAjB,MAAA,CAAAuB,aAAA,CAAAL,0BAAA,CAAiF;IAGnFnC,EAAA,CAAAK,SAAA,EAAiF;IAAjFL,EAAA,CAAA0B,WAAA,aAAAT,MAAA,CAAAiB,kBAAA,IAAAjB,MAAA,CAAAuB,aAAA,CAAAL,0BAAA,CAAiF;IACpFnC,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAoC,kBAAA,MAAAnB,MAAA,CAAAiB,kBAAA,OAAAjB,MAAA,CAAAuB,aAAA,CAAAL,0BAAA,iBACF;IAGInC,EAAA,CAAAK,SAAA,EAAwG;IAAxGL,EAAA,CAAAS,UAAA,UAAAQ,MAAA,CAAAU,cAAA,CAAAV,MAAA,CAAAuB,aAAA,CAAAZ,EAAA,KAAAX,MAAA,CAAAiB,kBAAA,GAAAjB,MAAA,CAAAuB,aAAA,CAAAL,0BAAA,CAAwG;IAO5EnC,EAAA,CAAAK,SAAA,EAAsC;IAAtCL,EAAA,CAAAS,UAAA,SAAAQ,MAAA,CAAAU,cAAA,CAAAV,MAAA,CAAAuB,aAAA,CAAAZ,EAAA,EAAsC;IAS9D5B,EAAA,CAAAK,SAAA,GAA0C;IAE1CL,EAFA,CAAAS,UAAA,cAAAQ,MAAA,CAAAY,aAAA,CAAAZ,MAAA,CAAAuB,aAAA,EAA0C,UAAAvB,MAAA,CAAAY,aAAA,CAAAZ,MAAA,CAAAuB,aAAA,yBAEmB;IAC3DxC,EAAA,CAAAK,SAAA,EAAuF;IAAvFL,EAAA,CAAAS,UAAA,SAAAQ,MAAA,CAAAU,cAAA,CAAAV,MAAA,CAAAuB,aAAA,CAAAZ,EAAA,gDAAuF;IACjG5B,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAyC,kBAAA,MAAAxB,MAAA,CAAAU,cAAA,CAAAV,MAAA,CAAAuB,aAAA,CAAAZ,EAAA,4CACF;;;;;;IApEF5B,EAFJ,CAAAC,cAAA,oBAAkC,kBACnB,gBACA;IAAAD,EAAA,CAAAG,MAAA,qBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAY;IAEnCJ,EADF,CAAAC,cAAA,sBAAwB,qBACoB;IAA9BD,EAAA,CAAAY,UAAA,mBAAAqC,mEAAA;MAAAjD,EAAA,CAAAc,aAAA,CAAAoC,GAAA;MAAA,MAAAjC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAkC,iBAAA,EAAmB;IAAA,EAAC;IACvCnD,EAAA,CAAAE,SAAA,mBAAuD;IAI/DF,EAHM,CAAAI,YAAA,EAAa,EACD,EACF,EACH;IAEbJ,EAAA,CAAAC,cAAA,sBAAiC;IAC/BD,EAAA,CAAAqB,UAAA,IAAA+B,4CAAA,oBAAiD;IA6DnDpD,EAAA,CAAAI,YAAA,EAAc;;;;IA7DgBJ,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAS,UAAA,SAAAQ,MAAA,CAAAuB,aAAA,CAAmB;;;ADpFvD,OAAM,MAAOa,cAAc;EAUzBC,YACUC,eAAgC,EAChCC,eAAgC;IADhC,KAAAD,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IAXzB,KAAAC,MAAM,GAAkB,IAAI;IAC5B,KAAAlB,eAAe,GAAqB,EAAE;IACtC,KAAAL,kBAAkB,GAAW,CAAC;IAC9B,KAAAM,aAAa,GAA0B,IAAI;IAC3C,KAAAkB,gBAAgB,GAAY,KAAK;IACjC,KAAAC,gBAAgB,GAAiB,IAAI5D,YAAY,EAAE;IACnD,KAAA6D,SAAS,GAAY,IAAI;IACzB,KAAAC,WAAW,GAAsB,EAAE;EAK/B;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACF,SAAS,GAAG,IAAI;IAErB,MAAMG,oBAAoB,GAAG,IAAI,CAACR,eAAe,CAACS,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC9E,IAAIA,IAAI,EAAE;QACR,IAAI,CAACT,MAAM,GAAGS,IAAI,CAACtC,EAAE;QACrB,IAAI,CAACuC,YAAY,EAAE;QACnB,IAAI,CAACC,mBAAmB,EAAE;QAC1B,IAAI,CAACC,eAAe,EAAE;MACxB,CAAC,MAAM;QACL,IAAI,CAACZ,MAAM,GAAG,IAAI;QAClB,IAAI,CAACG,SAAS,GAAG,KAAK;MACxB;IACF,CAAC,CAAC;IAEF,IAAI,CAACD,gBAAgB,CAACW,GAAG,CAACP,oBAAoB,CAAC;EACjD;EAEAQ,WAAWA,CAAA;IACT,IAAI,CAACZ,gBAAgB,CAACa,WAAW,EAAE;EACrC;EAEML,YAAYA,CAAA;IAAA,IAAAM,KAAA;IAAA,OAAAC,iBAAA;MAChB,IAAI,CAACD,KAAI,CAAChB,MAAM,EAAE;MAElB,IAAI;QACF,MAAM;UAAEkB,IAAI;UAAEC;QAAK,CAAE,SAASH,KAAI,CAAClB,eAAe,CAACsB,SAAS,EAAE,CAC3DC,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,IAAI,EAAEP,KAAI,CAAChB,MAAM,CAAC,CACrBwB,MAAM,EAAE;QAEX,IAAIN,IAAI,IAAI,CAACC,KAAK,EAAE;UAClBH,KAAI,CAACvC,kBAAkB,GAAGyC,IAAI,CAACO,oBAAoB,IAAI,CAAC;QAC1D;MACF,CAAC,CAAC,OAAON,KAAK,EAAE,CAChB;IAAC;EACH;EAEMR,mBAAmBA,CAAA;IAAA,IAAAe,MAAA;IAAA,OAAAT,iBAAA;MACvB,IAAI,CAACS,MAAI,CAAC1B,MAAM,EAAE;MAElB,IAAI;QACF,MAAM;UAAEkB,IAAI;UAAEC;QAAK,CAAE,SAASO,MAAI,CAAC5B,eAAe,CAACsB,SAAS,EAAE,CAC3DC,IAAI,CAAC,kBAAkB,CAAC,CACxBC,MAAM,CAAC,GAAG,CAAC,CACXK,KAAK,CAAC,4BAA4B,EAAE;UAAEC,SAAS,EAAE;QAAI,CAAE,CAAC;QAE3D,IAAIT,KAAK,EAAE;UACT,MAAMA,KAAK;QACb;QAEAO,MAAI,CAAC5C,eAAe,GAAGoC,IAAI,IAAI,EAAE;QAEjCQ,MAAI,CAAC5C,eAAe,GAAG4C,MAAI,CAAC5C,eAAe,CAAC+C,GAAG,CAACC,KAAK,IAAG;UACtD,IAAIA,KAAK,CAAC7E,WAAW,IAAI,CAAC6E,KAAK,CAAC7E,WAAW,CAAC8E,QAAQ,CAAC,KAAK,CAAC,EAAE;YAC3D,IAAI;cACF,MAAM;gBAAEb,IAAI,EAAEc;cAAO,CAAE,GAAGN,MAAI,CAAC5B,eAAe,CAACsB,SAAS,EAAE,CACvDa,OAAO,CACPZ,IAAI,CAAC,iBAAiB,CAAC,CACvBa,YAAY,CAACJ,KAAK,CAAC7E,WAAW,CAAC;cAGlC,OAAO;gBACL,GAAG6E,KAAK;gBACR7E,WAAW,EAAE+E,OAAO,CAACG;eACtB;YACH,CAAC,CAAC,OAAOC,GAAG,EAAE;cACZ,MAAMjB,KAAK,GAAGiB,GAAU;cACxB,OAAON,KAAK;YACd;UACF;UACA,OAAOA,KAAK;QACd,CAAC,CAAC;QAEFJ,MAAI,CAACvB,SAAS,GAAG,KAAK;MACxB,CAAC,CAAC,OAAOgB,KAAK,EAAE;QACdO,MAAI,CAACvB,SAAS,GAAG,KAAK;MACxB;IAAC;EACH;EAEMS,eAAeA,CAAA;IAAA,IAAAyB,MAAA;IAAA,OAAApB,iBAAA;MACnB,IAAI,CAACoB,MAAI,CAACrC,MAAM,EAAE;MAElB,IAAI;QACF,MAAM;UAAEkB,IAAI;UAAEC;QAAK,CAAE,SAASkB,MAAI,CAACvC,eAAe,CAACsB,SAAS,EAAE,CAC3DC,IAAI,CAAC,2BAA2B,CAAC,CACjCC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAEc,MAAI,CAACrC,MAAM,CAAC;QAE7B,IAAImB,KAAK,EAAE;UACT,MAAMA,KAAK;QACb;QAEAkB,MAAI,CAACjC,WAAW,GAAGc,IAAI,IAAI,EAAE;MAC/B,CAAC,CAAC,OAAOC,KAAK,EAAE,CAChB;IAAC;EACH;EAEAxD,gBAAgBA,CAACmE,KAAqB;IACpC,IAAI,CAAC/C,aAAa,GAAG+C,KAAK;IAC1B,IAAI,CAAC7B,gBAAgB,GAAG,IAAI;EAC9B;EAEAP,iBAAiBA,CAAA;IACf,IAAI,CAACO,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAClB,aAAa,GAAG,IAAI;EAC3B;EAEAb,cAAcA,CAACoE,OAAe;IAC5B,OAAO,IAAI,CAAClC,WAAW,CAACmC,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACC,eAAe,KAAKH,OAAO,CAAC;EAC5E;EAEAlE,aAAaA,CAAC0D,KAAqB;IACjC,OAAO,IAAI,CAACrD,kBAAkB,IAAIqD,KAAK,CAACpD,0BAA0B,IAAI,CAAC,IAAI,CAACR,cAAc,CAAC4D,KAAK,CAAC3D,EAAE,CAAC;EACtG;EAEMoB,UAAUA,CAACuC,KAAqB;IAAA,IAAAY,MAAA;IAAA,OAAAzB,iBAAA;MACpC,IAAI,CAACyB,MAAI,CAAC1C,MAAM,IAAI,CAAC0C,MAAI,CAACtE,aAAa,CAAC0D,KAAK,CAAC,EAAE;MAEhD,IAAI;QACF,MAAM;UAAEX;QAAK,CAAE,SAASuB,MAAI,CAAC5C,eAAe,CAACsB,SAAS,EAAE,CACrDC,IAAI,CAAC,2BAA2B,CAAC,CACjCsB,MAAM,CAAC,CACN;UACEC,OAAO,EAAEF,MAAI,CAAC1C,MAAM;UACpByC,eAAe,EAAEX,KAAK,CAAC3D,EAAE;UACzB0E,SAAS,EAAE,IAAI;UACfC,mBAAmB,EAAE,SAAS;UAC9BC,mBAAmB,EAAE,EAAE;UACvBtB,oBAAoB,EAAEiB,MAAI,CAACjE;SAC5B,CACF,CAAC;QAEJ,IAAI0C,KAAK,EAAE;UACT,MAAMA,KAAK;QACb;QAEAuB,MAAI,CAAC9B,eAAe,EAAE;QAEtB,MAAMoC,KAAK,SAASN,MAAI,CAAC3C,eAAe,CAACkD,MAAM,CAAC;UAC9CC,OAAO,EAAE,wBAAwBpB,KAAK,CAAC/E,IAAI,eAAe2F,MAAI,CAACnE,WAAW,CAACuD,KAAK,CAACtD,WAAW,CAAC,GAAG;UAChG2E,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE,SAAS;UAChBC,OAAO,EAAE,CACP;YACEC,IAAI,EAAE,IAAI;YACVC,IAAI,EAAE;WACP;SAEJ,CAAC;QACF,MAAMR,KAAK,CAACS,OAAO,EAAE;QAErBf,MAAI,CAAChD,iBAAiB,EAAE;MAC1B,CAAC,CAAC,OAAOyB,KAAK,EAAE;QAEd,MAAM6B,KAAK,SAASN,MAAI,CAAC3C,eAAe,CAACkD,MAAM,CAAC;UAC9CC,OAAO,EAAE,0CAA0C;UACnDC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE,QAAQ;UACfC,OAAO,EAAE,CACP;YACEC,IAAI,EAAE,IAAI;YACVC,IAAI,EAAE;WACP;SAEJ,CAAC;QACF,MAAMR,KAAK,CAACS,OAAO,EAAE;MACvB;IAAC;EACH;EAEAlF,WAAWA,CAACmF,KAAa,EAAEC,QAAQ,GAAG,KAAK;IACzC,IAAID,KAAK,KAAKE,SAAS,IAAIF,KAAK,KAAK,IAAI,EAAE,OAAO,EAAE;IAEpD,IAAI;MACF,OAAOC,QAAQ,GAAG,GAAGD,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC,MAAM,GAAG,IAAIH,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;IACtE,CAAC,CAAC,OAAO1C,KAAK,EAAE;MACd,OAAO,IAAIuC,KAAK,EAAE;IACpB;EACF;;kBApMW9D,cAAc;;mCAAdA,eAAc,EAAArD,EAAA,CAAAuH,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAzH,EAAA,CAAAuH,iBAAA,CAAAG,EAAA,CAAAC,eAAA;AAAA;;QAAdtE,eAAc;EAAAuE,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MChCrBlI,EAHN,CAAAC,cAAA,oBAAkC,kBACnB,qBACe,aACN;MAChBD,EAAA,CAAAE,SAAA,aAA6D;MAC7DF,EAAA,CAAAC,cAAA,WAAM;MAAAD,EAAA,CAAAG,MAAA,cAAO;MAEjBH,EAFiB,CAAAI,YAAA,EAAO,EAChB,EACM;MACdJ,EAAA,CAAAC,cAAA,gBAAW;MAAAD,EAAA,CAAAG,MAAA,wBAAiB;MAEhCH,EAFgC,CAAAI,YAAA,EAAY,EAC5B,EACH;MAMLJ,EAJR,CAAAC,cAAA,kBAAa,cACiB,cACE,cACE,UACtB;MAAAD,EAAA,CAAAG,MAAA,yBAAiB;MAAAH,EAAA,CAAAI,YAAA,EAAK;MAC1BJ,EAAA,CAAAC,cAAA,YAAoB;MAAAD,EAAA,CAAAG,MAAA,iDAAyC;MAC/DH,EAD+D,CAAAI,YAAA,EAAI,EAC7D;MAGJJ,EADF,CAAAC,cAAA,cAA+B,cACF;MAAAD,EAAA,CAAAG,MAAA,IAAwB;MAAAH,EAAA,CAAAI,YAAA,EAAM;MACzDJ,EAAA,CAAAC,cAAA,eAA2B;MAAAD,EAAA,CAAAG,MAAA,uBAAe;MAC5CH,EAD4C,CAAAI,YAAA,EAAM,EAC5C;MAGJJ,EADF,CAAAC,cAAA,eAAgC,SAC3B;MAAAD,EAAA,CAAAG,MAAA,kIAA0H;MAEjIH,EAFiI,CAAAI,YAAA,EAAI,EAC7H,EACF;MAaNJ,EAXA,CAAAqB,UAAA,KAAA+G,8BAAA,kBAAiD,KAAAC,8BAAA,kBAK2B,KAAAC,8BAAA,kBAME;MA6DhFtI,EAAA,CAAAI,YAAA,EAAM;MAGNJ,EAAA,CAAAC,cAAA,qBAAgJ;MAA3CD,EAAA,CAAAY,UAAA,gCAAA2H,iEAAA;QAAA,OAAsBJ,GAAA,CAAAhF,iBAAA,EAAmB;MAAA,EAAC;MAC7InD,EAAA,CAAAqB,UAAA,KAAAmH,sCAAA,sBAAa;MA6EjBxI,EADE,CAAAI,YAAA,EAAY,EACA;;;MAlKqBJ,EAAA,CAAAK,SAAA,IAAwB;MAAxBL,EAAA,CAAA8B,iBAAA,CAAAqG,GAAA,CAAAjG,kBAAA,CAAwB;MASjDlC,EAAA,CAAAK,SAAA,GAAe;MAAfL,EAAA,CAAAS,UAAA,SAAA0H,GAAA,CAAAvE,SAAA,CAAe;MAKf5D,EAAA,CAAAK,SAAA,EAAgD;MAAhDL,EAAA,CAAAS,UAAA,UAAA0H,GAAA,CAAAvE,SAAA,IAAAuE,GAAA,CAAA5F,eAAA,CAAAkG,MAAA,OAAgD;MAMxBzI,EAAA,CAAAK,SAAA,EAA8C;MAA9CL,EAAA,CAAAS,UAAA,UAAA0H,GAAA,CAAAvE,SAAA,IAAAuE,GAAA,CAAA5F,eAAA,CAAAkG,MAAA,KAA8C;MAgEnEzI,EAAA,CAAAK,SAAA,EAA2B;MAA0BL,EAArD,CAAAS,UAAA,WAAA0H,GAAA,CAAAzE,gBAAA,CAA2B,yBAAyB,qCAAqC;;;iBDxE1F5D,WAAW,EAAA4H,EAAA,CAAAgB,SAAA,EAAAhB,EAAA,CAAAiB,UAAA,EAAAjB,EAAA,CAAAkB,OAAA,EAAAlB,EAAA,CAAAmB,cAAA,EAAAnB,EAAA,CAAAoB,UAAA,EAAApB,EAAA,CAAAqB,SAAA,EAAArB,EAAA,CAAAsB,OAAA,EAAAtB,EAAA,CAAAuB,UAAA,EAAAvB,EAAA,CAAAwB,QAAA,EAAAxB,EAAA,CAAAyB,UAAA,EAAAzB,EAAA,CAAA0B,QAAA,EAAExJ,YAAY,EAAAyJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE1J,WAAW;EAAA2J,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}