﻿import { Injectable, EnvironmentInjector, inject, runInInjectionContext } from '@angular/core';
import { Quest, QuestProgress } from '../models/quest.model';
import { Observable, from, map, of, catchError, switchMap } from 'rxjs';
import { UserService } from './user.service';
import { SupabaseService } from './supabase.service';

@Injectable({
  providedIn: 'root'
})
export class QuestService {
  private supabaseService = inject(SupabaseService);
  private userService = inject(UserService);
  private injector = inject(EnvironmentInjector);

  getQuests(userId: string): Observable<Quest[]> {
    return runInInjectionContext(this.injector, () => {

      return from(
        this.supabaseService.getClient()
          .from('quests')
          .select('*')
          .filter('user_id', 'eq', userId)
          .filter('active', 'eq', true)
      ).pipe(
        map(response => {
          if (response.error) {
            return [];
          }

          return response.data as Quest[];
        }),
        catchError(error => {
          return of([]);
        })
      );
    });
  }

  getQuest(questId: string): Observable<Quest | null> {
    return runInInjectionContext(this.injector, () => {

      return from(
        this.supabaseService.getClient()
          .from('quests')
          .select('*')
          .filter('id', 'eq', questId)
      ).pipe(
        map(response => {
          if (response.error) {
            return null;
          }

          if (!response.data || response.data.length === 0) {
            return null;
          }

          return response.data[0] as Quest;
        }),
        catchError(error => {
          return of(null);
        })
      );
    });
  }

  async createQuest(quest: Omit<Quest, 'id' | 'created_at' | 'streak'>): Promise<string> {
    return runInInjectionContext(this.injector, async () => {

      try {
        const { data: userProfile, error: userError } = await this.supabaseService.getClient()
          .from('profiles')
          .select('id')
          .eq('id', quest.user_id)
          .maybeSingle();

        if (userError || !userProfile) {
          throw new Error('User profile not found. Please ensure you are logged in.');
        }


        const { id, ...questWithoutId } = quest as any;

        const initialStreak = questWithoutId.quest_type === 'quit' ? 1 : 0;

        const newQuest = {
          ...questWithoutId,
          created_at: new Date().toISOString(),
          streak: initialStreak
        };


        const { data, error } = await this.supabaseService.getClient()
          .from('quests')
          .insert(newQuest)
          .select()
          .maybeSingle();

        if (error) {

          if (error.code === '23503') {

            if (error.message.includes('quests_user_id_fkey') ||
                error.message.includes('quests_id_fkey') ||
                error.message.includes('Key is not present in table "users"')) {
              throw new Error('There is a database configuration issue with foreign key constraints. Please run the fix_quest_constraints.sql script in Supabase SQL Editor to fix it.');
            } else {
              throw new Error(`Foreign key constraint error: ${error.message}. Please check your database configuration.`);
            }
          } else {
            throw new Error(error.message);
          }
        }

        return data.id;
      } catch (error) {
        throw error;
      }
    });
  }

  async updateQuest(questId: string, data: Partial<Quest>): Promise<void> {
    return runInInjectionContext(this.injector, async () => {

      const { error } = await this.supabaseService.getClient()
        .from('quests')
        .update(data)
        .eq('id', questId);

      if (error) {
        throw new Error(error.message);
      }

    });
  }

  updateQuestStreak(questId: string, streak: number): Observable<void> {
    return runInInjectionContext(this.injector, () => {
      return new Observable<void>(observer => {
        const updateStreak = async () => {
          try {

            const { error } = await this.supabaseService.getClient()
              .from('quests')
              .update({ streak })
              .eq('id', questId);

            if (error) {
              observer.error(error);
              return;
            }


            if (streak >= 7) {
              this.updateStreakBadges(questId, streak);
            }

            observer.next();
            observer.complete();
          } catch (error) {
            observer.error(error);
          }
        };

        updateStreak();
      });
    });
  }

  async deleteQuest(questId: string): Promise<void> {
    return runInInjectionContext(this.injector, async () => {

      const { error } = await this.supabaseService.getClient()
        .from('quests')
        .delete()
        .eq('id', questId);

      if (error) {
        throw new Error(error.message);
      }

    });
  }

  getQuestProgress(userId: string, questId: string, date: Date): Observable<QuestProgress | null> {
    return runInInjectionContext(this.injector, () => {

      const dateString = date.toISOString().split('T')[0];

      return from(
        this.supabaseService.getClient()
          .from('quests')
          .select('*')
          .eq('id', questId)
          .maybeSingle()
      ).pipe(
        switchMap(questResponse => {
          if (questResponse.error) {
            return of(null);
          }

          const quest = questResponse.data as Quest;

          return from(
            this.supabaseService.getClient()
              .from('quest_progress')
              .select('*')
              .eq('quest_id', questId)
              .eq('user_id', userId)
              .eq('date', dateString)
              .limit(1)
          ).pipe(
            switchMap(response => {
              if (response.error) {
                if (response.error.code === '406' || response.error.code === 'PGRST116') {

                  return of({
                    id: '',
                    user_id: userId,
                    quest_id: questId,
                    date: dateString,
                    completed: false,
                    value_achieved: 0
                  } as QuestProgress);
                }

                return of(null);
              }

              if (!response.data || response.data.length === 0) {
                return of(null);
              }

              const progress = response.data[0] as QuestProgress;

              const today = new Date();
              today.setHours(0, 0, 0, 0);
              const selectedDate = new Date(date);
              selectedDate.setHours(0, 0, 0, 0);

              return of(progress);
            })
          );
        }),
        catchError(error => {
          return of(null);
        })
      );
    });
  }





  getQuestProgressForQuest(userId: string, questId: string): Observable<QuestProgress[]> {
    return runInInjectionContext(this.injector, () => {

      return from(
        this.supabaseService.getClient()
          .from('quest_progress')
          .select('*')
          .eq('quest_id', questId)
          .eq('user_id', userId)
      ).pipe(
        map(response => {
          if (response.error) {
            return [];
          }

          return response.data as QuestProgress[];
        }),
        catchError(error => {
          return of([]);
        })
      );
    });
  }

  getQuestProgressForDate(userId: string, date: Date): Observable<QuestProgress[]> {
    return runInInjectionContext(this.injector, () => {

      const dateString = date.toISOString().split('T')[0];

      return from(
        this.supabaseService.getClient()
          .from('quests')
          .select('*')
          .eq('user_id', userId)
          .eq('active', true)
      ).pipe(
        switchMap(questsResponse => {
          if (questsResponse.error) {
            return of([]);
          }

          const quests = questsResponse.data as Quest[];

          if (quests.length === 0) {
            return of([]);
          }

          return from(
            this.supabaseService.getClient()
              .from('quest_progress')
              .select('*')
              .eq('user_id', userId)
              .eq('date', dateString)
          ).pipe(
            map(response => {
              if (response.error) {
                if (response.error.code === '406' || response.error.code === 'PGRST116') {

                  return [];
                }

                return [];
              }

              return response.data as QuestProgress[];
            }),
            catchError(error => {
              return of([]);
            })
          );
        })
      );
    });
  }

  async toggleQuestCompletion(
    userId: string,
    questId: string,
    date: Date,
    valueAchieved: number,
    _quest: Quest 
  ): Promise<{ completed: boolean; value_achieved: number; streak: number }> {
    return runInInjectionContext(this.injector, async () => {
      try {
        const dateString = date.toISOString().split('T')[0];

        const supabase = this.supabaseService.getClient();

        const { data: questWithStreak, error: questStreakError } = await supabase
          .from('quests')
          .select('streak')
          .eq('id', questId)
          .single();

        if (questStreakError) {
        }

        const currentStreak = questWithStreak?.streak || 0;

        const { data: questData, error: questError } = await supabase
          .from('quests')
          .select('*')
          .eq('id', questId)
          .maybeSingle();
        if (questError || !questData) {
          throw new Error('Quest not found');
        }

        if (questData.user_id !== userId) {
          throw new Error('Quest does not belong to user');
        }

        const verifiedQuestId = questData.id;
        const verifiedUserId = questData.user_id;

        const { data: existingProgressData, error: progressError } = await supabase
          .from('quest_progress')
          .select('*')
          .eq('quest_id', verifiedQuestId)
          .eq('user_id', verifiedUserId)
          .eq('date', dateString);

        if (progressError) {
          if (progressError.code === '406' || progressError.code === 'PGRST116') {
          } else {
            throw new Error('Error checking quest progress');
          }
        }

        const wasCompleted = existingProgressData && existingProgressData.length > 0 ?
                            existingProgressData[0].completed : false;
        const previousValueAchieved = existingProgressData && existingProgressData.length > 0 ?
                            existingProgressData[0].value_achieved : 0;

        let isCompleted: boolean;
        if (questData.quest_type === 'build') {
          isCompleted = valueAchieved >= questData.goal_value;
        } else { 
          isCompleted = valueAchieved < questData.goal_value;
        }


        if (existingProgressData && existingProgressData.length > 0) {
          const firstEntry = existingProgressData[0];

          try {
            const { error: updateError } = await supabase
              .from('quest_progress')
              .update({
                value_achieved: valueAchieved,
                completed: isCompleted
              })
              .eq('id', firstEntry.id);

            if (updateError) {
            }
          } catch (error) {
          }

          if (existingProgressData.length > 1) {
            const idsToDelete = existingProgressData
              .slice(1)
              .map(p => p.id);

            if (idsToDelete.length > 0) {
              const { error: deleteError } = await supabase
                .from('quest_progress')
                .delete()
                .in('id', idsToDelete);

              if (deleteError) {
              }
            }
          }
        } else {
          const newProgress = {
            quest_id: verifiedQuestId,
            user_id: verifiedUserId,
            date: dateString,
            completed: isCompleted,
            value_achieved: valueAchieved
          };

          try {
            const { error: upsertError } = await supabase
              .from('quest_progress')
              .upsert(newProgress, {
                onConflict: 'quest_id,user_id,date',
                ignoreDuplicates: false
              });

            if (upsertError) {
            }
          } catch (error) {
          }
        }

        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const selectedDate = new Date(date);
        selectedDate.setHours(0, 0, 0, 0);


        if (isCompleted !== wasCompleted) {
          await this.updateUserXP(
            verifiedUserId,
            questData.category,
            isCompleted,
            wasCompleted,
            questData.priority
          );

          if (isCompleted) {
            await this.updateCategoryBadges(verifiedUserId, questData.category);
          }
        } else {
        }







        return {
          completed: isCompleted,
          value_achieved: valueAchieved,
          streak: currentStreak
        };
      } catch (error) {
        throw error;
      }
    });
  }



  private shouldQuestBeActive(quest: Quest, date: Date): boolean {
    if (quest.goal_period === 'day') {
      return true;
    }

    if (quest.goal_period === 'week') {
      if (!quest.task_days_of_week) {
        return true;
      }

      const dayName = date.toLocaleDateString('en-US', { weekday: 'short' }).substring(0, 3);

      let taskDays: string[] = [];
      if (typeof quest.task_days_of_week === 'string') {
        taskDays = quest.task_days_of_week.split(',').map(day => day.trim());
      } else if (Array.isArray(quest.task_days_of_week)) {
        taskDays = quest.task_days_of_week;
      }


      return taskDays.some(day =>
        day.toLowerCase() === dayName.toLowerCase() ||
        day.toLowerCase().substring(0, 3) === dayName.toLowerCase()
      );
    }

    if (quest.goal_period === 'month') {
      if (!quest.task_days_of_month) {
        return true;
      }

      const dayOfMonth = date.getDate(); 

      let taskDays: any[] = [];
      if (typeof quest.task_days_of_month === 'string') {
        taskDays = quest.task_days_of_month.split(',').map(day => {
          const trimmed = day.trim();
          return isNaN(parseInt(trimmed)) ? trimmed : parseInt(trimmed);
        });
      } else if (Array.isArray(quest.task_days_of_month)) {
        taskDays = quest.task_days_of_month;
      }


      return taskDays.some(day => {
        if (typeof day === 'number') {
          return day === dayOfMonth;
        } else if (typeof day === 'string') {
          return parseInt(day) === dayOfMonth;
        }
        return false;
      });
    }

    return true;
  }

  async checkMissedDays(questId: string): Promise<void> {
    return runInInjectionContext(this.injector, async () => {
      try {

        const { data: quest, error: questError } = await this.supabaseService.getClient()
          .from('quests')
          .select('*')
          .eq('id', questId)
          .maybeSingle();

        if (questError) {
          if (questError.code !== 'PGRST116' && questError.code !== '406') {
            return;
          } else {
            return;
          }
        }

        if (!quest) {
          return;
        }

        if (quest.quest_type !== 'quit') {
          is not a quit quest, skipping`);
          return;
        }

        const createdDate = new Date(quest.created_at);
        createdDate.setHours(0, 0, 0, 0);
        const createdDateStr = createdDate.toISOString().split('T')[0];

        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const todayStr = today.toISOString().split('T')[0];

        if (createdDate.getTime() === today.getTime()) {
          return;
        }

        const dates: Date[] = [];
        const currentDate = new Date(createdDate);

        while (currentDate <= today) {
          const dateStr = currentDate.toISOString().split('T')[0];
          dates.push(new Date(currentDate));
          currentDate.setDate(currentDate.getDate() + 1);
        }

        for (const date of dates) {
          const dateStr = date.toISOString().split('T')[0];

          let shouldShowOnDate = true;

          const dayOfWeek = date.getDay(); 
          const djangoDayOfWeek = dayOfWeek === 0 ? 6 : dayOfWeek - 1; 
          const dayOfMonth = date.getDate(); 

          if (quest.goal_period === 'week' && quest.task_days_of_week) {
            shouldShowOnDate = false;

            let taskDays: string[] = [];
            if (typeof quest.task_days_of_week === 'string') {
              taskDays = quest.task_days_of_week.split(',').map(day => day.trim());
            } else if (Array.isArray(quest.task_days_of_week)) {
              taskDays = quest.task_days_of_week;
            }

            const dayNameShort = this.getDayNameShort(djangoDayOfWeek);
            const dayNameFull = this.getDayNameFull(djangoDayOfWeek);

            shouldShowOnDate = taskDays.includes(djangoDayOfWeek) ||
                          taskDays.includes(djangoDayOfWeek.toString()) ||
                          taskDays.includes(dayNameShort) ||
                          taskDays.includes(dayNameFull);
          }

          if (quest.goal_period === 'month' && quest.task_days_of_month) {
            shouldShowOnDate = false;

            let taskDays: any[] = [];
            if (typeof quest.task_days_of_month === 'string') {
              taskDays = quest.task_days_of_month.split(',').map(day => parseInt(day.trim()));
            } else if (Array.isArray(quest.task_days_of_month)) {
              taskDays = quest.task_days_of_month;
            }

            shouldShowOnDate = taskDays.includes(dayOfMonth) ||
                         taskDays.includes(dayOfMonth.toString());
          }

          if (!shouldShowOnDate) {
            continue;
          }

          const { data: existingProgress, error: progressError } = await this.supabaseService.getClient()
            .from('quest_progress')
            .select('*')
            .eq('quest_id', questId)
            .eq('user_id', quest.user_id)
            .eq('date', dateStr)
            .maybeSingle();

          if (progressError && progressError.code !== 'PGRST116' && progressError.code !== '406') {
            continue;
          }

          if (existingProgress) {
            continue;
          }

          const dateObj = new Date(dateStr);
          if (dateObj < createdDate) {
            continue;
          }

          const newProgress = {
            quest_id: questId,
            user_id: quest.user_id,
            date: dateStr,
            completed: true,
            value_achieved: 0 
          };

          const { error: insertError } = await this.supabaseService.getClient()
            .from('quest_progress')
            .insert(newProgress);

          if (insertError) {
          }
        }


      } catch (error) {
      }
    });
  }

  async createQuitQuestProgressForToday(): Promise<boolean> {
    return runInInjectionContext(this.injector, async () => {
      try {
        const today = new Date();
        const todayStr = today.toISOString().split('T')[0];

        const dayOfWeek = today.getDay(); 
        const djangoDayOfWeek = dayOfWeek === 0 ? 6 : dayOfWeek - 1; 
        const dayOfMonth = today.getDate(); 

        const { data: quitQuests, error: questsError } = await this.supabaseService.getClient()
          .from('quests')
          .select('*')
          .eq('quest_type', 'quit')
          .eq('active', true);

        if (questsError) {
          return false;
        }

        if (!quitQuests || quitQuests.length === 0) {
          return true;
        }

        for (const quest of quitQuests) {
          const createdDate = new Date(quest.created_at);
          createdDate.setHours(0, 0, 0, 0);
          const createdDateStr = createdDate.toISOString().split('T')[0];

          if (createdDate > today) {
            continue;
          }

          let shouldShowToday = true;

          if (quest.goal_period === 'week' && quest.task_days_of_week) {
            shouldShowToday = false;

            let taskDays: string[] = [];
            if (typeof quest.task_days_of_week === 'string') {
              taskDays = quest.task_days_of_week.split(',').map(day => day.trim());
            } else if (Array.isArray(quest.task_days_of_week)) {
              taskDays = quest.task_days_of_week;
            }

            const dayNameShort = this.getDayNameShort(djangoDayOfWeek);
            const dayNameFull = this.getDayNameFull(djangoDayOfWeek);

            shouldShowToday = taskDays.includes(djangoDayOfWeek) ||
                          taskDays.includes(djangoDayOfWeek.toString()) ||
                          taskDays.includes(dayNameShort) ||
                          taskDays.includes(dayNameFull);
          }

          if (quest.goal_period === 'month' && quest.task_days_of_month) {
            shouldShowToday = false;

            let taskDays: any[] = [];
            if (typeof quest.task_days_of_month === 'string') {
              taskDays = quest.task_days_of_month.split(',').map(day => parseInt(day.trim()));
            } else if (Array.isArray(quest.task_days_of_month)) {
              taskDays = quest.task_days_of_month;
            }

            shouldShowToday = taskDays.includes(dayOfMonth) ||
                         taskDays.includes(dayOfMonth.toString());
          }

          if (!shouldShowToday) {
            continue;
          }

          const { data: existingProgress, error: progressError } = await this.supabaseService.getClient()
            .from('quest_progress')
            .select('*')
            .eq('quest_id', quest.id)
            .eq('user_id', quest.user_id)
            .eq('date', todayStr);

          if (progressError) {
            continue;
          }

          if (existingProgress && existingProgress.length > 0) {
            const { error: updateError } = await this.supabaseService.getClient()
              .from('quest_progress')
              .update({
                completed: true,
                value_achieved: 0
              })
              .eq('id', existingProgress[0].id);

            if (updateError) {
            }
          } else {
            const newProgress = {
              quest_id: quest.id,
              user_id: quest.user_id,
              date: todayStr,
              completed: true,
              value_achieved: 0
            };

            const { error: insertError } = await this.supabaseService.getClient()
              .from('quest_progress')
              .insert(newProgress);

            if (insertError) {
            }
          }


        }

        return true;
      } catch (error) {
        return false;
      }
    });
  }

  private getDayNameShort(djangoDayOfWeek: number): string {
    const dayNames = ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su'];
    return dayNames[djangoDayOfWeek];
  }

  private getDayNameFull(djangoDayOfWeek: number): string {
    const dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return dayNames[djangoDayOfWeek];
  }

  async recalculateAllStreaks(userId: string, selectedDate: Date): Promise<void> {
    return runInInjectionContext(this.injector, async () => {
      try {

        const { data: quests, error: questsError } = await this.supabaseService.getClient()
          .from('quests')
          .select('*')
          .eq('user_id', userId);

        if (questsError) {
          if (questsError.code !== 'PGRST116' && questsError.code !== '406') {
            return;
          } else {
            return;
          }
        }

        if (!quests || quests.length === 0) {
          return;
        }


        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const selectedDateObj = new Date(selectedDate);
        selectedDateObj.setHours(0, 0, 0, 0);
        const isToday = selectedDateObj.getTime() === today.getTime();
        const dateString = selectedDate.toISOString().split('T')[0];

        if (isToday) {

          for (const quest of quests) {
            await this.checkMissedDays(quest.id);
          }

          for (const quest of quests) {
          }
        } else {

          for (const quest of quests) {
            const { data: progressData, error: progressError } = await this.supabaseService.getClient()
              .from('quest_progress')
              .select('*')
              .eq('quest_id', quest.id)
              .eq('user_id', userId)
              .eq('date', dateString)
              .maybeSingle();

            if (progressError) {
              if (progressError.code !== 'PGRST116' && progressError.code !== '406') {
                continue;
              }
            }

            if (progressData) {
              let historicalStreak = 0;

              if (progressData.completed) {
                historicalStreak = 1;

                let currentDate = new Date(selectedDate);
                currentDate.setDate(currentDate.getDate() - 1);

                for (let i = 0; i < 100; i++) { 
                  const currentDateString = currentDate.toISOString().split('T')[0];

                  let shouldCount = true;
                  if (quest.goal_period === 'week' && quest.task_days_of_week) {
                    const dayName = currentDate.toLocaleDateString('en-US', { weekday: 'short' }).substring(0, 3);
                    const taskDays = quest.task_days_of_week.split(',').map((day: string) => day.trim());
                    shouldCount = taskDays.includes(dayName);
                  } else if (quest.goal_period === 'month' && quest.task_days_of_month) {
                    const dayNumber = currentDate.getDate();
                    const taskDays = quest.task_days_of_month.split(',').map((day: string) => parseInt(day.trim()));
                    shouldCount = taskDays.includes(dayNumber);
                  }

                  if (shouldCount) {
                    const { data: pastProgress, error: pastProgressError } = await this.supabaseService.getClient()
                      .from('quest_progress')
                      .select('*')
                      .eq('quest_id', quest.id)
                      .eq('user_id', userId)
                      .eq('date', currentDateString)
                      .eq('completed', true)
                      .maybeSingle();

                    if (pastProgressError && pastProgressError.code !== 'PGRST116' && pastProgressError.code !== '406') {
                      break;
                    }

                    if (!pastProgress) {
                      break;
                    }

                    historicalStreak++;
                  }

                  currentDate.setDate(currentDate.getDate() - 1);
                }
              }

              const { error: updateError } = await this.supabaseService.getClient()
                .from('quest_progress')
                .update({ historical_streak: historicalStreak })
                .eq('id', progressData.id);

              if (updateError) {
              } else {
              }
            }
          }
        }

      } catch (error) {
      }
    });
  }

  private async updateCategoryBadges(userId: string, category: string): Promise<void> {
    return runInInjectionContext(this.injector, async () => {
      try {

        const { data: badges, error: badgesError } = await this.supabaseService.getClient()
          .from('user_badges')
          .select('*')
          .eq('user_id', userId);

        if (badgesError) {
          return;
        }

        if (!badges || badges.length === 0) {

          const newBadges = {
            user_id: userId,
            badge_newbie: false,
            badge_warrior: false,
            badge_hardcore: false,
            badge_peak_performer: false,
            badge_indestructible: false,
            badge_professional: false,
            badge_streak_7_days: false,
            badge_streak_30_days: false,
            badge_streak_100_days: false,
            badge_streak_365_days: false,
            badge_sidequest_streak_7_days: false,
            badge_sidequest_streak_30_days: false,
            badge_sidequest_streak_100_days: false,
            badge_sidequest_streak_365_days: false,
            badge_friends_5: false,
            badge_friends_10: false,
            badge_strength_master: false,
            badge_money_master: false,
            badge_health_master: false,
            badge_knowledge_master: false,
            created_at: new Date(),
            updated_at: new Date()
          };

          const { data: newBadgeData, error: createError } = await this.supabaseService.getClient()
            .from('user_badges')
            .insert(newBadges)
            .select()
            .single();

          if (createError) {
            return;
          }

          badges.push(newBadgeData);
        }

        const badgeDoc = badges[0];
        const badgeField = `badge_${category}_master`;

        if (!badgeDoc[badgeField]) {

          const { error: updateError } = await this.supabaseService.getClient()
            .from('user_badges')
            .update({
              [badgeField]: true,
              updated_at: new Date()
            })
            .eq('id', badgeDoc.id);

          if (updateError) {
            return;
          }

        } else {
        }
      } catch (error) {
      }
    });
  }

  private async updateStreakBadges(questId: string, streak: number): Promise<void> {
    return runInInjectionContext(this.injector, async () => {
      try {

        const { data: quest, error: questError } = await this.supabaseService.getClient()
          .from('quests')
          .select('user_id')
          .eq('id', questId)
          .single();

        if (questError || !quest) {
          return;
        }

        const userId = quest.user_id;
        if (!userId) {
          return;
        }


        const { data: badges, error: badgesError } = await this.supabaseService.getClient()
          .from('user_badges')
          .select('*')
          .eq('user_id', userId);

        if (badgesError) {
          return;
        }

        if (!badges || badges.length === 0) {

          const newBadges = {
            user_id: userId,
            badge_newbie: false,
            badge_warrior: false,
            badge_hardcore: false,
            badge_peak_performer: false,
            badge_indestructible: false,
            badge_professional: false,
            badge_streak_7_days: false,
            badge_streak_30_days: false,
            badge_streak_100_days: false,
            badge_streak_365_days: false,
            badge_sidequest_streak_7_days: false,
            badge_sidequest_streak_30_days: false,
            badge_sidequest_streak_100_days: false,
            badge_sidequest_streak_365_days: false,
            badge_friends_5: false,
            badge_friends_10: false,
            badge_strength_master: false,
            badge_money_master: false,
            badge_health_master: false,
            badge_knowledge_master: false,
            created_at: new Date(),
            updated_at: new Date()
          };

          const { data: newBadgeData, error: createError } = await this.supabaseService.getClient()
            .from('user_badges')
            .insert(newBadges)
            .select()
            .single();

          if (createError) {
            return;
          }

          badges.push(newBadgeData);
        }

        const badgeDoc = badges[0];
        const updates: any = {
          updated_at: new Date()
        };

        if (streak >= 7) {
          updates.badge_streak_7_days = true;
        }
        if (streak >= 30) {
          updates.badge_streak_30_days = true;
        }
        if (streak >= 100) {
          updates.badge_streak_100_days = true;
        }
        if (streak >= 365) {
          updates.badge_streak_365_days = true;
        }

        if (Object.keys(updates).length > 1) { 

          const { error: updateError } = await this.supabaseService.getClient()
            .from('user_badges')
            .update(updates)
            .eq('id', badgeDoc.id);

          if (updateError) {
            return;
          }

        } else {
        }
      } catch (error) {
      }
    });
  }

  private async updateUserXP(
    userId: string,
    category: string,
    isCompleted: boolean,
    wasCompleted: boolean,
    priority: string
  ): Promise<void> {
    return runInInjectionContext(this.injector, async () => {
      try {
        if (isCompleted === wasCompleted) {
          return;
        }


        const xpField = `${category}_xp`;

        const xpValue = priority === 'high' ? 2 : 1;

        const { data: userData, error: userError } = await this.supabaseService.getClient()
          .from('profiles')
          .select(`${xpField}`)
          .eq('id', userId)
          .single();

        if (userError) {
          return;
        }

        const currentXP = userData[xpField] || 0;
        let newXP = currentXP;

        if (isCompleted && !wasCompleted) {
          newXP = currentXP + xpValue;
        } else if (!isCompleted && wasCompleted) {
          newXP = Math.max(0, currentXP - xpValue);
        }

        const { error: updateError } = await this.supabaseService.getClient()
          .from('profiles')
          .update({ [xpField]: newXP })
          .eq('id', userId);

        if (updateError) {
          return;
        }


        await this.userService.checkAndLevelUp(userId);

        await this.userService.refreshCurrentUserProfile();
      } catch (error) {
      }
    });
  }
}
