﻿import { Injectable } from '@angular/core';
import { createClient, SupabaseClient, User, AuthResponse, AuthSession  } from '@supabase/supabase-js';
import { BehaviorSubject, Observable, from } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { Router } from '@angular/router';
import { Preferences } from '@capacitor/preferences';
import { environment } from '../../environments/environment';

export const supabase = createClient(
  environment.supabase.url,
  environment.supabase.key,
  {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true
    },
    global: {
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    }
  }
);

@Injectable({
  providedIn: 'root'
})
export class SupabaseService {
  public session: AuthSession | null = null;
  public supabase: SupabaseClient = supabase;
  public storage: any = { url: environment.supabase.url };
  public supabaseKey: string = environment.supabase.key;
  _currentUser = new BehaviorSubject<User | null>(null);
  currentUser$ = this._currentUser.asObservable();

  constructor(private router: Router) {
    this.loadUser();

    this.supabase = createClient(this.storage.url, this.supabaseKey);

    this.supabase.auth.onAuthStateChange(async (event, session) => {
      if (session?.user) {
        this._currentUser.next(session.user);
        await Preferences.set({ key: 'uid', value: session.user.id });

        if (event === 'SIGNED_IN') {
          this.ensureProfileExists(session.user);

          const redirectUrl = localStorage.getItem('redirectUrl');
          if (redirectUrl) {
            localStorage.removeItem('redirectUrl');
            setTimeout(() => {
              this.router.navigateByUrl(redirectUrl);
            }, 500);
          } else {
            this.router.navigateByUrl('/tabs/home');
          }
        }
      } else {
        this._currentUser.next(null);
        await Preferences.remove({ key: 'uid' });

        if (event === 'SIGNED_OUT') {
          this.router.navigateByUrl('/signup');
        }
      }
    });
  }

  async loadSession() {
    const { data } = await this.supabase.auth.getSession();
    this.session = data.session;
    return this.session;
  }

  private async loadUser() {
    try {
      const { data, error } = await this.supabase.auth.getUser();

      if (error) {
        this._currentUser.next(null);
        return;
      }

      if (data.user) {
        this._currentUser.next(data.user);
        await Preferences.set({ key: 'uid', value: data.user.id });
        this.ensureProfileExists(data.user);
      } else {
        this._currentUser.next(null);
      }
    } catch (error) {
      this._currentUser.next(null);
    }
  }

  signUp(email: string, password: string): Observable<AuthResponse> {
    return from(this.supabase.auth.signUp({ email, password })).pipe(
      tap(res => {
        if (res.data?.user) {
          this._currentUser.next(res.data.user);
        }
      }),
      catchError(error => {
        throw error;
      })
    );
  }

  signIn(email: string, password: string): Observable<AuthResponse> {
    return from(this.supabase.auth.signInWithPassword({ email, password })).pipe(
      tap(res => {
        if (res.data?.user) {
          this._currentUser.next(res.data.user);
        }
      }),
      catchError(error => {
        throw error;
      })
    );
  }

  signOut(): Observable<any> {
    return from(this.supabase.auth.signOut()).pipe(
      tap(() => {
        this._currentUser.next(null);
        localStorage.removeItem('redirectUrl');
        this.router.navigate(['/signup/']);
      }),
      catchError(error => {
        throw error;
      })
    );
  }

  signInWithGoogle(): Observable<any> {
    return from(this.supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: window.location.origin + '/'
      }
    })).pipe(
      catchError(error => {
        throw error;
      })
    );
  }

  signInWithApple(): Observable<any> {
    return from(this.supabase.auth.signInWithOAuth({
      provider: 'apple',
      options: {
        redirectTo: window.location.origin + '/'
      }
    })).pipe(
      catchError(error => {
        throw error;
      })
    );
  }

  getCurrentUserId(): string | null {
    return this._currentUser.value?.id || null;
  }

  async getCurrentUser(): Promise<any | null> {
    const userId = this.getCurrentUserId();
    if (!userId) return null;

    try {
      const { data, error } = await this.supabase
        .from('profiles')
        .select('*')
        .filter('id', 'eq', userId)
        .single();

      if (error) {
        return null;
      }

      return data;
    } catch (error) {
      return null;
    }
  }

  isLoggedIn(): Observable<boolean> {
    return this.currentUser$.pipe(
      map(user => !!user)
    );
  }

  getClient(): SupabaseClient {
    return this.supabase;
  }

  async ensureProfileExists(user: User): Promise<void> {
    if (!user || !user.id) return;

    try {
      const { count, error: countError } = await this.supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true })
        .eq('id', user.id);

      if (countError) return;
      if (count && count > 0) return;

      const newProfile = {
        id: user.id,
        email: user.email,
        username: null,
        name: user.user_metadata?.['full_name'] || user.user_metadata?.['name'] || 'New User',
        profile_picture: user.user_metadata?.['avatar_url'] || null,
        registration_date: new Date(),
        last_login: new Date(),
        active: true,
        level: 0,
        title: '🥚 Beginner',
        strength_xp: 0,
        money_xp: 0,
        health_xp: 0,
        knowledge_xp: 0,
        plan: 'none',
        auto_renew: true,
        subscription_status: 'email marketing'
      };

      const { error: insertError } = await this.supabase
        .from('profiles')
        .insert(newProfile);

      if (insertError) {
        const minimalProfile = {
          id: user.id,
          email: user.email,
          name: user.user_metadata?.['full_name'] || user.user_metadata?.['name'] || 'New User',
          registration_date: new Date(),
          last_login: new Date(),
          active: true,
          level: 0
        };

        await this.supabase.from('profiles').insert(minimalProfile);
      }
    } catch (error) {
    }
  }
}
