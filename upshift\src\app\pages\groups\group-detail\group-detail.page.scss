﻿:host {
  --background-color: #0C0C0F;
  --text-color: #FFFFFF;
  --secondary-text: #8E8E93;
  --accent-color: #4169E1;
  --quest-bg: #1C1C1E;
  --quest-border: #2C2C2E;
  --active-date: #4169E1;
  --inactive-date: #2C2C2E;
  --card-bg: #1C1C1E;
  --border-color: #2C2C2E;
  --bg-color: #0C0C0F;
  --danger-color: #FF3B30;
  --bg-tertiary: #2C2C2E;
}

:host {
  background-color: var(--background-color);
  color: var(--text-color);
  min-height: 100vh;
  display: block;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.container {
  max-width: 480px;
  margin: 0 auto;
  padding: 20px;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo img {
  height: 24px;
}

.logo span {
  font-size: 20px;
  font-weight: 600;
}

h1 {
  font-size: 20px;
  font-weight: 600;
}

.back-link {
  display: inline-block;
  color: var(--secondary-text);
  text-decoration: none;
  margin-bottom: 20px;
  font-size: 14px;
}

.back-link:hover {
  color: var(--text-color);
}

.week-calendar {
  margin-bottom: 32px;
}

.days, .dates {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  gap: 8px;
}

.days {
  margin-bottom: 8px;
}

.day-name {
  color: #8E8E93;
  font-size: 12px;
  font-weight: 500;
}

.date {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin: 0 auto;
  font-size: 14px;
  background-color: #2C2C2E;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.date-content {
  position: relative;
  z-index: 1;
}

.date.active {
  background-color: #4169E1;
  color: white;
}

.date.selected {
  background-color: #4169E1;
  color: white;
}

.date.selected::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background-color: var(--accent-color);
  border-radius: 50%;
}

.date:hover:not(.disabled) {
  background-color: rgba(255, 255, 255, 0.1);
}

.date.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #1C1C1E;
  color: #666;
  position: relative;
}

 Add a diagonal line through dates that are before join date */
.progress-slider {
  background: linear-gradient(to right, var(--accent-color) 0%, var(--accent-color) 50%, var(--bg-tertiary) 50%, var(--bg-tertiary) 100%);
}

 Update slider background based on value */
.progress-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--accent-color);
  cursor: pointer;
  margin-top: -6px;  Adjust for the height of the track */
ion-range {
  --bar-height: 4px;
  --bar-background: var(--bg-tertiary);
  --bar-background-active: var(--accent-color);
  --knob-background: var(--accent-color);
  --knob-size: 16px;
  --pin-background: var(--accent-color);
  --pin-color: white;
  padding: 0;
  margin: 8px 0;
}

 Custom progress for ion-range */
.progress-text.values {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.members-count {
  position: relative;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.members-text {
  color: #888;
  margin-right: 4px;
}

.quest-message {
  color: #ff9800;
  font-style: italic;
  font-size: 12px;
  display: block !important;
  margin-top: 5px;
  font-weight: bold;
  text-align: left;
  width: 100%;
  white-space: normal;
  line-height: 1.2;
}

 Styles for member completion status */
.disabled-quest .quest-message {
  display: block !important;
  color: #ff9800 !important;
  font-weight: bold !important;
}

.disabled-quest::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.2);
  pointer-events: none;
  border: 2px solid #ff9800;
  border-radius: 8px;
}

.disabled-quest .quest-message {
  pointer-events: none;
}

.calendar-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  margin-bottom: 10px;
}

.nav-arrow {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: #FFFFFF;
  border-radius: 50%;
  cursor: pointer;
  text-decoration: none;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-arrow:hover {
  background: rgba(255, 255, 255, 0.1);
}

.no-quest {
  text-align: center;
  padding: 20px;
  background-color: var(--card-bg);
  border-radius: 12px;
  color: var(--secondary-text);
}

.group-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  gap: 20px;
  padding-bottom: 80px;
}

.settings-btn {
  background-color: #4d7bff;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: background-color 0.2s;
}

.settings-btn:hover {
  background-color: #3a68e0;
}

.add-quest-link {
  display: flex;
  align-items: center;
  gap: 5px;
  color: var(--accent-color);
  text-decoration: none;
  font-size: 14px;
  transition: color 0.2s;
}

.add-quest-link:hover {
  color: white;
}

.add-quest-icon {
  font-size: 16px;
  font-weight: bold;
}

 Modal styles */
.date-progress .progress-circle {
  stroke: #4169E1;
  stroke-opacity: 0.9;
}

.date-progress .progress-circle.low {
  stroke: #FF9500;
}

.date-progress circle {
  stroke-width: 3;
}

 Special handling for selected days */
