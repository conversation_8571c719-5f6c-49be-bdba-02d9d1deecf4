﻿.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  color: white;
  min-height: 100vh;
  padding-bottom: 80px;
  overflow-y: auto;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.5rem;
  font-weight: bold;
}

.logo img {
  height: 30px;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 20px;
}

.back-button {
  background: none;
  border: none;
  color: #FF9500;
  font-size: 1rem;
  cursor: pointer;
  padding: 5px 10px;
}

.admin-content {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 20px;
}

.collection-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.add-button {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.add-button:hover {
  background-color: #3e8e41;
}

.document-form {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
}

.document-form h2 {
  margin-top: 0;
  color: #FF9500;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-bottom: 20px;
}

.json-button {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
}

.save-button {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
}

.cancel-button {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
}

.form-fields {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-field label {
  font-size: 0.9rem;
  opacity: 0.8;
}

.form-field input,
.form-field select,
.form-field textarea {
  padding: 10px;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background-color: rgba(0, 0, 0, 0.2);
  color: white;
  width: 100%;
}

.form-field textarea {
  min-height: 80px;
  resize: vertical;
}

.file-input-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.file-preview {
  width: 100%;
  max-width: 200px;
  height: 150px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.form-field select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http:
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 20px;
  padding-right: 30px;
}

.checkbox-field {
  display: flex;
  align-items: center;
  gap: 10px;
}

.checkbox-field input[type="checkbox"] {
  width: 20px;
  height: 20px;
  margin: 0;
}

.checkbox-field label {
  margin: 0;
  opacity: 1;
}

.add-field {
  display: flex;
  gap: 10px;
  align-items: center;
}

.add-field input {
  flex: 1;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background-color: rgba(0, 0, 0, 0.2);
  color: white;
}

.add-field button {
  background-color: #FF9500;
  color: white;
  border: none;
  padding: 10px;
  border-radius: 4px;
  cursor: pointer;
}

.loading, .no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.documents-table {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.document-item {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 20px;
}

.document-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.document-header h3 {
  margin: 0;
  color: #FF9500;
}

.document-actions {
  display: flex;
  gap: 10px;
}

.edit-button {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
}

.delete-button {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
}

.document-fields {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 10px;
}

.document-field {
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.field-name {
  font-weight: bold;
  margin-right: 5px;
}

.field-value {
  word-break: break-word;
}

.document-edit h3 {
  margin-top: 0;
  color: #FF9500;
}

.json-editor-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.json-editor-content {
  background-color: #1e1e1e;
  border-radius: 8px;
  padding: 20px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
}

.json-editor-content h2 {
  margin-top: 0;
  color: #FF9500;
}

.json-error {
  color: #f44336;
  margin-bottom: 10px;
}

.json-editor-content textarea {
  width: 100%;
  background-color: #121212;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  padding: 10px;
  font-family: monospace;
}

.json-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 15px;
}

.apply-button {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
}
