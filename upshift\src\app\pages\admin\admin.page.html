<ion-content>
  <div class="container">
    <header>
      <div class="logo">
        <img src="assets/images/upshift_icon_mini.svg" alt="Upshift">
        <span>Upshift</span>
      </div>
      <h1>Admin Dashboard</h1>
    </header>

    <div class="admin-content">
      <div class="collections-list">
        <h2>Collections</h2>

        <div *ngIf="isLoading" class="loading">
          <ion-spinner></ion-spinner>
          <p>Loading collections...</p>
        </div>

        <div *ngIf="!isLoading && collections.length === 0" class="no-data">
          <p>No collections found.</p>
        </div>

        <div *ngIf="!isLoading && collections.length > 0" class="collection-grid">
          <div *ngFor="let collection of collections" class="collection-card" (click)="navigateToCollection(collection)">
            <h3>{{ collection }}</h3>
            <p>Manage {{ collection }}</p>
          </div>
        </div>

        <div class="add-collection">
          <h3>Add New Collection</h3>
          <div class="add-collection-form">
            <input type="text" [(ngModel)]="newCollectionName" placeholder="Collection name">
            <button (click)="addCollection()">Add</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</ion-content>

