﻿import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class CurrencyService {
  private userCurrency: string = 'USD'; 
  private currencySymbols: { [key: string]: string } = {
    'USD': '$',
    'EUR': '€',
    'GBP': '£',
    'JPY': '¥',
    'CAD': 'C$',
    'AUD': 'A$',
    'CHF': 'CHF',
    'CNY': '¥',
    'INR': '₹',
    'BRL': 'R$'
  };

  constructor() {
    this.detectUserCurrency();
  }

  private detectUserCurrency(): void {
    try {
      const locale = navigator.language;
      
      const localeToCurrency: { [key: string]: string } = {
        'en-US': 'USD',
        'en-GB': 'GBP',
        'en-CA': 'CAD',
        'en-AU': 'AUD',
        'fr-FR': 'EUR',
        'de-DE': 'EUR',
        'it-IT': 'EUR',
        'es-ES': 'EUR',
        'ja-JP': 'JPY',
        'zh-CN': 'CNY',
        'ru-RU': 'RUB',
        'pt-BR': 'BRL',
        'hi-IN': 'INR'
      };
      
      if (locale in localeToCurrency) {
        this.userCurrency = localeToCurrency[locale];
      } else if (locale.split('-')[0] === 'en') {
        this.userCurrency = 'USD';
      } else if (locale.split('-')[0] === 'fr' || 
                locale.split('-')[0] === 'de' || 
                locale.split('-')[0] === 'it' || 
                locale.split('-')[0] === 'es') {
        this.userCurrency = 'EUR';
      }
    } catch (error) {
      this.userCurrency = 'USD';
    }
  }

  formatPrice(value: number, showCode: boolean = false): string {
    if (!value && value !== 0) return '';
    
    try {
      if (showCode) {
        return `${value.toFixed(2)} ${this.userCurrency}`;
      } else {
        const symbol = this.currencySymbols[this.userCurrency] || '$';
        return `${symbol}${value.toFixed(2)}`;
      }
    } catch (error) {
      return `$${value}`;
    }
  }

  getUserCurrency(): string {
    return this.userCurrency;
  }

  setUserCurrency(currency: string): void {
    if (currency in this.currencySymbols) {
      this.userCurrency = currency;
    } else {
      this.userCurrency = 'USD';
    }
  }
}
