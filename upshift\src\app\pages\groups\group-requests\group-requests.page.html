﻿<!-- Exact HTML from Django template with Angular syntax -->
<div class="container">
    <header>
        <div class="logo-wrap">
            <div class="logo">
                <img src="assets/images/upshift_icon_mini.svg" alt="Upshift">
                <span>Upshift</span>
            </div>
        </div>
        <div class="top-date">
            Join Requests
        </div>
    </header>

    <a [routerLink]="['/groups']" class="back-link">&larr; Back to groups</a>

    <div *ngIf="successMessage" class="messages">
        <div class="message success">{{ successMessage }}</div>
    </div>

    <div *ngIf="errorMessage" class="messages">
        <div class="message error">{{ errorMessage }}</div>
    </div>

    <div *ngIf="!requestsRecent.length && !requestsMonth.length && !requestsOlder.length" class="no-requests">
        <p>You don't have any group invitations.</p>
    </div>

    <ng-container *ngIf="requestsRecent.length">
        <h2 class="section-title">Last 7 days</h2>
        <div class="request-section">
            <div *ngFor="let request of requestsRecent" class="request-card">
                <div class="request-avatar">{{ request.group?.emoji || '🏠' }}</div>
                <div class="request-info">
                    <div class="group-name"><strong>{{ request.group?.name || 'Unknown Group' }}</strong></div>
                    <div class="meta">invited by <strong>{{ request.inviter_username || 'Unknown' }}</strong></div>

                    <div class="meta-time">{{ request.created_at | date:'MMM d, y HH:mm' }}</div>
                </div>
                <div class="request-actions">
                    <a (click)="acceptRequest(request.id || '')" class="btn btn-accept">Accept</a>
                    <a (click)="rejectRequest(request.id || '')" class="btn btn-reject">Reject</a>
                </div>
            </div>
        </div>
    </ng-container>

    <ng-container *ngIf="requestsMonth.length">
        <h2 class="section-title">Last 30 days</h2>
        <div class="request-section">
            <div *ngFor="let request of requestsMonth" class="request-card">
                <div class="request-avatar">{{ request.group?.emoji || '🏠' }}</div>
                <div class="request-info">
                    <div class="group-name"><strong>{{ request.group?.name || 'Unknown Group' }}</strong></div>
                    <div class="meta">invited by <strong>{{ request.inviter_username || 'Unknown' }}</strong></div>

                    <div class="meta-time">{{ request.created_at | date:'MMM d, y HH:mm' }}</div>
                </div>
                <div class="request-actions">
                    <a (click)="acceptRequest(request.id || '')" class="btn btn-accept">Accept</a>
                    <a (click)="rejectRequest(request.id || '')" class="btn btn-reject">Reject</a>
                </div>
            </div>
        </div>
    </ng-container>

    <ng-container *ngIf="requestsOlder.length">
        <h2 class="section-title">Older</h2>
        <div class="request-section">
            <div *ngFor="let request of requestsOlder" class="request-card">
                <div class="request-avatar">{{ request.group?.emoji || '🏠' }}</div>
                <div class="request-info">
                    <div class="group-name"><strong>{{ request.group?.name || 'Unknown Group' }}</strong></div>
                    <div class="meta">invited by <strong>{{ request.inviter_username || 'Unknown' }}</strong></div>

                    <div class="meta-time">{{ request.created_at | date:'MMM d, y HH:mm' }}</div>
                </div>
                <div class="request-actions">
                    <a (click)="acceptRequest(request.id || '')" class="btn btn-accept">Accept</a>
                    <a (click)="rejectRequest(request.id || '')" class="btn btn-reject">Reject</a>
                </div>
            </div>
        </div>
    </ng-container>
</div>
