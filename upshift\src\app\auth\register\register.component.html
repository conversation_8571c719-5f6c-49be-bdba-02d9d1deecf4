﻿<ion-content>
  <div class="background-container">
    <div class="gradient-bg"></div>
    <div class="celestial-body"></div>
  </div>

  <div class="content-wrapper">
    <ion-grid>
      <ion-row>
        <ion-col size="12" class="ion-text-center">
          <h1 class="main-title">Create your <span class="gradient-text">Upshift</span> account</h1>
        </ion-col>
      </ion-row>

      <ion-row>
        <ion-col size="12">
          <div class="form-container">
            <form [formGroup]="form" (ngSubmit)="register()">
              <div class="form-fields">
                <div class="input-field">
                  <ion-input
                    formControlName="email"
                    type="email"
                    placeholder="Email"
                    required
                  ></ion-input>
                </div>

                <div class="input-field">
                  <ion-input
                    formControlName="password"
                    type="password"
                    placeholder="Password"
                    required
                  ></ion-input>
                </div>

                <div class="input-field">
                  <ion-input
                    formControlName="confirmPassword"
                    type="password"
                    placeholder="Confirm Password"
                    required
                  ></ion-input>
                </div>
              </div>

              <ion-button expand="block" type="submit" class="submit-button">
                Register
              </ion-button>
            </form>

            <div class="divider">
              <span>or continue with</span>
            </div>

            <div class="social-buttons">
              <ion-button class="social-button" (click)="signInWithGoogle()">
                <div class="button-content">
                  <ion-icon name="logo-google"></ion-icon>
                  <span>Sign up with Google</span>
                </div>
              </ion-button>

              <ion-button class="social-button" (click)="signInWithApple()">
                <div class="button-content">
                  <ion-icon name="logo-apple"></ion-icon>
                  <span>Sign up with Apple</span>
                </div>
              </ion-button>
            </div>
          </div>
        </ion-col>
      </ion-row>
    </ion-grid>
  </div>
</ion-content>