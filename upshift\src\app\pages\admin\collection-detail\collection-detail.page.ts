﻿import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { ActivatedRoute, Router } from '@angular/router';
import { AdminService } from '../../../services/admin.service';
import { forkJoin, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { SupabaseService } from '../../../services/supabase.service';

@Component({
  selector: 'app-collection-detail',
  templateUrl: './collection-detail.page.html',
  styleUrls: ['./collection-detail.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule]
})
export class CollectionDetailPage implements OnInit {
  collectionName: string = '';
  documents: any[] = [];
  isLoading = true;
  showAddForm = false;
  newDocument: any = {};
  editingDocument: any = null;
  showJsonEditor = false;
  jsonEditorContent = '';
  jsonError = '';
  foreignKeyOptions: Record<string, {id: string, display: string}[]> = {};

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private adminService: AdminService,
    private supabaseService: SupabaseService
  ) {}

  ngOnInit() {
    this.route.paramMap.subscribe(params => {
      this.collectionName = params.get('name') || '';
      if (this.collectionName) {
        this.loadDocuments();
      } else {
        this.router.navigate(['/admin']);
      }
    });
  }

  loadDocuments() {
    this.isLoading = true;
    this.adminService.getCollection(this.collectionName).subscribe({
      next: (documents) => {
        this.documents = documents;
        this.isLoading = false;
      },
      error: (error) => {
        this.isLoading = false;
      }
    });
  }

  toggleAddForm() {
    this.showAddForm = !this.showAddForm;
    if (this.showAddForm) {
      this.newDocument = this.adminService.getModelTemplate(this.collectionName);

      this.loadForeignKeyOptions();
    }
  }

  loadForeignKeyOptions() {

    const fieldTypes = this.adminService.getFieldTypes(this.collectionName);

    const foreignKeyFields = Object.keys(fieldTypes).filter(
      fieldName => fieldTypes[fieldName] === 'foreign-key'
    );

    if (foreignKeyFields.length === 0) {
      return;
    }


    interface ForeignKeyResult {
      fieldName: string;
      options: {id: string, display: string}[];
    }

    const observables = foreignKeyFields.map(fieldName => {
      return this.adminService.getForeignKeyOptions(fieldName).pipe(
        map(options => ({ fieldName, options } as ForeignKeyResult))
      );
    });

    forkJoin(observables).subscribe({
      next: (results: ForeignKeyResult[]) => {
        results.forEach(result => {
          this.foreignKeyOptions[result.fieldName] = result.options;
        });

      },
      error: (error) => {
      }
    });
  }

  selectedFiles: { [key: string]: File } = {};

  imagePreviewUrls: { [key: string]: string } = {};

  handleFileUpload(event: Event, document: any, fieldName: string) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];

      if (this.collectionName === 'affiliate_offers' && fieldName === 'image_cover') {
        const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
        if (!validTypes.includes(file.type)) {
          alert('Invalid file type. Please upload a JPG, PNG, or WebP image.');
          return;
        }

        if (file.size > 10 * 1024 * 1024) {
          alert('File size too large. Maximum size is 10MB.');
          return;
        }

        this.selectedFiles[`${document.id || 'new'}_${fieldName}`] = file;

        const reader = new FileReader();
        reader.onload = (e) => {
          const dataUrl = e.target?.result as string;
          this.imagePreviewUrls[`${document.id || 'new'}_${fieldName}`] = dataUrl;

          document[fieldName] = dataUrl;
        };
        reader.readAsDataURL(file);

      } else {
        document[fieldName] = file;
      }
    }
  }

  async addDocument() {
    this.isLoading = true;

    try {
      const processedDocument = this.handleDateConversion(this.newDocument);

      const fileKey = `new_image_cover`;
      if (this.selectedFiles[fileKey] && this.collectionName === 'affiliate_offers') {
        const file = this.selectedFiles[fileKey];

        const { data: userData, error: userError } = await this.supabaseService.getClient()
          .from('profiles')
          .select('username')
          .eq('id', this.supabaseService.getCurrentUserId())
          .single();

        if (userError) {
          throw new Error(`Error checking admin status: ${userError.message}`);
        }

        if (userData?.username !== 'admin') {
          throw new Error('Only admin users can upload files to this bucket.');
        }

        const fileName = `${Date.now()}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;


        const formData = new FormData();
        formData.append('file', file);


        const { data: { session } } = await this.supabaseService.getClient().auth.getSession();
        const accessToken = session?.access_token;

        if (!accessToken) {
          throw new Error('No access token available');
        }

        const uploadPromise = new Promise<{ publicUrl: string }>((resolve, reject) => {
          const xhr = new XMLHttpRequest();

          const queryParams = new URLSearchParams({
            'cacheControl': '3600',
            'upsert': 'true'
          });

          xhr.open('POST', `${this.supabaseService.storage.url}/storage/v1/object/affiliate-offer/${fileName}?${queryParams.toString()}`);

          xhr.setRequestHeader('Authorization', `Bearer ${accessToken}`);
          xhr.setRequestHeader('apikey', this.supabaseService.supabaseKey);

          xhr.onload = () => {
            if (xhr.status >= 200 && xhr.status < 300) {
              const { data: urlData } = this.supabaseService.getClient()
                .storage
                .from('affiliate-offer')
                .getPublicUrl(fileName);

              resolve({ publicUrl: urlData.publicUrl });
            } else {
              reject(new Error(`Upload failed: ${xhr.statusText}`));
            }
          };

          xhr.onerror = () => {
            reject(new Error('Network error during upload'));
          };

          xhr.send(formData);
        });

        const { publicUrl } = await uploadPromise;

        processedDocument.image_cover = fileName;
      }

      this.adminService.addDocument(this.collectionName, processedDocument).subscribe({
        next: () => {
          this.showAddForm = false;
          this.newDocument = {};
          this.selectedFiles = {}; 
          this.imagePreviewUrls = {}; 
          this.loadDocuments();
        },
        error: (error) => {
          this.isLoading = false;
        }
      });
    } catch (err) {
      const error = err as any;
      alert(`Error: ${error.message || JSON.stringify(error)}`);
      this.isLoading = false;
    }
  }

  editDocument(document: any) {
    this.editingDocument = { ...document };

    if (Object.keys(this.foreignKeyOptions).length === 0) {
      this.loadForeignKeyOptions();
    }
  }

  getForeignKeyOptions(fieldName: string): {id: string, display: string}[] {
    return this.foreignKeyOptions[fieldName] || [];
  }

  isForeignKey(fieldName: string): boolean {
    return this.getFieldType(fieldName) === 'foreign-key';
  }

  async updateDocument() {
    if (!this.editingDocument) return;

    this.isLoading = true;

    try {
      const docId = this.editingDocument.id;
      const docData = { ...this.editingDocument };
      delete docData.id;

      const processedData = this.handleDateConversion(docData);

      const fileKey = `${docId}_image_cover`;
      if (this.selectedFiles[fileKey] && this.collectionName === 'affiliate_offers') {
        const file = this.selectedFiles[fileKey];

        const { data: userData, error: userError } = await this.supabaseService.getClient()
          .from('profiles')
          .select('username')
          .eq('id', this.supabaseService.getCurrentUserId())
          .single();

        if (userError) {
          throw new Error(`Error checking admin status: ${userError.message}`);
        }

        if (userData?.username !== 'admin') {
          throw new Error('Only admin users can upload files to this bucket.');
        }

        const fileName = `${Date.now()}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;


        const formData = new FormData();
        formData.append('file', file);


        const { data: { session } } = await this.supabaseService.getClient().auth.getSession();
        const accessToken = session?.access_token;

        if (!accessToken) {
          throw new Error('No access token available');
        }

        const uploadPromise = new Promise<{ publicUrl: string }>((resolve, reject) => {
          const xhr = new XMLHttpRequest();

          const queryParams = new URLSearchParams({
            'cacheControl': '3600',
            'upsert': 'true'
          });

          xhr.open('POST', `${this.supabaseService.storage.url}/storage/v1/object/affiliate-offer/${fileName}?${queryParams.toString()}`);

          xhr.setRequestHeader('Authorization', `Bearer ${accessToken}`);
          xhr.setRequestHeader('apikey', this.supabaseService.supabaseKey);

          xhr.onload = () => {
            if (xhr.status >= 200 && xhr.status < 300) {
              const { data: urlData } = this.supabaseService.getClient()
                .storage
                .from('affiliate-offer')
                .getPublicUrl(fileName);

              resolve({ publicUrl: urlData.publicUrl });
            } else {
              reject(new Error(`Upload failed: ${xhr.statusText}`));
            }
          };

          xhr.onerror = () => {
            reject(new Error('Network error during upload'));
          };

          xhr.send(formData);
        });

        const { publicUrl } = await uploadPromise;

        processedData.image_cover = fileName;
      }

      this.adminService.updateDocument(this.collectionName, docId, processedData).subscribe({
        next: () => {
          this.editingDocument = null;
          this.selectedFiles = {}; 
          this.imagePreviewUrls = {}; 
          this.loadDocuments();
        },
        error: (error) => {
          this.isLoading = false;
        }
      });
    } catch (err) {
      const error = err as any;
      alert(`Error: ${error.message || JSON.stringify(error)}`);
      this.isLoading = false;
    }
  }

  fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  }

  formatDocumentFieldValue(document: any, key: string): string {
    if (this.collectionName === 'affiliate_offers' && key === 'image_cover' && document[key]) {
      try {
        if (!document[key].includes('://')) {
          const { data: urlData } = this.supabaseService.getClient()
            .storage
            .from('affiliate-offer')
            .getPublicUrl(document[key]);


          return `<img src="${urlData.publicUrl}" alt="Image" style="max-width: 100px; max-height: 100px;">`;
        } else {
          return `<img src="${document[key]}" alt="Image" style="max-width: 100px; max-height: 100px;">`;
        }
      } catch (err) {
        const error = err as any;
        return `Image: ${document[key]} (Error loading preview)`;
      }
    }

    return this.formatValue(document[key]);
  }

  deleteDocument(docId: string) {
    if (confirm('Are you sure you want to delete this document? This action cannot be undone.')) {
      this.isLoading = true;
      this.adminService.deleteDocument(this.collectionName, docId).subscribe({
        next: () => {
          this.loadDocuments();
        },
        error: (error) => {
          this.isLoading = false;
        }
      });
    }
  }

  cancelEdit() {
    this.editingDocument = null;
  }

  openJsonEditor(document?: any) {
    if (document) {
      const docCopy = { ...document };
      delete docCopy.id;
      this.jsonEditorContent = JSON.stringify(docCopy, null, 2);
      this.editingDocument = document;
    } else {
      this.jsonEditorContent = JSON.stringify(this.newDocument || {}, null, 2);
    }
    this.showJsonEditor = true;
    this.jsonError = '';
  }

  applyJsonChanges() {
    try {
      const parsedJson = JSON.parse(this.jsonEditorContent);

      if (this.editingDocument) {
        this.editingDocument = {
          id: this.editingDocument.id,
          ...parsedJson
        };
      } else {
        this.newDocument = parsedJson;
      }

      this.showJsonEditor = false;
      this.jsonError = '';
    } catch (e) {
      this.jsonError = 'Invalid JSON format';
    }
  }

  cancelJsonEdit() {
    this.showJsonEditor = false;
    this.jsonError = '';
  }

  goBack() {
    this.router.navigate(['/admin']);
  }

  getObjectKeys(obj: any): string[] {
    return Object.keys(obj || {});
  }

  isObject(value: any): boolean {
    return typeof value === 'object' && value !== null && !Array.isArray(value);
  }

  formatValue(value: any): string {
    if (value === null || value === undefined) {
      return 'null';
    }

    if (typeof value === 'object') {
      if (value instanceof Date) {
        return value.toLocaleString();
      }
      return JSON.stringify(value);
    }

    return String(value);
  }

  getFieldType(fieldName: string): string {
    if (this.collectionName === 'affiliate_offers' && fieldName === 'image_cover') {
      return 'file';
    }

    const fieldTypes = this.adminService.getFieldTypes(this.collectionName);
    return fieldTypes[fieldName] || 'text';
  }

  getSelectOptions(fieldName: string): string[] {
    return this.adminService.getSelectOptions(this.collectionName, fieldName);
  }

  formatDateForInput(value: any): string {
    if (!value) return '';

    let date: Date;

    if (value instanceof Date) {
      date = value;
    } else if (typeof value === 'object' && value.seconds) {
      date = new Date(value.seconds * 1000);
    } else if (typeof value === 'string') {
      try {
        date = new Date(value);
        if (isNaN(date.getTime())) {
          return value;
        }
      } catch (e) {
        return value;
      }
    } else {
      return String(value);
    }

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  handleDateChange(obj: any, key: string, event: any): void {
    if (event) {
      obj[key] = new Date(event);
    } else {
      obj[key] = null;
    }
  }

  handleDateConversion(obj: any): any {
    if (!obj) return obj;

    const result = { ...obj };

    for (const key in result) {
      const value = result[key];

      if (typeof value === 'string' && this.getFieldType(key) === 'date') {
        try {
          result[key] = new Date(value);
        } catch (e) {
        }
      }
    }

    return result;
  }
}
