import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { ActivatedRoute, Router } from '@angular/router';
import { AdminService } from '../../../services/admin.service';
import { forkJoin, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { SupabaseService } from '../../../services/supabase.service';

@Component({
  selector: 'app-collection-detail',
  templateUrl: './collection-detail.page.html',
  styleUrls: ['./collection-detail.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule]
})
export class CollectionDetailPage implements OnInit {
  collectionName: string = '';
  documents: any[] = [];
  isLoading = true;
  showAddForm = false;
  newDocument: any = {};
  editingDocument: any = null;
  showJsonEditor = false;
  jsonEditorContent = '';
  jsonError = '';
  foreignKeyOptions: Record<string, {id: string, display: string}[]> = {};

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private adminService: AdminService,
    private supabaseService: SupabaseService
  ) {}

  ngOnInit() {
    this.route.paramMap.subscribe(params => {
      this.collectionName = params.get('name') || '';
      if (this.collectionName) {
        this.loadDocuments();
      } else {
        this.router.navigate(['/admin']);
      }
    });
  }

  loadDocuments() {
    this.isLoading = true;
    this.adminService.getCollection(this.collectionName).subscribe({
      next: (documents) => {
        this.documents = documents;
        this.isLoading = false;
      },
      error: (error) => {
        console.error(`Error loading ${this.collectionName}:`, error);
        this.isLoading = false;
      }
    });
  }

  toggleAddForm() {
    this.showAddForm = !this.showAddForm;
    if (this.showAddForm) {
      // Get model template for this collection
      this.newDocument = this.adminService.getModelTemplate(this.collectionName);
      console.log('Using model template:', this.newDocument);

      // Load foreign key options
      this.loadForeignKeyOptions();
    }
  }

  // Load options for all foreign key fields in the current collection
  loadForeignKeyOptions() {
    console.log('Loading foreign key options for collection:', this.collectionName);

    // Get all field types for this collection
    const fieldTypes = this.adminService.getFieldTypes(this.collectionName);

    // Find all foreign key fields
    const foreignKeyFields = Object.keys(fieldTypes).filter(
      fieldName => fieldTypes[fieldName] === 'foreign-key'
    );

    if (foreignKeyFields.length === 0) {
      console.log('No foreign key fields found in this collection');
      return;
    }

    console.log('Found foreign key fields:', foreignKeyFields);

    // Define the result type
    interface ForeignKeyResult {
      fieldName: string;
      options: {id: string, display: string}[];
    }

    // Create an observable for each foreign key field
    const observables = foreignKeyFields.map(fieldName => {
      return this.adminService.getForeignKeyOptions(fieldName).pipe(
        map(options => ({ fieldName, options } as ForeignKeyResult))
      );
    });

    // Load all foreign key options in parallel
    forkJoin(observables).subscribe({
      next: (results: ForeignKeyResult[]) => {
        // Store the options for each field
        results.forEach(result => {
          this.foreignKeyOptions[result.fieldName] = result.options;
        });

        console.log('Loaded foreign key options:', this.foreignKeyOptions);
      },
      error: (error) => {
        console.error('Error loading foreign key options:', error);
      }
    });
  }

  // Store selected files temporarily
  selectedFiles: { [key: string]: File } = {};

  // Preview URLs for images
  imagePreviewUrls: { [key: string]: string } = {};

  handleFileUpload(event: Event, document: any, fieldName: string) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];

      // Check if this is for affiliate_offers image_cover
      if (this.collectionName === 'affiliate_offers' && fieldName === 'image_cover') {
        // Validate file type
        const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
        if (!validTypes.includes(file.type)) {
          alert('Invalid file type. Please upload a JPG, PNG, or WebP image.');
          return;
        }

        // Validate file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
          alert('File size too large. Maximum size is 10MB.');
          return;
        }

        // Store the file for later upload
        this.selectedFiles[`${document.id || 'new'}_${fieldName}`] = file;

        // Create a preview
        const reader = new FileReader();
        reader.onload = (e) => {
          const dataUrl = e.target?.result as string;
          this.imagePreviewUrls[`${document.id || 'new'}_${fieldName}`] = dataUrl;

          // Update the document with the preview URL for display purposes only
          document[fieldName] = dataUrl;
        };
        reader.readAsDataURL(file);

        console.log('File selected for later upload:', file.name);
      } else {
        // For other file uploads, just store the file directly
        document[fieldName] = file;
      }
    }
  }

  async addDocument() {
    this.isLoading = true;

    try {
      // Convert any string dates to Date objects
      const processedDocument = this.handleDateConversion(this.newDocument);
      console.log('Adding document with processed dates:', processedDocument);

      // Check if we have any files to upload
      const fileKey = `new_image_cover`;
      if (this.selectedFiles[fileKey] && this.collectionName === 'affiliate_offers') {
        const file = this.selectedFiles[fileKey];

        // Check if the current user is admin
        const { data: userData, error: userError } = await this.supabaseService.getClient()
          .from('profiles')
          .select('username')
          .eq('id', this.supabaseService.getCurrentUserId())
          .single();

        if (userError) {
          throw new Error(`Error checking admin status: ${userError.message}`);
        }

        if (userData?.username !== 'admin') {
          throw new Error('Only admin users can upload files to this bucket.');
        }

        // Create a unique file name
        const fileName = `${Date.now()}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;

        console.log('Uploading file:', fileName);

        // Create a FormData object
        const formData = new FormData();
        formData.append('file', file);

        console.log('Uploading file with type:', file.type);

        // Get the access token
        const { data: { session } } = await this.supabaseService.getClient().auth.getSession();
        const accessToken = session?.access_token;

        if (!accessToken) {
          throw new Error('No access token available');
        }

        // Create a promise to handle the XMLHttpRequest
        const uploadPromise = new Promise<{ publicUrl: string }>((resolve, reject) => {
          const xhr = new XMLHttpRequest();

          // Add additional query parameters
          const queryParams = new URLSearchParams({
            'cacheControl': '3600',
            'upsert': 'true'
          });

          // Set up the request with query parameters
          xhr.open('POST', `${this.supabaseService.storage.url}/storage/v1/object/affiliate-offer/${fileName}?${queryParams.toString()}`);

          // Set headers
          xhr.setRequestHeader('Authorization', `Bearer ${accessToken}`);
          xhr.setRequestHeader('apikey', this.supabaseService.supabaseKey);

          // Handle response
          xhr.onload = () => {
            if (xhr.status >= 200 && xhr.status < 300) {
              // Success - get the public URL
              const { data: urlData } = this.supabaseService.getClient()
                .storage
                .from('affiliate-offer')
                .getPublicUrl(fileName);

              resolve({ publicUrl: urlData.publicUrl });
            } else {
              // Error
              console.error('Upload failed:', xhr.statusText, xhr.responseText);
              reject(new Error(`Upload failed: ${xhr.statusText}`));
            }
          };

          // Handle network errors
          xhr.onerror = () => {
            console.error('Network error during upload');
            reject(new Error('Network error during upload'));
          };

          // Send the request with the FormData
          xhr.send(formData);
        });

        // Wait for the upload to complete
        const { publicUrl } = await uploadPromise;
        console.log('Upload successful, public URL:', publicUrl);

        // Set the image_cover field to the file name
        processedDocument.image_cover = fileName;
      }

      // Now add the document with the file reference
      this.adminService.addDocument(this.collectionName, processedDocument).subscribe({
        next: () => {
          this.showAddForm = false;
          this.newDocument = {};
          this.selectedFiles = {}; // Clear selected files
          this.imagePreviewUrls = {}; // Clear preview URLs
          this.loadDocuments();
        },
        error: (error) => {
          console.error('Error adding document:', error);
          this.isLoading = false;
        }
      });
    } catch (err) {
      const error = err as any;
      console.error('Error in addDocument:', error);
      alert(`Error: ${error.message || JSON.stringify(error)}`);
      this.isLoading = false;
    }
  }

  editDocument(document: any) {
    this.editingDocument = { ...document };

    // Make sure foreign key options are loaded
    if (Object.keys(this.foreignKeyOptions).length === 0) {
      this.loadForeignKeyOptions();
    }
  }

  // Get foreign key options for a specific field
  getForeignKeyOptions(fieldName: string): {id: string, display: string}[] {
    return this.foreignKeyOptions[fieldName] || [];
  }

  // Check if a field is a foreign key
  isForeignKey(fieldName: string): boolean {
    return this.getFieldType(fieldName) === 'foreign-key';
  }

  async updateDocument() {
    if (!this.editingDocument) return;

    this.isLoading = true;

    try {
      const docId = this.editingDocument.id;
      const docData = { ...this.editingDocument };
      delete docData.id;

      // Convert any string dates to Date objects
      const processedData = this.handleDateConversion(docData);
      console.log('Updating document with processed dates:', processedData);

      // Check if we have any files to upload
      const fileKey = `${docId}_image_cover`;
      if (this.selectedFiles[fileKey] && this.collectionName === 'affiliate_offers') {
        const file = this.selectedFiles[fileKey];

        // Check if the current user is admin
        const { data: userData, error: userError } = await this.supabaseService.getClient()
          .from('profiles')
          .select('username')
          .eq('id', this.supabaseService.getCurrentUserId())
          .single();

        if (userError) {
          throw new Error(`Error checking admin status: ${userError.message}`);
        }

        if (userData?.username !== 'admin') {
          throw new Error('Only admin users can upload files to this bucket.');
        }

        // Create a unique file name
        const fileName = `${Date.now()}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;

        console.log('Uploading file:', fileName);

        // Create a FormData object
        const formData = new FormData();
        formData.append('file', file);

        console.log('Uploading file with type:', file.type);

        // Get the access token
        const { data: { session } } = await this.supabaseService.getClient().auth.getSession();
        const accessToken = session?.access_token;

        if (!accessToken) {
          throw new Error('No access token available');
        }

        // Create a promise to handle the XMLHttpRequest
        const uploadPromise = new Promise<{ publicUrl: string }>((resolve, reject) => {
          const xhr = new XMLHttpRequest();

          // Add additional query parameters
          const queryParams = new URLSearchParams({
            'cacheControl': '3600',
            'upsert': 'true'
          });

          // Set up the request with query parameters
          xhr.open('POST', `${this.supabaseService.storage.url}/storage/v1/object/affiliate-offer/${fileName}?${queryParams.toString()}`);

          // Set headers
          xhr.setRequestHeader('Authorization', `Bearer ${accessToken}`);
          xhr.setRequestHeader('apikey', this.supabaseService.supabaseKey);

          // Handle response
          xhr.onload = () => {
            if (xhr.status >= 200 && xhr.status < 300) {
              // Success - get the public URL
              const { data: urlData } = this.supabaseService.getClient()
                .storage
                .from('affiliate-offer')
                .getPublicUrl(fileName);

              resolve({ publicUrl: urlData.publicUrl });
            } else {
              // Error
              console.error('Upload failed:', xhr.statusText, xhr.responseText);
              reject(new Error(`Upload failed: ${xhr.statusText}`));
            }
          };

          // Handle network errors
          xhr.onerror = () => {
            console.error('Network error during upload');
            reject(new Error('Network error during upload'));
          };

          // Send the request with the FormData
          xhr.send(formData);
        });

        // Wait for the upload to complete
        const { publicUrl } = await uploadPromise;
        console.log('Upload successful, public URL:', publicUrl);

        // Set the image_cover field to the file name
        processedData.image_cover = fileName;
      }

      // Now update the document with the file reference
      this.adminService.updateDocument(this.collectionName, docId, processedData).subscribe({
        next: () => {
          this.editingDocument = null;
          this.selectedFiles = {}; // Clear selected files
          this.imagePreviewUrls = {}; // Clear preview URLs
          this.loadDocuments();
        },
        error: (error) => {
          console.error('Error updating document:', error);
          this.isLoading = false;
        }
      });
    } catch (err) {
      const error = err as any;
      console.error('Error in updateDocument:', error);
      alert(`Error: ${error.message || JSON.stringify(error)}`);
      this.isLoading = false;
    }
  }

  // Helper method to convert File to base64
  fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  }

  // Format document field value for display
  formatDocumentFieldValue(document: any, key: string): string {
    // Special case for image_cover in affiliate_offers
    if (this.collectionName === 'affiliate_offers' && key === 'image_cover' && document[key]) {
      try {
        if (!document[key].includes('://')) {
          // Get the public URL for the image
          const { data: urlData } = this.supabaseService.getClient()
            .storage
            .from('affiliate-offer')
            .getPublicUrl(document[key]);

          console.log(`Generated public URL for display ${document[key]}:`, urlData);

          return `<img src="${urlData.publicUrl}" alt="Image" style="max-width: 100px; max-height: 100px;">`;
        } else {
          return `<img src="${document[key]}" alt="Image" style="max-width: 100px; max-height: 100px;">`;
        }
      } catch (err) {
        const error = err as any;
        console.error(`Error getting public URL for display ${document[key]}:`, error);
        return `Image: ${document[key]} (Error loading preview)`;
      }
    }

    return this.formatValue(document[key]);
  }

  deleteDocument(docId: string) {
    if (confirm('Are you sure you want to delete this document? This action cannot be undone.')) {
      this.isLoading = true;
      this.adminService.deleteDocument(this.collectionName, docId).subscribe({
        next: () => {
          this.loadDocuments();
        },
        error: (error) => {
          console.error('Error deleting document:', error);
          this.isLoading = false;
        }
      });
    }
  }

  cancelEdit() {
    this.editingDocument = null;
  }

  openJsonEditor(document?: any) {
    if (document) {
      // Editing existing document
      const docCopy = { ...document };
      delete docCopy.id;
      this.jsonEditorContent = JSON.stringify(docCopy, null, 2);
      this.editingDocument = document;
    } else {
      // Creating new document
      this.jsonEditorContent = JSON.stringify(this.newDocument || {}, null, 2);
    }
    this.showJsonEditor = true;
    this.jsonError = '';
  }

  applyJsonChanges() {
    try {
      const parsedJson = JSON.parse(this.jsonEditorContent);

      if (this.editingDocument) {
        // Update existing document
        this.editingDocument = {
          id: this.editingDocument.id,
          ...parsedJson
        };
      } else {
        // New document
        this.newDocument = parsedJson;
      }

      this.showJsonEditor = false;
      this.jsonError = '';
    } catch (e) {
      this.jsonError = 'Invalid JSON format';
    }
  }

  cancelJsonEdit() {
    this.showJsonEditor = false;
    this.jsonError = '';
  }

  goBack() {
    this.router.navigate(['/admin']);
  }

  // Helper method to get object keys for template
  getObjectKeys(obj: any): string[] {
    return Object.keys(obj || {});
  }

  // Helper method to determine if a value is an object
  isObject(value: any): boolean {
    return typeof value === 'object' && value !== null && !Array.isArray(value);
  }

  // Helper method to format values for display
  formatValue(value: any): string {
    if (value === null || value === undefined) {
      return 'null';
    }

    if (typeof value === 'object') {
      if (value instanceof Date) {
        return value.toLocaleString();
      }
      return JSON.stringify(value);
    }

    return String(value);
  }

  // Get the field type for a specific field
  getFieldType(fieldName: string): string {
    // Special case for image_cover in affiliate_offers
    if (this.collectionName === 'affiliate_offers' && fieldName === 'image_cover') {
      return 'file';
    }

    const fieldTypes = this.adminService.getFieldTypes(this.collectionName);
    return fieldTypes[fieldName] || 'text';
  }

  // Get select options for a field
  getSelectOptions(fieldName: string): string[] {
    return this.adminService.getSelectOptions(this.collectionName, fieldName);
  }

  // Format a date value for input fields
  formatDateForInput(value: any): string {
    if (!value) return '';

    let date: Date;

    if (value instanceof Date) {
      date = value;
    } else if (typeof value === 'object' && value.seconds) {
      // Handle Firestore Timestamp
      date = new Date(value.seconds * 1000);
    } else if (typeof value === 'string') {
      try {
        date = new Date(value);
        if (isNaN(date.getTime())) {
          return value;
        }
      } catch (e) {
        return value;
      }
    } else {
      return String(value);
    }

    // Format as YYYY-MM-DD
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // Handle date input change
  handleDateChange(obj: any, key: string, event: any): void {
    if (event) {
      obj[key] = new Date(event);
    } else {
      obj[key] = null;
    }
  }

  // Handle date conversion for saving to Firestore
  handleDateConversion(obj: any): any {
    if (!obj) return obj;

    const result = { ...obj };

    for (const key in result) {
      const value = result[key];

      // Convert string dates to Date objects
      if (typeof value === 'string' && this.getFieldType(key) === 'date') {
        try {
          result[key] = new Date(value);
        } catch (e) {
          console.error(`Failed to convert ${key} to Date:`, e);
        }
      }
    }

    return result;
  }
}
