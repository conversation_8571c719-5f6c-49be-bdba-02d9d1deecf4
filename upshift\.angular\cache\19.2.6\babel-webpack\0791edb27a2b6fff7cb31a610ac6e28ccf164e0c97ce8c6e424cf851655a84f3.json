{"ast": null, "code": "export const environment = {\n  production: false,\n  supabase: {\n    url: 'https://tobifepmbrrrvshpvrqa.supabase.co',\n    key: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRvYmlmZXBtYnJycnZzaHB2cnFhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU2ODY0NDgsImV4cCI6MjA2MTI2MjQ0OH0.afZPEEdrS9SF755RMeZCFzi5vi1nZFnlO86qKGUHeVw',\n    functionsUrl: 'https://tobifepmbrrrvshpvrqa.supabase.co/functions/v1'\n  },\n  stripe: {\n    publishableKey: 'pk_test_51LlETOEB7UFpNfHGFTFA8OLyCMuwT9TBOjUF0Tcw9zIbdFQCV7IenK3rfXlSvXE3CNJebHJ9tLwHxEHtF4XfAJfL00tLB9aiHN'\n  }\n};", "map": {"version": 3, "names": ["environment", "production", "supabase", "url", "key", "functionsUrl", "stripe", "publishableKey"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\environments\\environment.ts"], "sourcesContent": ["\r\nexport const environment = {\r\n  production: false,\r\n  supabase: {\r\n    url: 'https://tobifepmbrrrvshpvrqa.supabase.co',\r\n    key: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRvYmlmZXBtYnJycnZzaHB2cnFhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU2ODY0NDgsImV4cCI6MjA2MTI2MjQ0OH0.afZPEEdrS9SF755RMeZCFzi5vi1nZFnlO86qKGUHeVw',\r\n    functionsUrl: 'https://tobifepmbrrrvshpvrqa.supabase.co/functions/v1'\r\n  },\r\n  stripe: {\r\n    publishableKey: 'pk_test_51LlETOEB7UFpNfHGFTFA8OLyCMuwT9TBOjUF0Tcw9zIbdFQCV7IenK3rfXlSvXE3CNJebHJ9tLwHxEHtF4XfAJfL00tLB9aiHN'\r\n  }\r\n};\r\n\r\n"], "mappings": "AACA,OAAO,MAAMA,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EACjBC,QAAQ,EAAE;IACRC,GAAG,EAAE,0CAA0C;IAC/CC,GAAG,EAAE,kNAAkN;IACvNC,YAAY,EAAE;GACf;EACDC,MAAM,EAAE;IACNC,cAAc,EAAE;;CAEnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}