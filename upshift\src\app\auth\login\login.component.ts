﻿import { Component, inject, EnvironmentInjector, runInInjectionContext } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { Preferences } from '@capacitor/preferences';
import { SupabaseService } from '../../services/supabase.service';
import { UserService } from '../../services/user.service';

@Component({
  selector: 'app-login',
  standalone: true,
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
  imports: [CommonModule, IonicModule, ReactiveFormsModule, RouterModule],
})
export class LoginComponent {
  form: FormGroup;
  segment: 'login';
  private fb = inject(FormBuilder);
  private supabaseService = inject(SupabaseService);
  private userService = inject(UserService);
  private router = inject(Router);
  private injector = inject(EnvironmentInjector);

  constructor() {
    this.form = this.fb.group({
      email: [''],
      password: [''],
    });
  }


  async login() {
    const { email, password } = this.form.value;

    if (!email || !password) {
      alert('Please enter both email and password');
      return;
    }

    try {
      const { data, error } = await this.supabaseService.getClient().auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        alert('Login error: ' + error.message);
        return;
      }

      await this.handlePostLogin(data.user.id);
    } catch (error: any) {
      alert('Login error: ' + error.message);
    }
  }

  async signInWithGoogle() {
    try {
      const { data, error } = await this.supabaseService.getClient().auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: '/'
        }
      });

      if (error) {
        alert('Error signing in with Google: ' + error.message);
        return;
      }

    } catch (error: any) {
      alert('Error signing in with Google');
    }
  }

  async signInWithApple() {
    try {
      const { data, error } = await this.supabaseService.getClient().auth.signInWithOAuth({
        provider: 'apple',
        options: {
          redirectTo: '/'
        }
      });

      if (error) {
        alert('Error signing in with Apple: ' + error.message);
        return;
      }

    } catch (error: any) {
      alert('Error signing in with Apple');
    }
  }

  private async handlePostLogin(uid: string) {
    try {
      await Preferences.set({ key: 'uid', value: uid });

      const { data: userData, error } = await this.supabaseService.getClient()
        .from('profiles')
        .select('*')
        .eq('id', uid)
        .single();

      if (error && error.code !== 'PGRST116') { 
      }

      if (!userData) {
        const authUser = this.supabaseService._currentUser.value;
        if (authUser) {
          await this.createUserRecord(uid, authUser);
        }
      } else {
        const { error: updateError } = await this.supabaseService.getClient()
          .from('profiles')
          .update({ last_login: new Date() })
          .eq('id', uid);

        if (updateError) {
        }
      }

      this.router.navigateByUrl('/');
    } catch (error) {
    }
  }
}