﻿<!-- Static App Blocker Page -->
<div class="container">
    <header>
        <div class="logo">
            <img src="assets/images/upshift_icon_mini.svg" alt="Upshift">
            <span>Upshift</span>
        </div>
        <h1>App Blocker</h1>
    </header>

    <div class="app-blocker-container">
        <div class="info-card">
            <div class="info-icon">ℹ️</div>
            <div class="info-content">
                <p>The App Blocker helps you stay focused by blocking distracting apps and websites during focus sessions.</p>
                <p>This feature is currently only available on desktop and requires the Upshift browser extension.</p>
            </div>
        </div>

        <div class="blocker-settings">
            <h2>Blocker Settings</h2>
            <div class="setting-group">
                <label class="toggle-label">
                    <span>Enable App Blocker</span>
                    <div class="toggle-switch">
                        <input type="checkbox" [(ngModel)]="blockerEnabled">
                        <span class="toggle-slider"></span>
                    </div>
                </label>
                <p class="setting-description">Block distracting apps and websites during focus sessions</p>
            </div>

            <div class="setting-group">
                <label class="toggle-label">
                    <span>Strict Mode</span>
                    <div class="toggle-switch">
                        <input type="checkbox" [(ngModel)]="strictMode">
                        <span class="toggle-slider"></span>
                    </div>
                </label>
                <p class="setting-description">Cannot disable blocker during active focus sessions</p>
            </div>

            <div class="setting-group">
                <label for="blockDuration">Block Duration</label>
                <select id="blockDuration" [(ngModel)]="blockDuration">
                    <option value="focus">During Focus Sessions Only</option>
                    <option value="custom">Custom Schedule</option>
                </select>
            </div>

            <div class="custom-schedule" *ngIf="blockDuration === 'custom'">
                <h3>Custom Schedule</h3>
                <div class="time-range">
                    <div class="time-input">
                        <label for="startTime">Start Time</label>
                        <input type="time" id="startTime" [(ngModel)]="startTime">
                    </div>
                    <div class="time-input">
                        <label for="endTime">End Time</label>
                        <input type="time" id="endTime" [(ngModel)]="endTime">
                    </div>
                </div>
                <div class="days-selector">
                    <div class="day-checkbox" *ngFor="let day of weekDays">
                        <input type="checkbox" [id]="'day-' + day.value.toLowerCase()"
                               [value]="day.value"
                               [(ngModel)]="day.selected">
                        <label [for]="'day-' + day.value.toLowerCase()">{{ day.label }}</label>
                    </div>
                </div>
            </div>
        </div>

        <div class="blocked-items">
            <h2>Blocked Apps & Websites</h2>
            <div class="blocked-list">
                <div *ngFor="let item of blockedItems" class="blocked-item">
                    <div class="item-icon">{{ item.icon }}</div>
                    <div class="item-info">
                        <div class="item-name">{{ item.name }}</div>
                        <div class="item-type">{{ item.type }}</div>
                    </div>
                    <button class="remove-btn" (click)="removeBlockedItem(item)">Remove</button>
                </div>
                <div *ngIf="blockedItems.length === 0" class="no-items">
                    <p>No apps or websites are currently blocked.</p>
                </div>
            </div>

            <div class="add-item-form">
                <h3>Add New Item</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="itemType">Type</label>
                        <select id="itemType" [(ngModel)]="newItem.type">
                            <option value="website">Website</option>
                            <option value="app">Application</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="itemName">Name/URL</label>
                        <input type="text" id="itemName" [(ngModel)]="newItem.name" placeholder="Enter website URL or app name">
                    </div>
                </div>
                <button class="add-btn" (click)="addBlockedItem()">Add to Blocklist</button>
            </div>
        </div>

        <div class="extension-section">
            <h2>Browser Extension</h2>
            <div class="extension-card">
                <div class="extension-icon">🧩</div>
                <div class="extension-info">
                    <h3>Upshift Focus Extension</h3>
                    <p>Required for website blocking functionality</p>
                    <div class="extension-status" [class.connected]="extensionConnected">
                        {{ extensionConnected ? 'Connected' : 'Not Connected' }}
                    </div>
                </div>
                <a href="#" class="extension-btn" (click)="installExtension()">
                    {{ extensionConnected ? 'Settings' : 'Install' }}
                </a>
            </div>
        </div>

        <button class="save-settings-btn" (click)="saveSettings()">Save Settings</button>
    </div>
</div>
