<ion-content class="ion-padding">
  <ion-header>
    <ion-toolbar>
      <ion-row>
        <ion-col size="6">
          <ion-img src="/assets/images/upshift_icon_mini.svg"></ion-img>
          <ion-text>Upshift</ion-text>
        </ion-col>
        <ion-col size="6">
          <h2>{{ headerText }}</h2>
        </ion-col>
      </ion-row>
    </ion-toolbar>
  </ion-header>

  <ion-grid>
    <ion-row>
      <ion-col>
        <ion-icon name="arrow-back-outline" (click)="changeWeek(-1)"></ion-icon>
      </ion-col>
      <ion-col>
        <div class="day-name" *ngFor="let day of ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su']">{{ day }}</div>
      </ion-col>
      <ion-col>
        <ion-icon name="arrow-forward-outline" (click)="changeWeek(1)"></ion-icon>
      </ion-col>
    </ion-row>
    <ion-row>
      <div *ngFor="let date of weekDates"
           class="date"
           [class.active]="date.is_today"
           [class.selected]="date.is_selected"
           [class.disabled]="date.is_future"
           (click)="!date.is_future && selectDate(date.date)">
        <svg class="date-progress" viewBox="0 0 36 36" *ngIf="date.total_quests > 0 && !date.is_future">
          <circle cx="18" cy="18" r="15.5"
                  [attr.stroke-dasharray]="date.completion_percentage + ', 100'"
                  [attr.data-date]="date.date"
                  class="progress-circle"
                  [class.low]="date.completion_percentage < 50"></circle>
        </svg>
        <span class="date-content">{{ date.day }}</span>
      </div>
    </ion-row>
    <ion-row>
      <ion-col>
        <h2>Quests</h2>
      </ion-col>
      <ion-col>
        <ion-button fill="clear" id="add-quest-btn" class="add-quest-btn" (click)="openAddQuestModal($event)">
          + Add Quest
        </ion-button>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col *ngIf="quests.length === 0">
        <ion-card>
          <ion-card-header>
            <ion-card-title>No Quests</ion-card-title>
          </ion-card-header>
          <ion-card-content>
            Proste nemas ziadne quests
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
  </ion-grid>
  <div class="container">
    <div class="header-container">
      <div class="logo">
        <img src="assets/images/upshift_icon_mini.svg" alt="Upshift">
        <span>Upshift</span>
      </div>
      <div class="page-title">{{ headerText }}</div>
    </div>

    <div class="week-calendar">
      <div class="calendar-nav">
        <ion-button fill="clear" class="nav-arrow prev" (click)="changeWeek(-1)">←</ion-button>
        <div class="days">
          <div class="day-name" *ngFor="let day of ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su']">{{ day }}</div>
        </div>
        <ion-button fill="clear" class="nav-arrow next" (click)="changeWeek(1)">→</ion-button>
      </div>
      <div class="dates">
        <div *ngFor="let date of weekDates"
             class="date"
             [class.active]="date.is_today"
             [class.selected]="date.is_selected"
             [class.disabled]="date.is_future"
             (click)="!date.is_future && selectDate(date.date)">
          <svg class="date-progress" viewBox="0 0 36 36" *ngIf="date.total_quests > 0 && !date.is_future">
            <circle cx="18" cy="18" r="15.5"
                    [attr.stroke-dasharray]="date.completion_percentage + ', 100'"
                    [attr.data-date]="date.date"
                    class="progress-circle"
                    [class.low]="date.completion_percentage < 50"></circle>
          </svg>
          <span class="date-content">{{ date.day }}</span>
        </div>
      </div>
    </div>

    <section class="quests">
      <div class="section-header">
        <h2>Quests</h2>
        <ion-button fill="clear" id="add-quest-btn" class="add-quest-btn" (click)="openAddQuestModal($event)">
          + Add Quest
        </ion-button>
      </div>

      <!-- Debug info -->
      <div style="color: white; margin-bottom: 10px;">
        <p *ngIf="quests.length === 0">No quests found. Try adding a quest.</p>
      </div>

      <div class="quest-list">
        <div *ngFor="let quest of quests"
             class="quest-item"
             [class.completed]="quest.completed"
             [attr.data-quest-id]="quest.id"
             [attr.data-regular-quest]="true"
             (click)="toggleQuest(quest)">
          <div class="quest-icon">
            {{ quest.emoji }}
          </div>
          <div class="quest-info">
            <h3 class="quest-title">{{ quest.name }}</h3>
            <p class="quest-description">{{ quest.description }}</p>
            <div class="progress-container">
              <div class="progress-time" *ngIf="quest.goal_unit === 'time' || quest.goal_unit === 'min' || quest.goal_unit === 'hr' || quest.goal_unit === 'sec'">
                <ion-range
                  min="0"
                  [max]="quest.goal_value"
                  [(ngModel)]="quest.value_achieved"
                  class="progress-slider"
                  [attr.data-quest-id]="quest.id"
                  [attr.data-quest-type]="quest.quest_type"
                  [step]="1"
                  snaps="true"
                  ticks="false"
                  snaps-per-step="true"
                  (ionChange)="updateQuestProgress(quest, $event)"
                  (ionInput)="$event.target && updateSliderBackground($event.target)"
                  style="--progress-value: {{quest.value_achieved / quest.goal_value * 100}}%">
                </ion-range>
                <div class="progress-text">
                  {{ quest.value_achieved }}{{ quest.goal_unit === 'min' ? 'm' : quest.goal_unit === 'hr' ? 'h' : 's' }}/{{ quest.goal_value }}{{ quest.goal_unit === 'min' ? 'm' : quest.goal_unit === 'hr' ? 'h' : 's' }}
                </div>
              </div>
              <div class="progress" *ngIf="quest.goal_unit !== 'time' && quest.goal_unit !== 'min' && quest.goal_unit !== 'hr' && quest.goal_unit !== 'sec'">
                <ion-range
                  min="0"
                  [max]="quest.goal_value"
                  [(ngModel)]="quest.value_achieved"
                  class="progress-slider"
                  [attr.data-quest-id]="quest.id"
                  [attr.data-quest-type]="quest.quest_type"
                  [step]="1"
                  snaps="true"
                  ticks="false"
                  snaps-per-step="true"
                  (ionChange)="updateQuestProgress(quest, $event)"
                  (ionInput)="$event.target && updateSliderBackground($event.target)"
                  style="--progress-value: {{quest.value_achieved / quest.goal_value * 100}}%">
                </ion-range>
                <div class="progress-text">
                  {{ quest.value_achieved }}/{{ quest.goal_value }}
                  <ng-container *ngIf="quest.goal_unit !== 'count'">
                    {{ quest.goal_unit }}
                  </ng-container>
                </div>
              </div>
            </div>
          </div>
          <div class="quest-streak" *ngIf="isSameDay(selectedDate, getToday())">
            🔥{{ quest.streak }}d
          </div>
          <div class="quest-streak" *ngIf="!isSameDay(selectedDate, getToday())"></div>
        </div>
      </div>
    </section>

    <section class="daily-side-quest" *ngIf="showSidequests && isSameDay(selectedDate, getToday())">
      <h2>Daily Side Quest</h2>
      <div class="quest-list">
        <div *ngIf="dailyQuest && dailyQuest.current_quest"
             class="quest-item"
             [class.completed]="dailyQuest.completed"
             [attr.data-quest-id]="dailyQuest.id"
             [attr.data-side-quest]="true"
             (click)="toggleSideQuest(dailyQuest)">
          <div class="quest-icon">
            {{ dailyQuest.emoji }}
          </div>
          <div class="quest-info">
            <h3 class="quest-title">{{ dailyQuest.current_quest.name }}</h3>
            <p class="quest-description">{{ dailyQuest.current_quest.description }}</p>
            <div class="progress-container">
              <div class="progress-text">
                {{ dailyQuest.value_achieved }}/{{ dailyQuest.current_quest.goal_value }}
                <ng-container *ngIf="dailyQuest.current_quest.goal_unit !== 'count'">
                  {{ dailyQuest.current_quest.goal_unit }}
                </ng-container>
              </div>
            </div>
          </div>
          <div class="quest-streak" *ngIf="isSameDay(selectedDate, getToday())">
            🔥{{ dailyQuest.streak }}d
          </div>
          <div class="quest-streak" *ngIf="!isSameDay(selectedDate, getToday())"></div>
        </div>
        <div class="no-quest" *ngIf="!dailyQuest || !dailyQuest.current_quest">
          <p>No daily side quests are currently available. Please check back later or contact an administrator.</p>
        </div>
      </div>
    </section>
  </div>

  <!-- Add Quest Modal -->
  <ion-modal [isOpen]="showAddQuestModal" class="add-quest-modal" (ionModalDidDismiss)="closeAddQuestModal()" [backdropDismiss]="true">
    <ng-template>
      <div class="modal-header">
        <h2>Add New Quest</h2>
        <ion-button fill="clear" class="close-button" (click)="closeAddQuestModal()">
          <ion-icon name="close"></ion-icon>
        </ion-button>
      </div>
      <div class="modal-content">
        <form (ngSubmit)="createQuest()" #questForm="ngForm">
          <div class="quest-name-row">
            <div class="emoji-input">
              <ion-input type="text" id="emoji" name="emoji" [(ngModel)]="newQuest.emoji" value="🎯" appEmojiInput required></ion-input>
            </div>
            <div class="name-input">
              <ion-input type="text" id="name" name="name" [(ngModel)]="newQuest.name" placeholder="Enter quest name" required></ion-input>
            </div>
          </div>

          <div class="form-group">
            <label>Description</label>
            <ion-textarea id="description" name="description" [(ngModel)]="newQuest.description" placeholder="Enter quest description"></ion-textarea>
          </div>

          <div class="form-group">
            <label>Quest Type</label>
            <ion-select id="quest_type" name="quest_type" [(ngModel)]="newQuest.quest_type" interface="popover" required>
              <ion-select-option value="build">Build Habit</ion-select-option>
              <ion-select-option value="quit">Quit Habit</ion-select-option>
            </ion-select>
          </div>

          <div class="form-group">
            <label>Category</label>
            <ion-select id="category" name="category" [(ngModel)]="newQuest.category" required (ionChange)="checkCategoryPriority($event)" interface="popover">
              <ion-select-option value="">Select a category</ion-select-option>
              <ion-select-option value="strength">Strength</ion-select-option>
              <ion-select-option value="money">Money</ion-select-option>
              <ion-select-option value="health">Health</ion-select-option>
              <ion-select-option value="knowledge">Knowledge</ion-select-option>
            </ion-select>
          </div>

          <div class="form-group" *ngIf="newQuest.category">
            <label>Priority</label>
            <ion-select id="priority" name="priority" [(ngModel)]="newQuest.priority" [disabled]="hasHighPriorityQuest" interface="popover">
              <ion-select-option value="basic">Basic</ion-select-option>
              <ion-select-option value="high">High</ion-select-option>
            </ion-select>
            <div class="priority-warning" *ngIf="hasHighPriorityQuest">
              <small>You already have a high priority quest in this category.</small>
            </div>
          </div>

          <div class="form-group">
            <label>Goal</label>
            <div class="goal-inputs">
              <ion-input type="number" id="goal_value" name="goal_value" [(ngModel)]="newQuest.goal_value" value="1" min="1" required></ion-input>
              <ion-select id="goal_unit" name="goal_unit" [(ngModel)]="newQuest.goal_unit" interface="popover" required>
                <ion-select-option value="count">count</ion-select-option>
                <ion-select-option value="steps">steps</ion-select-option>
                <ion-select-option value="m">meters</ion-select-option>
                <ion-select-option value="km">kilometers</ion-select-option>
                <ion-select-option value="sec">seconds</ion-select-option>
                <ion-select-option value="min">minutes</ion-select-option>
                <ion-select-option value="hr">hours</ion-select-option>
                <ion-select-option value="Cal">calories</ion-select-option>
                <ion-select-option value="g">grams</ion-select-option>
                <ion-select-option value="mg">milligrams</ion-select-option>
                <ion-select-option value="l">liters</ion-select-option>
                <ion-select-option value="drink">drinks</ion-select-option>
                <ion-select-option value="pages">pages</ion-select-option>
                <ion-select-option value="books">books</ion-select-option>
                <ion-select-option value="%">percent</ion-select-option>
                <ion-select-option value="€">euros</ion-select-option>
                <ion-select-option value="$">dollars</ion-select-option>
                <ion-select-option value="£">pounds</ion-select-option>
              </ion-select>
            </div>
          </div>

          <div class="form-group">
            <label>Frequency</label>
            <ion-select id="goal_period" name="goal_period" [(ngModel)]="newQuest.goal_period" (ionChange)="updatePeriodDisplay()" interface="popover" required>
              <ion-select-option value="day">Every Day</ion-select-option>
              <ion-select-option value="week">Specific days of the week</ion-select-option>
              <ion-select-option value="month">Specific days of the month</ion-select-option>
            </ion-select>
          </div>

          <div class="form-group" *ngIf="newQuest.goal_period === 'week'">
            <label>Select Days of Week</label>
            <div class="days-selector">
              <div class="day-checkbox" *ngFor="let day of weekDays">
                <ion-checkbox [id]="'day-' + day.value.toLowerCase()"
                           name="days_of_week" [value]="day.value"
                           (ionChange)="updateDaysOfWeek($event, day.value)">
                </ion-checkbox>
                <label [for]="'day-' + day.value.toLowerCase()">{{ day.label }}</label>
              </div>
            </div>
          </div>

          <div class="form-group" *ngIf="newQuest.goal_period === 'month'">
            <label>Select Days of Month</label>
            <div class="month-days-selector">
              <div class="day-checkbox" *ngFor="let day of monthDays">
                <ion-checkbox [id]="'month-day-' + day"
                           name="days_of_month" [value]="day"
                           (ionChange)="updateDaysOfMonth($event, day)">
                </ion-checkbox>
                <label [for]="'month-day-' + day">{{ day }}</label>
              </div>
            </div>
          </div>

          <ion-button expand="block" type="submit" class="create-quest-btn">Create Quest</ion-button>
        </form>
      </div>
    </ng-template>
  </ion-modal>

  <!-- Celebration Modal -->
  <app-celebration
    *ngIf="showCelebration"
    [user]="currentUser"
    [date]="formatDate(selectedDate)"
    (close)="closeCelebration()">
  </app-celebration>
</ion-content>
