{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _CollectionDetailPage;\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { forkJoin } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/admin.service\";\nimport * as i3 from \"../../../services/supabase.service\";\nimport * as i4 from \"@ionic/angular\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nfunction CollectionDetailPage_div_15_div_9_input_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 38);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CollectionDetailPage_div_15_div_9_input_4_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const key_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newDocument[key_r4], $event) || (ctx_r1.newDocument[key_r4] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newDocument[key_r4]);\n  }\n}\nfunction CollectionDetailPage_div_15_div_9_input_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 39);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CollectionDetailPage_div_15_div_9_input_5_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const key_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newDocument[key_r4], $event) || (ctx_r1.newDocument[key_r4] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newDocument[key_r4]);\n  }\n}\nfunction CollectionDetailPage_div_15_div_9_input_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 40);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CollectionDetailPage_div_15_div_9_input_6_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const key_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newDocument[key_r4], $event) || (ctx_r1.newDocument[key_r4] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newDocument[key_r4]);\n  }\n}\nfunction CollectionDetailPage_div_15_div_9_input_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 41);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CollectionDetailPage_div_15_div_9_input_7_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const key_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newDocument[key_r4], $event) || (ctx_r1.newDocument[key_r4] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newDocument[key_r4]);\n  }\n}\nfunction CollectionDetailPage_div_15_div_9_input_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 42);\n    i0.ɵɵlistener(\"ngModelChange\", function CollectionDetailPage_div_15_div_9_input_8_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const key_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.handleDateChange(ctx_r1.newDocument, key_r4, $event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.formatDateForInput(ctx_r1.newDocument[key_r4]));\n  }\n}\nfunction CollectionDetailPage_div_15_div_9_textarea_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"textarea\", 43);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CollectionDetailPage_div_15_div_9_textarea_9_Template_textarea_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const key_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newDocument[key_r4], $event) || (ctx_r1.newDocument[key_r4] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newDocument[key_r4]);\n  }\n}\nfunction CollectionDetailPage_div_15_div_9_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"input\", 45);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CollectionDetailPage_div_15_div_9_div_10_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const key_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newDocument[key_r4], $event) || (ctx_r1.newDocument[key_r4] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 46);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const key_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newDocument[key_r4]);\n    i0.ɵɵproperty(\"id\", \"new-\" + key_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", \"new-\" + key_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.newDocument[key_r4] ? \"Yes\" : \"No\");\n  }\n}\nfunction CollectionDetailPage_div_15_div_9_select_11_option_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r12);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(option_r12);\n  }\n}\nfunction CollectionDetailPage_div_15_div_9_select_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"select\", 47);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CollectionDetailPage_div_15_div_9_select_11_Template_select_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const key_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newDocument[key_r4], $event) || (ctx_r1.newDocument[key_r4] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(1, \"option\", 48);\n    i0.ɵɵtext(2, \"-- Select --\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, CollectionDetailPage_div_15_div_9_select_11_option_3_Template, 2, 2, \"option\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newDocument[key_r4]);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getSelectOptions(key_r4));\n  }\n}\nfunction CollectionDetailPage_div_15_div_9_select_12_option_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r14.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r14.display, \" \");\n  }\n}\nfunction CollectionDetailPage_div_15_div_9_select_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"select\", 47);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CollectionDetailPage_div_15_div_9_select_12_Template_select_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const key_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newDocument[key_r4], $event) || (ctx_r1.newDocument[key_r4] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(1, \"option\", 48);\n    i0.ɵɵtext(2, \"-- Select --\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, CollectionDetailPage_div_15_div_9_select_12_option_3_Template, 2, 2, \"option\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newDocument[key_r4]);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getForeignKeyOptions(key_r4));\n  }\n}\nfunction CollectionDetailPage_div_15_div_9_input_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 51);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CollectionDetailPage_div_15_div_9_input_13_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const key_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newDocument[key_r4], $event) || (ctx_r1.newDocument[key_r4] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newDocument[key_r4]);\n  }\n}\nfunction CollectionDetailPage_div_15_div_9_div_14_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵelement(1, \"img\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r4 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.newDocument[key_r4], i0.ɵɵsanitizeUrl);\n  }\n}\nfunction CollectionDetailPage_div_15_div_9_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"input\", 53);\n    i0.ɵɵlistener(\"change\", function CollectionDetailPage_div_15_div_9_div_14_Template_input_change_1_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const key_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.handleFileUpload($event, ctx_r1.newDocument, key_r4));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, CollectionDetailPage_div_15_div_9_div_14_div_2_Template, 2, 1, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.newDocument[key_r4]);\n  }\n}\nfunction CollectionDetailPage_div_15_div_9_input_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 38);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CollectionDetailPage_div_15_div_9_input_15_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const key_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newDocument[key_r4], $event) || (ctx_r1.newDocument[key_r4] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newDocument[key_r4]);\n  }\n}\nfunction CollectionDetailPage_div_15_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"label\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerStart(3, 26);\n    i0.ɵɵtemplate(4, CollectionDetailPage_div_15_div_9_input_4_Template, 1, 1, \"input\", 27)(5, CollectionDetailPage_div_15_div_9_input_5_Template, 1, 1, \"input\", 28)(6, CollectionDetailPage_div_15_div_9_input_6_Template, 1, 1, \"input\", 29)(7, CollectionDetailPage_div_15_div_9_input_7_Template, 1, 1, \"input\", 30)(8, CollectionDetailPage_div_15_div_9_input_8_Template, 1, 1, \"input\", 31)(9, CollectionDetailPage_div_15_div_9_textarea_9_Template, 1, 1, \"textarea\", 32)(10, CollectionDetailPage_div_15_div_9_div_10_Template, 4, 4, \"div\", 33)(11, CollectionDetailPage_div_15_div_9_select_11_Template, 4, 2, \"select\", 34)(12, CollectionDetailPage_div_15_div_9_select_12_Template, 4, 2, \"select\", 34)(13, CollectionDetailPage_div_15_div_9_input_13_Template, 1, 1, \"input\", 35)(14, CollectionDetailPage_div_15_div_9_div_14_Template, 3, 1, \"div\", 36)(15, CollectionDetailPage_div_15_div_9_input_15_Template, 1, 1, \"input\", 37);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(key_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitch\", ctx_r1.getFieldType(key_r4));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"text\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"email\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"password\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"number\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"date\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"textarea\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"boolean\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"select\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"foreign-key\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"color\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"file\");\n  }\n}\nfunction CollectionDetailPage_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"h2\");\n    i0.ɵɵtext(2, \"Add New Document\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 17)(4, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function CollectionDetailPage_div_15_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openJsonEditor());\n    });\n    i0.ɵɵtext(5, \"Edit as JSON\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function CollectionDetailPage_div_15_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addDocument());\n    });\n    i0.ɵɵtext(7, \"Save Document\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 20);\n    i0.ɵɵtemplate(9, CollectionDetailPage_div_15_div_9_Template, 16, 13, \"div\", 21);\n    i0.ɵɵelementStart(10, \"div\", 22);\n    i0.ɵɵelement(11, \"input\", 23, 0);\n    i0.ɵɵelementStart(13, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function CollectionDetailPage_div_15_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const newFieldName_r18 = i0.ɵɵreference(12);\n      const ctx_r1 = i0.ɵɵnextContext();\n      ctx_r1.newDocument[newFieldName_r18.value] = \"\";\n      return i0.ɵɵresetView(newFieldName_r18.value = \"\");\n    });\n    i0.ɵɵtext(14, \"Add Field\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getObjectKeys(ctx_r1.newDocument));\n  }\n}\nfunction CollectionDetailPage_div_16_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.jsonError);\n  }\n}\nfunction CollectionDetailPage_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"div\", 58)(2, \"h2\");\n    i0.ɵɵtext(3, \"Edit as JSON\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CollectionDetailPage_div_16_div_4_Template, 2, 1, \"div\", 59);\n    i0.ɵɵelementStart(5, \"textarea\", 60);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CollectionDetailPage_div_16_Template_textarea_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.jsonEditorContent, $event) || (ctx_r1.jsonEditorContent = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 61)(7, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function CollectionDetailPage_div_16_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.cancelJsonEdit());\n    });\n    i0.ɵɵtext(8, \"Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function CollectionDetailPage_div_16_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.applyJsonChanges());\n    });\n    i0.ɵɵtext(10, \"Apply Changes\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.jsonError);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.jsonEditorContent);\n  }\n}\nfunction CollectionDetailPage_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵelement(1, \"ion-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading documents...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CollectionDetailPage_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"p\");\n    i0.ɵɵtext(2, \"No documents found in this collection.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CollectionDetailPage_div_20_div_1_div_1_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79)(1, \"span\", 80);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"span\", 81);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r22 = ctx.$implicit;\n    const document_r21 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"hidden\", key_r22 === \"id\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", key_r22, \":\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.formatDocumentFieldValue(document_r21, key_r22), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction CollectionDetailPage_div_20_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"div\", 73)(2, \"h3\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 74)(5, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function CollectionDetailPage_div_20_div_1_div_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const document_r21 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.editDocument(document_r21));\n    });\n    i0.ɵɵtext(6, \"Edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function CollectionDetailPage_div_20_div_1_div_1_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const document_r21 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.deleteDocument(document_r21.id));\n    });\n    i0.ɵɵtext(8, \"Delete\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 77);\n    i0.ɵɵtemplate(10, CollectionDetailPage_div_20_div_1_div_1_div_10_Template, 4, 3, \"div\", 78);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const document_r21 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"ID: \", document_r21.id, \"\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getObjectKeys(document_r21));\n  }\n}\nfunction CollectionDetailPage_div_20_div_1_div_2_div_11_input_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 38);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CollectionDetailPage_div_20_div_1_div_2_div_11_input_4_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const key_r25 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.editingDocument[key_r25], $event) || (ctx_r1.editingDocument[key_r25] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r25 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.editingDocument[key_r25]);\n  }\n}\nfunction CollectionDetailPage_div_20_div_1_div_2_div_11_input_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 39);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CollectionDetailPage_div_20_div_1_div_2_div_11_input_5_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const key_r25 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.editingDocument[key_r25], $event) || (ctx_r1.editingDocument[key_r25] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r25 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.editingDocument[key_r25]);\n  }\n}\nfunction CollectionDetailPage_div_20_div_1_div_2_div_11_input_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 40);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CollectionDetailPage_div_20_div_1_div_2_div_11_input_6_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const key_r25 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.editingDocument[key_r25], $event) || (ctx_r1.editingDocument[key_r25] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r25 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.editingDocument[key_r25]);\n  }\n}\nfunction CollectionDetailPage_div_20_div_1_div_2_div_11_input_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 41);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CollectionDetailPage_div_20_div_1_div_2_div_11_input_7_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const key_r25 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.editingDocument[key_r25], $event) || (ctx_r1.editingDocument[key_r25] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r25 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.editingDocument[key_r25]);\n  }\n}\nfunction CollectionDetailPage_div_20_div_1_div_2_div_11_input_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 42);\n    i0.ɵɵlistener(\"ngModelChange\", function CollectionDetailPage_div_20_div_1_div_2_div_11_input_8_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const key_r25 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.handleDateChange(ctx_r1.editingDocument, key_r25, $event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r25 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.formatDateForInput(ctx_r1.editingDocument[key_r25]));\n  }\n}\nfunction CollectionDetailPage_div_20_div_1_div_2_div_11_textarea_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"textarea\", 43);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CollectionDetailPage_div_20_div_1_div_2_div_11_textarea_9_Template_textarea_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const key_r25 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.editingDocument[key_r25], $event) || (ctx_r1.editingDocument[key_r25] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r25 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.editingDocument[key_r25]);\n  }\n}\nfunction CollectionDetailPage_div_20_div_1_div_2_div_11_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"input\", 45);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CollectionDetailPage_div_20_div_1_div_2_div_11_div_10_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const key_r25 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.editingDocument[key_r25], $event) || (ctx_r1.editingDocument[key_r25] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 46);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const key_r25 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.editingDocument[key_r25]);\n    i0.ɵɵproperty(\"id\", \"edit-\" + key_r25);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", \"edit-\" + key_r25);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.editingDocument[key_r25] ? \"Yes\" : \"No\");\n  }\n}\nfunction CollectionDetailPage_div_20_div_1_div_2_div_11_select_11_option_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r33 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r33);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(option_r33);\n  }\n}\nfunction CollectionDetailPage_div_20_div_1_div_2_div_11_select_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"select\", 47);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CollectionDetailPage_div_20_div_1_div_2_div_11_select_11_Template_select_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const key_r25 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.editingDocument[key_r25], $event) || (ctx_r1.editingDocument[key_r25] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(1, \"option\", 48);\n    i0.ɵɵtext(2, \"-- Select --\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, CollectionDetailPage_div_20_div_1_div_2_div_11_select_11_option_3_Template, 2, 2, \"option\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r25 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.editingDocument[key_r25]);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getSelectOptions(key_r25));\n  }\n}\nfunction CollectionDetailPage_div_20_div_1_div_2_div_11_select_12_option_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r35 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r35.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r35.display, \" \");\n  }\n}\nfunction CollectionDetailPage_div_20_div_1_div_2_div_11_select_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"select\", 47);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CollectionDetailPage_div_20_div_1_div_2_div_11_select_12_Template_select_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const key_r25 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.editingDocument[key_r25], $event) || (ctx_r1.editingDocument[key_r25] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(1, \"option\", 48);\n    i0.ɵɵtext(2, \"-- Select --\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, CollectionDetailPage_div_20_div_1_div_2_div_11_select_12_option_3_Template, 2, 2, \"option\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r25 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.editingDocument[key_r25]);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getForeignKeyOptions(key_r25));\n  }\n}\nfunction CollectionDetailPage_div_20_div_1_div_2_div_11_input_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 51);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CollectionDetailPage_div_20_div_1_div_2_div_11_input_13_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r36);\n      const key_r25 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.editingDocument[key_r25], $event) || (ctx_r1.editingDocument[key_r25] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r25 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.editingDocument[key_r25]);\n  }\n}\nfunction CollectionDetailPage_div_20_div_1_div_2_div_11_div_14_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵelement(1, \"img\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r25 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.editingDocument[key_r25], i0.ɵɵsanitizeUrl);\n  }\n}\nfunction CollectionDetailPage_div_20_div_1_div_2_div_11_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"input\", 53);\n    i0.ɵɵlistener(\"change\", function CollectionDetailPage_div_20_div_1_div_2_div_11_div_14_Template_input_change_1_listener($event) {\n      i0.ɵɵrestoreView(_r37);\n      const key_r25 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.handleFileUpload($event, ctx_r1.editingDocument, key_r25));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, CollectionDetailPage_div_20_div_1_div_2_div_11_div_14_div_2_Template, 2, 1, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r25 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.editingDocument[key_r25]);\n  }\n}\nfunction CollectionDetailPage_div_20_div_1_div_2_div_11_input_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r38 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 38);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CollectionDetailPage_div_20_div_1_div_2_div_11_input_15_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r38);\n      const key_r25 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.editingDocument[key_r25], $event) || (ctx_r1.editingDocument[key_r25] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r25 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.editingDocument[key_r25]);\n  }\n}\nfunction CollectionDetailPage_div_20_div_1_div_2_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84)(1, \"label\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerStart(3, 26);\n    i0.ɵɵtemplate(4, CollectionDetailPage_div_20_div_1_div_2_div_11_input_4_Template, 1, 1, \"input\", 27)(5, CollectionDetailPage_div_20_div_1_div_2_div_11_input_5_Template, 1, 1, \"input\", 28)(6, CollectionDetailPage_div_20_div_1_div_2_div_11_input_6_Template, 1, 1, \"input\", 29)(7, CollectionDetailPage_div_20_div_1_div_2_div_11_input_7_Template, 1, 1, \"input\", 30)(8, CollectionDetailPage_div_20_div_1_div_2_div_11_input_8_Template, 1, 1, \"input\", 31)(9, CollectionDetailPage_div_20_div_1_div_2_div_11_textarea_9_Template, 1, 1, \"textarea\", 32)(10, CollectionDetailPage_div_20_div_1_div_2_div_11_div_10_Template, 4, 4, \"div\", 33)(11, CollectionDetailPage_div_20_div_1_div_2_div_11_select_11_Template, 4, 2, \"select\", 34)(12, CollectionDetailPage_div_20_div_1_div_2_div_11_select_12_Template, 4, 2, \"select\", 34)(13, CollectionDetailPage_div_20_div_1_div_2_div_11_input_13_Template, 1, 1, \"input\", 35)(14, CollectionDetailPage_div_20_div_1_div_2_div_11_div_14_Template, 3, 1, \"div\", 36)(15, CollectionDetailPage_div_20_div_1_div_2_div_11_input_15_Template, 1, 1, \"input\", 37);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const key_r25 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"hidden\", key_r25 === \"id\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(key_r25);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitch\", ctx_r1.getFieldType(key_r25));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"text\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"email\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"password\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"number\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"date\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"textarea\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"boolean\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"select\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"foreign-key\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"color\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"file\");\n  }\n}\nfunction CollectionDetailPage_div_20_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 82)(1, \"h3\");\n    i0.ɵɵtext(2, \"Editing Document\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 17)(4, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function CollectionDetailPage_div_20_div_1_div_2_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const document_r21 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openJsonEditor(document_r21));\n    });\n    i0.ɵɵtext(5, \"Edit as JSON\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function CollectionDetailPage_div_20_div_1_div_2_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.cancelEdit());\n    });\n    i0.ɵɵtext(7, \"Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function CollectionDetailPage_div_20_div_1_div_2_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.updateDocument());\n    });\n    i0.ɵɵtext(9, \"Save Changes\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 20);\n    i0.ɵɵtemplate(11, CollectionDetailPage_div_20_div_1_div_2_div_11_Template, 16, 14, \"div\", 83);\n    i0.ɵɵelementStart(12, \"div\", 22);\n    i0.ɵɵelement(13, \"input\", 23, 1);\n    i0.ɵɵelementStart(15, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function CollectionDetailPage_div_20_div_1_div_2_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const newEditFieldName_r39 = i0.ɵɵreference(14);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.editingDocument[newEditFieldName_r39.value] = \"\";\n      return i0.ɵɵresetView(newEditFieldName_r39.value = \"\");\n    });\n    i0.ɵɵtext(16, \"Add Field\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getObjectKeys(ctx_r1.editingDocument));\n  }\n}\nfunction CollectionDetailPage_div_20_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵtemplate(1, CollectionDetailPage_div_20_div_1_div_1_Template, 11, 2, \"div\", 70)(2, CollectionDetailPage_div_20_div_1_div_2_Template, 17, 1, \"div\", 71);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const document_r21 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.editingDocument == null ? null : ctx_r1.editingDocument.id) !== document_r21.id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.editingDocument == null ? null : ctx_r1.editingDocument.id) === document_r21.id);\n  }\n}\nfunction CollectionDetailPage_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵtemplate(1, CollectionDetailPage_div_20_div_1_Template, 3, 2, \"div\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.documents);\n  }\n}\nexport class CollectionDetailPage {\n  constructor(route, router, adminService, supabaseService) {\n    this.route = route;\n    this.router = router;\n    this.adminService = adminService;\n    this.supabaseService = supabaseService;\n    this.collectionName = '';\n    this.documents = [];\n    this.isLoading = true;\n    this.showAddForm = false;\n    this.newDocument = {};\n    this.editingDocument = null;\n    this.showJsonEditor = false;\n    this.jsonEditorContent = '';\n    this.jsonError = '';\n    this.foreignKeyOptions = {};\n    this.selectedFiles = {};\n    this.imagePreviewUrls = {};\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      this.collectionName = params.get('name') || '';\n      if (this.collectionName) {\n        this.loadDocuments();\n      } else {\n        this.router.navigate(['/admin']);\n      }\n    });\n  }\n  loadDocuments() {\n    this.isLoading = true;\n    this.adminService.getCollection(this.collectionName).subscribe({\n      next: documents => {\n        this.documents = documents;\n        this.isLoading = false;\n      },\n      error: error => {\n        this.isLoading = false;\n      }\n    });\n  }\n  toggleAddForm() {\n    this.showAddForm = !this.showAddForm;\n    if (this.showAddForm) {\n      this.newDocument = this.adminService.getModelTemplate(this.collectionName);\n      this.loadForeignKeyOptions();\n    }\n  }\n  loadForeignKeyOptions() {\n    const fieldTypes = this.adminService.getFieldTypes(this.collectionName);\n    const foreignKeyFields = Object.keys(fieldTypes).filter(fieldName => fieldTypes[fieldName] === 'foreign-key');\n    if (foreignKeyFields.length === 0) {\n      return;\n    }\n    const observables = foreignKeyFields.map(fieldName => {\n      return this.adminService.getForeignKeyOptions(fieldName).pipe(map(options => ({\n        fieldName,\n        options\n      })));\n    });\n    forkJoin(observables).subscribe({\n      next: results => {\n        results.forEach(result => {\n          this.foreignKeyOptions[result.fieldName] = result.options;\n        });\n      },\n      error: error => {}\n    });\n  }\n  handleFileUpload(event, document, fieldName) {\n    const input = event.target;\n    if (input.files && input.files.length > 0) {\n      const file = input.files[0];\n      if (this.collectionName === 'affiliate_offers' && fieldName === 'image_cover') {\n        const validTypes = ['image/jpeg', 'image/png', 'image/webp'];\n        if (!validTypes.includes(file.type)) {\n          alert('Invalid file type. Please upload a JPG, PNG, or WebP image.');\n          return;\n        }\n        if (file.size > 10 * 1024 * 1024) {\n          alert('File size too large. Maximum size is 10MB.');\n          return;\n        }\n        this.selectedFiles[`${document.id || 'new'}_${fieldName}`] = file;\n        const reader = new FileReader();\n        reader.onload = e => {\n          var _e$target;\n          const dataUrl = (_e$target = e.target) === null || _e$target === void 0 ? void 0 : _e$target.result;\n          this.imagePreviewUrls[`${document.id || 'new'}_${fieldName}`] = dataUrl;\n          document[fieldName] = dataUrl;\n        };\n        reader.readAsDataURL(file);\n      } else {\n        document[fieldName] = file;\n      }\n    }\n  }\n  addDocument() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.isLoading = true;\n      try {\n        const processedDocument = _this.handleDateConversion(_this.newDocument);\n        const fileKey = `new_image_cover`;\n        if (_this.selectedFiles[fileKey] && _this.collectionName === 'affiliate_offers') {\n          const file = _this.selectedFiles[fileKey];\n          const {\n            data: userData,\n            error: userError\n          } = yield _this.supabaseService.getClient().from('profiles').select('username').eq('id', _this.supabaseService.getCurrentUserId()).single();\n          if (userError) {\n            throw new Error(`Error checking admin status: ${userError.message}`);\n          }\n          if ((userData === null || userData === void 0 ? void 0 : userData.username) !== 'admin') {\n            throw new Error('Only admin users can upload files to this bucket.');\n          }\n          const fileName = `${Date.now()}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;\n          const formData = new FormData();\n          formData.append('file', file);\n          const {\n            data: {\n              session\n            }\n          } = yield _this.supabaseService.getClient().auth.getSession();\n          const accessToken = session === null || session === void 0 ? void 0 : session.access_token;\n          if (!accessToken) {\n            throw new Error('No access token available');\n          }\n          const uploadPromise = new Promise((resolve, reject) => {\n            const xhr = new XMLHttpRequest();\n            const queryParams = new URLSearchParams({\n              'cacheControl': '3600',\n              'upsert': 'true'\n            });\n            xhr.open('POST', `${_this.supabaseService.storage.url}/storage/v1/object/affiliate-offer/${fileName}?${queryParams.toString()}`);\n            xhr.setRequestHeader('Authorization', `Bearer ${accessToken}`);\n            xhr.setRequestHeader('apikey', _this.supabaseService.supabaseKey);\n            xhr.onload = () => {\n              if (xhr.status >= 200 && xhr.status < 300) {\n                const {\n                  data: urlData\n                } = _this.supabaseService.getClient().storage.from('affiliate-offer').getPublicUrl(fileName);\n                resolve({\n                  publicUrl: urlData.publicUrl\n                });\n              } else {\n                reject(new Error(`Upload failed: ${xhr.statusText}`));\n              }\n            };\n            xhr.onerror = () => {\n              reject(new Error('Network error during upload'));\n            };\n            xhr.send(formData);\n          });\n          const {\n            publicUrl\n          } = yield uploadPromise;\n          processedDocument.image_cover = fileName;\n        }\n        _this.adminService.addDocument(_this.collectionName, processedDocument).subscribe({\n          next: () => {\n            _this.showAddForm = false;\n            _this.newDocument = {};\n            _this.selectedFiles = {};\n            _this.imagePreviewUrls = {};\n            _this.loadDocuments();\n          },\n          error: error => {\n            _this.isLoading = false;\n          }\n        });\n      } catch (err) {\n        const error = err;\n        alert(`Error: ${error.message || JSON.stringify(error)}`);\n        _this.isLoading = false;\n      }\n    })();\n  }\n  editDocument(document) {\n    this.editingDocument = {\n      ...document\n    };\n    if (Object.keys(this.foreignKeyOptions).length === 0) {\n      this.loadForeignKeyOptions();\n    }\n  }\n  getForeignKeyOptions(fieldName) {\n    return this.foreignKeyOptions[fieldName] || [];\n  }\n  isForeignKey(fieldName) {\n    return this.getFieldType(fieldName) === 'foreign-key';\n  }\n  updateDocument() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.editingDocument) return;\n      _this2.isLoading = true;\n      try {\n        const docId = _this2.editingDocument.id;\n        const docData = {\n          ..._this2.editingDocument\n        };\n        delete docData.id;\n        const processedData = _this2.handleDateConversion(docData);\n        const fileKey = `${docId}_image_cover`;\n        if (_this2.selectedFiles[fileKey] && _this2.collectionName === 'affiliate_offers') {\n          const file = _this2.selectedFiles[fileKey];\n          const {\n            data: userData,\n            error: userError\n          } = yield _this2.supabaseService.getClient().from('profiles').select('username').eq('id', _this2.supabaseService.getCurrentUserId()).single();\n          if (userError) {\n            throw new Error(`Error checking admin status: ${userError.message}`);\n          }\n          if ((userData === null || userData === void 0 ? void 0 : userData.username) !== 'admin') {\n            throw new Error('Only admin users can upload files to this bucket.');\n          }\n          const fileName = `${Date.now()}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;\n          const formData = new FormData();\n          formData.append('file', file);\n          const {\n            data: {\n              session\n            }\n          } = yield _this2.supabaseService.getClient().auth.getSession();\n          const accessToken = session === null || session === void 0 ? void 0 : session.access_token;\n          if (!accessToken) {\n            throw new Error('No access token available');\n          }\n          const uploadPromise = new Promise((resolve, reject) => {\n            const xhr = new XMLHttpRequest();\n            const queryParams = new URLSearchParams({\n              'cacheControl': '3600',\n              'upsert': 'true'\n            });\n            xhr.open('POST', `${_this2.supabaseService.storage.url}/storage/v1/object/affiliate-offer/${fileName}?${queryParams.toString()}`);\n            xhr.setRequestHeader('Authorization', `Bearer ${accessToken}`);\n            xhr.setRequestHeader('apikey', _this2.supabaseService.supabaseKey);\n            xhr.onload = () => {\n              if (xhr.status >= 200 && xhr.status < 300) {\n                const {\n                  data: urlData\n                } = _this2.supabaseService.getClient().storage.from('affiliate-offer').getPublicUrl(fileName);\n                resolve({\n                  publicUrl: urlData.publicUrl\n                });\n              } else {\n                reject(new Error(`Upload failed: ${xhr.statusText}`));\n              }\n            };\n            xhr.onerror = () => {\n              reject(new Error('Network error during upload'));\n            };\n            xhr.send(formData);\n          });\n          const {\n            publicUrl\n          } = yield uploadPromise;\n          processedData.image_cover = fileName;\n        }\n        _this2.adminService.updateDocument(_this2.collectionName, docId, processedData).subscribe({\n          next: () => {\n            _this2.editingDocument = null;\n            _this2.selectedFiles = {};\n            _this2.imagePreviewUrls = {};\n            _this2.loadDocuments();\n          },\n          error: error => {\n            _this2.isLoading = false;\n          }\n        });\n      } catch (err) {\n        const error = err;\n        alert(`Error: ${error.message || JSON.stringify(error)}`);\n        _this2.isLoading = false;\n      }\n    })();\n  }\n  fileToBase64(file) {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => resolve(reader.result);\n      reader.onerror = error => reject(error);\n    });\n  }\n  formatDocumentFieldValue(document, key) {\n    if (this.collectionName === 'affiliate_offers' && key === 'image_cover' && document[key]) {\n      try {\n        if (!document[key].includes('://')) {\n          const {\n            data: urlData\n          } = this.supabaseService.getClient().storage.from('affiliate-offer').getPublicUrl(document[key]);\n          return `<img src=\"${urlData.publicUrl}\" alt=\"Image\" style=\"max-width: 100px; max-height: 100px;\">`;\n        } else {\n          return `<img src=\"${document[key]}\" alt=\"Image\" style=\"max-width: 100px; max-height: 100px;\">`;\n        }\n      } catch (err) {\n        const error = err;\n        return `Image: ${document[key]} (Error loading preview)`;\n      }\n    }\n    return this.formatValue(document[key]);\n  }\n  deleteDocument(docId) {\n    if (confirm('Are you sure you want to delete this document? This action cannot be undone.')) {\n      this.isLoading = true;\n      this.adminService.deleteDocument(this.collectionName, docId).subscribe({\n        next: () => {\n          this.loadDocuments();\n        },\n        error: error => {\n          this.isLoading = false;\n        }\n      });\n    }\n  }\n  cancelEdit() {\n    this.editingDocument = null;\n  }\n  openJsonEditor(document) {\n    if (document) {\n      const docCopy = {\n        ...document\n      };\n      delete docCopy.id;\n      this.jsonEditorContent = JSON.stringify(docCopy, null, 2);\n      this.editingDocument = document;\n    } else {\n      this.jsonEditorContent = JSON.stringify(this.newDocument || {}, null, 2);\n    }\n    this.showJsonEditor = true;\n    this.jsonError = '';\n  }\n  applyJsonChanges() {\n    try {\n      const parsedJson = JSON.parse(this.jsonEditorContent);\n      if (this.editingDocument) {\n        this.editingDocument = {\n          id: this.editingDocument.id,\n          ...parsedJson\n        };\n      } else {\n        this.newDocument = parsedJson;\n      }\n      this.showJsonEditor = false;\n      this.jsonError = '';\n    } catch (e) {\n      this.jsonError = 'Invalid JSON format';\n    }\n  }\n  cancelJsonEdit() {\n    this.showJsonEditor = false;\n    this.jsonError = '';\n  }\n  goBack() {\n    this.router.navigate(['/admin']);\n  }\n  getObjectKeys(obj) {\n    return Object.keys(obj || {});\n  }\n  isObject(value) {\n    return typeof value === 'object' && value !== null && !Array.isArray(value);\n  }\n  formatValue(value) {\n    if (value === null || value === undefined) {\n      return 'null';\n    }\n    if (typeof value === 'object') {\n      if (value instanceof Date) {\n        return value.toLocaleString();\n      }\n      return JSON.stringify(value);\n    }\n    return String(value);\n  }\n  getFieldType(fieldName) {\n    if (this.collectionName === 'affiliate_offers' && fieldName === 'image_cover') {\n      return 'file';\n    }\n    const fieldTypes = this.adminService.getFieldTypes(this.collectionName);\n    return fieldTypes[fieldName] || 'text';\n  }\n  getSelectOptions(fieldName) {\n    return this.adminService.getSelectOptions(this.collectionName, fieldName);\n  }\n  formatDateForInput(value) {\n    if (!value) return '';\n    let date;\n    if (value instanceof Date) {\n      date = value;\n    } else if (typeof value === 'object' && value.seconds) {\n      date = new Date(value.seconds * 1000);\n    } else if (typeof value === 'string') {\n      try {\n        date = new Date(value);\n        if (isNaN(date.getTime())) {\n          return value;\n        }\n      } catch (e) {\n        return value;\n      }\n    } else {\n      return String(value);\n    }\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n  handleDateChange(obj, key, event) {\n    if (event) {\n      obj[key] = new Date(event);\n    } else {\n      obj[key] = null;\n    }\n  }\n  handleDateConversion(obj) {\n    if (!obj) return obj;\n    const result = {\n      ...obj\n    };\n    for (const key in result) {\n      const value = result[key];\n      if (typeof value === 'string' && this.getFieldType(key) === 'date') {\n        try {\n          result[key] = new Date(value);\n        } catch (e) {}\n      }\n    }\n    return result;\n  }\n}\n_CollectionDetailPage = CollectionDetailPage;\n_CollectionDetailPage.ɵfac = function CollectionDetailPage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CollectionDetailPage)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AdminService), i0.ɵɵdirectiveInject(i3.SupabaseService));\n};\n_CollectionDetailPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _CollectionDetailPage,\n  selectors: [[\"app-collection-detail\"]],\n  decls: 21,\n  vars: 7,\n  consts: [[\"newFieldName\", \"\"], [\"newEditFieldName\", \"\"], [1, \"container\"], [1, \"logo\"], [\"src\", \"assets/images/upshift_icon_mini.svg\", \"alt\", \"Upshift\"], [1, \"header-content\"], [1, \"back-button\", 3, \"click\"], [1, \"admin-content\"], [1, \"collection-actions\"], [1, \"add-button\", 3, \"click\"], [\"class\", \"document-form\", 4, \"ngIf\"], [\"class\", \"json-editor-modal\", 4, \"ngIf\"], [1, \"documents-list\"], [\"class\", \"loading\", 4, \"ngIf\"], [\"class\", \"no-data\", 4, \"ngIf\"], [\"class\", \"documents-table\", 4, \"ngIf\"], [1, \"document-form\"], [1, \"form-actions\"], [1, \"json-button\", 3, \"click\"], [1, \"save-button\", 3, \"click\"], [1, \"form-fields\"], [\"class\", \"form-field\", 4, \"ngFor\", \"ngForOf\"], [1, \"add-field\"], [\"type\", \"text\", \"placeholder\", \"Field name\"], [3, \"click\"], [1, \"form-field\"], [3, \"ngSwitch\"], [\"type\", \"text\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngSwitchCase\"], [\"type\", \"email\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngSwitchCase\"], [\"type\", \"password\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngSwitchCase\"], [\"type\", \"number\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngSwitchCase\"], [\"type\", \"date\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngSwitchCase\"], [\"rows\", \"3\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngSwitchCase\"], [\"class\", \"checkbox-field\", 4, \"ngSwitchCase\"], [3, \"ngModel\", \"ngModelChange\", 4, \"ngSwitchCase\"], [\"type\", \"color\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngSwitchCase\"], [\"class\", \"file-input-container\", 4, \"ngSwitchCase\"], [\"type\", \"text\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngSwitchDefault\"], [\"type\", \"text\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"email\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"password\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"date\", 3, \"ngModelChange\", \"ngModel\"], [\"rows\", \"3\", 3, \"ngModelChange\", \"ngModel\"], [1, \"checkbox-field\"], [\"type\", \"checkbox\", 3, \"ngModelChange\", \"ngModel\", \"id\"], [3, \"for\"], [3, \"ngModelChange\", \"ngModel\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [\"type\", \"color\", 3, \"ngModelChange\", \"ngModel\"], [1, \"file-input-container\"], [\"type\", \"file\", \"accept\", \"image/*\", 3, \"change\"], [\"class\", \"file-preview\", 4, \"ngIf\"], [1, \"file-preview\"], [\"alt\", \"Preview\", 3, \"src\"], [1, \"json-editor-modal\"], [1, \"json-editor-content\"], [\"class\", \"json-error\", 4, \"ngIf\"], [\"rows\", \"20\", 3, \"ngModelChange\", \"ngModel\"], [1, \"json-actions\"], [1, \"cancel-button\", 3, \"click\"], [1, \"apply-button\", 3, \"click\"], [1, \"json-error\"], [1, \"loading\"], [1, \"no-data\"], [1, \"documents-table\"], [\"class\", \"document-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"document-item\"], [\"class\", \"document-view\", 4, \"ngIf\"], [\"class\", \"document-edit\", 4, \"ngIf\"], [1, \"document-view\"], [1, \"document-header\"], [1, \"document-actions\"], [1, \"edit-button\", 3, \"click\"], [1, \"delete-button\", 3, \"click\"], [1, \"document-fields\"], [\"class\", \"document-field\", 3, \"hidden\", 4, \"ngFor\", \"ngForOf\"], [1, \"document-field\", 3, \"hidden\"], [1, \"field-name\"], [1, \"field-value\", 3, \"innerHTML\"], [1, \"document-edit\"], [\"class\", \"form-field\", 3, \"hidden\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-field\", 3, \"hidden\"]],\n  template: function CollectionDetailPage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 2)(1, \"header\")(2, \"div\", 3);\n      i0.ɵɵelement(3, \"img\", 4);\n      i0.ɵɵelementStart(4, \"span\");\n      i0.ɵɵtext(5, \"Upshift\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(6, \"div\", 5)(7, \"button\", 6);\n      i0.ɵɵlistener(\"click\", function CollectionDetailPage_Template_button_click_7_listener() {\n        return ctx.goBack();\n      });\n      i0.ɵɵtext(8, \"\\u2190 Back\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(9, \"h1\");\n      i0.ɵɵtext(10);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(11, \"div\", 7)(12, \"div\", 8)(13, \"button\", 9);\n      i0.ɵɵlistener(\"click\", function CollectionDetailPage_Template_button_click_13_listener() {\n        return ctx.toggleAddForm();\n      });\n      i0.ɵɵtext(14);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(15, CollectionDetailPage_div_15_Template, 15, 1, \"div\", 10)(16, CollectionDetailPage_div_16_Template, 11, 2, \"div\", 11);\n      i0.ɵɵelementStart(17, \"div\", 12);\n      i0.ɵɵtemplate(18, CollectionDetailPage_div_18_Template, 4, 0, \"div\", 13)(19, CollectionDetailPage_div_19_Template, 3, 0, \"div\", 14)(20, CollectionDetailPage_div_20_Template, 2, 1, \"div\", 15);\n      i0.ɵɵelementEnd()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(10);\n      i0.ɵɵtextInterpolate(ctx.collectionName);\n      i0.ɵɵadvance(4);\n      i0.ɵɵtextInterpolate1(\" \", ctx.showAddForm ? \"Cancel\" : \"Add Document\", \" \");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.showAddForm);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.showJsonEditor);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.documents.length === 0);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.documents.length > 0);\n    }\n  },\n  dependencies: [IonicModule, i4.IonSpinner, CommonModule, i5.NgForOf, i5.NgIf, i5.NgSwitch, i5.NgSwitchCase, i5.NgSwitchDefault, FormsModule, i6.NgSelectOption, i6.ɵNgSelectMultipleOption, i6.DefaultValueAccessor, i6.NumberValueAccessor, i6.CheckboxControlValueAccessor, i6.SelectControlValueAccessor, i6.NgControlStatus, i6.NgModel],\n  styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%];\\n\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\tvar __webpack_modules__ = ({\\n\\n\\n 715:\\n\\n\\n\\n\\n\\n (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/sass-loader/dist/cjs.js):\\\\nExpected \\\\\\\".\\\\n    \\u2577\\\\n173 \\u2502   background-image: url(\\\\\\\"data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http:\\\\n    \\u2502                                                                              ^\\\\n    \\u2575\\\\n  src\\\\\\\\app\\\\\\\\pages\\\\\\\\admin\\\\\\\\collection-detail\\\\\\\\collection-detail.page.scss 173:78  root stylesheet\\\");\\n\\n\\n })\\n\\n\\n \\t});\\n\\n\\n\\n \\t\\n\\n \\t// startup\\n\\n \\t// Load entry module and return exports\\n\\n \\t// This entry module doesn't tell about it's top-level declarations so it can't be inlined\\n\\n \\tvar __webpack_exports__ = {};\\n\\n \\t__webpack_modules__[715]();\\n\\n \\tresource = __webpack_exports__;\\n\\n \\t\\n\\n })()\\n;\"]\n});", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "IonicModule", "fork<PERSON><PERSON>n", "map", "i0", "ɵɵelementStart", "ɵɵtwoWayListener", "CollectionDetailPage_div_15_div_9_input_4_Template_input_ngModelChange_0_listener", "$event", "ɵɵrestoreView", "_r3", "key_r4", "ɵɵnextContext", "$implicit", "ctx_r1", "ɵɵtwoWayBindingSet", "newDocument", "ɵɵresetView", "ɵɵelementEnd", "ɵɵtwoWayProperty", "CollectionDetailPage_div_15_div_9_input_5_Template_input_ngModelChange_0_listener", "_r5", "CollectionDetailPage_div_15_div_9_input_6_Template_input_ngModelChange_0_listener", "_r6", "CollectionDetailPage_div_15_div_9_input_7_Template_input_ngModelChange_0_listener", "_r7", "ɵɵlistener", "CollectionDetailPage_div_15_div_9_input_8_Template_input_ngModelChange_0_listener", "_r8", "handleDateChange", "ɵɵproperty", "formatDateForInput", "CollectionDetailPage_div_15_div_9_textarea_9_Template_textarea_ngModelChange_0_listener", "_r9", "CollectionDetailPage_div_15_div_9_div_10_Template_input_ngModelChange_1_listener", "_r10", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate", "option_r12", "CollectionDetailPage_div_15_div_9_select_11_Template_select_ngModelChange_0_listener", "_r11", "ɵɵtemplate", "CollectionDetailPage_div_15_div_9_select_11_option_3_Template", "getSelectOptions", "option_r14", "id", "ɵɵtextInterpolate1", "display", "CollectionDetailPage_div_15_div_9_select_12_Template_select_ngModelChange_0_listener", "_r13", "CollectionDetailPage_div_15_div_9_select_12_option_3_Template", "getForeignKeyOptions", "CollectionDetailPage_div_15_div_9_input_13_Template_input_ngModelChange_0_listener", "_r15", "ɵɵelement", "ɵɵsanitizeUrl", "CollectionDetailPage_div_15_div_9_div_14_Template_input_change_1_listener", "_r16", "handleFileUpload", "CollectionDetailPage_div_15_div_9_div_14_div_2_Template", "CollectionDetailPage_div_15_div_9_input_15_Template_input_ngModelChange_0_listener", "_r17", "ɵɵelementContainerStart", "CollectionDetailPage_div_15_div_9_input_4_Template", "CollectionDetailPage_div_15_div_9_input_5_Template", "CollectionDetailPage_div_15_div_9_input_6_Template", "CollectionDetailPage_div_15_div_9_input_7_Template", "CollectionDetailPage_div_15_div_9_input_8_Template", "CollectionDetailPage_div_15_div_9_textarea_9_Template", "CollectionDetailPage_div_15_div_9_div_10_Template", "CollectionDetailPage_div_15_div_9_select_11_Template", "CollectionDetailPage_div_15_div_9_select_12_Template", "CollectionDetailPage_div_15_div_9_input_13_Template", "CollectionDetailPage_div_15_div_9_div_14_Template", "CollectionDetailPage_div_15_div_9_input_15_Template", "getFieldType", "CollectionDetailPage_div_15_Template_button_click_4_listener", "_r1", "openJsonEditor", "CollectionDetailPage_div_15_Template_button_click_6_listener", "addDocument", "CollectionDetailPage_div_15_div_9_Template", "CollectionDetailPage_div_15_Template_button_click_13_listener", "newFieldName_r18", "ɵɵreference", "value", "getObjectKeys", "jsonError", "CollectionDetailPage_div_16_div_4_Template", "CollectionDetailPage_div_16_Template_textarea_ngModelChange_5_listener", "_r19", "jsonEditorContent", "CollectionDetailPage_div_16_Template_button_click_7_listener", "cancelJsonEdit", "CollectionDetailPage_div_16_Template_button_click_9_listener", "applyJsonChanges", "key_r22", "formatDocumentFieldValue", "document_r21", "ɵɵsanitizeHtml", "CollectionDetailPage_div_20_div_1_div_1_Template_button_click_5_listener", "_r20", "editDocument", "CollectionDetailPage_div_20_div_1_div_1_Template_button_click_7_listener", "deleteDocument", "CollectionDetailPage_div_20_div_1_div_1_div_10_Template", "CollectionDetailPage_div_20_div_1_div_2_div_11_input_4_Template_input_ngModelChange_0_listener", "_r24", "key_r25", "editingDocument", "CollectionDetailPage_div_20_div_1_div_2_div_11_input_5_Template_input_ngModelChange_0_listener", "_r26", "CollectionDetailPage_div_20_div_1_div_2_div_11_input_6_Template_input_ngModelChange_0_listener", "_r27", "CollectionDetailPage_div_20_div_1_div_2_div_11_input_7_Template_input_ngModelChange_0_listener", "_r28", "CollectionDetailPage_div_20_div_1_div_2_div_11_input_8_Template_input_ngModelChange_0_listener", "_r29", "CollectionDetailPage_div_20_div_1_div_2_div_11_textarea_9_Template_textarea_ngModelChange_0_listener", "_r30", "CollectionDetailPage_div_20_div_1_div_2_div_11_div_10_Template_input_ngModelChange_1_listener", "_r31", "option_r33", "CollectionDetailPage_div_20_div_1_div_2_div_11_select_11_Template_select_ngModelChange_0_listener", "_r32", "CollectionDetailPage_div_20_div_1_div_2_div_11_select_11_option_3_Template", "option_r35", "CollectionDetailPage_div_20_div_1_div_2_div_11_select_12_Template_select_ngModelChange_0_listener", "_r34", "CollectionDetailPage_div_20_div_1_div_2_div_11_select_12_option_3_Template", "CollectionDetailPage_div_20_div_1_div_2_div_11_input_13_Template_input_ngModelChange_0_listener", "_r36", "CollectionDetailPage_div_20_div_1_div_2_div_11_div_14_Template_input_change_1_listener", "_r37", "CollectionDetailPage_div_20_div_1_div_2_div_11_div_14_div_2_Template", "CollectionDetailPage_div_20_div_1_div_2_div_11_input_15_Template_input_ngModelChange_0_listener", "_r38", "CollectionDetailPage_div_20_div_1_div_2_div_11_input_4_Template", "CollectionDetailPage_div_20_div_1_div_2_div_11_input_5_Template", "CollectionDetailPage_div_20_div_1_div_2_div_11_input_6_Template", "CollectionDetailPage_div_20_div_1_div_2_div_11_input_7_Template", "CollectionDetailPage_div_20_div_1_div_2_div_11_input_8_Template", "CollectionDetailPage_div_20_div_1_div_2_div_11_textarea_9_Template", "CollectionDetailPage_div_20_div_1_div_2_div_11_div_10_Template", "CollectionDetailPage_div_20_div_1_div_2_div_11_select_11_Template", "CollectionDetailPage_div_20_div_1_div_2_div_11_select_12_Template", "CollectionDetailPage_div_20_div_1_div_2_div_11_input_13_Template", "CollectionDetailPage_div_20_div_1_div_2_div_11_div_14_Template", "CollectionDetailPage_div_20_div_1_div_2_div_11_input_15_Template", "CollectionDetailPage_div_20_div_1_div_2_Template_button_click_4_listener", "_r23", "CollectionDetailPage_div_20_div_1_div_2_Template_button_click_6_listener", "cancelEdit", "CollectionDetailPage_div_20_div_1_div_2_Template_button_click_8_listener", "updateDocument", "CollectionDetailPage_div_20_div_1_div_2_div_11_Template", "CollectionDetailPage_div_20_div_1_div_2_Template_button_click_15_listener", "newEditFieldName_r39", "CollectionDetailPage_div_20_div_1_div_1_Template", "CollectionDetailPage_div_20_div_1_div_2_Template", "CollectionDetailPage_div_20_div_1_Template", "documents", "CollectionDetailPage", "constructor", "route", "router", "adminService", "supabaseService", "collectionName", "isLoading", "showAddForm", "showJsonEditor", "foreignKeyOptions", "selectedFiles", "imagePreviewUrls", "ngOnInit", "paramMap", "subscribe", "params", "get", "loadDocuments", "navigate", "getCollection", "next", "error", "toggleAddForm", "getModelTemplate", "loadForeignKeyOptions", "fieldTypes", "getFieldTypes", "<PERSON><PERSON><PERSON><PERSON><PERSON>s", "Object", "keys", "filter", "fieldName", "length", "observables", "pipe", "options", "results", "for<PERSON>ach", "result", "event", "document", "input", "target", "files", "file", "validTypes", "includes", "type", "alert", "size", "reader", "FileReader", "onload", "e", "_e$target", "dataUrl", "readAsDataURL", "_this", "_asyncToGenerator", "processedDocument", "handleDateConversion", "fileKey", "data", "userData", "userError", "getClient", "from", "select", "eq", "getCurrentUserId", "single", "Error", "message", "username", "fileName", "Date", "now", "name", "replace", "formData", "FormData", "append", "session", "auth", "getSession", "accessToken", "access_token", "uploadPromise", "Promise", "resolve", "reject", "xhr", "XMLHttpRequest", "queryParams", "URLSearchParams", "open", "storage", "url", "toString", "setRequestHeader", "supabase<PERSON>ey", "status", "urlData", "getPublicUrl", "publicUrl", "statusText", "onerror", "send", "image_cover", "err", "JSON", "stringify", "isForeignKey", "_this2", "docId", "docData", "processedData", "fileToBase64", "key", "formatValue", "confirm", "docCopy", "parsedJson", "parse", "goBack", "obj", "isObject", "Array", "isArray", "undefined", "toLocaleString", "String", "date", "seconds", "isNaN", "getTime", "year", "getFullYear", "month", "getMonth", "padStart", "day", "getDate", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "AdminService", "i3", "SupabaseService", "selectors", "decls", "vars", "consts", "template", "CollectionDetailPage_Template", "rf", "ctx", "CollectionDetailPage_Template_button_click_7_listener", "CollectionDetailPage_Template_button_click_13_listener", "CollectionDetailPage_div_15_Template", "CollectionDetailPage_div_16_Template", "CollectionDetailPage_div_18_Template", "CollectionDetailPage_div_19_Template", "CollectionDetailPage_div_20_Template", "i4", "Ion<PERSON><PERSON><PERSON>", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgSwitch", "NgSwitchCase", "NgSwitchDefault", "i6", "NgSelectOption", "ɵNgSelectMultipleOption", "DefaultValueAccessor", "NumberValueAccessor", "CheckboxControlValueAccessor", "SelectControlValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\admin\\collection-detail\\collection-detail.page.ts", "C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\admin\\collection-detail\\collection-detail.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { AdminService } from '../../../services/admin.service';\r\nimport { forkJoin, of } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { SupabaseService } from '../../../services/supabase.service';\r\n\r\n@Component({\r\n  selector: 'app-collection-detail',\r\n  templateUrl: './collection-detail.page.html',\r\n  styleUrls: ['./collection-detail.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule, FormsModule]\r\n})\r\nexport class CollectionDetailPage implements OnInit {\r\n  collectionName: string = '';\r\n  documents: any[] = [];\r\n  isLoading = true;\r\n  showAddForm = false;\r\n  newDocument: any = {};\r\n  editingDocument: any = null;\r\n  showJsonEditor = false;\r\n  jsonEditorContent = '';\r\n  jsonError = '';\r\n  foreignKeyOptions: Record<string, {id: string, display: string}[]> = {};\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private adminService: AdminService,\r\n    private supabaseService: SupabaseService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.route.paramMap.subscribe(params => {\r\n      this.collectionName = params.get('name') || '';\r\n      if (this.collectionName) {\r\n        this.loadDocuments();\r\n      } else {\r\n        this.router.navigate(['/admin']);\r\n      }\r\n    });\r\n  }\r\n\r\n  loadDocuments() {\r\n    this.isLoading = true;\r\n    this.adminService.getCollection(this.collectionName).subscribe({\r\n      next: (documents) => {\r\n        this.documents = documents;\r\n        this.isLoading = false;\r\n      },\r\n      error: (error) => {\r\n        this.isLoading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  toggleAddForm() {\r\n    this.showAddForm = !this.showAddForm;\r\n    if (this.showAddForm) {\r\n      this.newDocument = this.adminService.getModelTemplate(this.collectionName);\r\n\r\n      this.loadForeignKeyOptions();\r\n    }\r\n  }\r\n\r\n  loadForeignKeyOptions() {\r\n\r\n    const fieldTypes = this.adminService.getFieldTypes(this.collectionName);\r\n\r\n    const foreignKeyFields = Object.keys(fieldTypes).filter(\r\n      fieldName => fieldTypes[fieldName] === 'foreign-key'\r\n    );\r\n\r\n    if (foreignKeyFields.length === 0) {\r\n      return;\r\n    }\r\n\r\n\r\n    interface ForeignKeyResult {\r\n      fieldName: string;\r\n      options: {id: string, display: string}[];\r\n    }\r\n\r\n    const observables = foreignKeyFields.map(fieldName => {\r\n      return this.adminService.getForeignKeyOptions(fieldName).pipe(\r\n        map(options => ({ fieldName, options } as ForeignKeyResult))\r\n      );\r\n    });\r\n\r\n    forkJoin(observables).subscribe({\r\n      next: (results: ForeignKeyResult[]) => {\r\n        results.forEach(result => {\r\n          this.foreignKeyOptions[result.fieldName] = result.options;\r\n        });\r\n\r\n      },\r\n      error: (error) => {\r\n      }\r\n    });\r\n  }\r\n\r\n  selectedFiles: { [key: string]: File } = {};\r\n\r\n  imagePreviewUrls: { [key: string]: string } = {};\r\n\r\n  handleFileUpload(event: Event, document: any, fieldName: string) {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files && input.files.length > 0) {\r\n      const file = input.files[0];\r\n\r\n      if (this.collectionName === 'affiliate_offers' && fieldName === 'image_cover') {\r\n        const validTypes = ['image/jpeg', 'image/png', 'image/webp'];\r\n        if (!validTypes.includes(file.type)) {\r\n          alert('Invalid file type. Please upload a JPG, PNG, or WebP image.');\r\n          return;\r\n        }\r\n\r\n        if (file.size > 10 * 1024 * 1024) {\r\n          alert('File size too large. Maximum size is 10MB.');\r\n          return;\r\n        }\r\n\r\n        this.selectedFiles[`${document.id || 'new'}_${fieldName}`] = file;\r\n\r\n        const reader = new FileReader();\r\n        reader.onload = (e) => {\r\n          const dataUrl = e.target?.result as string;\r\n          this.imagePreviewUrls[`${document.id || 'new'}_${fieldName}`] = dataUrl;\r\n\r\n          document[fieldName] = dataUrl;\r\n        };\r\n        reader.readAsDataURL(file);\r\n\r\n      } else {\r\n        document[fieldName] = file;\r\n      }\r\n    }\r\n  }\r\n\r\n  async addDocument() {\r\n    this.isLoading = true;\r\n\r\n    try {\r\n      const processedDocument = this.handleDateConversion(this.newDocument);\r\n\r\n      const fileKey = `new_image_cover`;\r\n      if (this.selectedFiles[fileKey] && this.collectionName === 'affiliate_offers') {\r\n        const file = this.selectedFiles[fileKey];\r\n\r\n        const { data: userData, error: userError } = await this.supabaseService.getClient()\r\n          .from('profiles')\r\n          .select('username')\r\n          .eq('id', this.supabaseService.getCurrentUserId())\r\n          .single();\r\n\r\n        if (userError) {\r\n          throw new Error(`Error checking admin status: ${userError.message}`);\r\n        }\r\n\r\n        if (userData?.username !== 'admin') {\r\n          throw new Error('Only admin users can upload files to this bucket.');\r\n        }\r\n\r\n        const fileName = `${Date.now()}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;\r\n\r\n\r\n        const formData = new FormData();\r\n        formData.append('file', file);\r\n\r\n\r\n        const { data: { session } } = await this.supabaseService.getClient().auth.getSession();\r\n        const accessToken = session?.access_token;\r\n\r\n        if (!accessToken) {\r\n          throw new Error('No access token available');\r\n        }\r\n\r\n        const uploadPromise = new Promise<{ publicUrl: string }>((resolve, reject) => {\r\n          const xhr = new XMLHttpRequest();\r\n\r\n          const queryParams = new URLSearchParams({\r\n            'cacheControl': '3600',\r\n            'upsert': 'true'\r\n          });\r\n\r\n          xhr.open('POST', `${this.supabaseService.storage.url}/storage/v1/object/affiliate-offer/${fileName}?${queryParams.toString()}`);\r\n\r\n          xhr.setRequestHeader('Authorization', `Bearer ${accessToken}`);\r\n          xhr.setRequestHeader('apikey', this.supabaseService.supabaseKey);\r\n\r\n          xhr.onload = () => {\r\n            if (xhr.status >= 200 && xhr.status < 300) {\r\n              const { data: urlData } = this.supabaseService.getClient()\r\n                .storage\r\n                .from('affiliate-offer')\r\n                .getPublicUrl(fileName);\r\n\r\n              resolve({ publicUrl: urlData.publicUrl });\r\n            } else {\r\n              reject(new Error(`Upload failed: ${xhr.statusText}`));\r\n            }\r\n          };\r\n\r\n          xhr.onerror = () => {\r\n            reject(new Error('Network error during upload'));\r\n          };\r\n\r\n          xhr.send(formData);\r\n        });\r\n\r\n        const { publicUrl } = await uploadPromise;\r\n\r\n        processedDocument.image_cover = fileName;\r\n      }\r\n\r\n      this.adminService.addDocument(this.collectionName, processedDocument).subscribe({\r\n        next: () => {\r\n          this.showAddForm = false;\r\n          this.newDocument = {};\r\n          this.selectedFiles = {}; \n          this.imagePreviewUrls = {}; \n          this.loadDocuments();\r\n        },\r\n        error: (error) => {\r\n          this.isLoading = false;\r\n        }\r\n      });\r\n    } catch (err) {\r\n      const error = err as any;\r\n      alert(`Error: ${error.message || JSON.stringify(error)}`);\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  editDocument(document: any) {\r\n    this.editingDocument = { ...document };\r\n\r\n    if (Object.keys(this.foreignKeyOptions).length === 0) {\r\n      this.loadForeignKeyOptions();\r\n    }\r\n  }\r\n\r\n  getForeignKeyOptions(fieldName: string): {id: string, display: string}[] {\r\n    return this.foreignKeyOptions[fieldName] || [];\r\n  }\r\n\r\n  isForeignKey(fieldName: string): boolean {\r\n    return this.getFieldType(fieldName) === 'foreign-key';\r\n  }\r\n\r\n  async updateDocument() {\r\n    if (!this.editingDocument) return;\r\n\r\n    this.isLoading = true;\r\n\r\n    try {\r\n      const docId = this.editingDocument.id;\r\n      const docData = { ...this.editingDocument };\r\n      delete docData.id;\r\n\r\n      const processedData = this.handleDateConversion(docData);\r\n\r\n      const fileKey = `${docId}_image_cover`;\r\n      if (this.selectedFiles[fileKey] && this.collectionName === 'affiliate_offers') {\r\n        const file = this.selectedFiles[fileKey];\r\n\r\n        const { data: userData, error: userError } = await this.supabaseService.getClient()\r\n          .from('profiles')\r\n          .select('username')\r\n          .eq('id', this.supabaseService.getCurrentUserId())\r\n          .single();\r\n\r\n        if (userError) {\r\n          throw new Error(`Error checking admin status: ${userError.message}`);\r\n        }\r\n\r\n        if (userData?.username !== 'admin') {\r\n          throw new Error('Only admin users can upload files to this bucket.');\r\n        }\r\n\r\n        const fileName = `${Date.now()}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;\r\n\r\n\r\n        const formData = new FormData();\r\n        formData.append('file', file);\r\n\r\n\r\n        const { data: { session } } = await this.supabaseService.getClient().auth.getSession();\r\n        const accessToken = session?.access_token;\r\n\r\n        if (!accessToken) {\r\n          throw new Error('No access token available');\r\n        }\r\n\r\n        const uploadPromise = new Promise<{ publicUrl: string }>((resolve, reject) => {\r\n          const xhr = new XMLHttpRequest();\r\n\r\n          const queryParams = new URLSearchParams({\r\n            'cacheControl': '3600',\r\n            'upsert': 'true'\r\n          });\r\n\r\n          xhr.open('POST', `${this.supabaseService.storage.url}/storage/v1/object/affiliate-offer/${fileName}?${queryParams.toString()}`);\r\n\r\n          xhr.setRequestHeader('Authorization', `Bearer ${accessToken}`);\r\n          xhr.setRequestHeader('apikey', this.supabaseService.supabaseKey);\r\n\r\n          xhr.onload = () => {\r\n            if (xhr.status >= 200 && xhr.status < 300) {\r\n              const { data: urlData } = this.supabaseService.getClient()\r\n                .storage\r\n                .from('affiliate-offer')\r\n                .getPublicUrl(fileName);\r\n\r\n              resolve({ publicUrl: urlData.publicUrl });\r\n            } else {\r\n              reject(new Error(`Upload failed: ${xhr.statusText}`));\r\n            }\r\n          };\r\n\r\n          xhr.onerror = () => {\r\n            reject(new Error('Network error during upload'));\r\n          };\r\n\r\n          xhr.send(formData);\r\n        });\r\n\r\n        const { publicUrl } = await uploadPromise;\r\n\r\n        processedData.image_cover = fileName;\r\n      }\r\n\r\n      this.adminService.updateDocument(this.collectionName, docId, processedData).subscribe({\r\n        next: () => {\r\n          this.editingDocument = null;\r\n          this.selectedFiles = {}; \n          this.imagePreviewUrls = {}; \n          this.loadDocuments();\r\n        },\r\n        error: (error) => {\r\n          this.isLoading = false;\r\n        }\r\n      });\r\n    } catch (err) {\r\n      const error = err as any;\r\n      alert(`Error: ${error.message || JSON.stringify(error)}`);\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  fileToBase64(file: File): Promise<string> {\r\n    return new Promise((resolve, reject) => {\r\n      const reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => resolve(reader.result as string);\r\n      reader.onerror = error => reject(error);\r\n    });\r\n  }\r\n\r\n  formatDocumentFieldValue(document: any, key: string): string {\r\n    if (this.collectionName === 'affiliate_offers' && key === 'image_cover' && document[key]) {\r\n      try {\r\n        if (!document[key].includes('://')) {\r\n          const { data: urlData } = this.supabaseService.getClient()\r\n            .storage\r\n            .from('affiliate-offer')\r\n            .getPublicUrl(document[key]);\r\n\r\n\r\n          return `<img src=\"${urlData.publicUrl}\" alt=\"Image\" style=\"max-width: 100px; max-height: 100px;\">`;\r\n        } else {\r\n          return `<img src=\"${document[key]}\" alt=\"Image\" style=\"max-width: 100px; max-height: 100px;\">`;\r\n        }\r\n      } catch (err) {\r\n        const error = err as any;\r\n        return `Image: ${document[key]} (Error loading preview)`;\r\n      }\r\n    }\r\n\r\n    return this.formatValue(document[key]);\r\n  }\r\n\r\n  deleteDocument(docId: string) {\r\n    if (confirm('Are you sure you want to delete this document? This action cannot be undone.')) {\r\n      this.isLoading = true;\r\n      this.adminService.deleteDocument(this.collectionName, docId).subscribe({\r\n        next: () => {\r\n          this.loadDocuments();\r\n        },\r\n        error: (error) => {\r\n          this.isLoading = false;\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  cancelEdit() {\r\n    this.editingDocument = null;\r\n  }\r\n\r\n  openJsonEditor(document?: any) {\r\n    if (document) {\r\n      const docCopy = { ...document };\r\n      delete docCopy.id;\r\n      this.jsonEditorContent = JSON.stringify(docCopy, null, 2);\r\n      this.editingDocument = document;\r\n    } else {\r\n      this.jsonEditorContent = JSON.stringify(this.newDocument || {}, null, 2);\r\n    }\r\n    this.showJsonEditor = true;\r\n    this.jsonError = '';\r\n  }\r\n\r\n  applyJsonChanges() {\r\n    try {\r\n      const parsedJson = JSON.parse(this.jsonEditorContent);\r\n\r\n      if (this.editingDocument) {\r\n        this.editingDocument = {\r\n          id: this.editingDocument.id,\r\n          ...parsedJson\r\n        };\r\n      } else {\r\n        this.newDocument = parsedJson;\r\n      }\r\n\r\n      this.showJsonEditor = false;\r\n      this.jsonError = '';\r\n    } catch (e) {\r\n      this.jsonError = 'Invalid JSON format';\r\n    }\r\n  }\r\n\r\n  cancelJsonEdit() {\r\n    this.showJsonEditor = false;\r\n    this.jsonError = '';\r\n  }\r\n\r\n  goBack() {\r\n    this.router.navigate(['/admin']);\r\n  }\r\n\r\n  getObjectKeys(obj: any): string[] {\r\n    return Object.keys(obj || {});\r\n  }\r\n\r\n  isObject(value: any): boolean {\r\n    return typeof value === 'object' && value !== null && !Array.isArray(value);\r\n  }\r\n\r\n  formatValue(value: any): string {\r\n    if (value === null || value === undefined) {\r\n      return 'null';\r\n    }\r\n\r\n    if (typeof value === 'object') {\r\n      if (value instanceof Date) {\r\n        return value.toLocaleString();\r\n      }\r\n      return JSON.stringify(value);\r\n    }\r\n\r\n    return String(value);\r\n  }\r\n\r\n  getFieldType(fieldName: string): string {\r\n    if (this.collectionName === 'affiliate_offers' && fieldName === 'image_cover') {\r\n      return 'file';\r\n    }\r\n\r\n    const fieldTypes = this.adminService.getFieldTypes(this.collectionName);\r\n    return fieldTypes[fieldName] || 'text';\r\n  }\r\n\r\n  getSelectOptions(fieldName: string): string[] {\r\n    return this.adminService.getSelectOptions(this.collectionName, fieldName);\r\n  }\r\n\r\n  formatDateForInput(value: any): string {\r\n    if (!value) return '';\r\n\r\n    let date: Date;\r\n\r\n    if (value instanceof Date) {\r\n      date = value;\r\n    } else if (typeof value === 'object' && value.seconds) {\r\n      date = new Date(value.seconds * 1000);\r\n    } else if (typeof value === 'string') {\r\n      try {\r\n        date = new Date(value);\r\n        if (isNaN(date.getTime())) {\r\n          return value;\r\n        }\r\n      } catch (e) {\r\n        return value;\r\n      }\r\n    } else {\r\n      return String(value);\r\n    }\r\n\r\n    const year = date.getFullYear();\r\n    const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n    return `${year}-${month}-${day}`;\r\n  }\r\n\r\n  handleDateChange(obj: any, key: string, event: any): void {\r\n    if (event) {\r\n      obj[key] = new Date(event);\r\n    } else {\r\n      obj[key] = null;\r\n    }\r\n  }\r\n\r\n  handleDateConversion(obj: any): any {\r\n    if (!obj) return obj;\r\n\r\n    const result = { ...obj };\r\n\r\n    for (const key in result) {\r\n      const value = result[key];\r\n\r\n      if (typeof value === 'string' && this.getFieldType(key) === 'date') {\r\n        try {\r\n          result[key] = new Date(value);\r\n        } catch (e) {\r\n        }\r\n      }\r\n    }\r\n\r\n    return result;\r\n  }\r\n}\r\n", "<div class=\"container\">\r\n  <header>\r\n    <div class=\"logo\">\r\n      <img src=\"assets/images/upshift_icon_mini.svg\" alt=\"Upshift\">\r\n      <span>Upshift</span>\r\n    </div>\r\n    <div class=\"header-content\">\r\n      <button class=\"back-button\" (click)=\"goBack()\">← Back</button>\r\n      <h1>{{ collectionName }}</h1>\r\n    </div>\r\n  </header>\r\n\r\n  <div class=\"admin-content\">\r\n    <div class=\"collection-actions\">\r\n      <button class=\"add-button\" (click)=\"toggleAddForm()\">\r\n        {{ showAddForm ? 'Cancel' : 'Add Document' }}\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Add Document Form -->\r\n    <div *ngIf=\"showAddForm\" class=\"document-form\">\r\n      <h2>Add New Document</h2>\r\n\r\n      <div class=\"form-actions\">\r\n        <button class=\"json-button\" (click)=\"openJsonEditor()\">Edit as JSON</button>\r\n        <button class=\"save-button\" (click)=\"addDocument()\">Save Document</button>\r\n      </div>\r\n\r\n      <div class=\"form-fields\">\r\n        <div class=\"form-field\" *ngFor=\"let key of getObjectKeys(newDocument)\">\r\n          <label>{{ key }}</label>\r\n\r\n          <!-- Different input types based on field type -->\r\n          <ng-container [ngSwitch]=\"getFieldType(key)\">\r\n            <!-- Text input -->\r\n            <input *ngSwitchCase=\"'text'\" type=\"text\" [(ngModel)]=\"newDocument[key]\">\r\n\r\n            <!-- Email input -->\r\n            <input *ngSwitchCase=\"'email'\" type=\"email\" [(ngModel)]=\"newDocument[key]\">\r\n\r\n            <!-- Password input -->\r\n            <input *ngSwitchCase=\"'password'\" type=\"password\" [(ngModel)]=\"newDocument[key]\">\r\n\r\n            <!-- Number input -->\r\n            <input *ngSwitchCase=\"'number'\" type=\"number\" [(ngModel)]=\"newDocument[key]\">\r\n\r\n            <!-- Date input -->\r\n            <input *ngSwitchCase=\"'date'\" type=\"date\" [ngModel]=\"formatDateForInput(newDocument[key])\"\r\n                  (ngModelChange)=\"handleDateChange(newDocument, key, $event)\">\r\n\r\n            <!-- Textarea -->\r\n            <textarea *ngSwitchCase=\"'textarea'\" rows=\"3\" [(ngModel)]=\"newDocument[key]\"></textarea>\r\n\r\n            <!-- Boolean/Checkbox -->\r\n            <div *ngSwitchCase=\"'boolean'\" class=\"checkbox-field\">\r\n              <input type=\"checkbox\" [(ngModel)]=\"newDocument[key]\" [id]=\"'new-' + key\">\r\n              <label [for]=\"'new-' + key\">{{ newDocument[key] ? 'Yes' : 'No' }}</label>\r\n            </div>\r\n\r\n            <!-- Select/Dropdown -->\r\n            <select *ngSwitchCase=\"'select'\" [(ngModel)]=\"newDocument[key]\">\r\n              <option value=\"\">-- Select --</option>\r\n              <option *ngFor=\"let option of getSelectOptions(key)\" [value]=\"option\">{{ option }}</option>\r\n            </select>\r\n\r\n            <!-- Foreign Key Dropdown -->\r\n            <select *ngSwitchCase=\"'foreign-key'\" [(ngModel)]=\"newDocument[key]\">\r\n              <option value=\"\">-- Select --</option>\r\n              <option *ngFor=\"let option of getForeignKeyOptions(key)\" [value]=\"option.id\">\r\n                {{ option.display }}\r\n              </option>\r\n            </select>\r\n\r\n            <!-- Color picker -->\r\n            <input *ngSwitchCase=\"'color'\" type=\"color\" [(ngModel)]=\"newDocument[key]\">\r\n\r\n            <!-- File input for images -->\r\n            <div *ngSwitchCase=\"'file'\" class=\"file-input-container\">\r\n              <input type=\"file\" (change)=\"handleFileUpload($event, newDocument, key)\" accept=\"image/*\">\r\n              <div *ngIf=\"newDocument[key]\" class=\"file-preview\">\r\n                <img [src]=\"newDocument[key]\" alt=\"Preview\">\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Default text input -->\r\n            <input *ngSwitchDefault type=\"text\" [(ngModel)]=\"newDocument[key]\">\r\n          </ng-container>\r\n        </div>\r\n\r\n        <div class=\"add-field\">\r\n          <input type=\"text\" #newFieldName placeholder=\"Field name\">\r\n          <button (click)=\"newDocument[newFieldName.value] = ''; newFieldName.value = ''\">Add Field</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- JSON Editor Modal -->\r\n    <div *ngIf=\"showJsonEditor\" class=\"json-editor-modal\">\r\n      <div class=\"json-editor-content\">\r\n        <h2>Edit as JSON</h2>\r\n        <div class=\"json-error\" *ngIf=\"jsonError\">{{ jsonError }}</div>\r\n        <textarea [(ngModel)]=\"jsonEditorContent\" rows=\"20\"></textarea>\r\n        <div class=\"json-actions\">\r\n          <button class=\"cancel-button\" (click)=\"cancelJsonEdit()\">Cancel</button>\r\n          <button class=\"apply-button\" (click)=\"applyJsonChanges()\">Apply Changes</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Documents List -->\r\n    <div class=\"documents-list\">\r\n      <div *ngIf=\"isLoading\" class=\"loading\">\r\n        <ion-spinner></ion-spinner>\r\n        <p>Loading documents...</p>\r\n      </div>\r\n\r\n      <div *ngIf=\"!isLoading && documents.length === 0\" class=\"no-data\">\r\n        <p>No documents found in this collection.</p>\r\n      </div>\r\n\r\n      <div *ngIf=\"!isLoading && documents.length > 0\" class=\"documents-table\">\r\n        <div class=\"document-item\" *ngFor=\"let document of documents\">\r\n          <!-- View Mode -->\r\n          <div *ngIf=\"editingDocument?.id !== document.id\" class=\"document-view\">\r\n            <div class=\"document-header\">\r\n              <h3>ID: {{ document.id }}</h3>\r\n              <div class=\"document-actions\">\r\n                <button class=\"edit-button\" (click)=\"editDocument(document)\">Edit</button>\r\n                <button class=\"delete-button\" (click)=\"deleteDocument(document.id)\">Delete</button>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"document-fields\">\r\n              <div class=\"document-field\" *ngFor=\"let key of getObjectKeys(document)\" [hidden]=\"key === 'id'\">\r\n                <span class=\"field-name\">{{ key }}:</span>\r\n                <span class=\"field-value\" [innerHTML]=\"formatDocumentFieldValue(document, key)\"></span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Edit Mode -->\r\n          <div *ngIf=\"editingDocument?.id === document.id\" class=\"document-edit\">\r\n            <h3>Editing Document</h3>\r\n\r\n            <div class=\"form-actions\">\r\n              <button class=\"json-button\" (click)=\"openJsonEditor(document)\">Edit as JSON</button>\r\n              <button class=\"cancel-button\" (click)=\"cancelEdit()\">Cancel</button>\r\n              <button class=\"save-button\" (click)=\"updateDocument()\">Save Changes</button>\r\n            </div>\r\n\r\n            <div class=\"form-fields\">\r\n              <div class=\"form-field\" *ngFor=\"let key of getObjectKeys(editingDocument)\" [hidden]=\"key === 'id'\">\r\n                <label>{{ key }}</label>\r\n\r\n                <!-- Different input types based on field type -->\r\n                <ng-container [ngSwitch]=\"getFieldType(key)\">\r\n                  <!-- Text input -->\r\n                  <input *ngSwitchCase=\"'text'\" type=\"text\" [(ngModel)]=\"editingDocument[key]\">\r\n\r\n                  <!-- Email input -->\r\n                  <input *ngSwitchCase=\"'email'\" type=\"email\" [(ngModel)]=\"editingDocument[key]\">\r\n\r\n                  <!-- Password input -->\r\n                  <input *ngSwitchCase=\"'password'\" type=\"password\" [(ngModel)]=\"editingDocument[key]\">\r\n\r\n                  <!-- Number input -->\r\n                  <input *ngSwitchCase=\"'number'\" type=\"number\" [(ngModel)]=\"editingDocument[key]\">\r\n\r\n                  <!-- Date input -->\r\n                  <input *ngSwitchCase=\"'date'\" type=\"date\" [ngModel]=\"formatDateForInput(editingDocument[key])\"\r\n                        (ngModelChange)=\"handleDateChange(editingDocument, key, $event)\">\r\n\r\n                  <!-- Textarea -->\r\n                  <textarea *ngSwitchCase=\"'textarea'\" rows=\"3\" [(ngModel)]=\"editingDocument[key]\"></textarea>\r\n\r\n                  <!-- Boolean/Checkbox -->\r\n                  <div *ngSwitchCase=\"'boolean'\" class=\"checkbox-field\">\r\n                    <input type=\"checkbox\" [(ngModel)]=\"editingDocument[key]\" [id]=\"'edit-' + key\">\r\n                    <label [for]=\"'edit-' + key\">{{ editingDocument[key] ? 'Yes' : 'No' }}</label>\r\n                  </div>\r\n\r\n                  <!-- Select/Dropdown -->\r\n                  <select *ngSwitchCase=\"'select'\" [(ngModel)]=\"editingDocument[key]\">\r\n                    <option value=\"\">-- Select --</option>\r\n                    <option *ngFor=\"let option of getSelectOptions(key)\" [value]=\"option\">{{ option }}</option>\r\n                  </select>\r\n\r\n                  <!-- Foreign Key Dropdown -->\r\n                  <select *ngSwitchCase=\"'foreign-key'\" [(ngModel)]=\"editingDocument[key]\">\r\n                    <option value=\"\">-- Select --</option>\r\n                    <option *ngFor=\"let option of getForeignKeyOptions(key)\" [value]=\"option.id\">\r\n                      {{ option.display }}\r\n                    </option>\r\n                  </select>\r\n\r\n                  <!-- Color picker -->\r\n                  <input *ngSwitchCase=\"'color'\" type=\"color\" [(ngModel)]=\"editingDocument[key]\">\r\n\r\n                  <!-- File input for images -->\r\n                  <div *ngSwitchCase=\"'file'\" class=\"file-input-container\">\r\n                    <input type=\"file\" (change)=\"handleFileUpload($event, editingDocument, key)\" accept=\"image/*\">\r\n                    <div *ngIf=\"editingDocument[key]\" class=\"file-preview\">\r\n                      <img [src]=\"editingDocument[key]\" alt=\"Preview\">\r\n                    </div>\r\n                  </div>\r\n\r\n                  <!-- Default text input -->\r\n                  <input *ngSwitchDefault type=\"text\" [(ngModel)]=\"editingDocument[key]\">\r\n                </ng-container>\r\n              </div>\r\n\r\n              <div class=\"add-field\">\r\n                <input type=\"text\" #newEditFieldName placeholder=\"Field name\">\r\n                <button (click)=\"editingDocument[newEditFieldName.value] = ''; newEditFieldName.value = ''\">Add Field</button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,gBAAgB;AAG5C,SAASC,QAAQ,QAAY,MAAM;AACnC,SAASC,GAAG,QAAQ,gBAAgB;;;;;;;;;;;IC4BxBC,EAAA,CAAAC,cAAA,gBAAyE;IAA/BD,EAAA,CAAAE,gBAAA,2BAAAC,kFAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAW,kBAAA,CAAAD,MAAA,CAAAE,WAAA,CAAAL,MAAA,GAAAH,MAAA,MAAAM,MAAA,CAAAE,WAAA,CAAAL,MAAA,IAAAH,MAAA;MAAA,OAAAJ,EAAA,CAAAa,WAAA,CAAAT,MAAA;IAAA,EAA8B;IAAxEJ,EAAA,CAAAc,YAAA,EAAyE;;;;;IAA/Bd,EAAA,CAAAe,gBAAA,YAAAL,MAAA,CAAAE,WAAA,CAAAL,MAAA,EAA8B;;;;;;IAGxEP,EAAA,CAAAC,cAAA,gBAA2E;IAA/BD,EAAA,CAAAE,gBAAA,2BAAAc,kFAAAZ,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAP,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAW,kBAAA,CAAAD,MAAA,CAAAE,WAAA,CAAAL,MAAA,GAAAH,MAAA,MAAAM,MAAA,CAAAE,WAAA,CAAAL,MAAA,IAAAH,MAAA;MAAA,OAAAJ,EAAA,CAAAa,WAAA,CAAAT,MAAA;IAAA,EAA8B;IAA1EJ,EAAA,CAAAc,YAAA,EAA2E;;;;;IAA/Bd,EAAA,CAAAe,gBAAA,YAAAL,MAAA,CAAAE,WAAA,CAAAL,MAAA,EAA8B;;;;;;IAG1EP,EAAA,CAAAC,cAAA,gBAAiF;IAA/BD,EAAA,CAAAE,gBAAA,2BAAAgB,kFAAAd,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAc,GAAA;MAAA,MAAAZ,MAAA,GAAAP,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAW,kBAAA,CAAAD,MAAA,CAAAE,WAAA,CAAAL,MAAA,GAAAH,MAAA,MAAAM,MAAA,CAAAE,WAAA,CAAAL,MAAA,IAAAH,MAAA;MAAA,OAAAJ,EAAA,CAAAa,WAAA,CAAAT,MAAA;IAAA,EAA8B;IAAhFJ,EAAA,CAAAc,YAAA,EAAiF;;;;;IAA/Bd,EAAA,CAAAe,gBAAA,YAAAL,MAAA,CAAAE,WAAA,CAAAL,MAAA,EAA8B;;;;;;IAGhFP,EAAA,CAAAC,cAAA,gBAA6E;IAA/BD,EAAA,CAAAE,gBAAA,2BAAAkB,kFAAAhB,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAW,kBAAA,CAAAD,MAAA,CAAAE,WAAA,CAAAL,MAAA,GAAAH,MAAA,MAAAM,MAAA,CAAAE,WAAA,CAAAL,MAAA,IAAAH,MAAA;MAAA,OAAAJ,EAAA,CAAAa,WAAA,CAAAT,MAAA;IAAA,EAA8B;IAA5EJ,EAAA,CAAAc,YAAA,EAA6E;;;;;IAA/Bd,EAAA,CAAAe,gBAAA,YAAAL,MAAA,CAAAE,WAAA,CAAAL,MAAA,EAA8B;;;;;;IAG5EP,EAAA,CAAAC,cAAA,gBACmE;IAA7DD,EAAA,CAAAsB,UAAA,2BAAAC,kFAAAnB,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAmB,GAAA;MAAA,MAAAjB,MAAA,GAAAP,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAa,WAAA,CAAiBH,MAAA,CAAAe,gBAAA,CAAAf,MAAA,CAAAE,WAAA,EAAAL,MAAA,EAAAH,MAAA,CAA0C;IAAA,EAAC;IADlEJ,EAAA,CAAAc,YAAA,EACmE;;;;;IADzBd,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAAiB,kBAAA,CAAAjB,MAAA,CAAAE,WAAA,CAAAL,MAAA,GAAgD;;;;;;IAI1FP,EAAA,CAAAC,cAAA,mBAA6E;IAA/BD,EAAA,CAAAE,gBAAA,2BAAA0B,wFAAAxB,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAwB,GAAA;MAAA,MAAAtB,MAAA,GAAAP,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAW,kBAAA,CAAAD,MAAA,CAAAE,WAAA,CAAAL,MAAA,GAAAH,MAAA,MAAAM,MAAA,CAAAE,WAAA,CAAAL,MAAA,IAAAH,MAAA;MAAA,OAAAJ,EAAA,CAAAa,WAAA,CAAAT,MAAA;IAAA,EAA8B;IAACJ,EAAA,CAAAc,YAAA,EAAW;;;;;IAA1Cd,EAAA,CAAAe,gBAAA,YAAAL,MAAA,CAAAE,WAAA,CAAAL,MAAA,EAA8B;;;;;;IAI1EP,EADF,CAAAC,cAAA,cAAsD,gBACsB;IAAnDD,EAAA,CAAAE,gBAAA,2BAAA4B,iFAAA1B,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA0B,IAAA;MAAA,MAAAxB,MAAA,GAAAP,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAW,kBAAA,CAAAD,MAAA,CAAAE,WAAA,CAAAL,MAAA,GAAAH,MAAA,MAAAM,MAAA,CAAAE,WAAA,CAAAL,MAAA,IAAAH,MAAA;MAAA,OAAAJ,EAAA,CAAAa,WAAA,CAAAT,MAAA;IAAA,EAA8B;IAArDJ,EAAA,CAAAc,YAAA,EAA0E;IAC1Ed,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAgC,MAAA,GAAqC;IACnEhC,EADmE,CAAAc,YAAA,EAAQ,EACrE;;;;;IAFmBd,EAAA,CAAAiC,SAAA,EAA8B;IAA9BjC,EAAA,CAAAe,gBAAA,YAAAL,MAAA,CAAAE,WAAA,CAAAL,MAAA,EAA8B;IAACP,EAAA,CAAA0B,UAAA,gBAAAnB,MAAA,CAAmB;IAClEP,EAAA,CAAAiC,SAAA,EAAoB;IAApBjC,EAAA,CAAA0B,UAAA,iBAAAnB,MAAA,CAAoB;IAACP,EAAA,CAAAiC,SAAA,EAAqC;IAArCjC,EAAA,CAAAkC,iBAAA,CAAAxB,MAAA,CAAAE,WAAA,CAAAL,MAAA,iBAAqC;;;;;IAMjEP,EAAA,CAAAC,cAAA,iBAAsE;IAAAD,EAAA,CAAAgC,MAAA,GAAY;IAAAhC,EAAA,CAAAc,YAAA,EAAS;;;;IAAtCd,EAAA,CAAA0B,UAAA,UAAAS,UAAA,CAAgB;IAACnC,EAAA,CAAAiC,SAAA,EAAY;IAAZjC,EAAA,CAAAkC,iBAAA,CAAAC,UAAA,CAAY;;;;;;IAFpFnC,EAAA,CAAAC,cAAA,iBAAgE;IAA/BD,EAAA,CAAAE,gBAAA,2BAAAkC,qFAAAhC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAgC,IAAA;MAAA,MAAA9B,MAAA,GAAAP,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAW,kBAAA,CAAAD,MAAA,CAAAE,WAAA,CAAAL,MAAA,GAAAH,MAAA,MAAAM,MAAA,CAAAE,WAAA,CAAAL,MAAA,IAAAH,MAAA;MAAA,OAAAJ,EAAA,CAAAa,WAAA,CAAAT,MAAA;IAAA,EAA8B;IAC7DJ,EAAA,CAAAC,cAAA,iBAAiB;IAAAD,EAAA,CAAAgC,MAAA,mBAAY;IAAAhC,EAAA,CAAAc,YAAA,EAAS;IACtCd,EAAA,CAAAsC,UAAA,IAAAC,6DAAA,qBAAsE;IACxEvC,EAAA,CAAAc,YAAA,EAAS;;;;;IAHwBd,EAAA,CAAAe,gBAAA,YAAAL,MAAA,CAAAE,WAAA,CAAAL,MAAA,EAA8B;IAElCP,EAAA,CAAAiC,SAAA,GAAwB;IAAxBjC,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAA8B,gBAAA,CAAAjC,MAAA,EAAwB;;;;;IAMnDP,EAAA,CAAAC,cAAA,iBAA6E;IAC3ED,EAAA,CAAAgC,MAAA,GACF;IAAAhC,EAAA,CAAAc,YAAA,EAAS;;;;IAFgDd,EAAA,CAAA0B,UAAA,UAAAe,UAAA,CAAAC,EAAA,CAAmB;IAC1E1C,EAAA,CAAAiC,SAAA,EACF;IADEjC,EAAA,CAAA2C,kBAAA,MAAAF,UAAA,CAAAG,OAAA,MACF;;;;;;IAJF5C,EAAA,CAAAC,cAAA,iBAAqE;IAA/BD,EAAA,CAAAE,gBAAA,2BAAA2C,qFAAAzC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAyC,IAAA;MAAA,MAAAvC,MAAA,GAAAP,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAW,kBAAA,CAAAD,MAAA,CAAAE,WAAA,CAAAL,MAAA,GAAAH,MAAA,MAAAM,MAAA,CAAAE,WAAA,CAAAL,MAAA,IAAAH,MAAA;MAAA,OAAAJ,EAAA,CAAAa,WAAA,CAAAT,MAAA;IAAA,EAA8B;IAClEJ,EAAA,CAAAC,cAAA,iBAAiB;IAAAD,EAAA,CAAAgC,MAAA,mBAAY;IAAAhC,EAAA,CAAAc,YAAA,EAAS;IACtCd,EAAA,CAAAsC,UAAA,IAAAS,6DAAA,qBAA6E;IAG/E/C,EAAA,CAAAc,YAAA,EAAS;;;;;IAL6Bd,EAAA,CAAAe,gBAAA,YAAAL,MAAA,CAAAE,WAAA,CAAAL,MAAA,EAA8B;IAEvCP,EAAA,CAAAiC,SAAA,GAA4B;IAA5BjC,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAAsC,oBAAA,CAAAzC,MAAA,EAA4B;;;;;;IAMzDP,EAAA,CAAAC,cAAA,gBAA2E;IAA/BD,EAAA,CAAAE,gBAAA,2BAAA+C,mFAAA7C,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA6C,IAAA;MAAA,MAAA3C,MAAA,GAAAP,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAW,kBAAA,CAAAD,MAAA,CAAAE,WAAA,CAAAL,MAAA,GAAAH,MAAA,MAAAM,MAAA,CAAAE,WAAA,CAAAL,MAAA,IAAAH,MAAA;MAAA,OAAAJ,EAAA,CAAAa,WAAA,CAAAT,MAAA;IAAA,EAA8B;IAA1EJ,EAAA,CAAAc,YAAA,EAA2E;;;;;IAA/Bd,EAAA,CAAAe,gBAAA,YAAAL,MAAA,CAAAE,WAAA,CAAAL,MAAA,EAA8B;;;;;IAKxEP,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAmD,SAAA,cAA4C;IAC9CnD,EAAA,CAAAc,YAAA,EAAM;;;;;IADCd,EAAA,CAAAiC,SAAA,EAAwB;IAAxBjC,EAAA,CAAA0B,UAAA,QAAAhB,MAAA,CAAAE,WAAA,CAAAL,MAAA,GAAAP,EAAA,CAAAoD,aAAA,CAAwB;;;;;;IAF/BpD,EADF,CAAAC,cAAA,cAAyD,gBACmC;IAAvED,EAAA,CAAAsB,UAAA,oBAAA+B,0EAAAjD,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAiD,IAAA;MAAA,MAAA/C,MAAA,GAAAP,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAa,WAAA,CAAUH,MAAA,CAAA6C,gBAAA,CAAAnD,MAAA,EAAAM,MAAA,CAAAE,WAAA,EAAAL,MAAA,CAA0C;IAAA,EAAC;IAAxEP,EAAA,CAAAc,YAAA,EAA0F;IAC1Fd,EAAA,CAAAsC,UAAA,IAAAkB,uDAAA,kBAAmD;IAGrDxD,EAAA,CAAAc,YAAA,EAAM;;;;;IAHEd,EAAA,CAAAiC,SAAA,GAAsB;IAAtBjC,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAAE,WAAA,CAAAL,MAAA,EAAsB;;;;;;IAM9BP,EAAA,CAAAC,cAAA,gBAAmE;IAA/BD,EAAA,CAAAE,gBAAA,2BAAAuD,mFAAArD,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAqD,IAAA;MAAA,MAAAnD,MAAA,GAAAP,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAW,kBAAA,CAAAD,MAAA,CAAAE,WAAA,CAAAL,MAAA,GAAAH,MAAA,MAAAM,MAAA,CAAAE,WAAA,CAAAL,MAAA,IAAAH,MAAA;MAAA,OAAAJ,EAAA,CAAAa,WAAA,CAAAT,MAAA;IAAA,EAA8B;IAAlEJ,EAAA,CAAAc,YAAA,EAAmE;;;;;IAA/Bd,EAAA,CAAAe,gBAAA,YAAAL,MAAA,CAAAE,WAAA,CAAAL,MAAA,EAA8B;;;;;IAvDpEP,EADF,CAAAC,cAAA,cAAuE,YAC9D;IAAAD,EAAA,CAAAgC,MAAA,GAAS;IAAAhC,EAAA,CAAAc,YAAA,EAAQ;IAGxBd,EAAA,CAAA2D,uBAAA,OAA6C;IAoD3C3D,EAlDA,CAAAsC,UAAA,IAAAsB,kDAAA,oBAAyE,IAAAC,kDAAA,oBAGE,IAAAC,kDAAA,oBAGM,IAAAC,kDAAA,oBAGJ,IAAAC,kDAAA,oBAIV,IAAAC,qDAAA,uBAGU,KAAAC,iDAAA,kBAGvB,KAAAC,oDAAA,qBAMU,KAAAC,oDAAA,qBAMK,KAAAC,mDAAA,oBAQM,KAAAC,iDAAA,kBAGlB,KAAAC,mDAAA,oBAQU;;IAEvEvE,EAAA,CAAAc,YAAA,EAAM;;;;;IAzDGd,EAAA,CAAAiC,SAAA,GAAS;IAATjC,EAAA,CAAAkC,iBAAA,CAAA3B,MAAA,CAAS;IAGFP,EAAA,CAAAiC,SAAA,EAA8B;IAA9BjC,EAAA,CAAA0B,UAAA,aAAAhB,MAAA,CAAA8D,YAAA,CAAAjE,MAAA,EAA8B;IAElCP,EAAA,CAAAiC,SAAA,EAAoB;IAApBjC,EAAA,CAAA0B,UAAA,wBAAoB;IAGpB1B,EAAA,CAAAiC,SAAA,EAAqB;IAArBjC,EAAA,CAAA0B,UAAA,yBAAqB;IAGrB1B,EAAA,CAAAiC,SAAA,EAAwB;IAAxBjC,EAAA,CAAA0B,UAAA,4BAAwB;IAGxB1B,EAAA,CAAAiC,SAAA,EAAsB;IAAtBjC,EAAA,CAAA0B,UAAA,0BAAsB;IAGtB1B,EAAA,CAAAiC,SAAA,EAAoB;IAApBjC,EAAA,CAAA0B,UAAA,wBAAoB;IAIjB1B,EAAA,CAAAiC,SAAA,EAAwB;IAAxBjC,EAAA,CAAA0B,UAAA,4BAAwB;IAG7B1B,EAAA,CAAAiC,SAAA,EAAuB;IAAvBjC,EAAA,CAAA0B,UAAA,2BAAuB;IAMpB1B,EAAA,CAAAiC,SAAA,EAAsB;IAAtBjC,EAAA,CAAA0B,UAAA,0BAAsB;IAMtB1B,EAAA,CAAAiC,SAAA,EAA2B;IAA3BjC,EAAA,CAAA0B,UAAA,+BAA2B;IAQ5B1B,EAAA,CAAAiC,SAAA,EAAqB;IAArBjC,EAAA,CAAA0B,UAAA,yBAAqB;IAGvB1B,EAAA,CAAAiC,SAAA,EAAoB;IAApBjC,EAAA,CAAA0B,UAAA,wBAAoB;;;;;;IAxDhC1B,EADF,CAAAC,cAAA,cAA+C,SACzC;IAAAD,EAAA,CAAAgC,MAAA,uBAAgB;IAAAhC,EAAA,CAAAc,YAAA,EAAK;IAGvBd,EADF,CAAAC,cAAA,cAA0B,iBAC+B;IAA3BD,EAAA,CAAAsB,UAAA,mBAAAmD,6DAAA;MAAAzE,EAAA,CAAAK,aAAA,CAAAqE,GAAA;MAAA,MAAAhE,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAAiE,cAAA,EAAgB;IAAA,EAAC;IAAC3E,EAAA,CAAAgC,MAAA,mBAAY;IAAAhC,EAAA,CAAAc,YAAA,EAAS;IAC5Ed,EAAA,CAAAC,cAAA,iBAAoD;IAAxBD,EAAA,CAAAsB,UAAA,mBAAAsD,6DAAA;MAAA5E,EAAA,CAAAK,aAAA,CAAAqE,GAAA;MAAA,MAAAhE,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAAmE,WAAA,EAAa;IAAA,EAAC;IAAC7E,EAAA,CAAAgC,MAAA,oBAAa;IACnEhC,EADmE,CAAAc,YAAA,EAAS,EACtE;IAENd,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAsC,UAAA,IAAAwC,0CAAA,oBAAuE;IA4DvE9E,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAmD,SAAA,oBAA0D;IAC1DnD,EAAA,CAAAC,cAAA,kBAAgF;IAAxED,EAAA,CAAAsB,UAAA,mBAAAyD,8DAAA;MAAA/E,EAAA,CAAAK,aAAA,CAAAqE,GAAA;MAAA,MAAAM,gBAAA,GAAAhF,EAAA,CAAAiF,WAAA;MAAA,MAAAvE,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAE,MAAA,CAAAE,WAAA,CAAAoE,gBAAA,CAAAE,KAAA,IAA2C,EAAE;MAAA,OAAAlF,EAAA,CAAAa,WAAA,CAAAmE,gBAAA,CAAAE,KAAA,GAAuB,EAAE;IAAA,EAAC;IAAClF,EAAA,CAAAgC,MAAA,iBAAS;IAG/FhC,EAH+F,CAAAc,YAAA,EAAS,EAC9F,EACF,EACF;;;;IAjEsCd,EAAA,CAAAiC,SAAA,GAA6B;IAA7BjC,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAAyE,aAAA,CAAAzE,MAAA,CAAAE,WAAA,EAA6B;;;;;IAuErEZ,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAgC,MAAA,GAAe;IAAAhC,EAAA,CAAAc,YAAA,EAAM;;;;IAArBd,EAAA,CAAAiC,SAAA,EAAe;IAAfjC,EAAA,CAAAkC,iBAAA,CAAAxB,MAAA,CAAA0E,SAAA,CAAe;;;;;;IADzDpF,EAFJ,CAAAC,cAAA,cAAsD,cACnB,SAC3B;IAAAD,EAAA,CAAAgC,MAAA,mBAAY;IAAAhC,EAAA,CAAAc,YAAA,EAAK;IACrBd,EAAA,CAAAsC,UAAA,IAAA+C,0CAAA,kBAA0C;IAC1CrF,EAAA,CAAAC,cAAA,mBAAoD;IAA1CD,EAAA,CAAAE,gBAAA,2BAAAoF,uEAAAlF,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAkF,IAAA;MAAA,MAAA7E,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAW,kBAAA,CAAAD,MAAA,CAAA8E,iBAAA,EAAApF,MAAA,MAAAM,MAAA,CAAA8E,iBAAA,GAAApF,MAAA;MAAA,OAAAJ,EAAA,CAAAa,WAAA,CAAAT,MAAA;IAAA,EAA+B;IAAWJ,EAAA,CAAAc,YAAA,EAAW;IAE7Dd,EADF,CAAAC,cAAA,cAA0B,iBACiC;IAA3BD,EAAA,CAAAsB,UAAA,mBAAAmE,6DAAA;MAAAzF,EAAA,CAAAK,aAAA,CAAAkF,IAAA;MAAA,MAAA7E,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAAgF,cAAA,EAAgB;IAAA,EAAC;IAAC1F,EAAA,CAAAgC,MAAA,aAAM;IAAAhC,EAAA,CAAAc,YAAA,EAAS;IACxEd,EAAA,CAAAC,cAAA,iBAA0D;IAA7BD,EAAA,CAAAsB,UAAA,mBAAAqE,6DAAA;MAAA3F,EAAA,CAAAK,aAAA,CAAAkF,IAAA;MAAA,MAAA7E,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAAkF,gBAAA,EAAkB;IAAA,EAAC;IAAC5F,EAAA,CAAAgC,MAAA,qBAAa;IAG7EhC,EAH6E,CAAAc,YAAA,EAAS,EAC5E,EACF,EACF;;;;IAPuBd,EAAA,CAAAiC,SAAA,GAAe;IAAfjC,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAA0E,SAAA,CAAe;IAC9BpF,EAAA,CAAAiC,SAAA,EAA+B;IAA/BjC,EAAA,CAAAe,gBAAA,YAAAL,MAAA,CAAA8E,iBAAA,CAA+B;;;;;IAU3CxF,EAAA,CAAAC,cAAA,cAAuC;IACrCD,EAAA,CAAAmD,SAAA,kBAA2B;IAC3BnD,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAgC,MAAA,2BAAoB;IACzBhC,EADyB,CAAAc,YAAA,EAAI,EACvB;;;;;IAGJd,EADF,CAAAC,cAAA,cAAkE,QAC7D;IAAAD,EAAA,CAAAgC,MAAA,6CAAsC;IAC3ChC,EAD2C,CAAAc,YAAA,EAAI,EACzC;;;;;IAgBId,EADF,CAAAC,cAAA,cAAgG,eACrE;IAAAD,EAAA,CAAAgC,MAAA,GAAU;IAAAhC,EAAA,CAAAc,YAAA,EAAO;IAC1Cd,EAAA,CAAAmD,SAAA,eAAuF;IACzFnD,EAAA,CAAAc,YAAA,EAAM;;;;;;IAHkEd,EAAA,CAAA0B,UAAA,WAAAmE,OAAA,UAAuB;IACpE7F,EAAA,CAAAiC,SAAA,GAAU;IAAVjC,EAAA,CAAA2C,kBAAA,KAAAkD,OAAA,MAAU;IACT7F,EAAA,CAAAiC,SAAA,EAAqD;IAArDjC,EAAA,CAAA0B,UAAA,cAAAhB,MAAA,CAAAoF,wBAAA,CAAAC,YAAA,EAAAF,OAAA,GAAA7F,EAAA,CAAAgG,cAAA,CAAqD;;;;;;IAVjFhG,EAFJ,CAAAC,cAAA,cAAuE,cACxC,SACvB;IAAAD,EAAA,CAAAgC,MAAA,GAAqB;IAAAhC,EAAA,CAAAc,YAAA,EAAK;IAE5Bd,EADF,CAAAC,cAAA,cAA8B,iBACiC;IAAjCD,EAAA,CAAAsB,UAAA,mBAAA2E,yEAAA;MAAAjG,EAAA,CAAAK,aAAA,CAAA6F,IAAA;MAAA,MAAAH,YAAA,GAAA/F,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAAyF,YAAA,CAAAJ,YAAA,CAAsB;IAAA,EAAC;IAAC/F,EAAA,CAAAgC,MAAA,WAAI;IAAAhC,EAAA,CAAAc,YAAA,EAAS;IAC1Ed,EAAA,CAAAC,cAAA,iBAAoE;IAAtCD,EAAA,CAAAsB,UAAA,mBAAA8E,yEAAA;MAAApG,EAAA,CAAAK,aAAA,CAAA6F,IAAA;MAAA,MAAAH,YAAA,GAAA/F,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAA2F,cAAA,CAAAN,YAAA,CAAArD,EAAA,CAA2B;IAAA,EAAC;IAAC1C,EAAA,CAAAgC,MAAA,aAAM;IAE9EhC,EAF8E,CAAAc,YAAA,EAAS,EAC/E,EACF;IAENd,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAsC,UAAA,KAAAgE,uDAAA,kBAAgG;IAKpGtG,EADE,CAAAc,YAAA,EAAM,EACF;;;;;IAbEd,EAAA,CAAAiC,SAAA,GAAqB;IAArBjC,EAAA,CAAA2C,kBAAA,SAAAoD,YAAA,CAAArD,EAAA,KAAqB;IAQmB1C,EAAA,CAAAiC,SAAA,GAA0B;IAA1BjC,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAAyE,aAAA,CAAAY,YAAA,EAA0B;;;;;;IAwBlE/F,EAAA,CAAAC,cAAA,gBAA6E;IAAnCD,EAAA,CAAAE,gBAAA,2BAAAqG,+FAAAnG,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAmG,IAAA;MAAA,MAAAC,OAAA,GAAAzG,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAW,kBAAA,CAAAD,MAAA,CAAAgG,eAAA,CAAAD,OAAA,GAAArG,MAAA,MAAAM,MAAA,CAAAgG,eAAA,CAAAD,OAAA,IAAArG,MAAA;MAAA,OAAAJ,EAAA,CAAAa,WAAA,CAAAT,MAAA;IAAA,EAAkC;IAA5EJ,EAAA,CAAAc,YAAA,EAA6E;;;;;IAAnCd,EAAA,CAAAe,gBAAA,YAAAL,MAAA,CAAAgG,eAAA,CAAAD,OAAA,EAAkC;;;;;;IAG5EzG,EAAA,CAAAC,cAAA,gBAA+E;IAAnCD,EAAA,CAAAE,gBAAA,2BAAAyG,+FAAAvG,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAuG,IAAA;MAAA,MAAAH,OAAA,GAAAzG,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAW,kBAAA,CAAAD,MAAA,CAAAgG,eAAA,CAAAD,OAAA,GAAArG,MAAA,MAAAM,MAAA,CAAAgG,eAAA,CAAAD,OAAA,IAAArG,MAAA;MAAA,OAAAJ,EAAA,CAAAa,WAAA,CAAAT,MAAA;IAAA,EAAkC;IAA9EJ,EAAA,CAAAc,YAAA,EAA+E;;;;;IAAnCd,EAAA,CAAAe,gBAAA,YAAAL,MAAA,CAAAgG,eAAA,CAAAD,OAAA,EAAkC;;;;;;IAG9EzG,EAAA,CAAAC,cAAA,gBAAqF;IAAnCD,EAAA,CAAAE,gBAAA,2BAAA2G,+FAAAzG,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAyG,IAAA;MAAA,MAAAL,OAAA,GAAAzG,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAW,kBAAA,CAAAD,MAAA,CAAAgG,eAAA,CAAAD,OAAA,GAAArG,MAAA,MAAAM,MAAA,CAAAgG,eAAA,CAAAD,OAAA,IAAArG,MAAA;MAAA,OAAAJ,EAAA,CAAAa,WAAA,CAAAT,MAAA;IAAA,EAAkC;IAApFJ,EAAA,CAAAc,YAAA,EAAqF;;;;;IAAnCd,EAAA,CAAAe,gBAAA,YAAAL,MAAA,CAAAgG,eAAA,CAAAD,OAAA,EAAkC;;;;;;IAGpFzG,EAAA,CAAAC,cAAA,gBAAiF;IAAnCD,EAAA,CAAAE,gBAAA,2BAAA6G,+FAAA3G,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA2G,IAAA;MAAA,MAAAP,OAAA,GAAAzG,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAW,kBAAA,CAAAD,MAAA,CAAAgG,eAAA,CAAAD,OAAA,GAAArG,MAAA,MAAAM,MAAA,CAAAgG,eAAA,CAAAD,OAAA,IAAArG,MAAA;MAAA,OAAAJ,EAAA,CAAAa,WAAA,CAAAT,MAAA;IAAA,EAAkC;IAAhFJ,EAAA,CAAAc,YAAA,EAAiF;;;;;IAAnCd,EAAA,CAAAe,gBAAA,YAAAL,MAAA,CAAAgG,eAAA,CAAAD,OAAA,EAAkC;;;;;;IAGhFzG,EAAA,CAAAC,cAAA,gBACuE;IAAjED,EAAA,CAAAsB,UAAA,2BAAA2F,+FAAA7G,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA6G,IAAA;MAAA,MAAAT,OAAA,GAAAzG,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAa,WAAA,CAAiBH,MAAA,CAAAe,gBAAA,CAAAf,MAAA,CAAAgG,eAAA,EAAAD,OAAA,EAAArG,MAAA,CAA8C;IAAA,EAAC;IADtEJ,EAAA,CAAAc,YAAA,EACuE;;;;;IAD7Bd,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAAiB,kBAAA,CAAAjB,MAAA,CAAAgG,eAAA,CAAAD,OAAA,GAAoD;;;;;;IAI9FzG,EAAA,CAAAC,cAAA,mBAAiF;IAAnCD,EAAA,CAAAE,gBAAA,2BAAAiH,qGAAA/G,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA+G,IAAA;MAAA,MAAAX,OAAA,GAAAzG,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAW,kBAAA,CAAAD,MAAA,CAAAgG,eAAA,CAAAD,OAAA,GAAArG,MAAA,MAAAM,MAAA,CAAAgG,eAAA,CAAAD,OAAA,IAAArG,MAAA;MAAA,OAAAJ,EAAA,CAAAa,WAAA,CAAAT,MAAA;IAAA,EAAkC;IAACJ,EAAA,CAAAc,YAAA,EAAW;;;;;IAA9Cd,EAAA,CAAAe,gBAAA,YAAAL,MAAA,CAAAgG,eAAA,CAAAD,OAAA,EAAkC;;;;;;IAI9EzG,EADF,CAAAC,cAAA,cAAsD,gBAC2B;IAAxDD,EAAA,CAAAE,gBAAA,2BAAAmH,8FAAAjH,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAiH,IAAA;MAAA,MAAAb,OAAA,GAAAzG,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAW,kBAAA,CAAAD,MAAA,CAAAgG,eAAA,CAAAD,OAAA,GAAArG,MAAA,MAAAM,MAAA,CAAAgG,eAAA,CAAAD,OAAA,IAAArG,MAAA;MAAA,OAAAJ,EAAA,CAAAa,WAAA,CAAAT,MAAA;IAAA,EAAkC;IAAzDJ,EAAA,CAAAc,YAAA,EAA+E;IAC/Ed,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAgC,MAAA,GAAyC;IACxEhC,EADwE,CAAAc,YAAA,EAAQ,EAC1E;;;;;IAFmBd,EAAA,CAAAiC,SAAA,EAAkC;IAAlCjC,EAAA,CAAAe,gBAAA,YAAAL,MAAA,CAAAgG,eAAA,CAAAD,OAAA,EAAkC;IAACzG,EAAA,CAAA0B,UAAA,iBAAA+E,OAAA,CAAoB;IACvEzG,EAAA,CAAAiC,SAAA,EAAqB;IAArBjC,EAAA,CAAA0B,UAAA,kBAAA+E,OAAA,CAAqB;IAACzG,EAAA,CAAAiC,SAAA,EAAyC;IAAzCjC,EAAA,CAAAkC,iBAAA,CAAAxB,MAAA,CAAAgG,eAAA,CAAAD,OAAA,iBAAyC;;;;;IAMtEzG,EAAA,CAAAC,cAAA,iBAAsE;IAAAD,EAAA,CAAAgC,MAAA,GAAY;IAAAhC,EAAA,CAAAc,YAAA,EAAS;;;;IAAtCd,EAAA,CAAA0B,UAAA,UAAA6F,UAAA,CAAgB;IAACvH,EAAA,CAAAiC,SAAA,EAAY;IAAZjC,EAAA,CAAAkC,iBAAA,CAAAqF,UAAA,CAAY;;;;;;IAFpFvH,EAAA,CAAAC,cAAA,iBAAoE;IAAnCD,EAAA,CAAAE,gBAAA,2BAAAsH,kGAAApH,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAoH,IAAA;MAAA,MAAAhB,OAAA,GAAAzG,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAW,kBAAA,CAAAD,MAAA,CAAAgG,eAAA,CAAAD,OAAA,GAAArG,MAAA,MAAAM,MAAA,CAAAgG,eAAA,CAAAD,OAAA,IAAArG,MAAA;MAAA,OAAAJ,EAAA,CAAAa,WAAA,CAAAT,MAAA;IAAA,EAAkC;IACjEJ,EAAA,CAAAC,cAAA,iBAAiB;IAAAD,EAAA,CAAAgC,MAAA,mBAAY;IAAAhC,EAAA,CAAAc,YAAA,EAAS;IACtCd,EAAA,CAAAsC,UAAA,IAAAoF,0EAAA,qBAAsE;IACxE1H,EAAA,CAAAc,YAAA,EAAS;;;;;IAHwBd,EAAA,CAAAe,gBAAA,YAAAL,MAAA,CAAAgG,eAAA,CAAAD,OAAA,EAAkC;IAEtCzG,EAAA,CAAAiC,SAAA,GAAwB;IAAxBjC,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAA8B,gBAAA,CAAAiE,OAAA,EAAwB;;;;;IAMnDzG,EAAA,CAAAC,cAAA,iBAA6E;IAC3ED,EAAA,CAAAgC,MAAA,GACF;IAAAhC,EAAA,CAAAc,YAAA,EAAS;;;;IAFgDd,EAAA,CAAA0B,UAAA,UAAAiG,UAAA,CAAAjF,EAAA,CAAmB;IAC1E1C,EAAA,CAAAiC,SAAA,EACF;IADEjC,EAAA,CAAA2C,kBAAA,MAAAgF,UAAA,CAAA/E,OAAA,MACF;;;;;;IAJF5C,EAAA,CAAAC,cAAA,iBAAyE;IAAnCD,EAAA,CAAAE,gBAAA,2BAAA0H,kGAAAxH,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAwH,IAAA;MAAA,MAAApB,OAAA,GAAAzG,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAW,kBAAA,CAAAD,MAAA,CAAAgG,eAAA,CAAAD,OAAA,GAAArG,MAAA,MAAAM,MAAA,CAAAgG,eAAA,CAAAD,OAAA,IAAArG,MAAA;MAAA,OAAAJ,EAAA,CAAAa,WAAA,CAAAT,MAAA;IAAA,EAAkC;IACtEJ,EAAA,CAAAC,cAAA,iBAAiB;IAAAD,EAAA,CAAAgC,MAAA,mBAAY;IAAAhC,EAAA,CAAAc,YAAA,EAAS;IACtCd,EAAA,CAAAsC,UAAA,IAAAwF,0EAAA,qBAA6E;IAG/E9H,EAAA,CAAAc,YAAA,EAAS;;;;;IAL6Bd,EAAA,CAAAe,gBAAA,YAAAL,MAAA,CAAAgG,eAAA,CAAAD,OAAA,EAAkC;IAE3CzG,EAAA,CAAAiC,SAAA,GAA4B;IAA5BjC,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAAsC,oBAAA,CAAAyD,OAAA,EAA4B;;;;;;IAMzDzG,EAAA,CAAAC,cAAA,gBAA+E;IAAnCD,EAAA,CAAAE,gBAAA,2BAAA6H,gGAAA3H,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA2H,IAAA;MAAA,MAAAvB,OAAA,GAAAzG,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAW,kBAAA,CAAAD,MAAA,CAAAgG,eAAA,CAAAD,OAAA,GAAArG,MAAA,MAAAM,MAAA,CAAAgG,eAAA,CAAAD,OAAA,IAAArG,MAAA;MAAA,OAAAJ,EAAA,CAAAa,WAAA,CAAAT,MAAA;IAAA,EAAkC;IAA9EJ,EAAA,CAAAc,YAAA,EAA+E;;;;;IAAnCd,EAAA,CAAAe,gBAAA,YAAAL,MAAA,CAAAgG,eAAA,CAAAD,OAAA,EAAkC;;;;;IAK5EzG,EAAA,CAAAC,cAAA,cAAuD;IACrDD,EAAA,CAAAmD,SAAA,cAAgD;IAClDnD,EAAA,CAAAc,YAAA,EAAM;;;;;IADCd,EAAA,CAAAiC,SAAA,EAA4B;IAA5BjC,EAAA,CAAA0B,UAAA,QAAAhB,MAAA,CAAAgG,eAAA,CAAAD,OAAA,GAAAzG,EAAA,CAAAoD,aAAA,CAA4B;;;;;;IAFnCpD,EADF,CAAAC,cAAA,cAAyD,gBACuC;IAA3ED,EAAA,CAAAsB,UAAA,oBAAA2G,uFAAA7H,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA6H,IAAA;MAAA,MAAAzB,OAAA,GAAAzG,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAa,WAAA,CAAUH,MAAA,CAAA6C,gBAAA,CAAAnD,MAAA,EAAAM,MAAA,CAAAgG,eAAA,EAAAD,OAAA,CAA8C;IAAA,EAAC;IAA5EzG,EAAA,CAAAc,YAAA,EAA8F;IAC9Fd,EAAA,CAAAsC,UAAA,IAAA6F,oEAAA,kBAAuD;IAGzDnI,EAAA,CAAAc,YAAA,EAAM;;;;;IAHEd,EAAA,CAAAiC,SAAA,GAA0B;IAA1BjC,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAAgG,eAAA,CAAAD,OAAA,EAA0B;;;;;;IAMlCzG,EAAA,CAAAC,cAAA,gBAAuE;IAAnCD,EAAA,CAAAE,gBAAA,2BAAAkI,gGAAAhI,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAgI,IAAA;MAAA,MAAA5B,OAAA,GAAAzG,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAW,kBAAA,CAAAD,MAAA,CAAAgG,eAAA,CAAAD,OAAA,GAAArG,MAAA,MAAAM,MAAA,CAAAgG,eAAA,CAAAD,OAAA,IAAArG,MAAA;MAAA,OAAAJ,EAAA,CAAAa,WAAA,CAAAT,MAAA;IAAA,EAAkC;IAAtEJ,EAAA,CAAAc,YAAA,EAAuE;;;;;IAAnCd,EAAA,CAAAe,gBAAA,YAAAL,MAAA,CAAAgG,eAAA,CAAAD,OAAA,EAAkC;;;;;IAvDxEzG,EADF,CAAAC,cAAA,cAAmG,YAC1F;IAAAD,EAAA,CAAAgC,MAAA,GAAS;IAAAhC,EAAA,CAAAc,YAAA,EAAQ;IAGxBd,EAAA,CAAA2D,uBAAA,OAA6C;IAoD3C3D,EAlDA,CAAAsC,UAAA,IAAAgG,+DAAA,oBAA6E,IAAAC,+DAAA,oBAGE,IAAAC,+DAAA,oBAGM,IAAAC,+DAAA,oBAGJ,IAAAC,+DAAA,oBAIV,IAAAC,kEAAA,uBAGU,KAAAC,8DAAA,kBAG3B,KAAAC,iEAAA,qBAMc,KAAAC,iEAAA,qBAMK,KAAAC,gEAAA,oBAQM,KAAAC,8DAAA,kBAGtB,KAAAC,gEAAA,oBAQc;;IAE3EjJ,EAAA,CAAAc,YAAA,EAAM;;;;;IA1DqEd,EAAA,CAAA0B,UAAA,WAAA+E,OAAA,UAAuB;IACzFzG,EAAA,CAAAiC,SAAA,GAAS;IAATjC,EAAA,CAAAkC,iBAAA,CAAAuE,OAAA,CAAS;IAGFzG,EAAA,CAAAiC,SAAA,EAA8B;IAA9BjC,EAAA,CAAA0B,UAAA,aAAAhB,MAAA,CAAA8D,YAAA,CAAAiC,OAAA,EAA8B;IAElCzG,EAAA,CAAAiC,SAAA,EAAoB;IAApBjC,EAAA,CAAA0B,UAAA,wBAAoB;IAGpB1B,EAAA,CAAAiC,SAAA,EAAqB;IAArBjC,EAAA,CAAA0B,UAAA,yBAAqB;IAGrB1B,EAAA,CAAAiC,SAAA,EAAwB;IAAxBjC,EAAA,CAAA0B,UAAA,4BAAwB;IAGxB1B,EAAA,CAAAiC,SAAA,EAAsB;IAAtBjC,EAAA,CAAA0B,UAAA,0BAAsB;IAGtB1B,EAAA,CAAAiC,SAAA,EAAoB;IAApBjC,EAAA,CAAA0B,UAAA,wBAAoB;IAIjB1B,EAAA,CAAAiC,SAAA,EAAwB;IAAxBjC,EAAA,CAAA0B,UAAA,4BAAwB;IAG7B1B,EAAA,CAAAiC,SAAA,EAAuB;IAAvBjC,EAAA,CAAA0B,UAAA,2BAAuB;IAMpB1B,EAAA,CAAAiC,SAAA,EAAsB;IAAtBjC,EAAA,CAAA0B,UAAA,0BAAsB;IAMtB1B,EAAA,CAAAiC,SAAA,EAA2B;IAA3BjC,EAAA,CAAA0B,UAAA,+BAA2B;IAQ5B1B,EAAA,CAAAiC,SAAA,EAAqB;IAArBjC,EAAA,CAAA0B,UAAA,yBAAqB;IAGvB1B,EAAA,CAAAiC,SAAA,EAAoB;IAApBjC,EAAA,CAAA0B,UAAA,wBAAoB;;;;;;IAzDhC1B,EADF,CAAAC,cAAA,cAAuE,SACjE;IAAAD,EAAA,CAAAgC,MAAA,uBAAgB;IAAAhC,EAAA,CAAAc,YAAA,EAAK;IAGvBd,EADF,CAAAC,cAAA,cAA0B,iBACuC;IAAnCD,EAAA,CAAAsB,UAAA,mBAAA4H,yEAAA;MAAAlJ,EAAA,CAAAK,aAAA,CAAA8I,IAAA;MAAA,MAAApD,YAAA,GAAA/F,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAAiE,cAAA,CAAAoB,YAAA,CAAwB;IAAA,EAAC;IAAC/F,EAAA,CAAAgC,MAAA,mBAAY;IAAAhC,EAAA,CAAAc,YAAA,EAAS;IACpFd,EAAA,CAAAC,cAAA,iBAAqD;IAAvBD,EAAA,CAAAsB,UAAA,mBAAA8H,yEAAA;MAAApJ,EAAA,CAAAK,aAAA,CAAA8I,IAAA;MAAA,MAAAzI,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAA2I,UAAA,EAAY;IAAA,EAAC;IAACrJ,EAAA,CAAAgC,MAAA,aAAM;IAAAhC,EAAA,CAAAc,YAAA,EAAS;IACpEd,EAAA,CAAAC,cAAA,iBAAuD;IAA3BD,EAAA,CAAAsB,UAAA,mBAAAgI,yEAAA;MAAAtJ,EAAA,CAAAK,aAAA,CAAA8I,IAAA;MAAA,MAAAzI,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAA6I,cAAA,EAAgB;IAAA,EAAC;IAACvJ,EAAA,CAAAgC,MAAA,mBAAY;IACrEhC,EADqE,CAAAc,YAAA,EAAS,EACxE;IAENd,EAAA,CAAAC,cAAA,eAAyB;IACvBD,EAAA,CAAAsC,UAAA,KAAAkH,uDAAA,oBAAmG;IA4DnGxJ,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAmD,SAAA,oBAA8D;IAC9DnD,EAAA,CAAAC,cAAA,kBAA4F;IAApFD,EAAA,CAAAsB,UAAA,mBAAAmI,0EAAA;MAAAzJ,EAAA,CAAAK,aAAA,CAAA8I,IAAA;MAAA,MAAAO,oBAAA,GAAA1J,EAAA,CAAAiF,WAAA;MAAA,MAAAvE,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAE,MAAA,CAAAgG,eAAA,CAAAgD,oBAAA,CAAAxE,KAAA,IAAmD,EAAE;MAAA,OAAAlF,EAAA,CAAAa,WAAA,CAAA6I,oBAAA,CAAAxE,KAAA,GAA2B,EAAE;IAAA,EAAC;IAAClF,EAAA,CAAAgC,MAAA,iBAAS;IAG3GhC,EAH2G,CAAAc,YAAA,EAAS,EAC1G,EACF,EACF;;;;IAjEsCd,EAAA,CAAAiC,SAAA,IAAiC;IAAjCjC,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAAyE,aAAA,CAAAzE,MAAA,CAAAgG,eAAA,EAAiC;;;;;IA9B/E1G,EAAA,CAAAC,cAAA,cAA8D;IAoB5DD,EAlBA,CAAAsC,UAAA,IAAAqH,gDAAA,mBAAuE,IAAAC,gDAAA,mBAkBA;IA4EzE5J,EAAA,CAAAc,YAAA,EAAM;;;;;IA9FEd,EAAA,CAAAiC,SAAA,EAAyC;IAAzCjC,EAAA,CAAA0B,UAAA,UAAAhB,MAAA,CAAAgG,eAAA,kBAAAhG,MAAA,CAAAgG,eAAA,CAAAhE,EAAA,MAAAqD,YAAA,CAAArD,EAAA,CAAyC;IAkBzC1C,EAAA,CAAAiC,SAAA,EAAyC;IAAzCjC,EAAA,CAAA0B,UAAA,UAAAhB,MAAA,CAAAgG,eAAA,kBAAAhG,MAAA,CAAAgG,eAAA,CAAAhE,EAAA,MAAAqD,YAAA,CAAArD,EAAA,CAAyC;;;;;IArBnD1C,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAsC,UAAA,IAAAuH,0CAAA,kBAA8D;IAiGhE7J,EAAA,CAAAc,YAAA,EAAM;;;;IAjG4Cd,EAAA,CAAAiC,SAAA,EAAY;IAAZjC,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAAoJ,SAAA,CAAY;;;ADxGpE,OAAM,MAAOC,oBAAoB;EAY/BC,YACUC,KAAqB,EACrBC,MAAc,EACdC,YAA0B,EAC1BC,eAAgC;IAHhC,KAAAH,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,eAAe,GAAfA,eAAe;IAfzB,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAP,SAAS,GAAU,EAAE;IACrB,KAAAQ,SAAS,GAAG,IAAI;IAChB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAA3J,WAAW,GAAQ,EAAE;IACrB,KAAA8F,eAAe,GAAQ,IAAI;IAC3B,KAAA8D,cAAc,GAAG,KAAK;IACtB,KAAAhF,iBAAiB,GAAG,EAAE;IACtB,KAAAJ,SAAS,GAAG,EAAE;IACd,KAAAqF,iBAAiB,GAAoD,EAAE;IA8EvE,KAAAC,aAAa,GAA4B,EAAE;IAE3C,KAAAC,gBAAgB,GAA8B,EAAE;EAzE7C;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACX,KAAK,CAACY,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAI,CAACV,cAAc,GAAGU,MAAM,CAACC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE;MAC9C,IAAI,IAAI,CAACX,cAAc,EAAE;QACvB,IAAI,CAACY,aAAa,EAAE;MACtB,CAAC,MAAM;QACL,IAAI,CAACf,MAAM,CAACgB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAClC;IACF,CAAC,CAAC;EACJ;EAEAD,aAAaA,CAAA;IACX,IAAI,CAACX,SAAS,GAAG,IAAI;IACrB,IAAI,CAACH,YAAY,CAACgB,aAAa,CAAC,IAAI,CAACd,cAAc,CAAC,CAACS,SAAS,CAAC;MAC7DM,IAAI,EAAGtB,SAAS,IAAI;QAClB,IAAI,CAACA,SAAS,GAAGA,SAAS;QAC1B,IAAI,CAACQ,SAAS,GAAG,KAAK;MACxB,CAAC;MACDe,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACf,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEAgB,aAAaA,CAAA;IACX,IAAI,CAACf,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IACpC,IAAI,IAAI,CAACA,WAAW,EAAE;MACpB,IAAI,CAAC3J,WAAW,GAAG,IAAI,CAACuJ,YAAY,CAACoB,gBAAgB,CAAC,IAAI,CAAClB,cAAc,CAAC;MAE1E,IAAI,CAACmB,qBAAqB,EAAE;IAC9B;EACF;EAEAA,qBAAqBA,CAAA;IAEnB,MAAMC,UAAU,GAAG,IAAI,CAACtB,YAAY,CAACuB,aAAa,CAAC,IAAI,CAACrB,cAAc,CAAC;IAEvE,MAAMsB,gBAAgB,GAAGC,MAAM,CAACC,IAAI,CAACJ,UAAU,CAAC,CAACK,MAAM,CACrDC,SAAS,IAAIN,UAAU,CAACM,SAAS,CAAC,KAAK,aAAa,CACrD;IAED,IAAIJ,gBAAgB,CAACK,MAAM,KAAK,CAAC,EAAE;MACjC;IACF;IAQA,MAAMC,WAAW,GAAGN,gBAAgB,CAAC5L,GAAG,CAACgM,SAAS,IAAG;MACnD,OAAO,IAAI,CAAC5B,YAAY,CAACnH,oBAAoB,CAAC+I,SAAS,CAAC,CAACG,IAAI,CAC3DnM,GAAG,CAACoM,OAAO,KAAK;QAAEJ,SAAS;QAAEI;MAAO,CAAuB,EAAC,CAC7D;IACH,CAAC,CAAC;IAEFrM,QAAQ,CAACmM,WAAW,CAAC,CAACnB,SAAS,CAAC;MAC9BM,IAAI,EAAGgB,OAA2B,IAAI;QACpCA,OAAO,CAACC,OAAO,CAACC,MAAM,IAAG;UACvB,IAAI,CAAC7B,iBAAiB,CAAC6B,MAAM,CAACP,SAAS,CAAC,GAAGO,MAAM,CAACH,OAAO;QAC3D,CAAC,CAAC;MAEJ,CAAC;MACDd,KAAK,EAAGA,KAAK,IAAI,CACjB;KACD,CAAC;EACJ;EAMA9H,gBAAgBA,CAACgJ,KAAY,EAAEC,QAAa,EAAET,SAAiB;IAC7D,MAAMU,KAAK,GAAGF,KAAK,CAACG,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACE,KAAK,CAACX,MAAM,GAAG,CAAC,EAAE;MACzC,MAAMY,IAAI,GAAGH,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;MAE3B,IAAI,IAAI,CAACtC,cAAc,KAAK,kBAAkB,IAAI0B,SAAS,KAAK,aAAa,EAAE;QAC7E,MAAMc,UAAU,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,CAAC;QAC5D,IAAI,CAACA,UAAU,CAACC,QAAQ,CAACF,IAAI,CAACG,IAAI,CAAC,EAAE;UACnCC,KAAK,CAAC,6DAA6D,CAAC;UACpE;QACF;QAEA,IAAIJ,IAAI,CAACK,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE;UAChCD,KAAK,CAAC,4CAA4C,CAAC;UACnD;QACF;QAEA,IAAI,CAACtC,aAAa,CAAC,GAAG8B,QAAQ,CAAC9J,EAAE,IAAI,KAAK,IAAIqJ,SAAS,EAAE,CAAC,GAAGa,IAAI;QAEjE,MAAMM,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAI;UAAA,IAAAC,SAAA;UACpB,MAAMC,OAAO,IAAAD,SAAA,GAAGD,CAAC,CAACX,MAAM,cAAAY,SAAA,uBAARA,SAAA,CAAUhB,MAAgB;UAC1C,IAAI,CAAC3B,gBAAgB,CAAC,GAAG6B,QAAQ,CAAC9J,EAAE,IAAI,KAAK,IAAIqJ,SAAS,EAAE,CAAC,GAAGwB,OAAO;UAEvEf,QAAQ,CAACT,SAAS,CAAC,GAAGwB,OAAO;QAC/B,CAAC;QACDL,MAAM,CAACM,aAAa,CAACZ,IAAI,CAAC;MAE5B,CAAC,MAAM;QACLJ,QAAQ,CAACT,SAAS,CAAC,GAAGa,IAAI;MAC5B;IACF;EACF;EAEM/H,WAAWA,CAAA;IAAA,IAAA4I,KAAA;IAAA,OAAAC,iBAAA;MACfD,KAAI,CAACnD,SAAS,GAAG,IAAI;MAErB,IAAI;QACF,MAAMqD,iBAAiB,GAAGF,KAAI,CAACG,oBAAoB,CAACH,KAAI,CAAC7M,WAAW,CAAC;QAErE,MAAMiN,OAAO,GAAG,iBAAiB;QACjC,IAAIJ,KAAI,CAAC/C,aAAa,CAACmD,OAAO,CAAC,IAAIJ,KAAI,CAACpD,cAAc,KAAK,kBAAkB,EAAE;UAC7E,MAAMuC,IAAI,GAAGa,KAAI,CAAC/C,aAAa,CAACmD,OAAO,CAAC;UAExC,MAAM;YAAEC,IAAI,EAAEC,QAAQ;YAAE1C,KAAK,EAAE2C;UAAS,CAAE,SAASP,KAAI,CAACrD,eAAe,CAAC6D,SAAS,EAAE,CAChFC,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,UAAU,CAAC,CAClBC,EAAE,CAAC,IAAI,EAAEX,KAAI,CAACrD,eAAe,CAACiE,gBAAgB,EAAE,CAAC,CACjDC,MAAM,EAAE;UAEX,IAAIN,SAAS,EAAE;YACb,MAAM,IAAIO,KAAK,CAAC,gCAAgCP,SAAS,CAACQ,OAAO,EAAE,CAAC;UACtE;UAEA,IAAI,CAAAT,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEU,QAAQ,MAAK,OAAO,EAAE;YAClC,MAAM,IAAIF,KAAK,CAAC,mDAAmD,CAAC;UACtE;UAEA,MAAMG,QAAQ,GAAG,GAAGC,IAAI,CAACC,GAAG,EAAE,IAAIhC,IAAI,CAACiC,IAAI,CAACC,OAAO,CAAC,iBAAiB,EAAE,GAAG,CAAC,EAAE;UAG7E,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;UAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAErC,IAAI,CAAC;UAG7B,MAAM;YAAEkB,IAAI,EAAE;cAAEoB;YAAO;UAAE,CAAE,SAASzB,KAAI,CAACrD,eAAe,CAAC6D,SAAS,EAAE,CAACkB,IAAI,CAACC,UAAU,EAAE;UACtF,MAAMC,WAAW,GAAGH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEI,YAAY;UAEzC,IAAI,CAACD,WAAW,EAAE;YAChB,MAAM,IAAId,KAAK,CAAC,2BAA2B,CAAC;UAC9C;UAEA,MAAMgB,aAAa,GAAG,IAAIC,OAAO,CAAwB,CAACC,OAAO,EAAEC,MAAM,KAAI;YAC3E,MAAMC,GAAG,GAAG,IAAIC,cAAc,EAAE;YAEhC,MAAMC,WAAW,GAAG,IAAIC,eAAe,CAAC;cACtC,cAAc,EAAE,MAAM;cACtB,QAAQ,EAAE;aACX,CAAC;YAEFH,GAAG,CAACI,IAAI,CAAC,MAAM,EAAE,GAAGtC,KAAI,CAACrD,eAAe,CAAC4F,OAAO,CAACC,GAAG,sCAAsCvB,QAAQ,IAAImB,WAAW,CAACK,QAAQ,EAAE,EAAE,CAAC;YAE/HP,GAAG,CAACQ,gBAAgB,CAAC,eAAe,EAAE,UAAUd,WAAW,EAAE,CAAC;YAC9DM,GAAG,CAACQ,gBAAgB,CAAC,QAAQ,EAAE1C,KAAI,CAACrD,eAAe,CAACgG,WAAW,CAAC;YAEhET,GAAG,CAACvC,MAAM,GAAG,MAAK;cAChB,IAAIuC,GAAG,CAACU,MAAM,IAAI,GAAG,IAAIV,GAAG,CAACU,MAAM,GAAG,GAAG,EAAE;gBACzC,MAAM;kBAAEvC,IAAI,EAAEwC;gBAAO,CAAE,GAAG7C,KAAI,CAACrD,eAAe,CAAC6D,SAAS,EAAE,CACvD+B,OAAO,CACP9B,IAAI,CAAC,iBAAiB,CAAC,CACvBqC,YAAY,CAAC7B,QAAQ,CAAC;gBAEzBe,OAAO,CAAC;kBAAEe,SAAS,EAAEF,OAAO,CAACE;gBAAS,CAAE,CAAC;cAC3C,CAAC,MAAM;gBACLd,MAAM,CAAC,IAAInB,KAAK,CAAC,kBAAkBoB,GAAG,CAACc,UAAU,EAAE,CAAC,CAAC;cACvD;YACF,CAAC;YAEDd,GAAG,CAACe,OAAO,GAAG,MAAK;cACjBhB,MAAM,CAAC,IAAInB,KAAK,CAAC,6BAA6B,CAAC,CAAC;YAClD,CAAC;YAEDoB,GAAG,CAACgB,IAAI,CAAC5B,QAAQ,CAAC;UACpB,CAAC,CAAC;UAEF,MAAM;YAAEyB;UAAS,CAAE,SAASjB,aAAa;UAEzC5B,iBAAiB,CAACiD,WAAW,GAAGlC,QAAQ;QAC1C;QAEAjB,KAAI,CAACtD,YAAY,CAACtF,WAAW,CAAC4I,KAAI,CAACpD,cAAc,EAAEsD,iBAAiB,CAAC,CAAC7C,SAAS,CAAC;UAC9EM,IAAI,EAAEA,CAAA,KAAK;YACTqC,KAAI,CAAClD,WAAW,GAAG,KAAK;YACxBkD,KAAI,CAAC7M,WAAW,GAAG,EAAE;YACrB6M,KAAI,CAAC/C,aAAa,GAAG,EAAE;YACvB+C,KAAI,CAAC9C,gBAAgB,GAAG,EAAE;YAC1B8C,KAAI,CAACxC,aAAa,EAAE;UACtB,CAAC;UACDI,KAAK,EAAGA,KAAK,IAAI;YACfoC,KAAI,CAACnD,SAAS,GAAG,KAAK;UACxB;SACD,CAAC;MACJ,CAAC,CAAC,OAAOuG,GAAG,EAAE;QACZ,MAAMxF,KAAK,GAAGwF,GAAU;QACxB7D,KAAK,CAAC,UAAU3B,KAAK,CAACmD,OAAO,IAAIsC,IAAI,CAACC,SAAS,CAAC1F,KAAK,CAAC,EAAE,CAAC;QACzDoC,KAAI,CAACnD,SAAS,GAAG,KAAK;MACxB;IAAC;EACH;EAEAnE,YAAYA,CAACqG,QAAa;IACxB,IAAI,CAAC9F,eAAe,GAAG;MAAE,GAAG8F;IAAQ,CAAE;IAEtC,IAAIZ,MAAM,CAACC,IAAI,CAAC,IAAI,CAACpB,iBAAiB,CAAC,CAACuB,MAAM,KAAK,CAAC,EAAE;MACpD,IAAI,CAACR,qBAAqB,EAAE;IAC9B;EACF;EAEAxI,oBAAoBA,CAAC+I,SAAiB;IACpC,OAAO,IAAI,CAACtB,iBAAiB,CAACsB,SAAS,CAAC,IAAI,EAAE;EAChD;EAEAiF,YAAYA,CAACjF,SAAiB;IAC5B,OAAO,IAAI,CAACvH,YAAY,CAACuH,SAAS,CAAC,KAAK,aAAa;EACvD;EAEMxC,cAAcA,CAAA;IAAA,IAAA0H,MAAA;IAAA,OAAAvD,iBAAA;MAClB,IAAI,CAACuD,MAAI,CAACvK,eAAe,EAAE;MAE3BuK,MAAI,CAAC3G,SAAS,GAAG,IAAI;MAErB,IAAI;QACF,MAAM4G,KAAK,GAAGD,MAAI,CAACvK,eAAe,CAAChE,EAAE;QACrC,MAAMyO,OAAO,GAAG;UAAE,GAAGF,MAAI,CAACvK;QAAe,CAAE;QAC3C,OAAOyK,OAAO,CAACzO,EAAE;QAEjB,MAAM0O,aAAa,GAAGH,MAAI,CAACrD,oBAAoB,CAACuD,OAAO,CAAC;QAExD,MAAMtD,OAAO,GAAG,GAAGqD,KAAK,cAAc;QACtC,IAAID,MAAI,CAACvG,aAAa,CAACmD,OAAO,CAAC,IAAIoD,MAAI,CAAC5G,cAAc,KAAK,kBAAkB,EAAE;UAC7E,MAAMuC,IAAI,GAAGqE,MAAI,CAACvG,aAAa,CAACmD,OAAO,CAAC;UAExC,MAAM;YAAEC,IAAI,EAAEC,QAAQ;YAAE1C,KAAK,EAAE2C;UAAS,CAAE,SAASiD,MAAI,CAAC7G,eAAe,CAAC6D,SAAS,EAAE,CAChFC,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,UAAU,CAAC,CAClBC,EAAE,CAAC,IAAI,EAAE6C,MAAI,CAAC7G,eAAe,CAACiE,gBAAgB,EAAE,CAAC,CACjDC,MAAM,EAAE;UAEX,IAAIN,SAAS,EAAE;YACb,MAAM,IAAIO,KAAK,CAAC,gCAAgCP,SAAS,CAACQ,OAAO,EAAE,CAAC;UACtE;UAEA,IAAI,CAAAT,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEU,QAAQ,MAAK,OAAO,EAAE;YAClC,MAAM,IAAIF,KAAK,CAAC,mDAAmD,CAAC;UACtE;UAEA,MAAMG,QAAQ,GAAG,GAAGC,IAAI,CAACC,GAAG,EAAE,IAAIhC,IAAI,CAACiC,IAAI,CAACC,OAAO,CAAC,iBAAiB,EAAE,GAAG,CAAC,EAAE;UAG7E,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;UAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAErC,IAAI,CAAC;UAG7B,MAAM;YAAEkB,IAAI,EAAE;cAAEoB;YAAO;UAAE,CAAE,SAAS+B,MAAI,CAAC7G,eAAe,CAAC6D,SAAS,EAAE,CAACkB,IAAI,CAACC,UAAU,EAAE;UACtF,MAAMC,WAAW,GAAGH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEI,YAAY;UAEzC,IAAI,CAACD,WAAW,EAAE;YAChB,MAAM,IAAId,KAAK,CAAC,2BAA2B,CAAC;UAC9C;UAEA,MAAMgB,aAAa,GAAG,IAAIC,OAAO,CAAwB,CAACC,OAAO,EAAEC,MAAM,KAAI;YAC3E,MAAMC,GAAG,GAAG,IAAIC,cAAc,EAAE;YAEhC,MAAMC,WAAW,GAAG,IAAIC,eAAe,CAAC;cACtC,cAAc,EAAE,MAAM;cACtB,QAAQ,EAAE;aACX,CAAC;YAEFH,GAAG,CAACI,IAAI,CAAC,MAAM,EAAE,GAAGkB,MAAI,CAAC7G,eAAe,CAAC4F,OAAO,CAACC,GAAG,sCAAsCvB,QAAQ,IAAImB,WAAW,CAACK,QAAQ,EAAE,EAAE,CAAC;YAE/HP,GAAG,CAACQ,gBAAgB,CAAC,eAAe,EAAE,UAAUd,WAAW,EAAE,CAAC;YAC9DM,GAAG,CAACQ,gBAAgB,CAAC,QAAQ,EAAEc,MAAI,CAAC7G,eAAe,CAACgG,WAAW,CAAC;YAEhET,GAAG,CAACvC,MAAM,GAAG,MAAK;cAChB,IAAIuC,GAAG,CAACU,MAAM,IAAI,GAAG,IAAIV,GAAG,CAACU,MAAM,GAAG,GAAG,EAAE;gBACzC,MAAM;kBAAEvC,IAAI,EAAEwC;gBAAO,CAAE,GAAGW,MAAI,CAAC7G,eAAe,CAAC6D,SAAS,EAAE,CACvD+B,OAAO,CACP9B,IAAI,CAAC,iBAAiB,CAAC,CACvBqC,YAAY,CAAC7B,QAAQ,CAAC;gBAEzBe,OAAO,CAAC;kBAAEe,SAAS,EAAEF,OAAO,CAACE;gBAAS,CAAE,CAAC;cAC3C,CAAC,MAAM;gBACLd,MAAM,CAAC,IAAInB,KAAK,CAAC,kBAAkBoB,GAAG,CAACc,UAAU,EAAE,CAAC,CAAC;cACvD;YACF,CAAC;YAEDd,GAAG,CAACe,OAAO,GAAG,MAAK;cACjBhB,MAAM,CAAC,IAAInB,KAAK,CAAC,6BAA6B,CAAC,CAAC;YAClD,CAAC;YAEDoB,GAAG,CAACgB,IAAI,CAAC5B,QAAQ,CAAC;UACpB,CAAC,CAAC;UAEF,MAAM;YAAEyB;UAAS,CAAE,SAASjB,aAAa;UAEzC6B,aAAa,CAACR,WAAW,GAAGlC,QAAQ;QACtC;QAEAuC,MAAI,CAAC9G,YAAY,CAACZ,cAAc,CAAC0H,MAAI,CAAC5G,cAAc,EAAE6G,KAAK,EAAEE,aAAa,CAAC,CAACtG,SAAS,CAAC;UACpFM,IAAI,EAAEA,CAAA,KAAK;YACT6F,MAAI,CAACvK,eAAe,GAAG,IAAI;YAC3BuK,MAAI,CAACvG,aAAa,GAAG,EAAE;YACvBuG,MAAI,CAACtG,gBAAgB,GAAG,EAAE;YAC1BsG,MAAI,CAAChG,aAAa,EAAE;UACtB,CAAC;UACDI,KAAK,EAAGA,KAAK,IAAI;YACf4F,MAAI,CAAC3G,SAAS,GAAG,KAAK;UACxB;SACD,CAAC;MACJ,CAAC,CAAC,OAAOuG,GAAG,EAAE;QACZ,MAAMxF,KAAK,GAAGwF,GAAU;QACxB7D,KAAK,CAAC,UAAU3B,KAAK,CAACmD,OAAO,IAAIsC,IAAI,CAACC,SAAS,CAAC1F,KAAK,CAAC,EAAE,CAAC;QACzD4F,MAAI,CAAC3G,SAAS,GAAG,KAAK;MACxB;IAAC;EACH;EAEA+G,YAAYA,CAACzE,IAAU;IACrB,OAAO,IAAI4C,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMxC,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACM,aAAa,CAACZ,IAAI,CAAC;MAC1BM,MAAM,CAACE,MAAM,GAAG,MAAMqC,OAAO,CAACvC,MAAM,CAACZ,MAAgB,CAAC;MACtDY,MAAM,CAACwD,OAAO,GAAGrF,KAAK,IAAIqE,MAAM,CAACrE,KAAK,CAAC;IACzC,CAAC,CAAC;EACJ;EAEAvF,wBAAwBA,CAAC0G,QAAa,EAAE8E,GAAW;IACjD,IAAI,IAAI,CAACjH,cAAc,KAAK,kBAAkB,IAAIiH,GAAG,KAAK,aAAa,IAAI9E,QAAQ,CAAC8E,GAAG,CAAC,EAAE;MACxF,IAAI;QACF,IAAI,CAAC9E,QAAQ,CAAC8E,GAAG,CAAC,CAACxE,QAAQ,CAAC,KAAK,CAAC,EAAE;UAClC,MAAM;YAAEgB,IAAI,EAAEwC;UAAO,CAAE,GAAG,IAAI,CAAClG,eAAe,CAAC6D,SAAS,EAAE,CACvD+B,OAAO,CACP9B,IAAI,CAAC,iBAAiB,CAAC,CACvBqC,YAAY,CAAC/D,QAAQ,CAAC8E,GAAG,CAAC,CAAC;UAG9B,OAAO,aAAahB,OAAO,CAACE,SAAS,6DAA6D;QACpG,CAAC,MAAM;UACL,OAAO,aAAahE,QAAQ,CAAC8E,GAAG,CAAC,6DAA6D;QAChG;MACF,CAAC,CAAC,OAAOT,GAAG,EAAE;QACZ,MAAMxF,KAAK,GAAGwF,GAAU;QACxB,OAAO,UAAUrE,QAAQ,CAAC8E,GAAG,CAAC,0BAA0B;MAC1D;IACF;IAEA,OAAO,IAAI,CAACC,WAAW,CAAC/E,QAAQ,CAAC8E,GAAG,CAAC,CAAC;EACxC;EAEAjL,cAAcA,CAAC6K,KAAa;IAC1B,IAAIM,OAAO,CAAC,8EAA8E,CAAC,EAAE;MAC3F,IAAI,CAAClH,SAAS,GAAG,IAAI;MACrB,IAAI,CAACH,YAAY,CAAC9D,cAAc,CAAC,IAAI,CAACgE,cAAc,EAAE6G,KAAK,CAAC,CAACpG,SAAS,CAAC;QACrEM,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACH,aAAa,EAAE;QACtB,CAAC;QACDI,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACf,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACJ;EACF;EAEAjB,UAAUA,CAAA;IACR,IAAI,CAAC3C,eAAe,GAAG,IAAI;EAC7B;EAEA/B,cAAcA,CAAC6H,QAAc;IAC3B,IAAIA,QAAQ,EAAE;MACZ,MAAMiF,OAAO,GAAG;QAAE,GAAGjF;MAAQ,CAAE;MAC/B,OAAOiF,OAAO,CAAC/O,EAAE;MACjB,IAAI,CAAC8C,iBAAiB,GAAGsL,IAAI,CAACC,SAAS,CAACU,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;MACzD,IAAI,CAAC/K,eAAe,GAAG8F,QAAQ;IACjC,CAAC,MAAM;MACL,IAAI,CAAChH,iBAAiB,GAAGsL,IAAI,CAACC,SAAS,CAAC,IAAI,CAACnQ,WAAW,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;IAC1E;IACA,IAAI,CAAC4J,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACpF,SAAS,GAAG,EAAE;EACrB;EAEAQ,gBAAgBA,CAAA;IACd,IAAI;MACF,MAAM8L,UAAU,GAAGZ,IAAI,CAACa,KAAK,CAAC,IAAI,CAACnM,iBAAiB,CAAC;MAErD,IAAI,IAAI,CAACkB,eAAe,EAAE;QACxB,IAAI,CAACA,eAAe,GAAG;UACrBhE,EAAE,EAAE,IAAI,CAACgE,eAAe,CAAChE,EAAE;UAC3B,GAAGgP;SACJ;MACH,CAAC,MAAM;QACL,IAAI,CAAC9Q,WAAW,GAAG8Q,UAAU;MAC/B;MAEA,IAAI,CAAClH,cAAc,GAAG,KAAK;MAC3B,IAAI,CAACpF,SAAS,GAAG,EAAE;IACrB,CAAC,CAAC,OAAOiI,CAAC,EAAE;MACV,IAAI,CAACjI,SAAS,GAAG,qBAAqB;IACxC;EACF;EAEAM,cAAcA,CAAA;IACZ,IAAI,CAAC8E,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACpF,SAAS,GAAG,EAAE;EACrB;EAEAwM,MAAMA,CAAA;IACJ,IAAI,CAAC1H,MAAM,CAACgB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEA/F,aAAaA,CAAC0M,GAAQ;IACpB,OAAOjG,MAAM,CAACC,IAAI,CAACgG,GAAG,IAAI,EAAE,CAAC;EAC/B;EAEAC,QAAQA,CAAC5M,KAAU;IACjB,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,IAAI,CAAC6M,KAAK,CAACC,OAAO,CAAC9M,KAAK,CAAC;EAC7E;EAEAqM,WAAWA,CAACrM,KAAU;IACpB,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK+M,SAAS,EAAE;MACzC,OAAO,MAAM;IACf;IAEA,IAAI,OAAO/M,KAAK,KAAK,QAAQ,EAAE;MAC7B,IAAIA,KAAK,YAAYyJ,IAAI,EAAE;QACzB,OAAOzJ,KAAK,CAACgN,cAAc,EAAE;MAC/B;MACA,OAAOpB,IAAI,CAACC,SAAS,CAAC7L,KAAK,CAAC;IAC9B;IAEA,OAAOiN,MAAM,CAACjN,KAAK,CAAC;EACtB;EAEAV,YAAYA,CAACuH,SAAiB;IAC5B,IAAI,IAAI,CAAC1B,cAAc,KAAK,kBAAkB,IAAI0B,SAAS,KAAK,aAAa,EAAE;MAC7E,OAAO,MAAM;IACf;IAEA,MAAMN,UAAU,GAAG,IAAI,CAACtB,YAAY,CAACuB,aAAa,CAAC,IAAI,CAACrB,cAAc,CAAC;IACvE,OAAOoB,UAAU,CAACM,SAAS,CAAC,IAAI,MAAM;EACxC;EAEAvJ,gBAAgBA,CAACuJ,SAAiB;IAChC,OAAO,IAAI,CAAC5B,YAAY,CAAC3H,gBAAgB,CAAC,IAAI,CAAC6H,cAAc,EAAE0B,SAAS,CAAC;EAC3E;EAEApK,kBAAkBA,CAACuD,KAAU;IAC3B,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE;IAErB,IAAIkN,IAAU;IAEd,IAAIlN,KAAK,YAAYyJ,IAAI,EAAE;MACzByD,IAAI,GAAGlN,KAAK;IACd,CAAC,MAAM,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACmN,OAAO,EAAE;MACrDD,IAAI,GAAG,IAAIzD,IAAI,CAACzJ,KAAK,CAACmN,OAAO,GAAG,IAAI,CAAC;IACvC,CAAC,MAAM,IAAI,OAAOnN,KAAK,KAAK,QAAQ,EAAE;MACpC,IAAI;QACFkN,IAAI,GAAG,IAAIzD,IAAI,CAACzJ,KAAK,CAAC;QACtB,IAAIoN,KAAK,CAACF,IAAI,CAACG,OAAO,EAAE,CAAC,EAAE;UACzB,OAAOrN,KAAK;QACd;MACF,CAAC,CAAC,OAAOmI,CAAC,EAAE;QACV,OAAOnI,KAAK;MACd;IACF,CAAC,MAAM;MACL,OAAOiN,MAAM,CAACjN,KAAK,CAAC;IACtB;IAEA,MAAMsN,IAAI,GAAGJ,IAAI,CAACK,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGP,MAAM,CAACC,IAAI,CAACO,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGV,MAAM,CAACC,IAAI,CAACU,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,OAAO,GAAGJ,IAAI,IAAIE,KAAK,IAAIG,GAAG,EAAE;EAClC;EAEApR,gBAAgBA,CAACoQ,GAAQ,EAAEP,GAAW,EAAE/E,KAAU;IAChD,IAAIA,KAAK,EAAE;MACTsF,GAAG,CAACP,GAAG,CAAC,GAAG,IAAI3C,IAAI,CAACpC,KAAK,CAAC;IAC5B,CAAC,MAAM;MACLsF,GAAG,CAACP,GAAG,CAAC,GAAG,IAAI;IACjB;EACF;EAEA1D,oBAAoBA,CAACiE,GAAQ;IAC3B,IAAI,CAACA,GAAG,EAAE,OAAOA,GAAG;IAEpB,MAAMvF,MAAM,GAAG;MAAE,GAAGuF;IAAG,CAAE;IAEzB,KAAK,MAAMP,GAAG,IAAIhF,MAAM,EAAE;MACxB,MAAMpH,KAAK,GAAGoH,MAAM,CAACgF,GAAG,CAAC;MAEzB,IAAI,OAAOpM,KAAK,KAAK,QAAQ,IAAI,IAAI,CAACV,YAAY,CAAC8M,GAAG,CAAC,KAAK,MAAM,EAAE;QAClE,IAAI;UACFhF,MAAM,CAACgF,GAAG,CAAC,GAAG,IAAI3C,IAAI,CAACzJ,KAAK,CAAC;QAC/B,CAAC,CAAC,OAAOmI,CAAC,EAAE,CACZ;MACF;IACF;IAEA,OAAOf,MAAM;EACf;;wBAtgBWvC,oBAAoB;;mCAApBA,qBAAoB,EAAA/J,EAAA,CAAA+S,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAjT,EAAA,CAAA+S,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAlT,EAAA,CAAA+S,iBAAA,CAAAI,EAAA,CAAAC,YAAA,GAAApT,EAAA,CAAA+S,iBAAA,CAAAM,EAAA,CAAAC,eAAA;AAAA;;QAApBvJ,qBAAoB;EAAAwJ,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCf7B7T,EAFJ,CAAAC,cAAA,aAAuB,aACb,aACY;MAChBD,EAAA,CAAAmD,SAAA,aAA6D;MAC7DnD,EAAA,CAAAC,cAAA,WAAM;MAAAD,EAAA,CAAAgC,MAAA,cAAO;MACfhC,EADe,CAAAc,YAAA,EAAO,EAChB;MAEJd,EADF,CAAAC,cAAA,aAA4B,gBACqB;MAAnBD,EAAA,CAAAsB,UAAA,mBAAAyS,sDAAA;QAAA,OAASD,GAAA,CAAAlC,MAAA,EAAQ;MAAA,EAAC;MAAC5R,EAAA,CAAAgC,MAAA,kBAAM;MAAAhC,EAAA,CAAAc,YAAA,EAAS;MAC9Dd,EAAA,CAAAC,cAAA,SAAI;MAAAD,EAAA,CAAAgC,MAAA,IAAoB;MAE5BhC,EAF4B,CAAAc,YAAA,EAAK,EACzB,EACC;MAILd,EAFJ,CAAAC,cAAA,cAA2B,cACO,iBACuB;MAA1BD,EAAA,CAAAsB,UAAA,mBAAA0S,uDAAA;QAAA,OAASF,GAAA,CAAAxI,aAAA,EAAe;MAAA,EAAC;MAClDtL,EAAA,CAAAgC,MAAA,IACF;MACFhC,EADE,CAAAc,YAAA,EAAS,EACL;MAgFNd,EA7EA,CAAAsC,UAAA,KAAA2R,oCAAA,mBAA+C,KAAAC,oCAAA,mBA6EO;MAatDlU,EAAA,CAAAC,cAAA,eAA4B;MAU1BD,EATA,CAAAsC,UAAA,KAAA6R,oCAAA,kBAAuC,KAAAC,oCAAA,kBAK2B,KAAAC,oCAAA,kBAIM;MAqG9ErU,EAFI,CAAAc,YAAA,EAAM,EACF,EACF;;;MArNId,EAAA,CAAAiC,SAAA,IAAoB;MAApBjC,EAAA,CAAAkC,iBAAA,CAAA4R,GAAA,CAAAzJ,cAAA,CAAoB;MAOtBrK,EAAA,CAAAiC,SAAA,GACF;MADEjC,EAAA,CAAA2C,kBAAA,MAAAmR,GAAA,CAAAvJ,WAAA,kCACF;MAIIvK,EAAA,CAAAiC,SAAA,EAAiB;MAAjBjC,EAAA,CAAA0B,UAAA,SAAAoS,GAAA,CAAAvJ,WAAA,CAAiB;MA6EjBvK,EAAA,CAAAiC,SAAA,EAAoB;MAApBjC,EAAA,CAAA0B,UAAA,SAAAoS,GAAA,CAAAtJ,cAAA,CAAoB;MAclBxK,EAAA,CAAAiC,SAAA,GAAe;MAAfjC,EAAA,CAAA0B,UAAA,SAAAoS,GAAA,CAAAxJ,SAAA,CAAe;MAKftK,EAAA,CAAAiC,SAAA,EAA0C;MAA1CjC,EAAA,CAAA0B,UAAA,UAAAoS,GAAA,CAAAxJ,SAAA,IAAAwJ,GAAA,CAAAhK,SAAA,CAAAkC,MAAA,OAA0C;MAI1ChM,EAAA,CAAAiC,SAAA,EAAwC;MAAxCjC,EAAA,CAAA0B,UAAA,UAAAoS,GAAA,CAAAxJ,SAAA,IAAAwJ,GAAA,CAAAhK,SAAA,CAAAkC,MAAA,KAAwC;;;iBDzGxCnM,WAAW,EAAAyU,EAAA,CAAAC,UAAA,EAAE5U,YAAY,EAAA6U,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EAAAH,EAAA,CAAAI,YAAA,EAAAJ,EAAA,CAAAK,eAAA,EAAEjV,WAAW,EAAAkV,EAAA,CAAAC,cAAA,EAAAD,EAAA,CAAAE,uBAAA,EAAAF,EAAA,CAAAG,oBAAA,EAAAH,EAAA,CAAAI,mBAAA,EAAAJ,EAAA,CAAAK,4BAAA,EAAAL,EAAA,CAAAM,0BAAA,EAAAN,EAAA,CAAAO,eAAA,EAAAP,EAAA,CAAAQ,OAAA;EAAAC,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}