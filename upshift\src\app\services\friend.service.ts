﻿import { Injectable, inject } from '@angular/core';
import { Friend } from '../models/friend.model';
import { Observable, map, of, switchMap, from, catchError } from 'rxjs';
import { SupabaseService } from './supabase.service';

@Injectable({
  providedIn: 'root'
})
export class FriendService {
  private supabaseService = inject(SupabaseService);

  getFriends(userId: string): Observable<Friend[]> {

    return from(
      this.supabaseService.supabase
        .from('friends')
        .select('*')
        .or(`user_id.eq.${userId},friend_id.eq.${userId}`)
    ).pipe(
      map(response => {
        if (response.error) {
          return [];
        }

        const normalizedFriends = response.data.map(friend => {
          if (friend.friend_id === userId) {
            return {
              ...friend,
              friend_id: friend.user_id,
              user_id: userId
            };
          }
          return friend;
        });

        const uniqueFriendIds = new Set<string>();
        const uniqueFriends = normalizedFriends.filter(friend => {
          if (uniqueFriendIds.has(friend.friend_id)) {
            return false;
          }
          uniqueFriendIds.add(friend.friend_id);
          return true;
        });

        return uniqueFriends as Friend[];
      })
    );
  }

  getFriendsWithProfiles(userId: string): Observable<any[]> {

    return this.getFriends(userId).pipe(
      switchMap(friends => {
        if (friends.length === 0) {
          return of([]);
        }

        const friendIds = friends.map(friend => friend.friend_id);

        const profilePromises = friendIds.map(friendId => {
          return new Promise<any>((resolve) => {
            try {
              this.supabaseService.supabase
                .from('profiles')
                .select('*')
                .eq('id', friendId)
                .maybeSingle()
                .then(response => {
                  if (response.error) {
                    resolve(null);
                  } else {
                    resolve(response.data);
                  }
                });
            } catch (err: any) {
              resolve(null);
            }
          });
        });

        return from(Promise.all(profilePromises)).pipe(
          map(profiles => {

            const profileMap = new Map<string, any>();
            profiles.forEach((profile: any, index: number) => {
              if (profile) {
                profileMap.set(friendIds[index], profile);
              }
            });

            return friends.map(friend => {
              return {
                ...friend,
                profile: profileMap.get(friend.friend_id) || null
              };
            });
          }),
          catchError(error => {
            return of([]);
          })
        );
      })
    );
  }

  async addFriend(userId: string, friendId: string): Promise<void> {

    const friendship1: Friend = {
      user_id: userId,
      friend_id: friendId,
      created: new Date()
    };

    const friendship2: Friend = {
      user_id: friendId,
      friend_id: userId,
      created: new Date()
    };

    const { error: error1 } = await this.supabaseService.supabase
      .from('friends')
      .insert(friendship1);

    if (error1) {
      throw error1;
    }

    const { error: error2 } = await this.supabaseService.supabase
      .from('friends')
      .insert(friendship2);

    if (error2) {
      throw error2;
    }

    await this.updateFriendBadges(userId);
    await this.updateFriendBadges(friendId);

  }

  async removeFriend(userId: string, friendId: string): Promise<void> {

    const { error: error1 } = await this.supabaseService.supabase
      .from('friends')
      .delete()
      .eq('user_id', userId)
      .eq('friend_id', friendId);

    if (error1) {
      throw error1;
    }

    const { error: error2 } = await this.supabaseService.supabase
      .from('friends')
      .delete()
      .eq('user_id', friendId)
      .eq('friend_id', userId);

    if (error2) {
      throw error2;
    }

    await this.updateFriendBadges(userId);
    await this.updateFriendBadges(friendId);

  }

  async generateFriendCode(userId: string): Promise<string> {

    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let code = '';
    for (let i = 0; i < 8; i++) {
      code += characters.charAt(Math.floor(Math.random() * characters.length));
    }

    const expiry = new Date();
    expiry.setDate(expiry.getDate() + 1);

    const { error } = await this.supabaseService.supabase
      .from('profiles')
      .update({
        friend_code: code,
        friend_code_expiry: expiry
      })
      .eq('id', userId);

    if (error) {
      throw error;
    }

    return code;
  }

  validateUsernameFormat(input: string): boolean {
    const usernameRegex = /^[\w.@+-]+$/;
    return usernameRegex.test(input);
  }

  async addFriendByCode(userId: string, code: string): Promise<boolean> {

    if (!code || !this.validateUsernameFormat(code)) {
      return false;
    }

    const { data: users, error: userError } = await this.supabaseService.supabase
      .from('profiles')
      .select('*')
      .eq('friend_code', code)
      .limit(1);

    if (userError) {
      throw userError;
    }

    if (!users || users.length === 0) {
      return false;
    }

    const friend = users[0];
    const friendId = friend.id;

    const expiryDate = friend.friend_code_expiry ? new Date(friend.friend_code_expiry) : null;
    if (!expiryDate || expiryDate < new Date()) {
      return false;
    }

    const { data: existingFriends, error: friendError } = await this.supabaseService.supabase
      .from('friends')
      .select('*')
      .eq('user_id', userId)
      .eq('friend_id', friendId)
      .limit(1);

    if (friendError) {
      throw friendError;
    }

    if (existingFriends && existingFriends.length > 0) {
      return false;
    }

    await this.addFriend(userId, friendId);

    const { error: clearError } = await this.supabaseService.supabase
      .from('profiles')
      .update({
        friend_code: null,
        friend_code_expiry: null
      })
      .eq('id', friendId);

    if (clearError) {
    }

    return true;
  }

  searchFriendsByUsername(userId: string, query: string, groupId?: string): Observable<string[]> {

    if (!query || query.length < 2) {
      return of([]);
    }

    return this.getFriends(userId).pipe(
      switchMap(friends => {
        if (friends.length === 0) {
          return of([]);
        }

        const friendIds = friends.map(friend => friend.friend_id);

        return from(
          this.supabaseService.supabase
            .from('profiles')
            .select('id, username')
            .in('id', friendIds)
            .ilike('username', `%${query}%`)
            .limit(10)
        ).pipe(
          switchMap(response => {
            if (response.error) {
              return of<string[]>([]);
            }

            const matchingFriends = response.data;

            if (!groupId) {
              return of<string[]>(matchingFriends.map(friend => friend.username));
            }

            return from(
              this.supabaseService.supabase
                .from('group_members')
                .select('user_id')
                .eq('group_id', groupId)
            ).pipe(
              switchMap(membersResponse => {
                if (membersResponse.error) {
                  return of<string[]>(matchingFriends.map(friend => friend.username));
                }

                const memberIds = membersResponse.data.map(member => member.user_id);

                const filteredFriends = matchingFriends.filter(friend => !memberIds.includes(friend.id));

                return from(
                  this.supabaseService.supabase
                    .from('group_join_requests')
                    .select('username_invited')
                    .eq('group_id', groupId)
                ).pipe(
                  map(invitesResponse => {
                    if (invitesResponse.error) {
                      return filteredFriends.map(friend => friend.username);
                    }

                    const invitedUsernames = invitesResponse.data.map(invite => invite.username_invited);

                    const finalFilteredFriends = filteredFriends.filter(friend =>
                      !invitedUsernames.includes(friend.username)
                    );

                    return finalFilteredFriends.map(friend => friend.username);
                  })
                );
              }),
              catchError(error => {
                return of<string[]>(matchingFriends.map(friend => friend.username));
              })
            );
          }),
          catchError(error => {
            return of<string[]>([]);
          })
        );
      })
    );
  }

  private async updateFriendBadges(userId: string): Promise<void> {

    const { data: friends, error: friendError } = await this.supabaseService.supabase
      .from('friends')
      .select('*')
      .eq('user_id', userId);

    if (friendError) {
      return;
    }

    const friendCount = friends ? friends.length : 0;

    const { data: badges, error: badgeError } = await this.supabaseService.supabase
      .from('user_badges')
      .select('*')
      .eq('user_id', userId)
      .limit(1);

    if (badgeError) {
      return;
    }

    if (!badges || badges.length === 0) {
      return;
    }

    const badgeId = badges[0].id;
    const updates: any = {};

    if (friendCount >= 5) {
      updates.badge_friends_5 = true;
    }

    if (friendCount >= 10) {
      updates.badge_friends_10 = true;
    }

    if (Object.keys(updates).length > 0) {

      const { error: updateError } = await this.supabaseService.supabase
        .from('user_badges')
        .update(updates)
        .eq('id', badgeId);

      if (updateError) {
      }
    }
  }
}
