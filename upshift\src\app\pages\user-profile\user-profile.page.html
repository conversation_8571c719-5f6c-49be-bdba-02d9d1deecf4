﻿<div class="container">
    <header>
        <div class="logo">
            <img src="assets/images/upshift_icon_mini.svg" alt="Upshift">
            <span>Upshift</span>
        </div>
        <h1>User Profile</h1>
    </header>

    <a href="javascript:void(0)" (click)="goBack()" class="back-link">&larr; Go Back</a>

    <div class="profile-container" *ngIf="user">
        <div class="profile-header">
            <div class="profile-picture">
                <img *ngIf="user.profile_picture" [src]="user.profile_picture" [alt]="user.username">
                <ng-container *ngIf="!user.profile_picture">👤</ng-container>
            </div>
            <div class="profile-info">
                <div class="profile-name">{{ user.name }}</div>
                <div class="profile-username">&#64;{{ user.username }}</div>
                <div class="profile-level">
                    <div class="level-badge">Level {{ user.level }}</div>
                    <div class="profile-title">{{ user.title }}</div>
                </div>
            </div>
        </div>
        <div class="profile-bio-container" style="padding-left: 100px;">
            <div class="profile-bio" *ngIf="user.bio">{{ user.bio }}</div>
            <div class="profile-bio" *ngIf="!user.bio">No bio provided</div>
        </div>

        <div class="xp-section">
            <h2>XP Progress</h2>

            <div class="category-card" *ngFor="let category of categories">
                <div class="category-header">
                    <div class="category-icon">{{ category.icon }}</div>
                    <div class="category-name">{{ category.name }}</div>
                </div>
                <div class="progress-container">
                    <div class="progress-bar" [style.width.%]="category.progress" [style.background-color]="category.color"></div>
                </div>
                <div class="xp-text">
                    <span>{{ category.current_xp }} XP</span>
                    <span>{{ category.required_xp }} XP needed</span>
                </div>
            </div>

            <div class="next-level-info">
                <div class="next-level-text">Next Level: {{ nextLevel }}</div>
                <div class="next-level-requirements">
                    Reach required XP in all categories to level up
                </div>
            </div>
        </div>

        <div class="button-container">
            <a [routerLink]="['/badges', user.id]" style="margin-top: 15px; display: inline-block; margin-right: 10px; padding: 8px 16px; background-color: #1c1c1e; color: white; border: 1px solid #4d7bff; border-radius: 20px; text-decoration: none; font-size: 14px; font-weight: 600; transition: all 0.3s ease;">
                <span style="margin-right: 5px;">🏆</span> View Badges
            </a>
        </div>
    </div>
</div>
