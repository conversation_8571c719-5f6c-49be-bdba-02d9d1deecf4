﻿import { QuestCategory, QuestGoalUnit } from './quest.model';

export interface DailySideQuestPool {
  id?: string; 
  name: string;
  description: string;
  goal_value: number;
  category: QuestCategory;
  goal_unit: QuestGoalUnit | 'l' | 'pages' | 'books' | '%' | '€' | '$' | '£';
  active: boolean;
  emoji: string;
}

export interface UserDailySideQuest {
  id?: string; 
  user_id: string;
  current_quest_id: string;
  streak: number;
  last_completed_date?: string | null;
  date_assigned: string;
  completed: boolean;
  value_achieved: number;
  category: QuestCategory;
  emoji: string;
}
