﻿import { Injectable, inject } from '@angular/core';
import { Observable, from, of, throwError } from 'rxjs';
import { map, catchError, switchMap, take } from 'rxjs/operators';
import { SupabaseService } from './supabase.service';
import { DailySideQuestPool, UserDailySideQuest } from '../models/supabase.models';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class SideQuestSupabaseService {
  private supabaseService = inject(SupabaseService);
  private http = inject(HttpClient);

  getUserDailySideQuests(userId: string): Observable<UserDailySideQuest[]> {

    return from(
      this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*, daily_sidequest_pool(*)')
        .eq('user_id', userId)
        .order('date_assigned', { ascending: false })
        .limit(1)
    ).pipe(
      map(response => {
        if (response.error) {
          return [];
        }

        return response.data as UserDailySideQuest[];
      }),
      catchError(error => {
        return of([]);
      })
    );
  }

  getUserDailySideQuest(sideQuestId: string): Observable<UserDailySideQuest | null> {

    return from(
      this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*')
        .eq('id', sideQuestId)
        .single()
    ).pipe(
      map(response => {
        if (response.error) {
          return null;
        }

        return response.data as UserDailySideQuest;
      }),
      catchError(error => {
        return of(null);
      })
    );
  }

  getUserLatestDailySideQuest(userId: string): Observable<UserDailySideQuest | null> {

    return this.getUserDailySideQuests(userId).pipe(
      map(sideQuests => {
        if (!sideQuests || sideQuests.length === 0) {
          return null;
        }

        return sideQuests[0];
      }),
      catchError(error => {
        return of(null);
      })
    );
  }

  toggleSideQuestCompletion(sideQuestId: string, userId: string, selectedDate?: Date): Observable<{ completed: boolean; value_achieved: number; streak: number }> {

    return this.checkMissedDays(sideQuestId).pipe(
      switchMap(() => {
        const today = selectedDate ? new Date(selectedDate) : new Date();
        today.setHours(0, 0, 0, 0);
        const todayStr = today.toISOString().split('T')[0];

        const yesterday = new Date(today);
        yesterday.setDate(today.getDate() - 1);
        const yesterdayStr = yesterday.toISOString().split('T')[0];


        return from(
          this.supabaseService.getClient()
            .from('user_daily_sidequests')
            .select('*, daily_sidequest_pool(*)')
            .eq('id', sideQuestId)
            .single()
        ).pipe(
          switchMap(response => {
            if (response.error) {
              return throwError(() => new Error('Side quest not found'));
            }

            const sideQuest = response.data as any;
            if (!sideQuest) {
              return throwError(() => new Error('Side quest not found'));
            }

            const questDetails = sideQuest.daily_sidequest_pool;
            if (!questDetails) {
              return throwError(() => new Error('Quest details not found'));
            }

            console.log('SideQuestSupabaseService: Current side quest state:', {
              id: sideQuest.id,
              completed: sideQuest.completed,
              streak: sideQuest.streak,
              last_completed_date: sideQuest.last_completed_date,
              date_assigned: sideQuest.date_assigned
            });

            const wasCompleted = sideQuest.completed;

            const newCompletedState = !wasCompleted;

            const updates: Partial<UserDailySideQuest> = {};

            if (newCompletedState) {
              updates.value_achieved = questDetails.goal_value || 1;


              if (sideQuest.last_completed_date === yesterdayStr) {
                updates.streak = (sideQuest.streak || 0) + 1;
              } else {
                updates.streak = 1;
              }

              updates.last_completed_date = todayStr;
            } else {
              updates.value_achieved = 0;


              if (sideQuest.last_completed_date === todayStr) {
                if (sideQuest.streak > 1) {
                  updates.streak = sideQuest.streak - 1;
                  updates.last_completed_date = yesterdayStr;
                } else {
                  updates.streak = 0;
                  updates.last_completed_date = yesterdayStr;  
                }
              } else {
                updates.streak = sideQuest.streak;
              }
            }

            updates.completed = newCompletedState;


            return from(
              this.supabaseService.getClient()
                .from('user_daily_sidequests')
                .update(updates)
                .eq('id', sideQuestId)
                .select()
                .single()
            ).pipe(
              switchMap(response => {
                if (response.error) {
                  throw new Error(response.error.message);
                }


                if (wasCompleted !== newCompletedState) {
                  const xpAmount = 2; 
                  const category = sideQuest.category || questDetails.category;

                  if (newCompletedState) {
                    return this.updateUserXP(userId, category, true, xpAmount).pipe(
                      map(() => {
                        return {
                          completed: response.data.completed,
                          value_achieved: response.data.value_achieved,
                          streak: response.data.streak
                        };
                      })
                    );
                  } else {
                    return this.updateUserXP(userId, category, false, xpAmount).pipe(
                      map(() => {
                        return {
                          completed: response.data.completed,
                          value_achieved: response.data.value_achieved,
                          streak: response.data.streak
                        };
                      })
                    );
                  }
                }

                return of({
                  completed: response.data.completed,
                  value_achieved: response.data.value_achieved,
                  streak: response.data.streak
                });
              }),
              catchError(error => {
                return throwError(() => error);
              })
            );
          })
        );
      })
    );
  }

  createUserDailySideQuest(userId: string): Observable<string> {

    return from(
      this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*')
        .eq('user_id', userId)
    ).pipe(
      switchMap(existingResponse => {
        if (existingResponse.error) {
          return throwError(() => new Error(existingResponse.error.message));
        }

        if (existingResponse.data && existingResponse.data.length > 0) {
          return of(existingResponse.data[0].id);
        }


        return from(
          this.supabaseService.getClient()
            .from('daily_sidequest_pool')
            .select('*')
            .eq('active', true)
        ).pipe(
          switchMap(response => {
            if (response.error) {
              return throwError(() => new Error(response.error.message));
            }

            if (!response.data || response.data.length === 0) {
              return throwError(() => new Error('No active side quests available'));
            }


            const randomIndex = Math.floor(Math.random() * response.data.length);
            const selectedQuest = response.data[randomIndex] as DailySideQuestPool;


            const today = new Date();
            const dateString = today.toISOString().split('T')[0];

            const newSideQuest: Omit<UserDailySideQuest, 'id'> = {
              user_id: userId,
              current_quest_id: selectedQuest.id,
              streak: 0,
              date_assigned: dateString,
              completed: false,
              value_achieved: 0,
              category: selectedQuest.category || 'health',
              emoji: selectedQuest.emoji || '🎯',
              last_completed_date: null
            };

            return from(
              this.supabaseService.getClient()
                .from('user_daily_sidequests')
                .insert(newSideQuest)
                .select()
                .single()
            ).pipe(
              map(insertResponse => {
                if (insertResponse.error) {
                  throw new Error(insertResponse.error.message);
                }

                return insertResponse.data.id;
              }),
              catchError(error => {
                return throwError(() => error);
              })
            );
          })
        );
      })
    );
  }

  assignNewQuest(sideQuestId: string): Observable<void> {

    return from(
      this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*')
        .eq('id', sideQuestId)
        .single()
    ).pipe(
      switchMap(response => {
        if (response.error) {
          return throwError(() => new Error(response.error.message));
        }

        const sideQuest = response.data as UserDailySideQuest;
        if (!sideQuest) {
          return throwError(() => new Error('Side quest not found'));
        }

        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const todayStr = today.toISOString().split('T')[0];

        const dateAssignedStr = sideQuest.date_assigned;


        if (dateAssignedStr === todayStr) {
          return of(undefined);
        }

        return this.checkMissedDays(sideQuestId).pipe(
          switchMap(() => {
            return from(
              this.supabaseService.getClient()
                .from('daily_sidequest_pool')
                .select('*')
                .eq('active', true)
                .neq('id', sideQuest.current_quest_id)
            ).pipe(
              switchMap(poolResponse => {
                if (poolResponse.error) {
                  return throwError(() => new Error(poolResponse.error.message));
                }

                if (!poolResponse.data || poolResponse.data.length === 0) {
                  return throwError(() => new Error('No active side quests available'));
                }

                const randomIndex = Math.floor(Math.random() * poolResponse.data.length);
                const selectedQuest = poolResponse.data[randomIndex] as DailySideQuestPool;


                return from(
                  this.supabaseService.getClient()
                    .from('user_daily_sidequests')
                    .update({
                      current_quest_id: selectedQuest.id,
                      date_assigned: todayStr,
                      completed: false,
                      value_achieved: 0,
                      category: selectedQuest.category || 'health',
                      emoji: selectedQuest.emoji || '🎯'
                    })
                    .eq('id', sideQuestId)
                ).pipe(
                  map(updateResponse => {
                    if (updateResponse.error) {
                      throw new Error(updateResponse.error.message);
                    }

                    return undefined;
                  })
                );
              })
            );
          })
        );
      })
    );
  }

  checkAndReset(sideQuestId: string): Observable<void> {

    return from(
      this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*')
        .eq('id', sideQuestId)
        .single()
    ).pipe(
      switchMap(response => {
        if (response.error) {
          return throwError(() => new Error(response.error.message));
        }

        const sideQuest = response.data as UserDailySideQuest;
        if (!sideQuest) {
          return throwError(() => new Error('Side quest not found'));
        }

        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const todayStr = today.toISOString().split('T')[0];

        const dateAssignedStr = sideQuest.date_assigned;


        if (dateAssignedStr !== todayStr) {
          return this.assignNewQuest(sideQuestId);
        } else {
          return of(undefined);
        }
      })
    );
  }

  ensureUserHasDailySideQuests(userId: string): Observable<UserDailySideQuest[]> {

    const today = new Date();
    const dateString = today.toISOString().split('T')[0];

    return from(
      this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*, daily_sidequest_pool(*)')
        .eq('user_id', userId)
        .order('date_assigned', { ascending: false })
        .limit(1)
    ).pipe(
      switchMap(response => {
        if (response.error) {
          return of([]);
        }

        if (response.data && response.data.length > 0) {

          const sideQuest = response.data[0];

          return this.checkMissedDays(sideQuest.id).pipe(
            switchMap(() => {
              return this.checkAndReset(sideQuest.id).pipe(
                switchMap(() => {
                  return from(
                    this.supabaseService.getClient()
                      .from('user_daily_sidequests')
                      .select('*, daily_sidequest_pool(*)')
                      .eq('user_id', userId)
                      .order('date_assigned', { ascending: false })
                      .limit(1)
                  ).pipe(
                    map(updatedResponse => {
                      if (updatedResponse.error) {
                        return [];
                      }
                      return updatedResponse.data as UserDailySideQuest[];
                    })
                  );
                })
              );
            })
          );
        }


        return this.createUserDailySideQuest(userId).pipe(
          switchMap(newSideQuestId => {
            return from(
              this.supabaseService.getClient()
                .from('user_daily_sidequests')
                .select('*, daily_sidequest_pool(*)')
                .eq('user_id', userId)
                .order('date_assigned', { ascending: false })
                .limit(1)
            ).pipe(
              map(newResponse => {
                if (newResponse.error) {
                  return [];
                }
                return newResponse.data as UserDailySideQuest[];
              })
            );
          }),
          catchError(error => {
            return of([]);
          })
        );
      })
    );
  }

  recalculateSideQuestStreak(userId: string, selectedDate?: Date): Observable<void> {

    return this.getUserLatestDailySideQuest(userId).pipe(
      switchMap(sideQuest => {
        if (!sideQuest) {
          return of(undefined);
        }


        const toggleDate = selectedDate ? new Date(selectedDate) : new Date();
        toggleDate.setHours(0, 0, 0, 0);

        const yesterday = new Date(toggleDate);
        yesterday.setDate(toggleDate.getDate() - 1);

        let displayStreak = 0;

        let streakCount = 0;

        if (sideQuest.last_completed_date) {
          const lastCompletedDateStr = sideQuest.last_completed_date;
          const yesterdayStr = yesterday.toISOString().split('T')[0];


          if (lastCompletedDateStr === yesterdayStr) {
            streakCount = 1;
          } else {
          }
        }

        if (sideQuest.completed) {
          displayStreak = streakCount + 1;
        } else {
          displayStreak = streakCount;
        }

        return from(
          this.supabaseService.getClient()
            .from('user_daily_sidequests')
            .update({ streak: displayStreak })
            .eq('id', sideQuest.id)
        ).pipe(
          map(response => {
            if (response.error) {
              throw new Error(response.error.message);
            }

            return undefined;
          })
        );
      })
    );
  }

  private updateUserXP(userId: string, category: string, add: boolean = true, amount: number = 2): Observable<void> {

    return from(
      this.supabaseService.getClient()
        .from('profiles')
        .select(`${category}_xp`)
        .eq('id', userId)
        .single()
    ).pipe(
      switchMap(response => {
        if (response.error) {
          return throwError(() => new Error(response.error.message));
        }

        const currentXP = response.data[`${category}_xp`] || 0;
        const newXP = add ? currentXP + amount : Math.max(0, currentXP - amount);

        return from(
          this.supabaseService.getClient()
            .from('profiles')
            .update({ [`${category}_xp`]: newXP })
            .eq('id', userId)
        ).pipe(
          map(updateResponse => {
            if (updateResponse.error) {
              throw new Error(updateResponse.error.message);
            }

          }),
          catchError(error => {
            return throwError(() => error);
          })
        );
      })
    );
  }

  checkMissedDays(sideQuestId: string): Observable<void> {

    return from(
      this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*')
        .eq('id', sideQuestId)
        .single()
    ).pipe(
      switchMap(response => {
        if (response.error) {
          return throwError(() => new Error(response.error.message));
        }

        const sideQuest = response.data as UserDailySideQuest;
        if (!sideQuest) {
          return throwError(() => new Error('Side quest not found'));
        }

        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const todayStr = today.toISOString().split('T')[0];

        const yesterday = new Date(today);
        yesterday.setDate(today.getDate() - 1);
        const yesterdayStr = yesterday.toISOString().split('T')[0];


        const lastCompletedDateStr = sideQuest.last_completed_date;


        const missedDay = !lastCompletedDateStr ||
                         (lastCompletedDateStr < yesterdayStr &&
                          lastCompletedDateStr !== yesterdayStr);


        if (missedDay) {

          return from(
            this.supabaseService.getClient()
              .from('user_daily_sidequests')
              .update({
                streak: 0,
                last_completed_date: yesterdayStr  
              })
              .eq('id', sideQuestId)
          ).pipe(
            map(updateResponse => {
              if (updateResponse.error) {
                throw new Error(updateResponse.error.message);
              }



              return undefined;
            })
          );
        } else {
          return of(undefined);
        }
      })
    );
  }

  importSideQuestsFromJson(): Observable<boolean> {

    return this.http.get<any[]>('assets/data/sidequest-pool.json').pipe(
      switchMap(sideQuests => {
        if (!sideQuests || sideQuests.length === 0) {
          return of(false);
        }


        const sideQuestsToImport = sideQuests.map(quest => ({
          name: quest.name,
          description: quest.description || null,
          goal_value: quest.goal_value || 1,
          category: quest.category,
          goal_unit: quest.goal_unit || 'count',
          active: true,
          emoji: quest.emoji || '🎯'
        }));

        return from(
          this.supabaseService.getClient()
            .from('daily_sidequest_pool')
            .insert(sideQuestsToImport)
        ).pipe(
          map(response => {
            if (response.error) {
              return false;
            }

            return true;
          }),
          catchError(error => {
            return of(false);
          })
        );
      })
    );
  }
}
