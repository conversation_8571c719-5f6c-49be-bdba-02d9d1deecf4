﻿import { enableProdMode, importProvidersFrom } from '@angular/core';
import { bootstrapApplication } from '@angular/platform-browser';
import { provideHttpClient } from '@angular/common/http';

import { AppComponent } from './app/app.component';
import { provideRouter } from '@angular/router';
import { routes } from './app/app-routing.module';

import { provideIonicAngular } from '@ionic/angular/standalone';

import { ReactiveFormsModule } from '@angular/forms';

import { environment } from './environments/environment';

import { QuestService } from './app/services/quest.service';
import { SideQuestService } from './app/services/sidequest.service';
import { GoalService } from './app/services/goal.service';
import { FriendService } from './app/services/friend.service';
import { GroupService } from './app/services/group.service';
import { ActivityService } from './app/services/activity.service';
import { BadgeService } from './app/services/badge.service';
import { UserService } from './app/services/user.service';
import { AdminService } from './app/services/admin.service';
import { PreferencesService } from './app/services/preferences.service';
import { Animation, createAnimation } from '@ionic/angular/standalone';

export const fadeAnimation = (baseEl: HTMLElement, opts?: any): Animation => {
  const enteringEl = opts.enteringEl;
  const leavingEl = opts.leavingEl;

  const enteringAnimation = createAnimation()
    .addElement(enteringEl)
    .duration(300)
    .easing('ease-in-out')
    .fromTo('opacity', 0, 1);

  const leavingAnimation = createAnimation()
    .addElement(leavingEl)
    .duration(300)
    .easing('ease-in-out')
    .fromTo('opacity', 1, 0);

  return createAnimation()
    .addAnimation(enteringAnimation)
    .addAnimation(leavingAnimation);
};
if (environment.production) {
  enableProdMode();
}

bootstrapApplication(AppComponent, {
  providers: [
    provideRouter(routes),
    provideIonicAngular({
      animated: true,
      navAnimation: fadeAnimation
    }),
    provideHttpClient(),


    importProvidersFrom(ReactiveFormsModule),

    QuestService,
    SideQuestService,
    GoalService,
    FriendService,
    GroupService,
    ActivityService,
    BadgeService,
    UserService,
    AdminService,
    PreferencesService
  ]
});
