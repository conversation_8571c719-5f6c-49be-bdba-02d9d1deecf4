﻿<ion-content class="ion-padding">
  <div class="background-container">
    <div class="gradient-bg"></div>
    <div class="celestial-body"></div>
  </div>
  <ion-grid>
    <ion-row>
      <h1 class="ion-margin-bottom gradient-text">
        Upshift Yourself
      </h1>
      <ion-text class="ion-margin-top ion-margin-bottom dark-text join-text">
        Join over 300,000 people who have <br> already signed up for Upshift.
      </ion-text>
      <ion-button class="social-button" (click)="signInWithGoogle()">
        <div class="button-content">
          <ion-icon name="logo-google"></ion-icon>
          <span>Login with Google</span>
        </div>
      </ion-button>
      <ion-button class="ion-margin-top social-button" (click)="signInWithApple()">
        <div class="button-content">
          <ion-icon name="logo-apple"></ion-icon>
          <span>Login with Apple</span>
        </div>
      </ion-button>
      <ion-button class="skip-button ion-margin-bottom" [routerLink]="['/onboarding']">
        <div class="button-content">
          <ion-text class="button-text" slot="end">Skip for now</ion-text>
          <ion-icon name="arrow-forward-outline"></ion-icon>
        </div>
      </ion-button>
      <ion-text class="skip-text">
        Do you already have an account?
        <a [routerLink]="['/login']">Login</a>
      </ion-text>
    </ion-row>
  </ion-grid>
</ion-content>
