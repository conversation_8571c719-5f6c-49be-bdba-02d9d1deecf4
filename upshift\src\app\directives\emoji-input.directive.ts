﻿import { Directive, ElementRef, HostListener, Optional } from '@angular/core';
import { NgControl } from '@angular/forms';

@Directive({
  selector: '[appEmojiInput]',
  standalone: true
})
export class EmojiInputDirective {
  constructor(
    private el: ElementRef,
    @Optional() private ngControl: NgControl
  ) {}

  private isProcessing = false;

  @HostListener('input', ['$event'])
  onInput(event: Event) {
    if (this.isProcessing) {
      return;
    }

    this.isProcessing = true;

    try {
      const input = event.target as HTMLInputElement;
      const value = input.value;

      const emojiRegex = /\p{Extended_Pictographic}/u;
      const graphemes = [...value];

      const lastEmoji = graphemes.reverse().find(char => emojiRegex.test(char));

      const newValue = lastEmoji ? lastEmoji : '';

      input.value = newValue;

      if (this.ngControl && this.ngControl.control) {
        this.ngControl.control.setValue(newValue, { emitEvent: false });

        this.ngControl.control.markAsDirty();
        this.ngControl.control.markAsTouched();

        this.ngControl.control.updateValueAndValidity();
      }

    } finally {
      this.isProcessing = false;
    }
  }
}
