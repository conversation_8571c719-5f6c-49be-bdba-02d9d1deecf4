﻿import { Component, OnInit, AfterViewInit, ViewChild, ElementRef, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { Activity, ActivityType } from '../../models/activity.model';
import { TimeTrackerUnifiedService } from '../../services/time-tracker-unified.service';
import { take } from 'rxjs';
import { Chart, registerables } from 'chart.js';
import { SupabaseService } from '../../services/supabase.service';
import { EmojiInputDirective } from '../../directives/emoji-input.directive';

Chart.register(...registerables);

interface DateDisplay {
  date: string;
  day: number;
  is_today: boolean;
  is_selected: boolean;
  is_future: boolean;
}

@Component({
  selector: 'app-time-tracker',
  templateUrl: './time-tracker.page.html',
  styleUrls: ['./time-tracker.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule, EmojiInputDirective]
})
export class TimeTrackerPage implements OnInit, AfterViewInit {
  userId: string | null = null;

  dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  weekDates: DateDisplay[] = [];
  weekOffset = 0;
  selectedDate: Date = new Date();

  activityTypes: ActivityType[] = [];
  activities: Activity[] = [];

  selectedActivityId = '';
  hoursInput = 0;
  minutesInput = 0;
  customActivityName = '';
  customEmoji = '⚡';
  customHoursInput = 0;
  customMinutesInput = 0;

  showStandardInput = false;
  showCustomForm = false;

  totalTrackedHours = '0.0';
  remainingHours = '24.0';

  timeChart: Chart | null = null;

  @ViewChild('timeChart') chartCanvas: ElementRef | undefined;

  private supabaseService = inject(SupabaseService);
  private timeTrackerService = inject(TimeTrackerUnifiedService);

  constructor() {}

  ngOnInit() {
    this.supabaseService.currentUser$.pipe(
      take(1)
    ).subscribe(authUser => {
      if (authUser) {
        this.userId = authUser.id;
        this.loadActivityTypes();
        this.generateWeekDates();
        this.loadActivities();
      }
    });
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.initializeChart();
    }, 500);
  }

  loadActivityTypes() {
    this.timeTrackerService.getActivityTypes().subscribe((types: ActivityType[]) => {
      this.activityTypes = types;
    });
  }

  generateWeekDates() {
    const today = new Date();
    const currentDay = today.getDay(); 

    const mondayOffset = currentDay === 0 ? -6 : 1; 
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - currentDay + mondayOffset + (7 * this.weekOffset));

    this.weekDates = [];

    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);

      const dateStr = this.formatDate(date);
      const isToday = this.isSameDay(date, today);
      const isSelected = this.isSameDay(date, this.selectedDate);
      const isFuture = date > today;

      this.weekDates.push({
        date: dateStr,
        day: date.getDate(),
        is_today: isToday,
        is_selected: isSelected,
        is_future: isFuture
      });
    }
  }

  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  isSameDay(date1: Date, date2: Date): boolean {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  }

  changeWeek(offset: number) {
    this.weekOffset += offset;
    this.generateWeekDates();
  }

  selectDate(dateStr: string) {
    this.selectedDate = new Date(dateStr);
    this.generateWeekDates();
    this.loadActivities();
  }

  loadActivities() {
    if (!this.userId) return;

    const dateStr = this.formatDate(this.selectedDate);
    const userId = this.userId; 

    this.timeTrackerService.getDayTracking(userId, dateStr).subscribe(() => {
      this.timeTrackerService.getActivities(userId, dateStr).subscribe((activities: Activity[]) => {
        this.activities = activities;

        this.calculateTotals();

        this.updateChart();
      });
    });
  }

  calculateTotals() {
    const totalHours = this.activities.reduce((total, activity) => {
      return total + activity.hours + (activity.minutes / 60);
    }, 0);

    this.totalTrackedHours = totalHours.toFixed(1);

    const remainingHours = Math.max(0, 24 - totalHours);
    this.remainingHours = remainingHours.toFixed(1);

  }

  handleActivitySelection(event: Event) {
    const select = event.target as HTMLSelectElement;
    const value = select.value;

    this.selectedActivityId = value;
    this.showStandardInput = value !== '' && value !== 'custom';
    this.showCustomForm = value === 'custom';
  }

  addActivity() {
    if (!this.userId || !this.selectedActivityId || this.selectedActivityId === 'custom') return;
    if (this.hoursInput === 0 && this.minutesInput === 0) {
      alert('Please enter a time greater than 0');
      return;
    }

    if (!this.validateTotalTime(this.hoursInput, this.minutesInput)) {
      alert('⏱️ Total time cannot exceed 24 hours');
      return;
    }

    const activityType = this.activityTypes.find(type => type.id === this.selectedActivityId);
    if (!activityType) return;

    const existingActivity = this.activities.find(activity => activity.name === activityType.name);

    if (existingActivity) {
      const updatedHours = existingActivity.hours + this.hoursInput;
      const updatedMinutes = existingActivity.minutes + this.minutesInput;

      let finalHours = updatedHours + Math.floor(updatedMinutes / 60);
      let finalMinutes = updatedMinutes % 60;

      if (finalHours > 23) {
        finalHours = 23;
        finalMinutes = 59;
      }

      this.timeTrackerService.updateActivity(existingActivity.id, finalHours, finalMinutes).then(() => {
        existingActivity.hours = finalHours;
        existingActivity.minutes = finalMinutes;

        this.resetInputs();

        this.calculateTotals();

        this.updateChart();
      }).catch(error => {
        alert('Error updating activity. Please try again.');
      });
    } else {
      const userId = this.userId as string; 
      const dateStr = this.formatDate(this.selectedDate);

      this.timeTrackerService.createActivity(
        userId,
        dateStr,
        activityType.name,
        activityType.emoji,
        this.hoursInput,
        this.minutesInput,
        false
      ).then((result) => {
        this.activities.push({
          id: result.id,
          day_tracking_id: '', 
          name: activityType.name,
          emoji: activityType.emoji,
          hours: this.hoursInput,
          minutes: this.minutesInput,
          is_custom: false
        });

        this.resetInputs();

        this.calculateTotals();

        this.updateChart();
      }).catch(error => {
        alert('Error creating activity. Please try again.');
      });
    }
  }

  addCustomActivity() {
    if (!this.userId || !this.customActivityName.trim()) {
      alert('Please enter an activity name');
      return;
    }

    if (this.customHoursInput === 0 && this.customMinutesInput === 0) {
      alert('Please enter a time greater than 0');
      return;
    }

    if (!this.validateTotalTime(this.customHoursInput, this.customMinutesInput)) {
      alert('⏱️ Total time cannot exceed 24 hours');
      return;
    }

    const existingActivity = this.activities.find(activity =>
      activity.name.toLowerCase() === this.customActivityName.trim().toLowerCase()
    );

    if (existingActivity) {
      const updatedHours = existingActivity.hours + this.customHoursInput;
      const updatedMinutes = existingActivity.minutes + this.customMinutesInput;

      let finalHours = updatedHours + Math.floor(updatedMinutes / 60);
      let finalMinutes = updatedMinutes % 60;

      if (finalHours > 23) {
        finalHours = 23;
        finalMinutes = 59;
      }

      this.timeTrackerService.updateActivity(existingActivity.id, finalHours, finalMinutes).then(() => {
        existingActivity.hours = finalHours;
        existingActivity.minutes = finalMinutes;

        this.resetInputs();

        this.calculateTotals();

        this.updateChart();
      }).catch(error => {
        alert('Error updating activity. Please try again.');
      });
    } else {
      const userId = this.userId as string; 
      const dateStr = this.formatDate(this.selectedDate);

      this.timeTrackerService.createActivity(
        userId,
        dateStr,
        this.customActivityName.trim(),
        this.customEmoji,
        this.customHoursInput,
        this.customMinutesInput,
        true
      ).then((result) => {
        this.activities.push({
          id: result.id,
          day_tracking_id: '', 
          name: this.customActivityName.trim(),
          emoji: this.customEmoji,
          hours: this.customHoursInput,
          minutes: this.customMinutesInput,
          is_custom: true
        });

        this.resetInputs();

        this.calculateTotals();

        this.updateChart();
      }).catch(error => {
        alert('Error creating activity. Please try again.');
      });
    }
  }

  updateActivity(activity: Activity) {
    if (!activity.id) return;

    if (activity.hours < 0) activity.hours = 0;
    if (activity.minutes < 0) activity.minutes = 0;
    if (activity.hours > 23) activity.hours = 23;
    if (activity.minutes > 59) activity.minutes = 59;

    const currentHours = activity.hours;
    const currentMinutes = activity.minutes;

    const otherActivitiesTime = this.activities
      .filter(a => a.id !== activity.id)
      .reduce((total, a) => total + a.hours + (a.minutes / 60), 0);

    const totalTime = otherActivitiesTime + currentHours + (currentMinutes / 60);

    if (totalTime > 24) {
      alert('Total time cannot exceed 24 hours');
      this.loadActivities();
      return;
    }

    this.timeTrackerService.updateActivity(activity.id, activity.hours, activity.minutes).then(() => {
      this.calculateTotals();

      this.updateChart();
    }).catch(error => {
      alert('Error updating activity. Please try again.');
      this.loadActivities(); 
    });
  }

  deleteActivity(activityId: string) {
    if (!activityId) return;

    this.timeTrackerService.deleteActivity(activityId).then(() => {
      this.activities = this.activities.filter(a => a.id !== activityId);

      this.calculateTotals();

      this.updateChart();
    }).catch(error => {
      alert('Error deleting activity. Please try again.');
    });
  }

  resetInputs() {
    const currentEmoji = this.customEmoji;

    this.selectedActivityId = '';
    this.hoursInput = 0;
    this.minutesInput = 0;
    this.customActivityName = '';
    this.customEmoji = currentEmoji || '⚡';
    this.customHoursInput = 0;
    this.customMinutesInput = 0;
    this.showStandardInput = false;
    this.showCustomForm = false;

    setTimeout(() => {
      const selectElement = document.getElementById('activitySelect') as HTMLSelectElement;
      if (selectElement) {
        selectElement.value = '';

        this.selectedActivityId = '';
        this.showStandardInput = false;
        this.showCustomForm = false;
      }
    }, 0);
  }

  updateTotals() {
  }

  validateTotalTime(hours: number, minutes: number): boolean {
    const existingTime = this.activities.reduce((total, activity) => {
      return total + activity.hours + (activity.minutes / 60);
    }, 0);

    const totalTime = existingTime + hours + (minutes / 60);

    return totalTime <= 24;
  }

  initializeChart() {
    const canvas = document.getElementById('timeChart') as HTMLCanvasElement;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    if (this.timeChart) {
      this.timeChart.destroy();
      this.timeChart = null;
    }

    Chart.register({
      id: 'centerText',
      beforeDraw: (chart: any) => {
        const ctx = chart.ctx;
        const width = chart.width;
        const height = chart.height;

        ctx.restore();
        ctx.font = 'bold 20px Inter';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        ctx.fillStyle = '#FFFFFF';
        ctx.fillText('24h', width / 2, height / 2);
        ctx.save();
      }
    });

    try {
      this.timeChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: [],
          datasets: [{
            data: [],
            backgroundColor: [],
            borderWidth: 0,
            borderRadius: 5,
            spacing: 2
          }]
        },
        options: {
          cutout: '75%',
          radius: '90%',
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              enabled: true,
              callbacks: {
                label: function(context: any) {
                  const value = context.raw as number;
                  const hours = Math.floor(value);
                  const minutes = Math.round((value - hours) * 60);
                  return `${hours}h ${minutes}m`;
                }
              }
            }
          },
          animation: {
            animateRotate: true,
            animateScale: true
          }
        }
      });

      this.updateChart();
    } catch (error) {
    }
  }

  updateChart() {
    if (!this.timeChart) {
      this.initializeChart();
      return;
    }

    try {
      const data = this.processActivitiesForChart();

      this.timeChart.data.labels = data.labels;
      this.timeChart.data.datasets[0].data = data.values;
      this.timeChart.data.datasets[0].backgroundColor = this.generateColors(data.labels.length);

      this.timeChart.update();
    } catch (error) {
      this.timeChart = null;
      setTimeout(() => {
        this.initializeChart();
      }, 100);
    }
  }

  processActivitiesForChart() {
    const labels: string[] = [];
    const values: number[] = [];

    this.activities.forEach(activity => {
      labels.push(`${activity.emoji} ${activity.name}`);
      values.push(activity.hours + (activity.minutes / 60));
    });

    const totalHours = values.reduce((a, b) => a + b, 0);
    if (totalHours < 24) {
      labels.push('Remaining');
      values.push(24 - totalHours);
    }

    return { labels, values };
  }

  generateColors(count: number) {
    const colors: string[] = [];

    this.activities.forEach(activity => {
      const activityType = this.activityTypes.find(type => type.name === activity.name);

      if (activityType && activityType.color) {
        colors.push(activityType.color);
      } else if (activity.is_custom) {
        colors.push('#2C3E50'); 
      } else {
        colors.push('#2C3E50');
      }
    });

    if (count > colors.length) {
      colors.push('#1C1C1E');
    }

    return colors;
  }
}
