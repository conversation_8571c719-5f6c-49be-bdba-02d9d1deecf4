{"ast": null, "code": "var _CalculatingPage;\nimport { CommonModule } from '@angular/common';\nimport { IonicModule } from '@ionic/angular';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@ionic/angular\";\nexport class CalculatingPage {\n  constructor(router) {\n    this.router = router;\n    this.progress = 0;\n    this.subtitle = 'Learning relapse triggers';\n  }\n  ngOnInit() {\n    this.startProgress();\n  }\n  startProgress() {\n    const interval = setInterval(() => {\n      this.progress += 1;\n      if (this.progress >= 100) {\n        clearInterval(interval);\n        this.router.navigate(['/results']);\n      }\n    }, 50);\n  }\n}\n_CalculatingPage = CalculatingPage;\n_CalculatingPage.ɵfac = function CalculatingPage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CalculatingPage)(i0.ɵɵdirectiveInject(i1.Router));\n};\n_CalculatingPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _CalculatingPage,\n  selectors: [[\"app-calculating\"]],\n  decls: 18,\n  vars: 3,\n  consts: [[1, \"ion-padding\"], [1, \"calculating-container\"], [1, \"progress-circle\"], [\"viewBox\", \"0 0 36 36\", 1, \"circular-chart\"], [\"id\", \"gradient\", \"x1\", \"0%\", \"y1\", \"0%\", \"x2\", \"100%\", \"y2\", \"0%\"], [\"offset\", \"0%\", 2, \"stop-color\", \"var(--text)\"], [\"offset\", \"33%\", 2, \"stop-color\", \"var(--accent)\"], [\"offset\", \"66%\", 2, \"stop-color\", \"var(--text)\"], [\"offset\", \"100%\", 2, \"stop-color\", \"var(--accent)\"], [\"d\", \"M18 2.0845\\n            a 15.9155 15.9155 0 0 1 0 31.831\\n            a 15.9155 15.9155 0 0 1 0 -31.831\", 1, \"circle-bg\"], [\"d\", \"M18 2.0845\\n            a 15.9155 15.9155 0 0 1 0 31.831\\n            a 15.9155 15.9155 0 0 1 0 -31.831\", 1, \"circle\"], [\"x\", \"18\", \"y\", \"20.35\", 1, \"percentage\"], [1, \"subtitle\"]],\n  template: function CalculatingPage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ion-content\", 0)(1, \"div\", 1)(2, \"div\", 2);\n      i0.ɵɵnamespaceSVG();\n      i0.ɵɵelementStart(3, \"svg\", 3)(4, \"defs\")(5, \"linearGradient\", 4);\n      i0.ɵɵelement(6, \"stop\", 5)(7, \"stop\", 6)(8, \"stop\", 7)(9, \"stop\", 8);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelement(10, \"path\", 9)(11, \"path\", 10);\n      i0.ɵɵelementStart(12, \"text\", 11);\n      i0.ɵɵtext(13);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵnamespaceHTML();\n      i0.ɵɵelementStart(14, \"h1\");\n      i0.ɵɵtext(15, \"Calculating\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(16, \"p\", 12);\n      i0.ɵɵtext(17);\n      i0.ɵɵelementEnd()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(11);\n      i0.ɵɵattribute(\"stroke-dasharray\", ctx.progress + \", 100\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵtextInterpolate1(\"\", ctx.progress, \"%\");\n      i0.ɵɵadvance(4);\n      i0.ɵɵtextInterpolate(ctx.subtitle);\n    }\n  },\n  dependencies: [IonicModule, i2.IonContent, CommonModule],\n  styles: [\"ion-content[_ngcontent-%COMP%] {\\n  --background: var(--bg, #1a1b41);\\n}\\n\\n.calculating-container[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  text-align: center;\\n}\\n.calculating-container[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  font-weight: 600;\\n  margin: 24px 0 8px;\\n}\\n.calculating-container[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  opacity: 0.7;\\n  margin: 0;\\n}\\n\\n.progress-circle[_ngcontent-%COMP%] {\\n  width: 200px;\\n  height: 200px;\\n  margin-bottom: 20px;\\n}\\n\\n.circular-chart[_ngcontent-%COMP%] {\\n  display: block;\\n  margin: 0 auto;\\n  max-width: 100%;\\n  max-height: 100%;\\n  transform: rotate(-90deg);\\n}\\n\\n.circle-bg[_ngcontent-%COMP%] {\\n  fill: none;\\n  stroke: rgba(255, 255, 255, 0.1);\\n  stroke-width: 2.8;\\n}\\n\\n@keyframes _ngcontent-%COMP%_gradient {\\n  0% {\\n    stroke-dashoffset: 0;\\n  }\\n  100% {\\n    stroke-dashoffset: -100;\\n  }\\n}\\n.circle[_ngcontent-%COMP%] {\\n  fill: none;\\n  stroke: url(#gradient);\\n  stroke-width: 2.8;\\n  stroke-linecap: round;\\n  transition: stroke-dasharray 0.1s ease;\\n}\\n\\n.percentage[_ngcontent-%COMP%] {\\n  fill: white;\\n  font-family: sans-serif;\\n  font-size: 8px;\\n  text-anchor: middle;\\n  transform: rotate(90deg);\\n  transform-origin: center;\\n  font-weight: bold;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["CommonModule", "IonicModule", "CalculatingPage", "constructor", "router", "progress", "subtitle", "ngOnInit", "startProgress", "interval", "setInterval", "clearInterval", "navigate", "i0", "ɵɵdirectiveInject", "i1", "Router", "selectors", "decls", "vars", "consts", "template", "CalculatingPage_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵtextInterpolate", "i2", "IonContent", "styles"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\calculating\\calculating.page.ts", "C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\calculating\\calculating.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-calculating',\r\n  templateUrl: './calculating.page.html',\r\n  styleUrls: ['./calculating.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule]\r\n})\r\nexport class CalculatingPage implements OnInit {\r\n  progress = 0;\r\n  subtitle = 'Learning relapse triggers';\r\n\r\n  constructor(private router: Router) {}\r\n\r\n  ngOnInit() {\r\n    this.startProgress();\r\n  }\r\n\r\n  private startProgress() {\r\n    const interval = setInterval(() => {\r\n      this.progress += 1;\r\n      if (this.progress >= 100) {\r\n        clearInterval(interval);\r\n        this.router.navigate(['/results']); \n      }\r\n    }, 50);\r\n  }\r\n} ", "<ion-content class=\"ion-padding\">\r\n  <div class=\"calculating-container\">\r\n    <div class=\"progress-circle\">\r\n      <svg viewBox=\"0 0 36 36\" class=\"circular-chart\">\r\n        <defs>\r\n          <linearGradient id=\"gradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\r\n            <stop offset=\"0%\" style=\"stop-color: var(--text)\" />\r\n            <stop offset=\"33%\" style=\"stop-color: var(--accent)\" />\r\n            <stop offset=\"66%\" style=\"stop-color: var(--text)\" />\r\n            <stop offset=\"100%\" style=\"stop-color: var(--accent)\" />\r\n          </linearGradient>\r\n        </defs>\r\n        <path class=\"circle-bg\"\r\n          d=\"M18 2.0845\r\n            a 15.9155 15.9155 0 0 1 0 31.831\r\n            a 15.9155 15.9155 0 0 1 0 -31.831\"\r\n        />\r\n        <path class=\"circle\"\r\n          [attr.stroke-dasharray]=\"progress + ', 100'\"\r\n          d=\"M18 2.0845\r\n            a 15.9155 15.9155 0 0 1 0 31.831\r\n            a 15.9155 15.9155 0 0 1 0 -31.831\"\r\n        />\r\n        <text x=\"18\" y=\"20.35\" class=\"percentage\">{{progress}}%</text>\r\n      </svg>\r\n    </div>\r\n    <h1>Calculating</h1>\r\n    <p class=\"subtitle\">{{subtitle}}</p>\r\n  </div>\r\n</ion-content> "], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;;;;AAU5C,OAAM,MAAOC,eAAe;EAI1BC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAH1B,KAAAC,QAAQ,GAAG,CAAC;IACZ,KAAAC,QAAQ,GAAG,2BAA2B;EAED;EAErCC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;EACtB;EAEQA,aAAaA,CAAA;IACnB,MAAMC,QAAQ,GAAGC,WAAW,CAAC,MAAK;MAChC,IAAI,CAACL,QAAQ,IAAI,CAAC;MAClB,IAAI,IAAI,CAACA,QAAQ,IAAI,GAAG,EAAE;QACxBM,aAAa,CAACF,QAAQ,CAAC;QACvB,IAAI,CAACL,MAAM,CAACQ,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;MACpC;IACF,CAAC,EAAE,EAAE,CAAC;EACR;;mBAlBWV,eAAe;;mCAAfA,gBAAe,EAAAW,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA;AAAA;;QAAfd,gBAAe;EAAAe,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCVxBV,EAFJ,CAAAY,cAAA,qBAAiC,aACI,aACJ;;MAGvBZ,EAFJ,CAAAY,cAAA,aAAgD,WACxC,wBAC4D;MAI9DZ,EAHA,CAAAa,SAAA,cAAoD,cACG,cACF,cACG;MAE5Db,EADE,CAAAc,YAAA,EAAiB,EACZ;MAMPd,EALA,CAAAa,SAAA,eAIE,gBAMA;MACFb,EAAA,CAAAY,cAAA,gBAA0C;MAAAZ,EAAA,CAAAe,MAAA,IAAa;MAE3Df,EAF2D,CAAAc,YAAA,EAAO,EAC1D,EACF;;MACNd,EAAA,CAAAY,cAAA,UAAI;MAAAZ,EAAA,CAAAe,MAAA,mBAAW;MAAAf,EAAA,CAAAc,YAAA,EAAK;MACpBd,EAAA,CAAAY,cAAA,aAAoB;MAAAZ,EAAA,CAAAe,MAAA,IAAY;MAEpCf,EAFoC,CAAAc,YAAA,EAAI,EAChC,EACM;;;MAXJd,EAAA,CAAAgB,SAAA,IAA4C;;MAKJhB,EAAA,CAAAgB,SAAA,GAAa;MAAbhB,EAAA,CAAAiB,kBAAA,KAAAN,GAAA,CAAAnB,QAAA,MAAa;MAIvCQ,EAAA,CAAAgB,SAAA,GAAY;MAAZhB,EAAA,CAAAkB,iBAAA,CAAAP,GAAA,CAAAlB,QAAA,CAAY;;;iBDjBxBL,WAAW,EAAA+B,EAAA,CAAAC,UAAA,EAAEjC,YAAY;EAAAkC,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}