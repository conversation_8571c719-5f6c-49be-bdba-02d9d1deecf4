﻿import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-transition',
  templateUrl: './transition.component.html',
  styleUrls: ['./transition.component.scss'],
  standalone: true,
  imports: [CommonModule]
})
export class TransitionComponent implements OnInit {
  @ViewChild('videoPlayer') videoPlayer!: ElementRef<HTMLVideoElement>;

  constructor(private router: Router) { }

  ngOnInit(): void {
  }

  ngAfterViewInit(): void {
    const video = this.videoPlayer.nativeElement;

    video.play();

    setTimeout(() => {
      this.startSpeedingUp(video);
    }, 2000);

    video.addEventListener('ended', () => {
      this.navigateToOnboarding();
    });
  }

  startSpeedingUp(video: HTMLVideoElement): void {
    let playbackRate = 1.0;
    let scale = 1.0;

    const interval = setInterval(() => {
      playbackRate += 0.3; 
      scale += 0.15;      

      if (playbackRate <= 5.0) { 
        video.playbackRate = playbackRate;
        video.style.transform = `scale(${scale})`;
      } else {
        clearInterval(interval);
        this.addFlashEffect();
      }
    }, 50); 
  }

  addFlashEffect(): void {
    const flash = document.createElement('div');
    flash.className = 'flash-effect';
    document.body.appendChild(flash);

    setTimeout(() => {
      flash.style.opacity = '1';

      setTimeout(() => {
        this.navigateToOnboarding();
      }, 150);
    }, 10);
  }

  navigateToOnboarding(): void {
    this.router.navigate(['/onboarding']);
  }
}