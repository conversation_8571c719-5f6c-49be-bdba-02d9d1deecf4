﻿import { <PERSON><PERSON>ate<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QuestPeriod, QuestPriority, QuestType } from './quest.model';

export interface Group {
  id?: string; 
  name: string;
  emoji: string;
  admin_id: string;
  enable_sidequests: boolean;
  created: Date;

  timezone: string;

  country?: string;

  level: number;
  strength_xp: number;
  money_xp: number;
  health_xp: number;
  knowledge_xp: number;

  invitation_code?: string;
  code_expiry?: Date;
}

export interface GroupMember {
  id?: string; 
  group_id: string;
  user_id: string;
  nickname: string;
  is_admin: boolean;
  joined_date: Date;
}

export interface GroupQuest {
  id?: string; 
  group_id: string;
  name: string;
  description?: string; 
  emoji: string;
  category: QuestCategory;
  priority: QuestPriority;
  quest_type: QuestType;

  goal_value: number;
  goal_unit: QuestGoalUnit;
  goal_period: QuestPeriod;

  task_days_of_week?: string;
  task_days_of_month?: string;

  streak: number;
  created: Date;
}

export interface GroupQuestProgress {
  id?: string; 
  quest_id: string;
  user_id: string;
  date: Date;
  value_achieved: number;
  completed: boolean;
}

export interface GroupSideQuestPool {
  id?: string; 
  name: string;
  description: string;
  goal_value: number;
  category: QuestCategory;
  goal_unit: QuestGoalUnit;
  active: boolean;
  emoji: string;
}

export interface GroupSideQuest {
  id?: string; 
  group_id: string;
  current_quest_id: string;
  streak: number;
  last_completed_date?: Date;
  date_assigned: Date;
  completed: boolean;
  value_achieved: number;
  category: QuestCategory;
}

export interface GroupSideQuestMemberStatus {
  id?: string; 
  group_quest_id: string;
  member_id: string;
  completed: boolean;
  value_achieved: number;
  last_updated: Date;
}

export interface GroupJoinRequest {
  id?: string;
  group_id: string;
  username_invited: string;
  invited_by: string;
  created: Date;
  status?: string;
  nickname?: string;
  requested_at?: Date;
  inviter_username?: string; 
  groups?: any; 
}
