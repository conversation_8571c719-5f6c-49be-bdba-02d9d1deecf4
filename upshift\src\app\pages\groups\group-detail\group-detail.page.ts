﻿import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule, ViewWillEnter, ViewDidEnter, ToastController } from '@ionic/angular';
import { ActivatedRoute, Router, RouterModule, NavigationEnd } from '@angular/router';
import { Subscription, take, firstValueFrom, combineLatest, switchMap, map, of, Observable, filter } from 'rxjs';

import { SupabaseService } from '../../../services/supabase.service';
import { GroupService } from '../../../services/group.service';
import { UserService } from '../../../services/user.service';
import { XpService, EntityType } from '../../../services/xp.service';
import { Group, GroupMember } from '../../../models/supabase.models';
import { EmojiInputDirective } from '../../../directives/emoji-input.directive';
import { ComponentsModule } from '../../../components/components.module';
import { GroupWaitingRoomComponent } from '../../../components/group-waiting-room/group-waiting-room.component';
import { QuestType, QuestPeriod, QuestPriority, QuestCategory, QuestGoalUnit } from '../../../models/quest.model';

interface GroupWithOptionalId extends Omit<Group, 'id'> {
  id?: string;
}

interface WeekDate {
  date: string;
  day: number;
  isToday: boolean;
  isSelected: boolean;
  isFuture: boolean;
  isBeforeJoin: boolean;
  completionPercentage?: number;
  totalQuests?: number;
  completedQuests?: number;
}

interface Quest {
  id: string;
  name: string;
  description: string;
  emoji: string;
  category: string;
  quest_type: string;
  goal_value: number;
  goal_unit: string;
  value_achieved: number;
  completed: boolean;
  streak: number;
  is_side_quest: boolean;
  completed_members: number;
  total_members: number;
  message?: string;
}

interface DailyQuest {
  id: string;
  streak: number;
  completed_members: number;
  total_members: number;
  current_quest: {
    id: string;
    name: string;
    description: string;
    emoji: string;
    goal_value: number;
    goal_unit: string;
  };
}

interface MemberStatus {
  id: string;
  completed: boolean;
  value_achieved: number;
}

interface MemberWithProfile extends GroupMember {
  profile_picture?: string;
  username?: string;
  total_xp?: number;
  completed_quests?: number;
  max_streak?: number;
}

interface GroupActivity {
  id: string;
  user_id: string;
  user_nickname: string;
  message: string;
  icon: string;
  color: string;
  created_at: Date;
}

interface SideQuest {
  id: string;
  title: string;
  description: string;
  category: string;
  xp_reward: number;
  completed: boolean;
}

@Component({
  selector: 'app-group-detail',
  templateUrl: './group-detail.page.html',
  styleUrls: ['./group-detail.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule, EmojiInputDirective, ComponentsModule, GroupWaitingRoomComponent]
})
export class GroupDetailPage implements OnInit, OnDestroy, ViewWillEnter, ViewDidEnter {
  userId: string | null = null;

  groupId: string | null = null;
  group: GroupWithOptionalId | null = null;
  members: MemberWithProfile[] = [];
  isAdmin = false;
  requiredXp = 0;
  joinedDate: string = '';

  dayNames: string[] = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  weekOffset: number = 0;
  weekDates: WeekDate[] = [];
  selectedDate: string = '';
  headerText: string = '';
  isBeforeJoinDate: boolean = false;
  joinedToday: boolean = false;

  quests: Quest[] = [];
  dailyQuest: DailyQuest | null = null;
  memberStatus: MemberStatus = { id: '', completed: false, value_achieved: 0 };

  showAddQuestModal: boolean = false;
  newQuest = {
    emoji: '🎯',
    name: '',
    description: '',
    quest_type: 'build' as QuestType,
    category: '' as QuestCategory,
    priority: 'basic' as QuestPriority,
    goal_value: 1,
    goal_unit: 'count' as QuestGoalUnit,
    goal_period: 'day' as QuestPeriod
  };

  daysOfWeek = [
    { value: 'Monday', label: 'M', selected: false },
    { value: 'Tuesday', label: 'T', selected: false },
    { value: 'Wednesday', label: 'W', selected: false },
    { value: 'Thursday', label: 'T', selected: false },
    { value: 'Friday', label: 'F', selected: false },
    { value: 'Saturday', label: 'S', selected: false },
    { value: 'Sunday', label: 'S', selected: false }
  ];

  daysOfMonth = Array.from({ length: 31 }, (_, i) => ({ value: i + 1, selected: false }));

  private subscriptions: Subscription[] = [];

  private supabaseService = inject(SupabaseService);
  private groupService = inject(GroupService);
  private userService = inject(UserService);
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private toastController = inject(ToastController);

  constructor() {}

  ngOnInit() {
    this.subscriptions.push(
      this.supabaseService.currentUser$.subscribe(user => {
        if (user) {
          this.userId = user.id;

          this.route.paramMap.pipe(take(1)).subscribe(params => {
            this.groupId = params.get('id');
            if (this.groupId) {
              this.loadGroup();
              this.loadMembers();

              this.route.queryParams.pipe(take(1)).subscribe(queryParams => {
                this.initializeCalendar();
              });
            }
          });
        } else {
          this.router.navigate(['/login']);
        }
      })
    );

    this.subscriptions.push(
      this.router.events.pipe(
        filter(event => event instanceof NavigationEnd),
        filter(() => !!this.groupId) 
      ).subscribe(() => {
        this.refreshGroupData();
      })
    );
  }

  refreshGroupData() {
    if (!this.groupId) return;

    this.loadGroup();
    this.loadMembers();

    if (this.selectedDate) {
      this.loadQuestsForDate(this.selectedDate);
      this.loadDailySideQuest(this.selectedDate);
    }
  }

  ionViewWillEnter() {
    this.refreshGroupData();
  }

  ionViewDidEnter() {
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  loadGroup() {
    if (!this.groupId) return;

    this.subscriptions.push(
      this.groupService.getGroup(this.groupId).subscribe(group => {
        if (group) {
          this.group = group;
          this.calculateRequiredXp();
        } else {
          this.router.navigate(['/groups']);
        }
      })
    );
  }

  loadMembers() {
    if (!this.groupId || !this.userId) return;

    this.subscriptions.push(
      this.groupService.getGroupMembers(this.groupId).subscribe(members => {
        const currentUserMember = members.find(m => m.user_id === this.userId);
        this.isAdmin = currentUserMember?.is_admin || false;

        if (currentUserMember && currentUserMember.joined_date) {
          this.joinedDate = new Date(currentUserMember.joined_date).toISOString().split('T')[0];

          const joinedDate = new Date(currentUserMember.joined_date);
          joinedDate.setHours(0, 0, 0, 0);

          const today = new Date();
          today.setHours(0, 0, 0, 0);

          this.joinedToday = joinedDate.getTime() === today.getTime();
        }

        const membersWithProfiles: MemberWithProfile[] = [];

        const membersCopy = members.map(member => ({
          ...member,
          id: member.id || '',
          group_id: member.group_id || '',
          user_id: member.user_id || '',
          nickname: member.nickname || '',
          is_admin: member.is_admin || false,
          joined_date: member.joined_date || new Date()
        }));

        for (const member of membersCopy) {
          this.userService.getUserProfile(member.user_id).pipe(take(1)).subscribe(profile => {
            this.userService.getUserStats(member.user_id).pipe(take(1)).subscribe(stats => {
              membersWithProfiles.push({
                ...member,
                profile_picture: profile?.profile_picture || undefined,
                username: profile?.username,
                total_xp: stats?.total_xp || 0,
                completed_quests: stats?.completed_quests || 0,
                max_streak: stats?.max_streak || 0
              });

              this.members = [...membersWithProfiles];
            });
          });
        }
      })
    );
  }

  calculateRequiredXp() {
    if (!this.group) return;

    this.requiredXp = this.group.level * 1000;
  }

  getWeekOffset(direction: number): number {
    return this.weekOffset + direction;
  }

  initializeCalendar() {

    const today = new Date();

    this.route.queryParams.subscribe(params => {

      if (params['week_offset']) {
        try {
          this.weekOffset = parseInt(params['week_offset']);
        } catch (error) {
          this.weekOffset = 0;
        }
      } else {
        this.weekOffset = 0;
      }

      let selectedDate = today;
      if (params['date']) {
        try {
          selectedDate = new Date(params['date']);

          if (isNaN(selectedDate.getTime())) {
            selectedDate = today;
          }
        } catch (error) {
          selectedDate = today;
        }
      }

      this.selectedDate = this.formatDate(selectedDate);

      const startDate = new Date(today);
      startDate.setDate(today.getDate() + (this.weekOffset * 7));

      this.generateWeekDates(startDate);

      this.updateHeaderText();

      this.loadQuestsForDate(this.selectedDate);
      this.loadDailySideQuest(this.selectedDate);
    });
  }

  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  generateWeekDates(startDate: Date) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const currentDay = startDate.getDay(); 
    const mondayOffset = currentDay === 0 ? -6 : 1; 
    const monday = new Date(startDate);
    monday.setDate(startDate.getDate() - currentDay + mondayOffset);


    const dates: WeekDate[] = [];

    for (let i = 0; i < 7; i++) {
      const date = new Date(monday);
      date.setDate(monday.getDate() + i);

      const dateStr = this.formatDate(date);
      date.setHours(0, 0, 0, 0);
      const isToday = date.getTime() === today.getTime();

      const isFuture = date.getTime() > today.getTime();

      let isBeforeJoin = false;
      if (this.members.length > 0 && this.userId) {
        const currentMember = this.members.find(m => m.user_id === this.userId);
        if (currentMember && currentMember.joined_date) {
          const joinDate = new Date(currentMember.joined_date);
          joinDate.setHours(0, 0, 0, 0);
          isBeforeJoin = date < joinDate;
        }
      }

      dates.push({
        date: dateStr,
        day: date.getDate(),
        isToday,
        isSelected: dateStr === this.selectedDate,
        isFuture,
        isBeforeJoin,
        completionPercentage: 0,
        totalQuests: 0,
        completedQuests: 0
      });
    }

    this.weekDates = dates;

    this.updateWeekDateProgress();
  }

  navigateWeek(direction: number) {

    this.route.queryParams.pipe(take(1)).subscribe(params => {
      let currentWeekOffset = 0;
      if (params['week_offset']) {
        try {
          currentWeekOffset = parseInt(params['week_offset']);
        } catch (error) {
        }
      }

      const newWeekOffset = currentWeekOffset + direction;

      const firstDate = new Date(this.weekDates[0].date);

      firstDate.setDate(firstDate.getDate() + (direction * 7));

      this.router.navigate([], {
        relativeTo: this.route,
        queryParams: {
          week_offset: newWeekOffset,
          date: this.selectedDate 
        }
      });

      this.generateWeekDates(firstDate);

      const selectedDateExists = this.weekDates.some(date => date.date === this.selectedDate);
      if (!selectedDateExists) {
        const validDate = this.weekDates.find(date => !date.isFuture && !date.isBeforeJoin);
        if (validDate) {
          this.selectDate(validDate);
        }
      }
    });
  }

  async selectDate(date: WeekDate) {

    if (date.isFuture) {

      const toast = await this.toastController.create({
        message: 'Cannot select future dates',
        duration: 2000,
        position: 'bottom',
        color: 'warning'
      });
      await toast.present();

      return;
    }

    if (date.isBeforeJoin) {
      this.isBeforeJoinDate = true;

      const toast = await this.toastController.create({
        message: 'You cannot access dates before you joined the group',
        duration: 2000,
        position: 'bottom',
        color: 'warning'
      });
      await toast.present();

      return;
    } else {
      this.isBeforeJoinDate = false;
    }

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { date: date.date },
      queryParamsHandling: 'merge' 
    });

    this.weekDates.forEach(d => d.isSelected = d.date === date.date);
    this.selectedDate = date.date;


    this.updateHeaderText();

    this.loadQuestsForDate(date.date);
    this.loadDailySideQuest(date.date);
  }

  updateHeaderText() {
    if (!this.selectedDate) {
      this.headerText = 'Today';
      return;
    }

    const date = new Date(this.selectedDate);
    const today = new Date();

    date.setHours(0, 0, 0, 0);
    today.setHours(0, 0, 0, 0);

    console.log('GroupDetailPage: Comparing dates:', {
      selectedDate: this.selectedDate,
      selectedDateObj: date.toISOString(),
      todayObj: today.toISOString(),
      selectedTime: date.getTime(),
      todayTime: today.getTime(),
      isToday: date.getTime() === today.getTime()
    });

    if (date.getTime() === today.getTime()) {
      this.headerText = 'Today';
    } else {
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      console.log('GroupDetailPage: Checking if yesterday:', {
        yesterdayObj: yesterday.toISOString(),
        yesterdayTime: yesterday.getTime(),
        isYesterday: date.getTime() === yesterday.getTime()
      });

      if (date.getTime() === yesterday.getTime()) {
        this.headerText = 'Yesterday';
      } else {
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        console.log('GroupDetailPage: Checking if tomorrow:', {
          tomorrowObj: tomorrow.toISOString(),
          tomorrowTime: tomorrow.getTime(),
          isTomorrow: date.getTime() === tomorrow.getTime()
        });

        if (date.getTime() === tomorrow.getTime()) {
          this.headerText = 'Tomorrow';
        } else {
          this.headerText = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        }
      }
    }

  }

  hasFutureDates(): boolean {
    return this.weekDates.some(date => date.isFuture);
  }

  isToday(): boolean {
    if (!this.selectedDate) return false;

    const date = new Date(this.selectedDate);
    const today = new Date();

    date.setHours(0, 0, 0, 0);
    today.setHours(0, 0, 0, 0);

    return date.getTime() === today.getTime();
  }

  isYesterday(): boolean {
    if (!this.selectedDate) return false;

    const date = new Date(this.selectedDate);
    const today = new Date();

    date.setHours(0, 0, 0, 0);
    today.setHours(0, 0, 0, 0);

    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    return date.getTime() === yesterday.getTime();
  }

  isTomorrow(): boolean {
    if (!this.selectedDate) return false;

    const date = new Date(this.selectedDate);
    const today = new Date();

    date.setHours(0, 0, 0, 0);
    today.setHours(0, 0, 0, 0);

    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    return date.getTime() === tomorrow.getTime();
  }

  updateWeekDateProgress() {
    this.weekDates.forEach(date => {
      if (date.isFuture || date.isBeforeJoin) {
        date.completionPercentage = 0;
        date.totalQuests = 0;
        date.completedQuests = 0;
        return;
      }

      date.totalQuests = 0;
      date.completedQuests = 0;
      date.completionPercentage = 0;

    });
  }

  loadQuestsForDate(dateStr: string) {
    if (!this.groupId || !this.userId) return;


    this.subscriptions.push(
      this.groupService.getGroupQuests(this.groupId).subscribe(
        quests => {
          if (quests && quests.length > 0) {

            const selectedDate = new Date(dateStr);
            const filteredQuests = quests.filter(quest => {
              const createdDate = new Date(quest.created);
              createdDate.setHours(0, 0, 0, 0);
              selectedDate.setHours(0, 0, 0, 0);
              return createdDate <= selectedDate;
            });


            const questObservables = filteredQuests.map(quest => {
              if (!quest.id || !this.userId) {
                return of(null);
              }

              return this.groupService.getGroupQuestProgress(quest.id, this.userId, new Date(dateStr)).pipe(
                switchMap(progress => {
                  if (!quest.id) {
                    return of({} as Quest);
                  }
                  return this.groupService.getGroupQuestProgressForDate(quest.id, new Date(dateStr)).pipe(
                    map(allProgress => {
                      const selectedDate = new Date(dateStr);
                      selectedDate.setHours(0, 0, 0, 0); 

                      const eligibleMembers = this.members.filter(member => {
                        if (!member.joined_date) return false;
                        const joinDate = new Date(member.joined_date);
                        joinDate.setHours(0, 0, 0, 0);
                        return joinDate < selectedDate; 
                      });

                      const completedMembers = allProgress.filter(p =>
                        p.completed &&
                        eligibleMembers.some(m => m.user_id === p.user_id)
                      ).length;


                      let message = '';

                      return {
                        id: quest.id,
                        name: quest.name,
                        description: quest.description || '',
                        emoji: quest.emoji,
                        category: quest.category,
                        quest_type: quest.quest_type,
                        goal_value: quest.goal_value,
                        goal_unit: quest.goal_unit,
                        value_achieved: progress ? progress.value_achieved : 0,
                        completed: progress ? progress.completed : false,
                        streak: quest.streak,
                        is_side_quest: false,
                        completed_members: completedMembers,
                        total_members: eligibleMembers.length,
                        message: message
                      } as Quest;
                    })
                  );
                })
              );
            }).filter(obs => obs !== null) as Observable<Quest>[];

            if (questObservables.length > 0) {
              combineLatest(questObservables).subscribe(
                processedQuests => {
                  this.quests = processedQuests;
                },
                error => {
                  this.quests = [];
                }
              );
            } else {
              this.quests = [];
            }
          } else {
            this.quests = [];
          }
        },
        error => {
          this.quests = [];
        }
      )
    );
  }

  loadDailySideQuest(dateStr: string) {
    if (!this.groupId || !this.userId) return;


    if (!this.group?.enable_sidequests) {
      this.dailyQuest = null;
      return;
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const selectedDate = new Date(dateStr);
    selectedDate.setHours(0, 0, 0, 0);

    if (selectedDate.getTime() !== today.getTime()) {
      this.dailyQuest = null;
      return;
    }

    this.subscriptions.push(
      this.groupService.getGroupSideQuest(this.groupId).subscribe(
        sideQuest => {
          if (sideQuest) {

            this.groupService.getGroupSideQuestPoolItem(sideQuest.current_quest_id).subscribe(
              poolItem => {
                if (poolItem) {

                  if (!sideQuest.id || !this.userId) {
                    return;
                  }

                  this.groupService.getGroupSideQuestMemberStatus(sideQuest.id, this.userId).subscribe(
                    status => {

                      if (!sideQuest.id) {
                        return;
                      }
                      this.groupService.getGroupSideQuestMemberStatuses(sideQuest.id).subscribe(
                        allStatuses => {
                          const today = new Date();
                          today.setHours(0, 0, 0, 0); 

                          const eligibleMembers = this.members.filter(member => {
                            if (!member.joined_date) return false;
                            const joinDate = new Date(member.joined_date);
                            joinDate.setHours(0, 0, 0, 0);
                            return joinDate < today; 
                          });

                          const eligibleMemberIds = eligibleMembers.map(m => m.user_id);

                          const todayStr = new Date().toISOString().split('T')[0];

                          const completedMembers = allStatuses.filter(s =>
                            s.completed && 
                            eligibleMemberIds.includes(s.member_id) && 
                            (typeof s.last_updated === 'string' && s.last_updated === todayStr) 
                          ).length;

                          const totalMembers = eligibleMembers.length;

                          if (!poolItem.id) {
                            return;
                          }

                          this.dailyQuest = {
                            id: sideQuest.id || '',
                            streak: sideQuest.streak,
                            completed_members: completedMembers,
                            total_members: totalMembers,
                            current_quest: {
                              id: poolItem.id || '',
                              name: poolItem.name,
                              description: poolItem.description || '',
                              emoji: poolItem.emoji,
                              goal_value: poolItem.goal_value,
                              goal_unit: poolItem.goal_unit
                            }
                          };

                          this.memberStatus = status ? {
                            id: status.id || '',
                            completed: status.completed,
                            value_achieved: status.value_achieved
                          } : {
                            id: '',
                            completed: false,
                            value_achieved: 0
                          };
                        },
                        error => {
                        }
                      );
                    },
                    error => {
                      this.memberStatus = {
                        id: '',
                        completed: false,
                        value_achieved: 0
                      };
                    }
                  );
                }
              },
              error => {
              }
            );
          } else {
            this.dailyQuest = null;
          }
        },
        error => {
          this.dailyQuest = null;
        }
      )
    );
  }

  async updateQuestProgress(quest: Quest, event: any) {
    if (!this.groupId || !this.userId) return;

    const newValue = event.detail ? event.detail.value : parseInt(event.target.value);

    if (this.isBeforeJoinDate) {
      quest.message = 'You can participate in group quests starting tomorrow after joining the group.';
      quest.value_achieved = quest.value_achieved; 
      return;
    }


    const selectedDate = this.selectedDate ? new Date(this.selectedDate) : new Date();

    try {
      const result = await this.groupService.toggleGroupQuestCompletion(
        quest.id,
        this.userId,
        selectedDate,
        newValue
      );

      if (result.success) {

        quest.value_achieved = newValue;

        if (quest.quest_type === 'build') {
          quest.completed = newValue >= quest.goal_value;
        } else { 
          quest.completed = newValue < quest.goal_value;
        }

        this.loadQuestsForDate(this.selectedDate);
      } else {
        quest.message = result.message;

        quest.value_achieved = quest.value_achieved; 
      }
    } catch (error) {

      quest.value_achieved = quest.value_achieved; 

      quest.message = 'An error occurred while updating quest progress.';
    }
  }

  openAddQuestModal(event: Event) {
    event.preventDefault();

    this.newQuest = {
      emoji: '🎯',
      name: '',
      description: '',
      quest_type: 'build' as QuestType,
      category: '' as QuestCategory,
      priority: 'basic' as QuestPriority,
      goal_value: 1,
      goal_unit: 'count' as QuestGoalUnit,
      goal_period: 'day' as QuestPeriod
    };

    this.daysOfWeek.forEach(day => day.selected = false);
    this.daysOfMonth.forEach(day => day.selected = false);

    this.showAddQuestModal = true;
  }

  closeAddQuestModal() {
    this.showAddQuestModal = false;
  }

  goToSettings() {
    if (this.groupId) {
      window.open(`/groups/${this.groupId}/settings`, '_blank');
    }
  }

  navigateWeekWithUrl(direction: number) {

    const newWeekOffset = this.weekOffset + direction;
    this.weekOffset = newWeekOffset;

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: {
        week_offset: newWeekOffset,
        date: this.selectedDate 
      },
      queryParamsHandling: 'merge'
    });

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const startDate = new Date(today);
    startDate.setDate(today.getDate() + (this.weekOffset * 7));

    this.generateWeekDates(startDate);

    const selectedDateObj = this.weekDates.find(d => d.date === this.selectedDate);
    if (selectedDateObj && (selectedDateObj.isFuture || selectedDateObj.isBeforeJoin)) {
      const validDate = this.weekDates.find(date => !date.isFuture && !date.isBeforeJoin);
      if (validDate) {
        this.selectDate(validDate);
      }
    }
  }

  async selectDateWithUrl(date: WeekDate) {

    if (date.isFuture) {

      const toast = await this.toastController.create({
        message: 'Cannot select future dates',
        duration: 2000,
        position: 'bottom',
        color: 'warning'
      });
      await toast.present();

      return;
    }

    if (date.isBeforeJoin) {

      const toast = await this.toastController.create({
        message: 'You cannot access dates before you joined the group',
        duration: 2000,
        position: 'bottom',
        color: 'warning'
      });
      await toast.present();

      return;
    }

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { date: date.date },
      queryParamsHandling: 'merge' 
    });

    this.weekDates.forEach(d => d.isSelected = d.date === date.date);
    this.selectedDate = date.date;


    this.updateHeaderText();

    this.loadQuestsForDate(date.date);
    this.loadDailySideQuest(date.date);
  }

  onGoalPeriodChange() {
    if (this.newQuest.goal_period === 'week') {
      this.daysOfWeek.forEach(day => day.selected = false);
    } else if (this.newQuest.goal_period === 'month') {
      this.daysOfMonth.forEach(day => day.selected = false);
    }
  }

  createGroupQuest() {
    if (!this.groupId || !this.userId) return;

    if (!this.newQuest.name || !this.newQuest.category || !this.newQuest.priority) {
      alert('Please fill in all required fields');
      return;
    }

    let selectedDays: any[] = [];
    let task_days_of_week: string | undefined = undefined;
    let task_days_of_month: string | undefined = undefined;

    if (this.newQuest.goal_period === 'week') {
      selectedDays = this.daysOfWeek.filter(day => day.selected).map(day => day.value);
      task_days_of_week = selectedDays.join(',');
    } else if (this.newQuest.goal_period === 'month') {
      selectedDays = this.daysOfMonth.filter(day => day.selected).map(day => day.value);
      task_days_of_month = selectedDays.join(',');
    }

    const questData = {
      ...this.newQuest,
      group_id: this.groupId as string,
      task_days_of_week,
      task_days_of_month
    };


    this.groupService.createGroupQuest(questData)
      .then(questId => {
        this.loadQuestsForDate(this.selectedDate);
      })
      .catch(error => {
        alert('Failed to create quest: ' + error.message);
      });

    this.closeAddQuestModal();
    this.newQuest = {
      emoji: '🎯',
      name: '',
      description: '',
      quest_type: 'build' as QuestType,
      category: '' as QuestCategory,
      priority: 'basic' as QuestPriority,
      goal_value: 1,
      goal_unit: 'count' as QuestGoalUnit,
      goal_period: 'day' as QuestPeriod
    };
  }

  toggleSideQuest() {
    if (!this.groupId || !this.userId || !this.dailyQuest || this.isBeforeJoinDate) return;

    if (this.memberStatus.completed) {
      this.memberStatus.value_achieved = 0;
      this.memberStatus.completed = false;
    } else {
      this.memberStatus.value_achieved = this.dailyQuest.current_quest.goal_value;
      this.memberStatus.completed = true;
    }


    if (this.memberStatus.id) {
      this.groupService.updateGroupSideQuestMemberStatus(
        this.memberStatus.id,
        this.memberStatus.completed,
        this.memberStatus.value_achieved
      ).then(() => {

        this.loadDailySideQuest(this.selectedDate);
      }).catch(error => {
      });
    } else {
      this.groupService.createGroupSideQuestMemberStatus(
        this.dailyQuest.id,
        this.userId,
        this.memberStatus.completed,
        this.memberStatus.value_achieved
      ).then((statusId) => {
        this.memberStatus.id = statusId;

        this.loadDailySideQuest(this.selectedDate);
      }).catch(error => {
      });
    }
  }


  getCategoryIcon(category: string): string {
    switch (category.toLowerCase()) {
      case 'strength': return '💪';
      case 'money': return '💰';
      case 'health': return '❤️';
      case 'knowledge': return '🧠';
      default: return '🎯';
    }
  }

  getCategoryColor(category: string): string {
    switch (category.toLowerCase()) {
      case 'strength': return '#FF9500';
      case 'money': return '#30D158';
      case 'health': return '#FF2D55';
      case 'knowledge': return '#5E5CE6';
      default: return '#4D7BFF';
    }
  }

  getTotalXP(): number {
    if (!this.group) return 0;

    return this.group.strength_xp +
           this.group.money_xp +
           this.group.health_xp +
           this.group.knowledge_xp;
  }

  getCompletedQuests(): number {
    return this.members.reduce((total, member) => total + (member.completed_quests || 0), 0);
  }

  getProgressPercentage(xp: number): number {
    if (this.requiredXp <= 0) return 0;
    return Math.min(100, Math.round((xp / this.requiredXp) * 100));
  }

  updateSliderBackground(slider: HTMLInputElement | EventTarget | null) {
    if (!slider) {
      return;
    }

    let sliderElement: HTMLElement;
    let sliderValue = 0;
    let minValue = 0;
    let maxValue = 100;
    let sliderQuestId = '';

    if (slider instanceof HTMLInputElement) {
      sliderElement = slider;
      sliderQuestId = slider.getAttribute('data-quest-id') || '';
      sliderValue = parseInt(slider.value);
      minValue = parseInt(slider.min);
      maxValue = parseInt(slider.max);
    } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {
      sliderElement = slider;
      sliderQuestId = slider.getAttribute('data-quest-id') || '';

      const valueAttr = slider.getAttribute('value') || '0';
      const minAttr = slider.getAttribute('min') || '0';
      const maxAttr = slider.getAttribute('max') || '100';

      sliderValue = parseInt(valueAttr);
      minValue = parseInt(minAttr);
      maxValue = parseInt(maxAttr);
    } else {
      return;
    }

    if (!sliderQuestId) {
      return;
    }

    const percentage = maxValue > minValue ?
      ((sliderValue - minValue) / (maxValue - minValue)) * 100 : 0;

    if (sliderElement.tagName === 'ION-RANGE') {
      sliderElement.style.setProperty('--progress-value', `${percentage}%`);
    } else {
      sliderElement.style.background =
        `linear-gradient(to right, #4169E1 0%, #4169E1 ${percentage}%, #2C2C2E ${percentage}%, #2C2C2E 100%)`;
    }
  }
}
