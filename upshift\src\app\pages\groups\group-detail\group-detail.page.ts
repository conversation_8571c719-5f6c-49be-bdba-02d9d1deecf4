import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule, ViewWillEnter, ViewDidEnter, ToastController } from '@ionic/angular';
import { ActivatedRoute, Router, RouterModule, NavigationEnd } from '@angular/router';
import { Subscription, take, firstValueFrom, combineLatest, switchMap, map, of, Observable, filter } from 'rxjs';

import { SupabaseService } from '../../../services/supabase.service';
import { GroupService } from '../../../services/group.service';
import { UserService } from '../../../services/user.service';
import { XpService, EntityType } from '../../../services/xp.service';
import { Group, GroupMember } from '../../../models/supabase.models';
import { EmojiInputDirective } from '../../../directives/emoji-input.directive';
import { ComponentsModule } from '../../../components/components.module';
import { GroupWaitingRoomComponent } from '../../../components/group-waiting-room/group-waiting-room.component';
import { QuestType, QuestPeriod, QuestPriority, QuestCategory, QuestGoalUnit } from '../../../models/quest.model';

// Ensure Group interface has all required properties
interface GroupWithOptionalId extends Omit<Group, 'id'> {
  id?: string;
}

interface WeekDate {
  date: string;
  day: number;
  isToday: boolean;
  isSelected: boolean;
  isFuture: boolean;
  isBeforeJoin: boolean;
  completionPercentage?: number;
  totalQuests?: number;
  completedQuests?: number;
}

interface Quest {
  id: string;
  name: string;
  description: string;
  emoji: string;
  category: string;
  quest_type: string;
  goal_value: number;
  goal_unit: string;
  value_achieved: number;
  completed: boolean;
  streak: number;
  is_side_quest: boolean;
  completed_members: number;
  total_members: number;
  message?: string;
}

interface DailyQuest {
  id: string;
  streak: number;
  completed_members: number;
  total_members: number;
  current_quest: {
    id: string;
    name: string;
    description: string;
    emoji: string;
    goal_value: number;
    goal_unit: string;
  };
}

interface MemberStatus {
  id: string;
  completed: boolean;
  value_achieved: number;
}

interface MemberWithProfile extends GroupMember {
  profile_picture?: string;
  username?: string;
  total_xp?: number;
  completed_quests?: number;
  max_streak?: number;
}

interface GroupActivity {
  id: string;
  user_id: string;
  user_nickname: string;
  message: string;
  icon: string;
  color: string;
  created_at: Date;
}

interface SideQuest {
  id: string;
  title: string;
  description: string;
  category: string;
  xp_reward: number;
  completed: boolean;
}

@Component({
  selector: 'app-group-detail',
  templateUrl: './group-detail.page.html',
  styleUrls: ['./group-detail.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule, EmojiInputDirective, ComponentsModule, GroupWaitingRoomComponent]
})
export class GroupDetailPage implements OnInit, OnDestroy, ViewWillEnter, ViewDidEnter {
  // User data
  userId: string | null = null;

  // Group data
  groupId: string | null = null;
  group: GroupWithOptionalId | null = null;
  members: MemberWithProfile[] = [];
  isAdmin = false;
  requiredXp = 0;
  joinedDate: string = '';

  // Calendar data - starting with Monday
  dayNames: string[] = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  weekOffset: number = 0;
  weekDates: WeekDate[] = [];
  selectedDate: string = '';
  headerText: string = '';
  isBeforeJoinDate: boolean = false;
  joinedToday: boolean = false;

  // Quest data
  quests: Quest[] = [];
  dailyQuest: DailyQuest | null = null;
  memberStatus: MemberStatus = { id: '', completed: false, value_achieved: 0 };

  // Add Quest Modal
  showAddQuestModal: boolean = false;
  newQuest = {
    emoji: '🎯',
    name: '',
    description: '',
    quest_type: 'build' as QuestType,
    category: '' as QuestCategory,
    priority: 'basic' as QuestPriority,
    goal_value: 1,
    goal_unit: 'count' as QuestGoalUnit,
    goal_period: 'day' as QuestPeriod
  };

  // Days of week/month for quest scheduling
  daysOfWeek = [
    { value: 'Monday', label: 'M', selected: false },
    { value: 'Tuesday', label: 'T', selected: false },
    { value: 'Wednesday', label: 'W', selected: false },
    { value: 'Thursday', label: 'T', selected: false },
    { value: 'Friday', label: 'F', selected: false },
    { value: 'Saturday', label: 'S', selected: false },
    { value: 'Sunday', label: 'S', selected: false }
  ];

  daysOfMonth = Array.from({ length: 31 }, (_, i) => ({ value: i + 1, selected: false }));

  // Subscriptions
  private subscriptions: Subscription[] = [];

  // Services
  private supabaseService = inject(SupabaseService);
  private groupService = inject(GroupService);
  private userService = inject(UserService);
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private toastController = inject(ToastController);

  constructor() {}

  ngOnInit() {
    // Get the current user
    this.subscriptions.push(
      this.supabaseService.currentUser$.subscribe(user => {
        if (user) {
          this.userId = user.id;

          // Get the group ID from the route
          this.route.paramMap.pipe(take(1)).subscribe(params => {
            this.groupId = params.get('id');
            if (this.groupId) {
              this.loadGroup();
              this.loadMembers();

              // Initialize calendar after getting query params
              this.route.queryParams.pipe(take(1)).subscribe(queryParams => {
                console.log('GroupDetailPage: Query params:', queryParams);
                this.initializeCalendar();
              });
            }
          });
        } else {
          this.router.navigate(['/login']);
        }
      })
    );

    // Subscribe to router events to refresh data when navigating back to this page
    this.subscriptions.push(
      this.router.events.pipe(
        filter(event => event instanceof NavigationEnd),
        filter(() => !!this.groupId) // Only proceed if we have a groupId
      ).subscribe(() => {
        console.log('GroupDetailPage: Navigation event detected, refreshing data');
        this.refreshGroupData();
      })
    );
  }

  // Method to refresh all group data
  refreshGroupData() {
    if (!this.groupId) return;

    console.log('GroupDetailPage: Refreshing group data');
    this.loadGroup();
    this.loadMembers();

    // Reload quests and side quests for the selected date
    if (this.selectedDate) {
      this.loadQuestsForDate(this.selectedDate);
      this.loadDailySideQuest(this.selectedDate);
    }
  }

  // Ionic lifecycle hooks
  ionViewWillEnter() {
    console.log('GroupDetailPage: ionViewWillEnter called');
    this.refreshGroupData();
  }

  ionViewDidEnter() {
    console.log('GroupDetailPage: ionViewDidEnter called');
  }

  ngOnDestroy() {
    // Unsubscribe from all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  loadGroup() {
    if (!this.groupId) return;

    this.subscriptions.push(
      this.groupService.getGroup(this.groupId).subscribe(group => {
        if (group) {
          this.group = group;
          this.calculateRequiredXp();
        } else {
          this.router.navigate(['/groups']);
        }
      })
    );
  }

  loadMembers() {
    if (!this.groupId || !this.userId) return;

    this.subscriptions.push(
      this.groupService.getGroupMembers(this.groupId).subscribe(members => {
        // Check if current user is admin
        const currentUserMember = members.find(m => m.user_id === this.userId);
        this.isAdmin = currentUserMember?.is_admin || false;

        // Set joined date for current user
        if (currentUserMember && currentUserMember.joined_date) {
          this.joinedDate = new Date(currentUserMember.joined_date).toISOString().split('T')[0];

          // Check if user joined today
          const joinedDate = new Date(currentUserMember.joined_date);
          joinedDate.setHours(0, 0, 0, 0);

          const today = new Date();
          today.setHours(0, 0, 0, 0);

          this.joinedToday = joinedDate.getTime() === today.getTime();
          console.log('User joined today:', this.joinedToday);
        }

        // Load profile data for each member
        const membersWithProfiles: MemberWithProfile[] = [];

        // Create a copy of the members array with all required properties
        const membersCopy = members.map(member => ({
          ...member,
          id: member.id || '',
          group_id: member.group_id || '',
          user_id: member.user_id || '',
          nickname: member.nickname || '',
          is_admin: member.is_admin || false,
          joined_date: member.joined_date || new Date()
        }));

        for (const member of membersCopy) {
          this.userService.getUserProfile(member.user_id).pipe(take(1)).subscribe(profile => {
            // Get member stats
            this.userService.getUserStats(member.user_id).pipe(take(1)).subscribe(stats => {
              membersWithProfiles.push({
                ...member,
                profile_picture: profile?.profile_picture || undefined,
                username: profile?.username,
                total_xp: stats?.total_xp || 0,
                completed_quests: stats?.completed_quests || 0,
                max_streak: stats?.max_streak || 0
              });

              // Update the members array after each member is processed
              this.members = [...membersWithProfiles];
            });
          });
        }
      })
    );
  }

  calculateRequiredXp() {
    if (!this.group) return;

    // Calculate required XP for next level (level * 1000)
    this.requiredXp = this.group.level * 1000;
  }

  // Helper method to get the week offset for navigation
  getWeekOffset(direction: number): number {
    // Get current week offset from URL or use the stored value
    return this.weekOffset + direction;
  }

  // Calendar Methods
  initializeCalendar() {
    console.log('GroupDetailPage: Initializing calendar');

    // Get today's date
    const today = new Date();

    // Check for date and week_offset in URL query parameters
    this.route.queryParams.subscribe(params => {
      console.log('GroupDetailPage: URL query params:', params);

      // Get week offset from URL or default to 0
      if (params['week_offset']) {
        try {
          this.weekOffset = parseInt(params['week_offset']);
          console.log('GroupDetailPage: Week offset from URL:', this.weekOffset);
        } catch (error) {
          console.error('GroupDetailPage: Error parsing week offset:', error);
          this.weekOffset = 0;
        }
      } else {
        this.weekOffset = 0;
      }

      // Get date from URL or default to today
      let selectedDate = today;
      if (params['date']) {
        try {
          selectedDate = new Date(params['date']);
          console.log('GroupDetailPage: Selected date from URL:', selectedDate);

          // Validate the date
          if (isNaN(selectedDate.getTime())) {
            console.error('GroupDetailPage: Invalid date from URL:', params['date']);
            selectedDate = today;
          }
        } catch (error) {
          console.error('GroupDetailPage: Error parsing date from URL:', error);
          selectedDate = today;
        }
      }

      // Set the selected date
      this.selectedDate = this.formatDate(selectedDate);
      console.log('GroupDetailPage: Initial selected date set to:', this.selectedDate);

      // Calculate the start date for the week based on the week offset
      const startDate = new Date(today);
      startDate.setDate(today.getDate() + (this.weekOffset * 7));

      // Generate the week dates
      this.generateWeekDates(startDate);

      // Update the header text
      console.log('GroupDetailPage: Calling updateHeaderText from initializeCalendar');
      this.updateHeaderText();
      console.log('GroupDetailPage: After updateHeaderText, headerText is:', this.headerText);

      // Load quests and daily side quest for the selected date
      this.loadQuestsForDate(this.selectedDate);
      this.loadDailySideQuest(this.selectedDate);
    });
  }

  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  generateWeekDates(startDate: Date) {
    // Get today's date for comparison
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Calculate the start of the week (Monday)
    const currentDay = startDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const mondayOffset = currentDay === 0 ? -6 : 1; // If Sunday, go back 6 days to previous Monday
    const monday = new Date(startDate);
    monday.setDate(startDate.getDate() - currentDay + mondayOffset);

    console.log('GroupDetailPage: Generating week dates starting from Monday:', monday);

    const dates: WeekDate[] = [];

    // Generate dates for the week
    for (let i = 0; i < 7; i++) {
      const date = new Date(monday);
      date.setDate(monday.getDate() + i);

      const dateStr = this.formatDate(date);
      // Set hours, minutes, seconds, and milliseconds to 0 for proper comparison
      date.setHours(0, 0, 0, 0);
      const isToday = date.getTime() === today.getTime();

      // A date is in the future if it's after today
      const isFuture = date.getTime() > today.getTime();

      // Check if date is before join date (if member info is available)
      let isBeforeJoin = false;
      if (this.members.length > 0 && this.userId) {
        const currentMember = this.members.find(m => m.user_id === this.userId);
        if (currentMember && currentMember.joined_date) {
          const joinDate = new Date(currentMember.joined_date);
          joinDate.setHours(0, 0, 0, 0);
          isBeforeJoin = date < joinDate;
        }
      }

      dates.push({
        date: dateStr,
        day: date.getDate(),
        isToday,
        isSelected: dateStr === this.selectedDate,
        isFuture,
        isBeforeJoin,
        completionPercentage: 0,
        totalQuests: 0,
        completedQuests: 0
      });
    }

    this.weekDates = dates;
    console.log('GroupDetailPage: Generated week dates:', this.weekDates);

    // Update the completion percentages for all week dates
    this.updateWeekDateProgress();
  }

  navigateWeek(direction: number) {
    console.log('GroupDetailPage: Navigating week, direction:', direction);

    // Get current week offset from URL or default to 0
    this.route.queryParams.pipe(take(1)).subscribe(params => {
      let currentWeekOffset = 0;
      if (params['week_offset']) {
        try {
          currentWeekOffset = parseInt(params['week_offset']);
        } catch (error) {
          console.error('GroupDetailPage: Error parsing week offset:', error);
        }
      }

      // Calculate new week offset
      const newWeekOffset = currentWeekOffset + direction;
      console.log('GroupDetailPage: New week offset:', newWeekOffset);

      // Get the first date of the current week
      const firstDate = new Date(this.weekDates[0].date);

      // Move forward or backward by 7 days
      firstDate.setDate(firstDate.getDate() + (direction * 7));
      console.log('GroupDetailPage: New first date of week:', firstDate);

      // Update the URL with the new week offset
      this.router.navigate([], {
        relativeTo: this.route,
        queryParams: {
          week_offset: newWeekOffset,
          date: this.selectedDate // Keep the selected date
        }
      });

      // Generate new week dates
      this.generateWeekDates(firstDate);

      // Keep the same selected date if possible, otherwise select the first date of the new week
      const selectedDateExists = this.weekDates.some(date => date.date === this.selectedDate);
      if (!selectedDateExists) {
        // Find the first date that's not in the future and not before join date
        const validDate = this.weekDates.find(date => !date.isFuture && !date.isBeforeJoin);
        if (validDate) {
          this.selectDate(validDate);
        }
      }
    });
  }

  async selectDate(date: WeekDate) {
    console.log('GroupDetailPage: Selecting date:', date);

    // Don't allow selecting future dates or dates before join date
    if (date.isFuture) {
      console.log('GroupDetailPage: Cannot select future date');

      // Show toast notification
      const toast = await this.toastController.create({
        message: 'Cannot select future dates',
        duration: 2000,
        position: 'bottom',
        color: 'warning'
      });
      await toast.present();

      return;
    }

    if (date.isBeforeJoin) {
      console.log('GroupDetailPage: Cannot select date before join date');
      this.isBeforeJoinDate = true;

      // Show toast notification
      const toast = await this.toastController.create({
        message: 'You cannot access dates before you joined the group',
        duration: 2000,
        position: 'bottom',
        color: 'warning'
      });
      await toast.present();

      return;
    } else {
      this.isBeforeJoinDate = false;
    }

    // Update the URL with the selected date
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { date: date.date },
      queryParamsHandling: 'merge' // Keep existing query params
    });

    // Update selected state in all week dates
    this.weekDates.forEach(d => d.isSelected = d.date === date.date);
    this.selectedDate = date.date;

    console.log('GroupDetailPage: Selected date updated to:', this.selectedDate);

    // Update header text
    this.updateHeaderText();

    // Load quests and daily side quest for the selected date
    this.loadQuestsForDate(date.date);
    this.loadDailySideQuest(date.date);
  }

  updateHeaderText() {
    if (!this.selectedDate) {
      this.headerText = 'Today';
      console.log('GroupDetailPage: No selected date, setting header to Today');
      return;
    }

    const date = new Date(this.selectedDate);
    const today = new Date();

    // Set hours, minutes, seconds, and milliseconds to 0 for proper comparison
    date.setHours(0, 0, 0, 0);
    today.setHours(0, 0, 0, 0);

    console.log('GroupDetailPage: Comparing dates:', {
      selectedDate: this.selectedDate,
      selectedDateObj: date.toISOString(),
      todayObj: today.toISOString(),
      selectedTime: date.getTime(),
      todayTime: today.getTime(),
      isToday: date.getTime() === today.getTime()
    });

    // Check if selected date is today
    if (date.getTime() === today.getTime()) {
      this.headerText = 'Today';
      console.log('GroupDetailPage: Selected date is today');
    } else {
      // Check if selected date is yesterday
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      console.log('GroupDetailPage: Checking if yesterday:', {
        yesterdayObj: yesterday.toISOString(),
        yesterdayTime: yesterday.getTime(),
        isYesterday: date.getTime() === yesterday.getTime()
      });

      if (date.getTime() === yesterday.getTime()) {
        this.headerText = 'Yesterday';
        console.log('GroupDetailPage: Selected date is yesterday');
      } else {
        // Check if selected date is tomorrow
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        console.log('GroupDetailPage: Checking if tomorrow:', {
          tomorrowObj: tomorrow.toISOString(),
          tomorrowTime: tomorrow.getTime(),
          isTomorrow: date.getTime() === tomorrow.getTime()
        });

        if (date.getTime() === tomorrow.getTime()) {
          this.headerText = 'Tomorrow';
          console.log('GroupDetailPage: Selected date is tomorrow');
        } else {
          // Format as "Jan 1" or similar
          this.headerText = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
          console.log('GroupDetailPage: Selected date is neither today, yesterday, nor tomorrow');
        }
      }
    }

    console.log('GroupDetailPage: Updated header text to:', this.headerText);
  }

  // Check if there are any future dates in the week
  hasFutureDates(): boolean {
    return this.weekDates.some(date => date.isFuture);
  }

  // Check if selected date is today
  isToday(): boolean {
    if (!this.selectedDate) return false;

    const date = new Date(this.selectedDate);
    const today = new Date();

    // Set hours, minutes, seconds, and milliseconds to 0 for proper comparison
    date.setHours(0, 0, 0, 0);
    today.setHours(0, 0, 0, 0);

    return date.getTime() === today.getTime();
  }

  // Check if selected date is yesterday
  isYesterday(): boolean {
    if (!this.selectedDate) return false;

    const date = new Date(this.selectedDate);
    const today = new Date();

    // Set hours, minutes, seconds, and milliseconds to 0 for proper comparison
    date.setHours(0, 0, 0, 0);
    today.setHours(0, 0, 0, 0);

    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    return date.getTime() === yesterday.getTime();
  }

  // Check if selected date is tomorrow
  isTomorrow(): boolean {
    if (!this.selectedDate) return false;

    const date = new Date(this.selectedDate);
    const today = new Date();

    // Set hours, minutes, seconds, and milliseconds to 0 for proper comparison
    date.setHours(0, 0, 0, 0);
    today.setHours(0, 0, 0, 0);

    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    return date.getTime() === tomorrow.getTime();
  }

  // Update the completion percentages for all week dates
  updateWeekDateProgress() {
    // For each date in the week
    this.weekDates.forEach(date => {
      if (date.isFuture || date.isBeforeJoin) {
        date.completionPercentage = 0;
        date.totalQuests = 0;
        date.completedQuests = 0;
        return;
      }

      // Set all completion percentages to 0 since we don't have real data yet
      date.totalQuests = 0;
      date.completedQuests = 0;
      date.completionPercentage = 0;

      // In a real implementation, we would check the completion status for each quest
      // and calculate the completion percentage based on that
    });
  }

  // Quest Methods
  loadQuestsForDate(dateStr: string) {
    if (!this.groupId || !this.userId) return;

    console.log('GroupDetailPage: Loading quests for date:', dateStr);

    // Get the group quests for this group
    this.subscriptions.push(
      this.groupService.getGroupQuests(this.groupId).subscribe(
        quests => {
          if (quests && quests.length > 0) {
            console.log('GroupDetailPage: Found group quests:', quests);

            // Filter quests to only show those created on or before the selected date
            const selectedDate = new Date(dateStr);
            const filteredQuests = quests.filter(quest => {
              // Convert the created timestamp to a Date object
              const createdDate = new Date(quest.created);
              // Set hours, minutes, seconds, and milliseconds to 0 for both dates to compare just the day
              createdDate.setHours(0, 0, 0, 0);
              selectedDate.setHours(0, 0, 0, 0);
              // Only include quests created on or before the selected date
              return createdDate <= selectedDate;
            });

            console.log('GroupDetailPage: Filtered quests for date:', filteredQuests);

            // Process each quest to get progress and member statuses
            const questObservables = filteredQuests.map(quest => {
              if (!quest.id || !this.userId) {
                console.error('GroupDetailPage: Invalid quest ID or user ID');
                return of(null);
              }

              // Get the quest progress for this user and date
              return this.groupService.getGroupQuestProgress(quest.id, this.userId, new Date(dateStr)).pipe(
                switchMap(progress => {
                  // Get all progress entries for this quest and date to count completions
                  if (!quest.id) {
                    console.error('GroupDetailPage: Invalid quest ID for progress');
                    return of({} as Quest);
                  }
                  return this.groupService.getGroupQuestProgressForDate(quest.id, new Date(dateStr)).pipe(
                    map(allProgress => {
                      // Filter eligible members (joined before the selected date)
                      const selectedDate = new Date(dateStr);
                      selectedDate.setHours(0, 0, 0, 0); // Set to beginning of day for comparison

                      // Get eligible members who joined before the selected date
                      const eligibleMembers = this.members.filter(member => {
                        if (!member.joined_date) return false;
                        const joinDate = new Date(member.joined_date);
                        joinDate.setHours(0, 0, 0, 0);
                        return joinDate < selectedDate; // Before selected date, not on selected date
                      });

                      // Count completed members among eligible members
                      const completedMembers = allProgress.filter(p =>
                        p.completed &&
                        eligibleMembers.some(m => m.user_id === p.user_id)
                      ).length;

                      // No need to check eligibility anymore since we use the waiting room

                      // No message needed anymore since we use the waiting room
                      let message = '';

                      return {
                        id: quest.id,
                        name: quest.name,
                        description: quest.description || '',
                        emoji: quest.emoji,
                        category: quest.category,
                        quest_type: quest.quest_type,
                        goal_value: quest.goal_value,
                        goal_unit: quest.goal_unit,
                        value_achieved: progress ? progress.value_achieved : 0,
                        completed: progress ? progress.completed : false,
                        streak: quest.streak,
                        is_side_quest: false,
                        completed_members: completedMembers,
                        total_members: eligibleMembers.length,
                        message: message
                      } as Quest;
                    })
                  );
                })
              );
            }).filter(obs => obs !== null) as Observable<Quest>[];

            // Combine all quest observables
            if (questObservables.length > 0) {
              combineLatest(questObservables).subscribe(
                processedQuests => {
                  console.log('GroupDetailPage: Processed quests:', processedQuests);
                  this.quests = processedQuests;
                },
                error => {
                  console.error('GroupDetailPage: Error processing quests:', error);
                  this.quests = [];
                }
              );
            } else {
              console.log('GroupDetailPage: No quests found for this group');
              this.quests = [];
            }
          } else {
            console.log('GroupDetailPage: No quests found for this group');
            this.quests = [];
          }
        },
        error => {
          console.error('GroupDetailPage: Error getting group quests:', error);
          this.quests = [];
        }
      )
    );
  }

  loadDailySideQuest(dateStr: string) {
    if (!this.groupId || !this.userId) return;

    console.log('GroupDetailPage: Loading daily side quest for date:', dateStr);

    // Check if side quests are enabled for this group
    if (!this.group?.enable_sidequests) {
      console.log('GroupDetailPage: Side quests are disabled for this group, not loading side quest');
      this.dailyQuest = null;
      return;
    }

    // Check if the selected date is today
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const selectedDate = new Date(dateStr);
    selectedDate.setHours(0, 0, 0, 0);

    // Only show side quests for today
    if (selectedDate.getTime() !== today.getTime()) {
      console.log('GroupDetailPage: Selected date is not today, not showing side quest');
      this.dailyQuest = null;
      return;
    }

    // Get the group side quest for this group
    this.subscriptions.push(
      this.groupService.getGroupSideQuest(this.groupId).subscribe(
        sideQuest => {
          if (sideQuest) {
            console.log('GroupDetailPage: Found group side quest:', sideQuest);

            // Get the quest details from the pool
            this.groupService.getGroupSideQuestPoolItem(sideQuest.current_quest_id).subscribe(
              poolItem => {
                if (poolItem) {
                  console.log('GroupDetailPage: Found quest pool item:', poolItem);

                  // Get the member status
                  if (!sideQuest.id || !this.userId) {
                    console.error('GroupDetailPage: Invalid sidequest ID or user ID');
                    return;
                  }

                  this.groupService.getGroupSideQuestMemberStatus(sideQuest.id, this.userId).subscribe(
                    status => {
                      console.log('GroupDetailPage: Found member status:', status);

                      // Get all member statuses to count completions
                      if (!sideQuest.id) {
                        console.error('GroupDetailPage: Invalid sidequest ID for member statuses');
                        return;
                      }
                      this.groupService.getGroupSideQuestMemberStatuses(sideQuest.id).subscribe(
                        allStatuses => {
                          // Filter eligible members (joined before today)
                          const today = new Date();
                          today.setHours(0, 0, 0, 0); // Set to beginning of day for comparison

                          // Get eligible members who joined before today
                          const eligibleMembers = this.members.filter(member => {
                            if (!member.joined_date) return false;
                            const joinDate = new Date(member.joined_date);
                            joinDate.setHours(0, 0, 0, 0);
                            return joinDate < today; // Before today, not today
                          });

                          // Get eligible member IDs
                          const eligibleMemberIds = eligibleMembers.map(m => m.user_id);

                          // Get today's date in YYYY-MM-DD format
                          const todayStr = new Date().toISOString().split('T')[0];

                          // Count completed members among eligible members only
                          // Only count members who have completed the quest TODAY
                          const completedMembers = allStatuses.filter(s =>
                            s.completed && // Status is completed
                            eligibleMemberIds.includes(s.member_id) && // Member is eligible (joined at least a day ago)
                            (typeof s.last_updated === 'string' && s.last_updated === todayStr) // Status was updated today
                          ).length;

                          // Total members is the count of eligible members
                          const totalMembers = eligibleMembers.length;

                          if (!poolItem.id) {
                            console.error('GroupDetailPage: Invalid pool item ID');
                            return;
                          }

                          this.dailyQuest = {
                            id: sideQuest.id || '',
                            streak: sideQuest.streak,
                            completed_members: completedMembers,
                            total_members: totalMembers,
                            current_quest: {
                              id: poolItem.id || '',
                              name: poolItem.name,
                              description: poolItem.description || '',
                              emoji: poolItem.emoji,
                              goal_value: poolItem.goal_value,
                              goal_unit: poolItem.goal_unit
                            }
                          };

                          this.memberStatus = status ? {
                            id: status.id || '',
                            completed: status.completed,
                            value_achieved: status.value_achieved
                          } : {
                            id: '',
                            completed: false,
                            value_achieved: 0
                          };
                        },
                        error => {
                          console.error('GroupDetailPage: Error getting all member statuses:', error);
                        }
                      );
                    },
                    error => {
                      console.error('GroupDetailPage: Error getting member status:', error);
                      this.memberStatus = {
                        id: '',
                        completed: false,
                        value_achieved: 0
                      };
                    }
                  );
                }
              },
              error => {
                console.error('GroupDetailPage: Error getting quest pool item:', error);
              }
            );
          } else {
            console.log('GroupDetailPage: No side quest found for this group');
            this.dailyQuest = null;
          }
        },
        error => {
          console.error('GroupDetailPage: Error getting group side quest:', error);
          this.dailyQuest = null;
        }
      )
    );
  }

  async updateQuestProgress(quest: Quest, event: any) {
    if (!this.groupId || !this.userId) return;

    // Get the new value from the event
    const newValue = event.detail ? event.detail.value : parseInt(event.target.value);

    // Check if the date is before the user's join date
    if (this.isBeforeJoinDate) {
      console.log('Cannot update quest progress for date before join date');
      quest.message = 'You can participate in group quests starting tomorrow after joining the group.';
      // Reset the slider to previous value
      quest.value_achieved = quest.value_achieved; // Reset to original value
      return;
    }

    // Update the quest progress in the database
    console.log('Updating quest progress:', quest.id, newValue);

    // Get the current date
    const selectedDate = this.selectedDate ? new Date(this.selectedDate) : new Date();

    try {
      // Call the service to update the progress
      const result = await this.groupService.toggleGroupQuestCompletion(
        quest.id,
        this.userId,
        selectedDate,
        newValue
      );

      if (result.success) {
        console.log('Quest progress updated successfully');

        // Update the UI to reflect the changes
        quest.value_achieved = newValue;

        // Check if quest is completed based on quest type
        if (quest.quest_type === 'build') {
          quest.completed = newValue >= quest.goal_value;
        } else { // 'quit' type
          quest.completed = newValue < quest.goal_value;
        }

        // Reload the quests to get updated streak and completion status
        this.loadQuestsForDate(this.selectedDate);
      } else {
        console.error('Error updating quest progress:', result.message);
        quest.message = result.message;

        // Reset the slider to previous value
        quest.value_achieved = quest.value_achieved; // Reset to original value
      }
    } catch (error) {
      console.error('Error updating quest progress:', error);

      // Reset the slider to previous value
      quest.value_achieved = quest.value_achieved; // Reset to original value

      // Show error message
      quest.message = 'An error occurred while updating quest progress.';
    }
  }

  // Modal Methods
  openAddQuestModal(event: Event) {
    event.preventDefault();

    // Reset form before opening
    this.newQuest = {
      emoji: '🎯',
      name: '',
      description: '',
      quest_type: 'build' as QuestType,
      category: '' as QuestCategory,
      priority: 'basic' as QuestPriority,
      goal_value: 1,
      goal_unit: 'count' as QuestGoalUnit,
      goal_period: 'day' as QuestPeriod
    };

    // Reset day selections
    this.daysOfWeek.forEach(day => day.selected = false);
    this.daysOfMonth.forEach(day => day.selected = false);

    console.log('Opening add quest modal with reset form:', this.newQuest);
    this.showAddQuestModal = true;
  }

  closeAddQuestModal() {
    console.log('Closing add quest modal');
    this.showAddQuestModal = false;
  }

  // Navigation Methods
  goToSettings() {
    console.log('GroupDetailPage: Navigating to settings for group:', this.groupId);
    if (this.groupId) {
      // Use window.open to open in a new tab
      window.open(`/groups/${this.groupId}/settings`, '_blank');
    }
  }

  navigateWeekWithUrl(direction: number) {
    console.log('GroupDetailPage: Navigating week with URL, direction:', direction);

    // Calculate new week offset
    const newWeekOffset = this.weekOffset + direction;
    this.weekOffset = newWeekOffset;

    // Update the URL with the new week offset
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: {
        week_offset: newWeekOffset,
        date: this.selectedDate // Keep the selected date
      },
      queryParamsHandling: 'merge'
    });

    // Get today's date
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Calculate the start date for the week based on the week offset
    const startDate = new Date(today);
    startDate.setDate(today.getDate() + (this.weekOffset * 7));

    // Generate new week dates
    this.generateWeekDates(startDate);

    // Check if the currently selected date is still valid in the new week
    // (not in the future and not before join date)
    const selectedDateObj = this.weekDates.find(d => d.date === this.selectedDate);
    if (selectedDateObj && (selectedDateObj.isFuture || selectedDateObj.isBeforeJoin)) {
      // Find the first valid date in the new week
      const validDate = this.weekDates.find(date => !date.isFuture && !date.isBeforeJoin);
      if (validDate) {
        // Select the valid date
        this.selectDate(validDate);
      }
    }
  }

  async selectDateWithUrl(date: WeekDate) {
    console.log('GroupDetailPage: Selecting date with URL:', date);

    // Don't allow selecting future dates or dates before join date
    if (date.isFuture) {
      console.log('GroupDetailPage: Cannot select future date');

      // Show toast notification
      const toast = await this.toastController.create({
        message: 'Cannot select future dates',
        duration: 2000,
        position: 'bottom',
        color: 'warning'
      });
      await toast.present();

      return;
    }

    if (date.isBeforeJoin) {
      console.log('GroupDetailPage: Cannot select date before join date');

      // Show toast notification
      const toast = await this.toastController.create({
        message: 'You cannot access dates before you joined the group',
        duration: 2000,
        position: 'bottom',
        color: 'warning'
      });
      await toast.present();

      return;
    }

    // Update the URL with the selected date
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { date: date.date },
      queryParamsHandling: 'merge' // Keep existing query params
    });

    // Update selected state in all week dates
    this.weekDates.forEach(d => d.isSelected = d.date === date.date);
    this.selectedDate = date.date;

    console.log('GroupDetailPage: Selected date updated to:', this.selectedDate);

    // Update header text
    console.log('GroupDetailPage: Calling updateHeaderText from selectDateWithUrl');
    this.updateHeaderText();
    console.log('GroupDetailPage: After updateHeaderText, headerText is:', this.headerText);

    // Load quests and daily side quest for the selected date
    this.loadQuestsForDate(date.date);
    this.loadDailySideQuest(date.date);
  }

  onGoalPeriodChange() {
    // Reset selections when period changes
    if (this.newQuest.goal_period === 'week') {
      this.daysOfWeek.forEach(day => day.selected = false);
    } else if (this.newQuest.goal_period === 'month') {
      this.daysOfMonth.forEach(day => day.selected = false);
    }
  }

  createGroupQuest() {
    if (!this.groupId || !this.userId) return;

    // Validate form
    if (!this.newQuest.name || !this.newQuest.category || !this.newQuest.priority) {
      alert('Please fill in all required fields');
      return;
    }

    // Get selected days
    let selectedDays: any[] = [];
    let task_days_of_week: string | undefined = undefined;
    let task_days_of_month: string | undefined = undefined;

    if (this.newQuest.goal_period === 'week') {
      selectedDays = this.daysOfWeek.filter(day => day.selected).map(day => day.value);
      task_days_of_week = selectedDays.join(',');
    } else if (this.newQuest.goal_period === 'month') {
      selectedDays = this.daysOfMonth.filter(day => day.selected).map(day => day.value);
      task_days_of_month = selectedDays.join(',');
    }

    // Create quest object with proper types
    const questData = {
      ...this.newQuest,
      group_id: this.groupId as string,
      // Removed created_by as it doesn't exist in the database
      task_days_of_week,
      task_days_of_month
    };

    console.log('Creating new quest:', questData);

    // Call the service to create the quest
    this.groupService.createGroupQuest(questData)
      .then(questId => {
        console.log('Quest created successfully with ID:', questId);
        // Reload quests
        this.loadQuestsForDate(this.selectedDate);
      })
      .catch(error => {
        console.error('Error creating quest:', error);
        alert('Failed to create quest: ' + error.message);
      });

    // Close modal and reset form
    this.closeAddQuestModal();
    this.newQuest = {
      emoji: '🎯',
      name: '',
      description: '',
      quest_type: 'build' as QuestType,
      category: '' as QuestCategory,
      priority: 'basic' as QuestPriority,
      goal_value: 1,
      goal_unit: 'count' as QuestGoalUnit,
      goal_period: 'day' as QuestPeriod
    };
  }

  // Side Quest Methods
  toggleSideQuest() {
    if (!this.groupId || !this.userId || !this.dailyQuest || this.isBeforeJoinDate) return;

    // Toggle between 0 and goal value
    if (this.memberStatus.completed) {
      this.memberStatus.value_achieved = 0;
      this.memberStatus.completed = false;
    } else {
      this.memberStatus.value_achieved = this.dailyQuest.current_quest.goal_value;
      this.memberStatus.completed = true;
    }

    console.log('Toggling side quest:', this.dailyQuest.id, this.memberStatus.completed);

    // Update the side quest progress in the database
    if (this.memberStatus.id) {
      // Update existing status
      this.groupService.updateGroupSideQuestMemberStatus(
        this.memberStatus.id,
        this.memberStatus.completed,
        this.memberStatus.value_achieved
      ).then(() => {
        console.log('Side quest status updated successfully');

        // Reload the side quest to get updated streak and completion status
        this.loadDailySideQuest(this.selectedDate);
      }).catch(error => {
        console.error('Error updating side quest status:', error);
      });
    } else {
      // Create new status
      this.groupService.createGroupSideQuestMemberStatus(
        this.dailyQuest.id,
        this.userId,
        this.memberStatus.completed,
        this.memberStatus.value_achieved
      ).then((statusId) => {
        console.log('Side quest status created successfully with ID:', statusId);
        this.memberStatus.id = statusId;

        // Reload the side quest to get updated streak and completion status
        this.loadDailySideQuest(this.selectedDate);
      }).catch(error => {
        console.error('Error creating side quest status:', error);
      });
    }
  }

  // Emoji validation is now handled by the EmojiInputDirective

  // Helper Methods
  getCategoryIcon(category: string): string {
    switch (category.toLowerCase()) {
      case 'strength': return '💪';
      case 'money': return '💰';
      case 'health': return '❤️';
      case 'knowledge': return '🧠';
      default: return '🎯';
    }
  }

  getCategoryColor(category: string): string {
    switch (category.toLowerCase()) {
      case 'strength': return '#FF9500';
      case 'money': return '#30D158';
      case 'health': return '#FF2D55';
      case 'knowledge': return '#5E5CE6';
      default: return '#4D7BFF';
    }
  }

  getTotalXP(): number {
    if (!this.group) return 0;

    return this.group.strength_xp +
           this.group.money_xp +
           this.group.health_xp +
           this.group.knowledge_xp;
  }

  getCompletedQuests(): number {
    // Sum of all completed quests by members
    return this.members.reduce((total, member) => total + (member.completed_quests || 0), 0);
  }

  getProgressPercentage(xp: number): number {
    if (this.requiredXp <= 0) return 0;
    return Math.min(100, Math.round((xp / this.requiredXp) * 100));
  }

  // Update slider background based on value
  updateSliderBackground(slider: HTMLInputElement | EventTarget | null) {
    if (!slider) {
      return;
    }

    // Handle different types of slider elements
    let sliderElement: HTMLElement;
    let sliderValue = 0;
    let minValue = 0;
    let maxValue = 100;
    let sliderQuestId = '';

    if (slider instanceof HTMLInputElement) {
      // Standard HTML input range
      sliderElement = slider;
      sliderQuestId = slider.getAttribute('data-quest-id') || '';
      sliderValue = parseInt(slider.value);
      minValue = parseInt(slider.min);
      maxValue = parseInt(slider.max);
    } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {
      // Ionic range element
      sliderElement = slider;
      sliderQuestId = slider.getAttribute('data-quest-id') || '';

      // Get the value from the element's properties or attributes
      const valueAttr = slider.getAttribute('value') || '0';
      const minAttr = slider.getAttribute('min') || '0';
      const maxAttr = slider.getAttribute('max') || '100';

      sliderValue = parseInt(valueAttr);
      minValue = parseInt(minAttr);
      maxValue = parseInt(maxAttr);
    } else {
      return;
    }

    if (!sliderQuestId) {
      return;
    }

    // Calculate the percentage value
    const percentage = maxValue > minValue ?
      ((sliderValue - minValue) / (maxValue - minValue)) * 100 : 0;

    // For Ionic range, we need to set the CSS variable
    if (sliderElement.tagName === 'ION-RANGE') {
      sliderElement.style.setProperty('--progress-value', `${percentage}%`);
    } else {
      // Set the background directly with hardcoded colors for standard HTML input
      sliderElement.style.background =
        `linear-gradient(to right, #4169E1 0%, #4169E1 ${percentage}%, #2C2C2E ${percentage}%, #2C2C2E 100%)`;
    }
  }
}
