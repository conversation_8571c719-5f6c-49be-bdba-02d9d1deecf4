﻿<ion-content class="ion-padding">
  <div class="calculating-container">
    <div class="progress-circle">
      <svg viewBox="0 0 36 36" class="circular-chart">
        <defs>
          <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color: var(--text)" />
            <stop offset="33%" style="stop-color: var(--accent)" />
            <stop offset="66%" style="stop-color: var(--text)" />
            <stop offset="100%" style="stop-color: var(--accent)" />
          </linearGradient>
        </defs>
        <path class="circle-bg"
          d="M18 2.0845
            a 15.9155 15.9155 0 0 1 0 31.831
            a 15.9155 15.9155 0 0 1 0 -31.831"
        />
        <path class="circle"
          [attr.stroke-dasharray]="progress + ', 100'"
          d="M18 2.0845
            a 15.9155 15.9155 0 0 1 0 31.831
            a 15.9155 15.9155 0 0 1 0 -31.831"
        />
        <text x="18" y="20.35" class="percentage">{{progress}}%</text>
      </svg>
    </div>
    <h1>Calculating</h1>
    <p class="subtitle">{{subtitle}}</p>
  </div>
</ion-content> 