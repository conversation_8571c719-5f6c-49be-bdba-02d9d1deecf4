﻿:host {
  --background-color: #0C0C0F;
  --text-color: #FFFFFF;
  --secondary-text: #8E8E93;
  --accent-color: #4169E1;
  --card-bg: #1C1C1E;
  --card-border: #2C2C2E;
   Badge colors */
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo img {
  height: 24px;
}

.logo span {
  font-size: 20px;
  font-weight: 600;
}

h1 {
  font-size: 20px;
  font-weight: 600;
}

.badges-container {
  margin-bottom: 100px;  Space for navigation */
.title-badge .badge-icon {
  color: var(--title-badge-color);
}

.streak-badge .badge-icon {
  color: var(--streak-badge-color);
}

.sidequest-badge .badge-icon {
  color: var(--sidequest-badge-color);
}

.friend-badge .badge-icon {
  color: var(--friend-badge-color);
}

 Loading state */
@media (max-width: 480px) {
  .badges-grid {
    grid-template-columns: 1fr;
  }
}# sourceMappingURL=user-badges.page.css.map */