﻿ Celebration Animation Styles */
.achievement-container {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
    z-index: 1999;
    display: flex;
    justify-content: center;
    align-items: center;
    perspective: 1200px;
    transform-style: preserve-3d;
}

 Glowing Orb */
.particle-system {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 1997;
}

 Particle */
.hexagon-grid {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 1;  Changed from 0 to make it immediately visible */
.streak-counter {
    position: absolute;
    top: 20%;
    font-size: 120px;
    font-weight: 800;
    color: transparent;
    -webkit-text-stroke: 2px rgba(77, 123, 255, 0.3);
    opacity: 0;
    transform: translateY(-50px);
    animation: streak-appear 1.5s ease-out 0.8s forwards;
    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    letter-spacing: -5px;
}

 Level Up Text */

@keyframes grid-rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes hex-pulse {
    0%, 100% {
        border-color: rgba(77, 123, 255, 0.2);
    }
    50% {
        border-color: rgba(77, 123, 255, 0.6);
    }
}

@keyframes hexagon-appear {
    0% {
        opacity: 0;
        transform: scale(0) rotate(30deg);
    }
    100% {
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }
}



@keyframes orb-float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-15px);
    }
}

@keyframes orb-halo {
    0% {
        transform: scale(1) rotate(0deg);
    }
    50% {
        transform: scale(1.2) rotate(180deg);
    }
    100% {
        transform: scale(1) rotate(360deg);
    }
}

@keyframes orb-highlight {
    0% {
        opacity: 0.7;
        transform: rotate(0deg);
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.7;
        transform: rotate(360deg);
    }
}

@keyframes hexagon-appear {
    0% {
        opacity: 0;
        transform: scale(0) rotate(30deg);
    }
    70% {
        opacity: 0.8;
        transform: scale(1.1) rotate(5deg);
    }
    100% {
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }
}

@keyframes streak-appear {
    0% {
        opacity: 0;
        transform: translateY(-50px) scale(0.8);
    }
    60% {
        opacity: 0.8;
        transform: translateY(10px) scale(1.1);
    }
    100% {
        opacity: 0.3;
        transform: translateY(0) scale(1);
    }
}

@keyframes level-up-appear {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes check-appear {
    0% {
        transform: scale(0.5);
        opacity: 0;
    }
    70% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes draw-check-1 {
    0% {
        width: 0;
        opacity: 0;
    }
    100% {
        width: 35%;
        opacity: 1;
    }
}

@keyframes draw-check-2 {
    0% {
        width: 0;
        opacity: 0;
    }
    100% {
        width: 70%;
        opacity: 1;
    }
}

@keyframes popIn {
    0% {
        transform: scale(0.8) translateY(20px);
        opacity: 0;
    }
    40% {
        transform: scale(1.02) translateY(-5px);
    }
    70% {
        transform: scale(0.98) translateY(2px);
    }
    100% {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes trophy-bounce {
    0%, 100% {
        transform: translateY(0) rotate(5deg);
    }
    50% {
        transform: translateY(-10px) rotate(-5deg);
    }
}
