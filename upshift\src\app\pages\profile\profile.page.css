﻿ Exact CSS from Django template */
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo img {
  height: 24px;
}

.logo span {
  font-size: 20px;
  font-weight: 600;
}

h1 {
  font-size: 20px;
  font-weight: 600;
}

.profile-container {
  padding: 20px;
}

.profile-header {
  display: flex;
  align-items: center;
}

.profile-picture {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: var(--card-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  margin-right: 20px;
  overflow: hidden;
}

.profile-picture img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.profile-info {
  flex: 1;
}

.profile-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 5px;
}

.profile-username {
  font-size: 16px;
  color: var(--secondary-text);
  margin-bottom: 5px;
}

.profile-level {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.level-badge {
  background-color: var(--accent-color);
  color: white;
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 14px;
  font-weight: 600;
  margin-right: 10px;
}

.profile-title {
  font-size: 16px;
  color: var(--accent-color);
}

.button-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  margin-bottom: 30px;  Added margin at the bottom */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
}

input:checked + .slider {
  background-color: var(--accent-color);
}

input:focus + .slider {
  box-shadow: 0 0 1px var(--accent-color);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

.slider.round {
  border-radius: 24px;
}

.slider.round:before {
  border-radius: 50%;
}

 Navigation Styles */
# sourceMappingURL=profile.page.css.map */