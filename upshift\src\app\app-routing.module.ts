import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from './guards/auth.guard';
import { TransitionComponent } from './transition/transition.component';
import {MainRedirectComponent} from "./main-redirect/main-redirect.component";
export const routes: Routes = [
  {
    path: '',
    loadChildren: () => import('./tabs/tabs.module').then(m => m.TabsModule),
    canActivate: [AuthGuard]
  },
  {
      path: 'rating',
      loadComponent: () => import('./rating/rating.page').then(m => m.RatingPage)
  },
  {
    path: 'tabs',
    redirectTo: '/tabs/home',
    pathMatch: 'full'
  },
  {
      path: 'signup',
      loadComponent: () => import('./auth/signup/signup.component').then(m => m.SignupComponent)
  },
  {
      path: 'register',
      loadComponent: () => import('./auth/register/register.component').then(m => m.RegisterComponent)
  },
  {
      path: 'login',
      loadComponent: () => import('./auth/login/login.component').then(m => m.LoginComponent)
  },
  {
      path: 'transition',
      component: TransitionComponent
  },
  {
      path: 'pricing',
      loadComponent: () => import('./pricing/pricing.page').then(m => m.PricingPage)
  },
  {
      path: 'onboarding',
      loadComponent: () => import('./onboarding/onboarding.page').then(m => m.OnboardingPage)
  },
  {
      path: 'calculating',
      loadComponent: () => import('./calculating/calculating.page').then(m => m.CalculatingPage),
      canActivate: [AuthGuard]
  },
  {
    path: '**',
    loadChildren: () => import('./tabs/tabs.module').then( m => m.TabsModule),
    canActivate: [AuthGuard]
  },
  {
      path: 'redirect',
      component: MainRedirectComponent
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
