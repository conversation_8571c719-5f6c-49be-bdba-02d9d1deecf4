﻿import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { RateApp } from 'capacitor-rate-app';
import { AlertController } from '@ionic/angular';

@Component({
  selector: 'app-rating',
  templateUrl: './rating.page.html',
  styleUrls: ['./rating.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, RouterModule, FormsModule]
})
export class RatingPage implements OnInit {
  fakeTestimonials = [
    {
      name: 'Jergus',
      profilePic: '/assets/images/jergus.jpg',
      text: 'This app has changed my life! I can\'t imagine going back to how things were before. Highly recommend it to everyone!'
    },
    {
      name: '<PERSON>',
      profilePic: '/assets/images/ratingPicture.jpg',
      text: 'Absolutely love this app! It\'s so easy to use and has made my daily routine so much smoother. Thank you for creating this!'
    },
  ];

  fakeUsers = [
    {
      picture: '/assets/images/ratingPicture.jpg'
    },
    {
      picture: '/assets/images/ratingPicture.jpg'
    },
    {
      picture: '/assets/images/jergus.jpg'
    }
  ]

  constructor(
    private route: ActivatedRoute,
    private alertController: AlertController
  ) {}

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      if (params['plan']) {
      }
    });


    setTimeout(() => {
      this.showRatingAlert();
    }, 1500);
  }

  async showRatingAlert() {
    const alert = await this.alertController.create({
      header: 'Enjoying Upshift?',
      message: 'Would you like to rate our app in the App Store?',
      cssClass: 'app-rating-alert',
      buttons: [
        {
          text: 'No, Thanks',
          role: 'cancel'
        },
        {
          text: 'Rate Now',
          handler: () => {
            this.openAppStore();
          }
        }
      ]
    });

    await alert.present();
  }

  async openAppStore() {
    try {
      await RateApp.openStore();
    } catch (error) {
    }
  }
}