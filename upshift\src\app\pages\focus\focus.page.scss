﻿:host {
    --bg: #0c0c0f;
    --card: #121217;
    --pill: #1c1c1e;
    --text: #fff;
    --text-muted: #8e8e93;
    --accent: #4d7bff;
    --pill-padding: 10px 14px;
    --radius: 14px;

     Calendar variables */
.week-calendar {
    margin-bottom: 32px;
}

.days, .dates {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    text-align: center;
    gap: 8px;
}

.days {
    margin-bottom: 8px;
}

.day-name {
    color: var(--secondary-text);
    font-size: 14px;
}

.date {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin: 0 auto;
    font-size: 14px;
    background-color: var(--inactive-date);
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
}

.date-progress {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    pointer-events: none;
    z-index: 0;
}

.date-progress circle {
    fill: transparent;
    stroke-width: 2.5;
    stroke-linecap: round;
    transform-origin: center;
    transform: rotate(-90deg);
    transition: stroke-dasharray 0.3s ease;
}

 Different colors for different completion levels */
.date.selected .date-progress .progress-circle {
    stroke: #78a8f3;
    stroke-opacity: 0.7;
}

.date.active .date-progress .progress-circle {
    stroke: #78a8f3;
    stroke-opacity: 0.7;
}

.date-content {
    position: relative;
    z-index: 1;
}

.date.active {
    background-color: var(--active-date);
    color: white;
}

.date.selected {
    background-color: var(--accent-color);
    color: white;
}

.date.selected::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background-color: var(--accent-color);
    border-radius: 50%;
}

.date:hover:not(.disabled) {
    background-color: rgba(255, 255, 255, 0.1);
}

.date.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.calendar-nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
}

.nav-arrow {
    padding: 8px 12px;
    border: none;
    background: transparent;
    color: var(--text-primary);
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
}

.nav-arrow:hover {
    background: var(--bg-hover);
}

 === SCREEN CARD === */
.screen-time-box {
    display: flex;
    justify-content: space-evenly;
    align-items: baseline;
    margin-bottom: 24px;
}

.big-time {
    font-size: 36px;
    font-weight: 700;
    text-align: center;
    display: block;
    color: white;
}

.time-label {
    font-size: 13px;
    color: var(--text-muted);
    text-align: center;
    margin-top: 2px;
    display: block;
}

 === HORIZONTAL APP ROW === */
.blockers {
    display: flex;
    flex-direction: column;
    gap: 14px;
}

.blocker {
    background-color: var(--pill);
    border-radius: 14px;
    padding: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 15px;
    font-weight: 500;
}

.blocker span:first-child {
    display: flex;
    align-items: center;
    gap: 10px;
}

.blocker svg {
    width: 18px;
    height: 18px;
    color: var(--accent);
}

.cancel-link {
    font-size: 13px;
    color: var(--accent);
    text-decoration: none;
    font-weight: 500;
}

.cancel-link:hover {
    text-decoration: underline;
}
.scheduled-blocks {
    margin-top: 40px;
}

.scheduled-blocks h2 {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-muted);
    margin-bottom: 16px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

 --- CARD --- */
.block-card-icon {
    text-align: center;
    color: var(--text-muted);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.block-card-icon svg {
    width: 28px;
    height: 28px;
    margin-bottom: 4px;
}

.usage-text {
    font-size: 12px;
    text-decoration: underline;
}

 --- Button row --- */
