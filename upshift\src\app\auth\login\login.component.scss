﻿ion-content {
  --background: transparent;
    .divider {
      display: flex;
      align-items: center;
      margin: 20px 0;
      color: var(--text-secondary);
      font-size: 14px;

      &:before, &:after {
        content: "";
        flex: 1;
        height: 1px;
        background: var(--border);
        opacity: 0.7;
      }

      span {
        padding: 0 16px;
      }
    }

    .social-buttons {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin-bottom: 20px;
    }

    .background-container {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      z-index: -1;

    .gradient-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: var(--bg);
    }

    .celestial-body {
      position: absolute;
      width: 250px;
      height: 250px;
      border-radius: 50%;
      filter: blur(25px);
      background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 70%);
      animation: celestial-cycle 30s ease-in-out infinite;
      opacity: 0.5;
      box-shadow: 0 0 60px rgba(255, 255, 255, 0.1);
    }

    @keyframes celestial-cycle {
      0% {
        transform: translate(-150px, 120%);
        background: radial-gradient(circle, rgba(255,190,69,0.4) 0%, rgba(255,190,69,0) 70%);
        box-shadow: 0 0 80px rgba(255, 165, 0, 0.2);
      }
      25% {
        transform: translate(25%, 50%);
        background: radial-gradient(circle, rgba(255,190,69,0.5) 0%, rgba(255,190,69,0) 70%);
        box-shadow: 0 0 100px rgba(255, 165, 0, 0.3);
      }
      50% {
        transform: translate(120%, -150px);
        background: radial-gradient(circle, rgba(210,235,255,0.4) 0%, rgba(210,235,255,0) 70%);
        box-shadow: 0 0 80px rgba(173, 216, 230, 0.2);
      }
      75% {
        transform: translate(75%, 50%);
        background: radial-gradient(circle, rgba(210,235,255,0.5) 0%, rgba(210,235,255,0) 70%);
        box-shadow: 0 0 100px rgba(173, 216, 230, 0.3);
      }
      100% {
        transform: translate(-150px, 120%);
        background: radial-gradient(circle, rgba(255,190,69,0.4) 0%, rgba(255,190,69,0) 70%);
        box-shadow: 0 0 80px rgba(255, 165, 0, 0.2);
      }
    }

    @keyframes gradient {
      0% {
        background-position: 0% 50%;
      }
      50% {
        background-position: 100% 50%;
      }
      100% {
        background-position: 0% 50%;
      }
    }
  }
}
    
