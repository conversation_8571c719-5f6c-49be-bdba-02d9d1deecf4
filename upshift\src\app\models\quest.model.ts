﻿export type QuestType = 'build' | 'quit';
export type QuestPeriod = 'day' | 'week' | 'month';
export type QuestPriority = 'basic' | 'high';
export type QuestCategory = 'money' | 'health' | 'strength' | 'knowledge';
export type QuestGoalUnit = 'count' | 'steps' | 'm' | 'km' | 'sec' | 'min' | 'hr' | 'Cal' | 'g' | 'mg' | 'drink' | 'time' | 'pages' | 'books' | '%' | '€' | '$' | '£';

export interface Quest {
  id?: string; 
  name: string;
  description?: string;
  active: boolean;
  quest_type: QuestType;

  user_id: string;
  streak: number;

  goal_value: number;
  goal_unit: QuestGoalUnit;
  goal_period: QuestPeriod;
  priority: QuestPriority;
  category: QuestCategory;

  task_days_of_week?: string; 
  task_days_of_month?: string; 
  custom_reminder_times?: string; 

  created_at: Date;
  emoji: string;
}

export interface QuestProgress {
  id?: string; 
  user_id: string;
  quest_id: string;
  date: string | Date; 
  completed: boolean;
  value_achieved: number;
}
