﻿import { Component, OnInit, OnDestroy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { GoalService } from '../../../services/goal.service';
import { Goal, GoalUnit } from '../../../models/goal.model';
import { Subscription, combineLatest, map, of, switchMap, take } from 'rxjs';
import { SupabaseService } from '../../../services/supabase.service';
import { EmojiInputDirective } from '../../../directives/emoji-input.directive';

interface GoalDisplay extends Goal {
  progressPercent: number;
  totalMicrogoals: number;
  completedMicrogoals: number;
}

@Component({
  selector: 'app-goal-list',
  templateUrl: './goal-list.page.html',
  styleUrls: ['./goal-list.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule, EmojiInputDirective]
})
export class GoalListPage implements OnInit, OnDestroy {
  userId: string | null = null;

  goals: GoalDisplay[] = [];

  showAddGoalModal = false;
  newGoal = this.getEmptyGoal();

  private userSubscription: Subscription | null = null;

  private supabaseService = inject(SupabaseService);
  private goalService = inject(GoalService);

  constructor() {
  }

  ngOnInit() {
    this.userSubscription = this.supabaseService.currentUser$.pipe(
      take(1)
    ).subscribe(user => {
      if (user) {
        this.userId = user.id;
        this.loadGoals();
      }
    });
  }

  ionViewWillEnter() {
    if (this.userId) {
      this.loadGoals();
    }
  }

  ngOnDestroy() {
    if (this.userSubscription) {
      this.userSubscription.unsubscribe();
    }
  }

  loadGoals() {
    if (!this.userId) {
      return;
    }


    this.goals = [];

    this.goalService.getGoals(this.userId).pipe(
      switchMap(goals => {

        if (goals.length === 0) {
          return of([]);
        }

        const goalObservables = goals.map(goal =>
          this.goalService.getMicroGoals(goal.id!).pipe(
            map(microgoals => {

              const totalMicrogoals = microgoals.length;
              const completedMicrogoals = microgoals.filter(m => m.completed).length;
              const progressPercent = goal.goal_value > 0
                ? Math.min(100, Math.round((goal.current_value / goal.goal_value) * 100))
                : 0;

              return {
                ...goal,
                progressPercent,
                totalMicrogoals,
                completedMicrogoals
              } as GoalDisplay;
            })
          )
        );

        return combineLatest(goalObservables);
      })
    ).subscribe({
      next: goalsWithMicrogoals => {
        this.goals = goalsWithMicrogoals;
      },
      error: error => {
        this.goals = [];
      }
    });
  }

  openAddGoalModal(event: Event) {
    event.preventDefault();
    this.showAddGoalModal = true;
    this.newGoal = this.getEmptyGoal();
  }

  closeAddGoalModal() {
    this.showAddGoalModal = false;
  }

  createGoal() {
    if (!this.userId) {
      return;
    }

    if (!this.newGoal.name) {
      return;
    }

    const goalToCreate: Goal = {
      name: this.newGoal.name,
      description: this.newGoal.description || '',
      emoji: this.newGoal.emoji || '🎯',
      goal_value: this.newGoal.goal_value || 0,
      goal_unit: this.newGoal.goal_unit || 'count',
      user_id: this.userId,
      start_date: new Date(),
      current_value: 0
    };


    this.goalService.createGoal(goalToCreate)
      .then((goalId) => {
        this.closeAddGoalModal();
        this.loadGoals();
      })
      .catch(error => {
      });
  }

  private getEmptyGoal(): Partial<Goal> {
    return {
      name: '',
      description: '',
      emoji: '🎯',
      goal_value: 100,
      goal_unit: 'count' as GoalUnit
    };
  }
}
