﻿import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule, Router } from '@angular/router';
import { GroupService } from '../../../services/group.service';
import { UserService } from '../../../services/user.service';
import { SupabaseService } from '../../../services/supabase.service';
import { Group, GroupJoinRequest } from '../../../models/group.model';
import { User } from '../../../models/user.model';
import { Observable, Subscription, catchError, map, of, switchMap, take } from 'rxjs';

interface GroupJoinRequestWithDetails {
  id?: string;
  group_id: string;
  user_id: string;
  username_invited?: string;
  invited_by: string | User | any; 
  created?: Date;
  requested_at?: Date;
  status?: string;
  nickname?: string;
  group: Group | null;
  created_at: Date; 
  inviter_username?: string; 
}

@Component({
  selector: 'app-group-requests',
  templateUrl: './group-requests.page.html',
  styleUrls: ['./group-requests.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule]
})
export class GroupRequestsPage implements OnInit {
  userId: string | null = null;
  username: string | null = null;

  requestsRecent: GroupJoinRequestWithDetails[] = [];
  requestsMonth: GroupJoinRequestWithDetails[] = [];
  requestsOlder: GroupJoinRequestWithDetails[] = [];

  successMessage = '';
  errorMessage = '';

  private supabaseService = inject(SupabaseService);
  private groupService = inject(GroupService);
  private userService = inject(UserService);
  private router = inject(Router);

  constructor() {}



  ngOnInit() {

    this.supabaseService.currentUser$.pipe(
      take(1),
      switchMap(authUser => {

        if (authUser) {
          this.userId = authUser.id;

          return this.userService.getUser(this.userId).pipe(
            take(1),
            map(user => {
              this.username = user?.username || null;
              return user;
            })
          );
        }
        return of(null);
      })
    ).subscribe(user => {

      if (user && this.userId) {
        this.loadJoinRequests();

        setTimeout(() => {
          this.fetchAllInviterUsernames();
        }, 1000); 
      } else {
      }
    });
  }

  async fetchAllInviterUsernames() {

    const inviterUsernames = new Set<string>();

    this.requestsRecent.forEach(request => {
      if (typeof request.invited_by === 'string') {
        inviterUsernames.add(request.invited_by);
      }
    });

    this.requestsMonth.forEach(request => {
      if (typeof request.invited_by === 'string') {
        inviterUsernames.add(request.invited_by);
      }
    });

    this.requestsOlder.forEach(request => {
      if (typeof request.invited_by === 'string') {
        inviterUsernames.add(request.invited_by);
      }
    });


    if (inviterUsernames.size > 0) {
      inviterUsernames.forEach(username => {
        this.updateInviterUsernameDirectly(username, username);
      });
    }
  }

  private async fetchUsernameDirectly(invitedBy: string): Promise<void> {

    this.updateInviterUsernameDirectly(invitedBy, invitedBy);
  }

  loadJoinRequests() {
    if (!this.userId) {
      return;
    }


    this.requestsRecent = [];
    this.requestsMonth = [];
    this.requestsOlder = [];

    const processedRequestIds = new Set<string>();

    this.groupService.getJoinRequestsForUserId(this.userId).pipe(
      take(1)
    ).subscribe({
      next: (requests) => {

        if (requests.length > 0) {
          const inviterIds = new Set<string>();

          requests.forEach(request => {
            if (request.invited_by && typeof request.invited_by === 'string') {
              inviterIds.add(request.invited_by);
            }
          });


          if (inviterIds.size > 0) {
            this.fetchAllUsernames(Array.from(inviterIds));
          }

          requests.forEach(request => {
            if (request.id && !processedRequestIds.has(request.id)) {
              processedRequestIds.add(request.id);
              this.processRequest(request);
            }
          });
        } else if (this.username) {
          this.supabaseService.getClient()
            .from('group_join_requests')
            .select('*')
            .then(directResponse => {

              if (directResponse.error) {
              } else {

                const inviterIds = new Set<string>();

                directResponse.data.forEach(request => {
                  if (request.invited_by && typeof request.invited_by === 'string') {
                    inviterIds.add(request.invited_by);
                  }
                });


                if (inviterIds.size > 0) {
                  this.fetchAllUsernames(Array.from(inviterIds));
                }

                directResponse.data.forEach(request => {
                  if (request.id && !processedRequestIds.has(request.id)) {
                    processedRequestIds.add(request.id);
                    this.processRequest(request as GroupJoinRequest);
                  }
                });
              }
            });
        }
      },
      error: (error) => {
      }
    });
  }

  async fetchAllUsernames(userIds: string[]): Promise<void> {
    if (!userIds.length) return;


    try {
      const usernameMap = new Map<string, string>();

      for (const userId of userIds) {
        usernameMap.set(userId, userId);
      }

      this.updateAllInviters(usernameMap);
    } catch (error) {
    }
  }

  updateAllInviters(usernameMap: Map<string, string>): void {

    this.requestsRecent.forEach(request => {
      if (typeof request.invited_by === 'string' && usernameMap.has(request.invited_by)) {
        const username = usernameMap.get(request.invited_by);
        if (username) {
          request.inviter_username = username;
        }
      }
    });

    this.requestsMonth.forEach(request => {
      if (typeof request.invited_by === 'string' && usernameMap.has(request.invited_by)) {
        const username = usernameMap.get(request.invited_by);
        if (username) {
          request.inviter_username = username;
        }
      }
    });

    this.requestsOlder.forEach(request => {
      if (typeof request.invited_by === 'string' && usernameMap.has(request.invited_by)) {
        const username = usernameMap.get(request.invited_by);
        if (username) {
          request.inviter_username = username;
        }
      }
    });
  }

  processRequest(request: GroupJoinRequest) {

    if (request.inviter_username) {
    }

    this.groupService.getGroup(request.group_id).pipe(
      take(1),
      switchMap(group => {

        if (request.inviter_username) {
          const createdDate = request.created || request.requested_at || new Date();

          const requestWithDetails: GroupJoinRequestWithDetails = {
            id: request.id,
            group_id: request.group_id,
            user_id: request.invited_by, 
            username_invited: request.username_invited,
            invited_by: request.invited_by,
            created: createdDate,
            requested_at: request.requested_at,
            status: request.status,
            nickname: request.nickname,
            group: group,
            created_at: createdDate,
            inviter_username: request.inviter_username
          };

          :', requestWithDetails);
          return of<GroupJoinRequestWithDetails>(requestWithDetails);
        }


        return of({
          id: '',
          username: request.invited_by
        }).pipe(
          take(1),
          map(user => {

            const createdDate = request.created || request.requested_at || new Date();

            let userId = '';
            if (user && user.id) {
              userId = user.id;
            } else {
              userId = request.invited_by; 
            }

            const requestWithDetails: GroupJoinRequestWithDetails = {
              id: request.id,
              group_id: request.group_id,
              user_id: userId,
              username_invited: request.username_invited,
              invited_by: user || request.invited_by, 
              created: createdDate,
              requested_at: request.requested_at,
              status: request.status,
              nickname: request.nickname,
              group: group,
              created_at: createdDate,
              inviter_username: user?.username || request.inviter_username 
            };

            console.log('GroupRequestsPage: Inviter info:',
              typeof requestWithDetails.invited_by === 'string'
                ? requestWithDetails.invited_by
                : requestWithDetails.invited_by?.username);

            return requestWithDetails;
          }),
          catchError((error: any) => {

            const createdDate = request.created || request.requested_at || new Date();

            const requestWithDetails: GroupJoinRequestWithDetails = {
              id: request.id,
              group_id: request.group_id,
              user_id: request.invited_by,
              username_invited: request.username_invited,
              invited_by: request.invited_by,
              created: createdDate,
              requested_at: request.requested_at,
              status: request.status,
              nickname: request.nickname,
              group: group,
              created_at: createdDate,
              inviter_username: request.invited_by 
            };

            return of<GroupJoinRequestWithDetails>(requestWithDetails);
          })
        );
      })
    ).subscribe((requestWithDetails: GroupJoinRequestWithDetails) => {
      const now = new Date();
      const requestDate = new Date(requestWithDetails.created_at);
      const daysDiff = Math.floor((now.getTime() - requestDate.getTime()) / (1000 * 60 * 60 * 24));


      if (daysDiff < 7) {
        if (!this.requestsRecent.some(r => r.id === requestWithDetails.id)) {
          this.requestsRecent.push(requestWithDetails);
        }
      } else if (daysDiff < 30) {
        if (!this.requestsMonth.some(r => r.id === requestWithDetails.id)) {
          this.requestsMonth.push(requestWithDetails);
        }
      } else {
        if (!this.requestsOlder.some(r => r.id === requestWithDetails.id)) {
          this.requestsOlder.push(requestWithDetails);
        }
      }
    });
  }

  acceptRequest(requestId: string) {
    if (!this.userId) return;

    this.groupService.acceptGroupJoinRequest(requestId, this.userId).then(() => {
      this.successMessage = 'You have joined the group!';

      this.removeRequestFromLists(requestId);

      setTimeout(() => {
        this.router.navigate(['/groups']);
      }, 1500);

      setTimeout(() => {
        this.successMessage = '';
      }, 3000);
    }).catch(error => {
      this.errorMessage = 'Failed to join the group. Please try again.';

      setTimeout(() => {
        this.errorMessage = '';
      }, 3000);
    });
  }

  rejectRequest(requestId: string) {
    this.groupService.rejectGroupJoinRequest(requestId).then(() => {
      this.successMessage = 'Request rejected.';

      this.removeRequestFromLists(requestId);

      if (this.requestsRecent.length === 0 && this.requestsMonth.length === 0 && this.requestsOlder.length === 0) {
        setTimeout(() => {
          this.router.navigate(['/groups']);
        }, 1500);
      }

      setTimeout(() => {
        this.successMessage = '';
      }, 3000);
    }).catch(error => {
      this.errorMessage = 'Failed to reject the request. Please try again.';

      setTimeout(() => {
        this.errorMessage = '';
      }, 3000);
    });
  }

  removeRequestFromLists(requestId: string) {
    this.requestsRecent = this.requestsRecent.filter(r => r.id !== requestId);
    this.requestsMonth = this.requestsMonth.filter(r => r.id !== requestId);
    this.requestsOlder = this.requestsOlder.filter(r => r.id !== requestId);
  }

  getInviterUsername(inviter: string | User | any): string {

    if (typeof inviter === 'object' && inviter && 'inviter_username' in inviter && inviter.inviter_username) {
      return String(inviter.inviter_username);
    }

    if (typeof inviter === 'object' && inviter && 'username' in inviter && inviter.username) {
      return String(inviter.username);
    }

    if (typeof inviter === 'string') {
      return inviter;
    }

    return 'Unknown';
  }

  private updateInviterInLists(inviterId: string, user: User): void {

    this.requestsRecent.forEach(request => {
      if (typeof request.invited_by === 'string' && request.invited_by === inviterId) {
        request.invited_by = user;
        request.inviter_username = user.username;
      }
    });

    this.requestsMonth.forEach(request => {
      if (typeof request.invited_by === 'string' && request.invited_by === inviterId) {
        request.invited_by = user;
        request.inviter_username = user.username;
      }
    });

    this.requestsOlder.forEach(request => {
      if (typeof request.invited_by === 'string' && request.invited_by === inviterId) {
        request.invited_by = user;
        request.inviter_username = user.username;
      }
    });
  }

  fetchUsernameFromProfiles(invitedBy: string): void {

    this.updateInviterUsernameDirectly(invitedBy, invitedBy);

    const user: User = {
      id: '', 
      username: invitedBy
    } as User;

    this.updateInviterInLists(invitedBy, user);
  }

  updateInviterUsernameDirectly(userId: string, username: string): void {

    this.requestsRecent.forEach(request => {
      if (typeof request.invited_by === 'string' && request.invited_by === userId) {
        request.inviter_username = username;
      }
    });

    this.requestsMonth.forEach(request => {
      if (typeof request.invited_by === 'string' && request.invited_by === userId) {
        request.inviter_username = username;
      }
    });

    this.requestsOlder.forEach(request => {
      if (typeof request.invited_by === 'string' && request.invited_by === userId) {
        request.inviter_username = username;
      }
    });
  }
}
