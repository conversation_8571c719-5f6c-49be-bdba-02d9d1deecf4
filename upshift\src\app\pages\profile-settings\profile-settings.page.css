﻿:host {
  --background-color: #0C0C0F;
  --text-color: #FFFFFF;
  --secondary-text: #8E8E93;
  --accent-color: #4169E1;
  --card-bg: #1C1C1E;
  --card-border: #2C2C2E;
  --error-color: #FF3B30;
  --success-color: #34C759;
}

.container {
  max-width: 480px;
  margin: 0 auto;
  padding: 20px;
  color: var(--text-color);
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo img {
  height: 24px;
}

.logo span {
  font-size: 20px;
  font-weight: 600;
}

h1 {
  font-size: 20px;
  font-weight: 600;
}

.settings-container {
  margin-bottom: 80px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-color);
}

.form-group input[type=text],
.form-group input[type=email],
.form-group textarea {
  width: 100%;
  padding: 12px;
  background-color: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 8px;
  color: var(--text-color);
  font-size: 16px;
}

.form-group textarea {
  height: 100px;
  resize: vertical;
}

.character-count {
  text-align: right;
  font-size: 12px;
  color: var(--secondary-text);
  margin-top: 4px;
}

.error-message {
  color: var(--error-color);
  font-size: 14px;
  margin-top: 4px;
}

.form-section {
  margin-top: 30px;
  margin-bottom: 30px;
  border-top: 1px solid var(--card-border);
  padding-top: 20px;
}

.form-section h2 {
  font-size: 18px;
  margin-bottom: 16px;
  font-weight: 600;
}

 Toggle Switch */
.profile-picture-container {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
}

.current-picture {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  background-color: var(--card-bg);
  display: flex;
  align-items: center;
  justify-content: center;
}

.current-picture img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.default-avatar {
  font-size: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.picture-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.upload-button, .remove-button {
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border: none;
}

.upload-button {
  background-color: var(--accent-color);
  color: white;
}

.remove-button {
  background-color: transparent;
  border: 1px solid var(--error-color);
  color: var(--error-color);
}

 Submit Button */
.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
}

.back-button, .logout-button {
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
}

.back-button {
  background-color: transparent;
  border: 1px solid var(--card-border);
  color: var(--text-color);
}

.logout-button {
  background-color: transparent;
  border: 1px solid var(--error-color);
  color: var(--error-color);
}

 Badges Link */
