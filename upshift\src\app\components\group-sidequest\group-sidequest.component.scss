﻿.daily-side-quest {
  margin: 20px 0;

  h2 {
    font-size: 18px;
    margin-bottom: 15px;
    color: #fff;
    font-weight: 600;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 0;

  ion-spinner {
    margin-bottom: 16px;
  }

  p {
    color: var(--ion-color-medium);
    margin: 0;
  }
}

.no-sidequest {
  text-align: center;
  padding: 20px 0;

  p {
    color: var(--ion-color-medium);
    margin-bottom: 16px;
  }
}

.quest-list {
  margin-bottom: 15px;
}

.quest-item {
  background-color: var(--quest-bg, #1C1C1E);
  border: 1px solid var(--quest-border, #2C2C2E);
  border-radius: 8px;
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 10px;
  position: relative;

  &:active {
    transform: scale(0.98);
  }

  &.completed {
    border-color: var(--accent-color, #4169E1);
  }

  &.completed .quest-info h3 {
    color: var(--accent-color, #4169E1);
  }

  &.disabled-quest {
    opacity: 0.7;
    cursor: not-allowed;
    pointer-events: none;
    position: relative;
  }

  &.disabled-quest::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.2);
    pointer-events: none;
    border-radius: 8px;
  }

  .quest-icon {
    font-size: 20px;
    min-width: 24px;
    text-align: center;
    margin-right: 12px;
  }

  .quest-info {
    flex-grow: 1;

    h3 {
      font-size: 14px;
      font-weight: 600;
      margin: 0;
      margin-bottom: 2px;
    }

    p {
      color: var(--secondary-text, #8E8E93);
      font-size: 12px;
      margin: 0;
      margin-bottom: 4px;
    }

    .progress-container {
      width: 100%;
      margin: 4px 0;
    }

    .progress-text {
      font-size: 12px;
      color: var(--secondary-text, #8E8E93);
      margin-top: 2px;

      &.values {
        display: flex;
        justify-content: space-between;
        width: 100%;
      }
    }

    .members-count {
      position: relative;
      margin-right: 10px;
      font-size: 12px;
    }
    .members-count span:not(.quest-message) {
      background-color: rgba(65, 105, 225, 0.2);
      padding: 2px 6px;
      border-radius: 10px;
      font-size: 11px;
      white-space: nowrap;
      display: inline-block;
    }
    .quest-message {
      color: #ff9800;
      font-style: italic;
      font-size: 12px;
      display: block !important;
      margin-top: 5px;
      font-weight: bold;
      text-align: left;
      width: 100%;
      white-space: normal;
      line-height: 1.2;
      background-color: transparent !important;
      padding: 0 !important;
      border-radius: 0 !important;
    }
  }

  .quest-streak {
    font-size: 12px;
    white-space: nowrap;
    color: #ff9500;
  }
}
