{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _FriendService;\nimport { inject } from '@angular/core';\nimport { map, of, switchMap, from, catchError } from 'rxjs';\nimport { SupabaseService } from './supabase.service';\nimport * as i0 from \"@angular/core\";\nexport class FriendService {\n  constructor() {\n    this.supabaseService = inject(SupabaseService);\n  }\n  getFriends(userId) {\n    return from(this.supabaseService.supabase.from('friends').select('*').or(`user_id.eq.${userId},friend_id.eq.${userId}`)).pipe(map(response => {\n      if (response.error) {\n        return [];\n      }\n      const normalizedFriends = response.data.map(friend => {\n        if (friend.friend_id === userId) {\n          return {\n            ...friend,\n            friend_id: friend.user_id,\n            user_id: userId\n          };\n        }\n        return friend;\n      });\n      const uniqueFriendIds = new Set();\n      const uniqueFriends = normalizedFriends.filter(friend => {\n        if (uniqueFriendIds.has(friend.friend_id)) {\n          return false;\n        }\n        uniqueFriendIds.add(friend.friend_id);\n        return true;\n      });\n      return uniqueFriends;\n    }));\n  }\n  getFriendsWithProfiles(userId) {\n    return this.getFriends(userId).pipe(switchMap(friends => {\n      if (friends.length === 0) {\n        return of([]);\n      }\n      const friendIds = friends.map(friend => friend.friend_id);\n      const profilePromises = friendIds.map(friendId => {\n        return new Promise(resolve => {\n          try {\n            this.supabaseService.supabase.from('profiles').select('*').eq('id', friendId).maybeSingle().then(response => {\n              if (response.error) {\n                resolve(null);\n              } else {\n                resolve(response.data);\n              }\n            });\n          } catch (err) {\n            resolve(null);\n          }\n        });\n      });\n      return from(Promise.all(profilePromises)).pipe(map(profiles => {\n        const profileMap = new Map();\n        profiles.forEach((profile, index) => {\n          if (profile) {\n            profileMap.set(friendIds[index], profile);\n          }\n        });\n        return friends.map(friend => {\n          return {\n            ...friend,\n            profile: profileMap.get(friend.friend_id) || null\n          };\n        });\n      }), catchError(error => {\n        return of([]);\n      }));\n    }));\n  }\n  addFriend(userId, friendId) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const friendship1 = {\n        user_id: userId,\n        friend_id: friendId,\n        created: new Date()\n      };\n      const friendship2 = {\n        user_id: friendId,\n        friend_id: userId,\n        created: new Date()\n      };\n      const {\n        error: error1\n      } = yield _this.supabaseService.supabase.from('friends').insert(friendship1);\n      if (error1) {\n        throw error1;\n      }\n      const {\n        error: error2\n      } = yield _this.supabaseService.supabase.from('friends').insert(friendship2);\n      if (error2) {\n        throw error2;\n      }\n      yield _this.updateFriendBadges(userId);\n      yield _this.updateFriendBadges(friendId);\n    })();\n  }\n  removeFriend(userId, friendId) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const {\n        error: error1\n      } = yield _this2.supabaseService.supabase.from('friends').delete().eq('user_id', userId).eq('friend_id', friendId);\n      if (error1) {\n        throw error1;\n      }\n      const {\n        error: error2\n      } = yield _this2.supabaseService.supabase.from('friends').delete().eq('user_id', friendId).eq('friend_id', userId);\n      if (error2) {\n        throw error2;\n      }\n      yield _this2.updateFriendBadges(userId);\n      yield _this2.updateFriendBadges(friendId);\n    })();\n  }\n  generateFriendCode(userId) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n      let code = '';\n      for (let i = 0; i < 8; i++) {\n        code += characters.charAt(Math.floor(Math.random() * characters.length));\n      }\n      const expiry = new Date();\n      expiry.setDate(expiry.getDate() + 1);\n      const {\n        error\n      } = yield _this3.supabaseService.supabase.from('profiles').update({\n        friend_code: code,\n        friend_code_expiry: expiry\n      }).eq('id', userId);\n      if (error) {\n        throw error;\n      }\n      return code;\n    })();\n  }\n  *validateUsernameFormat(input) {\n    const usernameRegex = /^[\\w.@+-]+$/;\n    return usernameRegex.test(input);\n  }\n  addFriendByCode(userId, code) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (!code || !_this4.validateUsernameFormat(code)) {\n        return false;\n      }\n      const {\n        data: users,\n        error: userError\n      } = yield _this4.supabaseService.supabase.from('profiles').select('*').eq('friend_code', code).limit(1);\n      if (userError) {\n        throw userError;\n      }\n      if (!users || users.length === 0) {\n        return false;\n      }\n      const friend = users[0];\n      const friendId = friend.id;\n      const expiryDate = friend.friend_code_expiry ? new Date(friend.friend_code_expiry) : null;\n      if (!expiryDate || expiryDate < new Date()) {\n        return false;\n      }\n      const {\n        data: existingFriends,\n        error: friendError\n      } = yield _this4.supabaseService.supabase.from('friends').select('*').eq('user_id', userId).eq('friend_id', friendId).limit(1);\n      if (friendError) {\n        throw friendError;\n      }\n      if (existingFriends && existingFriends.length > 0) {\n        return false;\n      }\n      yield _this4.addFriend(userId, friendId);\n      const {\n        error: clearError\n      } = yield _this4.supabaseService.supabase.from('profiles').update({\n        friend_code: null,\n        friend_code_expiry: null\n      }).eq('id', friendId);\n      if (clearError) {}\n      return true;\n    })();\n  }\n  *searchFriendsByUsername(userId, query, groupId) {\n    if (!query || query.length < 2) {\n      return of([]);\n    }\n    return this.getFriends(userId).pipe(switchMap(friends => {\n      if (friends.length === 0) {\n        return of([]);\n      }\n      const friendIds = friends.map(friend => friend.friend_id);\n      return from(this.supabaseService.supabase.from('profiles').select('id, username').in('id', friendIds).ilike('username', `%${query}%`).limit(10)).pipe(switchMap(response => {\n        if (response.error) {\n          return of([]);\n        }\n        const matchingFriends = response.data;\n        if (!groupId) {\n          return of(matchingFriends.map(friend => friend.username));\n        }\n        return from(this.supabaseService.supabase.from('group_members').select('user_id').eq('group_id', groupId)).pipe(switchMap(membersResponse => {\n          if (membersResponse.error) {\n            return of(matchingFriends.map(friend => friend.username));\n          }\n          const memberIds = membersResponse.data.map(member => member.user_id);\n          const filteredFriends = matchingFriends.filter(friend => !memberIds.includes(friend.id));\n          return from(this.supabaseService.supabase.from('group_join_requests').select('username_invited').eq('group_id', groupId)).pipe(map(invitesResponse => {\n            if (invitesResponse.error) {\n              return filteredFriends.map(friend => friend.username);\n            }\n            const invitedUsernames = invitesResponse.data.map(invite => invite.username_invited);\n            const finalFilteredFriends = filteredFriends.filter(friend => !invitedUsernames.includes(friend.username));\n            return finalFilteredFriends.map(friend => friend.username);\n          }));\n        }), catchError(error => {\n          return of(matchingFriends.map(friend => friend.username));\n        }));\n      }), catchError(error => {\n        return of([]);\n      }));\n    }));\n  }\n  updateFriendBadges(userId) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      const {\n        data: friends,\n        error: friendError\n      } = yield _this5.supabaseService.supabase.from('friends').select('*').eq('user_id', userId);\n      if (friendError) {\n        return;\n      }\n      const friendCount = friends ? friends.length : 0;\n      const {\n        data: badges,\n        error: badgeError\n      } = yield _this5.supabaseService.supabase.from('user_badges').select('*').eq('user_id', userId).limit(1);\n      if (badgeError) {\n        return;\n      }\n      if (!badges || badges.length === 0) {\n        return;\n      }\n      const badgeId = badges[0].id;\n      const updates = {};\n      if (friendCount >= 5) {\n        updates.badge_friends_5 = true;\n      }\n      if (friendCount >= 10) {\n        updates.badge_friends_10 = true;\n      }\n      if (Object.keys(updates).length > 0) {\n        const {\n          error: updateError\n        } = yield _this5.supabaseService.supabase.from('user_badges').update(updates).eq('id', badgeId);\n        if (updateError) {}\n      }\n    })();\n  }\n}\n_FriendService = FriendService;\n_FriendService.ɵfac = function FriendService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _FriendService)();\n};\n_FriendService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _FriendService,\n  factory: _FriendService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "names": ["inject", "map", "of", "switchMap", "from", "catchError", "SupabaseService", "FriendService", "constructor", "supabaseService", "getFriends", "userId", "supabase", "select", "or", "pipe", "response", "error", "normalizedFriends", "data", "friend", "friend_id", "user_id", "uniqueFriendIds", "Set", "uniqueFriends", "filter", "has", "add", "getFriendsWithProfiles", "friends", "length", "friendIds", "profilePromises", "friendId", "Promise", "resolve", "eq", "<PERSON><PERSON><PERSON><PERSON>", "then", "err", "all", "profiles", "profileMap", "Map", "for<PERSON>ach", "profile", "index", "set", "get", "addFriend", "_this", "_asyncToGenerator", "friendship1", "created", "Date", "friendship2", "error1", "insert", "error2", "updateFriendBadges", "removeFriend", "_this2", "delete", "generateFriendCode", "_this3", "characters", "code", "i", "char<PERSON>t", "Math", "floor", "random", "expiry", "setDate", "getDate", "update", "friend_code", "friend_code_expiry", "validateUsernameFormat", "input", "usernameRegex", "test", "addFriendByCode", "_this4", "users", "userError", "limit", "id", "expiryDate", "existingFriends", "friend<PERSON><PERSON><PERSON>", "clearError", "searchFriendsByUsername", "query", "groupId", "in", "ilike", "matchingFriends", "username", "membersResponse", "memberIds", "member", "filteredFriends", "includes", "invitesResponse", "invitedUsernames", "invite", "username_invited", "finalFilteredFriends", "_this5", "friendCount", "badges", "badgeError", "badgeId", "updates", "badge_friends_5", "badge_friends_10", "Object", "keys", "updateError", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\services\\friend.service.ts"], "sourcesContent": ["import { Injectable, inject } from '@angular/core';\r\nimport { Friend } from '../models/friend.model';\r\nimport { Observable, map, of, switchMap, from, catchError } from 'rxjs';\r\nimport { SupabaseService } from './supabase.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class FriendService {\r\n  private supabaseService = inject(SupabaseService);\r\n\r\n  getFriends(userId: string): Observable<Friend[]> {\r\n\r\n    return from(\r\n      this.supabaseService.supabase\r\n        .from('friends')\r\n        .select('*')\r\n        .or(`user_id.eq.${userId},friend_id.eq.${userId}`)\r\n    ).pipe(\r\n      map(response => {\r\n        if (response.error) {\r\n          return [];\r\n        }\r\n\r\n        const normalizedFriends = response.data.map(friend => {\r\n          if (friend.friend_id === userId) {\r\n            return {\r\n              ...friend,\r\n              friend_id: friend.user_id,\r\n              user_id: userId\r\n            };\r\n          }\r\n          return friend;\r\n        });\r\n\r\n        const uniqueFriendIds = new Set<string>();\r\n        const uniqueFriends = normalizedFriends.filter(friend => {\r\n          if (uniqueFriendIds.has(friend.friend_id)) {\r\n            return false;\r\n          }\r\n          uniqueFriendIds.add(friend.friend_id);\r\n          return true;\r\n        });\r\n\r\n        return uniqueFriends as Friend[];\r\n      })\r\n    );\r\n  }\r\n\r\n  getFriendsWithProfiles(userId: string): Observable<any[]> {\r\n\r\n    return this.getFriends(userId).pipe(\r\n      switchMap(friends => {\r\n        if (friends.length === 0) {\r\n          return of([]);\r\n        }\r\n\r\n        const friendIds = friends.map(friend => friend.friend_id);\r\n\r\n        const profilePromises = friendIds.map(friendId => {\r\n          return new Promise<any>((resolve) => {\r\n            try {\r\n              this.supabaseService.supabase\r\n                .from('profiles')\r\n                .select('*')\r\n                .eq('id', friendId)\r\n                .maybeSingle()\r\n                .then(response => {\r\n                  if (response.error) {\r\n                    resolve(null);\r\n                  } else {\r\n                    resolve(response.data);\r\n                  }\r\n                });\r\n            } catch (err: any) {\r\n              resolve(null);\r\n            }\r\n          });\r\n        });\r\n\r\n        return from(Promise.all(profilePromises)).pipe(\r\n          map(profiles => {\r\n\r\n            const profileMap = new Map<string, any>();\r\n            profiles.forEach((profile: any, index: number) => {\r\n              if (profile) {\r\n                profileMap.set(friendIds[index], profile);\r\n              }\r\n            });\r\n\r\n            return friends.map(friend => {\r\n              return {\r\n                ...friend,\r\n                profile: profileMap.get(friend.friend_id) || null\r\n              };\r\n            });\r\n          }),\r\n          catchError(error => {\r\n            return of([]);\r\n          })\r\n        );\r\n      })\r\n    );\r\n  }\r\n\r\n  async addFriend(userId: string, friendId: string): Promise<void> {\r\n\r\n    const friendship1: Friend = {\r\n      user_id: userId,\r\n      friend_id: friendId,\r\n      created: new Date()\r\n    };\r\n\r\n    const friendship2: Friend = {\r\n      user_id: friendId,\r\n      friend_id: userId,\r\n      created: new Date()\r\n    };\r\n\r\n    const { error: error1 } = await this.supabaseService.supabase\r\n      .from('friends')\r\n      .insert(friendship1);\r\n\r\n    if (error1) {\r\n      throw error1;\r\n    }\r\n\r\n    const { error: error2 } = await this.supabaseService.supabase\r\n      .from('friends')\r\n      .insert(friendship2);\r\n\r\n    if (error2) {\r\n      throw error2;\r\n    }\r\n\r\n    await this.updateFriendBadges(userId);\r\n    await this.updateFriendBadges(friendId);\r\n\r\n  }\r\n\r\n  async removeFriend(userId: string, friendId: string): Promise<void> {\r\n\r\n    const { error: error1 } = await this.supabaseService.supabase\r\n      .from('friends')\r\n      .delete()\r\n      .eq('user_id', userId)\r\n      .eq('friend_id', friendId);\r\n\r\n    if (error1) {\r\n      throw error1;\r\n    }\r\n\r\n    const { error: error2 } = await this.supabaseService.supabase\r\n      .from('friends')\r\n      .delete()\r\n      .eq('user_id', friendId)\r\n      .eq('friend_id', userId);\r\n\r\n    if (error2) {\r\n      throw error2;\r\n    }\r\n\r\n    await this.updateFriendBadges(userId);\r\n    await this.updateFriendBadges(friendId);\r\n\r\n  }\r\n\r\n  async generateFriendCode(userId: string): Promise<string> {\r\n\r\n    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\r\n    let code = '';\r\n    for (let i = 0; i < 8; i++) {\r\n      code += characters.charAt(Math.floor(Math.random() * characters.length));\r\n    }\r\n\r\n    const expiry = new Date();\r\n    expiry.setDate(expiry.getDate() + 1);\r\n\r\n    const { error } = await this.supabaseService.supabase\r\n      .from('profiles')\r\n      .update({\r\n        friend_code: code,\r\n        friend_code_expiry: expiry\r\n      })\r\n      .eq('id', userId);\r\n\r\n    if (error) {\r\n      throw error;\r\n    }\r\n\r\n    return code;\r\n  }\r\n\r\n  *\r\n  validateUsernameFormat(input: string): boolean {\r\n    const usernameRegex = /^[\\w.@+-]+$/;\r\n    return usernameRegex.test(input);\r\n  }\r\n\r\n  async addFriendByCode(userId: string, code: string): Promise<boolean> {\r\n\r\n    if (!code || !this.validateUsernameFormat(code)) {\r\n      return false;\r\n    }\r\n\r\n    const { data: users, error: userError } = await this.supabaseService.supabase\r\n      .from('profiles')\r\n      .select('*')\r\n      .eq('friend_code', code)\r\n      .limit(1);\r\n\r\n    if (userError) {\r\n      throw userError;\r\n    }\r\n\r\n    if (!users || users.length === 0) {\r\n      return false;\r\n    }\r\n\r\n    const friend = users[0];\r\n    const friendId = friend.id;\r\n\r\n    const expiryDate = friend.friend_code_expiry ? new Date(friend.friend_code_expiry) : null;\r\n    if (!expiryDate || expiryDate < new Date()) {\r\n      return false;\r\n    }\r\n\r\n    const { data: existingFriends, error: friendError } = await this.supabaseService.supabase\r\n      .from('friends')\r\n      .select('*')\r\n      .eq('user_id', userId)\r\n      .eq('friend_id', friendId)\r\n      .limit(1);\r\n\r\n    if (friendError) {\r\n      throw friendError;\r\n    }\r\n\r\n    if (existingFriends && existingFriends.length > 0) {\r\n      return false;\r\n    }\r\n\r\n    await this.addFriend(userId, friendId);\r\n\r\n    const { error: clearError } = await this.supabaseService.supabase\r\n      .from('profiles')\r\n      .update({\r\n        friend_code: null,\r\n        friend_code_expiry: null\r\n      })\r\n      .eq('id', friendId);\r\n\r\n    if (clearError) {\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  *\r\n  searchFriendsByUsername(userId: string, query: string, groupId?: string): Observable<string[]> {\r\n\r\n    if (!query || query.length < 2) {\r\n      return of([]);\r\n    }\r\n\r\n    return this.getFriends(userId).pipe(\r\n      switchMap(friends => {\r\n        if (friends.length === 0) {\r\n          return of([]);\r\n        }\r\n\r\n        const friendIds = friends.map(friend => friend.friend_id);\r\n\r\n        return from(\r\n          this.supabaseService.supabase\r\n            .from('profiles')\r\n            .select('id, username')\r\n            .in('id', friendIds)\r\n            .ilike('username', `%${query}%`)\r\n            .limit(10)\r\n        ).pipe(\r\n          switchMap(response => {\r\n            if (response.error) {\r\n              return of<string[]>([]);\r\n            }\r\n\r\n            const matchingFriends = response.data;\r\n\r\n            if (!groupId) {\r\n              return of<string[]>(matchingFriends.map(friend => friend.username));\r\n            }\r\n\r\n            return from(\r\n              this.supabaseService.supabase\r\n                .from('group_members')\r\n                .select('user_id')\r\n                .eq('group_id', groupId)\r\n            ).pipe(\r\n              switchMap(membersResponse => {\r\n                if (membersResponse.error) {\r\n                  return of<string[]>(matchingFriends.map(friend => friend.username));\r\n                }\r\n\r\n                const memberIds = membersResponse.data.map(member => member.user_id);\r\n\r\n                const filteredFriends = matchingFriends.filter(friend => !memberIds.includes(friend.id));\r\n\r\n                return from(\r\n                  this.supabaseService.supabase\r\n                    .from('group_join_requests')\r\n                    .select('username_invited')\r\n                    .eq('group_id', groupId)\r\n                ).pipe(\r\n                  map(invitesResponse => {\r\n                    if (invitesResponse.error) {\r\n                      return filteredFriends.map(friend => friend.username);\r\n                    }\r\n\r\n                    const invitedUsernames = invitesResponse.data.map(invite => invite.username_invited);\r\n\r\n                    const finalFilteredFriends = filteredFriends.filter(friend =>\r\n                      !invitedUsernames.includes(friend.username)\r\n                    );\r\n\r\n                    return finalFilteredFriends.map(friend => friend.username);\r\n                  })\r\n                );\r\n              }),\r\n              catchError(error => {\r\n                return of<string[]>(matchingFriends.map(friend => friend.username));\r\n              })\r\n            );\r\n          }),\r\n          catchError(error => {\r\n            return of<string[]>([]);\r\n          })\r\n        );\r\n      })\r\n    );\r\n  }\r\n\r\n  private async updateFriendBadges(userId: string): Promise<void> {\r\n\r\n    const { data: friends, error: friendError } = await this.supabaseService.supabase\r\n      .from('friends')\r\n      .select('*')\r\n      .eq('user_id', userId);\r\n\r\n    if (friendError) {\r\n      return;\r\n    }\r\n\r\n    const friendCount = friends ? friends.length : 0;\r\n\r\n    const { data: badges, error: badgeError } = await this.supabaseService.supabase\r\n      .from('user_badges')\r\n      .select('*')\r\n      .eq('user_id', userId)\r\n      .limit(1);\r\n\r\n    if (badgeError) {\r\n      return;\r\n    }\r\n\r\n    if (!badges || badges.length === 0) {\r\n      return;\r\n    }\r\n\r\n    const badgeId = badges[0].id;\r\n    const updates: any = {};\r\n\r\n    if (friendCount >= 5) {\r\n      updates.badge_friends_5 = true;\r\n    }\r\n\r\n    if (friendCount >= 10) {\r\n      updates.badge_friends_10 = true;\r\n    }\r\n\r\n    if (Object.keys(updates).length > 0) {\r\n\r\n      const { error: updateError } = await this.supabaseService.supabase\r\n        .from('user_badges')\r\n        .update(updates)\r\n        .eq('id', badgeId);\r\n\r\n      if (updateError) {\r\n      }\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAAqBA,MAAM,QAAQ,eAAe;AAElD,SAAqBC,GAAG,EAAEC,EAAE,EAAEC,SAAS,EAAEC,IAAI,EAAEC,UAAU,QAAQ,MAAM;AACvE,SAASC,eAAe,QAAQ,oBAAoB;;AAKpD,OAAM,MAAOC,aAAa;EAH1BC,YAAA;IAIU,KAAAC,eAAe,GAAGT,MAAM,CAACM,eAAe,CAAC;;EAEjDI,UAAUA,CAACC,MAAc;IAEvB,OAAOP,IAAI,CACT,IAAI,CAACK,eAAe,CAACG,QAAQ,CAC1BR,IAAI,CAAC,SAAS,CAAC,CACfS,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,cAAcH,MAAM,iBAAiBA,MAAM,EAAE,CAAC,CACrD,CAACI,IAAI,CACJd,GAAG,CAACe,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,KAAK,EAAE;QAClB,OAAO,EAAE;MACX;MAEA,MAAMC,iBAAiB,GAAGF,QAAQ,CAACG,IAAI,CAAClB,GAAG,CAACmB,MAAM,IAAG;QACnD,IAAIA,MAAM,CAACC,SAAS,KAAKV,MAAM,EAAE;UAC/B,OAAO;YACL,GAAGS,MAAM;YACTC,SAAS,EAAED,MAAM,CAACE,OAAO;YACzBA,OAAO,EAAEX;WACV;QACH;QACA,OAAOS,MAAM;MACf,CAAC,CAAC;MAEF,MAAMG,eAAe,GAAG,IAAIC,GAAG,EAAU;MACzC,MAAMC,aAAa,GAAGP,iBAAiB,CAACQ,MAAM,CAACN,MAAM,IAAG;QACtD,IAAIG,eAAe,CAACI,GAAG,CAACP,MAAM,CAACC,SAAS,CAAC,EAAE;UACzC,OAAO,KAAK;QACd;QACAE,eAAe,CAACK,GAAG,CAACR,MAAM,CAACC,SAAS,CAAC;QACrC,OAAO,IAAI;MACb,CAAC,CAAC;MAEF,OAAOI,aAAyB;IAClC,CAAC,CAAC,CACH;EACH;EAEAI,sBAAsBA,CAAClB,MAAc;IAEnC,OAAO,IAAI,CAACD,UAAU,CAACC,MAAM,CAAC,CAACI,IAAI,CACjCZ,SAAS,CAAC2B,OAAO,IAAG;MAClB,IAAIA,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QACxB,OAAO7B,EAAE,CAAC,EAAE,CAAC;MACf;MAEA,MAAM8B,SAAS,GAAGF,OAAO,CAAC7B,GAAG,CAACmB,MAAM,IAAIA,MAAM,CAACC,SAAS,CAAC;MAEzD,MAAMY,eAAe,GAAGD,SAAS,CAAC/B,GAAG,CAACiC,QAAQ,IAAG;QAC/C,OAAO,IAAIC,OAAO,CAAOC,OAAO,IAAI;UAClC,IAAI;YACF,IAAI,CAAC3B,eAAe,CAACG,QAAQ,CAC1BR,IAAI,CAAC,UAAU,CAAC,CAChBS,MAAM,CAAC,GAAG,CAAC,CACXwB,EAAE,CAAC,IAAI,EAAEH,QAAQ,CAAC,CAClBI,WAAW,EAAE,CACbC,IAAI,CAACvB,QAAQ,IAAG;cACf,IAAIA,QAAQ,CAACC,KAAK,EAAE;gBAClBmB,OAAO,CAAC,IAAI,CAAC;cACf,CAAC,MAAM;gBACLA,OAAO,CAACpB,QAAQ,CAACG,IAAI,CAAC;cACxB;YACF,CAAC,CAAC;UACN,CAAC,CAAC,OAAOqB,GAAQ,EAAE;YACjBJ,OAAO,CAAC,IAAI,CAAC;UACf;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF,OAAOhC,IAAI,CAAC+B,OAAO,CAACM,GAAG,CAACR,eAAe,CAAC,CAAC,CAAClB,IAAI,CAC5Cd,GAAG,CAACyC,QAAQ,IAAG;QAEb,MAAMC,UAAU,GAAG,IAAIC,GAAG,EAAe;QACzCF,QAAQ,CAACG,OAAO,CAAC,CAACC,OAAY,EAAEC,KAAa,KAAI;UAC/C,IAAID,OAAO,EAAE;YACXH,UAAU,CAACK,GAAG,CAAChB,SAAS,CAACe,KAAK,CAAC,EAAED,OAAO,CAAC;UAC3C;QACF,CAAC,CAAC;QAEF,OAAOhB,OAAO,CAAC7B,GAAG,CAACmB,MAAM,IAAG;UAC1B,OAAO;YACL,GAAGA,MAAM;YACT0B,OAAO,EAAEH,UAAU,CAACM,GAAG,CAAC7B,MAAM,CAACC,SAAS,CAAC,IAAI;WAC9C;QACH,CAAC,CAAC;MACJ,CAAC,CAAC,EACFhB,UAAU,CAACY,KAAK,IAAG;QACjB,OAAOf,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH;EACH;EAEMgD,SAASA,CAACvC,MAAc,EAAEuB,QAAgB;IAAA,IAAAiB,KAAA;IAAA,OAAAC,iBAAA;MAE9C,MAAMC,WAAW,GAAW;QAC1B/B,OAAO,EAAEX,MAAM;QACfU,SAAS,EAAEa,QAAQ;QACnBoB,OAAO,EAAE,IAAIC,IAAI;OAClB;MAED,MAAMC,WAAW,GAAW;QAC1BlC,OAAO,EAAEY,QAAQ;QACjBb,SAAS,EAAEV,MAAM;QACjB2C,OAAO,EAAE,IAAIC,IAAI;OAClB;MAED,MAAM;QAAEtC,KAAK,EAAEwC;MAAM,CAAE,SAASN,KAAI,CAAC1C,eAAe,CAACG,QAAQ,CAC1DR,IAAI,CAAC,SAAS,CAAC,CACfsD,MAAM,CAACL,WAAW,CAAC;MAEtB,IAAII,MAAM,EAAE;QACV,MAAMA,MAAM;MACd;MAEA,MAAM;QAAExC,KAAK,EAAE0C;MAAM,CAAE,SAASR,KAAI,CAAC1C,eAAe,CAACG,QAAQ,CAC1DR,IAAI,CAAC,SAAS,CAAC,CACfsD,MAAM,CAACF,WAAW,CAAC;MAEtB,IAAIG,MAAM,EAAE;QACV,MAAMA,MAAM;MACd;MAEA,MAAMR,KAAI,CAACS,kBAAkB,CAACjD,MAAM,CAAC;MACrC,MAAMwC,KAAI,CAACS,kBAAkB,CAAC1B,QAAQ,CAAC;IAAC;EAE1C;EAEM2B,YAAYA,CAAClD,MAAc,EAAEuB,QAAgB;IAAA,IAAA4B,MAAA;IAAA,OAAAV,iBAAA;MAEjD,MAAM;QAAEnC,KAAK,EAAEwC;MAAM,CAAE,SAASK,MAAI,CAACrD,eAAe,CAACG,QAAQ,CAC1DR,IAAI,CAAC,SAAS,CAAC,CACf2D,MAAM,EAAE,CACR1B,EAAE,CAAC,SAAS,EAAE1B,MAAM,CAAC,CACrB0B,EAAE,CAAC,WAAW,EAAEH,QAAQ,CAAC;MAE5B,IAAIuB,MAAM,EAAE;QACV,MAAMA,MAAM;MACd;MAEA,MAAM;QAAExC,KAAK,EAAE0C;MAAM,CAAE,SAASG,MAAI,CAACrD,eAAe,CAACG,QAAQ,CAC1DR,IAAI,CAAC,SAAS,CAAC,CACf2D,MAAM,EAAE,CACR1B,EAAE,CAAC,SAAS,EAAEH,QAAQ,CAAC,CACvBG,EAAE,CAAC,WAAW,EAAE1B,MAAM,CAAC;MAE1B,IAAIgD,MAAM,EAAE;QACV,MAAMA,MAAM;MACd;MAEA,MAAMG,MAAI,CAACF,kBAAkB,CAACjD,MAAM,CAAC;MACrC,MAAMmD,MAAI,CAACF,kBAAkB,CAAC1B,QAAQ,CAAC;IAAC;EAE1C;EAEM8B,kBAAkBA,CAACrD,MAAc;IAAA,IAAAsD,MAAA;IAAA,OAAAb,iBAAA;MAErC,MAAMc,UAAU,GAAG,sCAAsC;MACzD,IAAIC,IAAI,GAAG,EAAE;MACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC1BD,IAAI,IAAID,UAAU,CAACG,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAGN,UAAU,CAACnC,MAAM,CAAC,CAAC;MAC1E;MAEA,MAAM0C,MAAM,GAAG,IAAIlB,IAAI,EAAE;MACzBkB,MAAM,CAACC,OAAO,CAACD,MAAM,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;MAEpC,MAAM;QAAE1D;MAAK,CAAE,SAASgD,MAAI,CAACxD,eAAe,CAACG,QAAQ,CAClDR,IAAI,CAAC,UAAU,CAAC,CAChBwE,MAAM,CAAC;QACNC,WAAW,EAAEV,IAAI;QACjBW,kBAAkB,EAAEL;OACrB,CAAC,CACDpC,EAAE,CAAC,IAAI,EAAE1B,MAAM,CAAC;MAEnB,IAAIM,KAAK,EAAE;QACT,MAAMA,KAAK;MACb;MAEA,OAAOkD,IAAI;IAAC;EACd;EAEA,CACAY,sBAAsBA,CAACC,KAAa;IAClC,MAAMC,aAAa,GAAG,aAAa;IACnC,OAAOA,aAAa,CAACC,IAAI,CAACF,KAAK,CAAC;EAClC;EAEMG,eAAeA,CAACxE,MAAc,EAAEwD,IAAY;IAAA,IAAAiB,MAAA;IAAA,OAAAhC,iBAAA;MAEhD,IAAI,CAACe,IAAI,IAAI,CAACiB,MAAI,CAACL,sBAAsB,CAACZ,IAAI,CAAC,EAAE;QAC/C,OAAO,KAAK;MACd;MAEA,MAAM;QAAEhD,IAAI,EAAEkE,KAAK;QAAEpE,KAAK,EAAEqE;MAAS,CAAE,SAASF,MAAI,CAAC3E,eAAe,CAACG,QAAQ,CAC1ER,IAAI,CAAC,UAAU,CAAC,CAChBS,MAAM,CAAC,GAAG,CAAC,CACXwB,EAAE,CAAC,aAAa,EAAE8B,IAAI,CAAC,CACvBoB,KAAK,CAAC,CAAC,CAAC;MAEX,IAAID,SAAS,EAAE;QACb,MAAMA,SAAS;MACjB;MAEA,IAAI,CAACD,KAAK,IAAIA,KAAK,CAACtD,MAAM,KAAK,CAAC,EAAE;QAChC,OAAO,KAAK;MACd;MAEA,MAAMX,MAAM,GAAGiE,KAAK,CAAC,CAAC,CAAC;MACvB,MAAMnD,QAAQ,GAAGd,MAAM,CAACoE,EAAE;MAE1B,MAAMC,UAAU,GAAGrE,MAAM,CAAC0D,kBAAkB,GAAG,IAAIvB,IAAI,CAACnC,MAAM,CAAC0D,kBAAkB,CAAC,GAAG,IAAI;MACzF,IAAI,CAACW,UAAU,IAAIA,UAAU,GAAG,IAAIlC,IAAI,EAAE,EAAE;QAC1C,OAAO,KAAK;MACd;MAEA,MAAM;QAAEpC,IAAI,EAAEuE,eAAe;QAAEzE,KAAK,EAAE0E;MAAW,CAAE,SAASP,MAAI,CAAC3E,eAAe,CAACG,QAAQ,CACtFR,IAAI,CAAC,SAAS,CAAC,CACfS,MAAM,CAAC,GAAG,CAAC,CACXwB,EAAE,CAAC,SAAS,EAAE1B,MAAM,CAAC,CACrB0B,EAAE,CAAC,WAAW,EAAEH,QAAQ,CAAC,CACzBqD,KAAK,CAAC,CAAC,CAAC;MAEX,IAAII,WAAW,EAAE;QACf,MAAMA,WAAW;MACnB;MAEA,IAAID,eAAe,IAAIA,eAAe,CAAC3D,MAAM,GAAG,CAAC,EAAE;QACjD,OAAO,KAAK;MACd;MAEA,MAAMqD,MAAI,CAAClC,SAAS,CAACvC,MAAM,EAAEuB,QAAQ,CAAC;MAEtC,MAAM;QAAEjB,KAAK,EAAE2E;MAAU,CAAE,SAASR,MAAI,CAAC3E,eAAe,CAACG,QAAQ,CAC9DR,IAAI,CAAC,UAAU,CAAC,CAChBwE,MAAM,CAAC;QACNC,WAAW,EAAE,IAAI;QACjBC,kBAAkB,EAAE;OACrB,CAAC,CACDzC,EAAE,CAAC,IAAI,EAAEH,QAAQ,CAAC;MAErB,IAAI0D,UAAU,EAAE,CAChB;MAEA,OAAO,IAAI;IAAC;EACd;EAEA,CACAC,uBAAuBA,CAAClF,MAAc,EAAEmF,KAAa,EAAEC,OAAgB;IAErE,IAAI,CAACD,KAAK,IAAIA,KAAK,CAAC/D,MAAM,GAAG,CAAC,EAAE;MAC9B,OAAO7B,EAAE,CAAC,EAAE,CAAC;IACf;IAEA,OAAO,IAAI,CAACQ,UAAU,CAACC,MAAM,CAAC,CAACI,IAAI,CACjCZ,SAAS,CAAC2B,OAAO,IAAG;MAClB,IAAIA,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QACxB,OAAO7B,EAAE,CAAC,EAAE,CAAC;MACf;MAEA,MAAM8B,SAAS,GAAGF,OAAO,CAAC7B,GAAG,CAACmB,MAAM,IAAIA,MAAM,CAACC,SAAS,CAAC;MAEzD,OAAOjB,IAAI,CACT,IAAI,CAACK,eAAe,CAACG,QAAQ,CAC1BR,IAAI,CAAC,UAAU,CAAC,CAChBS,MAAM,CAAC,cAAc,CAAC,CACtBmF,EAAE,CAAC,IAAI,EAAEhE,SAAS,CAAC,CACnBiE,KAAK,CAAC,UAAU,EAAE,IAAIH,KAAK,GAAG,CAAC,CAC/BP,KAAK,CAAC,EAAE,CAAC,CACb,CAACxE,IAAI,CACJZ,SAAS,CAACa,QAAQ,IAAG;QACnB,IAAIA,QAAQ,CAACC,KAAK,EAAE;UAClB,OAAOf,EAAE,CAAW,EAAE,CAAC;QACzB;QAEA,MAAMgG,eAAe,GAAGlF,QAAQ,CAACG,IAAI;QAErC,IAAI,CAAC4E,OAAO,EAAE;UACZ,OAAO7F,EAAE,CAAWgG,eAAe,CAACjG,GAAG,CAACmB,MAAM,IAAIA,MAAM,CAAC+E,QAAQ,CAAC,CAAC;QACrE;QAEA,OAAO/F,IAAI,CACT,IAAI,CAACK,eAAe,CAACG,QAAQ,CAC1BR,IAAI,CAAC,eAAe,CAAC,CACrBS,MAAM,CAAC,SAAS,CAAC,CACjBwB,EAAE,CAAC,UAAU,EAAE0D,OAAO,CAAC,CAC3B,CAAChF,IAAI,CACJZ,SAAS,CAACiG,eAAe,IAAG;UAC1B,IAAIA,eAAe,CAACnF,KAAK,EAAE;YACzB,OAAOf,EAAE,CAAWgG,eAAe,CAACjG,GAAG,CAACmB,MAAM,IAAIA,MAAM,CAAC+E,QAAQ,CAAC,CAAC;UACrE;UAEA,MAAME,SAAS,GAAGD,eAAe,CAACjF,IAAI,CAAClB,GAAG,CAACqG,MAAM,IAAIA,MAAM,CAAChF,OAAO,CAAC;UAEpE,MAAMiF,eAAe,GAAGL,eAAe,CAACxE,MAAM,CAACN,MAAM,IAAI,CAACiF,SAAS,CAACG,QAAQ,CAACpF,MAAM,CAACoE,EAAE,CAAC,CAAC;UAExF,OAAOpF,IAAI,CACT,IAAI,CAACK,eAAe,CAACG,QAAQ,CAC1BR,IAAI,CAAC,qBAAqB,CAAC,CAC3BS,MAAM,CAAC,kBAAkB,CAAC,CAC1BwB,EAAE,CAAC,UAAU,EAAE0D,OAAO,CAAC,CAC3B,CAAChF,IAAI,CACJd,GAAG,CAACwG,eAAe,IAAG;YACpB,IAAIA,eAAe,CAACxF,KAAK,EAAE;cACzB,OAAOsF,eAAe,CAACtG,GAAG,CAACmB,MAAM,IAAIA,MAAM,CAAC+E,QAAQ,CAAC;YACvD;YAEA,MAAMO,gBAAgB,GAAGD,eAAe,CAACtF,IAAI,CAAClB,GAAG,CAAC0G,MAAM,IAAIA,MAAM,CAACC,gBAAgB,CAAC;YAEpF,MAAMC,oBAAoB,GAAGN,eAAe,CAAC7E,MAAM,CAACN,MAAM,IACxD,CAACsF,gBAAgB,CAACF,QAAQ,CAACpF,MAAM,CAAC+E,QAAQ,CAAC,CAC5C;YAED,OAAOU,oBAAoB,CAAC5G,GAAG,CAACmB,MAAM,IAAIA,MAAM,CAAC+E,QAAQ,CAAC;UAC5D,CAAC,CAAC,CACH;QACH,CAAC,CAAC,EACF9F,UAAU,CAACY,KAAK,IAAG;UACjB,OAAOf,EAAE,CAAWgG,eAAe,CAACjG,GAAG,CAACmB,MAAM,IAAIA,MAAM,CAAC+E,QAAQ,CAAC,CAAC;QACrE,CAAC,CAAC,CACH;MACH,CAAC,CAAC,EACF9F,UAAU,CAACY,KAAK,IAAG;QACjB,OAAOf,EAAE,CAAW,EAAE,CAAC;MACzB,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH;EACH;EAEc0D,kBAAkBA,CAACjD,MAAc;IAAA,IAAAmG,MAAA;IAAA,OAAA1D,iBAAA;MAE7C,MAAM;QAAEjC,IAAI,EAAEW,OAAO;QAAEb,KAAK,EAAE0E;MAAW,CAAE,SAASmB,MAAI,CAACrG,eAAe,CAACG,QAAQ,CAC9ER,IAAI,CAAC,SAAS,CAAC,CACfS,MAAM,CAAC,GAAG,CAAC,CACXwB,EAAE,CAAC,SAAS,EAAE1B,MAAM,CAAC;MAExB,IAAIgF,WAAW,EAAE;QACf;MACF;MAEA,MAAMoB,WAAW,GAAGjF,OAAO,GAAGA,OAAO,CAACC,MAAM,GAAG,CAAC;MAEhD,MAAM;QAAEZ,IAAI,EAAE6F,MAAM;QAAE/F,KAAK,EAAEgG;MAAU,CAAE,SAASH,MAAI,CAACrG,eAAe,CAACG,QAAQ,CAC5ER,IAAI,CAAC,aAAa,CAAC,CACnBS,MAAM,CAAC,GAAG,CAAC,CACXwB,EAAE,CAAC,SAAS,EAAE1B,MAAM,CAAC,CACrB4E,KAAK,CAAC,CAAC,CAAC;MAEX,IAAI0B,UAAU,EAAE;QACd;MACF;MAEA,IAAI,CAACD,MAAM,IAAIA,MAAM,CAACjF,MAAM,KAAK,CAAC,EAAE;QAClC;MACF;MAEA,MAAMmF,OAAO,GAAGF,MAAM,CAAC,CAAC,CAAC,CAACxB,EAAE;MAC5B,MAAM2B,OAAO,GAAQ,EAAE;MAEvB,IAAIJ,WAAW,IAAI,CAAC,EAAE;QACpBI,OAAO,CAACC,eAAe,GAAG,IAAI;MAChC;MAEA,IAAIL,WAAW,IAAI,EAAE,EAAE;QACrBI,OAAO,CAACE,gBAAgB,GAAG,IAAI;MACjC;MAEA,IAAIC,MAAM,CAACC,IAAI,CAACJ,OAAO,CAAC,CAACpF,MAAM,GAAG,CAAC,EAAE;QAEnC,MAAM;UAAEd,KAAK,EAAEuG;QAAW,CAAE,SAASV,MAAI,CAACrG,eAAe,CAACG,QAAQ,CAC/DR,IAAI,CAAC,aAAa,CAAC,CACnBwE,MAAM,CAACuC,OAAO,CAAC,CACf9E,EAAE,CAAC,IAAI,EAAE6E,OAAO,CAAC;QAEpB,IAAIM,WAAW,EAAE,CACjB;MACF;IAAC;EACH;;iBA7XWjH,aAAa;;mCAAbA,cAAa;AAAA;;SAAbA,cAAa;EAAAkH,OAAA,EAAblH,cAAa,CAAAmH,IAAA;EAAAC,UAAA,EAFZ;AAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}