<!-- Exact HTML from Django template with Angular syntax -->
<div class="container">
  <header>
    <div class="logo">
      <img src="assets/images/upshift_icon_mini.svg" alt="Upshift">
      <span>Upshift</span>
    </div>
    <h1>Create New Group</h1>
  </header>
  <a [routerLink]="['/groups']" class="back-link">&larr; Back to groups</a>

  <div *ngIf="errorMessage" class="messages">
    <div class="message error">{{ errorMessage }}</div>
  </div>

  <form (ngSubmit)="createGroup()" class="group-form">
    <div class="form-group">
      <label for="name">Group Name</label>
      <div class="input-group">
        <div class="emoji"><input type="text" [(ngModel)]="group.emoji" name="emoji" id="emoji" value="👥" appEmojiInput></div>
        <input type="text" [(ngModel)]="group.name" name="name" id="name" placeholder="Enter group name" required (input)="checkGroupName()">
      </div>
      <div *ngIf="nameError" class="error-message">{{ nameError }}</div>
      <div *ngIf="nameChecking" class="checking-message">Checking availability...</div>
      <div *ngIf="nameAvailable" class="success-message">Group name is available!</div>
    </div>

    <div class="form-group">
      <label for="timezone">Timezone</label>
      <div class="select-wrapper">
        <select [(ngModel)]="group.timezone" name="timezone" id="timezone" required [disabled]="isLoadingCountry">
          <option *ngFor="let tz of timezones" [value]="tz.value">{{ tz.label }}</option>
        </select>
        <div *ngIf="isLoadingCountry" class="loading-indicator">
          <ion-spinner name="dots" color="medium"></ion-spinner>
        </div>
      </div>
      <div class="help-text">
        <span *ngIf="isLoadingCountry">Detecting your timezone...</span>
        <span *ngIf="!isLoadingCountry">Select the timezone for this group. Cannot be changed later.</span>
      </div>
    </div>

    <div class="form-group">
      <label for="country">Country</label>
      <div class="select-wrapper">
        <select [(ngModel)]="group.country" name="country" id="country" required [disabled]="isLoadingCountry">
          <option *ngFor="let country of countries" [value]="country.code">{{ country.name }}</option>
        </select>
        <div *ngIf="isLoadingCountry" class="loading-indicator">
          <ion-spinner name="dots" color="medium"></ion-spinner>
        </div>
      </div>
      <div class="help-text">
        <span *ngIf="isLoadingCountry">Detecting your location...</span>
        <span *ngIf="!isLoadingCountry">Select the country for this group.</span>
      </div>
    </div>

    <button type="submit" class="btn primary full-width" [disabled]="isSubmitting || nameError">
      {{ isSubmitting ? 'Creating...' : 'Create Group' }}
    </button>
  </form>
</div>
