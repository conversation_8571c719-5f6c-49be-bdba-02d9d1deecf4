{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _GroupSideQuestComponent;\nimport { CommonModule } from '@angular/common';\nimport { IonicModule } from '@ionic/angular';\nimport { take } from 'rxjs/operators';\nimport { from } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/group-sidequest.service\";\nimport * as i2 from \"@ionic/angular\";\nimport * as i3 from \"../../services/supabase.service\";\nimport * as i4 from \"@angular/common\";\nfunction GroupSideQuestComponent_section_0_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"ion-spinner\", 6);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading side quest...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction GroupSideQuestComponent_section_0_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"p\");\n    i0.ɵɵtext(2, \"No side quest available for this group.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction GroupSideQuestComponent_section_0_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9);\n    i0.ɵɵlistener(\"click\", function GroupSideQuestComponent_section_0_div_5_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(!ctx_r1.isBeforeJoinDate && !ctx_r1.togglingSideQuest && ctx_r1.toggleSideQuest());\n    });\n    i0.ɵɵelementStart(2, \"div\", 10);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 11)(5, \"h3\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 12)(10, \"div\", 13)(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 14);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"div\", 15);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"completed\", ctx_r1.memberStatus == null ? null : ctx_r1.memberStatus.completed)(\"disabled-quest\", ctx_r1.isBeforeJoinDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.dailyQuest.current_quest.emoji, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.dailyQuest.current_quest.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.dailyQuest.current_quest.description);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", (ctx_r1.memberStatus == null ? null : ctx_r1.memberStatus.completed) ? ctx_r1.dailyQuest.current_quest.goal_value : 0, \"/\", ctx_r1.dailyQuest.current_quest.goal_value, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" Members: \", ctx_r1.dailyQuest.completed_members_count, \"/\", ctx_r1.dailyQuest.eligible_members_count, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\uD83D\\uDD25\", ctx_r1.dailyQuest.streak, \"d \");\n  }\n}\nfunction GroupSideQuestComponent_section_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 1)(1, \"h2\");\n    i0.ɵɵtext(2, \"Daily Group Side Quest\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, GroupSideQuestComponent_section_0_div_3_Template, 4, 0, \"div\", 2)(4, GroupSideQuestComponent_section_0_div_4_Template, 3, 0, \"div\", 3)(5, GroupSideQuestComponent_section_0_div_5_Template, 17, 12, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading && !ctx_r1.dailyQuest);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading && ctx_r1.dailyQuest);\n  }\n}\nexport class GroupSideQuestComponent {\n  constructor(groupSideQuestService, toastController, supabaseService) {\n    this.groupSideQuestService = groupSideQuestService;\n    this.toastController = toastController;\n    this.supabaseService = supabaseService;\n    this.groupId = '';\n    this.userId = '';\n    this.joinedDate = '';\n    this.isAdmin = false;\n    this.enableSidequests = true;\n    this.selectedDate = '';\n    this.dailyQuest = null;\n    this.memberStatus = null;\n    this.memberStatuses = [];\n    this.isBeforeJoinDate = false;\n    this.isLoading = true;\n    this.togglingSideQuest = false;\n    this.isTodaySelected = false;\n  }\n  ngOnInit() {\n    if (this.enableSidequests) {\n      this.checkAndResetMemberStatuses();\n    } else {\n      this.isLoading = false;\n    }\n  }\n  checkAndResetMemberStatuses() {\n    this.groupSideQuestService.ensureGroupHasDailySideQuest(this.groupId).pipe(take(1)).subscribe({\n      next: sideQuest => {\n        if (!sideQuest) {\n          this.isLoading = false;\n          return;\n        }\n        const today = new Date();\n        const todayStr = today.toISOString().split('T')[0];\n        if (sideQuest.date_assigned === todayStr) {\n          this.loadSideQuest();\n          return;\n        }\n        this.groupSideQuestService.getAllMemberStatuses(sideQuest.id).pipe(take(1)).subscribe({\n          next: statuses => {\n            const needsReset = statuses.some(s => s.last_updated !== todayStr);\n            if (needsReset) {\n              from(this.supabaseService.getClient().rpc('reset_group_sidequest_member_statuses', {\n                sidequest_id: sideQuest.id\n              })).subscribe({\n                next: () => {\n                  this.loadSideQuest();\n                },\n                error: error => {\n                  this.loadSideQuest();\n                }\n              });\n            } else {\n              this.loadSideQuest();\n            }\n          },\n          error: error => {\n            this.loadSideQuest();\n          }\n        });\n      },\n      error: error => {\n        this.isLoading = false;\n      }\n    });\n  }\n  ngOnChanges(changes) {\n    if (changes['groupId'] || changes['userId'] || changes['enableSidequests'] || changes['selectedDate']) {\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      const selectedDate = this.selectedDate ? new Date(this.selectedDate) : new Date();\n      selectedDate.setHours(0, 0, 0, 0);\n      this.isTodaySelected = selectedDate.getTime() === today.getTime();\n      if (!this.enableSidequests) {\n        this.dailyQuest = null;\n        this.memberStatus = null;\n        this.isLoading = false;\n        return;\n      }\n      this.loadSideQuest();\n    }\n  }\n  loadSideQuest() {\n    if (!this.groupId || !this.userId) {\n      this.isLoading = false;\n      return;\n    }\n    if (!this.enableSidequests) {\n      this.dailyQuest = null;\n      this.memberStatus = null;\n      this.isLoading = false;\n      return;\n    }\n    this.isLoading = true;\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    const joinedDate = new Date(this.joinedDate);\n    joinedDate.setHours(0, 0, 0, 0);\n    const eligibleDate = new Date(today);\n    eligibleDate.setDate(today.getDate() - 1);\n    this.isBeforeJoinDate = joinedDate > eligibleDate;\n    this.groupSideQuestService.ensureGroupHasDailySideQuest(this.groupId).pipe(take(1)).subscribe({\n      next: sideQuest => {\n        if (sideQuest) {\n          var _sideQuest$current_qu, _sideQuest$current_qu2, _sideQuest$current_qu3, _sideQuest$current_qu4, _sideQuest$current_qu5, _sideQuest$current_qu6;\n          this.dailyQuest = {\n            id: sideQuest.id,\n            group_id: sideQuest.group_id,\n            streak: sideQuest.streak,\n            completed: sideQuest.completed,\n            value_achieved: sideQuest.value_achieved,\n            date_assigned: sideQuest.date_assigned,\n            last_completed_date: sideQuest.last_completed_date,\n            category: sideQuest.category,\n            current_quest: {\n              id: ((_sideQuest$current_qu = sideQuest.current_quest) === null || _sideQuest$current_qu === void 0 ? void 0 : _sideQuest$current_qu.id) || '',\n              name: ((_sideQuest$current_qu2 = sideQuest.current_quest) === null || _sideQuest$current_qu2 === void 0 ? void 0 : _sideQuest$current_qu2.name) || '',\n              description: (_sideQuest$current_qu3 = sideQuest.current_quest) === null || _sideQuest$current_qu3 === void 0 ? void 0 : _sideQuest$current_qu3.description,\n              goal_value: ((_sideQuest$current_qu4 = sideQuest.current_quest) === null || _sideQuest$current_qu4 === void 0 ? void 0 : _sideQuest$current_qu4.goal_value) || 0,\n              goal_unit: ((_sideQuest$current_qu5 = sideQuest.current_quest) === null || _sideQuest$current_qu5 === void 0 ? void 0 : _sideQuest$current_qu5.goal_unit) || 'count',\n              emoji: ((_sideQuest$current_qu6 = sideQuest.current_quest) === null || _sideQuest$current_qu6 === void 0 ? void 0 : _sideQuest$current_qu6.emoji) || '🎯'\n            },\n            eligible_members_count: 0,\n            completed_members_count: 0\n          };\n        } else {\n          this.dailyQuest = null;\n        }\n        if (sideQuest) {\n          this.loadMemberStatuses(sideQuest.id);\n          this.loadMemberStatus(sideQuest.id, this.userId);\n        }\n        this.isLoading = false;\n      },\n      error: error => {\n        this.isLoading = false;\n      }\n    });\n  }\n  loadMemberStatuses(sideQuestId) {\n    this.groupSideQuestService.getAllMemberStatuses(sideQuestId).pipe(take(1)).subscribe({\n      next: statuses => {\n        this.memberStatuses = statuses;\n        if (this.dailyQuest) {\n          const today = new Date();\n          today.setHours(0, 0, 0, 0);\n          from(this.supabaseService.getClient().from('group_members').select('user_id, joined_date, nickname').eq('group_id', this.groupId)).subscribe(response => {\n            if (response.error) {\n              return;\n            }\n            const eligibleMembers = response.data.filter(member => {\n              const joinedDate = new Date(member.joined_date);\n              return joinedDate < today;\n            });\n            const eligibleMemberIds = eligibleMembers.map(m => m.user_id);\n            this.dailyQuest.eligible_members_count = eligibleMembers.length;\n            const todayStr = new Date().toISOString().split('T')[0];\n            this.dailyQuest.completed_members_count = statuses.filter(s => s.completed && eligibleMemberIds.includes(s.member_id) && s.last_updated === todayStr).length;\n            statuses.forEach(s => {\n              const member = response.data.find(m => m.user_id === s.member_id);\n              const isEligible = eligibleMemberIds.includes(s.member_id);\n              const todayStr = new Date().toISOString().split('T')[0];\n              const isUpdatedToday = s.last_updated === todayStr;\n              console.log(`Member ${(member === null || member === void 0 ? void 0 : member.nickname) || s.member_id}:`, {\n                member_id: s.member_id,\n                completed: s.completed,\n                last_updated: s.last_updated,\n                is_eligible: isEligible,\n                is_updated_today: isUpdatedToday,\n                is_counted: s.completed && isEligible && isUpdatedToday\n              });\n            });\n          });\n        }\n      },\n      error: error => {}\n    });\n  }\n  loadMemberStatus(sideQuestId, userId) {\n    this.groupSideQuestService.getMemberStatus(sideQuestId, userId).pipe(take(1)).subscribe({\n      next: status => {\n        if (status) {\n          this.memberStatus = status;\n        } else {\n          this.createMemberStatus(sideQuestId, userId);\n        }\n      },\n      error: error => {}\n    });\n  }\n  createMemberStatus(sideQuestId, userId) {\n    const today = new Date();\n    const dateString = today.toISOString().split('T')[0];\n    const newStatus = {\n      group_quest_id: sideQuestId,\n      member_id: userId,\n      completed: false,\n      value_achieved: 0,\n      last_updated: dateString\n    };\n    from(this.supabaseService.getClient().from('group_sidequest_member_status').insert(newStatus).select().single()).pipe(take(1)).subscribe({\n      next: response => {\n        if (response.error) {\n          return;\n        }\n        this.memberStatus = response.data;\n        this.loadMemberStatuses(sideQuestId);\n      },\n      error: error => {}\n    });\n  }\n  toggleSideQuest() {\n    if (!this.groupId || !this.userId || !this.dailyQuest || !this.memberStatus || this.isBeforeJoinDate || this.togglingSideQuest) {\n      return;\n    }\n    this.togglingSideQuest = true;\n    const newCompletionStatus = !this.memberStatus.completed;\n    const newValueAchieved = newCompletionStatus ? this.dailyQuest.current_quest.goal_value : 0;\n    const today = new Date().toISOString().split('T')[0];\n    console.log('Toggling sidequest completion:', {\n      previous_status: {\n        completed: this.memberStatus.completed,\n        value_achieved: this.memberStatus.value_achieved,\n        last_updated: this.memberStatus.last_updated\n      },\n      new_status: {\n        completed: newCompletionStatus,\n        value_achieved: newValueAchieved,\n        last_updated: today\n      }\n    });\n    if (this.memberStatus) {\n      this.memberStatus.completed = newCompletionStatus;\n      this.memberStatus.value_achieved = newValueAchieved;\n      this.memberStatus.last_updated = today;\n    }\n    if (this.dailyQuest) {\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      from(this.supabaseService.getClient().from('group_members').select('joined_date').eq('group_id', this.groupId).eq('user_id', this.userId).single()).subscribe(response => {\n        var _this$memberStatus;\n        if (response.error) {\n          return;\n        }\n        const joinedDate = new Date(response.data.joined_date);\n        const isEligible = joinedDate < today;\n        const todayStr = new Date().toISOString().split('T')[0];\n        const wasUpdatedToday = ((_this$memberStatus = this.memberStatus) === null || _this$memberStatus === void 0 ? void 0 : _this$memberStatus.last_updated) === todayStr;\n        if (isEligible) {\n          if (newCompletionStatus) {\n            this.dailyQuest.completed_members_count = (this.dailyQuest.completed_members_count || 0) + 1;\n          } else {\n            if (this.dailyQuest.completed_members_count && this.dailyQuest.completed_members_count > 0) {\n              this.dailyQuest.completed_members_count--;\n            }\n          }\n        } else {}\n      });\n    }\n    this.groupSideQuestService.toggleMemberCompletion(this.memberStatus.id, this.groupId).pipe(take(1)).subscribe({\n      next: updatedStatus => {\n        var _this$memberStatus2, _this$memberStatus3, _this$memberStatus4;\n        console.log('Status comparison - Local vs Server:', {\n          local: {\n            completed: (_this$memberStatus2 = this.memberStatus) === null || _this$memberStatus2 === void 0 ? void 0 : _this$memberStatus2.completed,\n            value_achieved: (_this$memberStatus3 = this.memberStatus) === null || _this$memberStatus3 === void 0 ? void 0 : _this$memberStatus3.value_achieved,\n            last_updated: (_this$memberStatus4 = this.memberStatus) === null || _this$memberStatus4 === void 0 ? void 0 : _this$memberStatus4.last_updated\n          },\n          server: {\n            completed: updatedStatus.completed,\n            value_achieved: updatedStatus.value_achieved,\n            last_updated: updatedStatus.last_updated\n          }\n        });\n        this.memberStatus = updatedStatus;\n        if (this.dailyQuest) {\n          const completedCount = this.dailyQuest.completed_members_count || 0;\n          const eligibleCount = this.dailyQuest.eligible_members_count || 0;\n          const allCompleted = completedCount === eligibleCount && eligibleCount > 0;\n          const wasCompleted = this.dailyQuest.completed;\n          if (allCompleted && !wasCompleted) {\n            this.dailyQuest.streak += 1;\n            this.dailyQuest.completed = true;\n          } else if (!allCompleted && wasCompleted) {\n            this.dailyQuest.streak = Math.max(0, this.dailyQuest.streak - 1);\n            this.dailyQuest.completed = false;\n          }\n        }\n        this.togglingSideQuest = false;\n      },\n      error: error => {\n        var _this$memberStatus5;\n        const previousLastUpdated = ((_this$memberStatus5 = this.memberStatus) === null || _this$memberStatus5 === void 0 ? void 0 : _this$memberStatus5.last_updated) || '';\n        if (this.memberStatus) {\n          this.memberStatus.completed = !newCompletionStatus;\n          this.memberStatus.value_achieved = !newCompletionStatus ? this.dailyQuest.current_quest.goal_value : 0;\n          this.memberStatus.last_updated = previousLastUpdated;\n        }\n        if (this.dailyQuest) {\n          const today = new Date();\n          today.setHours(0, 0, 0, 0);\n          from(this.supabaseService.getClient().from('group_members').select('joined_date').eq('group_id', this.groupId).eq('user_id', this.userId).single()).subscribe(response => {\n            if (response.error) return;\n            const joinedDate = new Date(response.data.joined_date);\n            const isEligible = joinedDate < today;\n            const todayStr = new Date().toISOString().split('T')[0];\n            const wasUpdatedToday = previousLastUpdated === todayStr;\n            if (isEligible && this.dailyQuest) {\n              if (!newCompletionStatus && wasUpdatedToday) {\n                this.dailyQuest.completed_members_count = (this.dailyQuest.completed_members_count || 0) + 1;\n              } else if (newCompletionStatus && !wasUpdatedToday) {\n                if (this.dailyQuest.completed_members_count && this.dailyQuest.completed_members_count > 0) {\n                  this.dailyQuest.completed_members_count--;\n                }\n              }\n            }\n          });\n        }\n        this.togglingSideQuest = false;\n        this.showErrorToast('Error updating side quest. Please try again.');\n      }\n    });\n  }\n  showErrorToast(message) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const toast = yield _this.toastController.create({\n        message: message,\n        duration: 3000,\n        position: 'bottom',\n        color: 'danger'\n      });\n      yield toast.present();\n    })();\n  }\n  getProgressPercentage() {\n    if (!this.dailyQuest || !this.dailyQuest.eligible_members_count || this.dailyQuest.eligible_members_count === 0) {\n      return 0;\n    }\n    return (this.dailyQuest.completed_members_count || 0) / this.dailyQuest.eligible_members_count * 100;\n  }\n  getMemberStatusText() {\n    if (!this.dailyQuest || !this.dailyQuest.eligible_members_count) {\n      return 'No members';\n    }\n    return `${this.dailyQuest.completed_members_count || 0}/${this.dailyQuest.eligible_members_count} completed`;\n  }\n}\n_GroupSideQuestComponent = GroupSideQuestComponent;\n_GroupSideQuestComponent.ɵfac = function GroupSideQuestComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _GroupSideQuestComponent)(i0.ɵɵdirectiveInject(i1.GroupSideQuestService), i0.ɵɵdirectiveInject(i2.ToastController), i0.ɵɵdirectiveInject(i3.SupabaseService));\n};\n_GroupSideQuestComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _GroupSideQuestComponent,\n  selectors: [[\"app-group-sidequest\"]],\n  inputs: {\n    groupId: \"groupId\",\n    userId: \"userId\",\n    joinedDate: \"joinedDate\",\n    isAdmin: \"isAdmin\",\n    enableSidequests: \"enableSidequests\",\n    selectedDate: \"selectedDate\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 1,\n  vars: 1,\n  consts: [[\"class\", \"daily-side-quest\", 4, \"ngIf\"], [1, \"daily-side-quest\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"no-sidequest\", 4, \"ngIf\"], [\"class\", \"quest-list\", 4, \"ngIf\"], [1, \"loading-container\"], [\"name\", \"circles\"], [1, \"no-sidequest\"], [1, \"quest-list\"], [1, \"quest-item\", 3, \"click\"], [1, \"quest-icon\"], [1, \"quest-info\"], [1, \"progress-container\"], [1, \"progress-text\", \"values\"], [1, \"members-count\"], [1, \"quest-streak\"]],\n  template: function GroupSideQuestComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, GroupSideQuestComponent_section_0_Template, 6, 3, \"section\", 0);\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.enableSidequests && ctx.isTodaySelected);\n    }\n  },\n  dependencies: [CommonModule, i4.NgIf, IonicModule, i2.IonSpinner],\n  styles: [\".daily-side-quest[_ngcontent-%COMP%] {\\n  margin: 20px 0;\\n}\\n.daily-side-quest[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  margin-bottom: 15px;\\n  color: #fff;\\n  font-weight: 600;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 32px 0;\\n}\\n.loading-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--ion-color-medium);\\n  margin: 0;\\n}\\n\\n.no-sidequest[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 20px 0;\\n}\\n.no-sidequest[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--ion-color-medium);\\n  margin-bottom: 16px;\\n}\\n\\n.quest-list[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n\\n.quest-item[_ngcontent-%COMP%] {\\n  background-color: var(--quest-bg, #1C1C1E);\\n  border: 1px solid var(--quest-border, #2C2C2E);\\n  border-radius: 8px;\\n  padding: 12px;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  margin-bottom: 10px;\\n  position: relative;\\n}\\n.quest-item[_ngcontent-%COMP%]:active {\\n  transform: scale(0.98);\\n}\\n.quest-item.completed[_ngcontent-%COMP%] {\\n  border-color: var(--accent-color, #4169E1);\\n}\\n.quest-item.completed[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: var(--accent-color, #4169E1);\\n}\\n.quest-item.disabled-quest[_ngcontent-%COMP%] {\\n  opacity: 0.7;\\n  cursor: not-allowed;\\n  pointer-events: none;\\n  position: relative;\\n}\\n.quest-item.disabled-quest[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-color: rgba(0, 0, 0, 0.2);\\n  pointer-events: none;\\n  border-radius: 8px;\\n}\\n.quest-item[_ngcontent-%COMP%]   .quest-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  min-width: 24px;\\n  text-align: center;\\n  margin-right: 12px;\\n}\\n.quest-item[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n}\\n.quest-item[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin: 0;\\n  margin-bottom: 2px;\\n}\\n.quest-item[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--secondary-text, #8E8E93);\\n  font-size: 12px;\\n  margin: 0;\\n  margin-bottom: 4px;\\n}\\n.quest-item[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin: 4px 0;\\n}\\n.quest-item[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--secondary-text, #8E8E93);\\n  margin-top: 2px;\\n}\\n.quest-item[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]   .progress-text.values[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  width: 100%;\\n}\\n.quest-item[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]   .members-count[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-right: 10px;\\n  font-size: 12px;\\n}\\n.quest-item[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]   .members-count[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:not(.quest-message) {\\n  background-color: rgba(65, 105, 225, 0.2);\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  font-size: 11px;\\n  white-space: nowrap;\\n  display: inline-block;\\n}\\n.quest-item[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]   .quest-message[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n  font-style: italic;\\n  font-size: 12px;\\n  display: block !important;\\n  margin-top: 5px;\\n  font-weight: bold;\\n  text-align: left;\\n  width: 100%;\\n  white-space: normal;\\n  line-height: 1.2;\\n  background-color: transparent !important;\\n  padding: 0 !important;\\n  border-radius: 0 !important;\\n}\\n.quest-item[_ngcontent-%COMP%]   .quest-streak[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  white-space: nowrap;\\n  color: #ff9500;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["CommonModule", "IonicModule", "take", "from", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "GroupSideQuestComponent_section_0_div_5_Template_div_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "isBeforeJoinDate", "togglingSideQuest", "toggleSideQuest", "ɵɵadvance", "ɵɵclassProp", "memberStatus", "completed", "ɵɵtextInterpolate1", "dailyQuest", "current_quest", "emoji", "ɵɵtextInterpolate", "name", "description", "ɵɵtextInterpolate2", "goal_value", "completed_members_count", "eligible_members_count", "streak", "ɵɵtemplate", "GroupSideQuestComponent_section_0_div_3_Template", "GroupSideQuestComponent_section_0_div_4_Template", "GroupSideQuestComponent_section_0_div_5_Template", "ɵɵproperty", "isLoading", "GroupSideQuestComponent", "constructor", "groupSideQuestService", "toastController", "supabaseService", "groupId", "userId", "joinedDate", "isAdmin", "enableSidequests", "selectedDate", "memberStatuses", "isTodaySelected", "ngOnInit", "checkAndResetMemberStatuses", "ensureGroupHasDailySideQuest", "pipe", "subscribe", "next", "sideQuest", "today", "Date", "todayStr", "toISOString", "split", "date_assigned", "loadSideQuest", "getAllMemberStatuses", "id", "statuses", "needsReset", "some", "s", "last_updated", "getClient", "rpc", "sidequest_id", "error", "ngOnChanges", "changes", "setHours", "getTime", "eligibleDate", "setDate", "getDate", "_sideQuest$current_qu", "_sideQuest$current_qu2", "_sideQuest$current_qu3", "_sideQuest$current_qu4", "_sideQuest$current_qu5", "_sideQuest$current_qu6", "group_id", "value_achieved", "last_completed_date", "category", "goal_unit", "loadMemberStatuses", "loadMemberStatus", "sideQuestId", "select", "eq", "response", "eligibleMembers", "data", "filter", "member", "joined_date", "eligibleMemberIds", "map", "m", "user_id", "length", "includes", "member_id", "for<PERSON>ach", "find", "isEligible", "isUpdatedToday", "console", "log", "nickname", "is_eligible", "is_updated_today", "is_counted", "getMemberStatus", "status", "createMemberStatus", "dateString", "newStatus", "group_quest_id", "insert", "single", "newCompletionStatus", "newValueAchieved", "previous_status", "new_status", "_this$memberStatus", "wasUpdatedToday", "toggleMemberCompletion", "updatedStatus", "_this$memberStatus2", "_this$memberStatus3", "_this$memberStatus4", "local", "server", "completedCount", "eligibleCount", "allCompleted", "wasCompleted", "Math", "max", "_this$memberStatus5", "previousLastUpdated", "showErrorToast", "message", "_this", "_asyncToGenerator", "toast", "create", "duration", "position", "color", "present", "getProgressPercentage", "getMemberStatusText", "ɵɵdirectiveInject", "i1", "GroupSideQuestService", "i2", "ToastController", "i3", "SupabaseService", "selectors", "inputs", "features", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "GroupSideQuestComponent_Template", "rf", "ctx", "GroupSideQuestComponent_section_0_Template", "i4", "NgIf", "Ion<PERSON><PERSON><PERSON>", "styles"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\components\\group-sidequest\\group-sidequest.component.ts", "C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\components\\group-sidequest\\group-sidequest.component.html"], "sourcesContent": ["import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { GroupSideQuestService } from '../../services/group-sidequest.service';\r\nimport { GroupDailyQuest, GroupSideQuestMemberStatus } from '../../models/group-sidequest.model';\r\nimport { take } from 'rxjs/operators';\r\nimport { from } from 'rxjs';\r\nimport { ToastController } from '@ionic/angular';\r\nimport { SupabaseService } from '../../services/supabase.service';\r\n\r\n@Component({\r\n  selector: 'app-group-sidequest',\r\n  templateUrl: './group-sidequest.component.html',\r\n  styleUrls: ['./group-sidequest.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, IonicModule]\r\n})\r\nexport class GroupSideQuestComponent implements OnInit, OnChanges {\r\n  @Input() groupId: string = '';\r\n  @Input() userId: string = '';\r\n  @Input() joinedDate: string = '';\r\n  @Input() isAdmin: boolean = false;\r\n  @Input() enableSidequests: boolean = true;\r\n  @Input() selectedDate: string = '';\r\n\r\n  dailyQuest: GroupDailyQuest | null = null;\r\n  memberStatus: GroupSideQuestMemberStatus | null = null;\r\n  memberStatuses: GroupSideQuestMemberStatus[] = [];\r\n  isBeforeJoinDate: boolean = false;\r\n  isLoading: boolean = true;\r\n  togglingSideQuest: boolean = false;\r\n  isTodaySelected: boolean = false;\r\n\r\n  constructor(\r\n    private groupSideQuestService: GroupSideQuestService,\r\n    private toastController: ToastController,\r\n    private supabaseService: SupabaseService\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    if (this.enableSidequests) {\r\n      this.checkAndResetMemberStatuses();\r\n    } else {\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  checkAndResetMemberStatuses() {\r\n    this.groupSideQuestService.ensureGroupHasDailySideQuest(this.groupId).pipe(\r\n      take(1)\r\n    ).subscribe({\r\n      next: (sideQuest) => {\r\n        if (!sideQuest) {\r\n          this.isLoading = false;\r\n          return;\r\n        }\r\n\r\n        const today = new Date();\r\n        const todayStr = today.toISOString().split('T')[0]; \n\r\n        if (sideQuest.date_assigned === todayStr) {\r\n          this.loadSideQuest();\r\n          return;\r\n        }\r\n\r\n\r\n        this.groupSideQuestService.getAllMemberStatuses(sideQuest.id).pipe(\r\n          take(1)\r\n        ).subscribe({\r\n          next: (statuses) => {\r\n            const needsReset = statuses.some(s => s.last_updated !== todayStr);\r\n\r\n            if (needsReset) {\r\n\r\n              from(\r\n                this.supabaseService.getClient()\r\n                  .rpc('reset_group_sidequest_member_statuses', { sidequest_id: sideQuest.id })\r\n              ).subscribe({\r\n                next: () => {\r\n                  this.loadSideQuest();\r\n                },\r\n                error: (error) => {\r\n                  this.loadSideQuest();\r\n                }\r\n              });\r\n            } else {\r\n              this.loadSideQuest();\r\n            }\r\n          },\r\n          error: (error) => {\r\n            this.loadSideQuest();\r\n          }\r\n        });\r\n      },\r\n      error: (error) => {\r\n        this.isLoading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges) {\r\n    if (changes['groupId'] || changes['userId'] || changes['enableSidequests'] || changes['selectedDate']) {\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      const selectedDate = this.selectedDate ? new Date(this.selectedDate) : new Date();\r\n      selectedDate.setHours(0, 0, 0, 0);\r\n      this.isTodaySelected = selectedDate.getTime() === today.getTime();\r\n\r\n\r\n      if (!this.enableSidequests) {\r\n        this.dailyQuest = null;\r\n        this.memberStatus = null;\r\n        this.isLoading = false;\r\n        return;\r\n      }\r\n\r\n      this.loadSideQuest();\r\n    }\r\n  }\r\n\r\n  loadSideQuest() {\r\n\r\n    if (!this.groupId || !this.userId) {\r\n      this.isLoading = false;\r\n      return;\r\n    }\r\n\r\n    if (!this.enableSidequests) {\r\n      this.dailyQuest = null;\r\n      this.memberStatus = null;\r\n      this.isLoading = false;\r\n      return;\r\n    }\r\n\r\n    this.isLoading = true;\r\n\r\n    const today = new Date();\r\n    today.setHours(0, 0, 0, 0);\r\n    const joinedDate = new Date(this.joinedDate);\r\n    joinedDate.setHours(0, 0, 0, 0);\r\n    const eligibleDate = new Date(today);\r\n    eligibleDate.setDate(today.getDate() - 1);\r\n    this.isBeforeJoinDate = joinedDate > eligibleDate;\r\n\r\n\r\n    this.groupSideQuestService.ensureGroupHasDailySideQuest(this.groupId).pipe(\r\n      take(1)\r\n    ).subscribe({\r\n      next: (sideQuest) => {\r\n\r\n        if (sideQuest) {\r\n          this.dailyQuest = {\r\n            id: sideQuest.id,\r\n            group_id: sideQuest.group_id,\r\n            streak: sideQuest.streak,\r\n            completed: sideQuest.completed,\r\n            value_achieved: sideQuest.value_achieved,\r\n            date_assigned: sideQuest.date_assigned,\r\n            last_completed_date: sideQuest.last_completed_date,\r\n            category: sideQuest.category,\r\n            current_quest: {\r\n              id: sideQuest.current_quest?.id || '',\r\n              name: sideQuest.current_quest?.name || '',\r\n              description: sideQuest.current_quest?.description,\r\n              goal_value: sideQuest.current_quest?.goal_value || 0,\r\n              goal_unit: sideQuest.current_quest?.goal_unit || 'count',\r\n              emoji: sideQuest.current_quest?.emoji || '🎯'\r\n            },\r\n            eligible_members_count: 0,\r\n            completed_members_count: 0\r\n          };\r\n        } else {\r\n          this.dailyQuest = null;\r\n        }\r\n\r\n\r\n        if (sideQuest) {\r\n          this.loadMemberStatuses(sideQuest.id);\r\n\r\n          this.loadMemberStatus(sideQuest.id, this.userId);\r\n        }\r\n\r\n        this.isLoading = false;\r\n      },\r\n      error: (error) => {\r\n        this.isLoading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  loadMemberStatuses(sideQuestId: string) {\r\n    this.groupSideQuestService.getAllMemberStatuses(sideQuestId).pipe(\r\n      take(1)\r\n    ).subscribe({\r\n      next: (statuses) => {\r\n        this.memberStatuses = statuses;\r\n\r\n        if (this.dailyQuest) {\r\n          const today = new Date();\r\n          today.setHours(0, 0, 0, 0); \n\r\n          from(\r\n            this.supabaseService.getClient()\r\n              .from('group_members')\r\n              .select('user_id, joined_date, nickname')\r\n              .eq('group_id', this.groupId)\r\n          ).subscribe(response => {\r\n            if (response.error) {\r\n              return;\r\n            }\r\n\r\n            const eligibleMembers = response.data.filter(member => {\r\n              const joinedDate = new Date(member.joined_date);\r\n              return joinedDate < today; \n            });\r\n\r\n            const eligibleMemberIds = eligibleMembers.map(m => m.user_id);\r\n\r\n            this.dailyQuest!.eligible_members_count = eligibleMembers.length;\r\n\r\n            const todayStr = new Date().toISOString().split('T')[0];\r\n\r\n            this.dailyQuest!.completed_members_count = statuses.filter(s =>\r\n              s.completed && \n              eligibleMemberIds.includes(s.member_id) && \n              s.last_updated === todayStr \n            ).length;\r\n\r\n\r\n\r\n            statuses.forEach(s => {\r\n              const member = response.data.find(m => m.user_id === s.member_id);\r\n              const isEligible = eligibleMemberIds.includes(s.member_id);\r\n              const todayStr = new Date().toISOString().split('T')[0];\r\n              const isUpdatedToday = s.last_updated === todayStr;\r\n              console.log(`Member ${member?.nickname || s.member_id}:`, {\r\n                member_id: s.member_id,\r\n                completed: s.completed,\r\n                last_updated: s.last_updated,\r\n                is_eligible: isEligible,\r\n                is_updated_today: isUpdatedToday,\r\n                is_counted: s.completed && isEligible && isUpdatedToday\r\n              });\r\n            });\r\n          });\r\n        }\r\n      },\r\n      error: (error) => {\r\n      }\r\n    });\r\n  }\r\n\r\n  loadMemberStatus(sideQuestId: string, userId: string) {\r\n    this.groupSideQuestService.getMemberStatus(sideQuestId, userId).pipe(\r\n      take(1)\r\n    ).subscribe({\r\n      next: (status) => {\r\n        if (status) {\r\n          this.memberStatus = status;\r\n        } else {\r\n          this.createMemberStatus(sideQuestId, userId);\r\n        }\r\n      },\r\n      error: (error) => {\r\n      }\r\n    });\r\n  }\r\n\r\n  createMemberStatus(sideQuestId: string, userId: string) {\r\n    const today = new Date();\r\n    const dateString = today.toISOString().split('T')[0];\r\n\r\n    const newStatus = {\r\n      group_quest_id: sideQuestId,\r\n      member_id: userId,\r\n      completed: false,\r\n      value_achieved: 0,\r\n      last_updated: dateString\r\n    };\r\n\r\n    from(\r\n      this.supabaseService.getClient()\r\n        .from('group_sidequest_member_status')\r\n        .insert(newStatus)\r\n        .select()\r\n        .single()\r\n    ).pipe(\r\n      take(1)\r\n    ).subscribe({\r\n      next: (response) => {\r\n        if (response.error) {\r\n          return;\r\n        }\r\n\r\n        this.memberStatus = response.data as GroupSideQuestMemberStatus;\r\n\r\n        this.loadMemberStatuses(sideQuestId);\r\n      },\r\n      error: (error) => {\r\n      }\r\n    });\r\n  }\r\n\r\n  toggleSideQuest() {\r\n    if (!this.groupId || !this.userId || !this.dailyQuest || !this.memberStatus || this.isBeforeJoinDate || this.togglingSideQuest) {\r\n      return;\r\n    }\r\n\r\n    this.togglingSideQuest = true;\r\n\r\n    const newCompletionStatus = !this.memberStatus.completed;\r\n    const newValueAchieved = newCompletionStatus ? this.dailyQuest.current_quest.goal_value : 0;\r\n    const today = new Date().toISOString().split('T')[0]; \n\r\n    console.log('Toggling sidequest completion:', {\r\n      previous_status: {\r\n        completed: this.memberStatus.completed,\r\n        value_achieved: this.memberStatus.value_achieved,\r\n        last_updated: this.memberStatus.last_updated\r\n      },\r\n      new_status: {\r\n        completed: newCompletionStatus,\r\n        value_achieved: newValueAchieved,\r\n        last_updated: today\r\n      }\r\n    });\r\n\r\n    if (this.memberStatus) {\r\n      this.memberStatus.completed = newCompletionStatus;\r\n      this.memberStatus.value_achieved = newValueAchieved;\r\n      this.memberStatus.last_updated = today; \n    }\r\n\r\n    if (this.dailyQuest) {\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0); \n\r\n      from(\r\n        this.supabaseService.getClient()\r\n          .from('group_members')\r\n          .select('joined_date')\r\n          .eq('group_id', this.groupId)\r\n          .eq('user_id', this.userId)\r\n          .single()\r\n      ).subscribe(response => {\r\n        if (response.error) {\r\n          return;\r\n        }\r\n\r\n        const joinedDate = new Date(response.data.joined_date);\r\n        const isEligible = joinedDate < today; \n\r\n        const todayStr = new Date().toISOString().split('T')[0];\r\n\r\n        const wasUpdatedToday = this.memberStatus?.last_updated === todayStr;\r\n\r\n        if (isEligible) {\r\n          if (newCompletionStatus) {\r\n            this.dailyQuest!.completed_members_count = (this.dailyQuest!.completed_members_count || 0) + 1;\r\n          } else {\r\n            if (this.dailyQuest!.completed_members_count && this.dailyQuest!.completed_members_count > 0) {\r\n              this.dailyQuest!.completed_members_count--;\r\n            }\r\n          }\r\n        } else {\r\n        }\r\n\r\n      });\r\n    }\r\n\r\n    this.groupSideQuestService.toggleMemberCompletion(this.memberStatus.id, this.groupId).pipe(\r\n      take(1)\r\n    ).subscribe({\r\n      next: (updatedStatus) => {\r\n\r\n        console.log('Status comparison - Local vs Server:', {\r\n          local: {\r\n            completed: this.memberStatus?.completed,\r\n            value_achieved: this.memberStatus?.value_achieved,\r\n            last_updated: this.memberStatus?.last_updated\r\n          },\r\n          server: {\r\n            completed: updatedStatus.completed,\r\n            value_achieved: updatedStatus.value_achieved,\r\n            last_updated: updatedStatus.last_updated\r\n          }\r\n        });\r\n\r\n        this.memberStatus = updatedStatus;\r\n\r\n        if (this.dailyQuest) {\r\n          const completedCount = this.dailyQuest.completed_members_count || 0;\r\n          const eligibleCount = this.dailyQuest.eligible_members_count || 0;\r\n          const allCompleted = completedCount === eligibleCount && eligibleCount > 0;\r\n\r\n          const wasCompleted = this.dailyQuest.completed;\r\n\r\n\r\n          if (allCompleted && !wasCompleted) {\r\n            this.dailyQuest.streak += 1;\r\n            this.dailyQuest.completed = true;\r\n          } else if (!allCompleted && wasCompleted) {\r\n            this.dailyQuest.streak = Math.max(0, this.dailyQuest.streak - 1);\r\n            this.dailyQuest.completed = false;\r\n          }\r\n        }\r\n\r\n        this.togglingSideQuest = false;\r\n      },\r\n      error: (error) => {\r\n\r\n        const previousLastUpdated = this.memberStatus?.last_updated || '';\r\n\r\n        if (this.memberStatus) {\r\n          this.memberStatus.completed = !newCompletionStatus;\r\n          this.memberStatus.value_achieved = !newCompletionStatus ? this.dailyQuest!.current_quest.goal_value : 0;\r\n          this.memberStatus.last_updated = previousLastUpdated; \n        }\r\n\r\n        if (this.dailyQuest) {\r\n          const today = new Date();\r\n          today.setHours(0, 0, 0, 0);\r\n\r\n          from(\r\n            this.supabaseService.getClient()\r\n              .from('group_members')\r\n              .select('joined_date')\r\n              .eq('group_id', this.groupId)\r\n              .eq('user_id', this.userId)\r\n              .single()\r\n          ).subscribe(response => {\r\n            if (response.error) return;\r\n\r\n            const joinedDate = new Date(response.data.joined_date);\r\n            const isEligible = joinedDate < today;\r\n\r\n            const todayStr = new Date().toISOString().split('T')[0];\r\n            const wasUpdatedToday = previousLastUpdated === todayStr;\r\n\r\n            if (isEligible && this.dailyQuest) {\r\n              if (!newCompletionStatus && wasUpdatedToday) {\r\n                this.dailyQuest.completed_members_count = (this.dailyQuest.completed_members_count || 0) + 1;\r\n              } else if (newCompletionStatus && !wasUpdatedToday) {\r\n                if (this.dailyQuest.completed_members_count && this.dailyQuest.completed_members_count > 0) {\r\n                  this.dailyQuest.completed_members_count--;\r\n                }\r\n              }\r\n            }\r\n          });\r\n        }\r\n\r\n        this.togglingSideQuest = false;\r\n        this.showErrorToast('Error updating side quest. Please try again.');\r\n      }\r\n    });\r\n  }\r\n\r\n  async showErrorToast(message: string) {\r\n    const toast = await this.toastController.create({\r\n      message: message,\r\n      duration: 3000,\r\n      position: 'bottom',\r\n      color: 'danger'\r\n    });\r\n    await toast.present();\r\n  }\r\n\r\n  getProgressPercentage(): number {\r\n    if (!this.dailyQuest || !this.dailyQuest.eligible_members_count || this.dailyQuest.eligible_members_count === 0) {\r\n      return 0;\r\n    }\r\n\r\n    return (this.dailyQuest.completed_members_count || 0) / this.dailyQuest.eligible_members_count * 100;\r\n  }\r\n\r\n  getMemberStatusText(): string {\r\n    if (!this.dailyQuest || !this.dailyQuest.eligible_members_count) {\r\n      return 'No members';\r\n    }\r\n\r\n    return `${this.dailyQuest.completed_members_count || 0}/${this.dailyQuest.eligible_members_count} completed`;\r\n  }\r\n}\r\n", "<section class=\"daily-side-quest\" *ngIf=\"enableSidequests && isTodaySelected\">\r\n  <h2>Daily Group Side Quest</h2>\r\n\r\n  <div class=\"loading-container\" *ngIf=\"isLoading\">\r\n    <ion-spinner name=\"circles\"></ion-spinner>\r\n    <p>Loading side quest...</p>\r\n  </div>\r\n\r\n  <div class=\"no-sidequest\" *ngIf=\"!isLoading && !dailyQuest\">\r\n    <p>No side quest available for this group.</p>\r\n    <!-- Side quests are automatically loaded from the pool, no need for manual creation -->\r\n  </div>\r\n\r\n  <div class=\"quest-list\" *ngIf=\"!isLoading && dailyQuest\">\r\n    <div class=\"quest-item\"\r\n         [class.completed]=\"memberStatus?.completed\"\r\n         [class.disabled-quest]=\"isBeforeJoinDate\"\r\n         (click)=\"!isBeforeJoinDate && !togglingSideQuest && toggleSideQuest()\">\r\n      <div class=\"quest-icon\">\r\n        {{ dailyQuest.current_quest.emoji }}\r\n      </div>\r\n      <div class=\"quest-info\">\r\n        <h3>{{ dailyQuest.current_quest.name }}</h3>\r\n        <p>{{ dailyQuest.current_quest.description }}</p>\r\n        <div class=\"progress-container\">\r\n          <div class=\"progress-text values\">\r\n            <span>\r\n              {{ memberStatus?.completed ? dailyQuest.current_quest.goal_value : 0 }}/{{ dailyQuest.current_quest.goal_value }}\r\n            </span>\r\n            <span class=\"members-count\">\r\n              Members: {{ dailyQuest.completed_members_count }}/{{ dailyQuest.eligible_members_count }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"quest-streak\">\r\n        🔥{{ dailyQuest.streak }}d\r\n      </div>\r\n    </div>\r\n  </div>\r\n</section>\r\n"], "mappings": ";;AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAG5C,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,IAAI,QAAQ,MAAM;;;;;;;;ICHzBC,EAAA,CAAAC,cAAA,aAAiD;IAC/CD,EAAA,CAAAE,SAAA,qBAA0C;IAC1CF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,4BAAqB;IAC1BH,EAD0B,CAAAI,YAAA,EAAI,EACxB;;;;;IAGJJ,EADF,CAAAC,cAAA,aAA4D,QACvD;IAAAD,EAAA,CAAAG,MAAA,8CAAuC;IAE5CH,EAF4C,CAAAI,YAAA,EAAI,EAE1C;;;;;;IAGJJ,EADF,CAAAC,cAAA,aAAyD,aAIqB;IAAvED,EAAA,CAAAK,UAAA,mBAAAC,sEAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,EAAAF,MAAA,CAAAG,gBAAA,KAAAH,MAAA,CAAAI,iBAAA,IAAoDJ,MAAA,CAAAK,eAAA,EAAiB;IAAA,EAAC;IACzEd,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAC,cAAA,cAAwB,SAClB;IAAAD,EAAA,CAAAG,MAAA,GAAmC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5CJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAA0C;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAG7CJ,EAFJ,CAAAC,cAAA,cAAgC,eACI,YAC1B;IACJD,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAC,cAAA,gBAA4B;IAC1BD,EAAA,CAAAG,MAAA,IACF;IAGNH,EAHM,CAAAI,YAAA,EAAO,EACH,EACF,EACF;IACNJ,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAG,MAAA,IACF;IAEJH,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;;IAxBCJ,EAAA,CAAAe,SAAA,EAA2C;IAC3Cf,EADA,CAAAgB,WAAA,cAAAP,MAAA,CAAAQ,YAAA,kBAAAR,MAAA,CAAAQ,YAAA,CAAAC,SAAA,CAA2C,mBAAAT,MAAA,CAAAG,gBAAA,CACF;IAG1CZ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAV,MAAA,CAAAW,UAAA,CAAAC,aAAA,CAAAC,KAAA,MACF;IAEMtB,EAAA,CAAAe,SAAA,GAAmC;IAAnCf,EAAA,CAAAuB,iBAAA,CAAAd,MAAA,CAAAW,UAAA,CAAAC,aAAA,CAAAG,IAAA,CAAmC;IACpCxB,EAAA,CAAAe,SAAA,GAA0C;IAA1Cf,EAAA,CAAAuB,iBAAA,CAAAd,MAAA,CAAAW,UAAA,CAAAC,aAAA,CAAAI,WAAA,CAA0C;IAIvCzB,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAA0B,kBAAA,OAAAjB,MAAA,CAAAQ,YAAA,kBAAAR,MAAA,CAAAQ,YAAA,CAAAC,SAAA,IAAAT,MAAA,CAAAW,UAAA,CAAAC,aAAA,CAAAM,UAAA,WAAAlB,MAAA,CAAAW,UAAA,CAAAC,aAAA,CAAAM,UAAA,MACF;IAEE3B,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAA0B,kBAAA,eAAAjB,MAAA,CAAAW,UAAA,CAAAQ,uBAAA,OAAAnB,MAAA,CAAAW,UAAA,CAAAS,sBAAA,MACF;IAKJ7B,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,kBAAAV,MAAA,CAAAW,UAAA,CAAAU,MAAA,OACF;;;;;IApCJ9B,EADF,CAAAC,cAAA,iBAA8E,SACxE;IAAAD,EAAA,CAAAG,MAAA,6BAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAY/BJ,EAVA,CAAA+B,UAAA,IAAAC,gDAAA,iBAAiD,IAAAC,gDAAA,iBAKW,IAAAC,gDAAA,mBAKH;IA2B3DlC,EAAA,CAAAI,YAAA,EAAU;;;;IArCwBJ,EAAA,CAAAe,SAAA,GAAe;IAAff,EAAA,CAAAmC,UAAA,SAAA1B,MAAA,CAAA2B,SAAA,CAAe;IAKpBpC,EAAA,CAAAe,SAAA,EAA+B;IAA/Bf,EAAA,CAAAmC,UAAA,UAAA1B,MAAA,CAAA2B,SAAA,KAAA3B,MAAA,CAAAW,UAAA,CAA+B;IAKjCpB,EAAA,CAAAe,SAAA,EAA8B;IAA9Bf,EAAA,CAAAmC,UAAA,UAAA1B,MAAA,CAAA2B,SAAA,IAAA3B,MAAA,CAAAW,UAAA,CAA8B;;;ADIzD,OAAM,MAAOiB,uBAAuB;EAgBlCC,YACUC,qBAA4C,EAC5CC,eAAgC,EAChCC,eAAgC;IAFhC,KAAAF,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IAlBhB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,MAAM,GAAW,EAAE;IACnB,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAC,gBAAgB,GAAY,IAAI;IAChC,KAAAC,YAAY,GAAW,EAAE;IAElC,KAAA3B,UAAU,GAA2B,IAAI;IACzC,KAAAH,YAAY,GAAsC,IAAI;IACtD,KAAA+B,cAAc,GAAiC,EAAE;IACjD,KAAApC,gBAAgB,GAAY,KAAK;IACjC,KAAAwB,SAAS,GAAY,IAAI;IACzB,KAAAvB,iBAAiB,GAAY,KAAK;IAClC,KAAAoC,eAAe,GAAY,KAAK;EAM5B;EAEJC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACJ,gBAAgB,EAAE;MACzB,IAAI,CAACK,2BAA2B,EAAE;IACpC,CAAC,MAAM;MACL,IAAI,CAACf,SAAS,GAAG,KAAK;IACxB;EACF;EAEAe,2BAA2BA,CAAA;IACzB,IAAI,CAACZ,qBAAqB,CAACa,4BAA4B,CAAC,IAAI,CAACV,OAAO,CAAC,CAACW,IAAI,CACxEvD,IAAI,CAAC,CAAC,CAAC,CACR,CAACwD,SAAS,CAAC;MACVC,IAAI,EAAGC,SAAS,IAAI;QAClB,IAAI,CAACA,SAAS,EAAE;UACd,IAAI,CAACpB,SAAS,GAAG,KAAK;UACtB;QACF;QAEA,MAAMqB,KAAK,GAAG,IAAIC,IAAI,EAAE;QACxB,MAAMC,QAAQ,GAAGF,KAAK,CAACG,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAElD,IAAIL,SAAS,CAACM,aAAa,KAAKH,QAAQ,EAAE;UACxC,IAAI,CAACI,aAAa,EAAE;UACpB;QACF;QAGA,IAAI,CAACxB,qBAAqB,CAACyB,oBAAoB,CAACR,SAAS,CAACS,EAAE,CAAC,CAACZ,IAAI,CAChEvD,IAAI,CAAC,CAAC,CAAC,CACR,CAACwD,SAAS,CAAC;UACVC,IAAI,EAAGW,QAAQ,IAAI;YACjB,MAAMC,UAAU,GAAGD,QAAQ,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,YAAY,KAAKX,QAAQ,CAAC;YAElE,IAAIQ,UAAU,EAAE;cAEdpE,IAAI,CACF,IAAI,CAAC0C,eAAe,CAAC8B,SAAS,EAAE,CAC7BC,GAAG,CAAC,uCAAuC,EAAE;gBAAEC,YAAY,EAAEjB,SAAS,CAACS;cAAE,CAAE,CAAC,CAChF,CAACX,SAAS,CAAC;gBACVC,IAAI,EAAEA,CAAA,KAAK;kBACT,IAAI,CAACQ,aAAa,EAAE;gBACtB,CAAC;gBACDW,KAAK,EAAGA,KAAK,IAAI;kBACf,IAAI,CAACX,aAAa,EAAE;gBACtB;eACD,CAAC;YACJ,CAAC,MAAM;cACL,IAAI,CAACA,aAAa,EAAE;YACtB;UACF,CAAC;UACDW,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAACX,aAAa,EAAE;UACtB;SACD,CAAC;MACJ,CAAC;MACDW,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACtC,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEAuC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,SAAS,CAAC,IAAIA,OAAO,CAAC,QAAQ,CAAC,IAAIA,OAAO,CAAC,kBAAkB,CAAC,IAAIA,OAAO,CAAC,cAAc,CAAC,EAAE;MACrG,MAAMnB,KAAK,GAAG,IAAIC,IAAI,EAAE;MACxBD,KAAK,CAACoB,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC1B,MAAM9B,YAAY,GAAG,IAAI,CAACA,YAAY,GAAG,IAAIW,IAAI,CAAC,IAAI,CAACX,YAAY,CAAC,GAAG,IAAIW,IAAI,EAAE;MACjFX,YAAY,CAAC8B,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACjC,IAAI,CAAC5B,eAAe,GAAGF,YAAY,CAAC+B,OAAO,EAAE,KAAKrB,KAAK,CAACqB,OAAO,EAAE;MAGjE,IAAI,CAAC,IAAI,CAAChC,gBAAgB,EAAE;QAC1B,IAAI,CAAC1B,UAAU,GAAG,IAAI;QACtB,IAAI,CAACH,YAAY,GAAG,IAAI;QACxB,IAAI,CAACmB,SAAS,GAAG,KAAK;QACtB;MACF;MAEA,IAAI,CAAC2B,aAAa,EAAE;IACtB;EACF;EAEAA,aAAaA,CAAA;IAEX,IAAI,CAAC,IAAI,CAACrB,OAAO,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;MACjC,IAAI,CAACP,SAAS,GAAG,KAAK;MACtB;IACF;IAEA,IAAI,CAAC,IAAI,CAACU,gBAAgB,EAAE;MAC1B,IAAI,CAAC1B,UAAU,GAAG,IAAI;MACtB,IAAI,CAACH,YAAY,GAAG,IAAI;MACxB,IAAI,CAACmB,SAAS,GAAG,KAAK;MACtB;IACF;IAEA,IAAI,CAACA,SAAS,GAAG,IAAI;IAErB,MAAMqB,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxBD,KAAK,CAACoB,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1B,MAAMjC,UAAU,GAAG,IAAIc,IAAI,CAAC,IAAI,CAACd,UAAU,CAAC;IAC5CA,UAAU,CAACiC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC/B,MAAME,YAAY,GAAG,IAAIrB,IAAI,CAACD,KAAK,CAAC;IACpCsB,YAAY,CAACC,OAAO,CAACvB,KAAK,CAACwB,OAAO,EAAE,GAAG,CAAC,CAAC;IACzC,IAAI,CAACrE,gBAAgB,GAAGgC,UAAU,GAAGmC,YAAY;IAGjD,IAAI,CAACxC,qBAAqB,CAACa,4BAA4B,CAAC,IAAI,CAACV,OAAO,CAAC,CAACW,IAAI,CACxEvD,IAAI,CAAC,CAAC,CAAC,CACR,CAACwD,SAAS,CAAC;MACVC,IAAI,EAAGC,SAAS,IAAI;QAElB,IAAIA,SAAS,EAAE;UAAA,IAAA0B,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;UACb,IAAI,CAACnE,UAAU,GAAG;YAChB6C,EAAE,EAAET,SAAS,CAACS,EAAE;YAChBuB,QAAQ,EAAEhC,SAAS,CAACgC,QAAQ;YAC5B1D,MAAM,EAAE0B,SAAS,CAAC1B,MAAM;YACxBZ,SAAS,EAAEsC,SAAS,CAACtC,SAAS;YAC9BuE,cAAc,EAAEjC,SAAS,CAACiC,cAAc;YACxC3B,aAAa,EAAEN,SAAS,CAACM,aAAa;YACtC4B,mBAAmB,EAAElC,SAAS,CAACkC,mBAAmB;YAClDC,QAAQ,EAAEnC,SAAS,CAACmC,QAAQ;YAC5BtE,aAAa,EAAE;cACb4C,EAAE,EAAE,EAAAiB,qBAAA,GAAA1B,SAAS,CAACnC,aAAa,cAAA6D,qBAAA,uBAAvBA,qBAAA,CAAyBjB,EAAE,KAAI,EAAE;cACrCzC,IAAI,EAAE,EAAA2D,sBAAA,GAAA3B,SAAS,CAACnC,aAAa,cAAA8D,sBAAA,uBAAvBA,sBAAA,CAAyB3D,IAAI,KAAI,EAAE;cACzCC,WAAW,GAAA2D,sBAAA,GAAE5B,SAAS,CAACnC,aAAa,cAAA+D,sBAAA,uBAAvBA,sBAAA,CAAyB3D,WAAW;cACjDE,UAAU,EAAE,EAAA0D,sBAAA,GAAA7B,SAAS,CAACnC,aAAa,cAAAgE,sBAAA,uBAAvBA,sBAAA,CAAyB1D,UAAU,KAAI,CAAC;cACpDiE,SAAS,EAAE,EAAAN,sBAAA,GAAA9B,SAAS,CAACnC,aAAa,cAAAiE,sBAAA,uBAAvBA,sBAAA,CAAyBM,SAAS,KAAI,OAAO;cACxDtE,KAAK,EAAE,EAAAiE,sBAAA,GAAA/B,SAAS,CAACnC,aAAa,cAAAkE,sBAAA,uBAAvBA,sBAAA,CAAyBjE,KAAK,KAAI;aAC1C;YACDO,sBAAsB,EAAE,CAAC;YACzBD,uBAAuB,EAAE;WAC1B;QACH,CAAC,MAAM;UACL,IAAI,CAACR,UAAU,GAAG,IAAI;QACxB;QAGA,IAAIoC,SAAS,EAAE;UACb,IAAI,CAACqC,kBAAkB,CAACrC,SAAS,CAACS,EAAE,CAAC;UAErC,IAAI,CAAC6B,gBAAgB,CAACtC,SAAS,CAACS,EAAE,EAAE,IAAI,CAACtB,MAAM,CAAC;QAClD;QAEA,IAAI,CAACP,SAAS,GAAG,KAAK;MACxB,CAAC;MACDsC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACtC,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEAyD,kBAAkBA,CAACE,WAAmB;IACpC,IAAI,CAACxD,qBAAqB,CAACyB,oBAAoB,CAAC+B,WAAW,CAAC,CAAC1C,IAAI,CAC/DvD,IAAI,CAAC,CAAC,CAAC,CACR,CAACwD,SAAS,CAAC;MACVC,IAAI,EAAGW,QAAQ,IAAI;QACjB,IAAI,CAAClB,cAAc,GAAGkB,QAAQ;QAE9B,IAAI,IAAI,CAAC9C,UAAU,EAAE;UACnB,MAAMqC,KAAK,GAAG,IAAIC,IAAI,EAAE;UACxBD,KAAK,CAACoB,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAE1B9E,IAAI,CACF,IAAI,CAAC0C,eAAe,CAAC8B,SAAS,EAAE,CAC7BxE,IAAI,CAAC,eAAe,CAAC,CACrBiG,MAAM,CAAC,gCAAgC,CAAC,CACxCC,EAAE,CAAC,UAAU,EAAE,IAAI,CAACvD,OAAO,CAAC,CAChC,CAACY,SAAS,CAAC4C,QAAQ,IAAG;YACrB,IAAIA,QAAQ,CAACxB,KAAK,EAAE;cAClB;YACF;YAEA,MAAMyB,eAAe,GAAGD,QAAQ,CAACE,IAAI,CAACC,MAAM,CAACC,MAAM,IAAG;cACpD,MAAM1D,UAAU,GAAG,IAAIc,IAAI,CAAC4C,MAAM,CAACC,WAAW,CAAC;cAC/C,OAAO3D,UAAU,GAAGa,KAAK;YAC3B,CAAC,CAAC;YAEF,MAAM+C,iBAAiB,GAAGL,eAAe,CAACM,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC;YAE7D,IAAI,CAACvF,UAAW,CAACS,sBAAsB,GAAGsE,eAAe,CAACS,MAAM;YAEhE,MAAMjD,QAAQ,GAAG,IAAID,IAAI,EAAE,CAACE,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAEvD,IAAI,CAACzC,UAAW,CAACQ,uBAAuB,GAAGsC,QAAQ,CAACmC,MAAM,CAAChC,CAAC,IAC1DA,CAAC,CAACnD,SAAS,IACXsF,iBAAiB,CAACK,QAAQ,CAACxC,CAAC,CAACyC,SAAS,CAAC,IACvCzC,CAAC,CAACC,YAAY,KAAKX,QAAQ,CAC5B,CAACiD,MAAM;YAIR1C,QAAQ,CAAC6C,OAAO,CAAC1C,CAAC,IAAG;cACnB,MAAMiC,MAAM,GAAGJ,QAAQ,CAACE,IAAI,CAACY,IAAI,CAACN,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKtC,CAAC,CAACyC,SAAS,CAAC;cACjE,MAAMG,UAAU,GAAGT,iBAAiB,CAACK,QAAQ,CAACxC,CAAC,CAACyC,SAAS,CAAC;cAC1D,MAAMnD,QAAQ,GAAG,IAAID,IAAI,EAAE,CAACE,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;cACvD,MAAMqD,cAAc,GAAG7C,CAAC,CAACC,YAAY,KAAKX,QAAQ;cAClDwD,OAAO,CAACC,GAAG,CAAC,UAAU,CAAAd,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEe,QAAQ,KAAIhD,CAAC,CAACyC,SAAS,GAAG,EAAE;gBACxDA,SAAS,EAAEzC,CAAC,CAACyC,SAAS;gBACtB5F,SAAS,EAAEmD,CAAC,CAACnD,SAAS;gBACtBoD,YAAY,EAAED,CAAC,CAACC,YAAY;gBAC5BgD,WAAW,EAAEL,UAAU;gBACvBM,gBAAgB,EAAEL,cAAc;gBAChCM,UAAU,EAAEnD,CAAC,CAACnD,SAAS,IAAI+F,UAAU,IAAIC;eAC1C,CAAC;YACJ,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ;MACF,CAAC;MACDxC,KAAK,EAAGA,KAAK,IAAI,CACjB;KACD,CAAC;EACJ;EAEAoB,gBAAgBA,CAACC,WAAmB,EAAEpD,MAAc;IAClD,IAAI,CAACJ,qBAAqB,CAACkF,eAAe,CAAC1B,WAAW,EAAEpD,MAAM,CAAC,CAACU,IAAI,CAClEvD,IAAI,CAAC,CAAC,CAAC,CACR,CAACwD,SAAS,CAAC;MACVC,IAAI,EAAGmE,MAAM,IAAI;QACf,IAAIA,MAAM,EAAE;UACV,IAAI,CAACzG,YAAY,GAAGyG,MAAM;QAC5B,CAAC,MAAM;UACL,IAAI,CAACC,kBAAkB,CAAC5B,WAAW,EAAEpD,MAAM,CAAC;QAC9C;MACF,CAAC;MACD+B,KAAK,EAAGA,KAAK,IAAI,CACjB;KACD,CAAC;EACJ;EAEAiD,kBAAkBA,CAAC5B,WAAmB,EAAEpD,MAAc;IACpD,MAAMc,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxB,MAAMkE,UAAU,GAAGnE,KAAK,CAACG,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAEpD,MAAMgE,SAAS,GAAG;MAChBC,cAAc,EAAE/B,WAAW;MAC3Be,SAAS,EAAEnE,MAAM;MACjBzB,SAAS,EAAE,KAAK;MAChBuE,cAAc,EAAE,CAAC;MACjBnB,YAAY,EAAEsD;KACf;IAED7H,IAAI,CACF,IAAI,CAAC0C,eAAe,CAAC8B,SAAS,EAAE,CAC7BxE,IAAI,CAAC,+BAA+B,CAAC,CACrCgI,MAAM,CAACF,SAAS,CAAC,CACjB7B,MAAM,EAAE,CACRgC,MAAM,EAAE,CACZ,CAAC3E,IAAI,CACJvD,IAAI,CAAC,CAAC,CAAC,CACR,CAACwD,SAAS,CAAC;MACVC,IAAI,EAAG2C,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACxB,KAAK,EAAE;UAClB;QACF;QAEA,IAAI,CAACzD,YAAY,GAAGiF,QAAQ,CAACE,IAAkC;QAE/D,IAAI,CAACP,kBAAkB,CAACE,WAAW,CAAC;MACtC,CAAC;MACDrB,KAAK,EAAGA,KAAK,IAAI,CACjB;KACD,CAAC;EACJ;EAEA5D,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAAC4B,OAAO,IAAI,CAAC,IAAI,CAACC,MAAM,IAAI,CAAC,IAAI,CAACvB,UAAU,IAAI,CAAC,IAAI,CAACH,YAAY,IAAI,IAAI,CAACL,gBAAgB,IAAI,IAAI,CAACC,iBAAiB,EAAE;MAC9H;IACF;IAEA,IAAI,CAACA,iBAAiB,GAAG,IAAI;IAE7B,MAAMoH,mBAAmB,GAAG,CAAC,IAAI,CAAChH,YAAY,CAACC,SAAS;IACxD,MAAMgH,gBAAgB,GAAGD,mBAAmB,GAAG,IAAI,CAAC7G,UAAU,CAACC,aAAa,CAACM,UAAU,GAAG,CAAC;IAC3F,MAAM8B,KAAK,GAAG,IAAIC,IAAI,EAAE,CAACE,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAEpDsD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;MAC5Ce,eAAe,EAAE;QACfjH,SAAS,EAAE,IAAI,CAACD,YAAY,CAACC,SAAS;QACtCuE,cAAc,EAAE,IAAI,CAACxE,YAAY,CAACwE,cAAc;QAChDnB,YAAY,EAAE,IAAI,CAACrD,YAAY,CAACqD;OACjC;MACD8D,UAAU,EAAE;QACVlH,SAAS,EAAE+G,mBAAmB;QAC9BxC,cAAc,EAAEyC,gBAAgB;QAChC5D,YAAY,EAAEb;;KAEjB,CAAC;IAEF,IAAI,IAAI,CAACxC,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACC,SAAS,GAAG+G,mBAAmB;MACjD,IAAI,CAAChH,YAAY,CAACwE,cAAc,GAAGyC,gBAAgB;MACnD,IAAI,CAACjH,YAAY,CAACqD,YAAY,GAAGb,KAAK;IACxC;IAEA,IAAI,IAAI,CAACrC,UAAU,EAAE;MACnB,MAAMqC,KAAK,GAAG,IAAIC,IAAI,EAAE;MACxBD,KAAK,CAACoB,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAE1B9E,IAAI,CACF,IAAI,CAAC0C,eAAe,CAAC8B,SAAS,EAAE,CAC7BxE,IAAI,CAAC,eAAe,CAAC,CACrBiG,MAAM,CAAC,aAAa,CAAC,CACrBC,EAAE,CAAC,UAAU,EAAE,IAAI,CAACvD,OAAO,CAAC,CAC5BuD,EAAE,CAAC,SAAS,EAAE,IAAI,CAACtD,MAAM,CAAC,CAC1BqF,MAAM,EAAE,CACZ,CAAC1E,SAAS,CAAC4C,QAAQ,IAAG;QAAA,IAAAmC,kBAAA;QACrB,IAAInC,QAAQ,CAACxB,KAAK,EAAE;UAClB;QACF;QAEA,MAAM9B,UAAU,GAAG,IAAIc,IAAI,CAACwC,QAAQ,CAACE,IAAI,CAACG,WAAW,CAAC;QACtD,MAAMU,UAAU,GAAGrE,UAAU,GAAGa,KAAK;QAErC,MAAME,QAAQ,GAAG,IAAID,IAAI,EAAE,CAACE,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAEvD,MAAMyE,eAAe,GAAG,EAAAD,kBAAA,OAAI,CAACpH,YAAY,cAAAoH,kBAAA,uBAAjBA,kBAAA,CAAmB/D,YAAY,MAAKX,QAAQ;QAEpE,IAAIsD,UAAU,EAAE;UACd,IAAIgB,mBAAmB,EAAE;YACvB,IAAI,CAAC7G,UAAW,CAACQ,uBAAuB,GAAG,CAAC,IAAI,CAACR,UAAW,CAACQ,uBAAuB,IAAI,CAAC,IAAI,CAAC;UAChG,CAAC,MAAM;YACL,IAAI,IAAI,CAACR,UAAW,CAACQ,uBAAuB,IAAI,IAAI,CAACR,UAAW,CAACQ,uBAAuB,GAAG,CAAC,EAAE;cAC5F,IAAI,CAACR,UAAW,CAACQ,uBAAuB,EAAE;YAC5C;UACF;QACF,CAAC,MAAM,CACP;MAEF,CAAC,CAAC;IACJ;IAEA,IAAI,CAACW,qBAAqB,CAACgG,sBAAsB,CAAC,IAAI,CAACtH,YAAY,CAACgD,EAAE,EAAE,IAAI,CAACvB,OAAO,CAAC,CAACW,IAAI,CACxFvD,IAAI,CAAC,CAAC,CAAC,CACR,CAACwD,SAAS,CAAC;MACVC,IAAI,EAAGiF,aAAa,IAAI;QAAA,IAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA;QAEtBxB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE;UAClDwB,KAAK,EAAE;YACL1H,SAAS,GAAAuH,mBAAA,GAAE,IAAI,CAACxH,YAAY,cAAAwH,mBAAA,uBAAjBA,mBAAA,CAAmBvH,SAAS;YACvCuE,cAAc,GAAAiD,mBAAA,GAAE,IAAI,CAACzH,YAAY,cAAAyH,mBAAA,uBAAjBA,mBAAA,CAAmBjD,cAAc;YACjDnB,YAAY,GAAAqE,mBAAA,GAAE,IAAI,CAAC1H,YAAY,cAAA0H,mBAAA,uBAAjBA,mBAAA,CAAmBrE;WAClC;UACDuE,MAAM,EAAE;YACN3H,SAAS,EAAEsH,aAAa,CAACtH,SAAS;YAClCuE,cAAc,EAAE+C,aAAa,CAAC/C,cAAc;YAC5CnB,YAAY,EAAEkE,aAAa,CAAClE;;SAE/B,CAAC;QAEF,IAAI,CAACrD,YAAY,GAAGuH,aAAa;QAEjC,IAAI,IAAI,CAACpH,UAAU,EAAE;UACnB,MAAM0H,cAAc,GAAG,IAAI,CAAC1H,UAAU,CAACQ,uBAAuB,IAAI,CAAC;UACnE,MAAMmH,aAAa,GAAG,IAAI,CAAC3H,UAAU,CAACS,sBAAsB,IAAI,CAAC;UACjE,MAAMmH,YAAY,GAAGF,cAAc,KAAKC,aAAa,IAAIA,aAAa,GAAG,CAAC;UAE1E,MAAME,YAAY,GAAG,IAAI,CAAC7H,UAAU,CAACF,SAAS;UAG9C,IAAI8H,YAAY,IAAI,CAACC,YAAY,EAAE;YACjC,IAAI,CAAC7H,UAAU,CAACU,MAAM,IAAI,CAAC;YAC3B,IAAI,CAACV,UAAU,CAACF,SAAS,GAAG,IAAI;UAClC,CAAC,MAAM,IAAI,CAAC8H,YAAY,IAAIC,YAAY,EAAE;YACxC,IAAI,CAAC7H,UAAU,CAACU,MAAM,GAAGoH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC/H,UAAU,CAACU,MAAM,GAAG,CAAC,CAAC;YAChE,IAAI,CAACV,UAAU,CAACF,SAAS,GAAG,KAAK;UACnC;QACF;QAEA,IAAI,CAACL,iBAAiB,GAAG,KAAK;MAChC,CAAC;MACD6D,KAAK,EAAGA,KAAK,IAAI;QAAA,IAAA0E,mBAAA;QAEf,MAAMC,mBAAmB,GAAG,EAAAD,mBAAA,OAAI,CAACnI,YAAY,cAAAmI,mBAAA,uBAAjBA,mBAAA,CAAmB9E,YAAY,KAAI,EAAE;QAEjE,IAAI,IAAI,CAACrD,YAAY,EAAE;UACrB,IAAI,CAACA,YAAY,CAACC,SAAS,GAAG,CAAC+G,mBAAmB;UAClD,IAAI,CAAChH,YAAY,CAACwE,cAAc,GAAG,CAACwC,mBAAmB,GAAG,IAAI,CAAC7G,UAAW,CAACC,aAAa,CAACM,UAAU,GAAG,CAAC;UACvG,IAAI,CAACV,YAAY,CAACqD,YAAY,GAAG+E,mBAAmB;QACtD;QAEA,IAAI,IAAI,CAACjI,UAAU,EAAE;UACnB,MAAMqC,KAAK,GAAG,IAAIC,IAAI,EAAE;UACxBD,KAAK,CAACoB,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAE1B9E,IAAI,CACF,IAAI,CAAC0C,eAAe,CAAC8B,SAAS,EAAE,CAC7BxE,IAAI,CAAC,eAAe,CAAC,CACrBiG,MAAM,CAAC,aAAa,CAAC,CACrBC,EAAE,CAAC,UAAU,EAAE,IAAI,CAACvD,OAAO,CAAC,CAC5BuD,EAAE,CAAC,SAAS,EAAE,IAAI,CAACtD,MAAM,CAAC,CAC1BqF,MAAM,EAAE,CACZ,CAAC1E,SAAS,CAAC4C,QAAQ,IAAG;YACrB,IAAIA,QAAQ,CAACxB,KAAK,EAAE;YAEpB,MAAM9B,UAAU,GAAG,IAAIc,IAAI,CAACwC,QAAQ,CAACE,IAAI,CAACG,WAAW,CAAC;YACtD,MAAMU,UAAU,GAAGrE,UAAU,GAAGa,KAAK;YAErC,MAAME,QAAQ,GAAG,IAAID,IAAI,EAAE,CAACE,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACvD,MAAMyE,eAAe,GAAGe,mBAAmB,KAAK1F,QAAQ;YAExD,IAAIsD,UAAU,IAAI,IAAI,CAAC7F,UAAU,EAAE;cACjC,IAAI,CAAC6G,mBAAmB,IAAIK,eAAe,EAAE;gBAC3C,IAAI,CAAClH,UAAU,CAACQ,uBAAuB,GAAG,CAAC,IAAI,CAACR,UAAU,CAACQ,uBAAuB,IAAI,CAAC,IAAI,CAAC;cAC9F,CAAC,MAAM,IAAIqG,mBAAmB,IAAI,CAACK,eAAe,EAAE;gBAClD,IAAI,IAAI,CAAClH,UAAU,CAACQ,uBAAuB,IAAI,IAAI,CAACR,UAAU,CAACQ,uBAAuB,GAAG,CAAC,EAAE;kBAC1F,IAAI,CAACR,UAAU,CAACQ,uBAAuB,EAAE;gBAC3C;cACF;YACF;UACF,CAAC,CAAC;QACJ;QAEA,IAAI,CAACf,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACyI,cAAc,CAAC,8CAA8C,CAAC;MACrE;KACD,CAAC;EACJ;EAEMA,cAAcA,CAACC,OAAe;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAClC,MAAMC,KAAK,SAASF,KAAI,CAAChH,eAAe,CAACmH,MAAM,CAAC;QAC9CJ,OAAO,EAAEA,OAAO;QAChBK,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;OACR,CAAC;MACF,MAAMJ,KAAK,CAACK,OAAO,EAAE;IAAC;EACxB;EAEAC,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC5I,UAAU,IAAI,CAAC,IAAI,CAACA,UAAU,CAACS,sBAAsB,IAAI,IAAI,CAACT,UAAU,CAACS,sBAAsB,KAAK,CAAC,EAAE;MAC/G,OAAO,CAAC;IACV;IAEA,OAAO,CAAC,IAAI,CAACT,UAAU,CAACQ,uBAAuB,IAAI,CAAC,IAAI,IAAI,CAACR,UAAU,CAACS,sBAAsB,GAAG,GAAG;EACtG;EAEAoI,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAAC7I,UAAU,IAAI,CAAC,IAAI,CAACA,UAAU,CAACS,sBAAsB,EAAE;MAC/D,OAAO,YAAY;IACrB;IAEA,OAAO,GAAG,IAAI,CAACT,UAAU,CAACQ,uBAAuB,IAAI,CAAC,IAAI,IAAI,CAACR,UAAU,CAACS,sBAAsB,YAAY;EAC9G;;2BAhdWQ,uBAAuB;;mCAAvBA,wBAAuB,EAAArC,EAAA,CAAAkK,iBAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAApK,EAAA,CAAAkK,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAtK,EAAA,CAAAkK,iBAAA,CAAAK,EAAA,CAAAC,eAAA;AAAA;;QAAvBnI,wBAAuB;EAAAoI,SAAA;EAAAC,MAAA;IAAAhI,OAAA;IAAAC,MAAA;IAAAC,UAAA;IAAAC,OAAA;IAAAC,gBAAA;IAAAC,YAAA;EAAA;EAAA4H,QAAA,GAAA3K,EAAA,CAAA4K,oBAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCjBpClL,EAAA,CAAA+B,UAAA,IAAAqJ,0CAAA,qBAA8E;;;MAA3CpL,EAAA,CAAAmC,UAAA,SAAAgJ,GAAA,CAAArI,gBAAA,IAAAqI,GAAA,CAAAlI,eAAA,CAAyC;;;iBDehErD,YAAY,EAAAyL,EAAA,CAAAC,IAAA,EAAEzL,WAAW,EAAAwK,EAAA,CAAAkB,UAAA;EAAAC,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}