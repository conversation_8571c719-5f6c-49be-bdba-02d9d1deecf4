﻿import { Injectable, inject } from '@angular/core';
import { Activity, ActivityType, DayTracking } from '../models/activity.model';
import { Observable, from, of, catchError, map, switchMap } from 'rxjs';
import { SupabaseService } from './supabase.service';

@Injectable({
  providedIn: 'root'
})
export class TimeTrackerUnifiedService {
  private supabaseService = inject(SupabaseService);

  getActivityTypes(): Observable<ActivityType[]> {

    return from(
      this.supabaseService.getClient()
        .from('activity_types')
        .select('*')
        .eq('is_active', true)
        .order('order', { ascending: true })
    ).pipe(
      map(response => {
        if (response.error) {
          return [];
        }

        return response.data as ActivityType[];
      }),
      catchError(error => {
        return of([]);
      })
    );
  }

  getDayTracking(userId: string, date: string): Observable<DayTracking> {

    if (!userId || !date) {
      return of({ id: '', user_id: userId, date: date });
    }

    return from(
      this.supabaseService.getClient()
        .from('day_tracking')
        .select('*')
        .eq('user_id', userId)
        .eq('date', date)
        .single()
    ).pipe(
      switchMap(response => {
        if (response.error && response.error.code === 'PGRST116') { 
          return from(
            this.supabaseService.getClient()
              .from('day_tracking')
              .insert({ user_id: userId, date: date })
              .select()
              .single()
          ).pipe(
            map(insertResponse => {
              if (insertResponse.error) {
                return { id: '', user_id: userId, date: date };
              }

              return insertResponse.data as DayTracking;
            }),
            catchError(error => {
              return of({ id: '', user_id: userId, date: date });
            })
          );
        }

        if (response.error) {
          return of({ id: '', user_id: userId, date: date });
        }

        return of(response.data as DayTracking);
      }),
      catchError(error => {
        return of({ id: '', user_id: userId, date: date });
      })
    );
  }

  getActivitiesForDayTracking(dayTrackingId: string): Observable<Activity[]> {

    if (!dayTrackingId) {
      return of([]);
    }

    return from(
      this.supabaseService.getClient()
        .from('activities')
        .select('*')
        .eq('day_tracking_id', dayTrackingId)
        .order('name')
    ).pipe(
      map(response => {
        if (response.error) {
          return [];
        }

        return response.data as Activity[];
      }),
      catchError(error => {
        return of([]);
      })
    );
  }

  getActivities(userId: string, date: string): Observable<Activity[]> {

    if (!userId || !date) {
      return of([]);
    }

    return this.getDayTracking(userId, date).pipe(
      switchMap(dayTracking => {
        if (!dayTracking.id) {
          return of([]);
        }

        return this.getActivitiesForDayTracking(dayTracking.id);
      })
    );
  }

  async createActivity(
    userId: string,
    date: string,
    name: string,
    emoji: string,
    hours: number,
    minutes: number,
    isCustom: boolean
  ): Promise<{ id: string, total_hours: string, remaining_hours: string }> {

    try {
      const { data: dayTracking, error: dayTrackingError } = await this.supabaseService.getClient()
        .from('day_tracking')
        .select('id')
        .eq('user_id', userId)
        .eq('date', date)
        .single();

      let dayTrackingId: string;

      if (dayTrackingError && dayTrackingError.code === 'PGRST116') { 
        const { data: newDayTracking, error: newDayTrackingError } = await this.supabaseService.getClient()
          .from('day_tracking')
          .insert({ user_id: userId, date: date })
          .select('id')
          .single();

        if (newDayTrackingError) {
          return Promise.reject(newDayTrackingError);
        }

        dayTrackingId = newDayTracking.id;
      } else if (dayTrackingError) {
        return Promise.reject(dayTrackingError);
      } else {
        dayTrackingId = dayTracking.id;
      }

      const { data: newActivity, error: activityError } = await this.supabaseService.getClient()
        .from('activities')
        .insert({
          day_tracking_id: dayTrackingId,
          name: name,
          emoji: emoji,
          hours: hours,
          minutes: minutes,
          is_custom: isCustom
        })
        .select('id')
        .single();

      if (activityError) {
        return Promise.reject(activityError);
      }


      const { data: activities, error: activitiesError } = await this.supabaseService.getClient()
        .from('activities')
        .select('hours, minutes')
        .eq('day_tracking_id', dayTrackingId);

      if (activitiesError) {
        return Promise.reject(activitiesError);
      }

      const totalMinutes = activities.reduce((total, act) => {
        return total + (act.hours * 60) + act.minutes;
      }, 0);

      const totalHours = totalMinutes / 60;
      const remainingHours = Math.max(0, 24 - totalHours);


      return {
        id: newActivity.id,
        total_hours: totalHours.toFixed(1),
        remaining_hours: remainingHours.toFixed(1)
      };
    } catch (error) {
      return Promise.reject(error);
    }
  }

  async updateActivity(activityId: string, hours: number, minutes: number): Promise<{ total_hours: string, remaining_hours: string }> {

    try {
      const { data: activity, error: activityError } = await this.supabaseService.getClient()
        .from('activities')
        .select('day_tracking_id')
        .eq('id', activityId)
        .single();

      if (activityError) {
        return Promise.reject(activityError);
      }

      const dayTrackingId = activity.day_tracking_id;

      const { error: updateError } = await this.supabaseService.getClient()
        .from('activities')
        .update({ hours, minutes })
        .eq('id', activityId);

      if (updateError) {
        return Promise.reject(updateError);
      }


      const { data: activities, error: activitiesError } = await this.supabaseService.getClient()
        .from('activities')
        .select('hours, minutes')
        .eq('day_tracking_id', dayTrackingId);

      if (activitiesError) {
        return Promise.reject(activitiesError);
      }

      const totalMinutes = activities.reduce((total, act) => {
        return total + (act.hours * 60) + act.minutes;
      }, 0);

      const totalHours = totalMinutes / 60;
      const remainingHours = Math.max(0, 24 - totalHours);


      return {
        total_hours: totalHours.toFixed(1),
        remaining_hours: remainingHours.toFixed(1)
      };
    } catch (error) {
      return Promise.reject(error);
    }
  }

  async deleteActivity(activityId: string): Promise<{ total_hours: string, remaining_hours: string }> {

    try {
      const { data: activity, error: activityError } = await this.supabaseService.getClient()
        .from('activities')
        .select('day_tracking_id')
        .eq('id', activityId)
        .single();

      if (activityError) {
        return Promise.reject(activityError);
      }

      const dayTrackingId = activity.day_tracking_id;

      const { error: deleteError } = await this.supabaseService.getClient()
        .from('activities')
        .delete()
        .eq('id', activityId);

      if (deleteError) {
        return Promise.reject(deleteError);
      }


      const { data: activities, error: activitiesError } = await this.supabaseService.getClient()
        .from('activities')
        .select('hours, minutes')
        .eq('day_tracking_id', dayTrackingId);

      if (activitiesError) {
        return Promise.reject(activitiesError);
      }

      const totalMinutes = activities.reduce((total, act) => {
        return total + (act.hours * 60) + act.minutes;
      }, 0);

      const totalHours = totalMinutes / 60;
      const remainingHours = Math.max(0, 24 - totalHours);


      return {
        total_hours: totalHours.toFixed(1),
        remaining_hours: remainingHours.toFixed(1)
      };
    } catch (error) {
      return Promise.reject(error);
    }
  }

  async getTotalTime(dayTrackingId: string): Promise<{ total_hours: number, remaining_hours: number }> {

    try {
      const { data: activities, error: activitiesError } = await this.supabaseService.getClient()
        .from('activities')
        .select('hours, minutes')
        .eq('day_tracking_id', dayTrackingId);

      if (activitiesError) {
        return Promise.reject(activitiesError);
      }

      const totalMinutes = activities.reduce((total, act) => {
        return total + (act.hours * 60) + act.minutes;
      }, 0);

      const totalHours = totalMinutes / 60;
      const remainingHours = Math.max(0, 24 - totalHours);

      return {
        total_hours: totalHours,
        remaining_hours: remainingHours
      };
    } catch (error) {
      return Promise.reject(error);
    }
  }

  async createActivityType(activityType: Omit<ActivityType, 'id'>): Promise<string> {

    try {
      const { data, error } = await this.supabaseService.getClient()
        .from('activity_types')
        .insert(activityType)
        .select('id')
        .single();

      if (error) {
        return Promise.reject(error);
      }

      return data.id;
    } catch (error) {
      return Promise.reject(error);
    }
  }
}
