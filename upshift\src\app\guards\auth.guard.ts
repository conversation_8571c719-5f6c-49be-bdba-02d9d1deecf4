﻿import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { SupabaseService } from '../services/supabase.service';
import { UserService } from '../services/user.service';
import { Preferences } from '@capacitor/preferences';
import { from, of } from 'rxjs';
import { switchMap, catchError, take, map } from 'rxjs/operators';

export const AuthGuard: CanActivateFn = (route, state) => {
  const supabase = inject(SupabaseService);
  const userService = inject(UserService);
  const router = inject(Router);

  return supabase.currentUser$.pipe(
    take(1),
    switchMap(user => {
      if (!user) {
        localStorage.setItem('lastAttemptedUrl', state.url);
        localStorage.setItem('redirectUrl', state.url);
        return of(router.createUrlTree(['/signup']));
      }

      return from(Preferences.get({ key: 'onboarding_complete' })).pipe(
        switchMap(onboarding => {
          if (!onboarding?.value) {
            return of(router.createUrlTree(['/onboarding']));
          }

          return userService.getUserById(user.id).pipe(
            take(1),
            switchMap(userData => {
              if (!userData) {
                localStorage.setItem('redirectUrl', state.url);
                return of(router.createUrlTree(['/signup']));
              }

              if (!userData.username) {
                localStorage.setItem('redirectUrl', state.url);
                return of(router.createUrlTree(['/register']));
              }

              const endDate = userData.end_of_current_plan ? new Date(userData.end_of_current_plan) : null;
              const isValidPlan = endDate && endDate > new Date();
              const isHome = state.url.includes('/tabs/home');

              if (!isValidPlan && !isHome) {
                localStorage.setItem('redirectUrl', state.url);
                return of(router.createUrlTree(['/pricing']));
              }
              return of(true);
            }),
            catchError(() => of(router.createUrlTree(['/signup'])))
          );
        }),
        catchError(() => of(router.createUrlTree(['/onboarding'])))
      );
    }),
    catchError(() => of(router.createUrlTree(['/signup'])))
  );
};
