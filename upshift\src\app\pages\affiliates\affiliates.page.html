﻿<ion-header class="ion-no-border">
  <ion-toolbar>
    <ion-buttons slot="start">
      <div class="logo">
        <img src="assets/images/upshift_icon_mini.svg" alt="Upshift">
        <span>Upshift</span>
      </div>
    </ion-buttons>
    <ion-title>Affiliate Rewards</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div class="page-container">
    <div class="header-section">
      <div class="header-content">
        <h1>Affiliate Rewards</h1>
        <p class="subtitle">Invite friends and earn exclusive rewards</p>
      </div>

      <div class="affiliate-counter">
        <div class="counter-value">{{ userAffiliateCount }}</div>
        <div class="counter-label">Your Affiliates</div>
      </div>

      <div class="header-description">
        <p>Invite friends to join Upshift and unlock valuable rewards. The more friends you invite, the better rewards you can claim!</p>
      </div>
    </div>

    <div *ngIf="isLoading" class="loading-container">
      <ion-spinner name="crescent"></ion-spinner>
      <p>Loading offers...</p>
    </div>

    <div *ngIf="!isLoading && affiliateOffers.length === 0" class="empty-state">
      <ion-icon name="gift-outline" size="large"></ion-icon>
      <p>No affiliate offers available at the moment.</p>
      <p class="empty-subtitle">Check back later for exciting rewards!</p>
    </div>

    <div class="rewards-section" *ngIf="!isLoading && affiliateOffers.length > 0">
      <div class="section-header">
        <h2>Available Rewards</h2>
        <p class="section-subtitle">Claim these rewards when you reach the required number of affiliates</p>
      </div>

      <div class="rewards-grid">
        <ion-card class="reward-card"
             *ngFor="let offer of affiliateOffers"
             [class.claimed]="isOfferClaimed(offer.id)"
             [class.available]="canClaimOffer(offer)"
             (click)="viewOfferDetails(offer)">
          <div class="reward-status" *ngIf="isOfferClaimed(offer.id)">
            <ion-icon name="checkmark-circle"></ion-icon>
            <span>Claimed</span>
          </div>
          <div class="reward-status available" *ngIf="canClaimOffer(offer) && !isOfferClaimed(offer.id)">
            <ion-icon name="gift"></ion-icon>
            <span>Available</span>
          </div>

          <div class="reward-image" *ngIf="offer.image_cover">
            <img [src]="offer.image_cover" alt="{{ offer.name }}">
          </div>
          <div class="reward-image placeholder" *ngIf="!offer.image_cover">
            <ion-icon name="gift-outline"></ion-icon>
          </div>

          <ion-card-content>
            <h3 class="reward-title">{{ offer.name }}</h3>
            <p class="reward-description">{{ offer.description }}</p>

            <div class="reward-details">
              <div class="reward-value">
                <ion-icon name="cash-outline"></ion-icon>
                <span>{{ formatPrice(offer.price_value) }}</span>
              </div>
              <div class="reward-requirement" [class.met]="userAffiliateCount >= offer.number_of_invites_required">
                <ion-icon name="people-outline"></ion-icon>
                <span>{{ userAffiliateCount }}/{{ offer.number_of_invites_required }}</span>
              </div>
            </div>

            <div class="reward-progress">
              <div class="progress-bar">
                <div class="progress-fill"
                     [style.width]="(userAffiliateCount / offer.number_of_invites_required) * 100 + '%'"
                     [class.complete]="userAffiliateCount >= offer.number_of_invites_required"></div>
              </div>
            </div>

            <div class="reward-action">
              <ion-button expand="block" fill="clear" size="small">
                View Details
                <ion-icon name="chevron-forward-outline" slot="end"></ion-icon>
              </ion-button>
            </div>
          </ion-card-content>
        </ion-card>
      </div>
    </div>
  </div>

  <!-- Inline modal for offer details -->
  <ion-modal [isOpen]="showOfferDetails" [backdropDismiss]="true" [cssClass]="'affiliate-offer-modal'" (ionModalDidDismiss)="closeOfferDetails()">
    <ng-template>
      <ion-header class="ion-no-border">
        <ion-toolbar>
          <ion-title>Reward Details</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="closeOfferDetails()">
              <ion-icon name="close-outline" size="large"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>

      <ion-content class="ion-padding">
        <div class="modal-content" *ngIf="selectedOffer">
          <div class="reward-detail-header">
            <div class="reward-detail-image" *ngIf="selectedOffer.image_cover">
              <img [src]="selectedOffer.image_cover" [alt]="selectedOffer.name">
            </div>
            <div class="reward-detail-image placeholder" *ngIf="!selectedOffer.image_cover">
              <ion-icon name="gift-outline" size="large"></ion-icon>
            </div>

            <div class="reward-detail-title">
              <h1>{{ selectedOffer.name }}</h1>
              <div class="reward-detail-value">
                <ion-icon name="cash-outline"></ion-icon>
                <span>You save {{ formatPrice(selectedOffer.price_value, true) }}</span>
              </div>
            </div>
          </div>

          <div class="reward-detail-section">
            <h3>Description</h3>
            <p>{{ selectedOffer.description }}</p>
          </div>

          <div class="reward-detail-section">
            <h3>Requirements</h3>
            <div class="progress-container">
              <div class="progress-bar">
                <div class="progress-fill"
                     [style.width]="((userAffiliateCount / selectedOffer.number_of_invites_required) * 100) + '%'"
                     [class.complete]="userAffiliateCount >= selectedOffer.number_of_invites_required"></div>
              </div>
              <div class="progress-text"
                   [class.complete]="userAffiliateCount >= selectedOffer.number_of_invites_required">
                {{ userAffiliateCount }}/{{ selectedOffer.number_of_invites_required }} affiliates
              </div>
            </div>

            <div *ngIf="!isOfferClaimed(selectedOffer.id) && userAffiliateCount < selectedOffer.number_of_invites_required"
                 class="requirement-message">
              <ion-icon name="information-circle-outline"></ion-icon>
              <span>You need {{ selectedOffer.number_of_invites_required - userAffiliateCount }} more affiliate(s) to claim this reward</span>
            </div>
          </div>

          <div class="reward-detail-section" *ngIf="isOfferClaimed(selectedOffer.id)">
            <div class="claimed-badge">
              <ion-icon name="checkmark-circle"></ion-icon>
              <span>Reward Claimed</span>
            </div>
          </div>

          <div class="reward-detail-action">
            <ion-button expand="block"
                      [disabled]="!canClaimOffer(selectedOffer)"
                      (click)="claimOffer(selectedOffer)"
                      [color]="canClaimOffer(selectedOffer) ? 'primary' : 'medium'">
              <ion-icon [name]="isOfferClaimed(selectedOffer.id) ? 'checkmark-circle-outline' : 'gift-outline'" slot="start"></ion-icon>
              {{ isOfferClaimed(selectedOffer.id) ? 'Already Claimed' : 'Claim Reward' }}
            </ion-button>
          </div>
        </div>
      </ion-content>
    </ng-template>
  </ion-modal>
</ion-content>
