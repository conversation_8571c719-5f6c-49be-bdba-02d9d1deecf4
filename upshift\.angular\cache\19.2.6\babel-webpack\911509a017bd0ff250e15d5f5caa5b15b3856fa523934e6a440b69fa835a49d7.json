{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _GroupSettingsPage;\nimport { inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { ActivatedRoute, Router, RouterModule } from '@angular/router';\nimport { take, debounceTime, distinctUntilChanged, Subject, map } from 'rxjs';\nimport { SupabaseService } from '../../../services/supabase.service';\nimport { GroupService } from '../../../services/group.service';\nimport { UserService } from '../../../services/user.service';\nimport { FriendService } from '../../../services/friend.service';\nimport { XpService, EntityType } from '../../../services/xp.service';\nimport { GroupMember } from '../../../models/supabase.models';\nimport { EmojiInputDirective } from '../../../directives/emoji-input.directive';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/router\";\nconst _c0 = a0 => [\"/groups\", a0];\nfunction GroupSettingsPage_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Level \", ctx_r0.group.level, \"\");\n  }\n}\nfunction GroupSettingsPage_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.successMessage);\n  }\n}\nfunction GroupSettingsPage_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.errorMessage);\n  }\n}\nfunction GroupSettingsPage_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, GroupSettingsPage_div_9_div_1_Template, 2, 1, \"div\", 19)(2, GroupSettingsPage_div_9_div_2_Template, 2, 1, \"div\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.successMessage);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.errorMessage);\n  }\n}\nfunction GroupSettingsPage_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24)(2, \"span\", 25);\n    i0.ɵɵtext(3, \"\\uD83D\\uDCCA\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h2\");\n    i0.ɵɵtext(5, \"Group XP\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 26)(7, \"div\", 27)(8, \"div\", 28)(9, \"div\", 29);\n    i0.ɵɵtext(10, \"\\uD83D\\uDCAA\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 30);\n    i0.ɵɵtext(12, \"Strength\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 31)(14, \"div\", 32);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 33);\n    i0.ɵɵelement(17, \"div\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 27)(19, \"div\", 28)(20, \"div\", 29);\n    i0.ɵɵtext(21, \"\\uD83D\\uDCB0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 30);\n    i0.ɵɵtext(23, \"Money\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 31)(25, \"div\", 32);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 33);\n    i0.ɵɵelement(28, \"div\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 27)(30, \"div\", 28)(31, \"div\", 29);\n    i0.ɵɵtext(32, \"\\u2764\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 30);\n    i0.ɵɵtext(34, \"Health\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 31)(36, \"div\", 32);\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 33);\n    i0.ɵɵelement(39, \"div\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(40, \"div\", 27)(41, \"div\", 28)(42, \"div\", 29);\n    i0.ɵɵtext(43, \"\\uD83E\\uDDE0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\", 30);\n    i0.ɵɵtext(45, \"Knowledge\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 31)(47, \"div\", 32);\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 33);\n    i0.ɵɵelement(50, \"div\", 34);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r0.group.strength_xp, \"/\", ctx_r0.requiredXp, \" XP\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r0.getProgressPercentage(ctx_r0.group.strength_xp), \"%\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r0.group.money_xp, \"/\", ctx_r0.requiredXp, \" XP\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r0.getProgressPercentage(ctx_r0.group.money_xp), \"%\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r0.group.health_xp, \"/\", ctx_r0.requiredXp, \" XP\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r0.getProgressPercentage(ctx_r0.group.health_xp), \"%\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r0.group.knowledge_xp, \"/\", ctx_r0.requiredXp, \" XP\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r0.getProgressPercentage(ctx_r0.group.knowledge_xp), \"%\");\n  }\n}\nfunction GroupSettingsPage_div_11_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nameError);\n  }\n}\nfunction GroupSettingsPage_div_11_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtext(1, \"Checking availability...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GroupSettingsPage_div_11_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtext(1, \"Group name is available!\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GroupSettingsPage_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24)(2, \"span\", 25);\n    i0.ɵɵtext(3, \"\\u270F\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h2\");\n    i0.ɵɵtext(5, \"Group Information\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 35)(7, \"form\", 36);\n    i0.ɵɵlistener(\"ngSubmit\", function GroupSettingsPage_div_11_Template_form_ngSubmit_7_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.updateGroupInfo());\n    });\n    i0.ɵɵelementStart(8, \"div\", 37)(9, \"div\", 38)(10, \"input\", 39);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GroupSettingsPage_div_11_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.groupInfo.emoji, $event) || (ctx_r0.groupInfo.emoji = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 40)(12, \"input\", 41);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GroupSettingsPage_div_11_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.groupInfo.name, $event) || (ctx_r0.groupInfo.name = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function GroupSettingsPage_div_11_Template_input_input_12_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.checkGroupName());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, GroupSettingsPage_div_11_div_13_Template, 2, 1, \"div\", 42)(14, GroupSettingsPage_div_11_div_14_Template, 2, 0, \"div\", 43)(15, GroupSettingsPage_div_11_div_15_Template, 2, 0, \"div\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"button\", 45);\n    i0.ɵɵtext(17, \"Save\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.groupInfo.emoji);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.groupInfo.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.nameError);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.nameChecking);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.nameAvailable);\n  }\n}\nfunction GroupSettingsPage_div_12_div_7_ng_container_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const member_r4 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(member_r4.nickname.charAt(0).toUpperCase());\n  }\n}\nfunction GroupSettingsPage_div_12_div_7_ng_container_2_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 64);\n  }\n  if (rf & 2) {\n    const member_r4 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"src\", member_r4.profile_picture, i0.ɵɵsanitizeUrl)(\"alt\", member_r4.nickname);\n  }\n}\nfunction GroupSettingsPage_div_12_div_7_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 59);\n    i0.ɵɵlistener(\"click\", function GroupSettingsPage_div_12_div_7_ng_container_2_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const member_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.navigateToProfile(member_r4.user_id));\n    });\n    i0.ɵɵelementStart(2, \"div\", 60);\n    i0.ɵɵtemplate(3, GroupSettingsPage_div_12_div_7_ng_container_2_div_3_Template, 2, 1, \"div\", 61)(4, GroupSettingsPage_div_12_div_7_ng_container_2_img_4_Template, 1, 2, \"img\", 62);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const member_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !member_r4.profile_picture);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", member_r4.profile_picture);\n  }\n}\nfunction GroupSettingsPage_div_12_div_7_ng_container_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const member_r4 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(member_r4.nickname.charAt(0).toUpperCase());\n  }\n}\nfunction GroupSettingsPage_div_12_div_7_ng_container_3_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 64);\n  }\n  if (rf & 2) {\n    const member_r4 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"src\", member_r4.profile_picture, i0.ɵɵsanitizeUrl)(\"alt\", member_r4.nickname);\n  }\n}\nfunction GroupSettingsPage_div_12_div_7_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 60);\n    i0.ɵɵtemplate(2, GroupSettingsPage_div_12_div_7_ng_container_3_div_2_Template, 2, 1, \"div\", 61)(3, GroupSettingsPage_div_12_div_7_ng_container_3_img_3_Template, 1, 2, \"img\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const member_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !member_r4.profile_picture);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", member_r4.profile_picture);\n  }\n}\nfunction GroupSettingsPage_div_12_div_7_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 65);\n    i0.ɵɵlistener(\"click\", function GroupSettingsPage_div_12_div_7_ng_container_5_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const member_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.navigateToProfile(member_r4.user_id));\n    });\n    i0.ɵɵelementStart(2, \"span\", 66);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const member_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(member_r4.nickname);\n  }\n}\nfunction GroupSettingsPage_div_12_div_7_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 66);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const member_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(member_r4.nickname);\n  }\n}\nfunction GroupSettingsPage_div_12_div_7_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 67);\n    i0.ɵɵtext(1, \"Admin\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GroupSettingsPage_div_12_div_7_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function GroupSettingsPage_div_12_div_7_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.leaveGroup());\n    });\n    i0.ɵɵtext(1, \"Leave Group\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GroupSettingsPage_div_12_div_7_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function GroupSettingsPage_div_12_div_7_ng_container_13_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const member_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.editMemberNickname(member_r4));\n    });\n    i0.ɵɵtext(2, \"Edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function GroupSettingsPage_div_12_div_7_ng_container_13_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const member_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.removeMember(member_r4));\n    });\n    i0.ɵɵtext(4, \"Remove\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction GroupSettingsPage_div_12_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 52);\n    i0.ɵɵtemplate(2, GroupSettingsPage_div_12_div_7_ng_container_2_Template, 5, 2, \"ng-container\", 53)(3, GroupSettingsPage_div_12_div_7_ng_container_3_Template, 4, 2, \"ng-container\", 53);\n    i0.ɵɵelementStart(4, \"div\", 54);\n    i0.ɵɵtemplate(5, GroupSettingsPage_div_12_div_7_ng_container_5_Template, 4, 1, \"ng-container\", 53)(6, GroupSettingsPage_div_12_div_7_ng_container_6_Template, 3, 1, \"ng-container\", 53)(7, GroupSettingsPage_div_12_div_7_span_7_Template, 2, 0, \"span\", 55);\n    i0.ɵɵelementStart(8, \"span\", 56);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 57);\n    i0.ɵɵtemplate(12, GroupSettingsPage_div_12_div_7_button_12_Template, 2, 0, \"button\", 58)(13, GroupSettingsPage_div_12_div_7_ng_container_13_Template, 5, 0, \"ng-container\", 53);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const member_r4 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", member_r4.user_id !== ctx_r0.userId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", member_r4.user_id === ctx_r0.userId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", member_r4.user_id !== ctx_r0.userId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", member_r4.user_id === ctx_r0.userId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", member_r4.is_admin);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Joined \", i0.ɵɵpipeBind2(10, 8, member_r4.joined_date, \"MMM d, y\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", member_r4.user_id === ctx_r0.userId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isAdmin && member_r4.user_id !== ctx_r0.userId);\n  }\n}\nfunction GroupSettingsPage_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24)(2, \"span\", 25);\n    i0.ɵɵtext(3, \"\\uD83D\\uDC65\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h2\");\n    i0.ɵɵtext(5, \"Members\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 49);\n    i0.ɵɵtemplate(7, GroupSettingsPage_div_12_div_7_Template, 14, 11, \"div\", 50);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.members);\n  }\n}\nfunction GroupSettingsPage_div_13_div_12_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 87);\n    i0.ɵɵlistener(\"click\", function GroupSettingsPage_div_13_div_12_div_8_div_1_Template_div_click_0_listener() {\n      const suggestion_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r0.selectUsername(suggestion_r11));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const suggestion_r11 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", suggestion_r11, \" \");\n  }\n}\nfunction GroupSettingsPage_div_13_div_12_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85);\n    i0.ɵɵtemplate(1, GroupSettingsPage_div_13_div_12_div_8_div_1_Template, 2, 1, \"div\", 86);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.usernameSuggestions);\n  }\n}\nfunction GroupSettingsPage_div_13_div_12_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Username should only contain letters, numbers, and @/./+/-/_ characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GroupSettingsPage_div_13_div_12_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Username is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GroupSettingsPage_div_13_div_12_div_9_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" An invitation has already been sent to this user \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GroupSettingsPage_div_13_div_12_div_9_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" You can only invite users from your friends list \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GroupSettingsPage_div_13_div_12_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtemplate(1, GroupSettingsPage_div_13_div_12_div_9_div_1_Template, 2, 0, \"div\", 53)(2, GroupSettingsPage_div_13_div_12_div_9_div_2_Template, 2, 0, \"div\", 53)(3, GroupSettingsPage_div_13_div_12_div_9_div_3_Template, 2, 0, \"div\", 53)(4, GroupSettingsPage_div_13_div_12_div_9_div_4_Template, 2, 0, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const usernameInput_r12 = i0.ɵɵreference(7);\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", usernameInput_r12.errors == null ? null : usernameInput_r12.errors[\"pattern\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", usernameInput_r12.errors == null ? null : usernameInput_r12.errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", usernameInput_r12.valid && ctx_r0.inviteUsername.trim() && ctx_r0.alreadyInvitedUsernames.includes(ctx_r0.inviteUsername.trim()));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", usernameInput_r12.valid && ctx_r0.inviteUsername.trim() && !ctx_r0.alreadyInvitedUsernames.includes(ctx_r0.inviteUsername.trim()) && !ctx_r0.validFriendUsernames.includes(ctx_r0.inviteUsername.trim()));\n  }\n}\nfunction GroupSettingsPage_div_13_div_12_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"div\", 21);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.invitationSentMessage);\n  }\n}\nfunction GroupSettingsPage_div_13_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"p\");\n    i0.ɵɵtext(2, \"Invite your friends to join this group:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"form\", 77, 0);\n    i0.ɵɵlistener(\"ngSubmit\", function GroupSettingsPage_div_13_div_12_Template_form_ngSubmit_3_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.inviteFriend());\n    });\n    i0.ɵɵelementStart(5, \"div\", 78)(6, \"input\", 79, 1);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GroupSettingsPage_div_13_div_12_Template_input_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r0.inviteUsername, $event) || (ctx_r0.inviteUsername = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function GroupSettingsPage_div_13_div_12_Template_input_input_6_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onUsernameInput());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, GroupSettingsPage_div_13_div_12_div_8_Template, 2, 1, \"div\", 80)(9, GroupSettingsPage_div_13_div_12_div_9_Template, 5, 4, \"div\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 82);\n    i0.ɵɵtext(11, \"Invite Friend\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(12, GroupSettingsPage_div_13_div_12_div_12_Template, 3, 1, \"div\", 83);\n    i0.ɵɵelementStart(13, \"div\", 84);\n    i0.ɵɵtext(14, \"Note: You can only invite users from your friends list.\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const inviteForm_r13 = i0.ɵɵreference(4);\n    const usernameInput_r12 = i0.ɵɵreference(7);\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.inviteUsername);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.usernameSuggestions.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", usernameInput_r12.invalid && (usernameInput_r12.dirty || usernameInput_r12.touched) || usernameInput_r12.valid && ctx_r0.inviteUsername.trim() && (ctx_r0.alreadyInvitedUsernames.includes(ctx_r0.inviteUsername.trim()) || !ctx_r0.validFriendUsernames.includes(ctx_r0.inviteUsername.trim())));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", inviteForm_r13.invalid || ctx_r0.inviteUsername.trim() && (ctx_r0.alreadyInvitedUsernames.includes(ctx_r0.inviteUsername.trim()) || !ctx_r0.validFriendUsernames.includes(ctx_r0.inviteUsername.trim())));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.invitationSentMessage);\n  }\n}\nfunction GroupSettingsPage_div_13_div_13_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 95);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 96);\n    i0.ɵɵtext(4, \"This code is valid for 24 hours. Share it with friends to connect.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.invitationCode);\n  }\n}\nfunction GroupSettingsPage_div_13_div_13_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 97);\n    i0.ɵɵlistener(\"click\", function GroupSettingsPage_div_13_div_13_ng_template_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.generateInvitationCode());\n    });\n    i0.ɵɵtext(1, \"Generate Group Code\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GroupSettingsPage_div_13_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90)(1, \"div\", 91)(2, \"div\", 24)(3, \"span\", 25);\n    i0.ɵɵtext(4, \"\\uD83D\\uDD11\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\", 92);\n    i0.ɵɵtext(6, \"Group Code\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Generate a code that anyone can use to join this group. The code will be valid for 24 hours.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 93);\n    i0.ɵɵtemplate(10, GroupSettingsPage_div_13_div_13_ng_container_10_Template, 5, 1, \"ng-container\", 94)(11, GroupSettingsPage_div_13_div_13_ng_template_11_Template, 2, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const generateCodeBlock_r15 = i0.ɵɵreference(12);\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.invitationCode && ctx_r0.codeIsValid)(\"ngIfElse\", generateCodeBlock_r15);\n  }\n}\nfunction GroupSettingsPage_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24)(2, \"span\", 25);\n    i0.ɵɵtext(3, \"\\u2795\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h2\");\n    i0.ɵɵtext(5, \"Invite\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 71)(7, \"div\", 72)(8, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function GroupSettingsPage_div_13_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.setActiveTab(\"friends\"));\n    });\n    i0.ɵɵtext(9, \"Invite Friends\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function GroupSettingsPage_div_13_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.setActiveTab(\"code\"));\n    });\n    i0.ɵɵtext(11, \"Generate Code\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(12, GroupSettingsPage_div_13_div_12_Template, 15, 5, \"div\", 74)(13, GroupSettingsPage_div_13_div_13_Template, 13, 2, \"div\", 75);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵclassProp(\"active\", ctx_r0.activeTab === \"friends\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", ctx_r0.activeTab === \"code\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.activeTab === \"friends\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.activeTab === \"code\");\n  }\n}\nfunction GroupSettingsPage_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24)(2, \"span\", 25);\n    i0.ɵɵtext(3, \"\\u2699\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h2\");\n    i0.ɵɵtext(5, \"Side Quest Settings\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"form\", 98);\n    i0.ɵɵlistener(\"ngSubmit\", function GroupSettingsPage_div_14_Template_form_ngSubmit_6_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.updateSideQuestSettings());\n    });\n    i0.ɵɵelementStart(7, \"div\", 99)(8, \"label\", 100)(9, \"input\", 101);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GroupSettingsPage_div_14_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.group.enable_sidequests, $event) || (ctx_r0.group.enable_sidequests = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"span\", 102);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 103);\n    i0.ɵɵtext(12, \"Enable Daily Side Quests\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 104);\n    i0.ɵɵtext(14, \"Save Settings\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.group.enable_sidequests);\n  }\n}\nfunction GroupSettingsPage_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 105)(1, \"div\", 24)(2, \"span\", 25);\n    i0.ɵɵtext(3, \"\\u26A0\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h2\");\n    i0.ɵɵtext(5, \"Danger Zone\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 106)(7, \"button\", 107);\n    i0.ɵɵlistener(\"click\", function GroupSettingsPage_div_15_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.confirmDeleteGroup());\n    });\n    i0.ɵɵtext(8, \"Delete Group\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 108);\n    i0.ɵɵtext(10, \"This action cannot be undone. All group data will be permanently deleted.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class GroupSettingsPage {\n  constructor() {\n    this.userId = null;\n    this.groupId = null;\n    this.group = null;\n    this.members = [];\n    this.isAdmin = false;\n    this.requiredXp = 0;\n    this.groupInfo = {\n      name: '',\n      emoji: '👥'\n    };\n    this.nameError = '';\n    this.nameChecking = false;\n    this.nameAvailable = false;\n    this.nameCheckSubject = new Subject();\n    this.nameCheckSubscription = null;\n    this.activeTab = 'friends';\n    this.inviteUsername = '';\n    this.usernameSuggestions = [];\n    this.invitationCode = null;\n    this.codeIsValid = false;\n    this.invitationSentMessage = '';\n    this.isUsernameValid = false;\n    this.alreadyInvitedUsernames = [];\n    this.validFriendUsernames = [];\n    this.showEditNicknameModal = false;\n    this.editingMember = {\n      id: '',\n      group_id: '',\n      user_id: '',\n      nickname: '',\n      is_admin: false,\n      joined_date: new Date()\n    };\n    this.successMessage = '';\n    this.errorMessage = '';\n    this.subscriptions = [];\n    this.supabaseService = inject(SupabaseService);\n    this.groupService = inject(GroupService);\n    this.userService = inject(UserService);\n    this.friendService = inject(FriendService);\n    this.xpService = inject(XpService);\n    this.route = inject(ActivatedRoute);\n    this.router = inject(Router);\n  }\n  ngOnInit() {\n    this.nameCheckSubscription = this.nameCheckSubject.pipe(debounceTime(500), distinctUntilChanged()).subscribe(name => {\n      this.checkGroupNameExists(name);\n    });\n    if (this.nameCheckSubscription) {\n      this.subscriptions.push(this.nameCheckSubscription);\n    }\n    this.subscriptions.push(this.supabaseService.currentUser$.subscribe(user => {\n      if (user) {\n        this.userId = user.id;\n        this.route.paramMap.pipe(take(1)).subscribe(params => {\n          this.groupId = params.get('id');\n          if (this.groupId) {\n            this.loadGroup();\n            this.loadMembers();\n            this.checkInvitationCode();\n            this.loadAlreadyInvitedUsernames();\n            this.loadValidFriendUsernames();\n          } else {}\n        });\n      } else {\n        this.router.navigate(['/login']);\n      }\n    }));\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  loadGroup() {\n    if (!this.groupId) {\n      return;\n    }\n    this.subscriptions.push(this.groupService.getGroup(this.groupId).subscribe(group => {\n      if (group) {\n        this.group = group;\n        this.groupInfo.name = group.name;\n        this.groupInfo.emoji = group.emoji;\n        this.calculateRequiredXp();\n      } else {\n        this.router.navigate(['/groups']);\n      }\n    }));\n  }\n  loadMembers() {\n    if (!this.groupId || !this.userId) {\n      return;\n    }\n    this.subscriptions.push(this.groupService.getGroupMembers(this.groupId).subscribe(members => {\n      const currentUserMember = members.find(m => m.user_id === this.userId);\n      this.isAdmin = (currentUserMember === null || currentUserMember === void 0 ? void 0 : currentUserMember.is_admin) || false;\n      const membersWithProfiles = [];\n      const membersCopy = members.map(member => ({\n        ...member,\n        id: member.id || '',\n        group_id: member.group_id || '',\n        user_id: member.user_id || '',\n        nickname: member.nickname || '',\n        is_admin: member.is_admin || false,\n        joined_date: member.joined_date || new Date()\n      }));\n      for (const member of membersCopy) {\n        this.userService.getUserProfile(member.user_id).pipe(take(1)).subscribe(profile => {\n          membersWithProfiles.push({\n            ...member,\n            profile_picture: (profile === null || profile === void 0 ? void 0 : profile.profile_picture) || undefined,\n            username: profile === null || profile === void 0 ? void 0 : profile.username\n          });\n          this.members = [...membersWithProfiles];\n        });\n      }\n    }));\n  }\n  isFriend(userId) {\n    if (!this.userId || !userId) return false;\n    return false;\n  }\n  navigateToProfile(userId) {\n    if (!userId) return;\n    if (userId === this.userId) return;\n    this.friendService.getFriends(this.userId || '').pipe(take(1), map(friends => {\n      const isFriend = friends.some(friend => friend.user_id === this.userId && friend.friend_id === userId || friend.user_id === userId && friend.friend_id === this.userId);\n      if (isFriend) {\n        this.router.navigate(['/friends', userId]);\n      } else {\n        this.router.navigate(['/user-profile', userId]);\n      }\n    })).subscribe();\n  }\n  calculateRequiredXp() {\n    if (!this.group) return;\n    this.xpService.getRequiredXpForNextLevel(this.group.level, EntityType.GROUP).pipe(take(1)).subscribe(requiredXp => {\n      this.requiredXp = requiredXp;\n    });\n  }\n  getProgressPercentage(xp) {\n    if (this.requiredXp <= 0) return 0;\n    return Math.min(100, Math.round(xp / this.requiredXp * 100));\n  }\n  checkGroupName() {\n    var _this$groupInfo$name;\n    const name = (_this$groupInfo$name = this.groupInfo.name) === null || _this$groupInfo$name === void 0 ? void 0 : _this$groupInfo$name.trim();\n    this.nameAvailable = false;\n    if (!name) {\n      this.nameError = '';\n      this.nameChecking = false;\n      return;\n    }\n    this.nameChecking = true;\n    this.nameCheckSubject.next(name);\n  }\n  checkGroupNameExists(name) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!name || !_this.groupId) {\n        _this.nameError = '';\n        _this.nameChecking = false;\n        return;\n      }\n      try {\n        const isAvailable = yield _this.groupService.checkGroupNameAvailability(name, _this.groupId);\n        _this.nameChecking = false;\n        if (!isAvailable) {\n          _this.nameError = 'This group name is already taken';\n          _this.nameAvailable = false;\n        } else {\n          _this.nameError = '';\n          _this.nameAvailable = true;\n        }\n      } catch (error) {\n        _this.nameChecking = false;\n        _this.nameError = 'Error checking group name availability';\n      }\n    })();\n  }\n  updateGroupInfo() {\n    if (!this.groupId || !this.group || !this.isAdmin) return;\n    if (!this.groupInfo.name.trim()) {\n      this.errorMessage = 'Group name cannot be empty.';\n      setTimeout(() => this.errorMessage = '', 3000);\n      return;\n    }\n    if (this.nameError) {\n      this.errorMessage = 'Please choose a different group name.';\n      setTimeout(() => this.errorMessage = '', 3000);\n      return;\n    }\n    this.groupService.updateGroup(this.groupId, {\n      name: this.groupInfo.name.trim(),\n      emoji: this.groupInfo.emoji\n    }).then(() => {\n      this.successMessage = 'Group information updated successfully!';\n      setTimeout(() => this.successMessage = '', 3000);\n      if (this.group) {\n        this.group.name = this.groupInfo.name.trim();\n        this.group.emoji = this.groupInfo.emoji;\n      }\n    }).catch(error => {\n      this.errorMessage = error.message || 'Error updating group information.';\n      setTimeout(() => this.errorMessage = '', 3000);\n    });\n  }\n  setActiveTab(tab) {\n    this.activeTab = tab;\n    this.invitationSentMessage = '';\n  }\n  inviteFriend() {\n    if (!this.groupId || !this.isAdmin || !this.inviteUsername.trim()) return;\n    const usernameToInvite = this.inviteUsername.trim();\n    if (!this.friendService.validateUsernameFormat(usernameToInvite)) {\n      this.errorMessage = 'Invalid username format. Username should only contain letters, numbers, and special characters.';\n      setTimeout(() => this.errorMessage = '', 3000);\n      return;\n    }\n    if (this.alreadyInvitedUsernames.includes(usernameToInvite)) {\n      this.errorMessage = 'An invitation has already been sent to this user.';\n      setTimeout(() => this.errorMessage = '', 3000);\n      return;\n    }\n    if (!this.validFriendUsernames.includes(usernameToInvite)) {\n      this.errorMessage = 'You can only invite users from your friends list.';\n      setTimeout(() => this.errorMessage = '', 3000);\n      return;\n    }\n    this.groupService.inviteUserToGroup(this.groupId, usernameToInvite).then(() => {\n      this.alreadyInvitedUsernames.push(usernameToInvite);\n      this.successMessage = `Invitation sent to ${usernameToInvite}!`;\n      this.invitationSentMessage = `Invitation sent to ${usernameToInvite}!`;\n      this.inviteUsername = '';\n      setTimeout(() => this.successMessage = '', 3000);\n      setTimeout(() => this.invitationSentMessage = '', 5000);\n    }).catch(error => {\n      if (error.message && error.message.includes('already been sent')) {\n        this.errorMessage = 'An invitation has already been sent to this user.';\n      } else if (error.message && error.message.includes('already a member')) {\n        this.errorMessage = 'This user is already a member of the group.';\n      } else if (error.message && error.message.includes('User not found')) {\n        this.errorMessage = 'User not found. Please check the username.';\n      } else {\n        this.errorMessage = 'Error sending invitation. Make sure the username is correct and the user is in your friends list.';\n      }\n      setTimeout(() => this.errorMessage = '', 3000);\n    });\n  }\n  generateInvitationCode() {\n    if (!this.groupId || !this.isAdmin) return;\n    this.groupService.generateInvitationCode(this.groupId).then(code => {\n      this.invitationCode = code;\n      this.codeIsValid = true;\n    }).catch(error => {\n      this.errorMessage = 'Error generating invitation code.';\n      setTimeout(() => this.errorMessage = '', 3000);\n    });\n  }\n  checkInvitationCode() {\n    if (!this.groupId) return;\n    this.groupService.getGroup(this.groupId).pipe(take(1)).subscribe(group => {\n      if (group && group.invitation_code && group.code_expiry) {\n        const expiryDate = new Date(group.code_expiry);\n        if (expiryDate > new Date()) {\n          this.invitationCode = group.invitation_code;\n          this.codeIsValid = true;\n        }\n      }\n    });\n  }\n  loadAlreadyInvitedUsernames() {\n    if (!this.groupId) return;\n    this.supabaseService.supabase.from('group_join_requests').select('username_invited').eq('group_id', this.groupId).then(response => {\n      if (response.error) {\n        return;\n      }\n      this.alreadyInvitedUsernames = response.data.map(invite => invite.username_invited);\n    });\n  }\n  loadValidFriendUsernames() {\n    if (!this.userId) return;\n    this.friendService.getFriendsWithProfiles(this.userId).pipe(take(1)).subscribe(friends => {\n      if (!friends || friends.length === 0) {\n        return;\n      }\n      this.validFriendUsernames = friends.filter(friend => friend.profile && friend.profile.username).map(friend => friend.profile.username);\n    });\n  }\n  updateSideQuestSettings() {\n    if (!this.groupId || !this.group || !this.isAdmin) return;\n    this.groupService.updateGroup(this.groupId, {\n      enable_sidequests: this.group.enable_sidequests\n    }).then(() => {\n      this.successMessage = 'Side quest settings updated successfully!';\n      setTimeout(() => this.successMessage = '', 3000);\n      this.loadGroup();\n    }).catch(error => {\n      this.errorMessage = 'Error updating side quest settings.';\n      setTimeout(() => this.errorMessage = '', 3000);\n    });\n  }\n  editMemberNickname(member) {\n    this.editingMember = {\n      ...member\n    };\n    this.showEditNicknameModal = true;\n  }\n  updateMemberNickname() {\n    if (!this.groupId || !this.isAdmin || !this.editingMember.id) return;\n    this.groupService.updateGroupMember(this.editingMember.id, {\n      nickname: this.editingMember.nickname.trim()\n    }).then(() => {\n      this.successMessage = 'Member nickname updated successfully!';\n      this.showEditNicknameModal = false;\n      const index = this.members.findIndex(m => m.id === this.editingMember.id);\n      if (index !== -1) {\n        this.members[index].nickname = this.editingMember.nickname.trim();\n      }\n      setTimeout(() => this.successMessage = '', 3000);\n    }).catch(error => {\n      this.errorMessage = 'Error updating member nickname.';\n      setTimeout(() => this.errorMessage = '', 3000);\n    });\n  }\n  removeMember(member) {\n    if (!this.groupId || !this.isAdmin || !member.id) return;\n    if (confirm(`Are you sure you want to remove ${member.nickname} from the group?`)) {\n      this.groupService.removeGroupMemberById(this.groupId, member.id).then(() => {\n        this.successMessage = `${member.nickname} has been removed from the group.`;\n        this.members = this.members.filter(m => m.id !== member.id);\n        setTimeout(() => this.successMessage = '', 3000);\n      }).catch(error => {\n        this.errorMessage = 'Error removing member from group.';\n        setTimeout(() => this.errorMessage = '', 3000);\n      });\n    }\n  }\n  leaveGroup() {\n    if (!this.groupId || !this.userId) return;\n    if (confirm('Are you sure you want to leave this group? You will need to be invited again to rejoin.')) {\n      this.groupService.leaveGroup(this.groupId).then(() => {\n        this.router.navigate(['/groups']);\n      }).catch(error => {\n        this.errorMessage = 'Error leaving group.';\n        setTimeout(() => this.errorMessage = '', 3000);\n      });\n    }\n  }\n  confirmDeleteGroup() {\n    if (!this.groupId || !this.isAdmin) return;\n    if (confirm('Are you sure you want to delete this group? This action cannot be undone and all group data will be permanently deleted.')) {\n      this.groupService.deleteGroup(this.groupId).then(() => {\n        this.router.navigate(['/groups']);\n      }).catch(error => {\n        this.errorMessage = 'Error deleting group.';\n        setTimeout(() => this.errorMessage = '', 3000);\n      });\n    }\n  }\n  selectUsername(username) {\n    this.inviteUsername = username;\n    this.usernameSuggestions = [];\n    const trimmedUsername = username.trim();\n    const isFormatValid = trimmedUsername !== '' && this.friendService.validateUsernameFormat(trimmedUsername);\n    const isAlreadyInvited = this.alreadyInvitedUsernames.includes(trimmedUsername);\n    const isFriend = this.validFriendUsernames.includes(trimmedUsername);\n    this.isUsernameValid = isFormatValid && !isAlreadyInvited && isFriend;\n    if (isAlreadyInvited) {\n      this.errorMessage = 'An invitation has already been sent to this user.';\n      setTimeout(() => this.errorMessage = '', 3000);\n    } else if (!isFriend && isFormatValid) {\n      this.errorMessage = 'You can only invite users from your friends list.';\n      setTimeout(() => this.errorMessage = '', 3000);\n    }\n  }\n  onUsernameInput() {\n    this.invitationSentMessage = '';\n    const trimmedUsername = this.inviteUsername.trim();\n    const isFormatValid = trimmedUsername !== '' && this.friendService.validateUsernameFormat(trimmedUsername);\n    const isAlreadyInvited = this.alreadyInvitedUsernames.includes(trimmedUsername);\n    const isFriend = this.validFriendUsernames.includes(trimmedUsername);\n    this.isUsernameValid = isFormatValid && !isAlreadyInvited && isFriend;\n    if (!this.userId || !this.groupId || !this.inviteUsername || this.inviteUsername.length < 2) {\n      this.usernameSuggestions = [];\n      return;\n    }\n    this.friendService.searchFriendsByUsername(this.userId, this.inviteUsername, this.groupId).pipe(take(1)).subscribe(suggestions => {\n      this.usernameSuggestions = suggestions;\n    });\n  }\n}\n_GroupSettingsPage = GroupSettingsPage;\n_GroupSettingsPage.ɵfac = function GroupSettingsPage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _GroupSettingsPage)();\n};\n_GroupSettingsPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _GroupSettingsPage,\n  selectors: [[\"app-group-settings\"]],\n  decls: 26,\n  vars: 14,\n  consts: [[\"inviteForm\", \"ngForm\"], [\"usernameInput\", \"ngModel\"], [\"generateCodeBlock\", \"\"], [1, \"settings-container\"], [1, \"settings-header\"], [1, \"back-link\", 3, \"routerLink\"], [1, \"back-arrow\"], [\"class\", \"group-level-badge\", 4, \"ngIf\"], [\"class\", \"messages\", 4, \"ngIf\"], [\"class\", \"settings-section\", 4, \"ngIf\"], [\"class\", \"settings-section danger-section\", 4, \"ngIf\"], [\"id\", \"edit-nickname-modal\", 1, \"modal\"], [1, \"modal-content\"], [1, \"close-modal\", 3, \"click\"], [3, \"ngSubmit\"], [\"type\", \"text\", \"id\", \"edit-nickname-input\", \"name\", \"nickname\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"submit\", 1, \"save-nickname-btn\"], [1, \"group-level-badge\"], [1, \"messages\"], [\"class\", \"message success\", 4, \"ngIf\"], [\"class\", \"message error\", 4, \"ngIf\"], [1, \"message\", \"success\"], [1, \"message\", \"error\"], [1, \"settings-section\"], [1, \"section-header\"], [1, \"section-icon\"], [1, \"xp-section\"], [1, \"category-card\"], [1, \"category-header\"], [1, \"category-icon\"], [1, \"category-name\"], [1, \"xp-progress\"], [1, \"xp-value\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"group-info-section\"], [\"id\", \"edit-group-form\", 3, \"ngSubmit\"], [2, \"display\", \"flex\", \"gap\", \"10px\"], [1, \"form-group\"], [\"type\", \"text\", \"name\", \"emoji\", \"id\", \"emoji\", \"appEmojiInput\", \"\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", 2, \"width\", \"100%\"], [\"type\", \"text\", \"name\", \"name\", \"id\", \"name\", \"required\", \"\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"class\", \"checking-message\", 4, \"ngIf\"], [\"class\", \"success-message\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"save-btn\"], [1, \"error-message\"], [1, \"checking-message\"], [1, \"success-message\"], [1, \"members-list\"], [\"class\", \"member-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"member-item\"], [1, \"member-info\"], [4, \"ngIf\"], [1, \"member-details\"], [\"class\", \"admin-badge\", 4, \"ngIf\"], [1, \"member-joined\"], [1, \"member-actions\"], [\"class\", \"leave-btn\", 3, \"click\", 4, \"ngIf\"], [1, \"member-avatar-link\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"member-avatar\"], [\"class\", \"default-avatar\", 4, \"ngIf\"], [3, \"src\", \"alt\", 4, \"ngIf\"], [1, \"default-avatar\"], [3, \"src\", \"alt\"], [1, \"member-name-link\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"member-name\"], [1, \"admin-badge\"], [1, \"leave-btn\", 3, \"click\"], [1, \"edit-nickname-btn\", 3, \"click\"], [1, \"remove-btn\", 3, \"click\"], [1, \"invite-section\"], [1, \"invite-tabs\"], [1, \"invite-tab\", 3, \"click\"], [\"class\", \"invite-tab-content\", \"id\", \"friends-tab\", 4, \"ngIf\"], [\"class\", \"invite-tab-content\", \"id\", \"code-tab\", 4, \"ngIf\"], [\"id\", \"friends-tab\", 1, \"invite-tab-content\"], [\"id\", \"invite-form\", 3, \"ngSubmit\"], [1, \"invite-input-container\"], [\"type\", \"text\", \"name\", \"username\", \"id\", \"username\", \"placeholder\", \"Username to invite\", \"pattern\", \"^[\\\\w.@+-]+$\", \"title\", \"Username should only contain letters, numbers, and @/./+/-/_ characters\", \"required\", \"\", \"autocomplete\", \"off\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"id\", \"username-suggestions\", \"class\", \"username-suggestions\", 4, \"ngIf\"], [\"class\", \"validation-error\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"invite-btn\", 3, \"disabled\"], [\"class\", \"invitation-sent-message\", 4, \"ngIf\"], [1, \"invite-note\"], [\"id\", \"username-suggestions\", 1, \"username-suggestions\"], [\"class\", \"suggestion-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"suggestion-item\", 3, \"click\"], [1, \"validation-error\"], [1, \"invitation-sent-message\"], [\"id\", \"code-tab\", 1, \"invite-tab-content\"], [1, \"code-header\"], [2, \"margin\", \"0\", \"font-size\", \"18px\"], [1, \"code-actions\", 2, \"display\", \"flex\", \"flex-direction\", \"column\", \"gap\", \"15px\"], [4, \"ngIf\", \"ngIfElse\"], [1, \"code-display\"], [1, \"code-info\"], [1, \"invite-btn\", 2, \"width\", \"100%\", 3, \"click\"], [1, \"sidequest-settings-form\", 3, \"ngSubmit\"], [1, \"setting-item\"], [1, \"toggle-switch\"], [\"type\", \"checkbox\", \"name\", \"enable_sidequests\", \"id\", \"enable_sidequests\", 3, \"ngModelChange\", \"ngModel\"], [1, \"toggle-slider\"], [1, \"setting-label\"], [\"type\", \"submit\", 1, \"save-settings-btn\"], [1, \"settings-section\", \"danger-section\"], [1, \"danger-actions\"], [1, \"delete-btn\", 3, \"click\"], [1, \"danger-note\"]],\n  template: function GroupSettingsPage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"a\", 5)(3, \"span\", 6);\n      i0.ɵɵtext(4, \"\\u2190\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtext(5, \" Back to group \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(6, \"h1\");\n      i0.ɵɵtext(7, \"Settings\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(8, GroupSettingsPage_div_8_Template, 2, 1, \"div\", 7);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(9, GroupSettingsPage_div_9_Template, 3, 2, \"div\", 8)(10, GroupSettingsPage_div_10_Template, 51, 16, \"div\", 9)(11, GroupSettingsPage_div_11_Template, 18, 5, \"div\", 9)(12, GroupSettingsPage_div_12_Template, 8, 1, \"div\", 9)(13, GroupSettingsPage_div_13_Template, 14, 6, \"div\", 9)(14, GroupSettingsPage_div_14_Template, 15, 1, \"div\", 9)(15, GroupSettingsPage_div_15_Template, 11, 0, \"div\", 10);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(16, \"div\", 11)(17, \"div\", 12)(18, \"span\", 13);\n      i0.ɵɵlistener(\"click\", function GroupSettingsPage_Template_span_click_18_listener() {\n        return ctx.showEditNicknameModal = false;\n      });\n      i0.ɵɵtext(19, \"\\u00D7\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(20, \"h3\");\n      i0.ɵɵtext(21, \"Edit Nickname\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(22, \"form\", 14);\n      i0.ɵɵlistener(\"ngSubmit\", function GroupSettingsPage_Template_form_ngSubmit_22_listener() {\n        return ctx.updateMemberNickname();\n      });\n      i0.ɵɵelementStart(23, \"input\", 15);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function GroupSettingsPage_Template_input_ngModelChange_23_listener($event) {\n        i0.ɵɵtwoWayBindingSet(ctx.editingMember.nickname, $event) || (ctx.editingMember.nickname = $event);\n        return $event;\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(24, \"button\", 16);\n      i0.ɵɵtext(25, \"Save\");\n      i0.ɵɵelementEnd()()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(12, _c0, ctx.groupId));\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"ngIf\", ctx.group);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.successMessage || ctx.errorMessage);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.group);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.isAdmin && ctx.group);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.members.length > 0);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.isAdmin);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.isAdmin && ctx.group);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.isAdmin);\n      i0.ɵɵadvance();\n      i0.ɵɵstyleProp(\"display\", ctx.showEditNicknameModal ? \"block\" : \"none\");\n      i0.ɵɵadvance(7);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.editingMember.nickname);\n    }\n  },\n  dependencies: [IonicModule, i1.RouterLinkWithHrefDelegate, CommonModule, i2.NgForOf, i2.NgIf, i2.DatePipe, FormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.CheckboxControlValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.PatternValidator, i3.NgModel, i3.NgForm, RouterModule, i4.RouterLink, EmojiInputDirective],\n  styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%];\\n\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\tvar __webpack_modules__ = ({\\n\\n\\n 690:\\n\\n\\n\\n\\n\\n (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/sass-loader/dist/cjs.js):\\\\nexpected \\\\\\\"{\\\\\\\".\\\\n   \\u2577\\\\n20 \\u2502  Settings Container */\\\\r\\\\n   \\u2502                       ^\\\\n   \\u2575\\\\n  src\\\\\\\\app\\\\\\\\pages\\\\\\\\groups\\\\\\\\group-settings\\\\\\\\group-settings.page.scss 20:23  root stylesheet\\\");\\n\\n\\n })\\n\\n\\n \\t});\\n\\n\\n\\n \\t\\n\\n \\t// startup\\n\\n \\t// Load entry module and return exports\\n\\n \\t// This entry module doesn't tell about it's top-level declarations so it can't be inlined\\n\\n \\tvar __webpack_exports__ = {};\\n\\n \\t__webpack_modules__[690]();\\n\\n \\tresource = __webpack_exports__;\\n\\n \\t\\n\\n })()\\n;\"]\n});", "map": {"version": 3, "names": ["inject", "CommonModule", "FormsModule", "IonicModule", "ActivatedRoute", "Router", "RouterModule", "take", "debounceTime", "distinctUntilChanged", "Subject", "map", "SupabaseService", "GroupService", "UserService", "FriendService", "XpService", "EntityType", "GroupMember", "EmojiInputDirective", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "group", "level", "ɵɵtextInterpolate", "successMessage", "errorMessage", "ɵɵtemplate", "GroupSettingsPage_div_9_div_1_Template", "GroupSettingsPage_div_9_div_2_Template", "ɵɵproperty", "ɵɵelement", "ɵɵtextInterpolate2", "strength_xp", "requiredXp", "ɵɵstyleProp", "getProgressPercentage", "money_xp", "health_xp", "knowledge_xp", "nameError", "ɵɵlistener", "GroupSettingsPage_div_11_Template_form_ngSubmit_7_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "updateGroupInfo", "ɵɵtwoWayListener", "GroupSettingsPage_div_11_Template_input_ngModelChange_10_listener", "$event", "ɵɵtwoWayBindingSet", "groupInfo", "emoji", "GroupSettingsPage_div_11_Template_input_ngModelChange_12_listener", "name", "GroupSettingsPage_div_11_Template_input_input_12_listener", "checkGroupName", "GroupSettingsPage_div_11_div_13_Template", "GroupSettingsPage_div_11_div_14_Template", "GroupSettingsPage_div_11_div_15_Template", "ɵɵtwoWayProperty", "nameChecking", "nameAvailable", "member_r4", "nickname", "char<PERSON>t", "toUpperCase", "profile_picture", "ɵɵsanitizeUrl", "ɵɵelementContainerStart", "GroupSettingsPage_div_12_div_7_ng_container_2_Template_a_click_1_listener", "_r3", "$implicit", "navigateToProfile", "user_id", "GroupSettingsPage_div_12_div_7_ng_container_2_div_3_Template", "GroupSettingsPage_div_12_div_7_ng_container_2_img_4_Template", "GroupSettingsPage_div_12_div_7_ng_container_3_div_2_Template", "GroupSettingsPage_div_12_div_7_ng_container_3_img_3_Template", "GroupSettingsPage_div_12_div_7_ng_container_5_Template_a_click_1_listener", "_r5", "GroupSettingsPage_div_12_div_7_button_12_Template_button_click_0_listener", "_r6", "leaveGroup", "GroupSettingsPage_div_12_div_7_ng_container_13_Template_button_click_1_listener", "_r7", "editMemberNickname", "GroupSettingsPage_div_12_div_7_ng_container_13_Template_button_click_3_listener", "removeMember", "GroupSettingsPage_div_12_div_7_ng_container_2_Template", "GroupSettingsPage_div_12_div_7_ng_container_3_Template", "GroupSettingsPage_div_12_div_7_ng_container_5_Template", "GroupSettingsPage_div_12_div_7_ng_container_6_Template", "GroupSettingsPage_div_12_div_7_span_7_Template", "GroupSettingsPage_div_12_div_7_button_12_Template", "GroupSettingsPage_div_12_div_7_ng_container_13_Template", "userId", "is_admin", "ɵɵpipeBind2", "joined_date", "isAdmin", "GroupSettingsPage_div_12_div_7_Template", "members", "GroupSettingsPage_div_13_div_12_div_8_div_1_Template_div_click_0_listener", "suggestion_r11", "_r10", "selectUsername", "GroupSettingsPage_div_13_div_12_div_8_div_1_Template", "usernameSuggestions", "GroupSettingsPage_div_13_div_12_div_9_div_1_Template", "GroupSettingsPage_div_13_div_12_div_9_div_2_Template", "GroupSettingsPage_div_13_div_12_div_9_div_3_Template", "GroupSettingsPage_div_13_div_12_div_9_div_4_Template", "usernameInput_r12", "errors", "valid", "inviteUsername", "trim", "alreadyInvitedUsernames", "includes", "validFriendUsernames", "invitationSentMessage", "GroupSettingsPage_div_13_div_12_Template_form_ngSubmit_3_listener", "_r9", "inviteFriend", "GroupSettingsPage_div_13_div_12_Template_input_ngModelChange_6_listener", "GroupSettingsPage_div_13_div_12_Template_input_input_6_listener", "onUsernameInput", "GroupSettingsPage_div_13_div_12_div_8_Template", "GroupSettingsPage_div_13_div_12_div_9_Template", "GroupSettingsPage_div_13_div_12_div_12_Template", "length", "invalid", "dirty", "touched", "inviteForm_r13", "invitationCode", "GroupSettingsPage_div_13_div_13_ng_template_11_Template_button_click_0_listener", "_r14", "generateInvitationCode", "GroupSettingsPage_div_13_div_13_ng_container_10_Template", "GroupSettingsPage_div_13_div_13_ng_template_11_Template", "ɵɵtemplateRefExtractor", "codeIsValid", "generateCodeBlock_r15", "GroupSettingsPage_div_13_Template_button_click_8_listener", "_r8", "setActiveTab", "GroupSettingsPage_div_13_Template_button_click_10_listener", "GroupSettingsPage_div_13_div_12_Template", "GroupSettingsPage_div_13_div_13_Template", "ɵɵclassProp", "activeTab", "GroupSettingsPage_div_14_Template_form_ngSubmit_6_listener", "_r16", "updateSideQuestSettings", "GroupSettingsPage_div_14_Template_input_ngModelChange_9_listener", "enable_sidequests", "GroupSettingsPage_div_15_Template_button_click_7_listener", "_r17", "confirmDeleteGroup", "GroupSettingsPage", "constructor", "groupId", "nameCheckSubject", "nameCheckSubscription", "isUsernameValid", "showEditNicknameModal", "editingMember", "id", "group_id", "Date", "subscriptions", "supabaseService", "groupService", "userService", "friendService", "xpService", "route", "router", "ngOnInit", "pipe", "subscribe", "checkGroupNameExists", "push", "currentUser$", "user", "paramMap", "params", "get", "loadGroup", "loadMembers", "checkInvitationCode", "loadAlreadyInvitedUsernames", "loadValidFriendUsernames", "navigate", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "getGroup", "calculateRequiredXp", "getGroupMembers", "currentUserMember", "find", "m", "membersWithProfiles", "membersCopy", "member", "getUserProfile", "profile", "undefined", "username", "isFriend", "getFriends", "friends", "some", "friend", "friend_id", "getRequiredXpForNextLevel", "GROUP", "xp", "Math", "min", "round", "_this$groupInfo$name", "next", "_this", "_asyncToGenerator", "isAvailable", "checkGroupNameAvailability", "error", "setTimeout", "updateGroup", "then", "catch", "message", "tab", "usernameToInvite", "validateUsernameFormat", "inviteUserToGroup", "code", "invitation_code", "code_expiry", "expiryDate", "supabase", "from", "select", "eq", "response", "data", "invite", "username_invited", "getFriendsWithProfiles", "filter", "updateMemberNickname", "updateGroupMember", "index", "findIndex", "confirm", "removeGroupMemberById", "deleteGroup", "trimmedUsername", "isFormatValid", "isAlreadyInvited", "searchFriendsByUsername", "suggestions", "selectors", "decls", "vars", "consts", "template", "GroupSettingsPage_Template", "rf", "ctx", "GroupSettingsPage_div_8_Template", "GroupSettingsPage_div_9_Template", "GroupSettingsPage_div_10_Template", "GroupSettingsPage_div_11_Template", "GroupSettingsPage_div_12_Template", "GroupSettingsPage_div_13_Template", "GroupSettingsPage_div_14_Template", "GroupSettingsPage_div_15_Template", "GroupSettingsPage_Template_span_click_18_listener", "GroupSettingsPage_Template_form_ngSubmit_22_listener", "GroupSettingsPage_Template_input_ngModelChange_23_listener", "ɵɵpureFunction1", "_c0", "i1", "RouterLinkWithHrefDelegate", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i3", "ɵNgNoValidate", "DefaultValueAccessor", "CheckboxControlValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "Pat<PERSON>Vali<PERSON><PERSON>", "NgModel", "NgForm", "i4", "RouterLink", "styles"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\groups\\group-settings\\group-settings.page.ts", "C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\groups\\group-settings\\group-settings.page.html"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON>ni<PERSON>, On<PERSON><PERSON>roy, inject } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IonicModule, ToastController } from '@ionic/angular';\r\nimport { ActivatedRoute, Router, RouterModule } from '@angular/router';\r\nimport { Subscription, take, debounceTime, distinctUntilChanged, Subject, map } from 'rxjs';\r\n\r\nimport { SupabaseService } from '../../../services/supabase.service';\r\nimport { GroupService } from '../../../services/group.service';\r\nimport { UserService } from '../../../services/user.service';\r\nimport { FriendService } from '../../../services/friend.service';\r\nimport { XpService, EntityType } from '../../../services/xp.service';\r\nimport { Group, GroupMember } from '../../../models/supabase.models';\r\nimport { EmojiInputDirective } from '../../../directives/emoji-input.directive';\r\n\r\ninterface GroupWithOptionalId extends Omit<Group, 'id'> {\r\n  id?: string;\r\n}\r\n\r\ninterface MemberWithProfile extends GroupMember {\r\n  profile_picture?: string;\r\n  username?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-group-settings',\r\n  templateUrl: './group-settings.page.html',\r\n  styleUrls: ['./group-settings.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule, FormsModule, RouterModule, EmojiInputDirective]\r\n})\r\nexport class GroupSettingsPage implements OnInit, OnDestroy {\r\n  userId: string | null = null;\r\n\r\n  groupId: string | null = null;\r\n  group: GroupWithOptionalId | null = null;\r\n  members: MemberWithProfile[] = [];\r\n  isAdmin = false;\r\n  requiredXp = 0;\r\n\r\n  groupInfo = {\r\n    name: '',\r\n    emoji: '👥'\r\n  };\r\n  nameError = '';\r\n  nameChecking = false;\r\n  nameAvailable = false;\r\n  private nameCheckSubject = new Subject<string>();\r\n  private nameCheckSubscription: Subscription | null = null;\r\n\r\n  activeTab = 'friends';\r\n  inviteUsername = '';\r\n  usernameSuggestions: string[] = [];\r\n  invitationCode: string | null = null;\r\n  codeIsValid = false;\r\n  invitationSentMessage = '';\r\n  isUsernameValid = false; \n  alreadyInvitedUsernames: string[] = []; \n  validFriendUsernames: string[] = []; \n\r\n  showEditNicknameModal = false;\r\n  editingMember: MemberWithProfile = {\r\n    id: '',\r\n    group_id: '',\r\n    user_id: '',\r\n    nickname: '',\r\n    is_admin: false,\r\n    joined_date: new Date()\r\n  };\r\n\r\n  successMessage = '';\r\n  errorMessage = '';\r\n\r\n  private subscriptions: Subscription[] = [];\r\n\r\n  private supabaseService = inject(SupabaseService);\r\n  private groupService = inject(GroupService);\r\n  private userService = inject(UserService);\r\n  private friendService = inject(FriendService);\r\n  private xpService = inject(XpService);\r\n  private route = inject(ActivatedRoute);\r\n  private router = inject(Router);\r\n\r\n  constructor() {}\r\n\r\n  ngOnInit() {\r\n\r\n    this.nameCheckSubscription = this.nameCheckSubject.pipe(\r\n      debounceTime(500), \n      distinctUntilChanged() \n    ).subscribe(name => {\r\n      this.checkGroupNameExists(name);\r\n    });\r\n\r\n    if (this.nameCheckSubscription) {\r\n      this.subscriptions.push(this.nameCheckSubscription);\r\n    }\r\n\r\n    this.subscriptions.push(\r\n      this.supabaseService.currentUser$.subscribe(user => {\r\n        if (user) {\r\n          this.userId = user.id;\r\n\r\n          this.route.paramMap.pipe(take(1)).subscribe(params => {\r\n            this.groupId = params.get('id');\r\n\r\n            if (this.groupId) {\r\n              this.loadGroup();\r\n              this.loadMembers();\r\n              this.checkInvitationCode();\r\n              this.loadAlreadyInvitedUsernames();\r\n              this.loadValidFriendUsernames();\r\n            } else {\r\n            }\r\n          });\r\n        } else {\r\n          this.router.navigate(['/login']);\r\n        }\r\n      })\r\n    );\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subscriptions.forEach(sub => sub.unsubscribe());\r\n  }\r\n\r\n  loadGroup() {\r\n    if (!this.groupId) {\r\n      return;\r\n    }\r\n\r\n    this.subscriptions.push(\r\n      this.groupService.getGroup(this.groupId).subscribe(group => {\r\n        if (group) {\r\n          this.group = group;\r\n          this.groupInfo.name = group.name;\r\n          this.groupInfo.emoji = group.emoji;\r\n          this.calculateRequiredXp();\r\n        } else {\r\n          this.router.navigate(['/groups']);\r\n        }\r\n      })\r\n    );\r\n  }\r\n\r\n  loadMembers() {\r\n    if (!this.groupId || !this.userId) {\r\n      return;\r\n    }\r\n\r\n    this.subscriptions.push(\r\n      this.groupService.getGroupMembers(this.groupId).subscribe(members => {\r\n\r\n        const currentUserMember = members.find(m => m.user_id === this.userId);\r\n        this.isAdmin = currentUserMember?.is_admin || false;\r\n\r\n        const membersWithProfiles: MemberWithProfile[] = [];\r\n\r\n        const membersCopy = members.map(member => ({\r\n          ...member,\r\n          id: member.id || '',\r\n          group_id: member.group_id || '',\r\n          user_id: member.user_id || '',\r\n          nickname: member.nickname || '',\r\n          is_admin: member.is_admin || false,\r\n          joined_date: member.joined_date || new Date()\r\n        }));\r\n\r\n        for (const member of membersCopy) {\r\n          this.userService.getUserProfile(member.user_id).pipe(take(1)).subscribe(profile => {\r\n            membersWithProfiles.push({\r\n              ...member,\r\n              profile_picture: profile?.profile_picture || undefined,\r\n              username: profile?.username\r\n            });\r\n\r\n            this.members = [...membersWithProfiles];\r\n          });\r\n        }\r\n      })\r\n    );\r\n  }\r\n\r\n  isFriend(userId: string): boolean {\r\n    if (!this.userId || !userId) return false;\r\n\r\n    return false;\r\n  }\r\n\r\n  navigateToProfile(userId: string) {\r\n    if (!userId) return;\r\n\r\n    if (userId === this.userId) return;\r\n\r\n    this.friendService.getFriends(this.userId || '').pipe(\r\n      take(1),\r\n      map((friends: any[]) => {\r\n        const isFriend = friends.some((friend: any) =>\r\n          (friend.user_id === this.userId && friend.friend_id === userId) ||\r\n          (friend.user_id === userId && friend.friend_id === this.userId)\r\n        );\r\n\r\n        if (isFriend) {\r\n          this.router.navigate(['/friends', userId]);\r\n        } else {\r\n          this.router.navigate(['/user-profile', userId]);\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  calculateRequiredXp() {\r\n    if (!this.group) return;\r\n\r\n    this.xpService.getRequiredXpForNextLevel(this.group.level, EntityType.GROUP)\r\n      .pipe(take(1))\r\n      .subscribe(requiredXp => {\r\n        this.requiredXp = requiredXp;\r\n      });\r\n  }\r\n\r\n  getProgressPercentage(xp: number): number {\r\n    if (this.requiredXp <= 0) return 0;\r\n    return Math.min(100, Math.round((xp / this.requiredXp) * 100));\r\n  }\r\n\r\n  checkGroupName() {\r\n    const name = this.groupInfo.name?.trim();\r\n\r\n    this.nameAvailable = false;\r\n\r\n    if (!name) {\r\n      this.nameError = '';\r\n      this.nameChecking = false;\r\n      return;\r\n    }\r\n\r\n    this.nameChecking = true;\r\n\r\n    this.nameCheckSubject.next(name);\r\n  }\r\n\r\n  async checkGroupNameExists(name: string) {\r\n    if (!name || !this.groupId) {\r\n      this.nameError = '';\r\n      this.nameChecking = false;\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const isAvailable = await this.groupService.checkGroupNameAvailability(name, this.groupId);\r\n\r\n      this.nameChecking = false;\r\n\r\n      if (!isAvailable) {\r\n        this.nameError = 'This group name is already taken';\r\n        this.nameAvailable = false;\r\n      } else {\r\n        this.nameError = '';\r\n        this.nameAvailable = true;\r\n      }\r\n    } catch (error) {\r\n      this.nameChecking = false;\r\n      this.nameError = 'Error checking group name availability';\r\n    }\r\n  }\r\n\r\n  updateGroupInfo() {\r\n    if (!this.groupId || !this.group || !this.isAdmin) return;\r\n\r\n    if (!this.groupInfo.name.trim()) {\r\n      this.errorMessage = 'Group name cannot be empty.';\r\n      setTimeout(() => this.errorMessage = '', 3000);\r\n      return;\r\n    }\r\n\r\n    if (this.nameError) {\r\n      this.errorMessage = 'Please choose a different group name.';\r\n      setTimeout(() => this.errorMessage = '', 3000);\r\n      return;\r\n    }\r\n\r\n    this.groupService.updateGroup(this.groupId, {\r\n      name: this.groupInfo.name.trim(),\r\n      emoji: this.groupInfo.emoji\r\n    }).then(() => {\r\n      this.successMessage = 'Group information updated successfully!';\r\n      setTimeout(() => this.successMessage = '', 3000);\r\n\r\n      if (this.group) {\r\n        this.group.name = this.groupInfo.name.trim();\r\n        this.group.emoji = this.groupInfo.emoji;\r\n      }\r\n    }).catch(error => {\r\n      this.errorMessage = error.message || 'Error updating group information.';\r\n      setTimeout(() => this.errorMessage = '', 3000);\r\n    });\r\n  }\r\n\r\n\r\n  setActiveTab(tab: string) {\r\n    this.activeTab = tab;\r\n    this.invitationSentMessage = '';\r\n  }\r\n\r\n  inviteFriend() {\r\n    if (!this.groupId || !this.isAdmin || !this.inviteUsername.trim()) return;\r\n\r\n    const usernameToInvite = this.inviteUsername.trim();\r\n\r\n    if (!this.friendService.validateUsernameFormat(usernameToInvite)) {\r\n      this.errorMessage = 'Invalid username format. Username should only contain letters, numbers, and special characters.';\r\n      setTimeout(() => this.errorMessage = '', 3000);\r\n      return;\r\n    }\r\n\r\n    if (this.alreadyInvitedUsernames.includes(usernameToInvite)) {\r\n      this.errorMessage = 'An invitation has already been sent to this user.';\r\n      setTimeout(() => this.errorMessage = '', 3000);\r\n      return;\r\n    }\r\n\r\n    if (!this.validFriendUsernames.includes(usernameToInvite)) {\r\n      this.errorMessage = 'You can only invite users from your friends list.';\r\n      setTimeout(() => this.errorMessage = '', 3000);\r\n      return;\r\n    }\r\n\r\n    this.groupService.inviteUserToGroup(this.groupId, usernameToInvite)\r\n      .then(() => {\r\n        this.alreadyInvitedUsernames.push(usernameToInvite);\r\n\r\n        this.successMessage = `Invitation sent to ${usernameToInvite}!`;\r\n        this.invitationSentMessage = `Invitation sent to ${usernameToInvite}!`;\r\n        this.inviteUsername = '';\r\n\r\n        setTimeout(() => this.successMessage = '', 3000);\r\n\r\n        setTimeout(() => this.invitationSentMessage = '', 5000);\r\n      })\r\n      .catch(error => {\r\n\r\n        if (error.message && error.message.includes('already been sent')) {\r\n          this.errorMessage = 'An invitation has already been sent to this user.';\r\n        } else if (error.message && error.message.includes('already a member')) {\r\n          this.errorMessage = 'This user is already a member of the group.';\r\n        } else if (error.message && error.message.includes('User not found')) {\r\n          this.errorMessage = 'User not found. Please check the username.';\r\n        } else {\r\n          this.errorMessage = 'Error sending invitation. Make sure the username is correct and the user is in your friends list.';\r\n        }\r\n\r\n        setTimeout(() => this.errorMessage = '', 3000);\r\n      });\r\n  }\r\n\r\n  generateInvitationCode() {\r\n    if (!this.groupId || !this.isAdmin) return;\r\n\r\n    this.groupService.generateInvitationCode(this.groupId)\r\n      .then(code => {\r\n        this.invitationCode = code;\r\n        this.codeIsValid = true;\r\n      })\r\n      .catch(error => {\r\n        this.errorMessage = 'Error generating invitation code.';\r\n        setTimeout(() => this.errorMessage = '', 3000);\r\n      });\r\n  }\r\n\r\n  checkInvitationCode() {\r\n    if (!this.groupId) return;\r\n\r\n    this.groupService.getGroup(this.groupId).pipe(take(1)).subscribe(group => {\r\n      if (group && group.invitation_code && group.code_expiry) {\r\n        const expiryDate = new Date(group.code_expiry);\r\n        if (expiryDate > new Date()) {\r\n          this.invitationCode = group.invitation_code;\r\n          this.codeIsValid = true;\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  loadAlreadyInvitedUsernames() {\r\n    if (!this.groupId) return;\r\n\r\n    this.supabaseService.supabase\r\n      .from('group_join_requests')\r\n      .select('username_invited')\r\n      .eq('group_id', this.groupId)\r\n      .then(response => {\r\n        if (response.error) {\r\n          return;\r\n        }\r\n\r\n        this.alreadyInvitedUsernames = response.data.map(invite => invite.username_invited);\r\n      });\r\n  }\r\n\r\n  loadValidFriendUsernames() {\r\n    if (!this.userId) return;\r\n\r\n    this.friendService.getFriendsWithProfiles(this.userId)\r\n      .pipe(take(1))\r\n      .subscribe(friends => {\r\n        if (!friends || friends.length === 0) {\r\n          return;\r\n        }\r\n\r\n        this.validFriendUsernames = friends\r\n          .filter(friend => friend.profile && friend.profile.username)\r\n          .map(friend => friend.profile.username);\r\n\r\n      });\r\n  }\r\n\r\n  updateSideQuestSettings() {\r\n    if (!this.groupId || !this.group || !this.isAdmin) return;\r\n\r\n\r\n    this.groupService.updateGroup(this.groupId, {\r\n      enable_sidequests: this.group.enable_sidequests\r\n    }).then(() => {\r\n      this.successMessage = 'Side quest settings updated successfully!';\r\n      setTimeout(() => this.successMessage = '', 3000);\r\n\r\n      this.loadGroup();\r\n    }).catch(error => {\r\n      this.errorMessage = 'Error updating side quest settings.';\r\n      setTimeout(() => this.errorMessage = '', 3000);\r\n    });\r\n  }\r\n\r\n  editMemberNickname(member: MemberWithProfile) {\r\n    this.editingMember = { ...member };\r\n    this.showEditNicknameModal = true;\r\n  }\r\n\r\n  updateMemberNickname() {\r\n    if (!this.groupId || !this.isAdmin || !this.editingMember.id) return;\r\n\r\n    this.groupService.updateGroupMember(this.editingMember.id, {\r\n      nickname: this.editingMember.nickname.trim()\r\n    }).then(() => {\r\n      this.successMessage = 'Member nickname updated successfully!';\r\n      this.showEditNicknameModal = false;\r\n\r\n      const index = this.members.findIndex(m => m.id === this.editingMember.id);\r\n      if (index !== -1) {\r\n        this.members[index].nickname = this.editingMember.nickname.trim();\r\n      }\r\n\r\n      setTimeout(() => this.successMessage = '', 3000);\r\n    }).catch(error => {\r\n      this.errorMessage = 'Error updating member nickname.';\r\n      setTimeout(() => this.errorMessage = '', 3000);\r\n    });\r\n  }\r\n\r\n  removeMember(member: MemberWithProfile) {\r\n    if (!this.groupId || !this.isAdmin || !member.id) return;\r\n\r\n    if (confirm(`Are you sure you want to remove ${member.nickname} from the group?`)) {\r\n      this.groupService.removeGroupMemberById(this.groupId, member.id)\r\n        .then(() => {\r\n          this.successMessage = `${member.nickname} has been removed from the group.`;\r\n\r\n          this.members = this.members.filter(m => m.id !== member.id);\r\n\r\n          setTimeout(() => this.successMessage = '', 3000);\r\n        })\r\n        .catch(error => {\r\n          this.errorMessage = 'Error removing member from group.';\r\n          setTimeout(() => this.errorMessage = '', 3000);\r\n        });\r\n    }\r\n  }\r\n\r\n  leaveGroup() {\r\n    if (!this.groupId || !this.userId) return;\r\n\r\n    if (confirm('Are you sure you want to leave this group? You will need to be invited again to rejoin.')) {\r\n      this.groupService.leaveGroup(this.groupId)\r\n        .then(() => {\r\n          this.router.navigate(['/groups']);\r\n        })\r\n        .catch(error => {\r\n          this.errorMessage = 'Error leaving group.';\r\n          setTimeout(() => this.errorMessage = '', 3000);\r\n        });\r\n    }\r\n  }\r\n\r\n  confirmDeleteGroup() {\r\n    if (!this.groupId || !this.isAdmin) return;\r\n\r\n    if (confirm('Are you sure you want to delete this group? This action cannot be undone and all group data will be permanently deleted.')) {\r\n      this.groupService.deleteGroup(this.groupId)\r\n        .then(() => {\r\n          this.router.navigate(['/groups']);\r\n        })\r\n        .catch(error => {\r\n          this.errorMessage = 'Error deleting group.';\r\n          setTimeout(() => this.errorMessage = '', 3000);\r\n        });\r\n    }\r\n  }\r\n\r\n  selectUsername(username: string) {\r\n    this.inviteUsername = username;\r\n    this.usernameSuggestions = [];\r\n\r\n    const trimmedUsername = username.trim();\r\n\r\n    const isFormatValid = trimmedUsername !== '' &&\r\n                         this.friendService.validateUsernameFormat(trimmedUsername);\r\n\r\n    const isAlreadyInvited = this.alreadyInvitedUsernames.includes(trimmedUsername);\r\n\r\n    const isFriend = this.validFriendUsernames.includes(trimmedUsername);\r\n\r\n    this.isUsernameValid = isFormatValid && !isAlreadyInvited && isFriend;\r\n\r\n    if (isAlreadyInvited) {\r\n      this.errorMessage = 'An invitation has already been sent to this user.';\r\n      setTimeout(() => this.errorMessage = '', 3000);\r\n    }\r\n    else if (!isFriend && isFormatValid) {\r\n      this.errorMessage = 'You can only invite users from your friends list.';\r\n      setTimeout(() => this.errorMessage = '', 3000);\r\n    }\r\n  }\r\n\r\n  onUsernameInput() {\r\n\r\n    this.invitationSentMessage = '';\r\n\r\n    const trimmedUsername = this.inviteUsername.trim();\r\n\r\n    const isFormatValid = trimmedUsername !== '' &&\r\n                         this.friendService.validateUsernameFormat(trimmedUsername);\r\n\r\n    const isAlreadyInvited = this.alreadyInvitedUsernames.includes(trimmedUsername);\r\n\r\n    const isFriend = this.validFriendUsernames.includes(trimmedUsername);\r\n\r\n    this.isUsernameValid = isFormatValid && !isAlreadyInvited && isFriend;\r\n\r\n    if (!this.userId || !this.groupId || !this.inviteUsername || this.inviteUsername.length < 2) {\r\n      this.usernameSuggestions = [];\r\n      return;\r\n    }\r\n\r\n    this.friendService.searchFriendsByUsername(this.userId, this.inviteUsername, this.groupId)\r\n      .pipe(take(1))\r\n      .subscribe(suggestions => {\r\n        this.usernameSuggestions = suggestions;\r\n      });\r\n  }\r\n}\r\n", "<div class=\"settings-container\">\r\n  <div class=\"settings-header\">\r\n    <a [routerLink]=\"['/groups', groupId]\" class=\"back-link\">\r\n      <span class=\"back-arrow\">←</span> Back to group\r\n    </a>\r\n    <h1>Settings</h1>\r\n    <div class=\"group-level-badge\" *ngIf=\"group\">Level {{ group.level }}</div>\r\n  </div>\r\n\r\n  <!-- Messages -->\r\n  <div class=\"messages\" *ngIf=\"successMessage || errorMessage\">\r\n    <div class=\"message success\" *ngIf=\"successMessage\">{{ successMessage }}</div>\r\n    <div class=\"message error\" *ngIf=\"errorMessage\">{{ errorMessage }}</div>\r\n  </div>\r\n\r\n  <!-- XP Section -->\r\n  <div class=\"settings-section\" *ngIf=\"group\">\r\n    <div class=\"section-header\">\r\n      <span class=\"section-icon\">📊</span>\r\n      <h2>Group XP</h2>\r\n    </div>\r\n    <div class=\"xp-section\">\r\n      <div class=\"category-card\">\r\n        <div class=\"category-header\">\r\n          <div class=\"category-icon\">💪</div>\r\n          <div class=\"category-name\">Strength</div>\r\n        </div>\r\n        <div class=\"xp-progress\">\r\n          <div class=\"xp-value\">{{ group.strength_xp }}/{{ requiredXp }} XP</div>\r\n          <div class=\"progress-bar\">\r\n            <div class=\"progress-fill\" [style.width.%]=\"getProgressPercentage(group.strength_xp)\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"category-card\">\r\n        <div class=\"category-header\">\r\n          <div class=\"category-icon\">💰</div>\r\n          <div class=\"category-name\">Money</div>\r\n        </div>\r\n        <div class=\"xp-progress\">\r\n          <div class=\"xp-value\">{{ group.money_xp }}/{{ requiredXp }} XP</div>\r\n          <div class=\"progress-bar\">\r\n            <div class=\"progress-fill\" [style.width.%]=\"getProgressPercentage(group.money_xp)\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"category-card\">\r\n        <div class=\"category-header\">\r\n          <div class=\"category-icon\">❤️</div>\r\n          <div class=\"category-name\">Health</div>\r\n        </div>\r\n        <div class=\"xp-progress\">\r\n          <div class=\"xp-value\">{{ group.health_xp }}/{{ requiredXp }} XP</div>\r\n          <div class=\"progress-bar\">\r\n            <div class=\"progress-fill\" [style.width.%]=\"getProgressPercentage(group.health_xp)\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"category-card\">\r\n        <div class=\"category-header\">\r\n          <div class=\"category-icon\">🧠</div>\r\n          <div class=\"category-name\">Knowledge</div>\r\n        </div>\r\n        <div class=\"xp-progress\">\r\n          <div class=\"xp-value\">{{ group.knowledge_xp }}/{{ requiredXp }} XP</div>\r\n          <div class=\"progress-bar\">\r\n            <div class=\"progress-fill\" [style.width.%]=\"getProgressPercentage(group.knowledge_xp)\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Group Information Section (Admin only) -->\r\n  <div class=\"settings-section\" *ngIf=\"isAdmin && group\">\r\n    <div class=\"section-header\">\r\n      <span class=\"section-icon\">✏️</span>\r\n      <h2>Group Information</h2>\r\n    </div>\r\n    <div class=\"group-info-section\">\r\n      <form (ngSubmit)=\"updateGroupInfo()\" id=\"edit-group-form\">\r\n        <div style=\"display: flex;gap: 10px;\">\r\n          <div class=\"form-group\">\r\n            <input type=\"text\" name=\"emoji\" id=\"emoji\" [(ngModel)]=\"groupInfo.emoji\" appEmojiInput>\r\n          </div>\r\n          <div class=\"form-group\" style=\"width: 100%;\">\r\n            <input type=\"text\" name=\"name\" id=\"name\" [(ngModel)]=\"groupInfo.name\" (input)=\"checkGroupName()\" required>\r\n            <div *ngIf=\"nameError\" class=\"error-message\">{{ nameError }}</div>\r\n            <div *ngIf=\"nameChecking\" class=\"checking-message\">Checking availability...</div>\r\n            <div *ngIf=\"nameAvailable\" class=\"success-message\">Group name is available!</div>\r\n          </div>\r\n          <button type=\"submit\" class=\"save-btn\">Save</button>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Members Section -->\r\n  <div class=\"settings-section\" *ngIf=\"members.length > 0\">\r\n    <div class=\"section-header\">\r\n      <span class=\"section-icon\">👥</span>\r\n      <h2>Members</h2>\r\n    </div>\r\n    <div class=\"members-list\">\r\n      <div class=\"member-item\" *ngFor=\"let member of members\">\r\n        <div class=\"member-info\">\r\n          <ng-container *ngIf=\"member.user_id !== userId\">\r\n            <a (click)=\"navigateToProfile(member.user_id)\" class=\"member-avatar-link\" style=\"cursor: pointer;\">\r\n              <div class=\"member-avatar\">\r\n                <div class=\"default-avatar\" *ngIf=\"!member.profile_picture\">{{ member.nickname.charAt(0).toUpperCase() }}</div>\r\n                <img *ngIf=\"member.profile_picture\" [src]=\"member.profile_picture\" [alt]=\"member.nickname\">\r\n              </div>\r\n            </a>\r\n          </ng-container>\r\n          <ng-container *ngIf=\"member.user_id === userId\">\r\n            <div class=\"member-avatar\">\r\n              <div class=\"default-avatar\" *ngIf=\"!member.profile_picture\">{{ member.nickname.charAt(0).toUpperCase() }}</div>\r\n              <img *ngIf=\"member.profile_picture\" [src]=\"member.profile_picture\" [alt]=\"member.nickname\">\r\n            </div>\r\n          </ng-container>\r\n          <div class=\"member-details\">\r\n            <ng-container *ngIf=\"member.user_id !== userId\">\r\n              <a (click)=\"navigateToProfile(member.user_id)\" class=\"member-name-link\" style=\"cursor: pointer;\">\r\n                <span class=\"member-name\">{{ member.nickname }}</span>\r\n              </a>\r\n            </ng-container>\r\n            <ng-container *ngIf=\"member.user_id === userId\">\r\n              <span class=\"member-name\">{{ member.nickname }}</span>\r\n            </ng-container>\r\n            <!-- <span class=\"member-username\" *ngIf=\"member.username && member.username !== member.nickname\">({{ member.username }})</span> -->\r\n            <span class=\"admin-badge\" *ngIf=\"member.is_admin\">Admin</span>\r\n            <span class=\"member-joined\">Joined {{ member.joined_date | date:'MMM d, y' }}</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"member-actions\">\r\n          <button *ngIf=\"member.user_id === userId\" class=\"leave-btn\" (click)=\"leaveGroup()\">Leave Group</button>\r\n          <ng-container *ngIf=\"isAdmin && member.user_id !== userId\">\r\n            <button class=\"edit-nickname-btn\" (click)=\"editMemberNickname(member)\">Edit</button>\r\n            <button class=\"remove-btn\" (click)=\"removeMember(member)\">Remove</button>\r\n          </ng-container>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Invite Section (Admin only) -->\r\n  <div class=\"settings-section\" *ngIf=\"isAdmin\">\r\n    <div class=\"section-header\">\r\n      <span class=\"section-icon\">➕</span>\r\n      <h2>Invite</h2>\r\n    </div>\r\n    <div class=\"invite-section\">\r\n      <div class=\"invite-tabs\">\r\n        <button class=\"invite-tab\" [class.active]=\"activeTab === 'friends'\" (click)=\"setActiveTab('friends')\">Invite Friends</button>\r\n        <button class=\"invite-tab\" [class.active]=\"activeTab === 'code'\" (click)=\"setActiveTab('code')\">Generate Code</button>\r\n      </div>\r\n\r\n      <div class=\"invite-tab-content\" id=\"friends-tab\" *ngIf=\"activeTab === 'friends'\">\r\n        <p>Invite your friends to join this group:</p>\r\n        <form (ngSubmit)=\"inviteFriend()\" id=\"invite-form\" #inviteForm=\"ngForm\">\r\n          <div class=\"invite-input-container\">\r\n            <input\r\n              type=\"text\"\r\n              name=\"username\"\r\n              id=\"username\"\r\n              [(ngModel)]=\"inviteUsername\"\r\n              (input)=\"onUsernameInput()\"\r\n              placeholder=\"Username to invite\"\r\n              pattern=\"^[\\w.@+-]+$\"\r\n              title=\"Username should only contain letters, numbers, and &#64;/./+/-/_ characters\"\r\n              required\r\n              #usernameInput=\"ngModel\"\r\n              autocomplete=\"off\">\r\n            <div id=\"username-suggestions\" class=\"username-suggestions\" *ngIf=\"usernameSuggestions.length > 0\">\r\n              <div class=\"suggestion-item\" *ngFor=\"let suggestion of usernameSuggestions\" (click)=\"selectUsername(suggestion)\">\r\n                {{ suggestion }}\r\n              </div>\r\n            </div>\r\n            <div *ngIf=\"(usernameInput.invalid && (usernameInput.dirty || usernameInput.touched)) ||\r\n                     (usernameInput.valid && inviteUsername.trim() &&\r\n                      (alreadyInvitedUsernames.includes(inviteUsername.trim()) ||\r\n                       !validFriendUsernames.includes(inviteUsername.trim())))\"\r\n                 class=\"validation-error\">\r\n              <div *ngIf=\"usernameInput.errors?.['pattern']\">\r\n                Username should only contain letters, numbers, and &#64;/./+/-/_ characters\r\n              </div>\r\n              <div *ngIf=\"usernameInput.errors?.['required']\">\r\n                Username is required\r\n              </div>\r\n              <div *ngIf=\"usernameInput.valid && inviteUsername.trim() && alreadyInvitedUsernames.includes(inviteUsername.trim())\">\r\n                An invitation has already been sent to this user\r\n              </div>\r\n              <div *ngIf=\"usernameInput.valid && inviteUsername.trim() &&\r\n                          !alreadyInvitedUsernames.includes(inviteUsername.trim()) &&\r\n                          !validFriendUsernames.includes(inviteUsername.trim())\">\r\n                You can only invite users from your friends list\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <button type=\"submit\" class=\"invite-btn\" [disabled]=\"inviteForm.invalid ||\r\n                                                  (inviteUsername.trim() &&\r\n                                                   (alreadyInvitedUsernames.includes(inviteUsername.trim()) ||\r\n                                                    !validFriendUsernames.includes(inviteUsername.trim())))\">Invite Friend</button>\r\n        </form>\r\n        <div *ngIf=\"invitationSentMessage\" class=\"invitation-sent-message\">\r\n          <div class=\"message success\">{{ invitationSentMessage }}</div>\r\n        </div>\r\n        <div class=\"invite-note\">Note: You can only invite users from your friends list.</div>\r\n      </div>\r\n\r\n      <div class=\"invite-tab-content\" id=\"code-tab\" *ngIf=\"activeTab === 'code'\">\r\n        <div class=\"code-header\">\r\n          <div class=\"section-header\">\r\n            <span class=\"section-icon\">🔑</span>\r\n            <h3 style=\"margin: 0; font-size: 18px;\">Group Code</h3>\r\n          </div>\r\n          <p>Generate a code that anyone can use to join this group. The code will be valid for 24 hours.</p>\r\n        </div>\r\n        <div class=\"code-actions\" style=\"display: flex; flex-direction: column; gap: 15px;\">\r\n          <ng-container *ngIf=\"invitationCode && codeIsValid; else generateCodeBlock\">\r\n            <div class=\"code-display\">{{ invitationCode }}</div>\r\n            <div class=\"code-info\">This code is valid for 24 hours. Share it with friends to connect.</div>\r\n          </ng-container>\r\n          <ng-template #generateCodeBlock>\r\n            <button (click)=\"generateInvitationCode()\" class=\"invite-btn\" style=\"width: 100%;\">Generate Group Code</button>\r\n          </ng-template>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Side Quest Settings (Admin only) -->\r\n  <div class=\"settings-section\" *ngIf=\"isAdmin && group\">\r\n    <div class=\"section-header\">\r\n      <span class=\"section-icon\">⚙️</span>\r\n      <h2>Side Quest Settings</h2>\r\n    </div>\r\n    <form (ngSubmit)=\"updateSideQuestSettings()\" class=\"sidequest-settings-form\">\r\n      <div class=\"setting-item\">\r\n        <label class=\"toggle-switch\">\r\n          <input type=\"checkbox\" name=\"enable_sidequests\" id=\"enable_sidequests\" [(ngModel)]=\"group.enable_sidequests\">\r\n          <span class=\"toggle-slider\"></span>\r\n        </label>\r\n        <span class=\"setting-label\">Enable Daily Side Quests</span>\r\n      </div>\r\n      <button type=\"submit\" class=\"save-settings-btn\">Save Settings</button>\r\n    </form>\r\n  </div>\r\n\r\n  <!-- Delete Group Section (Admin only) -->\r\n  <div class=\"settings-section danger-section\" *ngIf=\"isAdmin\">\r\n    <div class=\"section-header\">\r\n      <span class=\"section-icon\">⚠️</span>\r\n      <h2>Danger Zone</h2>\r\n    </div>\r\n    <div class=\"danger-actions\">\r\n      <button class=\"delete-btn\" (click)=\"confirmDeleteGroup()\">Delete Group</button>\r\n      <div class=\"danger-note\">This action cannot be undone. All group data will be permanently deleted.</div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<!-- Edit Nickname Modal -->\r\n<div id=\"edit-nickname-modal\" class=\"modal\" [style.display]=\"showEditNicknameModal ? 'block' : 'none'\">\r\n  <div class=\"modal-content\">\r\n    <span class=\"close-modal\" (click)=\"showEditNicknameModal = false\">&times;</span>\r\n    <h3>Edit Nickname</h3>\r\n    <form (ngSubmit)=\"updateMemberNickname()\">\r\n      <input type=\"text\" id=\"edit-nickname-input\" name=\"nickname\" [(ngModel)]=\"editingMember.nickname\" required>\r\n      <button type=\"submit\" class=\"save-nickname-btn\">Save</button>\r\n    </form>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;AAAA,SAAuCA,MAAM,QAAQ,eAAe;AACpE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAyB,gBAAgB;AAC7D,SAASC,cAAc,EAAEC,MAAM,EAAEC,YAAY,QAAQ,iBAAiB;AACtE,SAAuBC,IAAI,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,OAAO,EAAEC,GAAG,QAAQ,MAAM;AAE3F,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,SAAS,EAAEC,UAAU,QAAQ,8BAA8B;AACpE,SAAgBC,WAAW,QAAQ,iCAAiC;AACpE,SAASC,mBAAmB,QAAQ,2CAA2C;;;;;;;;;ICP3EC,EAAA,CAAAC,cAAA,cAA6C;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAA7BH,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAK,kBAAA,WAAAC,MAAA,CAAAC,KAAA,CAAAC,KAAA,KAAuB;;;;;IAKpER,EAAA,CAAAC,cAAA,cAAoD;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAA1BH,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAS,iBAAA,CAAAH,MAAA,CAAAI,cAAA,CAAoB;;;;;IACxEV,EAAA,CAAAC,cAAA,cAAgD;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAxBH,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAS,iBAAA,CAAAH,MAAA,CAAAK,YAAA,CAAkB;;;;;IAFpEX,EAAA,CAAAC,cAAA,cAA6D;IAE3DD,EADA,CAAAY,UAAA,IAAAC,sCAAA,kBAAoD,IAAAC,sCAAA,kBACJ;IAClDd,EAAA,CAAAG,YAAA,EAAM;;;;IAF0BH,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAAI,cAAA,CAAoB;IACtBV,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAAK,YAAA,CAAkB;;;;;IAM5CX,EAFJ,CAAAC,cAAA,cAA4C,cACd,eACC;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IACdF,EADc,CAAAG,YAAA,EAAK,EACb;IAIAH,EAHN,CAAAC,cAAA,cAAwB,cACK,cACI,cACA;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACnCH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IACrCF,EADqC,CAAAG,YAAA,EAAM,EACrC;IAEJH,EADF,CAAAC,cAAA,eAAyB,eACD;IAAAD,EAAA,CAAAE,MAAA,IAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvEH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAgB,SAAA,eAA4F;IAGlGhB,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAIFH,EAFJ,CAAAC,cAAA,eAA2B,eACI,eACA;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACnCH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAClCF,EADkC,CAAAG,YAAA,EAAM,EAClC;IAEJH,EADF,CAAAC,cAAA,eAAyB,eACD;IAAAD,EAAA,CAAAE,MAAA,IAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpEH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAgB,SAAA,eAAyF;IAG/FhB,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAIFH,EAFJ,CAAAC,cAAA,eAA2B,eACI,eACA;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACnCH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,cAAM;IACnCF,EADmC,CAAAG,YAAA,EAAM,EACnC;IAEJH,EADF,CAAAC,cAAA,eAAyB,eACD;IAAAD,EAAA,CAAAE,MAAA,IAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACrEH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAgB,SAAA,eAA0F;IAGhGhB,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAIFH,EAFJ,CAAAC,cAAA,eAA2B,eACI,eACA;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACnCH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IACtCF,EADsC,CAAAG,YAAA,EAAM,EACtC;IAEJH,EADF,CAAAC,cAAA,eAAyB,eACD;IAAAD,EAAA,CAAAE,MAAA,IAA4C;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACxEH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAgB,SAAA,eAA6F;IAKvGhB,EAJQ,CAAAG,YAAA,EAAM,EACF,EACF,EACF,EACF;;;;IA9CwBH,EAAA,CAAAI,SAAA,IAA2C;IAA3CJ,EAAA,CAAAiB,kBAAA,KAAAX,MAAA,CAAAC,KAAA,CAAAW,WAAA,OAAAZ,MAAA,CAAAa,UAAA,QAA2C;IAEpCnB,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAAoB,WAAA,UAAAd,MAAA,CAAAe,qBAAA,CAAAf,MAAA,CAAAC,KAAA,CAAAW,WAAA,OAA0D;IAWjElB,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAiB,kBAAA,KAAAX,MAAA,CAAAC,KAAA,CAAAe,QAAA,OAAAhB,MAAA,CAAAa,UAAA,QAAwC;IAEjCnB,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAoB,WAAA,UAAAd,MAAA,CAAAe,qBAAA,CAAAf,MAAA,CAAAC,KAAA,CAAAe,QAAA,OAAuD;IAW9DtB,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAiB,kBAAA,KAAAX,MAAA,CAAAC,KAAA,CAAAgB,SAAA,OAAAjB,MAAA,CAAAa,UAAA,QAAyC;IAElCnB,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAoB,WAAA,UAAAd,MAAA,CAAAe,qBAAA,CAAAf,MAAA,CAAAC,KAAA,CAAAgB,SAAA,OAAwD;IAW/DvB,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAiB,kBAAA,KAAAX,MAAA,CAAAC,KAAA,CAAAiB,YAAA,OAAAlB,MAAA,CAAAa,UAAA,QAA4C;IAErCnB,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAoB,WAAA,UAAAd,MAAA,CAAAe,qBAAA,CAAAf,MAAA,CAAAC,KAAA,CAAAiB,YAAA,OAA2D;;;;;IAqBtFxB,EAAA,CAAAC,cAAA,cAA6C;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAArBH,EAAA,CAAAI,SAAA,EAAe;IAAfJ,EAAA,CAAAS,iBAAA,CAAAH,MAAA,CAAAmB,SAAA,CAAe;;;;;IAC5DzB,EAAA,CAAAC,cAAA,cAAmD;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACjFH,EAAA,CAAAC,cAAA,cAAmD;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAbvFH,EAFJ,CAAAC,cAAA,cAAuD,cACzB,eACC;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IACvBF,EADuB,CAAAG,YAAA,EAAK,EACtB;IAEJH,EADF,CAAAC,cAAA,cAAgC,eAC4B;IAApDD,EAAA,CAAA0B,UAAA,sBAAAC,2DAAA;MAAA3B,EAAA,CAAA4B,aAAA,CAAAC,GAAA;MAAA,MAAAvB,MAAA,GAAAN,EAAA,CAAA8B,aAAA;MAAA,OAAA9B,EAAA,CAAA+B,WAAA,CAAYzB,MAAA,CAAA0B,eAAA,EAAiB;IAAA,EAAC;IAG9BhC,EAFJ,CAAAC,cAAA,cAAsC,cACZ,iBACiE;IAA5CD,EAAA,CAAAiC,gBAAA,2BAAAC,kEAAAC,MAAA;MAAAnC,EAAA,CAAA4B,aAAA,CAAAC,GAAA;MAAA,MAAAvB,MAAA,GAAAN,EAAA,CAAA8B,aAAA;MAAA9B,EAAA,CAAAoC,kBAAA,CAAA9B,MAAA,CAAA+B,SAAA,CAAAC,KAAA,EAAAH,MAAA,MAAA7B,MAAA,CAAA+B,SAAA,CAAAC,KAAA,GAAAH,MAAA;MAAA,OAAAnC,EAAA,CAAA+B,WAAA,CAAAI,MAAA;IAAA,EAA6B;IAC1EnC,EADE,CAAAG,YAAA,EAAuF,EACnF;IAEJH,EADF,CAAAC,cAAA,eAA6C,iBAC+D;IAAjED,EAAA,CAAAiC,gBAAA,2BAAAM,kEAAAJ,MAAA;MAAAnC,EAAA,CAAA4B,aAAA,CAAAC,GAAA;MAAA,MAAAvB,MAAA,GAAAN,EAAA,CAAA8B,aAAA;MAAA9B,EAAA,CAAAoC,kBAAA,CAAA9B,MAAA,CAAA+B,SAAA,CAAAG,IAAA,EAAAL,MAAA,MAAA7B,MAAA,CAAA+B,SAAA,CAAAG,IAAA,GAAAL,MAAA;MAAA,OAAAnC,EAAA,CAAA+B,WAAA,CAAAI,MAAA;IAAA,EAA4B;IAACnC,EAAA,CAAA0B,UAAA,mBAAAe,0DAAA;MAAAzC,EAAA,CAAA4B,aAAA,CAAAC,GAAA;MAAA,MAAAvB,MAAA,GAAAN,EAAA,CAAA8B,aAAA;MAAA,OAAA9B,EAAA,CAAA+B,WAAA,CAASzB,MAAA,CAAAoC,cAAA,EAAgB;IAAA,EAAC;IAAhG1C,EAAA,CAAAG,YAAA,EAA0G;IAG1GH,EAFA,CAAAY,UAAA,KAAA+B,wCAAA,kBAA6C,KAAAC,wCAAA,kBACM,KAAAC,wCAAA,kBACA;IACrD7C,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,kBAAuC;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAInDF,EAJmD,CAAAG,YAAA,EAAS,EAChD,EACD,EACH,EACF;;;;IAZ+CH,EAAA,CAAAI,SAAA,IAA6B;IAA7BJ,EAAA,CAAA8C,gBAAA,YAAAxC,MAAA,CAAA+B,SAAA,CAAAC,KAAA,CAA6B;IAG/BtC,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAA8C,gBAAA,YAAAxC,MAAA,CAAA+B,SAAA,CAAAG,IAAA,CAA4B;IAC/DxC,EAAA,CAAAI,SAAA,EAAe;IAAfJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAAmB,SAAA,CAAe;IACfzB,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAAyC,YAAA,CAAkB;IAClB/C,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAA0C,aAAA,CAAmB;;;;;IAoBrBhD,EAAA,CAAAC,cAAA,cAA4D;IAAAD,EAAA,CAAAE,MAAA,GAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAnDH,EAAA,CAAAI,SAAA,EAA6C;IAA7CJ,EAAA,CAAAS,iBAAA,CAAAwC,SAAA,CAAAC,QAAA,CAAAC,MAAA,IAAAC,WAAA,GAA6C;;;;;IACzGpD,EAAA,CAAAgB,SAAA,cAA2F;;;;IAAxBhB,EAA/B,CAAAe,UAAA,QAAAkC,SAAA,CAAAI,eAAA,EAAArD,EAAA,CAAAsD,aAAA,CAA8B,QAAAL,SAAA,CAAAC,QAAA,CAAwB;;;;;;IAJhGlD,EAAA,CAAAuD,uBAAA,GAAgD;IAC9CvD,EAAA,CAAAC,cAAA,YAAmG;IAAhGD,EAAA,CAAA0B,UAAA,mBAAA8B,0EAAA;MAAAxD,EAAA,CAAA4B,aAAA,CAAA6B,GAAA;MAAA,MAAAR,SAAA,GAAAjD,EAAA,CAAA8B,aAAA,GAAA4B,SAAA;MAAA,MAAApD,MAAA,GAAAN,EAAA,CAAA8B,aAAA;MAAA,OAAA9B,EAAA,CAAA+B,WAAA,CAASzB,MAAA,CAAAqD,iBAAA,CAAAV,SAAA,CAAAW,OAAA,CAAiC;IAAA,EAAC;IAC5C5D,EAAA,CAAAC,cAAA,cAA2B;IAEzBD,EADA,CAAAY,UAAA,IAAAiD,4DAAA,kBAA4D,IAAAC,4DAAA,kBAC+B;IAE/F9D,EADE,CAAAG,YAAA,EAAM,EACJ;;;;;IAH6BH,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAe,UAAA,UAAAkC,SAAA,CAAAI,eAAA,CAA6B;IACpDrD,EAAA,CAAAI,SAAA,EAA4B;IAA5BJ,EAAA,CAAAe,UAAA,SAAAkC,SAAA,CAAAI,eAAA,CAA4B;;;;;IAMpCrD,EAAA,CAAAC,cAAA,cAA4D;IAAAD,EAAA,CAAAE,MAAA,GAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAnDH,EAAA,CAAAI,SAAA,EAA6C;IAA7CJ,EAAA,CAAAS,iBAAA,CAAAwC,SAAA,CAAAC,QAAA,CAAAC,MAAA,IAAAC,WAAA,GAA6C;;;;;IACzGpD,EAAA,CAAAgB,SAAA,cAA2F;;;;IAAxBhB,EAA/B,CAAAe,UAAA,QAAAkC,SAAA,CAAAI,eAAA,EAAArD,EAAA,CAAAsD,aAAA,CAA8B,QAAAL,SAAA,CAAAC,QAAA,CAAwB;;;;;IAH9FlD,EAAA,CAAAuD,uBAAA,GAAgD;IAC9CvD,EAAA,CAAAC,cAAA,cAA2B;IAEzBD,EADA,CAAAY,UAAA,IAAAmD,4DAAA,kBAA4D,IAAAC,4DAAA,kBAC+B;IAC7FhE,EAAA,CAAAG,YAAA,EAAM;;;;;IAFyBH,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAe,UAAA,UAAAkC,SAAA,CAAAI,eAAA,CAA6B;IACpDrD,EAAA,CAAAI,SAAA,EAA4B;IAA5BJ,EAAA,CAAAe,UAAA,SAAAkC,SAAA,CAAAI,eAAA,CAA4B;;;;;;IAIpCrD,EAAA,CAAAuD,uBAAA,GAAgD;IAC9CvD,EAAA,CAAAC,cAAA,YAAiG;IAA9FD,EAAA,CAAA0B,UAAA,mBAAAuC,0EAAA;MAAAjE,EAAA,CAAA4B,aAAA,CAAAsC,GAAA;MAAA,MAAAjB,SAAA,GAAAjD,EAAA,CAAA8B,aAAA,GAAA4B,SAAA;MAAA,MAAApD,MAAA,GAAAN,EAAA,CAAA8B,aAAA;MAAA,OAAA9B,EAAA,CAAA+B,WAAA,CAASzB,MAAA,CAAAqD,iBAAA,CAAAV,SAAA,CAAAW,OAAA,CAAiC;IAAA,EAAC;IAC5C5D,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IACjDF,EADiD,CAAAG,YAAA,EAAO,EACpD;;;;;IADwBH,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAS,iBAAA,CAAAwC,SAAA,CAAAC,QAAA,CAAqB;;;;;IAGnDlD,EAAA,CAAAuD,uBAAA,GAAgD;IAC9CvD,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAA5BH,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAS,iBAAA,CAAAwC,SAAA,CAAAC,QAAA,CAAqB;;;;;IAGjDlD,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAKhEH,EAAA,CAAAC,cAAA,iBAAmF;IAAvBD,EAAA,CAAA0B,UAAA,mBAAAyC,0EAAA;MAAAnE,EAAA,CAAA4B,aAAA,CAAAwC,GAAA;MAAA,MAAA9D,MAAA,GAAAN,EAAA,CAAA8B,aAAA;MAAA,OAAA9B,EAAA,CAAA+B,WAAA,CAASzB,MAAA,CAAA+D,UAAA,EAAY;IAAA,EAAC;IAACrE,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACvGH,EAAA,CAAAuD,uBAAA,GAA2D;IACzDvD,EAAA,CAAAC,cAAA,iBAAuE;IAArCD,EAAA,CAAA0B,UAAA,mBAAA4C,gFAAA;MAAAtE,EAAA,CAAA4B,aAAA,CAAA2C,GAAA;MAAA,MAAAtB,SAAA,GAAAjD,EAAA,CAAA8B,aAAA,GAAA4B,SAAA;MAAA,MAAApD,MAAA,GAAAN,EAAA,CAAA8B,aAAA;MAAA,OAAA9B,EAAA,CAAA+B,WAAA,CAASzB,MAAA,CAAAkE,kBAAA,CAAAvB,SAAA,CAA0B;IAAA,EAAC;IAACjD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpFH,EAAA,CAAAC,cAAA,iBAA0D;IAA/BD,EAAA,CAAA0B,UAAA,mBAAA+C,gFAAA;MAAAzE,EAAA,CAAA4B,aAAA,CAAA2C,GAAA;MAAA,MAAAtB,SAAA,GAAAjD,EAAA,CAAA8B,aAAA,GAAA4B,SAAA;MAAA,MAAApD,MAAA,GAAAN,EAAA,CAAA8B,aAAA;MAAA,OAAA9B,EAAA,CAAA+B,WAAA,CAASzB,MAAA,CAAAoE,YAAA,CAAAzB,SAAA,CAAoB;IAAA,EAAC;IAACjD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAjC7EH,EADF,CAAAC,cAAA,cAAwD,cAC7B;IASvBD,EARA,CAAAY,UAAA,IAAA+D,sDAAA,2BAAgD,IAAAC,sDAAA,2BAQA;IAMhD5E,EAAA,CAAAC,cAAA,cAA4B;IAU1BD,EATA,CAAAY,UAAA,IAAAiE,sDAAA,2BAAgD,IAAAC,sDAAA,2BAKA,IAAAC,8CAAA,mBAIE;IAClD/E,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAiD;;IAEjFF,EAFiF,CAAAG,YAAA,EAAO,EAChF,EACF;IACNH,EAAA,CAAAC,cAAA,eAA4B;IAE1BD,EADA,CAAAY,UAAA,KAAAoE,iDAAA,qBAAmF,KAAAC,uDAAA,2BACxB;IAK/DjF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAnCaH,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAe,UAAA,SAAAkC,SAAA,CAAAW,OAAA,KAAAtD,MAAA,CAAA4E,MAAA,CAA+B;IAQ/BlF,EAAA,CAAAI,SAAA,EAA+B;IAA/BJ,EAAA,CAAAe,UAAA,SAAAkC,SAAA,CAAAW,OAAA,KAAAtD,MAAA,CAAA4E,MAAA,CAA+B;IAO7BlF,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAe,UAAA,SAAAkC,SAAA,CAAAW,OAAA,KAAAtD,MAAA,CAAA4E,MAAA,CAA+B;IAK/BlF,EAAA,CAAAI,SAAA,EAA+B;IAA/BJ,EAAA,CAAAe,UAAA,SAAAkC,SAAA,CAAAW,OAAA,KAAAtD,MAAA,CAAA4E,MAAA,CAA+B;IAInBlF,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAe,UAAA,SAAAkC,SAAA,CAAAkC,QAAA,CAAqB;IACpBnF,EAAA,CAAAI,SAAA,GAAiD;IAAjDJ,EAAA,CAAAK,kBAAA,YAAAL,EAAA,CAAAoF,WAAA,QAAAnC,SAAA,CAAAoC,WAAA,kBAAiD;IAItErF,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAe,UAAA,SAAAkC,SAAA,CAAAW,OAAA,KAAAtD,MAAA,CAAA4E,MAAA,CAA+B;IACzBlF,EAAA,CAAAI,SAAA,EAA0C;IAA1CJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAAgF,OAAA,IAAArC,SAAA,CAAAW,OAAA,KAAAtD,MAAA,CAAA4E,MAAA,CAA0C;;;;;IApC7DlF,EAFJ,CAAAC,cAAA,cAAyD,cAC3B,eACC;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,cAAO;IACbF,EADa,CAAAG,YAAA,EAAK,EACZ;IACNH,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAY,UAAA,IAAA2E,uCAAA,oBAAwD;IAuC5DvF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAvC0CH,EAAA,CAAAI,SAAA,GAAU;IAAVJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAkF,OAAA,CAAU;;;;;;IAsE9CxF,EAAA,CAAAC,cAAA,cAAiH;IAArCD,EAAA,CAAA0B,UAAA,mBAAA+D,0EAAA;MAAA,MAAAC,cAAA,GAAA1F,EAAA,CAAA4B,aAAA,CAAA+D,IAAA,EAAAjC,SAAA;MAAA,MAAApD,MAAA,GAAAN,EAAA,CAAA8B,aAAA;MAAA,OAAA9B,EAAA,CAAA+B,WAAA,CAASzB,MAAA,CAAAsF,cAAA,CAAAF,cAAA,CAA0B;IAAA,EAAC;IAC9G1F,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAqF,cAAA,MACF;;;;;IAHF1F,EAAA,CAAAC,cAAA,cAAmG;IACjGD,EAAA,CAAAY,UAAA,IAAAiF,oDAAA,kBAAiH;IAGnH7F,EAAA,CAAAG,YAAA,EAAM;;;;IAHgDH,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAAwF,mBAAA,CAAsB;;;;;IAS1E9F,EAAA,CAAAC,cAAA,UAA+C;IAC7CD,EAAA,CAAAE,MAAA,gFACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,UAAgD;IAC9CD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,UAAqH;IACnHD,EAAA,CAAAE,MAAA,yDACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,UAEmE;IACjED,EAAA,CAAAE,MAAA,yDACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAlBRH,EAAA,CAAAC,cAAA,cAI8B;IAU5BD,EATA,CAAAY,UAAA,IAAAmF,oDAAA,kBAA+C,IAAAC,oDAAA,kBAGC,IAAAC,oDAAA,kBAGqE,IAAAC,oDAAA,kBAKlD;IAGrElG,EAAA,CAAAG,YAAA,EAAM;;;;;;IAdEH,EAAA,CAAAI,SAAA,EAAuC;IAAvCJ,EAAA,CAAAe,UAAA,SAAAoF,iBAAA,CAAAC,MAAA,kBAAAD,iBAAA,CAAAC,MAAA,YAAuC;IAGvCpG,EAAA,CAAAI,SAAA,EAAwC;IAAxCJ,EAAA,CAAAe,UAAA,SAAAoF,iBAAA,CAAAC,MAAA,kBAAAD,iBAAA,CAAAC,MAAA,aAAwC;IAGxCpG,EAAA,CAAAI,SAAA,EAA6G;IAA7GJ,EAAA,CAAAe,UAAA,SAAAoF,iBAAA,CAAAE,KAAA,IAAA/F,MAAA,CAAAgG,cAAA,CAAAC,IAAA,MAAAjG,MAAA,CAAAkG,uBAAA,CAAAC,QAAA,CAAAnG,MAAA,CAAAgG,cAAA,CAAAC,IAAA,IAA6G;IAG7GvG,EAAA,CAAAI,SAAA,EAEyD;IAFzDJ,EAAA,CAAAe,UAAA,SAAAoF,iBAAA,CAAAE,KAAA,IAAA/F,MAAA,CAAAgG,cAAA,CAAAC,IAAA,OAAAjG,MAAA,CAAAkG,uBAAA,CAAAC,QAAA,CAAAnG,MAAA,CAAAgG,cAAA,CAAAC,IAAA,QAAAjG,MAAA,CAAAoG,oBAAA,CAAAD,QAAA,CAAAnG,MAAA,CAAAgG,cAAA,CAAAC,IAAA,IAEyD;;;;;IAWnEvG,EADF,CAAAC,cAAA,cAAmE,cACpC;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAC1DF,EAD0D,CAAAG,YAAA,EAAM,EAC1D;;;;IADyBH,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAS,iBAAA,CAAAH,MAAA,CAAAqG,qBAAA,CAA2B;;;;;;IA/C1D3G,EADF,CAAAC,cAAA,cAAiF,QAC5E;IAAAD,EAAA,CAAAE,MAAA,8CAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC9CH,EAAA,CAAAC,cAAA,kBAAwE;IAAlED,EAAA,CAAA0B,UAAA,sBAAAkF,kEAAA;MAAA5G,EAAA,CAAA4B,aAAA,CAAAiF,GAAA;MAAA,MAAAvG,MAAA,GAAAN,EAAA,CAAA8B,aAAA;MAAA,OAAA9B,EAAA,CAAA+B,WAAA,CAAYzB,MAAA,CAAAwG,YAAA,EAAc;IAAA,EAAC;IAE7B9G,EADF,CAAAC,cAAA,cAAoC,mBAYb;IAPnBD,EAAA,CAAAiC,gBAAA,2BAAA8E,wEAAA5E,MAAA;MAAAnC,EAAA,CAAA4B,aAAA,CAAAiF,GAAA;MAAA,MAAAvG,MAAA,GAAAN,EAAA,CAAA8B,aAAA;MAAA9B,EAAA,CAAAoC,kBAAA,CAAA9B,MAAA,CAAAgG,cAAA,EAAAnE,MAAA,MAAA7B,MAAA,CAAAgG,cAAA,GAAAnE,MAAA;MAAA,OAAAnC,EAAA,CAAA+B,WAAA,CAAAI,MAAA;IAAA,EAA4B;IAC5BnC,EAAA,CAAA0B,UAAA,mBAAAsF,gEAAA;MAAAhH,EAAA,CAAA4B,aAAA,CAAAiF,GAAA;MAAA,MAAAvG,MAAA,GAAAN,EAAA,CAAA8B,aAAA;MAAA,OAAA9B,EAAA,CAAA+B,WAAA,CAASzB,MAAA,CAAA2G,eAAA,EAAiB;IAAA,EAAC;IAL7BjH,EAAA,CAAAG,YAAA,EAWqB;IAMrBH,EALA,CAAAY,UAAA,IAAAsG,8CAAA,kBAAmG,IAAAC,8CAAA,kBASrE;IAgBhCnH,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,kBAGmG;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAClHF,EADkH,CAAAG,YAAA,EAAS,EACpH;IACPH,EAAA,CAAAY,UAAA,KAAAwG,+CAAA,kBAAmE;IAGnEpH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,+DAAuD;IAClFF,EADkF,CAAAG,YAAA,EAAM,EAClF;;;;;;IA3CEH,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAA8C,gBAAA,YAAAxC,MAAA,CAAAgG,cAAA,CAA4B;IAQ+BtG,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAAwF,mBAAA,CAAAuB,MAAA,KAAoC;IAK3FrH,EAAA,CAAAI,SAAA,EAGyD;IAHzDJ,EAAA,CAAAe,UAAA,SAAAoF,iBAAA,CAAAmB,OAAA,KAAAnB,iBAAA,CAAAoB,KAAA,IAAApB,iBAAA,CAAAqB,OAAA,KAAArB,iBAAA,CAAAE,KAAA,IAAA/F,MAAA,CAAAgG,cAAA,CAAAC,IAAA,OAAAjG,MAAA,CAAAkG,uBAAA,CAAAC,QAAA,CAAAnG,MAAA,CAAAgG,cAAA,CAAAC,IAAA,QAAAjG,MAAA,CAAAoG,oBAAA,CAAAD,QAAA,CAAAnG,MAAA,CAAAgG,cAAA,CAAAC,IAAA,KAGyD;IAkBxBvG,EAAA,CAAAI,SAAA,EAGyD;IAHzDJ,EAAA,CAAAe,UAAA,aAAA0G,cAAA,CAAAH,OAAA,IAAAhH,MAAA,CAAAgG,cAAA,CAAAC,IAAA,OAAAjG,MAAA,CAAAkG,uBAAA,CAAAC,QAAA,CAAAnG,MAAA,CAAAgG,cAAA,CAAAC,IAAA,QAAAjG,MAAA,CAAAoG,oBAAA,CAAAD,QAAA,CAAAnG,MAAA,CAAAgG,cAAA,CAAAC,IAAA,KAGyD;IAE9FvG,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAAqG,qBAAA,CAA2B;;;;;IAe/B3G,EAAA,CAAAuD,uBAAA,GAA4E;IAC1EvD,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpDH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,yEAAkE;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IADrEH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAS,iBAAA,CAAAH,MAAA,CAAAoH,cAAA,CAAoB;;;;;;IAI9C1H,EAAA,CAAAC,cAAA,iBAAmF;IAA3ED,EAAA,CAAA0B,UAAA,mBAAAiG,gFAAA;MAAA3H,EAAA,CAAA4B,aAAA,CAAAgG,IAAA;MAAA,MAAAtH,MAAA,GAAAN,EAAA,CAAA8B,aAAA;MAAA,OAAA9B,EAAA,CAAA+B,WAAA,CAASzB,MAAA,CAAAuH,sBAAA,EAAwB;IAAA,EAAC;IAAyC7H,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAX/GH,EAHN,CAAAC,cAAA,cAA2E,cAChD,cACK,eACC;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpCH,EAAA,CAAAC,cAAA,aAAwC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IACpDF,EADoD,CAAAG,YAAA,EAAK,EACnD;IACNH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,mGAA4F;IACjGF,EADiG,CAAAG,YAAA,EAAI,EAC/F;IACNH,EAAA,CAAAC,cAAA,cAAoF;IAKlFD,EAJA,CAAAY,UAAA,KAAAkH,wDAAA,2BAA4E,KAAAC,uDAAA,gCAAA/H,EAAA,CAAAgI,sBAAA,CAI5C;IAIpChI,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IARaH,EAAA,CAAAI,SAAA,IAAqC;IAAAJ,EAArC,CAAAe,UAAA,SAAAT,MAAA,CAAAoH,cAAA,IAAApH,MAAA,CAAA2H,WAAA,CAAqC,aAAAC,qBAAA,CAAsB;;;;;;IAvE9ElI,EAFJ,CAAAC,cAAA,cAA8C,cAChB,eACC;IAAAD,EAAA,CAAAE,MAAA,aAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,aAAM;IACZF,EADY,CAAAG,YAAA,EAAK,EACX;IAGFH,EAFJ,CAAAC,cAAA,cAA4B,cACD,iBAC+E;IAAlCD,EAAA,CAAA0B,UAAA,mBAAAyG,0DAAA;MAAAnI,EAAA,CAAA4B,aAAA,CAAAwG,GAAA;MAAA,MAAA9H,MAAA,GAAAN,EAAA,CAAA8B,aAAA;MAAA,OAAA9B,EAAA,CAAA+B,WAAA,CAASzB,MAAA,CAAA+H,YAAA,CAAa,SAAS,CAAC;IAAA,EAAC;IAACrI,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7HH,EAAA,CAAAC,cAAA,kBAAgG;IAA/BD,EAAA,CAAA0B,UAAA,mBAAA4G,2DAAA;MAAAtI,EAAA,CAAA4B,aAAA,CAAAwG,GAAA;MAAA,MAAA9H,MAAA,GAAAN,EAAA,CAAA8B,aAAA;MAAA,OAAA9B,EAAA,CAAA+B,WAAA,CAASzB,MAAA,CAAA+H,YAAA,CAAa,MAAM,CAAC;IAAA,EAAC;IAACrI,EAAA,CAAAE,MAAA,qBAAa;IAC/GF,EAD+G,CAAAG,YAAA,EAAS,EAClH;IAuDNH,EArDA,CAAAY,UAAA,KAAA2H,wCAAA,mBAAiF,KAAAC,wCAAA,mBAqDN;IAmB/ExI,EADE,CAAAG,YAAA,EAAM,EACF;;;;IA5E2BH,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAyI,WAAA,WAAAnI,MAAA,CAAAoI,SAAA,eAAwC;IACxC1I,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAyI,WAAA,WAAAnI,MAAA,CAAAoI,SAAA,YAAqC;IAGhB1I,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAAoI,SAAA,eAA6B;IAqDhC1I,EAAA,CAAAI,SAAA,EAA0B;IAA1BJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAAoI,SAAA,YAA0B;;;;;;IAwBzE1I,EAFJ,CAAAC,cAAA,cAAuD,cACzB,eACC;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IACzBF,EADyB,CAAAG,YAAA,EAAK,EACxB;IACNH,EAAA,CAAAC,cAAA,eAA6E;IAAvED,EAAA,CAAA0B,UAAA,sBAAAiH,2DAAA;MAAA3I,EAAA,CAAA4B,aAAA,CAAAgH,IAAA;MAAA,MAAAtI,MAAA,GAAAN,EAAA,CAAA8B,aAAA;MAAA,OAAA9B,EAAA,CAAA+B,WAAA,CAAYzB,MAAA,CAAAuI,uBAAA,EAAyB;IAAA,EAAC;IAGtC7I,EAFJ,CAAAC,cAAA,cAA0B,iBACK,iBACkF;IAAtCD,EAAA,CAAAiC,gBAAA,2BAAA6G,iEAAA3G,MAAA;MAAAnC,EAAA,CAAA4B,aAAA,CAAAgH,IAAA;MAAA,MAAAtI,MAAA,GAAAN,EAAA,CAAA8B,aAAA;MAAA9B,EAAA,CAAAoC,kBAAA,CAAA9B,MAAA,CAAAC,KAAA,CAAAwI,iBAAA,EAAA5G,MAAA,MAAA7B,MAAA,CAAAC,KAAA,CAAAwI,iBAAA,GAAA5G,MAAA;MAAA,OAAAnC,EAAA,CAAA+B,WAAA,CAAAI,MAAA;IAAA,EAAqC;IAA5GnC,EAAA,CAAAG,YAAA,EAA6G;IAC7GH,EAAA,CAAAgB,SAAA,iBAAmC;IACrChB,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAA4B;IAAAD,EAAA,CAAAE,MAAA,gCAAwB;IACtDF,EADsD,CAAAG,YAAA,EAAO,EACvD;IACNH,EAAA,CAAAC,cAAA,mBAAgD;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAEjEF,EAFiE,CAAAG,YAAA,EAAS,EACjE,EACH;;;;IAPyEH,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAA8C,gBAAA,YAAAxC,MAAA,CAAAC,KAAA,CAAAwI,iBAAA,CAAqC;;;;;;IAYhH/I,EAFJ,CAAAC,cAAA,eAA6D,cAC/B,eACC;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IACjBF,EADiB,CAAAG,YAAA,EAAK,EAChB;IAEJH,EADF,CAAAC,cAAA,eAA4B,kBACgC;IAA/BD,EAAA,CAAA0B,UAAA,mBAAAsH,0DAAA;MAAAhJ,EAAA,CAAA4B,aAAA,CAAAqH,IAAA;MAAA,MAAA3I,MAAA,GAAAN,EAAA,CAAA8B,aAAA;MAAA,OAAA9B,EAAA,CAAA+B,WAAA,CAASzB,MAAA,CAAA4I,kBAAA,EAAoB;IAAA,EAAC;IAAClJ,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC/EH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,iFAAyE;IAEtGF,EAFsG,CAAAG,YAAA,EAAM,EACpG,EACF;;;ADvOR,OAAM,MAAOgJ,iBAAiB;EAoD5BC,YAAA;IAnDA,KAAAlE,MAAM,GAAkB,IAAI;IAE5B,KAAAmE,OAAO,GAAkB,IAAI;IAC7B,KAAA9I,KAAK,GAA+B,IAAI;IACxC,KAAAiF,OAAO,GAAwB,EAAE;IACjC,KAAAF,OAAO,GAAG,KAAK;IACf,KAAAnE,UAAU,GAAG,CAAC;IAEd,KAAAkB,SAAS,GAAG;MACVG,IAAI,EAAE,EAAE;MACRF,KAAK,EAAE;KACR;IACD,KAAAb,SAAS,GAAG,EAAE;IACd,KAAAsB,YAAY,GAAG,KAAK;IACpB,KAAAC,aAAa,GAAG,KAAK;IACb,KAAAsG,gBAAgB,GAAG,IAAIhK,OAAO,EAAU;IACxC,KAAAiK,qBAAqB,GAAwB,IAAI;IAEzD,KAAAb,SAAS,GAAG,SAAS;IACrB,KAAApC,cAAc,GAAG,EAAE;IACnB,KAAAR,mBAAmB,GAAa,EAAE;IAClC,KAAA4B,cAAc,GAAkB,IAAI;IACpC,KAAAO,WAAW,GAAG,KAAK;IACnB,KAAAtB,qBAAqB,GAAG,EAAE;IAC1B,KAAA6C,eAAe,GAAG,KAAK;IACvB,KAAAhD,uBAAuB,GAAa,EAAE;IACtC,KAAAE,oBAAoB,GAAa,EAAE;IAEnC,KAAA+C,qBAAqB,GAAG,KAAK;IAC7B,KAAAC,aAAa,GAAsB;MACjCC,EAAE,EAAE,EAAE;MACNC,QAAQ,EAAE,EAAE;MACZhG,OAAO,EAAE,EAAE;MACXV,QAAQ,EAAE,EAAE;MACZiC,QAAQ,EAAE,KAAK;MACfE,WAAW,EAAE,IAAIwE,IAAI;KACtB;IAED,KAAAnJ,cAAc,GAAG,EAAE;IACnB,KAAAC,YAAY,GAAG,EAAE;IAET,KAAAmJ,aAAa,GAAmB,EAAE;IAElC,KAAAC,eAAe,GAAGnL,MAAM,CAACY,eAAe,CAAC;IACzC,KAAAwK,YAAY,GAAGpL,MAAM,CAACa,YAAY,CAAC;IACnC,KAAAwK,WAAW,GAAGrL,MAAM,CAACc,WAAW,CAAC;IACjC,KAAAwK,aAAa,GAAGtL,MAAM,CAACe,aAAa,CAAC;IACrC,KAAAwK,SAAS,GAAGvL,MAAM,CAACgB,SAAS,CAAC;IAC7B,KAAAwK,KAAK,GAAGxL,MAAM,CAACI,cAAc,CAAC;IAC9B,KAAAqL,MAAM,GAAGzL,MAAM,CAACK,MAAM,CAAC;EAEhB;EAEfqL,QAAQA,CAAA;IAEN,IAAI,CAACf,qBAAqB,GAAG,IAAI,CAACD,gBAAgB,CAACiB,IAAI,CACrDnL,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,CACvB,CAACmL,SAAS,CAAChI,IAAI,IAAG;MACjB,IAAI,CAACiI,oBAAoB,CAACjI,IAAI,CAAC;IACjC,CAAC,CAAC;IAEF,IAAI,IAAI,CAAC+G,qBAAqB,EAAE;MAC9B,IAAI,CAACO,aAAa,CAACY,IAAI,CAAC,IAAI,CAACnB,qBAAqB,CAAC;IACrD;IAEA,IAAI,CAACO,aAAa,CAACY,IAAI,CACrB,IAAI,CAACX,eAAe,CAACY,YAAY,CAACH,SAAS,CAACI,IAAI,IAAG;MACjD,IAAIA,IAAI,EAAE;QACR,IAAI,CAAC1F,MAAM,GAAG0F,IAAI,CAACjB,EAAE;QAErB,IAAI,CAACS,KAAK,CAACS,QAAQ,CAACN,IAAI,CAACpL,IAAI,CAAC,CAAC,CAAC,CAAC,CAACqL,SAAS,CAACM,MAAM,IAAG;UACnD,IAAI,CAACzB,OAAO,GAAGyB,MAAM,CAACC,GAAG,CAAC,IAAI,CAAC;UAE/B,IAAI,IAAI,CAAC1B,OAAO,EAAE;YAChB,IAAI,CAAC2B,SAAS,EAAE;YAChB,IAAI,CAACC,WAAW,EAAE;YAClB,IAAI,CAACC,mBAAmB,EAAE;YAC1B,IAAI,CAACC,2BAA2B,EAAE;YAClC,IAAI,CAACC,wBAAwB,EAAE;UACjC,CAAC,MAAM,CACP;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACf,MAAM,CAACgB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAClC;IACF,CAAC,CAAC,CACH;EACH;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACxB,aAAa,CAACyB,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;EACtD;EAEAT,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAAC3B,OAAO,EAAE;MACjB;IACF;IAEA,IAAI,CAACS,aAAa,CAACY,IAAI,CACrB,IAAI,CAACV,YAAY,CAAC0B,QAAQ,CAAC,IAAI,CAACrC,OAAO,CAAC,CAACmB,SAAS,CAACjK,KAAK,IAAG;MACzD,IAAIA,KAAK,EAAE;QACT,IAAI,CAACA,KAAK,GAAGA,KAAK;QAClB,IAAI,CAAC8B,SAAS,CAACG,IAAI,GAAGjC,KAAK,CAACiC,IAAI;QAChC,IAAI,CAACH,SAAS,CAACC,KAAK,GAAG/B,KAAK,CAAC+B,KAAK;QAClC,IAAI,CAACqJ,mBAAmB,EAAE;MAC5B,CAAC,MAAM;QACL,IAAI,CAACtB,MAAM,CAACgB,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;MACnC;IACF,CAAC,CAAC,CACH;EACH;EAEAJ,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAC5B,OAAO,IAAI,CAAC,IAAI,CAACnE,MAAM,EAAE;MACjC;IACF;IAEA,IAAI,CAAC4E,aAAa,CAACY,IAAI,CACrB,IAAI,CAACV,YAAY,CAAC4B,eAAe,CAAC,IAAI,CAACvC,OAAO,CAAC,CAACmB,SAAS,CAAChF,OAAO,IAAG;MAElE,MAAMqG,iBAAiB,GAAGrG,OAAO,CAACsG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnI,OAAO,KAAK,IAAI,CAACsB,MAAM,CAAC;MACtE,IAAI,CAACI,OAAO,GAAG,CAAAuG,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE1G,QAAQ,KAAI,KAAK;MAEnD,MAAM6G,mBAAmB,GAAwB,EAAE;MAEnD,MAAMC,WAAW,GAAGzG,OAAO,CAACjG,GAAG,CAAC2M,MAAM,KAAK;QACzC,GAAGA,MAAM;QACTvC,EAAE,EAAEuC,MAAM,CAACvC,EAAE,IAAI,EAAE;QACnBC,QAAQ,EAAEsC,MAAM,CAACtC,QAAQ,IAAI,EAAE;QAC/BhG,OAAO,EAAEsI,MAAM,CAACtI,OAAO,IAAI,EAAE;QAC7BV,QAAQ,EAAEgJ,MAAM,CAAChJ,QAAQ,IAAI,EAAE;QAC/BiC,QAAQ,EAAE+G,MAAM,CAAC/G,QAAQ,IAAI,KAAK;QAClCE,WAAW,EAAE6G,MAAM,CAAC7G,WAAW,IAAI,IAAIwE,IAAI;OAC5C,CAAC,CAAC;MAEH,KAAK,MAAMqC,MAAM,IAAID,WAAW,EAAE;QAChC,IAAI,CAAChC,WAAW,CAACkC,cAAc,CAACD,MAAM,CAACtI,OAAO,CAAC,CAAC2G,IAAI,CAACpL,IAAI,CAAC,CAAC,CAAC,CAAC,CAACqL,SAAS,CAAC4B,OAAO,IAAG;UAChFJ,mBAAmB,CAACtB,IAAI,CAAC;YACvB,GAAGwB,MAAM;YACT7I,eAAe,EAAE,CAAA+I,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE/I,eAAe,KAAIgJ,SAAS;YACtDC,QAAQ,EAAEF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEE;WACpB,CAAC;UAEF,IAAI,CAAC9G,OAAO,GAAG,CAAC,GAAGwG,mBAAmB,CAAC;QACzC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,CACH;EACH;EAEAO,QAAQA,CAACrH,MAAc;IACrB,IAAI,CAAC,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,EAAE,OAAO,KAAK;IAEzC,OAAO,KAAK;EACd;EAEAvB,iBAAiBA,CAACuB,MAAc;IAC9B,IAAI,CAACA,MAAM,EAAE;IAEb,IAAIA,MAAM,KAAK,IAAI,CAACA,MAAM,EAAE;IAE5B,IAAI,CAACgF,aAAa,CAACsC,UAAU,CAAC,IAAI,CAACtH,MAAM,IAAI,EAAE,CAAC,CAACqF,IAAI,CACnDpL,IAAI,CAAC,CAAC,CAAC,EACPI,GAAG,CAAEkN,OAAc,IAAI;MACrB,MAAMF,QAAQ,GAAGE,OAAO,CAACC,IAAI,CAAEC,MAAW,IACvCA,MAAM,CAAC/I,OAAO,KAAK,IAAI,CAACsB,MAAM,IAAIyH,MAAM,CAACC,SAAS,KAAK1H,MAAM,IAC7DyH,MAAM,CAAC/I,OAAO,KAAKsB,MAAM,IAAIyH,MAAM,CAACC,SAAS,KAAK,IAAI,CAAC1H,MAAO,CAChE;MAED,IAAIqH,QAAQ,EAAE;QACZ,IAAI,CAAClC,MAAM,CAACgB,QAAQ,CAAC,CAAC,UAAU,EAAEnG,MAAM,CAAC,CAAC;MAC5C,CAAC,MAAM;QACL,IAAI,CAACmF,MAAM,CAACgB,QAAQ,CAAC,CAAC,eAAe,EAAEnG,MAAM,CAAC,CAAC;MACjD;IACF,CAAC,CAAC,CACH,CAACsF,SAAS,EAAE;EACf;EAEAmB,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACpL,KAAK,EAAE;IAEjB,IAAI,CAAC4J,SAAS,CAAC0C,yBAAyB,CAAC,IAAI,CAACtM,KAAK,CAACC,KAAK,EAAEX,UAAU,CAACiN,KAAK,CAAC,CACzEvC,IAAI,CAACpL,IAAI,CAAC,CAAC,CAAC,CAAC,CACbqL,SAAS,CAACrJ,UAAU,IAAG;MACtB,IAAI,CAACA,UAAU,GAAGA,UAAU;IAC9B,CAAC,CAAC;EACN;EAEAE,qBAAqBA,CAAC0L,EAAU;IAC9B,IAAI,IAAI,CAAC5L,UAAU,IAAI,CAAC,EAAE,OAAO,CAAC;IAClC,OAAO6L,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,KAAK,CAAEH,EAAE,GAAG,IAAI,CAAC5L,UAAU,GAAI,GAAG,CAAC,CAAC;EAChE;EAEAuB,cAAcA,CAAA;IAAA,IAAAyK,oBAAA;IACZ,MAAM3K,IAAI,IAAA2K,oBAAA,GAAG,IAAI,CAAC9K,SAAS,CAACG,IAAI,cAAA2K,oBAAA,uBAAnBA,oBAAA,CAAqB5G,IAAI,EAAE;IAExC,IAAI,CAACvD,aAAa,GAAG,KAAK;IAE1B,IAAI,CAACR,IAAI,EAAE;MACT,IAAI,CAACf,SAAS,GAAG,EAAE;MACnB,IAAI,CAACsB,YAAY,GAAG,KAAK;MACzB;IACF;IAEA,IAAI,CAACA,YAAY,GAAG,IAAI;IAExB,IAAI,CAACuG,gBAAgB,CAAC8D,IAAI,CAAC5K,IAAI,CAAC;EAClC;EAEMiI,oBAAoBA,CAACjI,IAAY;IAAA,IAAA6K,KAAA;IAAA,OAAAC,iBAAA;MACrC,IAAI,CAAC9K,IAAI,IAAI,CAAC6K,KAAI,CAAChE,OAAO,EAAE;QAC1BgE,KAAI,CAAC5L,SAAS,GAAG,EAAE;QACnB4L,KAAI,CAACtK,YAAY,GAAG,KAAK;QACzB;MACF;MAEA,IAAI;QACF,MAAMwK,WAAW,SAASF,KAAI,CAACrD,YAAY,CAACwD,0BAA0B,CAAChL,IAAI,EAAE6K,KAAI,CAAChE,OAAO,CAAC;QAE1FgE,KAAI,CAACtK,YAAY,GAAG,KAAK;QAEzB,IAAI,CAACwK,WAAW,EAAE;UAChBF,KAAI,CAAC5L,SAAS,GAAG,kCAAkC;UACnD4L,KAAI,CAACrK,aAAa,GAAG,KAAK;QAC5B,CAAC,MAAM;UACLqK,KAAI,CAAC5L,SAAS,GAAG,EAAE;UACnB4L,KAAI,CAACrK,aAAa,GAAG,IAAI;QAC3B;MACF,CAAC,CAAC,OAAOyK,KAAK,EAAE;QACdJ,KAAI,CAACtK,YAAY,GAAG,KAAK;QACzBsK,KAAI,CAAC5L,SAAS,GAAG,wCAAwC;MAC3D;IAAC;EACH;EAEAO,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACqH,OAAO,IAAI,CAAC,IAAI,CAAC9I,KAAK,IAAI,CAAC,IAAI,CAAC+E,OAAO,EAAE;IAEnD,IAAI,CAAC,IAAI,CAACjD,SAAS,CAACG,IAAI,CAAC+D,IAAI,EAAE,EAAE;MAC/B,IAAI,CAAC5F,YAAY,GAAG,6BAA6B;MACjD+M,UAAU,CAAC,MAAM,IAAI,CAAC/M,YAAY,GAAG,EAAE,EAAE,IAAI,CAAC;MAC9C;IACF;IAEA,IAAI,IAAI,CAACc,SAAS,EAAE;MAClB,IAAI,CAACd,YAAY,GAAG,uCAAuC;MAC3D+M,UAAU,CAAC,MAAM,IAAI,CAAC/M,YAAY,GAAG,EAAE,EAAE,IAAI,CAAC;MAC9C;IACF;IAEA,IAAI,CAACqJ,YAAY,CAAC2D,WAAW,CAAC,IAAI,CAACtE,OAAO,EAAE;MAC1C7G,IAAI,EAAE,IAAI,CAACH,SAAS,CAACG,IAAI,CAAC+D,IAAI,EAAE;MAChCjE,KAAK,EAAE,IAAI,CAACD,SAAS,CAACC;KACvB,CAAC,CAACsL,IAAI,CAAC,MAAK;MACX,IAAI,CAAClN,cAAc,GAAG,yCAAyC;MAC/DgN,UAAU,CAAC,MAAM,IAAI,CAAChN,cAAc,GAAG,EAAE,EAAE,IAAI,CAAC;MAEhD,IAAI,IAAI,CAACH,KAAK,EAAE;QACd,IAAI,CAACA,KAAK,CAACiC,IAAI,GAAG,IAAI,CAACH,SAAS,CAACG,IAAI,CAAC+D,IAAI,EAAE;QAC5C,IAAI,CAAChG,KAAK,CAAC+B,KAAK,GAAG,IAAI,CAACD,SAAS,CAACC,KAAK;MACzC;IACF,CAAC,CAAC,CAACuL,KAAK,CAACJ,KAAK,IAAG;MACf,IAAI,CAAC9M,YAAY,GAAG8M,KAAK,CAACK,OAAO,IAAI,mCAAmC;MACxEJ,UAAU,CAAC,MAAM,IAAI,CAAC/M,YAAY,GAAG,EAAE,EAAE,IAAI,CAAC;IAChD,CAAC,CAAC;EACJ;EAGA0H,YAAYA,CAAC0F,GAAW;IACtB,IAAI,CAACrF,SAAS,GAAGqF,GAAG;IACpB,IAAI,CAACpH,qBAAqB,GAAG,EAAE;EACjC;EAEAG,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACuC,OAAO,IAAI,CAAC,IAAI,CAAC/D,OAAO,IAAI,CAAC,IAAI,CAACgB,cAAc,CAACC,IAAI,EAAE,EAAE;IAEnE,MAAMyH,gBAAgB,GAAG,IAAI,CAAC1H,cAAc,CAACC,IAAI,EAAE;IAEnD,IAAI,CAAC,IAAI,CAAC2D,aAAa,CAAC+D,sBAAsB,CAACD,gBAAgB,CAAC,EAAE;MAChE,IAAI,CAACrN,YAAY,GAAG,iGAAiG;MACrH+M,UAAU,CAAC,MAAM,IAAI,CAAC/M,YAAY,GAAG,EAAE,EAAE,IAAI,CAAC;MAC9C;IACF;IAEA,IAAI,IAAI,CAAC6F,uBAAuB,CAACC,QAAQ,CAACuH,gBAAgB,CAAC,EAAE;MAC3D,IAAI,CAACrN,YAAY,GAAG,mDAAmD;MACvE+M,UAAU,CAAC,MAAM,IAAI,CAAC/M,YAAY,GAAG,EAAE,EAAE,IAAI,CAAC;MAC9C;IACF;IAEA,IAAI,CAAC,IAAI,CAAC+F,oBAAoB,CAACD,QAAQ,CAACuH,gBAAgB,CAAC,EAAE;MACzD,IAAI,CAACrN,YAAY,GAAG,mDAAmD;MACvE+M,UAAU,CAAC,MAAM,IAAI,CAAC/M,YAAY,GAAG,EAAE,EAAE,IAAI,CAAC;MAC9C;IACF;IAEA,IAAI,CAACqJ,YAAY,CAACkE,iBAAiB,CAAC,IAAI,CAAC7E,OAAO,EAAE2E,gBAAgB,CAAC,CAChEJ,IAAI,CAAC,MAAK;MACT,IAAI,CAACpH,uBAAuB,CAACkE,IAAI,CAACsD,gBAAgB,CAAC;MAEnD,IAAI,CAACtN,cAAc,GAAG,sBAAsBsN,gBAAgB,GAAG;MAC/D,IAAI,CAACrH,qBAAqB,GAAG,sBAAsBqH,gBAAgB,GAAG;MACtE,IAAI,CAAC1H,cAAc,GAAG,EAAE;MAExBoH,UAAU,CAAC,MAAM,IAAI,CAAChN,cAAc,GAAG,EAAE,EAAE,IAAI,CAAC;MAEhDgN,UAAU,CAAC,MAAM,IAAI,CAAC/G,qBAAqB,GAAG,EAAE,EAAE,IAAI,CAAC;IACzD,CAAC,CAAC,CACDkH,KAAK,CAACJ,KAAK,IAAG;MAEb,IAAIA,KAAK,CAACK,OAAO,IAAIL,KAAK,CAACK,OAAO,CAACrH,QAAQ,CAAC,mBAAmB,CAAC,EAAE;QAChE,IAAI,CAAC9F,YAAY,GAAG,mDAAmD;MACzE,CAAC,MAAM,IAAI8M,KAAK,CAACK,OAAO,IAAIL,KAAK,CAACK,OAAO,CAACrH,QAAQ,CAAC,kBAAkB,CAAC,EAAE;QACtE,IAAI,CAAC9F,YAAY,GAAG,6CAA6C;MACnE,CAAC,MAAM,IAAI8M,KAAK,CAACK,OAAO,IAAIL,KAAK,CAACK,OAAO,CAACrH,QAAQ,CAAC,gBAAgB,CAAC,EAAE;QACpE,IAAI,CAAC9F,YAAY,GAAG,4CAA4C;MAClE,CAAC,MAAM;QACL,IAAI,CAACA,YAAY,GAAG,mGAAmG;MACzH;MAEA+M,UAAU,CAAC,MAAM,IAAI,CAAC/M,YAAY,GAAG,EAAE,EAAE,IAAI,CAAC;IAChD,CAAC,CAAC;EACN;EAEAkH,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACwB,OAAO,IAAI,CAAC,IAAI,CAAC/D,OAAO,EAAE;IAEpC,IAAI,CAAC0E,YAAY,CAACnC,sBAAsB,CAAC,IAAI,CAACwB,OAAO,CAAC,CACnDuE,IAAI,CAACO,IAAI,IAAG;MACX,IAAI,CAACzG,cAAc,GAAGyG,IAAI;MAC1B,IAAI,CAAClG,WAAW,GAAG,IAAI;IACzB,CAAC,CAAC,CACD4F,KAAK,CAACJ,KAAK,IAAG;MACb,IAAI,CAAC9M,YAAY,GAAG,mCAAmC;MACvD+M,UAAU,CAAC,MAAM,IAAI,CAAC/M,YAAY,GAAG,EAAE,EAAE,IAAI,CAAC;IAChD,CAAC,CAAC;EACN;EAEAuK,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAAC7B,OAAO,EAAE;IAEnB,IAAI,CAACW,YAAY,CAAC0B,QAAQ,CAAC,IAAI,CAACrC,OAAO,CAAC,CAACkB,IAAI,CAACpL,IAAI,CAAC,CAAC,CAAC,CAAC,CAACqL,SAAS,CAACjK,KAAK,IAAG;MACvE,IAAIA,KAAK,IAAIA,KAAK,CAAC6N,eAAe,IAAI7N,KAAK,CAAC8N,WAAW,EAAE;QACvD,MAAMC,UAAU,GAAG,IAAIzE,IAAI,CAACtJ,KAAK,CAAC8N,WAAW,CAAC;QAC9C,IAAIC,UAAU,GAAG,IAAIzE,IAAI,EAAE,EAAE;UAC3B,IAAI,CAACnC,cAAc,GAAGnH,KAAK,CAAC6N,eAAe;UAC3C,IAAI,CAACnG,WAAW,GAAG,IAAI;QACzB;MACF;IACF,CAAC,CAAC;EACJ;EAEAkD,2BAA2BA,CAAA;IACzB,IAAI,CAAC,IAAI,CAAC9B,OAAO,EAAE;IAEnB,IAAI,CAACU,eAAe,CAACwE,QAAQ,CAC1BC,IAAI,CAAC,qBAAqB,CAAC,CAC3BC,MAAM,CAAC,kBAAkB,CAAC,CAC1BC,EAAE,CAAC,UAAU,EAAE,IAAI,CAACrF,OAAO,CAAC,CAC5BuE,IAAI,CAACe,QAAQ,IAAG;MACf,IAAIA,QAAQ,CAAClB,KAAK,EAAE;QAClB;MACF;MAEA,IAAI,CAACjH,uBAAuB,GAAGmI,QAAQ,CAACC,IAAI,CAACrP,GAAG,CAACsP,MAAM,IAAIA,MAAM,CAACC,gBAAgB,CAAC;IACrF,CAAC,CAAC;EACN;EAEA1D,wBAAwBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAAClG,MAAM,EAAE;IAElB,IAAI,CAACgF,aAAa,CAAC6E,sBAAsB,CAAC,IAAI,CAAC7J,MAAM,CAAC,CACnDqF,IAAI,CAACpL,IAAI,CAAC,CAAC,CAAC,CAAC,CACbqL,SAAS,CAACiC,OAAO,IAAG;MACnB,IAAI,CAACA,OAAO,IAAIA,OAAO,CAACpF,MAAM,KAAK,CAAC,EAAE;QACpC;MACF;MAEA,IAAI,CAACX,oBAAoB,GAAG+F,OAAO,CAChCuC,MAAM,CAACrC,MAAM,IAAIA,MAAM,CAACP,OAAO,IAAIO,MAAM,CAACP,OAAO,CAACE,QAAQ,CAAC,CAC3D/M,GAAG,CAACoN,MAAM,IAAIA,MAAM,CAACP,OAAO,CAACE,QAAQ,CAAC;IAE3C,CAAC,CAAC;EACN;EAEAzD,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACQ,OAAO,IAAI,CAAC,IAAI,CAAC9I,KAAK,IAAI,CAAC,IAAI,CAAC+E,OAAO,EAAE;IAGnD,IAAI,CAAC0E,YAAY,CAAC2D,WAAW,CAAC,IAAI,CAACtE,OAAO,EAAE;MAC1CN,iBAAiB,EAAE,IAAI,CAACxI,KAAK,CAACwI;KAC/B,CAAC,CAAC6E,IAAI,CAAC,MAAK;MACX,IAAI,CAAClN,cAAc,GAAG,2CAA2C;MACjEgN,UAAU,CAAC,MAAM,IAAI,CAAChN,cAAc,GAAG,EAAE,EAAE,IAAI,CAAC;MAEhD,IAAI,CAACsK,SAAS,EAAE;IAClB,CAAC,CAAC,CAAC6C,KAAK,CAACJ,KAAK,IAAG;MACf,IAAI,CAAC9M,YAAY,GAAG,qCAAqC;MACzD+M,UAAU,CAAC,MAAM,IAAI,CAAC/M,YAAY,GAAG,EAAE,EAAE,IAAI,CAAC;IAChD,CAAC,CAAC;EACJ;EAEA6D,kBAAkBA,CAAC0H,MAAyB;IAC1C,IAAI,CAACxC,aAAa,GAAG;MAAE,GAAGwC;IAAM,CAAE;IAClC,IAAI,CAACzC,qBAAqB,GAAG,IAAI;EACnC;EAEAwF,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAAC5F,OAAO,IAAI,CAAC,IAAI,CAAC/D,OAAO,IAAI,CAAC,IAAI,CAACoE,aAAa,CAACC,EAAE,EAAE;IAE9D,IAAI,CAACK,YAAY,CAACkF,iBAAiB,CAAC,IAAI,CAACxF,aAAa,CAACC,EAAE,EAAE;MACzDzG,QAAQ,EAAE,IAAI,CAACwG,aAAa,CAACxG,QAAQ,CAACqD,IAAI;KAC3C,CAAC,CAACqH,IAAI,CAAC,MAAK;MACX,IAAI,CAAClN,cAAc,GAAG,uCAAuC;MAC7D,IAAI,CAAC+I,qBAAqB,GAAG,KAAK;MAElC,MAAM0F,KAAK,GAAG,IAAI,CAAC3J,OAAO,CAAC4J,SAAS,CAACrD,CAAC,IAAIA,CAAC,CAACpC,EAAE,KAAK,IAAI,CAACD,aAAa,CAACC,EAAE,CAAC;MACzE,IAAIwF,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,IAAI,CAAC3J,OAAO,CAAC2J,KAAK,CAAC,CAACjM,QAAQ,GAAG,IAAI,CAACwG,aAAa,CAACxG,QAAQ,CAACqD,IAAI,EAAE;MACnE;MAEAmH,UAAU,CAAC,MAAM,IAAI,CAAChN,cAAc,GAAG,EAAE,EAAE,IAAI,CAAC;IAClD,CAAC,CAAC,CAACmN,KAAK,CAACJ,KAAK,IAAG;MACf,IAAI,CAAC9M,YAAY,GAAG,iCAAiC;MACrD+M,UAAU,CAAC,MAAM,IAAI,CAAC/M,YAAY,GAAG,EAAE,EAAE,IAAI,CAAC;IAChD,CAAC,CAAC;EACJ;EAEA+D,YAAYA,CAACwH,MAAyB;IACpC,IAAI,CAAC,IAAI,CAAC7C,OAAO,IAAI,CAAC,IAAI,CAAC/D,OAAO,IAAI,CAAC4G,MAAM,CAACvC,EAAE,EAAE;IAElD,IAAI0F,OAAO,CAAC,mCAAmCnD,MAAM,CAAChJ,QAAQ,kBAAkB,CAAC,EAAE;MACjF,IAAI,CAAC8G,YAAY,CAACsF,qBAAqB,CAAC,IAAI,CAACjG,OAAO,EAAE6C,MAAM,CAACvC,EAAE,CAAC,CAC7DiE,IAAI,CAAC,MAAK;QACT,IAAI,CAAClN,cAAc,GAAG,GAAGwL,MAAM,CAAChJ,QAAQ,mCAAmC;QAE3E,IAAI,CAACsC,OAAO,GAAG,IAAI,CAACA,OAAO,CAACwJ,MAAM,CAACjD,CAAC,IAAIA,CAAC,CAACpC,EAAE,KAAKuC,MAAM,CAACvC,EAAE,CAAC;QAE3D+D,UAAU,CAAC,MAAM,IAAI,CAAChN,cAAc,GAAG,EAAE,EAAE,IAAI,CAAC;MAClD,CAAC,CAAC,CACDmN,KAAK,CAACJ,KAAK,IAAG;QACb,IAAI,CAAC9M,YAAY,GAAG,mCAAmC;QACvD+M,UAAU,CAAC,MAAM,IAAI,CAAC/M,YAAY,GAAG,EAAE,EAAE,IAAI,CAAC;MAChD,CAAC,CAAC;IACN;EACF;EAEA0D,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACgF,OAAO,IAAI,CAAC,IAAI,CAACnE,MAAM,EAAE;IAEnC,IAAImK,OAAO,CAAC,yFAAyF,CAAC,EAAE;MACtG,IAAI,CAACrF,YAAY,CAAC3F,UAAU,CAAC,IAAI,CAACgF,OAAO,CAAC,CACvCuE,IAAI,CAAC,MAAK;QACT,IAAI,CAACvD,MAAM,CAACgB,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;MACnC,CAAC,CAAC,CACDwC,KAAK,CAACJ,KAAK,IAAG;QACb,IAAI,CAAC9M,YAAY,GAAG,sBAAsB;QAC1C+M,UAAU,CAAC,MAAM,IAAI,CAAC/M,YAAY,GAAG,EAAE,EAAE,IAAI,CAAC;MAChD,CAAC,CAAC;IACN;EACF;EAEAuI,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACG,OAAO,IAAI,CAAC,IAAI,CAAC/D,OAAO,EAAE;IAEpC,IAAI+J,OAAO,CAAC,0HAA0H,CAAC,EAAE;MACvI,IAAI,CAACrF,YAAY,CAACuF,WAAW,CAAC,IAAI,CAAClG,OAAO,CAAC,CACxCuE,IAAI,CAAC,MAAK;QACT,IAAI,CAACvD,MAAM,CAACgB,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;MACnC,CAAC,CAAC,CACDwC,KAAK,CAACJ,KAAK,IAAG;QACb,IAAI,CAAC9M,YAAY,GAAG,uBAAuB;QAC3C+M,UAAU,CAAC,MAAM,IAAI,CAAC/M,YAAY,GAAG,EAAE,EAAE,IAAI,CAAC;MAChD,CAAC,CAAC;IACN;EACF;EAEAiF,cAAcA,CAAC0G,QAAgB;IAC7B,IAAI,CAAChG,cAAc,GAAGgG,QAAQ;IAC9B,IAAI,CAACxG,mBAAmB,GAAG,EAAE;IAE7B,MAAM0J,eAAe,GAAGlD,QAAQ,CAAC/F,IAAI,EAAE;IAEvC,MAAMkJ,aAAa,GAAGD,eAAe,KAAK,EAAE,IACvB,IAAI,CAACtF,aAAa,CAAC+D,sBAAsB,CAACuB,eAAe,CAAC;IAE/E,MAAME,gBAAgB,GAAG,IAAI,CAAClJ,uBAAuB,CAACC,QAAQ,CAAC+I,eAAe,CAAC;IAE/E,MAAMjD,QAAQ,GAAG,IAAI,CAAC7F,oBAAoB,CAACD,QAAQ,CAAC+I,eAAe,CAAC;IAEpE,IAAI,CAAChG,eAAe,GAAGiG,aAAa,IAAI,CAACC,gBAAgB,IAAInD,QAAQ;IAErE,IAAImD,gBAAgB,EAAE;MACpB,IAAI,CAAC/O,YAAY,GAAG,mDAAmD;MACvE+M,UAAU,CAAC,MAAM,IAAI,CAAC/M,YAAY,GAAG,EAAE,EAAE,IAAI,CAAC;IAChD,CAAC,MACI,IAAI,CAAC4L,QAAQ,IAAIkD,aAAa,EAAE;MACnC,IAAI,CAAC9O,YAAY,GAAG,mDAAmD;MACvE+M,UAAU,CAAC,MAAM,IAAI,CAAC/M,YAAY,GAAG,EAAE,EAAE,IAAI,CAAC;IAChD;EACF;EAEAsG,eAAeA,CAAA;IAEb,IAAI,CAACN,qBAAqB,GAAG,EAAE;IAE/B,MAAM6I,eAAe,GAAG,IAAI,CAAClJ,cAAc,CAACC,IAAI,EAAE;IAElD,MAAMkJ,aAAa,GAAGD,eAAe,KAAK,EAAE,IACvB,IAAI,CAACtF,aAAa,CAAC+D,sBAAsB,CAACuB,eAAe,CAAC;IAE/E,MAAME,gBAAgB,GAAG,IAAI,CAAClJ,uBAAuB,CAACC,QAAQ,CAAC+I,eAAe,CAAC;IAE/E,MAAMjD,QAAQ,GAAG,IAAI,CAAC7F,oBAAoB,CAACD,QAAQ,CAAC+I,eAAe,CAAC;IAEpE,IAAI,CAAChG,eAAe,GAAGiG,aAAa,IAAI,CAACC,gBAAgB,IAAInD,QAAQ;IAErE,IAAI,CAAC,IAAI,CAACrH,MAAM,IAAI,CAAC,IAAI,CAACmE,OAAO,IAAI,CAAC,IAAI,CAAC/C,cAAc,IAAI,IAAI,CAACA,cAAc,CAACe,MAAM,GAAG,CAAC,EAAE;MAC3F,IAAI,CAACvB,mBAAmB,GAAG,EAAE;MAC7B;IACF;IAEA,IAAI,CAACoE,aAAa,CAACyF,uBAAuB,CAAC,IAAI,CAACzK,MAAM,EAAE,IAAI,CAACoB,cAAc,EAAE,IAAI,CAAC+C,OAAO,CAAC,CACvFkB,IAAI,CAACpL,IAAI,CAAC,CAAC,CAAC,CAAC,CACbqL,SAAS,CAACoF,WAAW,IAAG;MACvB,IAAI,CAAC9J,mBAAmB,GAAG8J,WAAW;IACxC,CAAC,CAAC;EACN;;qBAhhBWzG,iBAAiB;;mCAAjBA,kBAAiB;AAAA;;QAAjBA,kBAAiB;EAAA0G,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MC5BxBnQ,EAHN,CAAAC,cAAA,aAAgC,aACD,WAC8B,cAC9B;MAAAD,EAAA,CAAAE,MAAA,aAAC;MAAAF,EAAA,CAAAG,YAAA,EAAO;MAACH,EAAA,CAAAE,MAAA,sBACpC;MAAAF,EAAA,CAAAG,YAAA,EAAI;MACJH,EAAA,CAAAC,cAAA,SAAI;MAAAD,EAAA,CAAAE,MAAA,eAAQ;MAAAF,EAAA,CAAAG,YAAA,EAAK;MACjBH,EAAA,CAAAY,UAAA,IAAAyP,gCAAA,iBAA6C;MAC/CrQ,EAAA,CAAAG,YAAA,EAAM;MAsPNH,EAnPA,CAAAY,UAAA,IAAA0P,gCAAA,iBAA6D,KAAAC,iCAAA,mBAMjB,KAAAC,iCAAA,kBA6DW,KAAAC,iCAAA,iBAwBE,KAAAC,iCAAA,kBAgDX,KAAAC,iCAAA,kBAsFS,KAAAC,iCAAA,mBAkBM;MAU/D5Q,EAAA,CAAAG,YAAA,EAAM;MAKFH,EAFJ,CAAAC,cAAA,eAAuG,eAC1E,gBACyC;MAAxCD,EAAA,CAAA0B,UAAA,mBAAAmP,kDAAA;QAAA,OAAAT,GAAA,CAAA3G,qBAAA,GAAiC,KAAK;MAAA,EAAC;MAACzJ,EAAA,CAAAE,MAAA,cAAO;MAAAF,EAAA,CAAAG,YAAA,EAAO;MAChFH,EAAA,CAAAC,cAAA,UAAI;MAAAD,EAAA,CAAAE,MAAA,qBAAa;MAAAF,EAAA,CAAAG,YAAA,EAAK;MACtBH,EAAA,CAAAC,cAAA,gBAA0C;MAApCD,EAAA,CAAA0B,UAAA,sBAAAoP,qDAAA;QAAA,OAAYV,GAAA,CAAAnB,oBAAA,EAAsB;MAAA,EAAC;MACvCjP,EAAA,CAAAC,cAAA,iBAA0G;MAA9CD,EAAA,CAAAiC,gBAAA,2BAAA8O,2DAAA5O,MAAA;QAAAnC,EAAA,CAAAoC,kBAAA,CAAAgO,GAAA,CAAA1G,aAAA,CAAAxG,QAAA,EAAAf,MAAA,MAAAiO,GAAA,CAAA1G,aAAA,CAAAxG,QAAA,GAAAf,MAAA;QAAA,OAAAA,MAAA;MAAA,EAAoC;MAAhGnC,EAAA,CAAAG,YAAA,EAA0G;MAC1GH,EAAA,CAAAC,cAAA,kBAAgD;MAAAD,EAAA,CAAAE,MAAA,YAAI;MAG1DF,EAH0D,CAAAG,YAAA,EAAS,EACxD,EACH,EACF;;;MAjRCH,EAAA,CAAAI,SAAA,GAAmC;MAAnCJ,EAAA,CAAAe,UAAA,eAAAf,EAAA,CAAAgR,eAAA,KAAAC,GAAA,EAAAb,GAAA,CAAA/G,OAAA,EAAmC;MAINrJ,EAAA,CAAAI,SAAA,GAAW;MAAXJ,EAAA,CAAAe,UAAA,SAAAqP,GAAA,CAAA7P,KAAA,CAAW;MAItBP,EAAA,CAAAI,SAAA,EAAoC;MAApCJ,EAAA,CAAAe,UAAA,SAAAqP,GAAA,CAAA1P,cAAA,IAAA0P,GAAA,CAAAzP,YAAA,CAAoC;MAM5BX,EAAA,CAAAI,SAAA,EAAW;MAAXJ,EAAA,CAAAe,UAAA,SAAAqP,GAAA,CAAA7P,KAAA,CAAW;MA6DXP,EAAA,CAAAI,SAAA,EAAsB;MAAtBJ,EAAA,CAAAe,UAAA,SAAAqP,GAAA,CAAA9K,OAAA,IAAA8K,GAAA,CAAA7P,KAAA,CAAsB;MAwBtBP,EAAA,CAAAI,SAAA,EAAwB;MAAxBJ,EAAA,CAAAe,UAAA,SAAAqP,GAAA,CAAA5K,OAAA,CAAA6B,MAAA,KAAwB;MAgDxBrH,EAAA,CAAAI,SAAA,EAAa;MAAbJ,EAAA,CAAAe,UAAA,SAAAqP,GAAA,CAAA9K,OAAA,CAAa;MAsFbtF,EAAA,CAAAI,SAAA,EAAsB;MAAtBJ,EAAA,CAAAe,UAAA,SAAAqP,GAAA,CAAA9K,OAAA,IAAA8K,GAAA,CAAA7P,KAAA,CAAsB;MAkBPP,EAAA,CAAAI,SAAA,EAAa;MAAbJ,EAAA,CAAAe,UAAA,SAAAqP,GAAA,CAAA9K,OAAA,CAAa;MAajBtF,EAAA,CAAAI,SAAA,EAA0D;MAA1DJ,EAAA,CAAAoB,WAAA,YAAAgP,GAAA,CAAA3G,qBAAA,oBAA0D;MAKpCzJ,EAAA,CAAAI,SAAA,GAAoC;MAApCJ,EAAA,CAAA8C,gBAAA,YAAAsN,GAAA,CAAA1G,aAAA,CAAAxG,QAAA,CAAoC;;;iBDlP1FnE,WAAW,EAAAmS,EAAA,CAAAC,0BAAA,EAAEtS,YAAY,EAAAuS,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EAAEzS,WAAW,EAAA0S,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,4BAAA,EAAAH,EAAA,CAAAI,eAAA,EAAAJ,EAAA,CAAAK,oBAAA,EAAAL,EAAA,CAAAM,iBAAA,EAAAN,EAAA,CAAAO,gBAAA,EAAAP,EAAA,CAAAQ,OAAA,EAAAR,EAAA,CAAAS,MAAA,EAAE/S,YAAY,EAAAgT,EAAA,CAAAC,UAAA,EAAEpS,mBAAmB;EAAAqS,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}