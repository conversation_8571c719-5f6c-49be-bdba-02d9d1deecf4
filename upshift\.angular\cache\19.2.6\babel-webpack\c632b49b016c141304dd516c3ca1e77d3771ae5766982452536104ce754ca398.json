{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _RegisterComponent;\nimport { inject, EnvironmentInjector } from '@angular/core';\nimport { FormBuilder, ReactiveFormsModule } from '@angular/forms';\nimport { Router, RouterModule } from '@angular/router';\nimport { CommonModule } from '@angular/common';\nimport { IonicModule } from '@ionic/angular';\nimport { Preferences } from '@capacitor/preferences';\nimport { SupabaseService } from '../../services/supabase.service';\nimport { UserService } from '../../services/user.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nimport * as i2 from \"@angular/forms\";\nexport class RegisterComponent {\n  constructor() {\n    this.segment = 'login';\n    this.fb = inject(FormBuilder);\n    this.supabaseService = inject(SupabaseService);\n    this.userService = inject(UserService);\n    this.router = inject(Router);\n    this.injector = inject(EnvironmentInjector);\n    this.form = this.fb.group({\n      email: [''],\n      password: [''],\n      confirmPassword: ['']\n    });\n  }\n  onSegmentChange(event) {\n    const value = event.detail.value;\n    if (value === 'login' || value === 'register') {\n      this.segment = value;\n    }\n  }\n  login() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const {\n        email,\n        password\n      } = _this.form.value;\n      if (!email || !password) {\n        alert('Please enter both email and password');\n        return;\n      }\n      try {\n        const {\n          data,\n          error\n        } = yield _this.supabaseService.getClient().auth.signInWithPassword({\n          email,\n          password\n        });\n        if (error) {\n          alert('Login error: ' + error.message);\n          return;\n        }\n        yield _this.handlePostLogin(data.user.id);\n      } catch (error) {\n        alert('Login error: ' + error.message);\n      }\n    })();\n  }\n  register() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const {\n        email,\n        password,\n        confirmPassword\n      } = _this2.form.value;\n      if (password !== confirmPassword) {\n        alert('Passwords do not match');\n        return;\n      }\n      try {\n        const {\n          data,\n          error\n        } = yield _this2.supabaseService.getClient().auth.signUp({\n          email,\n          password\n        });\n        if (error) {\n          alert('Registration error: ' + error.message);\n          return;\n        }\n        if (data !== null && data !== void 0 && data.user) {\n          yield _this2.handlePostRegister(data.user.id);\n        } else {\n          alert('Registration successful but no user data returned');\n        }\n      } catch (error) {\n        alert('Registration error: ' + error.message);\n      }\n    })();\n  }\n  signInWithGoogle() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          data,\n          error\n        } = yield _this3.supabaseService.getClient().auth.signInWithOAuth({\n          provider: 'google',\n          options: {\n            redirectTo: window.location.origin + '/'\n          }\n        });\n        if (error) {\n          alert('Error signing in with Google: ' + error.message);\n          return;\n        }\n      } catch (error) {\n        alert('Error signing in with Google');\n      }\n    })();\n  }\n  signInWithApple() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          data,\n          error\n        } = yield _this4.supabaseService.getClient().auth.signInWithOAuth({\n          provider: 'apple',\n          options: {\n            redirectTo: window.location.origin + '/'\n          }\n        });\n        if (error) {\n          alert('Error signing in with Apple: ' + error.message);\n          return;\n        }\n      } catch (error) {\n        alert('Error signing in with Apple');\n      }\n    })();\n  }\n  createUserRecord(userId, userData) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        var _userData$user_metada, _userData$user_metada2;\n        const newUser = {\n          id: userId,\n          email: userData.email || '',\n          username: '',\n          name: ((_userData$user_metada = userData.user_metadata) === null || _userData$user_metada === void 0 ? void 0 : _userData$user_metada.full_name) || '',\n          profile_picture: ((_userData$user_metada2 = userData.user_metadata) === null || _userData$user_metada2 === void 0 ? void 0 : _userData$user_metada2.avatar_url) || '',\n          registration_date: new Date(),\n          last_login: new Date(),\n          active: true,\n          xp: null,\n          level: null,\n          title: null,\n          plan: 'none',\n          auto_renew: true,\n          start_of_current_plan: null,\n          end_of_current_plan: null,\n          start_of_sick_days: null,\n          end_of_sick_days: null,\n          subscription_status: 'email marketing',\n          affiliate_code_used: null,\n          strength_xp: 0,\n          money_xp: 0,\n          health_xp: 0,\n          knowledge_xp: 0\n        };\n        const {\n          error\n        } = yield _this5.supabaseService.getClient().from('profiles').insert(newUser);\n        if (error) {\n          throw error;\n        }\n      } catch (error) {\n        throw error;\n      }\n    })();\n  }\n  handlePostLogin(uid) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield Preferences.set({\n          key: 'uid',\n          value: uid\n        });\n        const {\n          data: userData,\n          error\n        } = yield _this6.supabaseService.getClient().from('profiles').select('*').eq('id', uid).single();\n        if (error && error.code !== 'PGRST116') {}\n        if (!userData) {\n          const authUser = _this6.supabaseService._currentUser.value;\n          if (authUser) {\n            yield _this6.createUserRecord(uid, authUser);\n          }\n        } else {\n          const {\n            error: updateError\n          } = yield _this6.supabaseService.getClient().from('profiles').update({\n            last_login: new Date()\n          }).eq('id', uid);\n          if (updateError) {}\n        }\n        _this6.router.navigateByUrl('/tabs/home');\n      } catch (error) {}\n    })();\n  }\n  handlePostRegister(uid) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield Preferences.set({\n          key: 'uid',\n          value: uid\n        });\n        const authUser = _this7.supabaseService._currentUser.value;\n        if (authUser) {\n          yield _this7.createUserRecord(uid, authUser);\n        }\n        _this7.router.navigateByUrl('/onboarding');\n      } catch (error) {}\n    })();\n  }\n}\n_RegisterComponent = RegisterComponent;\n_RegisterComponent.ɵfac = function RegisterComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _RegisterComponent)();\n};\n_RegisterComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _RegisterComponent,\n  selectors: [[\"app-register\"]],\n  decls: 40,\n  vars: 1,\n  consts: [[1, \"background-container\"], [1, \"gradient-bg\"], [1, \"celestial-body\"], [1, \"content-wrapper\"], [\"size\", \"12\", 1, \"ion-text-center\"], [1, \"main-title\"], [1, \"gradient-text\"], [\"size\", \"12\"], [1, \"form-container\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-fields\"], [1, \"input-field\"], [\"formControlName\", \"email\", \"type\", \"email\", \"placeholder\", \"Email\", \"required\", \"\"], [\"formControlName\", \"password\", \"type\", \"password\", \"placeholder\", \"Password\", \"required\", \"\"], [\"formControlName\", \"confirmPassword\", \"type\", \"password\", \"placeholder\", \"Confirm Password\", \"required\", \"\"], [\"expand\", \"block\", \"type\", \"submit\", 1, \"submit-button\"], [1, \"divider\"], [1, \"social-buttons\"], [1, \"social-button\", 3, \"click\"], [1, \"button-content\"], [\"name\", \"logo-google\"], [\"name\", \"logo-apple\"]],\n  template: function RegisterComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ion-content\")(1, \"div\", 0);\n      i0.ɵɵelement(2, \"div\", 1)(3, \"div\", 2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"div\", 3)(5, \"ion-grid\")(6, \"ion-row\")(7, \"ion-col\", 4)(8, \"h1\", 5);\n      i0.ɵɵtext(9, \"Create your \");\n      i0.ɵɵelementStart(10, \"span\", 6);\n      i0.ɵɵtext(11, \"Upshift\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtext(12, \" account\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(13, \"ion-row\")(14, \"ion-col\", 7)(15, \"div\", 8)(16, \"form\", 9);\n      i0.ɵɵlistener(\"ngSubmit\", function RegisterComponent_Template_form_ngSubmit_16_listener() {\n        return ctx.register();\n      });\n      i0.ɵɵelementStart(17, \"div\", 10)(18, \"div\", 11);\n      i0.ɵɵelement(19, \"ion-input\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(20, \"div\", 11);\n      i0.ɵɵelement(21, \"ion-input\", 13);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(22, \"div\", 11);\n      i0.ɵɵelement(23, \"ion-input\", 14);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(24, \"ion-button\", 15);\n      i0.ɵɵtext(25, \" Register \");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(26, \"div\", 16)(27, \"span\");\n      i0.ɵɵtext(28, \"or continue with\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(29, \"div\", 17)(30, \"ion-button\", 18);\n      i0.ɵɵlistener(\"click\", function RegisterComponent_Template_ion_button_click_30_listener() {\n        return ctx.signInWithGoogle();\n      });\n      i0.ɵɵelementStart(31, \"div\", 19);\n      i0.ɵɵelement(32, \"ion-icon\", 20);\n      i0.ɵɵelementStart(33, \"span\");\n      i0.ɵɵtext(34, \"Sign up with Google\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(35, \"ion-button\", 18);\n      i0.ɵɵlistener(\"click\", function RegisterComponent_Template_ion_button_click_35_listener() {\n        return ctx.signInWithApple();\n      });\n      i0.ɵɵelementStart(36, \"div\", 19);\n      i0.ɵɵelement(37, \"ion-icon\", 21);\n      i0.ɵɵelementStart(38, \"span\");\n      i0.ɵɵtext(39, \"Sign up with Apple\");\n      i0.ɵɵelementEnd()()()()()()()()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(16);\n      i0.ɵɵproperty(\"formGroup\", ctx.form);\n    }\n  },\n  dependencies: [CommonModule, IonicModule, i1.IonButton, i1.IonCol, i1.IonContent, i1.IonGrid, i1.IonIcon, i1.IonInput, i1.IonRow, i1.TextValueAccessor, ReactiveFormsModule, i2.ɵNgNoValidate, i2.NgControlStatus, i2.NgControlStatusGroup, i2.RequiredValidator, i2.FormGroupDirective, i2.FormControlName, RouterModule],\n  styles: [\"ion-content[_ngcontent-%COMP%] {\\n  --background: transparent;\\n  position: relative;\\n}\\nion-content[_ngcontent-%COMP%]::part(scroll) {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: 100vh;\\n}\\nion-content[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n  margin-bottom: 20px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXV0aC9yZWdpc3Rlci9yZWdpc3Rlci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLHlCQUFBO0VBQ0Esa0JBQUE7QUFDRjtBQUFFO0VBQ0UsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBQUE7RUFDQSxpQkFBQTtBQUVKO0FBQ0U7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxTQUFBO0VBQ0EsbUJBQUE7QUFDSiIsInNvdXJjZXNDb250ZW50IjpbImlvbi1jb250ZW50IHtcclxuICAtLWJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xyXG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICAmOjpwYXJ0KHNjcm9sbCkge1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgIG1pbi1oZWlnaHQ6IDEwMHZoO1xyXG4gIH1cclxuXHJcbiAgLnNvY2lhbC1idXR0b25zIHtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgZ2FwOiAxMnB4O1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMjBweDtcclxuICB9XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n});", "map": {"version": 3, "names": ["inject", "EnvironmentInjector", "FormBuilder", "ReactiveFormsModule", "Router", "RouterModule", "CommonModule", "IonicModule", "Preferences", "SupabaseService", "UserService", "RegisterComponent", "constructor", "segment", "fb", "supabaseService", "userService", "router", "injector", "form", "group", "email", "password", "confirmPassword", "onSegmentChange", "event", "value", "detail", "login", "_this", "_asyncToGenerator", "alert", "data", "error", "getClient", "auth", "signInWithPassword", "message", "handlePostLogin", "user", "id", "register", "_this2", "signUp", "handlePostRegister", "signInWithGoogle", "_this3", "signInWithOAuth", "provider", "options", "redirectTo", "window", "location", "origin", "signInWithApple", "_this4", "createUserRecord", "userId", "userData", "_this5", "_userData$user_metada", "_userData$user_metada2", "newUser", "username", "name", "user_metadata", "full_name", "profile_picture", "avatar_url", "registration_date", "Date", "last_login", "active", "xp", "level", "title", "plan", "auto_renew", "start_of_current_plan", "end_of_current_plan", "start_of_sick_days", "end_of_sick_days", "subscription_status", "affiliate_code_used", "strength_xp", "money_xp", "health_xp", "knowledge_xp", "from", "insert", "uid", "_this6", "set", "key", "select", "eq", "single", "code", "authUser", "_currentUser", "updateError", "update", "navigateByUrl", "_this7", "selectors", "decls", "vars", "consts", "template", "RegisterComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "RegisterComponent_Template_form_ngSubmit_16_listener", "RegisterComponent_Template_ion_button_click_30_listener", "RegisterComponent_Template_ion_button_click_35_listener", "ɵɵadvance", "ɵɵproperty", "i1", "IonButton", "IonCol", "IonContent", "IonGrid", "IonIcon", "IonInput", "IonRow", "TextValueAccessor", "i2", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\auth\\register\\register.component.ts", "C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\auth\\register\\register.component.html"], "sourcesContent": ["import { Component, inject, EnvironmentInjector, runInInjectionContext } from '@angular/core';\r\nimport { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';\r\nimport { Router, RouterModule } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { Preferences } from '@capacitor/preferences';\r\nimport { SupabaseService } from '../../services/supabase.service';\r\nimport { UserService } from '../../services/user.service';\r\n\r\n@Component({\r\n  selector: 'app-register',\r\n  standalone: true,\r\n  templateUrl: './register.component.html',\r\n  styleUrls: ['./register.component.scss'],\r\n  imports: [CommonModule, IonicModule, ReactiveFormsModule, RouterModule],\r\n})\r\nexport class RegisterComponent {\r\n  form: FormGroup;\r\n  segment: 'login' | 'register' = 'login';\r\n  private fb = inject(FormBuilder);\r\n  private supabaseService = inject(SupabaseService);\r\n  private userService = inject(UserService);\r\n  private router = inject(Router);\r\n  private injector = inject(EnvironmentInjector);\r\n\r\n  constructor() {\r\n    this.form = this.fb.group({\r\n      email: [''],\r\n      password: [''],\r\n      confirmPassword: [''],\r\n    });\r\n  }\r\n\r\n  onSegmentChange(event: CustomEvent) {\r\n    const value = event.detail.value;\r\n    if (value === 'login' || value === 'register') {\r\n      this.segment = value;\r\n    }\r\n  }\r\n\r\n  async login() {\r\n    const { email, password } = this.form.value;\r\n\r\n    if (!email || !password) {\r\n      alert('Please enter both email and password');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const { data, error } = await this.supabaseService.getClient().auth.signInWithPassword({\r\n        email,\r\n        password\r\n      });\r\n\r\n      if (error) {\r\n        alert('Login error: ' + error.message);\r\n        return;\r\n      }\r\n\r\n      await this.handlePostLogin(data.user.id);\r\n    } catch (error: any) {\r\n      alert('Login error: ' + error.message);\r\n    }\r\n  }\r\n\r\n  async register() {\r\n    const { email, password, confirmPassword } = this.form.value;\r\n\r\n    if (password !== confirmPassword) {\r\n      alert('Passwords do not match');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const { data, error } = await this.supabaseService.getClient().auth.signUp({\r\n        email,\r\n        password\r\n      });\r\n\r\n      if (error) {\r\n        alert('Registration error: ' + error.message);\r\n        return;\r\n      }\r\n\r\n      if (data?.user) {\r\n        await this.handlePostRegister(data.user.id);\r\n      } else {\r\n        alert('Registration successful but no user data returned');\r\n      }\r\n    } catch (error: any) {\r\n      alert('Registration error: ' + error.message);\r\n    }\r\n  }\r\n\r\n  async signInWithGoogle() {\r\n    try {\r\n      const { data, error } = await this.supabaseService.getClient().auth.signInWithOAuth({\r\n        provider: 'google',\r\n        options: {\r\n          redirectTo: window.location.origin + '/'\r\n        }\r\n      });\r\n\r\n      if (error) {\r\n        alert('Error signing in with Google: ' + error.message);\r\n        return;\r\n      }\r\n\r\n    } catch (error: any) {\r\n      alert('Error signing in with Google');\r\n    }\r\n  }\r\n\r\n  async signInWithApple() {\r\n    try {\r\n      const { data, error } = await this.supabaseService.getClient().auth.signInWithOAuth({\r\n        provider: 'apple',\r\n        options: {\r\n          redirectTo: window.location.origin + '/'\r\n        }\r\n      });\r\n\r\n      if (error) {\r\n        alert('Error signing in with Apple: ' + error.message);\r\n        return;\r\n      }\r\n\r\n    } catch (error: any) {\r\n      alert('Error signing in with Apple');\r\n    }\r\n  }\r\n\r\n  private async createUserRecord(userId: string, userData: any) {\r\n    try {\r\n      const newUser = {\r\n        id: userId,\r\n        email: userData.email || '',\r\n        username: '',\r\n        name: userData.user_metadata?.full_name || '',\r\n        profile_picture: userData.user_metadata?.avatar_url || '',\r\n        registration_date: new Date(),\r\n        last_login: new Date(),\r\n        active: true,\r\n        xp: null,\r\n        level: null,\r\n        title: null,\r\n        plan: 'none',\r\n        auto_renew: true,\r\n        start_of_current_plan: null,\r\n        end_of_current_plan: null,\r\n        start_of_sick_days: null,\r\n        end_of_sick_days: null,\r\n        subscription_status: 'email marketing',\r\n        affiliate_code_used: null,\r\n        strength_xp: 0,\r\n        money_xp: 0,\r\n        health_xp: 0,\r\n        knowledge_xp: 0,\r\n      };\r\n\r\n      const { error } = await this.supabaseService.getClient()\r\n        .from('profiles')\r\n        .insert(newUser);\r\n\r\n      if (error) {\r\n        throw error;\r\n      }\r\n\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  private async handlePostLogin(uid: string) {\r\n    try {\r\n      await Preferences.set({ key: 'uid', value: uid });\r\n\r\n      const { data: userData, error } = await this.supabaseService.getClient()\r\n        .from('profiles')\r\n        .select('*')\r\n        .eq('id', uid)\r\n        .single();\r\n\r\n      if (error && error.code !== 'PGRST116') { \n      }\r\n\r\n      if (!userData) {\r\n        const authUser = this.supabaseService._currentUser.value;\r\n        if (authUser) {\r\n          await this.createUserRecord(uid, authUser);\r\n        }\r\n      } else {\r\n        const { error: updateError } = await this.supabaseService.getClient()\r\n          .from('profiles')\r\n          .update({ last_login: new Date() })\r\n          .eq('id', uid);\r\n\r\n        if (updateError) {\r\n        }\r\n      }\r\n\r\n      this.router.navigateByUrl('/tabs/home');\r\n    } catch (error) {\r\n    }\r\n  }\r\n\r\n  private async handlePostRegister(uid: string) {\r\n    try {\r\n      await Preferences.set({ key: 'uid', value: uid });\r\n\r\n      const authUser = this.supabaseService._currentUser.value;\r\n      if (authUser) {\r\n        await this.createUserRecord(uid, authUser);\r\n      }\r\n\r\n      this.router.navigateByUrl('/onboarding');\r\n    } catch (error) {\r\n    }\r\n  }\r\n}", "<ion-content>\r\n  <div class=\"background-container\">\r\n    <div class=\"gradient-bg\"></div>\r\n    <div class=\"celestial-body\"></div>\r\n  </div>\r\n\r\n  <div class=\"content-wrapper\">\r\n    <ion-grid>\r\n      <ion-row>\r\n        <ion-col size=\"12\" class=\"ion-text-center\">\r\n          <h1 class=\"main-title\">Create your <span class=\"gradient-text\">Upshift</span> account</h1>\r\n        </ion-col>\r\n      </ion-row>\r\n\r\n      <ion-row>\r\n        <ion-col size=\"12\">\r\n          <div class=\"form-container\">\r\n            <form [formGroup]=\"form\" (ngSubmit)=\"register()\">\r\n              <div class=\"form-fields\">\r\n                <div class=\"input-field\">\r\n                  <ion-input\r\n                    formControlName=\"email\"\r\n                    type=\"email\"\r\n                    placeholder=\"Email\"\r\n                    required\r\n                  ></ion-input>\r\n                </div>\r\n\r\n                <div class=\"input-field\">\r\n                  <ion-input\r\n                    formControlName=\"password\"\r\n                    type=\"password\"\r\n                    placeholder=\"Password\"\r\n                    required\r\n                  ></ion-input>\r\n                </div>\r\n\r\n                <div class=\"input-field\">\r\n                  <ion-input\r\n                    formControlName=\"confirmPassword\"\r\n                    type=\"password\"\r\n                    placeholder=\"Confirm Password\"\r\n                    required\r\n                  ></ion-input>\r\n                </div>\r\n              </div>\r\n\r\n              <ion-button expand=\"block\" type=\"submit\" class=\"submit-button\">\r\n                Register\r\n              </ion-button>\r\n            </form>\r\n\r\n            <div class=\"divider\">\r\n              <span>or continue with</span>\r\n            </div>\r\n\r\n            <div class=\"social-buttons\">\r\n              <ion-button class=\"social-button\" (click)=\"signInWithGoogle()\">\r\n                <div class=\"button-content\">\r\n                  <ion-icon name=\"logo-google\"></ion-icon>\r\n                  <span>Sign up with Google</span>\r\n                </div>\r\n              </ion-button>\r\n\r\n              <ion-button class=\"social-button\" (click)=\"signInWithApple()\">\r\n                <div class=\"button-content\">\r\n                  <ion-icon name=\"logo-apple\"></ion-icon>\r\n                  <span>Sign up with Apple</span>\r\n                </div>\r\n              </ion-button>\r\n            </div>\r\n          </div>\r\n        </ion-col>\r\n      </ion-row>\r\n    </ion-grid>\r\n  </div>\r\n</ion-content>"], "mappings": ";;AAAA,SAAoBA,MAAM,EAAEC,mBAAmB,QAA+B,eAAe;AAC7F,SAASC,WAAW,EAAaC,mBAAmB,QAAQ,gBAAgB;AAC5E,SAASC,MAAM,EAAEC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,WAAW,QAAQ,6BAA6B;;;;AASzD,OAAM,MAAOC,iBAAiB;EAS5BC,YAAA;IAPA,KAAAC,OAAO,GAAyB,OAAO;IAC/B,KAAAC,EAAE,GAAGd,MAAM,CAACE,WAAW,CAAC;IACxB,KAAAa,eAAe,GAAGf,MAAM,CAACS,eAAe,CAAC;IACzC,KAAAO,WAAW,GAAGhB,MAAM,CAACU,WAAW,CAAC;IACjC,KAAAO,MAAM,GAAGjB,MAAM,CAACI,MAAM,CAAC;IACvB,KAAAc,QAAQ,GAAGlB,MAAM,CAACC,mBAAmB,CAAC;IAG5C,IAAI,CAACkB,IAAI,GAAG,IAAI,CAACL,EAAE,CAACM,KAAK,CAAC;MACxBC,KAAK,EAAE,CAAC,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,eAAe,EAAE,CAAC,EAAE;KACrB,CAAC;EACJ;EAEAC,eAAeA,CAACC,KAAkB;IAChC,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IAChC,IAAIA,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,UAAU,EAAE;MAC7C,IAAI,CAACb,OAAO,GAAGa,KAAK;IACtB;EACF;EAEME,KAAKA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACT,MAAM;QAAET,KAAK;QAAEC;MAAQ,CAAE,GAAGO,KAAI,CAACV,IAAI,CAACO,KAAK;MAE3C,IAAI,CAACL,KAAK,IAAI,CAACC,QAAQ,EAAE;QACvBS,KAAK,CAAC,sCAAsC,CAAC;QAC7C;MACF;MAEA,IAAI;QACF,MAAM;UAAEC,IAAI;UAAEC;QAAK,CAAE,SAASJ,KAAI,CAACd,eAAe,CAACmB,SAAS,EAAE,CAACC,IAAI,CAACC,kBAAkB,CAAC;UACrFf,KAAK;UACLC;SACD,CAAC;QAEF,IAAIW,KAAK,EAAE;UACTF,KAAK,CAAC,eAAe,GAAGE,KAAK,CAACI,OAAO,CAAC;UACtC;QACF;QAEA,MAAMR,KAAI,CAACS,eAAe,CAACN,IAAI,CAACO,IAAI,CAACC,EAAE,CAAC;MAC1C,CAAC,CAAC,OAAOP,KAAU,EAAE;QACnBF,KAAK,CAAC,eAAe,GAAGE,KAAK,CAACI,OAAO,CAAC;MACxC;IAAC;EACH;EAEMI,QAAQA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAZ,iBAAA;MACZ,MAAM;QAAET,KAAK;QAAEC,QAAQ;QAAEC;MAAe,CAAE,GAAGmB,MAAI,CAACvB,IAAI,CAACO,KAAK;MAE5D,IAAIJ,QAAQ,KAAKC,eAAe,EAAE;QAChCQ,KAAK,CAAC,wBAAwB,CAAC;QAC/B;MACF;MAEA,IAAI;QACF,MAAM;UAAEC,IAAI;UAAEC;QAAK,CAAE,SAASS,MAAI,CAAC3B,eAAe,CAACmB,SAAS,EAAE,CAACC,IAAI,CAACQ,MAAM,CAAC;UACzEtB,KAAK;UACLC;SACD,CAAC;QAEF,IAAIW,KAAK,EAAE;UACTF,KAAK,CAAC,sBAAsB,GAAGE,KAAK,CAACI,OAAO,CAAC;UAC7C;QACF;QAEA,IAAIL,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEO,IAAI,EAAE;UACd,MAAMG,MAAI,CAACE,kBAAkB,CAACZ,IAAI,CAACO,IAAI,CAACC,EAAE,CAAC;QAC7C,CAAC,MAAM;UACLT,KAAK,CAAC,mDAAmD,CAAC;QAC5D;MACF,CAAC,CAAC,OAAOE,KAAU,EAAE;QACnBF,KAAK,CAAC,sBAAsB,GAAGE,KAAK,CAACI,OAAO,CAAC;MAC/C;IAAC;EACH;EAEMQ,gBAAgBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAhB,iBAAA;MACpB,IAAI;QACF,MAAM;UAAEE,IAAI;UAAEC;QAAK,CAAE,SAASa,MAAI,CAAC/B,eAAe,CAACmB,SAAS,EAAE,CAACC,IAAI,CAACY,eAAe,CAAC;UAClFC,QAAQ,EAAE,QAAQ;UAClBC,OAAO,EAAE;YACPC,UAAU,EAAEC,MAAM,CAACC,QAAQ,CAACC,MAAM,GAAG;;SAExC,CAAC;QAEF,IAAIpB,KAAK,EAAE;UACTF,KAAK,CAAC,gCAAgC,GAAGE,KAAK,CAACI,OAAO,CAAC;UACvD;QACF;MAEF,CAAC,CAAC,OAAOJ,KAAU,EAAE;QACnBF,KAAK,CAAC,8BAA8B,CAAC;MACvC;IAAC;EACH;EAEMuB,eAAeA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAzB,iBAAA;MACnB,IAAI;QACF,MAAM;UAAEE,IAAI;UAAEC;QAAK,CAAE,SAASsB,MAAI,CAACxC,eAAe,CAACmB,SAAS,EAAE,CAACC,IAAI,CAACY,eAAe,CAAC;UAClFC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE;YACPC,UAAU,EAAEC,MAAM,CAACC,QAAQ,CAACC,MAAM,GAAG;;SAExC,CAAC;QAEF,IAAIpB,KAAK,EAAE;UACTF,KAAK,CAAC,+BAA+B,GAAGE,KAAK,CAACI,OAAO,CAAC;UACtD;QACF;MAEF,CAAC,CAAC,OAAOJ,KAAU,EAAE;QACnBF,KAAK,CAAC,6BAA6B,CAAC;MACtC;IAAC;EACH;EAEcyB,gBAAgBA,CAACC,MAAc,EAAEC,QAAa;IAAA,IAAAC,MAAA;IAAA,OAAA7B,iBAAA;MAC1D,IAAI;QAAA,IAAA8B,qBAAA,EAAAC,sBAAA;QACF,MAAMC,OAAO,GAAG;UACdtB,EAAE,EAAEiB,MAAM;UACVpC,KAAK,EAAEqC,QAAQ,CAACrC,KAAK,IAAI,EAAE;UAC3B0C,QAAQ,EAAE,EAAE;UACZC,IAAI,EAAE,EAAAJ,qBAAA,GAAAF,QAAQ,CAACO,aAAa,cAAAL,qBAAA,uBAAtBA,qBAAA,CAAwBM,SAAS,KAAI,EAAE;UAC7CC,eAAe,EAAE,EAAAN,sBAAA,GAAAH,QAAQ,CAACO,aAAa,cAAAJ,sBAAA,uBAAtBA,sBAAA,CAAwBO,UAAU,KAAI,EAAE;UACzDC,iBAAiB,EAAE,IAAIC,IAAI,EAAE;UAC7BC,UAAU,EAAE,IAAID,IAAI,EAAE;UACtBE,MAAM,EAAE,IAAI;UACZC,EAAE,EAAE,IAAI;UACRC,KAAK,EAAE,IAAI;UACXC,KAAK,EAAE,IAAI;UACXC,IAAI,EAAE,MAAM;UACZC,UAAU,EAAE,IAAI;UAChBC,qBAAqB,EAAE,IAAI;UAC3BC,mBAAmB,EAAE,IAAI;UACzBC,kBAAkB,EAAE,IAAI;UACxBC,gBAAgB,EAAE,IAAI;UACtBC,mBAAmB,EAAE,iBAAiB;UACtCC,mBAAmB,EAAE,IAAI;UACzBC,WAAW,EAAE,CAAC;UACdC,QAAQ,EAAE,CAAC;UACXC,SAAS,EAAE,CAAC;UACZC,YAAY,EAAE;SACf;QAED,MAAM;UAAEtD;QAAK,CAAE,SAAS0B,MAAI,CAAC5C,eAAe,CAACmB,SAAS,EAAE,CACrDsD,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC3B,OAAO,CAAC;QAElB,IAAI7B,KAAK,EAAE;UACT,MAAMA,KAAK;QACb;MAEF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACd,MAAMA,KAAK;MACb;IAAC;EACH;EAEcK,eAAeA,CAACoD,GAAW;IAAA,IAAAC,MAAA;IAAA,OAAA7D,iBAAA;MACvC,IAAI;QACF,MAAMtB,WAAW,CAACoF,GAAG,CAAC;UAAEC,GAAG,EAAE,KAAK;UAAEnE,KAAK,EAAEgE;QAAG,CAAE,CAAC;QAEjD,MAAM;UAAE1D,IAAI,EAAE0B,QAAQ;UAAEzB;QAAK,CAAE,SAAS0D,MAAI,CAAC5E,eAAe,CAACmB,SAAS,EAAE,CACrEsD,IAAI,CAAC,UAAU,CAAC,CAChBM,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,IAAI,EAAEL,GAAG,CAAC,CACbM,MAAM,EAAE;QAEX,IAAI/D,KAAK,IAAIA,KAAK,CAACgE,IAAI,KAAK,UAAU,EAAE,CACxC;QAEA,IAAI,CAACvC,QAAQ,EAAE;UACb,MAAMwC,QAAQ,GAAGP,MAAI,CAAC5E,eAAe,CAACoF,YAAY,CAACzE,KAAK;UACxD,IAAIwE,QAAQ,EAAE;YACZ,MAAMP,MAAI,CAACnC,gBAAgB,CAACkC,GAAG,EAAEQ,QAAQ,CAAC;UAC5C;QACF,CAAC,MAAM;UACL,MAAM;YAAEjE,KAAK,EAAEmE;UAAW,CAAE,SAAST,MAAI,CAAC5E,eAAe,CAACmB,SAAS,EAAE,CAClEsD,IAAI,CAAC,UAAU,CAAC,CAChBa,MAAM,CAAC;YAAE9B,UAAU,EAAE,IAAID,IAAI;UAAE,CAAE,CAAC,CAClCyB,EAAE,CAAC,IAAI,EAAEL,GAAG,CAAC;UAEhB,IAAIU,WAAW,EAAE,CACjB;QACF;QAEAT,MAAI,CAAC1E,MAAM,CAACqF,aAAa,CAAC,YAAY,CAAC;MACzC,CAAC,CAAC,OAAOrE,KAAK,EAAE,CAChB;IAAC;EACH;EAEcW,kBAAkBA,CAAC8C,GAAW;IAAA,IAAAa,MAAA;IAAA,OAAAzE,iBAAA;MAC1C,IAAI;QACF,MAAMtB,WAAW,CAACoF,GAAG,CAAC;UAAEC,GAAG,EAAE,KAAK;UAAEnE,KAAK,EAAEgE;QAAG,CAAE,CAAC;QAEjD,MAAMQ,QAAQ,GAAGK,MAAI,CAACxF,eAAe,CAACoF,YAAY,CAACzE,KAAK;QACxD,IAAIwE,QAAQ,EAAE;UACZ,MAAMK,MAAI,CAAC/C,gBAAgB,CAACkC,GAAG,EAAEQ,QAAQ,CAAC;QAC5C;QAEAK,MAAI,CAACtF,MAAM,CAACqF,aAAa,CAAC,aAAa,CAAC;MAC1C,CAAC,CAAC,OAAOrE,KAAK,EAAE,CAChB;IAAC;EACH;;qBA1MWtB,iBAAiB;;mCAAjBA,kBAAiB;AAAA;;QAAjBA,kBAAiB;EAAA6F,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCf5BE,EADF,CAAAC,cAAA,kBAAa,aACuB;MAEhCD,EADA,CAAAE,SAAA,aAA+B,aACG;MACpCF,EAAA,CAAAG,YAAA,EAAM;MAMEH,EAJR,CAAAC,cAAA,aAA6B,eACjB,cACC,iBACoC,YAClB;MAAAD,EAAA,CAAAI,MAAA,mBAAY;MAAAJ,EAAA,CAAAC,cAAA,eAA4B;MAAAD,EAAA,CAAAI,MAAA,eAAO;MAAAJ,EAAA,CAAAG,YAAA,EAAO;MAACH,EAAA,CAAAI,MAAA,gBAAO;MAEzFJ,EAFyF,CAAAG,YAAA,EAAK,EAClF,EACF;MAKJH,EAHN,CAAAC,cAAA,eAAS,kBACY,cACW,eACuB;MAAxBD,EAAA,CAAAK,UAAA,sBAAAC,qDAAA;QAAA,OAAYP,GAAA,CAAAtE,QAAA,EAAU;MAAA,EAAC;MAE5CuE,EADF,CAAAC,cAAA,eAAyB,eACE;MACvBD,EAAA,CAAAE,SAAA,qBAKa;MACfF,EAAA,CAAAG,YAAA,EAAM;MAENH,EAAA,CAAAC,cAAA,eAAyB;MACvBD,EAAA,CAAAE,SAAA,qBAKa;MACfF,EAAA,CAAAG,YAAA,EAAM;MAENH,EAAA,CAAAC,cAAA,eAAyB;MACvBD,EAAA,CAAAE,SAAA,qBAKa;MAEjBF,EADE,CAAAG,YAAA,EAAM,EACF;MAENH,EAAA,CAAAC,cAAA,sBAA+D;MAC7DD,EAAA,CAAAI,MAAA,kBACF;MACFJ,EADE,CAAAG,YAAA,EAAa,EACR;MAGLH,EADF,CAAAC,cAAA,eAAqB,YACb;MAAAD,EAAA,CAAAI,MAAA,wBAAgB;MACxBJ,EADwB,CAAAG,YAAA,EAAO,EACzB;MAGJH,EADF,CAAAC,cAAA,eAA4B,sBACqC;MAA7BD,EAAA,CAAAK,UAAA,mBAAAE,wDAAA;QAAA,OAASR,GAAA,CAAAlE,gBAAA,EAAkB;MAAA,EAAC;MAC5DmE,EAAA,CAAAC,cAAA,eAA4B;MAC1BD,EAAA,CAAAE,SAAA,oBAAwC;MACxCF,EAAA,CAAAC,cAAA,YAAM;MAAAD,EAAA,CAAAI,MAAA,2BAAmB;MAE7BJ,EAF6B,CAAAG,YAAA,EAAO,EAC5B,EACK;MAEbH,EAAA,CAAAC,cAAA,sBAA8D;MAA5BD,EAAA,CAAAK,UAAA,mBAAAG,wDAAA;QAAA,OAAST,GAAA,CAAAzD,eAAA,EAAiB;MAAA,EAAC;MAC3D0D,EAAA,CAAAC,cAAA,eAA4B;MAC1BD,EAAA,CAAAE,SAAA,oBAAuC;MACvCF,EAAA,CAAAC,cAAA,YAAM;MAAAD,EAAA,CAAAI,MAAA,0BAAkB;MAS1CJ,EAT0C,CAAAG,YAAA,EAAO,EAC3B,EACK,EACT,EACF,EACE,EACF,EACD,EACP,EACM;;;MA3DIH,EAAA,CAAAS,SAAA,IAAkB;MAAlBT,EAAA,CAAAU,UAAA,cAAAX,GAAA,CAAA5F,IAAA,CAAkB;;;iBDHxBb,YAAY,EAAEC,WAAW,EAAAoH,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,MAAA,EAAAF,EAAA,CAAAG,UAAA,EAAAH,EAAA,CAAAI,OAAA,EAAAJ,EAAA,CAAAK,OAAA,EAAAL,EAAA,CAAAM,QAAA,EAAAN,EAAA,CAAAO,MAAA,EAAAP,EAAA,CAAAQ,iBAAA,EAAEhI,mBAAmB,EAAAiI,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,oBAAA,EAAAH,EAAA,CAAAI,iBAAA,EAAAJ,EAAA,CAAAK,kBAAA,EAAAL,EAAA,CAAAM,eAAA,EAAErI,YAAY;EAAAsI,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}