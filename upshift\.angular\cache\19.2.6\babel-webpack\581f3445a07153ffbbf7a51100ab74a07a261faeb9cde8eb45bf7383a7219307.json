{"ast": null, "code": "var _TransitionComponent;\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst _c0 = [\"videoPlayer\"];\nexport class TransitionComponent {\n  constructor(router) {\n    this.router = router;\n  }\n  ngOnInit() {}\n  ngAfterViewInit() {\n    const video = this.videoPlayer.nativeElement;\n    video.play();\n    setTimeout(() => {\n      this.startSpeedingUp(video);\n    }, 2000);\n    video.addEventListener('ended', () => {\n      this.navigateToOnboarding();\n    });\n  }\n  startSpeedingUp(video) {\n    let playbackRate = 1.0;\n    let scale = 1.0;\n    const interval = setInterval(() => {\n      playbackRate += 0.3;\n      scale += 0.15;\n      if (playbackRate <= 5.0) {\n        video.playbackRate = playbackRate;\n        video.style.transform = `scale(${scale})`;\n      } else {\n        clearInterval(interval);\n        this.addFlashEffect();\n      }\n    }, 50);\n  }\n  addFlashEffect() {\n    const flash = document.createElement('div');\n    flash.className = 'flash-effect';\n    document.body.appendChild(flash);\n    setTimeout(() => {\n      flash.style.opacity = '1';\n      setTimeout(() => {\n        this.navigateToOnboarding();\n      }, 150);\n    }, 10);\n  }\n  navigateToOnboarding() {\n    this.router.navigate(['/onboarding']);\n  }\n}\n_TransitionComponent = TransitionComponent;\n_TransitionComponent.ɵfac = function TransitionComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _TransitionComponent)(i0.ɵɵdirectiveInject(i1.Router));\n};\n_TransitionComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _TransitionComponent,\n  selectors: [[\"app-transition\"]],\n  viewQuery: function TransitionComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.videoPlayer = _t.first);\n    }\n  },\n  decls: 4,\n  vars: 2,\n  consts: [[\"videoPlayer\", \"\"], [1, \"transition-container\"], [1, \"video-wrapper\"], [1, \"transition-video\", 3, \"src\", \"muted\"]],\n  template: function TransitionComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n      i0.ɵɵelement(2, \"video\", 3, 0);\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"src\", \"assets/animations/animation.mp4\", i0.ɵɵsanitizeUrl)(\"muted\", true);\n    }\n  },\n  dependencies: [CommonModule],\n  styles: [\".transition-container[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: #000;\\n  z-index: 9999;\\n  overflow: hidden;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.transition-container[_ngcontent-%COMP%]   .video-wrapper[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  overflow: hidden;\\n}\\n.transition-container[_ngcontent-%COMP%]   .transition-video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.05s linear;\\n  transform-origin: center center;\\n}\\n\\n.flash-effect[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: white;\\n  z-index: 10000;\\n  opacity: 0;\\n  transition: opacity 0.15s ease-in-out;\\n  pointer-events: none;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdHJhbnNpdGlvbi90cmFuc2l0aW9uLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0ksZUFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxzQkFBQTtFQUNBLGFBQUE7RUFDQSxnQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0FBQ0o7QUFDSTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxnQkFBQTtBQUNOO0FBRUk7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGlCQUFBO0VBQ0Esa0NBQUE7RUFDQSwrQkFBQTtBQUFOOztBQUlFO0VBQ0UsZUFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSx1QkFBQTtFQUNBLGNBQUE7RUFDQSxVQUFBO0VBQ0EscUNBQUE7RUFDQSxvQkFBQTtBQURKIiwic291cmNlc0NvbnRlbnQiOlsiLnRyYW5zaXRpb24tY29udGFpbmVyIHtcclxuICAgIHBvc2l0aW9uOiBmaXhlZDtcclxuICAgIHRvcDogMDtcclxuICAgIGxlZnQ6IDA7XHJcbiAgICB3aWR0aDogMTAwJTtcclxuICAgIGhlaWdodDogMTAwJTtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICMwMDA7XHJcbiAgICB6LWluZGV4OiA5OTk5O1xyXG4gICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgXHJcbiAgICAudmlkZW8td3JhcHBlciB7XHJcbiAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICBoZWlnaHQ6IDEwMCU7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gICAgICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gICAgfVxyXG4gIFxyXG4gICAgLnRyYW5zaXRpb24tdmlkZW8ge1xyXG4gICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgICBvYmplY3QtZml0OiBjb3ZlcjtcclxuICAgICAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuMDVzIGxpbmVhcjsgXG4gICAgICB0cmFuc2Zvcm0tb3JpZ2luOiBjZW50ZXIgY2VudGVyOyBcbiAgICB9XHJcbiAgfVxyXG4gIFxyXG4gIC5mbGFzaC1lZmZlY3Qge1xyXG4gICAgcG9zaXRpb246IGZpeGVkO1xyXG4gICAgdG9wOiAwO1xyXG4gICAgbGVmdDogMDtcclxuICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7XHJcbiAgICB6LWluZGV4OiAxMDAwMDtcclxuICAgIG9wYWNpdHk6IDA7XHJcbiAgICB0cmFuc2l0aW9uOiBvcGFjaXR5IDAuMTVzIGVhc2UtaW4tb3V0O1xyXG4gICAgcG9pbnRlci1ldmVudHM6IG5vbmU7XHJcbiAgfSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n});", "map": {"version": 3, "names": ["CommonModule", "TransitionComponent", "constructor", "router", "ngOnInit", "ngAfterViewInit", "video", "videoPlayer", "nativeElement", "play", "setTimeout", "startSpeedingUp", "addEventListener", "navigateToOnboarding", "playbackRate", "scale", "interval", "setInterval", "style", "transform", "clearInterval", "addFlashEffect", "flash", "document", "createElement", "className", "body", "append<PERSON><PERSON><PERSON>", "opacity", "navigate", "i0", "ɵɵdirectiveInject", "i1", "Router", "selectors", "viewQuery", "TransitionComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵsanitizeUrl", "styles"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\transition\\transition.component.ts", "C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\transition\\transition.component.html"], "sourcesContent": ["import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\n\r\n@Component({\r\n  selector: 'app-transition',\r\n  templateUrl: './transition.component.html',\r\n  styleUrls: ['./transition.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule]\r\n})\r\nexport class TransitionComponent implements OnInit {\r\n  @ViewChild('videoPlayer') videoPlayer!: ElementRef<HTMLVideoElement>;\r\n\r\n  constructor(private router: Router) { }\r\n\r\n  ngOnInit(): void {\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    const video = this.videoPlayer.nativeElement;\r\n\r\n    video.play();\r\n\r\n    setTimeout(() => {\r\n      this.startSpeedingUp(video);\r\n    }, 2000);\r\n\r\n    video.addEventListener('ended', () => {\r\n      this.navigateToOnboarding();\r\n    });\r\n  }\r\n\r\n  startSpeedingUp(video: HTMLVideoElement): void {\r\n    let playbackRate = 1.0;\r\n    let scale = 1.0;\r\n\r\n    const interval = setInterval(() => {\r\n      playbackRate += 0.3; \n      scale += 0.15;      \n\r\n      if (playbackRate <= 5.0) { \n        video.playbackRate = playbackRate;\r\n        video.style.transform = `scale(${scale})`;\r\n      } else {\r\n        clearInterval(interval);\r\n        this.addFlashEffect();\r\n      }\r\n    }, 50); \n  }\r\n\r\n  addFlashEffect(): void {\r\n    const flash = document.createElement('div');\r\n    flash.className = 'flash-effect';\r\n    document.body.appendChild(flash);\r\n\r\n    setTimeout(() => {\r\n      flash.style.opacity = '1';\r\n\r\n      setTimeout(() => {\r\n        this.navigateToOnboarding();\r\n      }, 150);\r\n    }, 10);\r\n  }\r\n\r\n  navigateToOnboarding(): void {\r\n    this.router.navigate(['/onboarding']);\r\n  }\r\n}", "<div class=\"transition-container\">\r\n    <div class=\"video-wrapper\">\r\n      <video #videoPlayer class=\"transition-video\" [src]=\"'assets/animations/animation.mp4'\" [muted]=\"true\"></video>\r\n    </div>\r\n  </div>"], "mappings": ";AAEA,SAASA,YAAY,QAAQ,iBAAiB;;;;AAS9C,OAAM,MAAOC,mBAAmB;EAG9BC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;EAAY;EAEtCC,QAAQA,CAAA,GACR;EAEAC,eAAeA,CAAA;IACb,MAAMC,KAAK,GAAG,IAAI,CAACC,WAAW,CAACC,aAAa;IAE5CF,KAAK,CAACG,IAAI,EAAE;IAEZC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,eAAe,CAACL,KAAK,CAAC;IAC7B,CAAC,EAAE,IAAI,CAAC;IAERA,KAAK,CAACM,gBAAgB,CAAC,OAAO,EAAE,MAAK;MACnC,IAAI,CAACC,oBAAoB,EAAE;IAC7B,CAAC,CAAC;EACJ;EAEAF,eAAeA,CAACL,KAAuB;IACrC,IAAIQ,YAAY,GAAG,GAAG;IACtB,IAAIC,KAAK,GAAG,GAAG;IAEf,MAAMC,QAAQ,GAAGC,WAAW,CAAC,MAAK;MAChCH,YAAY,IAAI,GAAG;MACnBC,KAAK,IAAI,IAAI;MAEb,IAAID,YAAY,IAAI,GAAG,EAAE;QACvBR,KAAK,CAACQ,YAAY,GAAGA,YAAY;QACjCR,KAAK,CAACY,KAAK,CAACC,SAAS,GAAG,SAASJ,KAAK,GAAG;MAC3C,CAAC,MAAM;QACLK,aAAa,CAACJ,QAAQ,CAAC;QACvB,IAAI,CAACK,cAAc,EAAE;MACvB;IACF,CAAC,EAAE,EAAE,CAAC;EACR;EAEAA,cAAcA,CAAA;IACZ,MAAMC,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC3CF,KAAK,CAACG,SAAS,GAAG,cAAc;IAChCF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,KAAK,CAAC;IAEhCZ,UAAU,CAAC,MAAK;MACdY,KAAK,CAACJ,KAAK,CAACU,OAAO,GAAG,GAAG;MAEzBlB,UAAU,CAAC,MAAK;QACd,IAAI,CAACG,oBAAoB,EAAE;MAC7B,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,EAAE,EAAE,CAAC;EACR;EAEAA,oBAAoBA,CAAA;IAClB,IAAI,CAACV,MAAM,CAAC0B,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;;uBAxDW5B,mBAAmB;;mCAAnBA,oBAAmB,EAAA6B,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA;AAAA;;QAAnBhC,oBAAmB;EAAAiC,SAAA;EAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;;;;;;;;;;;;;MCV5BP,EADJ,CAAAS,cAAA,aAAkC,aACH;MACzBT,EAAA,CAAAU,SAAA,kBAA8G;MAElHV,EADE,CAAAW,YAAA,EAAM,EACF;;;MAF2CX,EAAA,CAAAY,SAAA,GAAyC;MAACZ,EAA1C,CAAAa,UAAA,2CAAAb,EAAA,CAAAc,aAAA,CAAyC,eAAe;;;iBDO/F5C,YAAY;EAAA6C,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}