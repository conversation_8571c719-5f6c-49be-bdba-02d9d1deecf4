{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\models\\supabase.models.ts"], "sourcesContent": ["\r\nexport interface UserProfile {\r\n  id: string;                      \n  username: string;                \n  profile_picture?: string;        \n  active: boolean;                 \n  strength_xp: number;             \n  money_xp: number;                \n  health_xp: number;               \n  knowledge_xp: number;            \n  level: number;                   \n  title: string;                   \n  bio: string;                     \n  timezone: string;                \n  friend_code?: string;            \n  friend_code_expiry?: Date;       \n  plan?: string;                   \n  start_of_current_plan?: Date;    \n  end_of_current_plan?: Date;      \n  auto_renew: boolean;             \n  start_of_sick_days?: Date;       \n  end_of_sick_days?: Date;         \n  sidequests_switch: boolean;      \n  show_celebration: boolean;       \n  celebration_name: string;        \n  celebration_description: string; \n  celebration_emoji?: string;      \n  subscription_status: string;     \n}\r\n\r\nexport interface UserBadge {\r\n  id: string;                      \n  user_id: string;                 \n  badge_newbie?: boolean;          \n  badge_warrior?: boolean;         \n  badge_monk?: boolean;            \n  badge_nonchalant?: boolean;      \n  badge_hardcore?: boolean;        \n  badge_disciplined_machine?: boolean; \n  badge_high_performer?: boolean;  \n  badge_master_of_consistency?: boolean; \n  badge_peak_performer?: boolean;  \n  badge_elite_operator?: boolean;  \n  badge_indestructible?: boolean;  \n  badge_ultra_human?: boolean;     \n  badge_professional?: boolean;    \n  created_at: Date;                \n  updated_at: Date;                \n}\r\n\r\nexport interface SubscriptionHistory {\r\n  id: string;                      \n  user_id: string;                 \n  plan: string;                    \n  start_date_of_subscription: Date;\r\n  end_date_of_subscription?: Date; \n  created: Date;                   \n  updated: Date;                   \n}\r\n\r\nexport interface Quest {\r\n  id: string;                      \n  user_id: string;                 \n  name: string;\r\n  description?: string;            \n  active: boolean;                 \n  quest_type: string;              \n  streak: number;                  \n  goal_value: number;              \n  goal_unit: string;               \n  goal_period: string;             \n  priority: string;                \n  category: string;                \n  task_days_of_week?: string;      \n  task_days_of_month?: string;     \n  custom_reminder_times?: string;  \n  created_at: Date;                \n  emoji: string;                   \n}\r\n\r\nexport interface QuestProgress {\r\n  id: string;                      \n  user_id: string;                 \n  quest_id: string;                \n  date: Date;\r\n  completed: boolean;              \n  value_achieved: number;          \n  historical_streak?: number;      \n}\r\n\r\nexport interface DailySideQuestPool {\r\n  id: string;                      \n  name: string;\r\n  description?: string;            \n  goal_value: number;              \n  category: string;                \n  goal_unit: string;               \n  active: boolean;                 \n  emoji: string;                   \n}\r\n\r\nexport interface UserDailySideQuest {\r\n  id: string;                      \n  user_id: string;                 \n  current_quest_id: string;        \n  streak: number;                  \n  last_completed_date?: string | null;    \n  date_assigned: string;           \n  completed: boolean;              \n  value_achieved: number;          \n  category: string;                \n  emoji: string;                   \n}\r\n\r\nexport interface DayTracking {\r\n  id: string;                      \n  user_id: string;                 \n  date: Date;\r\n}\r\n\r\nexport interface Activity {\r\n  id: string;                      \n  day_tracking_id: string;         \n  name: string;\r\n  emoji: string;                   \n  hours: number;                   \n  minutes: number;                 \n  is_custom: boolean;              \n}\r\n\r\nexport interface ActivityType {\r\n  id: string;                      \n  name: string;                    \n  emoji: string;\r\n  is_active: boolean;              \n  order: number;                   \n}\r\n\r\nexport interface Goal {\r\n  id: string;                      \n  user_id: string;                 \n  name: string;\r\n  description?: string;            \n  emoji: string;                   \n  start_date: Date;                \n  end_date?: Date;                 \n  goal_value: number;              \n  current_value: number;           \n  goal_unit: string;\r\n  before_photo?: string;           \n  after_photo?: string;            \n}\r\n\r\nexport interface MicroGoal {\r\n  id: string;                      \n  goal_id: string;                 \n  title: string;\r\n  completed: boolean;              \n  completed_at?: Date;             \n}\r\n\r\nexport interface GoalJournalEntry {\r\n  id: string;                      \n  goal_id: string;                 \n  milestone_percentage: number;    \n  content: string;\r\n  created_at: Date;                \n}\r\n\r\nexport interface Friend {\r\n  id: string;                      \n  user_id: string;                 \n  friend_id: string;               \n  created: Date;                   \n}\r\n\r\nexport interface Group {\r\n  id: string;                      \n  name: string;\r\n  emoji: string;                   \n  admin_id: string;                \n  enable_sidequests: boolean;      \n  created: Date;                   \n  timezone: string;                \n  level: number;                   \n  strength_xp: number;             \n  money_xp: number;                \n  health_xp: number;               \n  knowledge_xp: number;            \n  invitation_code?: string;        \n  code_expiry?: Date;              \n}\r\n\r\nexport interface GroupMember {\r\n  id: string;                      \n  group_id: string;                \n  user_id: string;                 \n  nickname: string;\r\n  is_admin: boolean;               \n  joined_date: Date;               \n}\r\n\r\nexport interface GroupQuest {\r\n  id: string;                      \n  group_id: string;                \n  name: string;\r\n  description?: string;            \n  emoji: string;                   \n  category: string;\r\n  priority: string;                \n  quest_type: string;              \n  goal_value: number;              \n  goal_unit: string;\r\n  goal_period: string;\r\n  task_days_of_week?: string;      \n  task_days_of_month?: string;     \n  streak: number;                  \n  created: Date;                   \n}\r\n\r\nexport interface GroupQuestProgress {\r\n  id: string;                      \n  quest_id: string;                \n  user_id: string;                 \n  date: Date;\r\n  value_achieved: number;          \n  completed: boolean;              \n}\r\n\r\nexport interface GroupJoinRequest {\r\n  id: string;                      \n  group_id: string;                \n  username_invited: string;        \n  invited_by: string;              \n  created: Date;                   \n}\r\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}