﻿import { Injectable } from '@angular/core';
import { DailySideQuestPool, UserDailySideQuest } from '../models/sidequest.model';
import { Observable, map, of, from, switchMap, catchError, take } from 'rxjs';
import { UserService } from './user.service';
import { HttpClient } from '@angular/common/http';
import { SupabaseService } from './supabase.service';

@Injectable({
  providedIn: 'root'
})
export class SideQuestService {
  constructor(
    private supabaseService: SupabaseService,
    private userService: UserService,
    private http: HttpClient
  ) {}

  getSideQuestPools(): Observable<DailySideQuestPool[]> {


    return from(
      this.supabaseService.getClient()
        .from('daily_sidequest_pool')
        .select('*')
        .eq('active', true)
    ).pipe(
      map(response => {
        if (response.error) {
          return [];
        }


        return response.data as DailySideQuestPool[];
      }),
      catchError(error => {
        return of([]);
      })
    );
  }

  getUserDailySideQuest(userId: string): Observable<UserDailySideQuest | null> {


    return from(
      this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*')
        .eq('user_id', userId)
    ).pipe(
      map(response => {
        if (response.error) {
          return null;
        }


        return response.data.length > 0 ? response.data[0] as UserDailySideQuest : null;
      }),
      catchError(error => {
        return of(null);
      })
    );
  }

  async createUserDailySideQuest(userId: string): Promise<string> {


    try {
      const { data: poolQuests, error: poolError } = await this.supabaseService.getClient()
        .from('daily_sidequest_pool')
        .select('*')
        .eq('active', true);

      if (poolError) {

        throw new Error('Error getting side quest pool: ' + poolError.message);
      }

      if (!poolQuests || poolQuests.length === 0) {
        throw new Error('No active side quests available');
      }



      const randomIndex = Math.floor(Math.random() * poolQuests.length);
      const randomQuest = poolQuests[randomIndex];


      const today = new Date();
      const dateString = today.toISOString().split('T')[0];

      const newSideQuest: Omit<UserDailySideQuest, 'id'> = {
        user_id: userId,
        current_quest_id: randomQuest.id,
        streak: 0,
        date_assigned: dateString,
        completed: false,
        value_achieved: 0,
        category: randomQuest.category,
        emoji: randomQuest.emoji,
        last_completed_date: null
      };



      const { data: insertData, error: insertError } = await this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .insert(newSideQuest)
        .select('id')
        .single();

      if (insertError) {
        throw new Error('Error creating side quest: ' + insertError.message);
      }


      return insertData.id;
    } catch (error) {
      throw error;
    }
  }

  async toggleSideQuestCompletion(sideQuestId: string, userId: string, selectedDate?: Date): Promise<{
    completed: boolean;
    value_achieved: number;
    streak: number;
    goal_value: number;
    goal_unit: string;
  }> {


    try {
      const { data: sideQuest, error: questError } = await this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*')
        .eq('id', sideQuestId)
        .single();

      if (questError || !sideQuest) {
        throw new Error('Side quest not found');
      }

      const wasCompleted = sideQuest.completed;

      const { data: questPool, error: poolError } = await this.supabaseService.getClient()
        .from('daily_sidequest_pool')
        .select('*')
        .eq('id', sideQuest.current_quest_id)
        .single();

      if (poolError || !questPool) {
        throw new Error('Side quest pool item not found');
      }



      const isCompleted = !wasCompleted;
      const today = selectedDate || new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      let newStreak = sideQuest.streak;
      let newLastCompletedDate = sideQuest.last_completed_date;
      let newValueAchieved = sideQuest.value_achieved;

      if (isCompleted) {
        newValueAchieved = questPool.goal_value;

        if (sideQuest.last_completed_date) {
          const lastCompletedDate = new Date(sideQuest.last_completed_date);
          const lastCompletedDay = lastCompletedDate.getDate();
          const yesterdayDay = yesterday.getDate();

          if (lastCompletedDay === yesterdayDay) {
            newStreak += 1;
          } else {
            newStreak = 1;
          }
        } else {
          newStreak = 1;
        }

        newLastCompletedDate = today;
      } else {
        newValueAchieved = 0;

        if (sideQuest.last_completed_date && new Date(sideQuest.last_completed_date).getDate() === today.getDate()) {
          if (newStreak > 1) {
            newStreak -= 1;
            newLastCompletedDate = yesterday;
          } else {
            newStreak = 0;
            newLastCompletedDate = yesterday;  
          }
        }
      }

      const { error: updateError } = await this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .update({
          completed: isCompleted,
          value_achieved: newValueAchieved,
          streak: newStreak,
          last_completed_date: newLastCompletedDate
        })
        .eq('id', sideQuestId);

      if (updateError) {
        throw new Error('Error updating side quest: ' + updateError.message);
      }


      if (isCompleted !== wasCompleted) {
        await this.updateUserXP(userId, sideQuest.category, isCompleted, wasCompleted);
      }

      if (newStreak >= 7) {
        await this.updateSideQuestBadges(userId, newStreak);
      }

      const actualToday = new Date();
      actualToday.setHours(0, 0, 0, 0);

      if (selectedDate && selectedDate < actualToday) {

        const { data: todaySideQuest, error: todaySideQuestError } = await this.supabaseService.getClient()
          .from('user_daily_sidequests')
          .select('*')
          .eq('user_id', userId)
          .single();

        if (todaySideQuestError || !todaySideQuest) {
        } else {
          if (todaySideQuest.id !== sideQuestId) {

            await this.checkMissedDays(todaySideQuest.id);
          }
        }
      }

      return {
        completed: isCompleted,
        value_achieved: newValueAchieved,
        streak: newStreak,
        goal_value: questPool.goal_value,
        goal_unit: questPool.goal_unit
      };
    } catch (error) {
      throw error;
    }
  }

  async checkMissedDays(sideQuestId: string | undefined): Promise<void> {

    if (!sideQuestId) {
      return;
    }

    try {
      const { data: sideQuest, error: questError } = await this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*')
        .eq('id', sideQuestId)
        .single();

      if (questError || !sideQuest) {
        return;
      }

      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      const lastCompletedDate = sideQuest.last_completed_date ? new Date(sideQuest.last_completed_date) : null;

      if (lastCompletedDate) {
        lastCompletedDate.setHours(0, 0, 0, 0);
      }
      yesterday.setHours(0, 0, 0, 0);

      const missedDay = !lastCompletedDate ||
                       (lastCompletedDate < yesterday &&
                        lastCompletedDate.toISOString().split('T')[0] !== yesterday.toISOString().split('T')[0]);


      if (missedDay) {


        const { error: updateError } = await this.supabaseService.getClient()
          .from('user_daily_sidequests')
          .update({
            streak: 0,
            last_completed_date: yesterday.toISOString().split('T')[0]  
          })
          .eq('id', sideQuestId);

        if (updateError) {
          return;
        }

      } else {
      }
    } catch (error) {
    }
  }

  async assignNewQuest(sideQuestId: string | undefined): Promise<void> {

    if (!sideQuestId) {
      return;
    }

    try {
      const { data: sideQuest, error: questError } = await this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*')
        .eq('id', sideQuestId)
        .single();

      if (questError || !sideQuest) {
        return;
      }

      const today = new Date();

      if (new Date(sideQuest.date_assigned).getDate() === today.getDate()) {
        return;
      }

      await this.checkMissedDays(sideQuestId);

      const { data: poolQuests, error: poolError } = await this.supabaseService.getClient()
        .from('daily_sidequest_pool')
        .select('*')
        .eq('active', true)
        .neq('id', sideQuest.current_quest_id);

      if (poolError) {
        return;
      }

      if (!poolQuests || poolQuests.length === 0) {
        return;
      }


      const randomIndex = Math.floor(Math.random() * poolQuests.length);
      const randomQuest = poolQuests[randomIndex];

      const { error: updateError } = await this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .update({
          current_quest_id: randomQuest.id,
          date_assigned: today,
          completed: false,
          value_achieved: 0,
          category: randomQuest.category,
          emoji: randomQuest.emoji
        })
        .eq('id', sideQuestId);

      if (updateError) {
        return;
      }

    } catch (error) {
    }
  }

  private async updateUserXP(
    userId: string,
    category: string,
    isCompleted: boolean,
    wasCompleted: boolean
  ): Promise<void> {

    try {
      if (isCompleted === wasCompleted) {
        return;
      }


      const xpField = `${category}_xp`;

      const xpValue = 2;

      try {
        let updateData = {};

        if (isCompleted && !wasCompleted) {

          const { data: userData, error: userError } = await this.supabaseService.getClient()
            .from('profiles')
            .select(`${xpField}`)
            .eq('id', userId)
            .single();

          if (userError) {
            return;
          }
          const currentXP = userData[xpField] || 0;
          const newXP = currentXP + xpValue;

          updateData = { [xpField]: newXP };

        } else if (!isCompleted && wasCompleted) {

          const { data: userData, error: userError } = await this.supabaseService.getClient()
            .from('profiles')
            .select(`${xpField}`)
            .eq('id', userId)
            .single();

          if (userError) {
            return;
          }

          const currentXP = userData[xpField] || 0;
          const newXP = Math.max(0, currentXP - xpValue);

          updateData = { [xpField]: newXP };
        }

        if (Object.keys(updateData).length > 0) {
          const { error: updateError } = await this.supabaseService.getClient()
            .from('profiles')
            .update(updateData)
            .eq('id', userId);

          if (updateError) {
            return;
          }


          await this.userService.checkAndLevelUp(userId);

        }
      } catch (error) {
      }
    } catch (error) {
    }
  }

  private async updateSideQuestBadges(userId: string, streak: number): Promise<void> {

    try {
      const { data: badges, error: badgesError } = await this.supabaseService.getClient()
        .from('user_badges')
        .select('*')
        .eq('user_id', userId);

      if (badgesError) {
        return;
      }

      if (!badges || badges.length === 0) {
        return;
      }

      const badgeDoc = badges[0];

      const updates: any = {};

      if (streak >= 7) {
        updates.badge_sidequest_streak_7_days = true;
      }
      if (streak >= 30) {
        updates.badge_sidequest_streak_30_days = true;
      }
      if (streak >= 100) {
        updates.badge_sidequest_streak_100_days = true;
      }
      if (streak >= 365) {
        updates.badge_sidequest_streak_365_days = true;
      }

      if (Object.keys(updates).length > 0) {

        const { error: updateError } = await this.supabaseService.getClient()
          .from('user_badges')
          .update(updates)
          .eq('id', badgeDoc.id);

        if (updateError) {
          return;
        }

      } else {
      }
    } catch (error) {
    }
  }

  private isSameDay(date1: Date, date2: Date): boolean {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  }

  getOrCreateDailySideQuest(userId: string): Observable<UserDailySideQuest | null> {
    const today = new Date();

    return from(
      this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*')
        .eq('user_id', userId)
    ).pipe(
      take(1),
      map(response => {
        if (response.error) {
          return null;
        }

        return response.data.length > 0 ? response.data[0] as UserDailySideQuest : null;
      }),
      switchMap(existingSideQuest => {
        if (existingSideQuest) {

          const assignedDate = new Date(existingSideQuest.date_assigned);
          if (assignedDate.getDate() !== today.getDate() ||
              assignedDate.getMonth() !== today.getMonth() ||
              assignedDate.getFullYear() !== today.getFullYear()) {

            return from(this.assignNewQuest(existingSideQuest.id)).pipe(
              switchMap(() => from(
                this.supabaseService.getClient()
                  .from('user_daily_sidequests')
                  .select('*')
                  .eq('user_id', userId)
              ).pipe(
                take(1),
                map(response => {
                  if (response.error) {
                    return null;
                  }

                  return response.data.length > 0 ? response.data[0] as UserDailySideQuest : null;
                })
              ))
            );
          }


          return from(this.checkMissedDays(existingSideQuest.id)).pipe(
            switchMap(() => of(existingSideQuest))
          );
        } else {

          return from(this.createUserDailySideQuest(userId)).pipe(
            switchMap(() => from(
              this.supabaseService.getClient()
                .from('user_daily_sidequests')
                .select('*')
                .eq('user_id', userId)
            ).pipe(
              take(1),
              map(response => {
                if (response.error) {
                  return null;
                }

                return response.data.length > 0 ? response.data[0] as UserDailySideQuest : null;
              })
            ))
          );
        }
      }),
      catchError(error => {
        return of(null);
      })
    );
  }

  recalculateSideQuestStreak(userId: string, _selectedDate?: Date): Observable<void> {

    return from(
      this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*')
        .eq('user_id', userId)
        .order('date_assigned', { ascending: false })
        .limit(1)
    ).pipe(
      map(response => {
        if (response.error) {
          return null;
        }
        return response.data.length > 0 ? response.data[0] : null;
      }),
      switchMap(sideQuest => {
        if (!sideQuest) {
          return of(undefined);
        }

        return from(this.checkMissedDays(sideQuest.id)).pipe(
          map(() => undefined)
        );
      }),
      catchError(error => {
        return of(undefined);
      })
    );
  }

  ensureUserHasDailySideQuests(userId: string): Observable<UserDailySideQuest[]> {

    const today = new Date();

    return from(
      this.supabaseService.getClient()
        .from('user_daily_sidequests')
        .select('*, daily_sidequest_pool(*)')
        .eq('user_id', userId)
        .order('date_assigned', { ascending: false })
        .limit(1)
    ).pipe(
      switchMap(response => {
        if (response.error) {
          return of([]);
        }

        if (response.data && response.data.length > 0) {

          const sideQuest = response.data[0];

          return from(this.checkMissedDays(sideQuest.id)).pipe(
            switchMap(() => {

              const assignedDate = new Date(sideQuest.date_assigned);
              if (assignedDate.getDate() !== today.getDate() ||
                  assignedDate.getMonth() !== today.getMonth() ||
                  assignedDate.getFullYear() !== today.getFullYear()) {

                return from(this.assignNewQuest(sideQuest.id)).pipe(
                  switchMap(() => from(
                    this.supabaseService.getClient()
                      .from('user_daily_sidequests')
                      .select('*, daily_sidequest_pool(*)')
                      .eq('user_id', userId)
                      .order('date_assigned', { ascending: false })
                      .limit(1)
                  ).pipe(
                    map(updatedResponse => {
                      if (updatedResponse.error) {
                        return [];
                      }
                      return updatedResponse.data as UserDailySideQuest[];
                    })
                  ))
                );
              }

              return of([sideQuest] as UserDailySideQuest[]);
            })
          );
        }


        return from(this.createUserDailySideQuest(userId)).pipe(
          switchMap(newSideQuestId => {
            return from(
              this.supabaseService.getClient()
                .from('user_daily_sidequests')
                .select('*, daily_sidequest_pool(*)')
                .eq('user_id', userId)
                .order('date_assigned', { ascending: false })
                .limit(1)
            ).pipe(
              map(newResponse => {
                if (newResponse.error) {
                  return [];
                }
                return newResponse.data as UserDailySideQuest[];
              })
            );
          }),
          catchError(error => {
            return of([]);
          })
        );
      })
    );
  }

  importSideQuestsFromJson(): Observable<boolean> {

    return this.http.get<DailySideQuestPool[]>('assets/data/sidequest-pool.json').pipe(
      switchMap(sideQuests => {
        if (!sideQuests || sideQuests.length === 0) {
          return of(false);
        }


        return from(
          this.supabaseService.getClient()
            .from('daily_sidequest_pool')
            .select('id', { count: 'exact' })
        ).pipe(
          switchMap(response => {
            if (response.error) {
              return of(false);
            }

            if (response.data && response.data.length > 0) {
              return of(false);
            }

            const questsWithActive = sideQuests.map(quest => ({
              ...quest,
              active: true
            }));

            return from(
              this.supabaseService.getClient()
                .from('daily_sidequest_pool')
                .insert(questsWithActive)
            ).pipe(
              map(insertResponse => {
                if (insertResponse.error) {
                  return false;
                }

                return true;
              }),
              catchError(error => {
                return of(false);
              })
            );
          })
        );
      }),
      catchError(error => {
        return of(false);
      })
    );
  }
}
