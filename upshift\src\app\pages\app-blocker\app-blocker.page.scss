﻿ App Blocker Page Styles */
.main-navigation {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #121212;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    z-index: 1000;
    padding: 8px 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
}

.nav-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    max-width: 600px;
    margin: 0 auto;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #888;
    padding: 5px 0;
    transition: color 0.2s ease;
    width: 20%;
}

.nav-item:hover {
    color: #fff;
}

.nav-item.active {
    color: #4D7BFF;
    position: relative;
}

.nav-item.active::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background-color: #4D7BFF;
    border-radius: 50%;
}

.nav-icon {
    font-size: 18px;
    margin-bottom: 4px;
}

.nav-text {
    font-size: 12px;
    font-weight: 500;
}

 Adjust container padding to account for navigation bar */
