<!-- Waiting Room for users who joined today -->
<app-group-waiting-room
  *ngIf="joinedToday"
  [groupId]="groupId || ''"
  [groupName]="group?.name || 'Group'"
  [isAdmin]="isAdmin">
</app-group-waiting-room>

<!-- Regular Group Detail View for users who joined before today -->
<div class="container" *ngIf="!joinedToday">
  <header>
    <div class="logo-wrap">
      <div class="logo">
        <img src="assets/images/upshift_icon_mini.svg" alt="Upshift">
        <span>Upshift</span>
      </div>
    </div>
    <h1>{{ group?.emoji }} {{ group?.name }}</h1>
  </header>
  <a [routerLink]="['/groups']" class="back-link">&larr; Back to groups</a>

  <div class="week-calendar">
    <div class="calendar-nav">
      <button class="nav-arrow prev" (click)="navigateWeekWithUrl(-1)">←</button>
      <div class="days">
        <div class="day-name" *ngFor="let day of dayNames">{{ day }}</div>
      </div>
      <button class="nav-arrow next" (click)="navigateWeekWithUrl(1)">→</button>
    </div>
    <div class="dates">
      <div *ngFor="let date of weekDates"
           class="date"
           [class.active]="date.isToday"
           [class.selected]="date.isSelected"
           [class.disabled]="date.isFuture || date.isBeforeJoin"
           [attr.isBeforeJoin]="date.isBeforeJoin"
           [title]="date.isBeforeJoin ? 'You cannot access dates before you joined the group' : (date.isFuture ? 'Cannot select future dates' : '')"
           (click)="selectDateWithUrl(date)">
        <svg class="date-progress" viewBox="0 0 36 36" *ngIf="date.completionPercentage && date.completionPercentage > 0 && !date.isFuture && !date.isBeforeJoin">
          <circle cx="18" cy="18" r="15.5"
                  [attr.stroke-dasharray]="(date.completionPercentage || 0) + ', 100'"
                  [attr.data-date]="date.date"
                  class="progress-circle"
                  [class.low]="date.completionPercentage && date.completionPercentage < 50"></circle>
        </svg>
        <span class="date-content">{{ date.day }}</span>
      </div>
    </div>
    <div class="future-date-label" *ngIf="hasFutureDates()">Future date</div>
  </div>

  <section class="quests">
    <div style="display: flex; justify-content: space-between;">
      <h2>Quests</h2>
      <h2>
        <ng-container *ngIf="isToday()">Today</ng-container>
        <ng-container *ngIf="isYesterday()">Yesterday</ng-container>
        <ng-container *ngIf="isTomorrow()">Tomorrow</ng-container>
        <ng-container *ngIf="!isToday() && !isYesterday() && !isTomorrow()">{{ headerText }}</ng-container>
      </h2>
    </div>
    <div class="quest-list">
      <ng-container *ngFor="let quest of quests">
        <div *ngIf="!quest.is_side_quest"
             class="quest-item"
             [class.completed]="quest.completed"
             [attr.data-quest-id]="quest.id"
             [attr.data-group-quest]="true"
             [attr.data-is-admin]="isAdmin">
        <div class="quest-icon">
          {{ quest.emoji }}
        </div>
        <div class="quest-info">
          <h3>{{ quest.name }}</h3>
          <p>{{ quest.description }}</p>
          <div class="progress-container">
            <div class="progress-time" *ngIf="quest.goal_unit === 'time' || quest.goal_unit === 'min' || quest.goal_unit === 'hr' || quest.goal_unit === 'sec'">
              <ion-range
                min="0"
                [max]="quest.goal_value"
                [(ngModel)]="quest.value_achieved"
                class="progress-slider"
                [attr.data-quest-id]="quest.id"
                [attr.data-quest-type]="quest.quest_type"
                [step]="1"
                snaps="true"
                ticks="false"
                snaps-per-step="true"
                (ionChange)="updateQuestProgress(quest, $event)"
                style="--progress-value: {{quest.value_achieved / quest.goal_value * 100}}%">
              </ion-range>
              <div class="progress-text">
                {{ quest.value_achieved }}{{ quest.goal_unit === 'min' ? 'm' : quest.goal_unit === 'hr' ? 'h' : 's' }}/{{ quest.goal_value }}{{ quest.goal_unit === 'min' ? 'm' : quest.goal_unit === 'hr' ? 'h' : 's' }}
              </div>
            </div>
            <div class="progress" *ngIf="quest.goal_unit !== 'time' && quest.goal_unit !== 'min' && quest.goal_unit !== 'hr' && quest.goal_unit !== 'sec'">
              <ion-range
                min="0"
                [max]="quest.goal_value"
                [(ngModel)]="quest.value_achieved"
                class="progress-slider"
                [attr.data-quest-id]="quest.id"
                [attr.data-quest-type]="quest.quest_type"
                [step]="1"
                snaps="true"
                ticks="false"
                snaps-per-step="true"
                (ionChange)="updateQuestProgress(quest, $event)"
                style="--progress-value: {{quest.value_achieved / quest.goal_value * 100}}%">
              </ion-range>
              <div class="progress-text values">
                <span>
                  {{ quest.value_achieved }}/{{ quest.goal_value }}
                </span>
                <span class="members-count">
                  Members: {{ quest.completed_members }}/{{ quest.total_members }}
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="quest-streak" *ngIf="isToday()">
          🔥{{ quest.streak }}d
        </div>
        <div class="quest-streak" *ngIf="!isToday()"></div>
      </div>
      </ng-container>
    </div>
  </section>

  <!-- New Group Sidequest Component -->
  <app-group-sidequest
    *ngIf="group?.enable_sidequests && groupId && userId"
    [groupId]="groupId || ''"
    [userId]="userId || ''"
    [joinedDate]="joinedDate"
    [isAdmin]="isAdmin"
    [enableSidequests]="group?.enable_sidequests || false"
    [selectedDate]="selectedDate">
  </app-group-sidequest>
</div>

<div class="group-footer" *ngIf="!joinedToday || isAdmin">
  <a [routerLink]="['/groups', groupId, 'settings']" class="settings-btn" style="text-decoration: none; ">⚙️ Settings</a>
  <a href="#" id="add-quest-btn" class="add-quest-link" *ngIf="isAdmin" (click)="openAddQuestModal($event)">
    <span class="add-quest-icon">+</span> Add Quest
  </a>
</div>

<!-- Add Quest Modal -->
<div id="add-quest-modal" class="modal" [style.display]="showAddQuestModal ? 'block' : 'none'">
  <div class="modal-content">
    <span class="close-modal" (click)="closeAddQuestModal()">&times;</span>
    <h2>Add New Group Quest</h2>
    <form (ngSubmit)="createGroupQuest()" id="add-quest-form">
      <div style="display: flex;gap: 10px;">
        <div class="form-group">
          <input type="text" id="emoji" name="emoji" [(ngModel)]="newQuest.emoji" value="🎯" appEmojiInput>
        </div>
        <div class="form-group">
          <input type="text" id="name" name="name" [(ngModel)]="newQuest.name" placeholder="Enter quest name" required>
        </div>
      </div>
      <div class="form-group">
        <label for="description">Description</label>
        <textarea id="description" name="description" [(ngModel)]="newQuest.description" placeholder="Enter quest description"></textarea>
      </div>
      <div class="form-group">
        <label for="quest_type">Quest Type</label>
        <select id="quest_type" name="quest_type" [(ngModel)]="newQuest.quest_type">
          <option value="build">Build Habit</option>
          <option value="quit">Quit Habit</option>
        </select>
      </div>
      <div class="form-group">
        <label for="category">Category</label>
        <select id="category" name="category" [(ngModel)]="newQuest.category" required>
          <option value="">Select a category</option>
          <option value="strength">Strength</option>
          <option value="money">Money</option>
          <option value="health">Health</option>
          <option value="knowledge">Knowledge</option>
        </select>
      </div>
      <div class="form-group">
        <label for="priority">Priority</label>
        <select id="priority" name="priority" [(ngModel)]="newQuest.priority" required>
          <option value="basic">Basic</option>
          <option value="high">High Priority</option>
        </select>
      </div>
      <div class="form-group goal-settings">
        <label>Goal</label>
        <div class="goal-inputs">
          <input type="number" id="goal_value" name="goal_value" [(ngModel)]="newQuest.goal_value" value="1" min="1">
          <select id="goal_unit" name="goal_unit" [(ngModel)]="newQuest.goal_unit">
            <option value="count">count</option>
            <option value="steps">steps</option>
            <option value="m">meters</option>
            <option value="km">kilometers</option>
            <option value="sec">seconds</option>
            <option value="min">minutes</option>
            <option value="hr">hours</option>
            <option value="Cal">calories</option>
            <option value="g">grams</option>
            <option value="mg">milligrams</option>
            <option value="drink">drinks</option>
          </select>
        </div>
      </div>
      <div class="form-group">
        <label for="goal_period">Frequency</label>
        <select id="goal_period" name="goal_period" [(ngModel)]="newQuest.goal_period" (change)="onGoalPeriodChange()">
          <option value="day">Every Day</option>
          <option value="week">Specific days of the week</option>
          <option value="month">Specific days of the month</option>
        </select>
      </div>
      <div id="days-of-week-container" class="form-group schedule-container" [style.display]="newQuest.goal_period === 'week' ? 'block' : 'none'">
        <label>Select Days of Week</label>
        <div class="days-selector">
          <div class="day-checkbox" *ngFor="let day of daysOfWeek">
            <input type="checkbox" [id]="'day-' + day.value.toLowerCase()" name="days_of_week" [value]="day.value"
                   [(ngModel)]="day.selected">
            <label [for]="'day-' + day.value.toLowerCase()">{{ day.label }}</label>
          </div>
        </div>
      </div>
      <div id="days-of-month-container" class="form-group schedule-container" [style.display]="newQuest.goal_period === 'month' ? 'block' : 'none'">
        <label>Select Days of Month</label>
        <div class="month-days-selector">
          <div class="day-checkbox" *ngFor="let day of daysOfMonth">
            <input type="checkbox" [id]="'month-day-' + day.value" name="days_of_month" [value]="day.value"
                   [(ngModel)]="day.selected">
            <label [for]="'month-day-' + day.value">{{ day.value }}</label>
          </div>
        </div>
      </div>
      <button type="submit" class="submit-btn">Create Quest</button>
    </form>
  </div>
</div>
