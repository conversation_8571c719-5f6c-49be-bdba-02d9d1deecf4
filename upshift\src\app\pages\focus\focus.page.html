﻿
<div class="container">
    <header>
        <div class="logo">
            <img src="assets/images/upshift_icon_mini.svg" alt="Upshift">
            <span>Upshift</span>
        </div>
        <h1>{{ headerText }}</h1>
    </header>

    <!-- Calendar -->
    <div class="week-calendar">
        <div class="calendar-nav">
            <button class="nav-arrow prev" (click)="changeWeek(-1)">←</button>
            <div class="days">
                <div class="day-name" *ngFor="let dayName of dayNames">{{ dayName }}</div>
            </div>
            <button class="nav-arrow next" (click)="changeWeek(1)">→</button>
        </div>
        <div class="dates">
            <div *ngFor="let date of weekDates"
                 class="date"
                 [class.active]="date.is_today"
                 [class.selected]="date.is_selected"
                 [class.disabled]="date.is_future"
                 (click)="!date.is_future && selectDate(date.date)">
                <svg class="date-progress" viewBox="0 0 36 36" *ngIf="date.total_quests > 0 && !date.is_future">
                    <circle cx="18" cy="18" r="15.5"
                            [attr.stroke-dasharray]="date.completion_percentage + ', 100'"
                            [attr.data-date]="date.date"
                            class="progress-circle"
                            [class.low]="date.completion_percentage < 50"></circle>
                </svg>
                <span class="date-content">{{ date.day }}</span>
            </div>
        </div>
    </div>

        <!-- Focus Summary Block -->
        <section class="screen-summary">
            <p class="summary-text">
                You've spent 6 hours today – enough to learn a new skill!
            </p>

            <div class="screen-time-box">
                <div>
                    <span class="big-time">41m</span>
                    <span class="time-label">SCREEN TIME</span>
                </div>
                <div>
                    <span class="big-time">16</span>
                    <span class="time-label">SESSIONS</span>
                </div>
            </div>

            <div class="app-times">
                <div class="app-time">Classroom <span>11m</span></div>
                <div class="app-time">Disk <span>10m</span></div>
                <div class="app-time">Instagram <span>9m</span></div>
                <div class="app-time">Trading212 <span>7m</span></div>
            </div>

            <div class="blockers">
                <div class="blocker">
                    <span>
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 15v2m0-6.5a2.5 2.5 0 00-2.5 2.5h5a2.5 2.5 0 00-2.5-2.5zm0 0v-3m0 10v.01" />
                        </svg>
                        App Blocker
                    </span>
                    <span>Until 9:00 PM <a href="#" class="cancel-link">Cancel</a></span>
                </div>

                <div class="blocker">
                    <span>
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4S8 5.79 8 8s1.79 4 4 4zm0 0c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
                        </svg>
                        Website Blocker
                    </span>
                </div>
            </div>
        </section>
        <section class="scheduled-blocks">
            <h2>Scheduled to block later</h2>

            <div class="block-card">
                <div class="block-card-text">
                    <div class="block-card-title">Time Limit Session</div>
                    <div class="block-card-sub">1 app</div>
                    <div class="block-card-detail">Monitoring • All day<br>30 minute limit</div>
                </div>
                <div class="block-card-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 6v6l4 2m-4-14a10 10 0 100 20 10 10 0 000-20z" />
                    </svg>
                    <span class="usage-text">Usage</span>
                </div>
            </div>

            <div class="block-buttons">
                <button class="block-btn">
                    <svg viewBox="0 0 24 24"><path d="M19 3h-1V1h-2v2H8V1H6v2H5a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2V5a2 2 0 00-2-2zm0 16H5V8h14v11zM7 10h5v5H7v-5z" fill="currentColor"/></svg>
                    Schedule
                </button>
                <button class="block-btn">
                    <svg viewBox="0 0 24 24"><path d="M12 7v5l4 2M12 1a11 11 0 100 22 11 11 0 000-22z" fill="currentColor"/></svg>
                    Time Limit
                </button>
                <button class="block-btn">
                    <svg viewBox="0 0 24 24"><path d="M13 2.05v2.01a8.001 8.001 0 11-6.36 13.41l1.45-1.45a6.001 6.001 0 107.91-9.27V11h-2V2.05z" fill="currentColor"/></svg>
                    Block Now
                </button>
            </div>
        </section>


    </div>
