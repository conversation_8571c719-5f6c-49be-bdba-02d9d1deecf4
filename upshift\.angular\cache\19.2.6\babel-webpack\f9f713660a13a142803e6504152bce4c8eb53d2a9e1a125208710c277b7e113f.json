{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _SupabaseService;\nimport { createClient } from '@supabase/supabase-js';\nimport { BehaviorSubject, from } from 'rxjs';\nimport { map, catchError, tap } from 'rxjs/operators';\nimport { Preferences } from '@capacitor/preferences';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport const supabase = createClient(environment.supabase.url, environment.supabase.key, {\n  auth: {\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true\n  },\n  global: {\n    headers: {\n      'Accept': 'application/json',\n      'Content-Type': 'application/json'\n    }\n  }\n});\nexport class SupabaseService {\n  constructor(router) {\n    var _this = this;\n    this.router = router;\n    this.session = null;\n    this.supabase = supabase;\n    this.storage = {\n      url: environment.supabase.url\n    };\n    this.supabaseKey = environment.supabase.key;\n    this._currentUser = new BehaviorSubject(null);\n    this.currentUser$ = this._currentUser.asObservable();\n    this.loadUser();\n    this.supabase = createClient(this.storage.url, this.supabaseKey);\n    this.supabase.auth.onAuthStateChange(/*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (event, session) {\n        if (session !== null && session !== void 0 && session.user) {\n          _this._currentUser.next(session.user);\n          yield Preferences.set({\n            key: 'uid',\n            value: session.user.id\n          });\n          if (event === 'SIGNED_IN') {\n            _this.ensureProfileExists(session.user);\n            const redirectUrl = localStorage.getItem('redirectUrl');\n            if (redirectUrl) {\n              localStorage.removeItem('redirectUrl');\n              setTimeout(() => {\n                _this.router.navigateByUrl(redirectUrl);\n              }, 500);\n            } else {\n              _this.router.navigateByUrl('/tabs/home');\n            }\n          }\n        } else {\n          _this._currentUser.next(null);\n          yield Preferences.remove({\n            key: 'uid'\n          });\n          if (event === 'SIGNED_OUT') {\n            _this.router.navigateByUrl('/signup');\n          }\n        }\n      });\n      return function (_x, _x2) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n  }\n  loadSession() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const {\n        data\n      } = yield _this2.supabase.auth.getSession();\n      _this2.session = data.session;\n      return _this2.session;\n    })();\n  }\n  loadUser() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          data,\n          error\n        } = yield _this3.supabase.auth.getUser();\n        if (error) {\n          _this3._currentUser.next(null);\n          return;\n        }\n        if (data.user) {\n          _this3._currentUser.next(data.user);\n          yield Preferences.set({\n            key: 'uid',\n            value: data.user.id\n          });\n          _this3.ensureProfileExists(data.user);\n        } else {\n          _this3._currentUser.next(null);\n        }\n      } catch (error) {\n        _this3._currentUser.next(null);\n      }\n    })();\n  }\n  signUp(email, password) {\n    return from(this.supabase.auth.signUp({\n      email,\n      password\n    })).pipe(tap(res => {\n      var _res$data;\n      if ((_res$data = res.data) !== null && _res$data !== void 0 && _res$data.user) {\n        this._currentUser.next(res.data.user);\n      }\n    }), catchError(error => {\n      throw error;\n    }));\n  }\n  signIn(email, password) {\n    return from(this.supabase.auth.signInWithPassword({\n      email,\n      password\n    })).pipe(tap(res => {\n      var _res$data2;\n      if ((_res$data2 = res.data) !== null && _res$data2 !== void 0 && _res$data2.user) {\n        this._currentUser.next(res.data.user);\n      }\n    }), catchError(error => {\n      throw error;\n    }));\n  }\n  signOut() {\n    return from(this.supabase.auth.signOut()).pipe(tap(() => {\n      this._currentUser.next(null);\n      localStorage.removeItem('redirectUrl');\n      this.router.navigate(['/signup/']);\n    }), catchError(error => {\n      throw error;\n    }));\n  }\n  signInWithGoogle() {\n    return from(this.supabase.auth.signInWithOAuth({\n      provider: 'google',\n      options: {\n        redirectTo: window.location.origin + '/'\n      }\n    })).pipe(catchError(error => {\n      throw error;\n    }));\n  }\n  signInWithApple() {\n    return from(this.supabase.auth.signInWithOAuth({\n      provider: 'apple',\n      options: {\n        redirectTo: window.location.origin + '/'\n      }\n    })).pipe(catchError(error => {\n      throw error;\n    }));\n  }\n  getCurrentUserId() {\n    var _this$_currentUser$va;\n    return ((_this$_currentUser$va = this._currentUser.value) === null || _this$_currentUser$va === void 0 ? void 0 : _this$_currentUser$va.id) || null;\n  }\n  getCurrentUser() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      const userId = _this4.getCurrentUserId();\n      if (!userId) return null;\n      try {\n        const {\n          data,\n          error\n        } = yield _this4.supabase.from('profiles').select('*').filter('id', 'eq', userId).single();\n        if (error) {\n          return null;\n        }\n        return data;\n      } catch (error) {\n        return null;\n      }\n    })();\n  }\n  isLoggedIn() {\n    return this.currentUser$.pipe(map(user => !!user));\n  }\n  getClient() {\n    return this.supabase;\n  }\n  ensureProfileExists(user) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      if (!user || !user.id) return;\n      try {\n        var _user$user_metadata, _user$user_metadata2, _user$user_metadata3;\n        const {\n          count,\n          error: countError\n        } = yield _this5.supabase.from('profiles').select('*', {\n          count: 'exact',\n          head: true\n        }).eq('id', user.id);\n        if (countError) return;\n        if (count && count > 0) return;\n        const newProfile = {\n          id: user.id,\n          email: user.email,\n          username: null,\n          name: ((_user$user_metadata = user.user_metadata) === null || _user$user_metadata === void 0 ? void 0 : _user$user_metadata['full_name']) || ((_user$user_metadata2 = user.user_metadata) === null || _user$user_metadata2 === void 0 ? void 0 : _user$user_metadata2['name']) || 'New User',\n          profile_picture: ((_user$user_metadata3 = user.user_metadata) === null || _user$user_metadata3 === void 0 ? void 0 : _user$user_metadata3['avatar_url']) || null,\n          registration_date: new Date(),\n          last_login: new Date(),\n          active: true,\n          level: 0,\n          title: '🥚 Beginner',\n          strength_xp: 0,\n          money_xp: 0,\n          health_xp: 0,\n          knowledge_xp: 0,\n          plan: 'none',\n          auto_renew: true,\n          subscription_status: 'email marketing'\n        };\n        const {\n          error: insertError\n        } = yield _this5.supabase.from('profiles').insert(newProfile);\n        if (insertError) {\n          var _user$user_metadata4, _user$user_metadata5;\n          const minimalProfile = {\n            id: user.id,\n            email: user.email,\n            name: ((_user$user_metadata4 = user.user_metadata) === null || _user$user_metadata4 === void 0 ? void 0 : _user$user_metadata4['full_name']) || ((_user$user_metadata5 = user.user_metadata) === null || _user$user_metadata5 === void 0 ? void 0 : _user$user_metadata5['name']) || 'New User',\n            registration_date: new Date(),\n            last_login: new Date(),\n            active: true,\n            level: 0\n          };\n          yield _this5.supabase.from('profiles').insert(minimalProfile);\n        }\n      } catch (error) {}\n    })();\n  }\n}\n_SupabaseService = SupabaseService;\n_SupabaseService.ɵfac = function SupabaseService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _SupabaseService)(i0.ɵɵinject(i1.Router));\n};\n_SupabaseService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _SupabaseService,\n  factory: _SupabaseService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "names": ["createClient", "BehaviorSubject", "from", "map", "catchError", "tap", "Preferences", "environment", "supabase", "url", "key", "auth", "autoRefreshToken", "persistSession", "detectSessionInUrl", "global", "headers", "SupabaseService", "constructor", "router", "_this", "session", "storage", "supabase<PERSON>ey", "_currentUser", "currentUser$", "asObservable", "loadUser", "onAuthStateChange", "_ref", "_asyncToGenerator", "event", "user", "next", "set", "value", "id", "ensureProfileExists", "redirectUrl", "localStorage", "getItem", "removeItem", "setTimeout", "navigateByUrl", "remove", "_x", "_x2", "apply", "arguments", "loadSession", "_this2", "data", "getSession", "_this3", "error", "getUser", "signUp", "email", "password", "pipe", "res", "_res$data", "signIn", "signInWithPassword", "_res$data2", "signOut", "navigate", "signInWithGoogle", "signInWithOAuth", "provider", "options", "redirectTo", "window", "location", "origin", "signInWithApple", "getCurrentUserId", "_this$_currentUser$va", "getCurrentUser", "_this4", "userId", "select", "filter", "single", "isLoggedIn", "getClient", "_this5", "_user$user_metadata", "_user$user_metadata2", "_user$user_metadata3", "count", "countError", "head", "eq", "newProfile", "username", "name", "user_metadata", "profile_picture", "registration_date", "Date", "last_login", "active", "level", "title", "strength_xp", "money_xp", "health_xp", "knowledge_xp", "plan", "auto_renew", "subscription_status", "insertError", "insert", "_user$user_metadata4", "_user$user_metadata5", "minimalProfile", "i0", "ɵɵinject", "i1", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\services\\supabase.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { createClient, SupabaseClient, User, AuthResponse, AuthSession  } from '@supabase/supabase-js';\r\nimport { BehaviorSubject, Observable, from } from 'rxjs';\r\nimport { map, catchError, tap } from 'rxjs/operators';\r\nimport { Router } from '@angular/router';\r\nimport { Preferences } from '@capacitor/preferences';\r\nimport { environment } from '../../environments/environment';\r\n\r\nexport const supabase = createClient(\r\n  environment.supabase.url,\r\n  environment.supabase.key,\r\n  {\r\n    auth: {\r\n      autoRefreshToken: true,\r\n      persistSession: true,\r\n      detectSessionInUrl: true\r\n    },\r\n    global: {\r\n      headers: {\r\n        'Accept': 'application/json',\r\n        'Content-Type': 'application/json'\r\n      }\r\n    }\r\n  }\r\n);\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class SupabaseService {\r\n  public session: AuthSession | null = null;\r\n  public supabase: SupabaseClient = supabase;\r\n  public storage: any = { url: environment.supabase.url };\r\n  public supabaseKey: string = environment.supabase.key;\r\n  _currentUser = new BehaviorSubject<User | null>(null);\r\n  currentUser$ = this._currentUser.asObservable();\r\n\r\n  constructor(private router: Router) {\r\n    this.loadUser();\r\n\r\n    this.supabase = createClient(this.storage.url, this.supabaseKey);\r\n\r\n    this.supabase.auth.onAuthStateChange(async (event, session) => {\r\n      if (session?.user) {\r\n        this._currentUser.next(session.user);\r\n        await Preferences.set({ key: 'uid', value: session.user.id });\r\n\r\n        if (event === 'SIGNED_IN') {\r\n          this.ensureProfileExists(session.user);\r\n\r\n          const redirectUrl = localStorage.getItem('redirectUrl');\r\n          if (redirectUrl) {\r\n            localStorage.removeItem('redirectUrl');\r\n            setTimeout(() => {\r\n              this.router.navigateByUrl(redirectUrl);\r\n            }, 500);\r\n          } else {\r\n            this.router.navigateByUrl('/tabs/home');\r\n          }\r\n        }\r\n      } else {\r\n        this._currentUser.next(null);\r\n        await Preferences.remove({ key: 'uid' });\r\n\r\n        if (event === 'SIGNED_OUT') {\r\n          this.router.navigateByUrl('/signup');\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  async loadSession() {\r\n    const { data } = await this.supabase.auth.getSession();\r\n    this.session = data.session;\r\n    return this.session;\r\n  }\r\n\r\n  private async loadUser() {\r\n    try {\r\n      const { data, error } = await this.supabase.auth.getUser();\r\n\r\n      if (error) {\r\n        this._currentUser.next(null);\r\n        return;\r\n      }\r\n\r\n      if (data.user) {\r\n        this._currentUser.next(data.user);\r\n        await Preferences.set({ key: 'uid', value: data.user.id });\r\n        this.ensureProfileExists(data.user);\r\n      } else {\r\n        this._currentUser.next(null);\r\n      }\r\n    } catch (error) {\r\n      this._currentUser.next(null);\r\n    }\r\n  }\r\n\r\n  signUp(email: string, password: string): Observable<AuthResponse> {\r\n    return from(this.supabase.auth.signUp({ email, password })).pipe(\r\n      tap(res => {\r\n        if (res.data?.user) {\r\n          this._currentUser.next(res.data.user);\r\n        }\r\n      }),\r\n      catchError(error => {\r\n        throw error;\r\n      })\r\n    );\r\n  }\r\n\r\n  signIn(email: string, password: string): Observable<AuthResponse> {\r\n    return from(this.supabase.auth.signInWithPassword({ email, password })).pipe(\r\n      tap(res => {\r\n        if (res.data?.user) {\r\n          this._currentUser.next(res.data.user);\r\n        }\r\n      }),\r\n      catchError(error => {\r\n        throw error;\r\n      })\r\n    );\r\n  }\r\n\r\n  signOut(): Observable<any> {\r\n    return from(this.supabase.auth.signOut()).pipe(\r\n      tap(() => {\r\n        this._currentUser.next(null);\r\n        localStorage.removeItem('redirectUrl');\r\n        this.router.navigate(['/signup/']);\r\n      }),\r\n      catchError(error => {\r\n        throw error;\r\n      })\r\n    );\r\n  }\r\n\r\n  signInWithGoogle(): Observable<any> {\r\n    return from(this.supabase.auth.signInWithOAuth({\r\n      provider: 'google',\r\n      options: {\r\n        redirectTo: window.location.origin + '/'\r\n      }\r\n    })).pipe(\r\n      catchError(error => {\r\n        throw error;\r\n      })\r\n    );\r\n  }\r\n\r\n  signInWithApple(): Observable<any> {\r\n    return from(this.supabase.auth.signInWithOAuth({\r\n      provider: 'apple',\r\n      options: {\r\n        redirectTo: window.location.origin + '/'\r\n      }\r\n    })).pipe(\r\n      catchError(error => {\r\n        throw error;\r\n      })\r\n    );\r\n  }\r\n\r\n  getCurrentUserId(): string | null {\r\n    return this._currentUser.value?.id || null;\r\n  }\r\n\r\n  async getCurrentUser(): Promise<any | null> {\r\n    const userId = this.getCurrentUserId();\r\n    if (!userId) return null;\r\n\r\n    try {\r\n      const { data, error } = await this.supabase\r\n        .from('profiles')\r\n        .select('*')\r\n        .filter('id', 'eq', userId)\r\n        .single();\r\n\r\n      if (error) {\r\n        return null;\r\n      }\r\n\r\n      return data;\r\n    } catch (error) {\r\n      return null;\r\n    }\r\n  }\r\n\r\n  isLoggedIn(): Observable<boolean> {\r\n    return this.currentUser$.pipe(\r\n      map(user => !!user)\r\n    );\r\n  }\r\n\r\n  getClient(): SupabaseClient {\r\n    return this.supabase;\r\n  }\r\n\r\n  async ensureProfileExists(user: User): Promise<void> {\r\n    if (!user || !user.id) return;\r\n\r\n    try {\r\n      const { count, error: countError } = await this.supabase\r\n        .from('profiles')\r\n        .select('*', { count: 'exact', head: true })\r\n        .eq('id', user.id);\r\n\r\n      if (countError) return;\r\n      if (count && count > 0) return;\r\n\r\n      const newProfile = {\r\n        id: user.id,\r\n        email: user.email,\r\n        username: null,\r\n        name: user.user_metadata?.['full_name'] || user.user_metadata?.['name'] || 'New User',\r\n        profile_picture: user.user_metadata?.['avatar_url'] || null,\r\n        registration_date: new Date(),\r\n        last_login: new Date(),\r\n        active: true,\r\n        level: 0,\r\n        title: '🥚 Beginner',\r\n        strength_xp: 0,\r\n        money_xp: 0,\r\n        health_xp: 0,\r\n        knowledge_xp: 0,\r\n        plan: 'none',\r\n        auto_renew: true,\r\n        subscription_status: 'email marketing'\r\n      };\r\n\r\n      const { error: insertError } = await this.supabase\r\n        .from('profiles')\r\n        .insert(newProfile);\r\n\r\n      if (insertError) {\r\n        const minimalProfile = {\r\n          id: user.id,\r\n          email: user.email,\r\n          name: user.user_metadata?.['full_name'] || user.user_metadata?.['name'] || 'New User',\r\n          registration_date: new Date(),\r\n          last_login: new Date(),\r\n          active: true,\r\n          level: 0\r\n        };\r\n\r\n        await this.supabase.from('profiles').insert(minimalProfile);\r\n      }\r\n    } catch (error) {\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;AACA,SAASA,YAAY,QAA0D,uBAAuB;AACtG,SAASC,eAAe,EAAcC,IAAI,QAAQ,MAAM;AACxD,SAASC,GAAG,EAAEC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAErD,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,WAAW,QAAQ,gCAAgC;;;AAE5D,OAAO,MAAMC,QAAQ,GAAGR,YAAY,CAClCO,WAAW,CAACC,QAAQ,CAACC,GAAG,EACxBF,WAAW,CAACC,QAAQ,CAACE,GAAG,EACxB;EACEC,IAAI,EAAE;IACJC,gBAAgB,EAAE,IAAI;IACtBC,cAAc,EAAE,IAAI;IACpBC,kBAAkB,EAAE;GACrB;EACDC,MAAM,EAAE;IACNC,OAAO,EAAE;MACP,QAAQ,EAAE,kBAAkB;MAC5B,cAAc,EAAE;;;CAGrB,CACF;AAKD,OAAM,MAAOC,eAAe;EAQ1BC,YAAoBC,MAAc;IAAA,IAAAC,KAAA;IAAd,KAAAD,MAAM,GAANA,MAAM;IAPnB,KAAAE,OAAO,GAAuB,IAAI;IAClC,KAAAb,QAAQ,GAAmBA,QAAQ;IACnC,KAAAc,OAAO,GAAQ;MAAEb,GAAG,EAAEF,WAAW,CAACC,QAAQ,CAACC;IAAG,CAAE;IAChD,KAAAc,WAAW,GAAWhB,WAAW,CAACC,QAAQ,CAACE,GAAG;IACrD,KAAAc,YAAY,GAAG,IAAIvB,eAAe,CAAc,IAAI,CAAC;IACrD,KAAAwB,YAAY,GAAG,IAAI,CAACD,YAAY,CAACE,YAAY,EAAE;IAG7C,IAAI,CAACC,QAAQ,EAAE;IAEf,IAAI,CAACnB,QAAQ,GAAGR,YAAY,CAAC,IAAI,CAACsB,OAAO,CAACb,GAAG,EAAE,IAAI,CAACc,WAAW,CAAC;IAEhE,IAAI,CAACf,QAAQ,CAACG,IAAI,CAACiB,iBAAiB;MAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAC,WAAOC,KAAK,EAAEV,OAAO,EAAI;QAC5D,IAAIA,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEW,IAAI,EAAE;UACjBZ,KAAI,CAACI,YAAY,CAACS,IAAI,CAACZ,OAAO,CAACW,IAAI,CAAC;UACpC,MAAM1B,WAAW,CAAC4B,GAAG,CAAC;YAAExB,GAAG,EAAE,KAAK;YAAEyB,KAAK,EAAEd,OAAO,CAACW,IAAI,CAACI;UAAE,CAAE,CAAC;UAE7D,IAAIL,KAAK,KAAK,WAAW,EAAE;YACzBX,KAAI,CAACiB,mBAAmB,CAAChB,OAAO,CAACW,IAAI,CAAC;YAEtC,MAAMM,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;YACvD,IAAIF,WAAW,EAAE;cACfC,YAAY,CAACE,UAAU,CAAC,aAAa,CAAC;cACtCC,UAAU,CAAC,MAAK;gBACdtB,KAAI,CAACD,MAAM,CAACwB,aAAa,CAACL,WAAW,CAAC;cACxC,CAAC,EAAE,GAAG,CAAC;YACT,CAAC,MAAM;cACLlB,KAAI,CAACD,MAAM,CAACwB,aAAa,CAAC,YAAY,CAAC;YACzC;UACF;QACF,CAAC,MAAM;UACLvB,KAAI,CAACI,YAAY,CAACS,IAAI,CAAC,IAAI,CAAC;UAC5B,MAAM3B,WAAW,CAACsC,MAAM,CAAC;YAAElC,GAAG,EAAE;UAAK,CAAE,CAAC;UAExC,IAAIqB,KAAK,KAAK,YAAY,EAAE;YAC1BX,KAAI,CAACD,MAAM,CAACwB,aAAa,CAAC,SAAS,CAAC;UACtC;QACF;MACF,CAAC;MAAA,iBAAAE,EAAA,EAAAC,GAAA;QAAA,OAAAjB,IAAA,CAAAkB,KAAA,OAAAC,SAAA;MAAA;IAAA,IAAC;EACJ;EAEMC,WAAWA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAApB,iBAAA;MACf,MAAM;QAAEqB;MAAI,CAAE,SAASD,MAAI,CAAC1C,QAAQ,CAACG,IAAI,CAACyC,UAAU,EAAE;MACtDF,MAAI,CAAC7B,OAAO,GAAG8B,IAAI,CAAC9B,OAAO;MAC3B,OAAO6B,MAAI,CAAC7B,OAAO;IAAC;EACtB;EAEcM,QAAQA,CAAA;IAAA,IAAA0B,MAAA;IAAA,OAAAvB,iBAAA;MACpB,IAAI;QACF,MAAM;UAAEqB,IAAI;UAAEG;QAAK,CAAE,SAASD,MAAI,CAAC7C,QAAQ,CAACG,IAAI,CAAC4C,OAAO,EAAE;QAE1D,IAAID,KAAK,EAAE;UACTD,MAAI,CAAC7B,YAAY,CAACS,IAAI,CAAC,IAAI,CAAC;UAC5B;QACF;QAEA,IAAIkB,IAAI,CAACnB,IAAI,EAAE;UACbqB,MAAI,CAAC7B,YAAY,CAACS,IAAI,CAACkB,IAAI,CAACnB,IAAI,CAAC;UACjC,MAAM1B,WAAW,CAAC4B,GAAG,CAAC;YAAExB,GAAG,EAAE,KAAK;YAAEyB,KAAK,EAAEgB,IAAI,CAACnB,IAAI,CAACI;UAAE,CAAE,CAAC;UAC1DiB,MAAI,CAAChB,mBAAmB,CAACc,IAAI,CAACnB,IAAI,CAAC;QACrC,CAAC,MAAM;UACLqB,MAAI,CAAC7B,YAAY,CAACS,IAAI,CAAC,IAAI,CAAC;QAC9B;MACF,CAAC,CAAC,OAAOqB,KAAK,EAAE;QACdD,MAAI,CAAC7B,YAAY,CAACS,IAAI,CAAC,IAAI,CAAC;MAC9B;IAAC;EACH;EAEAuB,MAAMA,CAACC,KAAa,EAAEC,QAAgB;IACpC,OAAOxD,IAAI,CAAC,IAAI,CAACM,QAAQ,CAACG,IAAI,CAAC6C,MAAM,CAAC;MAAEC,KAAK;MAAEC;IAAQ,CAAE,CAAC,CAAC,CAACC,IAAI,CAC9DtD,GAAG,CAACuD,GAAG,IAAG;MAAA,IAAAC,SAAA;MACR,KAAAA,SAAA,GAAID,GAAG,CAACT,IAAI,cAAAU,SAAA,eAARA,SAAA,CAAU7B,IAAI,EAAE;QAClB,IAAI,CAACR,YAAY,CAACS,IAAI,CAAC2B,GAAG,CAACT,IAAI,CAACnB,IAAI,CAAC;MACvC;IACF,CAAC,CAAC,EACF5B,UAAU,CAACkD,KAAK,IAAG;MACjB,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEAQ,MAAMA,CAACL,KAAa,EAAEC,QAAgB;IACpC,OAAOxD,IAAI,CAAC,IAAI,CAACM,QAAQ,CAACG,IAAI,CAACoD,kBAAkB,CAAC;MAAEN,KAAK;MAAEC;IAAQ,CAAE,CAAC,CAAC,CAACC,IAAI,CAC1EtD,GAAG,CAACuD,GAAG,IAAG;MAAA,IAAAI,UAAA;MACR,KAAAA,UAAA,GAAIJ,GAAG,CAACT,IAAI,cAAAa,UAAA,eAARA,UAAA,CAAUhC,IAAI,EAAE;QAClB,IAAI,CAACR,YAAY,CAACS,IAAI,CAAC2B,GAAG,CAACT,IAAI,CAACnB,IAAI,CAAC;MACvC;IACF,CAAC,CAAC,EACF5B,UAAU,CAACkD,KAAK,IAAG;MACjB,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEAW,OAAOA,CAAA;IACL,OAAO/D,IAAI,CAAC,IAAI,CAACM,QAAQ,CAACG,IAAI,CAACsD,OAAO,EAAE,CAAC,CAACN,IAAI,CAC5CtD,GAAG,CAAC,MAAK;MACP,IAAI,CAACmB,YAAY,CAACS,IAAI,CAAC,IAAI,CAAC;MAC5BM,YAAY,CAACE,UAAU,CAAC,aAAa,CAAC;MACtC,IAAI,CAACtB,MAAM,CAAC+C,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;IACpC,CAAC,CAAC,EACF9D,UAAU,CAACkD,KAAK,IAAG;MACjB,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEAa,gBAAgBA,CAAA;IACd,OAAOjE,IAAI,CAAC,IAAI,CAACM,QAAQ,CAACG,IAAI,CAACyD,eAAe,CAAC;MAC7CC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE;QACPC,UAAU,EAAEC,MAAM,CAACC,QAAQ,CAACC,MAAM,GAAG;;KAExC,CAAC,CAAC,CAACf,IAAI,CACNvD,UAAU,CAACkD,KAAK,IAAG;MACjB,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEAqB,eAAeA,CAAA;IACb,OAAOzE,IAAI,CAAC,IAAI,CAACM,QAAQ,CAACG,IAAI,CAACyD,eAAe,CAAC;MAC7CC,QAAQ,EAAE,OAAO;MACjBC,OAAO,EAAE;QACPC,UAAU,EAAEC,MAAM,CAACC,QAAQ,CAACC,MAAM,GAAG;;KAExC,CAAC,CAAC,CAACf,IAAI,CACNvD,UAAU,CAACkD,KAAK,IAAG;MACjB,MAAMA,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEAsB,gBAAgBA,CAAA;IAAA,IAAAC,qBAAA;IACd,OAAO,EAAAA,qBAAA,OAAI,CAACrD,YAAY,CAACW,KAAK,cAAA0C,qBAAA,uBAAvBA,qBAAA,CAAyBzC,EAAE,KAAI,IAAI;EAC5C;EAEM0C,cAAcA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAjD,iBAAA;MAClB,MAAMkD,MAAM,GAAGD,MAAI,CAACH,gBAAgB,EAAE;MACtC,IAAI,CAACI,MAAM,EAAE,OAAO,IAAI;MAExB,IAAI;QACF,MAAM;UAAE7B,IAAI;UAAEG;QAAK,CAAE,SAASyB,MAAI,CAACvE,QAAQ,CACxCN,IAAI,CAAC,UAAU,CAAC,CAChB+E,MAAM,CAAC,GAAG,CAAC,CACXC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAEF,MAAM,CAAC,CAC1BG,MAAM,EAAE;QAEX,IAAI7B,KAAK,EAAE;UACT,OAAO,IAAI;QACb;QAEA,OAAOH,IAAI;MACb,CAAC,CAAC,OAAOG,KAAK,EAAE;QACd,OAAO,IAAI;MACb;IAAC;EACH;EAEA8B,UAAUA,CAAA;IACR,OAAO,IAAI,CAAC3D,YAAY,CAACkC,IAAI,CAC3BxD,GAAG,CAAC6B,IAAI,IAAI,CAAC,CAACA,IAAI,CAAC,CACpB;EACH;EAEAqD,SAASA,CAAA;IACP,OAAO,IAAI,CAAC7E,QAAQ;EACtB;EAEM6B,mBAAmBA,CAACL,IAAU;IAAA,IAAAsD,MAAA;IAAA,OAAAxD,iBAAA;MAClC,IAAI,CAACE,IAAI,IAAI,CAACA,IAAI,CAACI,EAAE,EAAE;MAEvB,IAAI;QAAA,IAAAmD,mBAAA,EAAAC,oBAAA,EAAAC,oBAAA;QACF,MAAM;UAAEC,KAAK;UAAEpC,KAAK,EAAEqC;QAAU,CAAE,SAASL,MAAI,CAAC9E,QAAQ,CACrDN,IAAI,CAAC,UAAU,CAAC,CAChB+E,MAAM,CAAC,GAAG,EAAE;UAAES,KAAK,EAAE,OAAO;UAAEE,IAAI,EAAE;QAAI,CAAE,CAAC,CAC3CC,EAAE,CAAC,IAAI,EAAE7D,IAAI,CAACI,EAAE,CAAC;QAEpB,IAAIuD,UAAU,EAAE;QAChB,IAAID,KAAK,IAAIA,KAAK,GAAG,CAAC,EAAE;QAExB,MAAMI,UAAU,GAAG;UACjB1D,EAAE,EAAEJ,IAAI,CAACI,EAAE;UACXqB,KAAK,EAAEzB,IAAI,CAACyB,KAAK;UACjBsC,QAAQ,EAAE,IAAI;UACdC,IAAI,EAAE,EAAAT,mBAAA,GAAAvD,IAAI,CAACiE,aAAa,cAAAV,mBAAA,uBAAlBA,mBAAA,CAAqB,WAAW,CAAC,OAAAC,oBAAA,GAAIxD,IAAI,CAACiE,aAAa,cAAAT,oBAAA,uBAAlBA,oBAAA,CAAqB,MAAM,CAAC,KAAI,UAAU;UACrFU,eAAe,EAAE,EAAAT,oBAAA,GAAAzD,IAAI,CAACiE,aAAa,cAAAR,oBAAA,uBAAlBA,oBAAA,CAAqB,YAAY,CAAC,KAAI,IAAI;UAC3DU,iBAAiB,EAAE,IAAIC,IAAI,EAAE;UAC7BC,UAAU,EAAE,IAAID,IAAI,EAAE;UACtBE,MAAM,EAAE,IAAI;UACZC,KAAK,EAAE,CAAC;UACRC,KAAK,EAAE,aAAa;UACpBC,WAAW,EAAE,CAAC;UACdC,QAAQ,EAAE,CAAC;UACXC,SAAS,EAAE,CAAC;UACZC,YAAY,EAAE,CAAC;UACfC,IAAI,EAAE,MAAM;UACZC,UAAU,EAAE,IAAI;UAChBC,mBAAmB,EAAE;SACtB;QAED,MAAM;UAAEzD,KAAK,EAAE0D;QAAW,CAAE,SAAS1B,MAAI,CAAC9E,QAAQ,CAC/CN,IAAI,CAAC,UAAU,CAAC,CAChB+G,MAAM,CAACnB,UAAU,CAAC;QAErB,IAAIkB,WAAW,EAAE;UAAA,IAAAE,oBAAA,EAAAC,oBAAA;UACf,MAAMC,cAAc,GAAG;YACrBhF,EAAE,EAAEJ,IAAI,CAACI,EAAE;YACXqB,KAAK,EAAEzB,IAAI,CAACyB,KAAK;YACjBuC,IAAI,EAAE,EAAAkB,oBAAA,GAAAlF,IAAI,CAACiE,aAAa,cAAAiB,oBAAA,uBAAlBA,oBAAA,CAAqB,WAAW,CAAC,OAAAC,oBAAA,GAAInF,IAAI,CAACiE,aAAa,cAAAkB,oBAAA,uBAAlBA,oBAAA,CAAqB,MAAM,CAAC,KAAI,UAAU;YACrFhB,iBAAiB,EAAE,IAAIC,IAAI,EAAE;YAC7BC,UAAU,EAAE,IAAID,IAAI,EAAE;YACtBE,MAAM,EAAE,IAAI;YACZC,KAAK,EAAE;WACR;UAED,MAAMjB,MAAI,CAAC9E,QAAQ,CAACN,IAAI,CAAC,UAAU,CAAC,CAAC+G,MAAM,CAACG,cAAc,CAAC;QAC7D;MACF,CAAC,CAAC,OAAO9D,KAAK,EAAE,CAChB;IAAC;EACH;;mBA5NWrC,eAAe;;mCAAfA,gBAAe,EAAAoG,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA;AAAA;;SAAfvG,gBAAe;EAAAwG,OAAA,EAAfxG,gBAAe,CAAAyG,IAAA;EAAAC,UAAA,EAFd;AAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}