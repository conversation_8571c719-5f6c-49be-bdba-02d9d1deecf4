﻿
 Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

 Optional CSS utils that can be commented out */

 @import "@ionic/angular/css/palettes/dark.always.css"; */
@import "@ionic/angular/css/palettes/dark.system.css";

* {
  font-family: var(--ion-font-family);
}

:root {
  --bg: #0a0b0f;               Main background - darker, more neutral */
  --surface-alt: #1e1f25;      Slight variation for subtle contrast */
  --accent-hover: #5277e8;     Lighter accent for hover/focus */
  --text: #ffffff;             High contrast text */
  --text-muted: #6b6d7c;       Hints, timestamps, soft elements */
  --success: #32d690;          Completed or positive status */
  --error: #ef4444;            Incomplete/failed task indicators */
  --progress-bg: rgba(65, 105, 225, 0.1);   Progress bar background */
  --rating-alert-bg: var(--surface);
  --rating-alert-border: var(--border);
  --rating-alert-text: var(--text);
  --rating-alert-button-bg: var(--accent);
  --rating-alert-button-text: var(--text);
}

 App Rating Alert Styles */
}
 Global styles for cards and containers */
.progress-bar {
  background: var(--progress-bg);
  border-radius: 6px;
  overflow: hidden;
}

.progress-bar .fill {
  background: linear-gradient(90deg, var(--accent), var(--accent-hover));
  box-shadow: 0 0 15px var(--accent-glow);
}

 Quest icons and badges */
h1, h2, h3, h4, h5, h6 {
  color: var(--text);
  font-weight: 600;
}

p, span {
  color: var(--text-secondary);
}

.muted {
  color: var(--text-muted);
}



ion-button {
  --height: 40px;
}

.blue-button {
  --background: var(--accent);
  --color: var(--text);
  margin: 0;
  font-weight: 600;
  font-size: 15px;
}

.social-button {
  flex: 1;
  --background: var(--surface);
  --color: var(--text);
  --border-color: var(--border);
  --border-style: solid;
  --border-width: 1px;

  .button-content {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;

    ion-icon {
      font-size: 18px;
      margin-right: 8px;
    }

    span {
      font-size: 15px;
      font-weight: 500;
    }
  }
}


.dark-input {
  --background: var(--surface);
  --color: var(--text);
  --border-color: var(--border);
  --border-style: solid;
  --border-radius: 8px;
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 14px;
  --padding-bottom: 14px;
  --border-width: 1px;
  --font-size: 16px;
  --height: 40px;
}



.upshift-title {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 24px;
  color: var(--text);

  .gradient-text {
    background: linear-gradient(
      270deg,
      var(--text),
      var(--accent),
      var(--text),
      var(--accent)
    );
    background-size: 200% 100%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient 6s ease infinite;
  }
}
