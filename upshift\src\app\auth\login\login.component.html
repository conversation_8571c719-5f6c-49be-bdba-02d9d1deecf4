﻿<ion-content class="ion-padding">
    <div class="background-container">
      <div class="gradient-bg"></div>
      <div class="celestial-body"></div>
    </div>

    <ion-grid>
      <ion-row>
        <ion-col size="12" class="ion-text-center">
          <h1 class="upshift-title">Welcome back to <span class="gradient-text">Upshift</span></h1>
        </ion-col>
      </ion-row>

      <ion-row>
        <ion-col size="12">
          <form [formGroup]="form" (ngSubmit)="login()">
            <ion-input
              formControlName="email"
              type="email"
              placeholder="Email"
              required
              class="dark-input ion-margin-top ion-margin-bottom"
            ></ion-input>

            <ion-input  
              formControlName="password"
              type="password"
              placeholder="Password"
              required
              class="dark-input ion-margin-top ion-margin-bottom"
            ></ion-input>

            <ion-button expand="block" type="submit" class="blue-button">
              Login
            </ion-button>
          </form>

          <div class="divider">
            <span>or continue with</span>
          </div>

          <div class="social-buttons">
            <ion-button class="social-button" (click)="signInWithGoogle()">
              <div class="button-content">
                <ion-icon name="logo-google"></ion-icon>
                <span>Login with Google</span>
              </div>
            </ion-button>

            <ion-button class="social-button" (click)="signInWithApple()">
              <div class="button-content">
                <ion-icon name="logo-apple"></ion-icon>
                <span>Login with Apple</span>
              </div>
            </ion-button>
          </div>
        </ion-col>
      </ion-row>
    </ion-grid>
  </ion-content>