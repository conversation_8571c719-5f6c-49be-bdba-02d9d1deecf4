﻿ion-toolbar {
  --border-width: 0 !important;
  border: none !important;
  border-bottom: none !important;
  box-shadow: none !important;
  --background: transparent;
  .head {
    display: flex;
    align-items: center;
    padding: 16px;

    ion-icon {
      font-size: 24px;
      color: var(--text);
      margin-right: 16px;
      cursor: pointer;
    }

    ion-progress-bar {
      flex: 1;
      --background: var(--progress-bg);
      height: 8px;
      border-radius: 4px;
      margin: 0 16px;
    }

    ion-badge {
      background: var(--accent-glow);
      color: var(--accent);
      padding: 4px 8px;
      border-radius: 12px;
      font-weight: 500;
    }
  }
}

ion-grid {
  ion-row {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;

    ion-text {
      &.can-skip {
        &::before {
          content: '✨ ';
        }
      }
    }
  }

  .questions {
    display: flex;
    justify-content: center;
    flex-direction: column;
    .question {
      min-height: 80px;
    }

    ion-list {
      background: none;
      padding: 0;
      width: 100%;

      ion-button {
        width: 100%;
        margin: 8px 0;
        --background: var(--surface);
        --padding-start: 16px;
        --padding-end: 16px;

        span {
          width: 100%;
          text-align: left;
          display: flex;
          align-items: center;
          color: var(--text);
          font-size: 16px;

          ion-badge {
            margin-right: 16px;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--accent);
            ion-text {
              font-size: 16px;
              color: var(--text);
              margin: 0;
            }
          }

          .answer-text {
            color: var(--text);
            font-size: 16px;
            flex: 1;
            text-align: left;
          }
        }
      }
    }
  }

  .referral-section {
    padding: 32px;
    position: absolute;
    width: 100%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    .referral-content {
      .input-wrapper{
          margin-top: 20px;
          .input-with-button {
            display: flex;
            width: 100%;

            .referral-input {
              flex: 1;
              background: var(--surface, #1e1e1e);
              color: var(--text, #ffffff);
              padding: 16px;
              border-radius: 12px 0 0 12px;
              font-size: 16px;
              border: none;
              outline: none;

              &::placeholder {
                color: var(--text-muted, #a0a0a0);
              }

              &:disabled {
                opacity: 0.7;
              }
            }

            .apply-button {
              background: var(--accent-color, #4169e1);
              color: white;
              border: none;
              border-radius: 0 12px 12px 0;
              padding: 0 16px;
              cursor: pointer;
              display: flex;
              align-items: center;
              justify-content: center;

              &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
              }

              ion-icon {
                font-size: 20px;
              }
            }
          }
      }

        .error-message {
          color: #ff4961;
          font-size: 14px;
          margin-top: 8px;
          text-align: center;
        }

        .loading-indicator {
          color: var(--text-secondary, #a0a3b1);
          font-size: 14px;
          margin-top: 8px;
          text-align: center;
        }

        .applied-code {
          margin-top: 20px;
          display: flex;
          justify-content: center;

          .code-badge {
            display: flex;
            align-items: center;
            background-color: rgba(65, 105, 225, 0.1);
            border-radius: 20px;
            padding: 8px 16px;
            color: var(--accent-color, #4169e1);
            font-size: 14px;

            .remove-code {
              background: none;
              border: none;
              color: var(--accent-color, #4169e1);
              font-size: 18px;
              margin-left: 8px;
              cursor: pointer;
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 0;
              width: 20px;
              height: 20px;
              border-radius: 50%;

              &:hover {
                background-color: rgba(65, 105, 225, 0.2);
              }
            }
          }
        }
    }
  }
}

ion-footer {
  padding: 16px;
  .button-group {
    display: flex;
    gap: 16px;
    width: 100%;
    margin: 0 auto;
    ion-button {
      flex: 1;
      font-size: 16px;
      --color: var(--text);
      position: relative;
      &.skip-btn {
        --background: var(--surface);
      }

      &.submit-btn {
        --background: var(--accent);
      }
    }
  }
}
