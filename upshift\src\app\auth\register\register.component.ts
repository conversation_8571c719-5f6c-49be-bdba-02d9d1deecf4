﻿import { Component, inject, EnvironmentInjector, runInInjectionContext } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { Preferences } from '@capacitor/preferences';
import { SupabaseService } from '../../services/supabase.service';
import { UserService } from '../../services/user.service';

@Component({
  selector: 'app-register',
  standalone: true,
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.scss'],
  imports: [CommonModule, IonicModule, ReactiveFormsModule, RouterModule],
})
export class RegisterComponent {
  form: FormGroup;
  segment: 'login' | 'register' = 'login';
  private fb = inject(FormBuilder);
  private supabaseService = inject(SupabaseService);
  private userService = inject(UserService);
  private router = inject(Router);
  private injector = inject(EnvironmentInjector);

  constructor() {
    this.form = this.fb.group({
      email: [''],
      password: [''],
      confirmPassword: [''],
    });
  }

  onSegmentChange(event: CustomEvent) {
    const value = event.detail.value;
    if (value === 'login' || value === 'register') {
      this.segment = value;
    }
  }

  async login() {
    const { email, password } = this.form.value;

    if (!email || !password) {
      alert('Please enter both email and password');
      return;
    }

    try {
      const { data, error } = await this.supabaseService.getClient().auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        alert('Login error: ' + error.message);
        return;
      }

      await this.handlePostLogin(data.user.id);
    } catch (error: any) {
      alert('Login error: ' + error.message);
    }
  }

  async register() {
    const { email, password, confirmPassword } = this.form.value;

    if (password !== confirmPassword) {
      alert('Passwords do not match');
      return;
    }

    try {
      const { data, error } = await this.supabaseService.getClient().auth.signUp({
        email,
        password
      });

      if (error) {
        alert('Registration error: ' + error.message);
        return;
      }

      if (data?.user) {
        await this.handlePostRegister(data.user.id);
      } else {
        alert('Registration successful but no user data returned');
      }
    } catch (error: any) {
      alert('Registration error: ' + error.message);
    }
  }

  async signInWithGoogle() {
    try {
      const { data, error } = await this.supabaseService.getClient().auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: window.location.origin + '/'
        }
      });

      if (error) {
        alert('Error signing in with Google: ' + error.message);
        return;
      }

    } catch (error: any) {
      alert('Error signing in with Google');
    }
  }

  async signInWithApple() {
    try {
      const { data, error } = await this.supabaseService.getClient().auth.signInWithOAuth({
        provider: 'apple',
        options: {
          redirectTo: window.location.origin + '/'
        }
      });

      if (error) {
        alert('Error signing in with Apple: ' + error.message);
        return;
      }

    } catch (error: any) {
      alert('Error signing in with Apple');
    }
  }

  private async createUserRecord(userId: string, userData: any) {
    try {
      const newUser = {
        id: userId,
        email: userData.email || '',
        username: '',
        name: userData.user_metadata?.full_name || '',
        profile_picture: userData.user_metadata?.avatar_url || '',
        registration_date: new Date(),
        last_login: new Date(),
        active: true,
        xp: null,
        level: null,
        title: null,
        plan: 'none',
        auto_renew: true,
        start_of_current_plan: null,
        end_of_current_plan: null,
        start_of_sick_days: null,
        end_of_sick_days: null,
        subscription_status: 'email marketing',
        affiliate_code_used: null,
        strength_xp: 0,
        money_xp: 0,
        health_xp: 0,
        knowledge_xp: 0,
      };

      const { error } = await this.supabaseService.getClient()
        .from('profiles')
        .insert(newUser);

      if (error) {
        throw error;
      }

    } catch (error) {
      throw error;
    }
  }

  private async handlePostLogin(uid: string) {
    try {
      await Preferences.set({ key: 'uid', value: uid });

      const { data: userData, error } = await this.supabaseService.getClient()
        .from('profiles')
        .select('*')
        .eq('id', uid)
        .single();

      if (error && error.code !== 'PGRST116') { 
      }

      if (!userData) {
        const authUser = this.supabaseService._currentUser.value;
        if (authUser) {
          await this.createUserRecord(uid, authUser);
        }
      } else {
        const { error: updateError } = await this.supabaseService.getClient()
          .from('profiles')
          .update({ last_login: new Date() })
          .eq('id', uid);

        if (updateError) {
        }
      }

      this.router.navigateByUrl('/tabs/home');
    } catch (error) {
    }
  }

  private async handlePostRegister(uid: string) {
    try {
      await Preferences.set({ key: 'uid', value: uid });

      const authUser = this.supabaseService._currentUser.value;
      if (authUser) {
        await this.createUserRecord(uid, authUser);
      }

      this.router.navigateByUrl('/onboarding');
    } catch (error) {
    }
  }
}