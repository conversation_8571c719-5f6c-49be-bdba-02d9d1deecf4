﻿import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { UserService } from '../../services/user.service';
import { User } from '../../models/user.model';
import { Subscription, of, switchMap } from 'rxjs';
import { Router } from '@angular/router';
import { SupabaseService } from '../../services/supabase.service';
import { EmojiInputDirective } from '../../directives/emoji-input.directive';

@Component({
  selector: 'app-profile-settings',
  templateUrl: './profile-settings.page.html',
  styleUrls: ['./profile-settings.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, ReactiveFormsModule, RouterModule, EmojiInputDirective]
})
export class ProfileSettingsPage implements OnInit {
  user: User | null = null;
  userSubscription: Subscription | null = null;

  settingsForm: FormGroup;

  profilePicture: File | null = null;
  tempImagePreview: string | null = null; 
  fileStatus = 'No file selected';
  errors: { [key: string]: string } = {};
  successMessage = '';

  private supabaseService = inject(SupabaseService);
  private userService = inject(UserService);
  private fb = inject(FormBuilder);
  private router = inject(Router);

  constructor() {
    this.settingsForm = this.fb.group({
      firstName: ['', Validators.required],
      lastName: [''],
      username: ['', Validators.required],
      bio: ['', Validators.maxLength(100)],
      show_celebration: [true],
      celebration_name: [''],
      celebration_description: [''],
      celebration_emoji: [''],
      sidequests_switch: [true]
    });
  }

  ngOnInit() {
    this.userSubscription = this.supabaseService.currentUser$.pipe(
      switchMap(authUser => {
        if (!authUser) {
          return of(null);
        }

        return this.userService.getUser(authUser.id);
      })
    ).subscribe(user => {
      if (user) {
        this.user = user;
        this.initializeForm(user);
      } else {
        this.router.navigate(['/login']);
      }
    });
  }

  ngOnDestroy() {
    if (this.userSubscription) {
      this.userSubscription.unsubscribe();
    }
  }

  initializeForm(user: User) {
    const nameParts = (user.name || '').split(' ');
    const firstName = nameParts[0] || '';
    const lastName = nameParts.slice(1).join(' ') || '';

    this.settingsForm.patchValue({
      firstName: firstName,
      lastName: lastName,
      username: user.username || '',
      bio: user.bio || '',
      show_celebration: user.show_celebration !== false, 
      celebration_name: user.celebration_name || 'Another Day, Another W',
      celebration_description: user.celebration_description || 'You\'ve completed all your quests for today. Keep up the great work!',
      celebration_emoji: user.celebration_emoji || '🦅',
      sidequests_switch: user.sidequests_switch !== false 
    });

    this.fileStatus = 'No file selected';
    if (user.profile_picture) {
      this.fileStatus = 'Current profile picture';
    }

    this.settingsForm.markAsPristine();
  }

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];

      const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
      if (!validTypes.includes(file.type) && !file.type.includes('heic') && !file.type.includes('heif')) {
        const fileName = file.name.toLowerCase();
        if (!fileName.endsWith('.heic') && !fileName.endsWith('.heif') &&
            !fileName.endsWith('.jpg') && !fileName.endsWith('.jpeg') &&
            !fileName.endsWith('.png') && !fileName.endsWith('.webp')) {
          this.errors['profilePicture'] = 'File type not supported. Please select JPG, PNG, WebP, or HEIC/HEIF.';
          this.fileStatus = 'Invalid file type';
          this.profilePicture = null;
          return;
        }
      }

      if (file.type === 'image/gif') {
        this.errors['profilePicture'] = 'GIF files are not supported. Please select a static image file.';
        this.fileStatus = 'GIF not supported';
        this.profilePicture = null;
        return;
      }

      delete this.errors['profilePicture'];

      this.profilePicture = file;
      this.fileStatus = file.name;

      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          const dataUrl = e.target.result as string;

          this.tempImagePreview = dataUrl;

          if (this.user) {
            this.user = {
              ...this.user,
              profile_picture: dataUrl
            };
          }

          this.settingsForm.markAsDirty();
        }
      };
      reader.readAsDataURL(file);
    }
  }

  toggleCelebrationSettings() {
    if (!this.settingsForm.get('show_celebration')?.value) {
      this.settingsForm.patchValue({
        celebration_name: 'Another Day, Another W',
        celebration_description: 'You\'ve completed all your quests for today. Keep up the great work!',
        celebration_emoji: ''
      });
    }
  }


  async saveChanges() {

    if (!this.user || !this.user.id) {
      return;
    }

    if (this.settingsForm.invalid && !this.profilePicture) {
      return;
    }

    this.errors = {};
    this.successMessage = '';

    const formValues = this.settingsForm.value;

    const fullName = `${formValues.firstName?.trim() || ''} ${formValues.lastName?.trim() || ''}`.trim();

    const updateData: Partial<User> = {
      name: fullName,
      username: formValues.username?.trim() || '',
      bio: formValues.bio?.trim() || '',
      show_celebration: formValues.show_celebration,
      sidequests_switch: formValues.sidequests_switch
    };

    :', updateData);

    if (formValues.show_celebration) {
      updateData.celebration_name = formValues.celebration_name?.trim() || '';
      updateData.celebration_description = formValues.celebration_description?.trim() || '';
      updateData.celebration_emoji = formValues.celebration_emoji?.trim() || '';
    }

    :', updateData);

    try {
      if (this.profilePicture) {

        let fileExtension = 'jpg'; 
        if (this.profilePicture) {
          const originalName = this.profilePicture.name.toLowerCase();
          if (originalName.endsWith('.png')) {
            fileExtension = 'png';
          } else if (originalName.endsWith('.webp')) {
            fileExtension = 'webp';
          } else if (originalName.endsWith('.heic') || originalName.endsWith('.heif')) {
            fileExtension = 'jpg';
          }
        }

        const fileName = `profile_${this.user.id}_${Date.now()}.${fileExtension}`;

        const bucketName = 'profile-pictures';

        if (this.user.profile_picture) {
          try {

            const { data: files, error: listError } = await this.supabaseService.getClient()
              .storage
              .from(bucketName)
              .list('', {
                limit: 100,
                search: `profile_${this.user.id}`
              });

            if (listError) {
            } else if (files && files.length > 0) {

              const filesToDelete = files.map(file => file.name);

              if (filesToDelete.length > 0) {

                const { error: deleteError } = await this.supabaseService.getClient()
                  .storage
                  .from(bucketName)
                  .remove(filesToDelete);

                if (deleteError) {
                } else {
                }
              }
            } else {
            }
          } catch (deleteErr) {
          }
        }


        const formData = new FormData();
        formData.append('file', this.profilePicture);

        const supabaseUrl = 'https://tobifepmbrrrvshpvrqa.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRvYmlmZXBtYnJycnZzaHB2cnFhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MDI0NzA5NzYsImV4cCI6MjAxODA0Njk3Nn0.Ej3qqUNZLzRnQKYL-POUQJa-3Ib3QJF5F_2zYRvXOYM';

        const { data: { session } } = await this.supabaseService.getClient().auth.getSession();
        const accessToken = session?.access_token;

        if (!accessToken) {
          throw new Error('No access token available');
        }

        const uploadPromise = new Promise<{ publicUrl: string }>((resolve, reject) => {
          const xhr = new XMLHttpRequest();

          const queryParams = new URLSearchParams({
            'cacheControl': '3600',
            'upsert': 'true'
          });

          xhr.open('POST', `${supabaseUrl}/storage/v1/object/${bucketName}/${fileName}?${queryParams.toString()}`);

          xhr.setRequestHeader('Authorization', `Bearer ${accessToken}`);
          xhr.setRequestHeader('apikey', supabaseKey);

          xhr.onload = () => {
            if (xhr.status >= 200 && xhr.status < 300) {
              const { data: urlData } = this.supabaseService.getClient()
                .storage
                .from(bucketName)
                .getPublicUrl(fileName);

              resolve({ publicUrl: urlData.publicUrl });
            } else {
              reject(new Error(`Upload failed: ${xhr.statusText}`));
            }
          };

          xhr.onerror = () => {
            reject(new Error('Network error during upload'));
          };


          xhr.send(formData);
        });

        const { publicUrl } = await uploadPromise;


        updateData.profile_picture = publicUrl;
      }

      try {
        await this.userService.updateUserProfile(this.user.id, updateData);
      } catch (updateError) {
        throw updateError;
      }

      this.user = { ...this.user, ...updateData };

      this.successMessage = 'Profile updated successfully!';

      setTimeout(() => {
        const container = document.querySelector('.container') as HTMLElement;
        if (container) {
          container.scrollTop = 0;
        } else {
          window.scrollTo(0, 0);
        }

        setTimeout(() => {
          this.successMessage = '';
        }, 3000);
      }, 100);

      this.tempImagePreview = null;

      this.settingsForm.markAsPristine();

      this.profilePicture = null;
      this.fileStatus = updateData.profile_picture ? 'Profile picture updated' : 'No file selected';

      await this.userService.refreshCurrentUserProfile();
    } catch (error) {
      this.errors['general'] = 'An error occurred while updating your profile. Please try again.';
      alert('Error: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  async compressImage(file: File): Promise<Blob> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (event) => {
        const img = new Image();
        img.onload = () => {
          const canvas = document.createElement('canvas');

          let width = img.width;
          let height = img.height;
          const maxSize = 800;

          if (width > height && width > maxSize) {
            height = Math.round((height * maxSize) / width);
            width = maxSize;
          } else if (height > maxSize) {
            width = Math.round((width * maxSize) / height);
            height = maxSize;
          }

          canvas.width = width;
          canvas.height = height;

          const ctx = canvas.getContext('2d');
          if (!ctx) {
            reject(new Error('Could not get canvas context'));
            return;
          }

          ctx.drawImage(img, 0, 0, width, height);

          canvas.toBlob(
            (blob) => {
              if (blob) {
                resolve(blob);
              } else {
                reject(new Error('Failed to compress image'));
              }
            },
            'image/jpeg',
            0.8
          );
        };

        img.onerror = () => {
          reject(new Error('Failed to load image'));
        };

        img.src = event.target?.result as string;
      };

      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };

      reader.readAsDataURL(file);
    });
  }

  async navigateToProfile() {
    try {
      await this.userService.refreshCurrentUserProfile();
      this.router.navigate(['/profile']);
    } catch (error) {
      this.router.navigate(['/profile']);
    }
  }

  logout() {
    try {
      this.supabaseService.signOut();
      this.router.navigateByUrl('/');
    } catch (error) {
    }
  }
}
