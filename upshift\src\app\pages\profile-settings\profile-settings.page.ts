import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { UserService } from '../../services/user.service';
import { User } from '../../models/user.model';
import { Subscription, of, switchMap } from 'rxjs';
import { Router } from '@angular/router';
import { SupabaseService } from '../../services/supabase.service';
import { EmojiInputDirective } from '../../directives/emoji-input.directive';

@Component({
  selector: 'app-profile-settings',
  templateUrl: './profile-settings.page.html',
  styleUrls: ['./profile-settings.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, ReactiveFormsModule, RouterModule, EmojiInputDirective]
})
export class ProfileSettingsPage implements OnInit {
  // User data
  user: User | null = null;
  userSubscription: Subscription | null = null;

  // Form
  settingsForm: FormGroup;

  // Profile picture
  profilePicture: File | null = null;
  tempImagePreview: string | null = null; // Temporary preview only for current session
  fileStatus = 'No file selected';
  errors: { [key: string]: string } = {};
  successMessage = '';

  // Services
  private supabaseService = inject(SupabaseService);
  private userService = inject(UserService);
  private fb = inject(FormBuilder);
  private router = inject(Router);

  constructor() {
    // Initialize form with empty values
    this.settingsForm = this.fb.group({
      firstName: ['', Validators.required],
      lastName: [''],
      username: ['', Validators.required],
      bio: ['', Validators.maxLength(100)],
      show_celebration: [true],
      celebration_name: [''],
      celebration_description: [''],
      celebration_emoji: [''],
      sidequests_switch: [true]
    });
  }

  ngOnInit() {
    this.userSubscription = this.supabaseService.currentUser$.pipe(
      switchMap(authUser => {
        if (!authUser) {
          console.log('ProfileSettings: No authenticated user found');
          return of(null);
        }

        console.log('ProfileSettings: Auth user ID:', authUser.id);
        return this.userService.getUser(authUser.id);
      })
    ).subscribe(user => {
      if (user) {
        this.user = user;
        this.initializeForm(user);
      } else {
        console.error('ProfileSettings: No user data received');
        this.router.navigate(['/login']);
      }
    });
  }

  ngOnDestroy() {
    if (this.userSubscription) {
      this.userSubscription.unsubscribe();
    }
  }

  initializeForm(user: User) {
    // Split name into first and last name
    const nameParts = (user.name || '').split(' ');
    const firstName = nameParts[0] || '';
    const lastName = nameParts.slice(1).join(' ') || '';

    this.settingsForm.patchValue({
      firstName: firstName,
      lastName: lastName,
      username: user.username || '',
      bio: user.bio || '',
      show_celebration: user.show_celebration !== false, // Default to true if undefined
      celebration_name: user.celebration_name || 'Another Day, Another W',
      celebration_description: user.celebration_description || 'You\'ve completed all your quests for today. Keep up the great work!',
      celebration_emoji: user.celebration_emoji || '🦅',
      sidequests_switch: user.sidequests_switch !== false // Default to true if undefined
    });

    // Reset file status
    this.fileStatus = 'No file selected';
    if (user.profile_picture) {
      this.fileStatus = 'Current profile picture';
    }

    // Reset form state
    this.settingsForm.markAsPristine();
  }

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];

      // Validate file type
      const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
      if (!validTypes.includes(file.type) && !file.type.includes('heic') && !file.type.includes('heif')) {
        // Special check for HEIC/HEIF which might have inconsistent MIME types
        const fileName = file.name.toLowerCase();
        if (!fileName.endsWith('.heic') && !fileName.endsWith('.heif') &&
            !fileName.endsWith('.jpg') && !fileName.endsWith('.jpeg') &&
            !fileName.endsWith('.png') && !fileName.endsWith('.webp')) {
          this.errors['profilePicture'] = 'File type not supported. Please select JPG, PNG, WebP, or HEIC/HEIF.';
          this.fileStatus = 'Invalid file type';
          this.profilePicture = null;
          return;
        }
      }

      // Check if file is a GIF (we don't want to allow GIFs)
      if (file.type === 'image/gif') {
        this.errors['profilePicture'] = 'GIF files are not supported. Please select a static image file.';
        this.fileStatus = 'GIF not supported';
        this.profilePicture = null;
        return;
      }

      // Clear errors
      delete this.errors['profilePicture'];

      // Set file and status
      this.profilePicture = file;
      this.fileStatus = file.name;

      // Create preview immediately
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          const dataUrl = e.target.result as string;

          // Store the temporary preview
          this.tempImagePreview = dataUrl;

          // Also update the user object for immediate display
          if (this.user) {
            // Create a copy of the user object to avoid reference issues
            this.user = {
              ...this.user,
              profile_picture: dataUrl
            };
          }

          // Mark the form as dirty so the Save button becomes enabled
          this.settingsForm.markAsDirty();
        }
      };
      reader.readAsDataURL(file);
    }
  }

  toggleCelebrationSettings() {
    // If celebration is turned off, reset the fields
    if (!this.settingsForm.get('show_celebration')?.value) {
      this.settingsForm.patchValue({
        celebration_name: 'Another Day, Another W',
        celebration_description: 'You\'ve completed all your quests for today. Keep up the great work!',
        celebration_emoji: ''
      });
    }
  }

  // The emoji input is now handled by the EmojiInputDirective

  async saveChanges() {
    console.log('saveChanges called');

    if (!this.user || !this.user.id) {
      console.error('No user or user ID');
      return;
    }

    // Check if form is invalid (but allow saving if only profile picture has changed)
    if (this.settingsForm.invalid && !this.profilePicture) {
      console.error('Form is invalid and no profile picture selected');
      return;
    }

    // Reset errors and success message
    this.errors = {};
    this.successMessage = '';

    const formValues = this.settingsForm.value;
    console.log('Form values:', formValues);

    // Combine first and last name
    const fullName = `${formValues.firstName?.trim() || ''} ${formValues.lastName?.trim() || ''}`.trim();

    // Prepare update data
    const updateData: Partial<User> = {
      name: fullName,
      username: formValues.username?.trim() || '',
      bio: formValues.bio?.trim() || '',
      show_celebration: formValues.show_celebration,
      sidequests_switch: formValues.sidequests_switch
    };

    console.log('Update data (before celebration):', updateData);

    // Only include celebration fields if celebrations are enabled
    if (formValues.show_celebration) {
      updateData.celebration_name = formValues.celebration_name?.trim() || '';
      updateData.celebration_description = formValues.celebration_description?.trim() || '';
      updateData.celebration_emoji = formValues.celebration_emoji?.trim() || '';
    }

    console.log('Update data (after celebration):', updateData);
    console.log('Profile picture selected:', !!this.profilePicture);

    try {
      // Upload profile picture if selected
      if (this.profilePicture) {
        console.log('Processing image...');

        // Get file extension from original file
        let fileExtension = 'jpg'; // Default to jpg
        if (this.profilePicture) {
          const originalName = this.profilePicture.name.toLowerCase();
          if (originalName.endsWith('.png')) {
            fileExtension = 'png';
          } else if (originalName.endsWith('.webp')) {
            fileExtension = 'webp';
          } else if (originalName.endsWith('.heic') || originalName.endsWith('.heif')) {
            // For HEIC/HEIF, we'll convert to JPG during compression
            fileExtension = 'jpg';
          }
        }

        // Create a unique file name that includes the user ID and preserves the extension
        const fileName = `profile_${this.user.id}_${Date.now()}.${fileExtension}`;
        console.log('Uploading to:', fileName);

        // Use the profile-pictures bucket (must be created in Supabase dashboard)
        const bucketName = 'profile-pictures';

        // Try to delete the previous profile picture if it exists
        if (this.user.profile_picture) {
          try {
            console.log('Current profile picture URL:', this.user.profile_picture);

            // List all files in the bucket that match the user's profile pattern
            const { data: files, error: listError } = await this.supabaseService.getClient()
              .storage
              .from(bucketName)
              .list('', {
                limit: 100,
                search: `profile_${this.user.id}`
              });

            if (listError) {
              console.warn('Error listing files:', listError);
            } else if (files && files.length > 0) {
              console.log('Found existing profile pictures:', files);

              // Create an array of file names to delete
              const filesToDelete = files.map(file => file.name);

              if (filesToDelete.length > 0) {
                console.log('Attempting to delete previous profile pictures:', filesToDelete);

                const { error: deleteError } = await this.supabaseService.getClient()
                  .storage
                  .from(bucketName)
                  .remove(filesToDelete);

                if (deleteError) {
                  console.warn('Error deleting previous profile pictures:', deleteError);
                  // Continue anyway, this is not critical
                } else {
                  console.log('Previous profile pictures deleted successfully');
                }
              }
            } else {
              console.log('No existing profile pictures found');
            }
          } catch (deleteErr) {
            // If there's an error listing or deleting files, just log it and continue
            console.warn('Error while trying to delete previous profile pictures:', deleteErr);
          }
        }

        // Try a completely different approach - use XMLHttpRequest
        console.log('File type:', this.profilePicture.type);

        // Create a FormData object
        const formData = new FormData();
        formData.append('file', this.profilePicture);

        // Get Supabase URL and key from environment
        const supabaseUrl = 'https://tobifepmbrrrvshpvrqa.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRvYmlmZXBtYnJycnZzaHB2cnFhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MDI0NzA5NzYsImV4cCI6MjAxODA0Njk3Nn0.Ej3qqUNZLzRnQKYL-POUQJa-3Ib3QJF5F_2zYRvXOYM';

        // Get the access token
        const { data: { session } } = await this.supabaseService.getClient().auth.getSession();
        const accessToken = session?.access_token;

        if (!accessToken) {
          throw new Error('No access token available');
        }

        // Create a promise to handle the XMLHttpRequest
        const uploadPromise = new Promise<{ publicUrl: string }>((resolve, reject) => {
          const xhr = new XMLHttpRequest();

          // Add additional query parameters
          const queryParams = new URLSearchParams({
            'cacheControl': '3600',
            'upsert': 'true'
          });

          // Set up the request with query parameters
          xhr.open('POST', `${supabaseUrl}/storage/v1/object/${bucketName}/${fileName}?${queryParams.toString()}`);

          // Set headers
          xhr.setRequestHeader('Authorization', `Bearer ${accessToken}`);
          xhr.setRequestHeader('apikey', supabaseKey);

          // Handle response
          xhr.onload = () => {
            if (xhr.status >= 200 && xhr.status < 300) {
              // Success - get the public URL
              const { data: urlData } = this.supabaseService.getClient()
                .storage
                .from(bucketName)
                .getPublicUrl(fileName);

              resolve({ publicUrl: urlData.publicUrl });
            } else {
              // Error
              console.error('Upload failed:', xhr.statusText, xhr.responseText);
              reject(new Error(`Upload failed: ${xhr.statusText}`));
            }
          };

          // Handle network errors
          xhr.onerror = () => {
            console.error('Network error during upload');
            reject(new Error('Network error during upload'));
          };

          // For FormData, we should NOT set Content-Type header as the browser will set it with the boundary
          // We can't actually remove a header, but we'll just not set it in the first place

          // Send the request with the FormData
          xhr.send(formData);
        });

        // Wait for the upload to complete
        const { publicUrl } = await uploadPromise;
        console.log('Upload successful, public URL:', publicUrl);

        // No error handling needed here since we're using promises

        // Set the profile picture URL in the update data
        updateData.profile_picture = publicUrl;
        console.log('Profile picture URL:', updateData.profile_picture);
      }

      // Update user in Supabase
      console.log('Updating user profile in Supabase...');
      try {
        await this.userService.updateUserProfile(this.user.id, updateData);
        console.log('User profile updated successfully');
      } catch (updateError) {
        console.error('Error in updateUserProfile:', updateError);
        throw updateError;
      }

      // Update local user object first
      this.user = { ...this.user, ...updateData };
      console.log('Local user object updated');

      // Then show success message
      this.successMessage = 'Profile updated successfully!';
      console.log('Success message set:', this.successMessage);

      // Force a small delay to ensure the DOM updates before scrolling
      setTimeout(() => {
        // Scroll to the top of the page to show the success message
        const container = document.querySelector('.container') as HTMLElement;
        if (container) {
          container.scrollTop = 0;
        } else {
          window.scrollTo(0, 0);
        }
        console.log('Scrolled to top');

        // Automatically hide the success message after 3 seconds
        setTimeout(() => {
          this.successMessage = '';
          console.log('Success message cleared');
        }, 3000);
      }, 100);

      // Reset temporary preview
      this.tempImagePreview = null;

      // Reset form state
      this.settingsForm.markAsPristine();

      // Reset file input
      this.profilePicture = null;
      this.fileStatus = updateData.profile_picture ? 'Profile picture updated' : 'No file selected';
      console.log('Form reset complete');

      // Refresh the current user profile to ensure it's up to date
      await this.userService.refreshCurrentUserProfile();
      console.log('User profile refreshed after save');
    } catch (error) {
      console.error('Error updating profile:', error);
      this.errors['general'] = 'An error occurred while updating your profile. Please try again.';
      alert('Error: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  // Helper method to compress images
  async compressImage(file: File): Promise<Blob> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (event) => {
        const img = new Image();
        img.onload = () => {
          // Create a canvas to draw the compressed image
          const canvas = document.createElement('canvas');

          // Calculate new dimensions (max 800px width/height while maintaining aspect ratio)
          let width = img.width;
          let height = img.height;
          const maxSize = 800;

          if (width > height && width > maxSize) {
            height = Math.round((height * maxSize) / width);
            width = maxSize;
          } else if (height > maxSize) {
            width = Math.round((width * maxSize) / height);
            height = maxSize;
          }

          canvas.width = width;
          canvas.height = height;

          // Draw the image on the canvas
          const ctx = canvas.getContext('2d');
          if (!ctx) {
            reject(new Error('Could not get canvas context'));
            return;
          }

          ctx.drawImage(img, 0, 0, width, height);

          // Convert to blob with quality 0.8 (80%)
          canvas.toBlob(
            (blob) => {
              if (blob) {
                resolve(blob);
              } else {
                reject(new Error('Failed to compress image'));
              }
            },
            'image/jpeg',
            0.8
          );
        };

        img.onerror = () => {
          reject(new Error('Failed to load image'));
        };

        img.src = event.target?.result as string;
      };

      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };

      reader.readAsDataURL(file);
    });
  }

  async navigateToProfile() {
    // Refresh the user profile before navigating
    try {
      await this.userService.refreshCurrentUserProfile();
      console.log('User profile refreshed before navigation');
      this.router.navigate(['/profile']);
    } catch (error) {
      console.error('Error refreshing profile:', error);
      // Navigate anyway even if refresh fails
      this.router.navigate(['/profile']);
    }
  }

  logout() {
    try {
      this.supabaseService.signOut();
      console.log('User logged out');
      this.router.navigateByUrl('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  }
}
