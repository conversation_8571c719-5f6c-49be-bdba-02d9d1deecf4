﻿import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule, ToastController } from '@ionic/angular';
import { RouterModule, Router } from '@angular/router';
import { GroupService } from '../../../services/group.service';
import { Group, GroupJoinRequest } from '../../../models/group.model';
import { take } from 'rxjs';
import { SupabaseService } from '../../../services/supabase.service';

@Component({
  selector: 'app-group-list',
  templateUrl: './group-list.page.html',
  styleUrls: ['./group-list.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule]
})
export class GroupListPage implements OnInit {
  userId: string | null = null;

  groups: Group[] = [];
  joinRequests: GroupJoinRequest[] = [];

  invitationCode = '';

  private supabaseService = inject(SupabaseService);
  private groupService = inject(GroupService);
  private router = inject(Router);
  private toastController = inject(ToastController);

  constructor() {}

  ngOnInit() {

    this.supabaseService.currentUser$.pipe(
      take(1)
    ).subscribe(authUser => {

      if (authUser) {
        this.userId = authUser.id;


        this.loadGroups();
        this.loadJoinRequests();
      } else {
      }
    });
  }

  async loadGroups() {
    if (!this.userId) return;

    return new Promise<void>((resolve) => {
      this.groupService.getUserGroups(this.userId!).subscribe(groups => {
        this.groups = groups;
        resolve();
      });
    });
  }

  loadJoinRequests() {
    if (!this.userId) {
      return;
    }


    this.supabaseService.getClient()
      .from('profiles')
      .select('username')
      .eq('id', this.userId)
      .single()
      .then(response => {
        if (response.error) {
          return;
        }

        const username = response.data.username;

        this.supabaseService.getClient()
          .from('group_join_requests')
          .select('*')
          .ilike('username_invited', username)
          .then(directResponse => {

            if (directResponse.error) {
            } else {

              this.joinRequests = directResponse.data;

            }
          });
      });

    this.groupService.getJoinRequestsForUserId(this.userId).subscribe({
      next: (requests) => {
      },
      error: (error) => {
      }
    });
  }

  async joinGroupByCode() {
    if (!this.userId || !this.invitationCode.trim()) return;

    try {
      const success = await this.groupService.joinGroupByCode(this.userId!, this.invitationCode.trim());

      if (success) {
        const toast = await this.toastController.create({
          message: 'Successfully joined the group!',
          duration: 2000,
          position: 'bottom',
          color: 'success'
        });
        await toast.present();

        this.invitationCode = '';

        await this.loadGroups();

        window.location.reload();
      } else {
        const toast = await this.toastController.create({
          message: 'Invalid or expired invitation code.',
          duration: 2000,
          position: 'bottom',
          color: 'danger'
        });
        await toast.present();
      }
    } catch (error) {

      const toast = await this.toastController.create({
        message: 'Failed to join group. Please try again.',
        duration: 2000,
        position: 'bottom',
        color: 'danger'
      });
      await toast.present();
    }
  }

  goToLeaderboard() {
    window.location.href = '/leaderboard/groups';
  }

  refreshData() {

    if (this.userId) {
      this.supabaseService.getClient()
        .from('profiles')
        .select('username')
        .eq('id', this.userId!)  
        .single()
        .then(response => {
          if (response.error) {
          } else {
            const username = response.data.username;

            this.supabaseService.getClient()
              .from('group_join_requests')
              .select('*')
              .ilike('username_invited', username)
              .then(joinResponse => {
                if (joinResponse.error) {
                } else {

                  this.joinRequests = joinResponse.data;
                }
              });
          }
        });

      this.loadGroups();
    }
  }
}
