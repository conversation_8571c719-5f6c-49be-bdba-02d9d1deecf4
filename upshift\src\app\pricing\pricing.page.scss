﻿
ion-content {
  &::part(scroll) {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
  }

  ion-grid {
    .icon-col {
      position: relative;
      display: flex;
      justify-content: center;

      .vertical-bar {
        position: absolute;
        width: 2px;
        background: var(--accent);
        top: 48px;
        bottom: -24px;
        opacity: 0.5;

        &.last-bar {
          display: none;
        }
      }

      .icon-badge {
        background: var(--surface);
        border: 2px solid var(--accent);
        padding: 12px;
        border-radius: 50%;
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1;

        ion-icon {
          font-size: 24px;
          color: var(--accent);
        }
      }
    }

    .text-col {
      h2 {
        color: var(--text);
        font-size: 20px;
        font-weight: 600;
        margin-top: 12px;
      }

      ion-text {
        font-size: 16px;
      }
    }
    .affiliate-discount-row {
      margin-top: 24px;

      .affiliate-discount-card {
        background: rgba(var(--accent-rgb), 0.1);
        border: 1px solid var(--accent);
        border-radius: 12px;
        margin: 0;
        width: 90%;
        margin-left: auto;
        margin-right: auto;

        ion-card-content {
          display: flex;
          align-items: center;
          padding: 12px;

          ion-icon {
            font-size: 24px;
            color: var(--accent);
            margin-right: 12px;
          }

          p {
            margin: 0;
            color: var(--text);
            font-size: 14px;
            margin-left: auto;
            margin-right: auto;

            strong {
              color: var(--accent);
            }
          }
        }
      }
    }

    .price-row {
      display: flex;
      justify-content: center;
      gap: 24px;
      margin-top: 36px;

      .price {
        position: relative;
        background: var(--surface);
        border: 1px solid var(--border);
        border-radius: 16px;
        transition: transform 0.2s ease;
        display: flex;
        justify-content: center;
        align-items: center;

        &:hover {
          transform: translateY(-4px);
          border-color: var(--accent);
          box-shadow: 0 8px 24px var(--accent-glow);
        }

        ion-badge {
          position: absolute;
          top: -12px;
          left: 50%;
          transform: translateX(-50%);
          background: var(--accent);
          color: var(--text);
          padding: 4px 12px;
          border-radius: 12px;
          font-weight: 500;
        }

        ion-text {
          color: var(--accent);
          font-size: 20px;
          font-weight: 700;
          display: block;
          margin-bottom: 16px;
        }
      }
    }
  }
}

ion-footer {
  display: flex;
  justify-content: center;
  ion-button {
    width: 100%;
  }
}
