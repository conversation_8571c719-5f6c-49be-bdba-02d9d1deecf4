{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _GroupDetailPage;\nimport { inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule, ToastController } from '@ionic/angular';\nimport { ActivatedRoute, Router, RouterModule, NavigationEnd } from '@angular/router';\nimport { take, combineLatest, switchMap, map, of, filter } from 'rxjs';\nimport { SupabaseService } from '../../../services/supabase.service';\nimport { GroupService } from '../../../services/group.service';\nimport { UserService } from '../../../services/user.service';\nimport { GroupMember } from '../../../models/supabase.models';\nimport { EmojiInputDirective } from '../../../directives/emoji-input.directive';\nimport { ComponentsModule } from '../../../components/components.module';\nimport { GroupWaitingRoomComponent } from '../../../components/group-waiting-room/group-waiting-room.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"../../../components/group-sidequest/group-sidequest.component\";\nconst _c0 = () => [\"/groups\"];\nconst _c1 = a0 => [\"/groups\", a0, \"settings\"];\nfunction GroupDetailPage_app_group_waiting_room_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-group-waiting-room\", 54);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"groupId\", ctx_r0.groupId || \"\")(\"groupName\", (ctx_r0.group == null ? null : ctx_r0.group.name) || \"Group\")(\"isAdmin\", ctx_r0.isAdmin);\n  }\n}\nfunction GroupDetailPage_div_1_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(day_r3);\n  }\n}\nfunction GroupDetailPage_div_1_div_20__svg_svg_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 79);\n    i0.ɵɵelement(1, \"circle\", 80);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const date_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"low\", date_r5.completionPercentage && date_r5.completionPercentage < 50);\n    i0.ɵɵattribute(\"stroke-dasharray\", (date_r5.completionPercentage || 0) + \", 100\")(\"data-date\", date_r5.date);\n  }\n}\nfunction GroupDetailPage_div_1_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵlistener(\"click\", function GroupDetailPage_div_1_div_20_Template_div_click_0_listener() {\n      const date_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.selectDateWithUrl(date_r5));\n    });\n    i0.ɵɵtemplate(1, GroupDetailPage_div_1_div_20__svg_svg_1_Template, 2, 4, \"svg\", 77);\n    i0.ɵɵelementStart(2, \"span\", 78);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const date_r5 = ctx.$implicit;\n    i0.ɵɵclassProp(\"active\", date_r5.isToday)(\"selected\", date_r5.isSelected)(\"disabled\", date_r5.isFuture || date_r5.isBeforeJoin);\n    i0.ɵɵproperty(\"title\", date_r5.isBeforeJoin ? \"You cannot access dates before you joined the group\" : date_r5.isFuture ? \"Cannot select future dates\" : \"\");\n    i0.ɵɵattribute(\"isBeforeJoin\", date_r5.isBeforeJoin);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", date_r5.completionPercentage && date_r5.completionPercentage > 0 && !date_r5.isFuture && !date_r5.isBeforeJoin);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(date_r5.day);\n  }\n}\nfunction GroupDetailPage_div_1_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 81);\n    i0.ɵɵtext(1, \"Future date\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GroupDetailPage_div_1_ng_container_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \"Today\");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction GroupDetailPage_div_1_ng_container_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \"Yesterday\");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction GroupDetailPage_div_1_ng_container_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \"Tomorrow\");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction GroupDetailPage_div_1_ng_container_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.headerText);\n  }\n}\nfunction GroupDetailPage_div_1_ng_container_32_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 90)(1, \"ion-range\", 91);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GroupDetailPage_div_1_ng_container_32_div_1_div_9_Template_ion_range_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const quest_r7 = i0.ɵɵnextContext(2).$implicit;\n      i0.ɵɵtwoWayBindingSet(quest_r7.value_achieved, $event) || (quest_r7.value_achieved = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ionChange\", function GroupDetailPage_div_1_ng_container_32_div_1_div_9_Template_ion_range_ionChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const quest_r7 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.updateQuestProgress(quest_r7, $event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 92);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const quest_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMapInterpolate1(\"--progress-value: \", quest_r7.value_achieved / quest_r7.goal_value * 100, \"%\");\n    i0.ɵɵproperty(\"max\", quest_r7.goal_value);\n    i0.ɵɵtwoWayProperty(\"ngModel\", quest_r7.value_achieved);\n    i0.ɵɵproperty(\"step\", 1);\n    i0.ɵɵattribute(\"data-quest-id\", quest_r7.id)(\"data-quest-type\", quest_r7.quest_type);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate4(\" \", quest_r7.value_achieved, \"\", quest_r7.goal_unit === \"min\" ? \"m\" : quest_r7.goal_unit === \"hr\" ? \"h\" : \"s\", \"/\", quest_r7.goal_value, \"\", quest_r7.goal_unit === \"min\" ? \"m\" : quest_r7.goal_unit === \"hr\" ? \"h\" : \"s\", \" \");\n  }\n}\nfunction GroupDetailPage_div_1_ng_container_32_div_1_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 93)(1, \"ion-range\", 91);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GroupDetailPage_div_1_ng_container_32_div_1_div_10_Template_ion_range_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const quest_r7 = i0.ɵɵnextContext(2).$implicit;\n      i0.ɵɵtwoWayBindingSet(quest_r7.value_achieved, $event) || (quest_r7.value_achieved = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ionChange\", function GroupDetailPage_div_1_ng_container_32_div_1_div_10_Template_ion_range_ionChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const quest_r7 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.updateQuestProgress(quest_r7, $event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 94)(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 95);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const quest_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMapInterpolate1(\"--progress-value: \", quest_r7.value_achieved / quest_r7.goal_value * 100, \"%\");\n    i0.ɵɵproperty(\"max\", quest_r7.goal_value);\n    i0.ɵɵtwoWayProperty(\"ngModel\", quest_r7.value_achieved);\n    i0.ɵɵproperty(\"step\", 1);\n    i0.ɵɵattribute(\"data-quest-id\", quest_r7.id)(\"data-quest-type\", quest_r7.quest_type);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", quest_r7.value_achieved, \"/\", quest_r7.goal_value, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" Members: \", quest_r7.completed_members, \"/\", quest_r7.total_members, \" \");\n  }\n}\nfunction GroupDetailPage_div_1_ng_container_32_div_1_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const quest_r7 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\uD83D\\uDD25\", quest_r7.streak, \"d \");\n  }\n}\nfunction GroupDetailPage_div_1_ng_container_32_div_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 96);\n  }\n}\nfunction GroupDetailPage_div_1_ng_container_32_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83)(1, \"div\", 84);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 85)(4, \"h3\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 86);\n    i0.ɵɵtemplate(9, GroupDetailPage_div_1_ng_container_32_div_1_div_9_Template, 4, 12, \"div\", 87)(10, GroupDetailPage_div_1_ng_container_32_div_1_div_10_Template, 7, 12, \"div\", 88);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, GroupDetailPage_div_1_ng_container_32_div_1_div_11_Template, 2, 1, \"div\", 89)(12, GroupDetailPage_div_1_ng_container_32_div_1_div_12_Template, 1, 0, \"div\", 89);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const quest_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"completed\", quest_r7.completed);\n    i0.ɵɵattribute(\"data-quest-id\", quest_r7.id)(\"data-group-quest\", true)(\"data-is-admin\", ctx_r0.isAdmin);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", quest_r7.emoji, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(quest_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(quest_r7.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", quest_r7.goal_unit === \"time\" || quest_r7.goal_unit === \"min\" || quest_r7.goal_unit === \"hr\" || quest_r7.goal_unit === \"sec\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", quest_r7.goal_unit !== \"time\" && quest_r7.goal_unit !== \"min\" && quest_r7.goal_unit !== \"hr\" && quest_r7.goal_unit !== \"sec\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isToday());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isToday());\n  }\n}\nfunction GroupDetailPage_div_1_ng_container_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, GroupDetailPage_div_1_ng_container_32_div_1_Template, 13, 12, \"div\", 82);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const quest_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !quest_r7.is_side_quest);\n  }\n}\nfunction GroupDetailPage_div_1_app_group_sidequest_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-group-sidequest\", 97);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"groupId\", ctx_r0.groupId || \"\")(\"userId\", ctx_r0.userId || \"\")(\"joinedDate\", ctx_r0.joinedDate)(\"isAdmin\", ctx_r0.isAdmin)(\"enableSidequests\", (ctx_r0.group == null ? null : ctx_r0.group.enable_sidequests) || false)(\"selectedDate\", ctx_r0.selectedDate);\n  }\n}\nfunction GroupDetailPage_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"header\")(2, \"div\", 56)(3, \"div\", 57);\n    i0.ɵɵelement(4, \"img\", 58);\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6, \"Upshift\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"h1\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"a\", 59);\n    i0.ɵɵtext(10, \"\\u2190 Back to groups\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 60)(12, \"div\", 61)(13, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function GroupDetailPage_div_1_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.navigateWeekWithUrl(-1));\n    });\n    i0.ɵɵtext(14, \"\\u2190\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 63);\n    i0.ɵɵtemplate(16, GroupDetailPage_div_1_div_16_Template, 2, 1, \"div\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function GroupDetailPage_div_1_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.navigateWeekWithUrl(1));\n    });\n    i0.ɵɵtext(18, \"\\u2192\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 66);\n    i0.ɵɵtemplate(20, GroupDetailPage_div_1_div_20_Template, 4, 10, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, GroupDetailPage_div_1_div_21_Template, 2, 0, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"section\", 69)(23, \"div\", 70)(24, \"h2\");\n    i0.ɵɵtext(25, \"Quests\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"h2\");\n    i0.ɵɵtemplate(27, GroupDetailPage_div_1_ng_container_27_Template, 2, 0, \"ng-container\", 71)(28, GroupDetailPage_div_1_ng_container_28_Template, 2, 0, \"ng-container\", 71)(29, GroupDetailPage_div_1_ng_container_29_Template, 2, 0, \"ng-container\", 71)(30, GroupDetailPage_div_1_ng_container_30_Template, 2, 1, \"ng-container\", 71);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 72);\n    i0.ɵɵtemplate(32, GroupDetailPage_div_1_ng_container_32_Template, 2, 1, \"ng-container\", 73);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(33, GroupDetailPage_div_1_app_group_sidequest_33_Template, 1, 6, \"app-group-sidequest\", 74);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r0.group == null ? null : ctx_r0.group.emoji, \" \", ctx_r0.group == null ? null : ctx_r0.group.name, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(12, _c0));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.dayNames);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.weekDates);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.hasFutureDates());\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isToday());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isYesterday());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isTomorrow());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isToday() && !ctx_r0.isYesterday() && !ctx_r0.isTomorrow());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.quests);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r0.group == null ? null : ctx_r0.group.enable_sidequests) && ctx_r0.groupId && ctx_r0.userId);\n  }\n}\nfunction GroupDetailPage_div_2_a_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 101);\n    i0.ɵɵlistener(\"click\", function GroupDetailPage_div_2_a_3_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.openAddQuestModal($event));\n    });\n    i0.ɵɵelementStart(1, \"span\", 102);\n    i0.ɵɵtext(2, \"+\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Add Quest \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GroupDetailPage_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98)(1, \"a\", 99);\n    i0.ɵɵtext(2, \"\\u2699\\uFE0F Settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, GroupDetailPage_div_2_a_3_Template, 4, 0, \"a\", 100);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(2, _c1, ctx_r0.groupId));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isAdmin);\n  }\n}\nfunction GroupDetailPage_div_91_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 103)(1, \"input\", 104);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GroupDetailPage_div_91_Template_input_ngModelChange_1_listener($event) {\n      const day_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      i0.ɵɵtwoWayBindingSet(day_r11.selected, $event) || (day_r11.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 105);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const day_r11 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"day-\" + day_r11.value.toLowerCase())(\"value\", day_r11.value);\n    i0.ɵɵtwoWayProperty(\"ngModel\", day_r11.selected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", \"day-\" + day_r11.value.toLowerCase());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(day_r11.label);\n  }\n}\nfunction GroupDetailPage_div_96_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 103)(1, \"input\", 106);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GroupDetailPage_div_96_Template_input_ngModelChange_1_listener($event) {\n      const day_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      i0.ɵɵtwoWayBindingSet(day_r13.selected, $event) || (day_r13.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 105);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const day_r13 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"month-day-\" + day_r13.value)(\"value\", day_r13.value);\n    i0.ɵɵtwoWayProperty(\"ngModel\", day_r13.selected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", \"month-day-\" + day_r13.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(day_r13.value);\n  }\n}\nexport class GroupDetailPage {\n  constructor() {\n    this.userId = null;\n    this.groupId = null;\n    this.group = null;\n    this.members = [];\n    this.isAdmin = false;\n    this.requiredXp = 0;\n    this.joinedDate = '';\n    this.dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];\n    this.weekOffset = 0;\n    this.weekDates = [];\n    this.selectedDate = '';\n    this.headerText = '';\n    this.isBeforeJoinDate = false;\n    this.joinedToday = false;\n    this.quests = [];\n    this.dailyQuest = null;\n    this.memberStatus = {\n      id: '',\n      completed: false,\n      value_achieved: 0\n    };\n    this.showAddQuestModal = false;\n    this.newQuest = {\n      emoji: '🎯',\n      name: '',\n      description: '',\n      quest_type: 'build',\n      category: '',\n      priority: 'basic',\n      goal_value: 1,\n      goal_unit: 'count',\n      goal_period: 'day'\n    };\n    this.daysOfWeek = [{\n      value: 'Monday',\n      label: 'M',\n      selected: false\n    }, {\n      value: 'Tuesday',\n      label: 'T',\n      selected: false\n    }, {\n      value: 'Wednesday',\n      label: 'W',\n      selected: false\n    }, {\n      value: 'Thursday',\n      label: 'T',\n      selected: false\n    }, {\n      value: 'Friday',\n      label: 'F',\n      selected: false\n    }, {\n      value: 'Saturday',\n      label: 'S',\n      selected: false\n    }, {\n      value: 'Sunday',\n      label: 'S',\n      selected: false\n    }];\n    this.daysOfMonth = Array.from({\n      length: 31\n    }, (_, i) => ({\n      value: i + 1,\n      selected: false\n    }));\n    this.subscriptions = [];\n    this.supabaseService = inject(SupabaseService);\n    this.groupService = inject(GroupService);\n    this.userService = inject(UserService);\n    this.route = inject(ActivatedRoute);\n    this.router = inject(Router);\n    this.toastController = inject(ToastController);\n  }\n  ngOnInit() {\n    this.subscriptions.push(this.supabaseService.currentUser$.subscribe(user => {\n      if (user) {\n        this.userId = user.id;\n        this.route.paramMap.pipe(take(1)).subscribe(params => {\n          this.groupId = params.get('id');\n          if (this.groupId) {\n            this.loadGroup();\n            this.loadMembers();\n            this.route.queryParams.pipe(take(1)).subscribe(queryParams => {\n              this.initializeCalendar();\n            });\n          }\n        });\n      } else {\n        this.router.navigate(['/login']);\n      }\n    }));\n    this.subscriptions.push(this.router.events.pipe(filter(event => event instanceof NavigationEnd), filter(() => !!this.groupId)).subscribe(() => {\n      this.refreshGroupData();\n    }));\n  }\n  refreshGroupData() {\n    if (!this.groupId) return;\n    this.loadGroup();\n    this.loadMembers();\n    if (this.selectedDate) {\n      this.loadQuestsForDate(this.selectedDate);\n      this.loadDailySideQuest(this.selectedDate);\n    }\n  }\n  ionViewWillEnter() {\n    this.refreshGroupData();\n  }\n  ionViewDidEnter() {}\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  loadGroup() {\n    if (!this.groupId) return;\n    this.subscriptions.push(this.groupService.getGroup(this.groupId).subscribe(group => {\n      if (group) {\n        this.group = group;\n        this.calculateRequiredXp();\n      } else {\n        this.router.navigate(['/groups']);\n      }\n    }));\n  }\n  loadMembers() {\n    if (!this.groupId || !this.userId) return;\n    this.subscriptions.push(this.groupService.getGroupMembers(this.groupId).subscribe(members => {\n      const currentUserMember = members.find(m => m.user_id === this.userId);\n      this.isAdmin = (currentUserMember === null || currentUserMember === void 0 ? void 0 : currentUserMember.is_admin) || false;\n      if (currentUserMember && currentUserMember.joined_date) {\n        this.joinedDate = new Date(currentUserMember.joined_date).toISOString().split('T')[0];\n        const joinedDate = new Date(currentUserMember.joined_date);\n        joinedDate.setHours(0, 0, 0, 0);\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        this.joinedToday = joinedDate.getTime() === today.getTime();\n      }\n      const membersWithProfiles = [];\n      const membersCopy = members.map(member => ({\n        ...member,\n        id: member.id || '',\n        group_id: member.group_id || '',\n        user_id: member.user_id || '',\n        nickname: member.nickname || '',\n        is_admin: member.is_admin || false,\n        joined_date: member.joined_date || new Date()\n      }));\n      for (const member of membersCopy) {\n        this.userService.getUserProfile(member.user_id).pipe(take(1)).subscribe(profile => {\n          this.userService.getUserStats(member.user_id).pipe(take(1)).subscribe(stats => {\n            membersWithProfiles.push({\n              ...member,\n              profile_picture: (profile === null || profile === void 0 ? void 0 : profile.profile_picture) || undefined,\n              username: profile === null || profile === void 0 ? void 0 : profile.username,\n              total_xp: (stats === null || stats === void 0 ? void 0 : stats.total_xp) || 0,\n              completed_quests: (stats === null || stats === void 0 ? void 0 : stats.completed_quests) || 0,\n              max_streak: (stats === null || stats === void 0 ? void 0 : stats.max_streak) || 0\n            });\n            this.members = [...membersWithProfiles];\n          });\n        });\n      }\n    }));\n  }\n  calculateRequiredXp() {\n    if (!this.group) return;\n    this.requiredXp = this.group.level * 1000;\n  }\n  getWeekOffset(direction) {\n    return this.weekOffset + direction;\n  }\n  initializeCalendar() {\n    const today = new Date();\n    this.route.queryParams.subscribe(params => {\n      if (params['week_offset']) {\n        try {\n          this.weekOffset = parseInt(params['week_offset']);\n        } catch (error) {\n          this.weekOffset = 0;\n        }\n      } else {\n        this.weekOffset = 0;\n      }\n      let selectedDate = today;\n      if (params['date']) {\n        try {\n          selectedDate = new Date(params['date']);\n          if (isNaN(selectedDate.getTime())) {\n            selectedDate = today;\n          }\n        } catch (error) {\n          selectedDate = today;\n        }\n      }\n      this.selectedDate = this.formatDate(selectedDate);\n      const startDate = new Date(today);\n      startDate.setDate(today.getDate() + this.weekOffset * 7);\n      this.generateWeekDates(startDate);\n      this.updateHeaderText();\n      this.loadQuestsForDate(this.selectedDate);\n      this.loadDailySideQuest(this.selectedDate);\n    });\n  }\n  formatDate(date) {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n  generateWeekDates(startDate) {\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    const currentDay = startDate.getDay();\n    const mondayOffset = currentDay === 0 ? -6 : 1;\n    const monday = new Date(startDate);\n    monday.setDate(startDate.getDate() - currentDay + mondayOffset);\n    const dates = [];\n    for (let i = 0; i < 7; i++) {\n      const date = new Date(monday);\n      date.setDate(monday.getDate() + i);\n      const dateStr = this.formatDate(date);\n      date.setHours(0, 0, 0, 0);\n      const isToday = date.getTime() === today.getTime();\n      const isFuture = date.getTime() > today.getTime();\n      let isBeforeJoin = false;\n      if (this.members.length > 0 && this.userId) {\n        const currentMember = this.members.find(m => m.user_id === this.userId);\n        if (currentMember && currentMember.joined_date) {\n          const joinDate = new Date(currentMember.joined_date);\n          joinDate.setHours(0, 0, 0, 0);\n          isBeforeJoin = date < joinDate;\n        }\n      }\n      dates.push({\n        date: dateStr,\n        day: date.getDate(),\n        isToday,\n        isSelected: dateStr === this.selectedDate,\n        isFuture,\n        isBeforeJoin,\n        completionPercentage: 0,\n        totalQuests: 0,\n        completedQuests: 0\n      });\n    }\n    this.weekDates = dates;\n    this.updateWeekDateProgress();\n  }\n  navigateWeek(direction) {\n    this.route.queryParams.pipe(take(1)).subscribe(params => {\n      let currentWeekOffset = 0;\n      if (params['week_offset']) {\n        try {\n          currentWeekOffset = parseInt(params['week_offset']);\n        } catch (error) {}\n      }\n      const newWeekOffset = currentWeekOffset + direction;\n      const firstDate = new Date(this.weekDates[0].date);\n      firstDate.setDate(firstDate.getDate() + direction * 7);\n      this.router.navigate([], {\n        relativeTo: this.route,\n        queryParams: {\n          week_offset: newWeekOffset,\n          date: this.selectedDate\n        }\n      });\n      this.generateWeekDates(firstDate);\n      const selectedDateExists = this.weekDates.some(date => date.date === this.selectedDate);\n      if (!selectedDateExists) {\n        const validDate = this.weekDates.find(date => !date.isFuture && !date.isBeforeJoin);\n        if (validDate) {\n          this.selectDate(validDate);\n        }\n      }\n    });\n  }\n  selectDate(date) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (date.isFuture) {\n        const toast = yield _this.toastController.create({\n          message: 'Cannot select future dates',\n          duration: 2000,\n          position: 'bottom',\n          color: 'warning'\n        });\n        yield toast.present();\n        return;\n      }\n      if (date.isBeforeJoin) {\n        _this.isBeforeJoinDate = true;\n        const toast = yield _this.toastController.create({\n          message: 'You cannot access dates before you joined the group',\n          duration: 2000,\n          position: 'bottom',\n          color: 'warning'\n        });\n        yield toast.present();\n        return;\n      } else {\n        _this.isBeforeJoinDate = false;\n      }\n      _this.router.navigate([], {\n        relativeTo: _this.route,\n        queryParams: {\n          date: date.date\n        },\n        queryParamsHandling: 'merge'\n      });\n      _this.weekDates.forEach(d => d.isSelected = d.date === date.date);\n      _this.selectedDate = date.date;\n      _this.updateHeaderText();\n      _this.loadQuestsForDate(date.date);\n      _this.loadDailySideQuest(date.date);\n    })();\n  }\n  updateHeaderText() {\n    if (!this.selectedDate) {\n      this.headerText = 'Today';\n      return;\n    }\n    const date = new Date(this.selectedDate);\n    const today = new Date();\n    date.setHours(0, 0, 0, 0);\n    today.setHours(0, 0, 0, 0);\n    console.log('GroupDetailPage: Comparing dates:', {\n      selectedDate: this.selectedDate,\n      selectedDateObj: date.toISOString(),\n      todayObj: today.toISOString(),\n      selectedTime: date.getTime(),\n      todayTime: today.getTime(),\n      isToday: date.getTime() === today.getTime()\n    });\n    if (date.getTime() === today.getTime()) {\n      this.headerText = 'Today';\n    } else {\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      console.log('GroupDetailPage: Checking if yesterday:', {\n        yesterdayObj: yesterday.toISOString(),\n        yesterdayTime: yesterday.getTime(),\n        isYesterday: date.getTime() === yesterday.getTime()\n      });\n      if (date.getTime() === yesterday.getTime()) {\n        this.headerText = 'Yesterday';\n      } else {\n        const tomorrow = new Date(today);\n        tomorrow.setDate(tomorrow.getDate() + 1);\n        console.log('GroupDetailPage: Checking if tomorrow:', {\n          tomorrowObj: tomorrow.toISOString(),\n          tomorrowTime: tomorrow.getTime(),\n          isTomorrow: date.getTime() === tomorrow.getTime()\n        });\n        if (date.getTime() === tomorrow.getTime()) {\n          this.headerText = 'Tomorrow';\n        } else {\n          this.headerText = date.toLocaleDateString('en-US', {\n            month: 'short',\n            day: 'numeric'\n          });\n        }\n      }\n    }\n  }\n  hasFutureDates() {\n    return this.weekDates.some(date => date.isFuture);\n  }\n  isToday() {\n    if (!this.selectedDate) return false;\n    const date = new Date(this.selectedDate);\n    const today = new Date();\n    date.setHours(0, 0, 0, 0);\n    today.setHours(0, 0, 0, 0);\n    return date.getTime() === today.getTime();\n  }\n  isYesterday() {\n    if (!this.selectedDate) return false;\n    const date = new Date(this.selectedDate);\n    const today = new Date();\n    date.setHours(0, 0, 0, 0);\n    today.setHours(0, 0, 0, 0);\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    return date.getTime() === yesterday.getTime();\n  }\n  isTomorrow() {\n    if (!this.selectedDate) return false;\n    const date = new Date(this.selectedDate);\n    const today = new Date();\n    date.setHours(0, 0, 0, 0);\n    today.setHours(0, 0, 0, 0);\n    const tomorrow = new Date(today);\n    tomorrow.setDate(tomorrow.getDate() + 1);\n    return date.getTime() === tomorrow.getTime();\n  }\n  updateWeekDateProgress() {\n    this.weekDates.forEach(date => {\n      if (date.isFuture || date.isBeforeJoin) {\n        date.completionPercentage = 0;\n        date.totalQuests = 0;\n        date.completedQuests = 0;\n        return;\n      }\n      date.totalQuests = 0;\n      date.completedQuests = 0;\n      date.completionPercentage = 0;\n    });\n  }\n  loadQuestsForDate(dateStr) {\n    if (!this.groupId || !this.userId) return;\n    this.subscriptions.push(this.groupService.getGroupQuests(this.groupId).subscribe(quests => {\n      if (quests && quests.length > 0) {\n        const selectedDate = new Date(dateStr);\n        const filteredQuests = quests.filter(quest => {\n          const createdDate = new Date(quest.created);\n          createdDate.setHours(0, 0, 0, 0);\n          selectedDate.setHours(0, 0, 0, 0);\n          return createdDate <= selectedDate;\n        });\n        const questObservables = filteredQuests.map(quest => {\n          if (!quest.id || !this.userId) {\n            return of(null);\n          }\n          return this.groupService.getGroupQuestProgress(quest.id, this.userId, new Date(dateStr)).pipe(switchMap(progress => {\n            if (!quest.id) {\n              return of({});\n            }\n            return this.groupService.getGroupQuestProgressForDate(quest.id, new Date(dateStr)).pipe(map(allProgress => {\n              const selectedDate = new Date(dateStr);\n              selectedDate.setHours(0, 0, 0, 0);\n              const eligibleMembers = this.members.filter(member => {\n                if (!member.joined_date) return false;\n                const joinDate = new Date(member.joined_date);\n                joinDate.setHours(0, 0, 0, 0);\n                return joinDate < selectedDate;\n              });\n              const completedMembers = allProgress.filter(p => p.completed && eligibleMembers.some(m => m.user_id === p.user_id)).length;\n              let message = '';\n              return {\n                id: quest.id,\n                name: quest.name,\n                description: quest.description || '',\n                emoji: quest.emoji,\n                category: quest.category,\n                quest_type: quest.quest_type,\n                goal_value: quest.goal_value,\n                goal_unit: quest.goal_unit,\n                value_achieved: progress ? progress.value_achieved : 0,\n                completed: progress ? progress.completed : false,\n                streak: quest.streak,\n                is_side_quest: false,\n                completed_members: completedMembers,\n                total_members: eligibleMembers.length,\n                message: message\n              };\n            }));\n          }));\n        }).filter(obs => obs !== null);\n        if (questObservables.length > 0) {\n          combineLatest(questObservables).subscribe(processedQuests => {\n            this.quests = processedQuests;\n          }, error => {\n            this.quests = [];\n          });\n        } else {\n          this.quests = [];\n        }\n      } else {\n        this.quests = [];\n      }\n    }, error => {\n      this.quests = [];\n    }));\n  }\n  loadDailySideQuest(dateStr) {\n    var _this$group;\n    if (!this.groupId || !this.userId) return;\n    if (!((_this$group = this.group) !== null && _this$group !== void 0 && _this$group.enable_sidequests)) {\n      this.dailyQuest = null;\n      return;\n    }\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    const selectedDate = new Date(dateStr);\n    selectedDate.setHours(0, 0, 0, 0);\n    if (selectedDate.getTime() !== today.getTime()) {\n      this.dailyQuest = null;\n      return;\n    }\n    this.subscriptions.push(this.groupService.getGroupSideQuest(this.groupId).subscribe(sideQuest => {\n      if (sideQuest) {\n        this.groupService.getGroupSideQuestPoolItem(sideQuest.current_quest_id).subscribe(poolItem => {\n          if (poolItem) {\n            if (!sideQuest.id || !this.userId) {\n              return;\n            }\n            this.groupService.getGroupSideQuestMemberStatus(sideQuest.id, this.userId).subscribe(status => {\n              if (!sideQuest.id) {\n                return;\n              }\n              this.groupService.getGroupSideQuestMemberStatuses(sideQuest.id).subscribe(allStatuses => {\n                const today = new Date();\n                today.setHours(0, 0, 0, 0);\n                const eligibleMembers = this.members.filter(member => {\n                  if (!member.joined_date) return false;\n                  const joinDate = new Date(member.joined_date);\n                  joinDate.setHours(0, 0, 0, 0);\n                  return joinDate < today;\n                });\n                const eligibleMemberIds = eligibleMembers.map(m => m.user_id);\n                const todayStr = new Date().toISOString().split('T')[0];\n                const completedMembers = allStatuses.filter(s => s.completed && eligibleMemberIds.includes(s.member_id) && typeof s.last_updated === 'string' && s.last_updated === todayStr).length;\n                const totalMembers = eligibleMembers.length;\n                if (!poolItem.id) {\n                  return;\n                }\n                this.dailyQuest = {\n                  id: sideQuest.id || '',\n                  streak: sideQuest.streak,\n                  completed_members: completedMembers,\n                  total_members: totalMembers,\n                  current_quest: {\n                    id: poolItem.id || '',\n                    name: poolItem.name,\n                    description: poolItem.description || '',\n                    emoji: poolItem.emoji,\n                    goal_value: poolItem.goal_value,\n                    goal_unit: poolItem.goal_unit\n                  }\n                };\n                this.memberStatus = status ? {\n                  id: status.id || '',\n                  completed: status.completed,\n                  value_achieved: status.value_achieved\n                } : {\n                  id: '',\n                  completed: false,\n                  value_achieved: 0\n                };\n              }, error => {});\n            }, error => {\n              this.memberStatus = {\n                id: '',\n                completed: false,\n                value_achieved: 0\n              };\n            });\n          }\n        }, error => {});\n      } else {\n        this.dailyQuest = null;\n      }\n    }, error => {\n      this.dailyQuest = null;\n    }));\n  }\n  updateQuestProgress(quest, event) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.groupId || !_this2.userId) return;\n      const newValue = event.detail ? event.detail.value : parseInt(event.target.value);\n      if (_this2.isBeforeJoinDate) {\n        quest.message = 'You can participate in group quests starting tomorrow after joining the group.';\n        quest.value_achieved = quest.value_achieved;\n        return;\n      }\n      const selectedDate = _this2.selectedDate ? new Date(_this2.selectedDate) : new Date();\n      try {\n        const result = yield _this2.groupService.toggleGroupQuestCompletion(quest.id, _this2.userId, selectedDate, newValue);\n        if (result.success) {\n          quest.value_achieved = newValue;\n          if (quest.quest_type === 'build') {\n            quest.completed = newValue >= quest.goal_value;\n          } else {\n            quest.completed = newValue < quest.goal_value;\n          }\n          _this2.loadQuestsForDate(_this2.selectedDate);\n        } else {\n          quest.message = result.message;\n          quest.value_achieved = quest.value_achieved;\n        }\n      } catch (error) {\n        quest.value_achieved = quest.value_achieved;\n        quest.message = 'An error occurred while updating quest progress.';\n      }\n    })();\n  }\n  openAddQuestModal(event) {\n    event.preventDefault();\n    this.newQuest = {\n      emoji: '🎯',\n      name: '',\n      description: '',\n      quest_type: 'build',\n      category: '',\n      priority: 'basic',\n      goal_value: 1,\n      goal_unit: 'count',\n      goal_period: 'day'\n    };\n    this.daysOfWeek.forEach(day => day.selected = false);\n    this.daysOfMonth.forEach(day => day.selected = false);\n    this.showAddQuestModal = true;\n  }\n  closeAddQuestModal() {\n    this.showAddQuestModal = false;\n  }\n  goToSettings() {\n    if (this.groupId) {\n      window.open(`/groups/${this.groupId}/settings`, '_blank');\n    }\n  }\n  navigateWeekWithUrl(direction) {\n    const newWeekOffset = this.weekOffset + direction;\n    this.weekOffset = newWeekOffset;\n    this.router.navigate([], {\n      relativeTo: this.route,\n      queryParams: {\n        week_offset: newWeekOffset,\n        date: this.selectedDate\n      },\n      queryParamsHandling: 'merge'\n    });\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    const startDate = new Date(today);\n    startDate.setDate(today.getDate() + this.weekOffset * 7);\n    this.generateWeekDates(startDate);\n    const selectedDateObj = this.weekDates.find(d => d.date === this.selectedDate);\n    if (selectedDateObj && (selectedDateObj.isFuture || selectedDateObj.isBeforeJoin)) {\n      const validDate = this.weekDates.find(date => !date.isFuture && !date.isBeforeJoin);\n      if (validDate) {\n        this.selectDate(validDate);\n      }\n    }\n  }\n  selectDateWithUrl(date) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (date.isFuture) {\n        const toast = yield _this3.toastController.create({\n          message: 'Cannot select future dates',\n          duration: 2000,\n          position: 'bottom',\n          color: 'warning'\n        });\n        yield toast.present();\n        return;\n      }\n      if (date.isBeforeJoin) {\n        const toast = yield _this3.toastController.create({\n          message: 'You cannot access dates before you joined the group',\n          duration: 2000,\n          position: 'bottom',\n          color: 'warning'\n        });\n        yield toast.present();\n        return;\n      }\n      _this3.router.navigate([], {\n        relativeTo: _this3.route,\n        queryParams: {\n          date: date.date\n        },\n        queryParamsHandling: 'merge'\n      });\n      _this3.weekDates.forEach(d => d.isSelected = d.date === date.date);\n      _this3.selectedDate = date.date;\n      _this3.updateHeaderText();\n      _this3.loadQuestsForDate(date.date);\n      _this3.loadDailySideQuest(date.date);\n    })();\n  }\n  onGoalPeriodChange() {\n    if (this.newQuest.goal_period === 'week') {\n      this.daysOfWeek.forEach(day => day.selected = false);\n    } else if (this.newQuest.goal_period === 'month') {\n      this.daysOfMonth.forEach(day => day.selected = false);\n    }\n  }\n  createGroupQuest() {\n    if (!this.groupId || !this.userId) return;\n    if (!this.newQuest.name || !this.newQuest.category || !this.newQuest.priority) {\n      alert('Please fill in all required fields');\n      return;\n    }\n    let selectedDays = [];\n    let task_days_of_week = undefined;\n    let task_days_of_month = undefined;\n    if (this.newQuest.goal_period === 'week') {\n      selectedDays = this.daysOfWeek.filter(day => day.selected).map(day => day.value);\n      task_days_of_week = selectedDays.join(',');\n    } else if (this.newQuest.goal_period === 'month') {\n      selectedDays = this.daysOfMonth.filter(day => day.selected).map(day => day.value);\n      task_days_of_month = selectedDays.join(',');\n    }\n    const questData = {\n      ...this.newQuest,\n      group_id: this.groupId,\n      task_days_of_week,\n      task_days_of_month\n    };\n    this.groupService.createGroupQuest(questData).then(questId => {\n      this.loadQuestsForDate(this.selectedDate);\n    }).catch(error => {\n      alert('Failed to create quest: ' + error.message);\n    });\n    this.closeAddQuestModal();\n    this.newQuest = {\n      emoji: '🎯',\n      name: '',\n      description: '',\n      quest_type: 'build',\n      category: '',\n      priority: 'basic',\n      goal_value: 1,\n      goal_unit: 'count',\n      goal_period: 'day'\n    };\n  }\n  toggleSideQuest() {\n    if (!this.groupId || !this.userId || !this.dailyQuest || this.isBeforeJoinDate) return;\n    if (this.memberStatus.completed) {\n      this.memberStatus.value_achieved = 0;\n      this.memberStatus.completed = false;\n    } else {\n      this.memberStatus.value_achieved = this.dailyQuest.current_quest.goal_value;\n      this.memberStatus.completed = true;\n    }\n    if (this.memberStatus.id) {\n      this.groupService.updateGroupSideQuestMemberStatus(this.memberStatus.id, this.memberStatus.completed, this.memberStatus.value_achieved).then(() => {\n        this.loadDailySideQuest(this.selectedDate);\n      }).catch(error => {});\n    } else {\n      this.groupService.createGroupSideQuestMemberStatus(this.dailyQuest.id, this.userId, this.memberStatus.completed, this.memberStatus.value_achieved).then(statusId => {\n        this.memberStatus.id = statusId;\n        this.loadDailySideQuest(this.selectedDate);\n      }).catch(error => {});\n    }\n  }\n  getCategoryIcon(category) {\n    switch (category.toLowerCase()) {\n      case 'strength':\n        return '💪';\n      case 'money':\n        return '💰';\n      case 'health':\n        return '❤️';\n      case 'knowledge':\n        return '🧠';\n      default:\n        return '🎯';\n    }\n  }\n  getCategoryColor(category) {\n    switch (category.toLowerCase()) {\n      case 'strength':\n        return '#FF9500';\n      case 'money':\n        return '#30D158';\n      case 'health':\n        return '#FF2D55';\n      case 'knowledge':\n        return '#5E5CE6';\n      default:\n        return '#4D7BFF';\n    }\n  }\n  getTotalXP() {\n    if (!this.group) return 0;\n    return this.group.strength_xp + this.group.money_xp + this.group.health_xp + this.group.knowledge_xp;\n  }\n  getCompletedQuests() {\n    return this.members.reduce((total, member) => total + (member.completed_quests || 0), 0);\n  }\n  getProgressPercentage(xp) {\n    if (this.requiredXp <= 0) return 0;\n    return Math.min(100, Math.round(xp / this.requiredXp * 100));\n  }\n  updateSliderBackground(slider) {\n    if (!slider) {\n      return;\n    }\n    let sliderElement;\n    let sliderValue = 0;\n    let minValue = 0;\n    let maxValue = 100;\n    let sliderQuestId = '';\n    if (slider instanceof HTMLInputElement) {\n      sliderElement = slider;\n      sliderQuestId = slider.getAttribute('data-quest-id') || '';\n      sliderValue = parseInt(slider.value);\n      minValue = parseInt(slider.min);\n      maxValue = parseInt(slider.max);\n    } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {\n      sliderElement = slider;\n      sliderQuestId = slider.getAttribute('data-quest-id') || '';\n      const valueAttr = slider.getAttribute('value') || '0';\n      const minAttr = slider.getAttribute('min') || '0';\n      const maxAttr = slider.getAttribute('max') || '100';\n      sliderValue = parseInt(valueAttr);\n      minValue = parseInt(minAttr);\n      maxValue = parseInt(maxAttr);\n    } else {\n      return;\n    }\n    if (!sliderQuestId) {\n      return;\n    }\n    const percentage = maxValue > minValue ? (sliderValue - minValue) / (maxValue - minValue) * 100 : 0;\n    if (sliderElement.tagName === 'ION-RANGE') {\n      sliderElement.style.setProperty('--progress-value', `${percentage}%`);\n    } else {\n      sliderElement.style.background = `linear-gradient(to right, #4169E1 0%, #4169E1 ${percentage}%, #2C2C2E ${percentage}%, #2C2C2E 100%)`;\n    }\n  }\n}\n_GroupDetailPage = GroupDetailPage;\n_GroupDetailPage.ɵfac = function GroupDetailPage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _GroupDetailPage)();\n};\n_GroupDetailPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _GroupDetailPage,\n  selectors: [[\"app-group-detail\"]],\n  decls: 99,\n  vars: 20,\n  consts: [[3, \"groupId\", \"groupName\", \"isAdmin\", 4, \"ngIf\"], [\"class\", \"container\", 4, \"ngIf\"], [\"class\", \"group-footer\", 4, \"ngIf\"], [\"id\", \"add-quest-modal\", 1, \"modal\"], [1, \"modal-content\"], [1, \"close-modal\", 3, \"click\"], [\"id\", \"add-quest-form\", 3, \"ngSubmit\"], [2, \"display\", \"flex\", \"gap\", \"10px\"], [1, \"form-group\"], [\"type\", \"text\", \"id\", \"emoji\", \"name\", \"emoji\", \"value\", \"\\uD83C\\uDFAF\", \"appEmojiInput\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"id\", \"name\", \"name\", \"name\", \"placeholder\", \"Enter quest name\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"description\"], [\"id\", \"description\", \"name\", \"description\", \"placeholder\", \"Enter quest description\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"quest_type\"], [\"id\", \"quest_type\", \"name\", \"quest_type\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"build\"], [\"value\", \"quit\"], [\"for\", \"category\"], [\"id\", \"category\", \"name\", \"category\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"\"], [\"value\", \"strength\"], [\"value\", \"money\"], [\"value\", \"health\"], [\"value\", \"knowledge\"], [\"for\", \"priority\"], [\"id\", \"priority\", \"name\", \"priority\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"basic\"], [\"value\", \"high\"], [1, \"form-group\", \"goal-settings\"], [1, \"goal-inputs\"], [\"type\", \"number\", \"id\", \"goal_value\", \"name\", \"goal_value\", \"value\", \"1\", \"min\", \"1\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"goal_unit\", \"name\", \"goal_unit\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"count\"], [\"value\", \"steps\"], [\"value\", \"m\"], [\"value\", \"km\"], [\"value\", \"sec\"], [\"value\", \"min\"], [\"value\", \"hr\"], [\"value\", \"Cal\"], [\"value\", \"g\"], [\"value\", \"mg\"], [\"value\", \"drink\"], [\"for\", \"goal_period\"], [\"id\", \"goal_period\", \"name\", \"goal_period\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [\"value\", \"day\"], [\"value\", \"week\"], [\"value\", \"month\"], [\"id\", \"days-of-week-container\", 1, \"form-group\", \"schedule-container\"], [1, \"days-selector\"], [\"class\", \"day-checkbox\", 4, \"ngFor\", \"ngForOf\"], [\"id\", \"days-of-month-container\", 1, \"form-group\", \"schedule-container\"], [1, \"month-days-selector\"], [\"type\", \"submit\", 1, \"submit-btn\"], [3, \"groupId\", \"groupName\", \"isAdmin\"], [1, \"container\"], [1, \"logo-wrap\"], [1, \"logo\"], [\"src\", \"assets/images/upshift_icon_mini.svg\", \"alt\", \"Upshift\"], [1, \"back-link\", 3, \"routerLink\"], [1, \"week-calendar\"], [1, \"calendar-nav\"], [1, \"nav-arrow\", \"prev\", 3, \"click\"], [1, \"days\"], [\"class\", \"day-name\", 4, \"ngFor\", \"ngForOf\"], [1, \"nav-arrow\", \"next\", 3, \"click\"], [1, \"dates\"], [\"class\", \"date\", 3, \"active\", \"selected\", \"disabled\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"future-date-label\", 4, \"ngIf\"], [1, \"quests\"], [2, \"display\", \"flex\", \"justify-content\", \"space-between\"], [4, \"ngIf\"], [1, \"quest-list\"], [4, \"ngFor\", \"ngForOf\"], [3, \"groupId\", \"userId\", \"joinedDate\", \"isAdmin\", \"enableSidequests\", \"selectedDate\", 4, \"ngIf\"], [1, \"day-name\"], [1, \"date\", 3, \"click\", \"title\"], [\"class\", \"date-progress\", \"viewBox\", \"0 0 36 36\", 4, \"ngIf\"], [1, \"date-content\"], [\"viewBox\", \"0 0 36 36\", 1, \"date-progress\"], [\"cx\", \"18\", \"cy\", \"18\", \"r\", \"15.5\", 1, \"progress-circle\"], [1, \"future-date-label\"], [\"class\", \"quest-item\", 3, \"completed\", 4, \"ngIf\"], [1, \"quest-item\"], [1, \"quest-icon\"], [1, \"quest-info\"], [1, \"progress-container\"], [\"class\", \"progress-time\", 4, \"ngIf\"], [\"class\", \"progress\", 4, \"ngIf\"], [\"class\", \"quest-streak\", 4, \"ngIf\"], [1, \"progress-time\"], [\"min\", \"0\", \"snaps\", \"true\", \"ticks\", \"false\", \"snaps-per-step\", \"true\", 1, \"progress-slider\", 3, \"ngModelChange\", \"ionChange\", \"max\", \"ngModel\", \"step\"], [1, \"progress-text\"], [1, \"progress\"], [1, \"progress-text\", \"values\"], [1, \"members-count\"], [1, \"quest-streak\"], [3, \"groupId\", \"userId\", \"joinedDate\", \"isAdmin\", \"enableSidequests\", \"selectedDate\"], [1, \"group-footer\"], [1, \"settings-btn\", 2, \"text-decoration\", \"none\", 3, \"routerLink\"], [\"href\", \"#\", \"id\", \"add-quest-btn\", \"class\", \"add-quest-link\", 3, \"click\", 4, \"ngIf\"], [\"href\", \"#\", \"id\", \"add-quest-btn\", 1, \"add-quest-link\", 3, \"click\"], [1, \"add-quest-icon\"], [1, \"day-checkbox\"], [\"type\", \"checkbox\", \"name\", \"days_of_week\", 3, \"ngModelChange\", \"id\", \"value\", \"ngModel\"], [3, \"for\"], [\"type\", \"checkbox\", \"name\", \"days_of_month\", 3, \"ngModelChange\", \"id\", \"value\", \"ngModel\"]],\n  template: function GroupDetailPage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, GroupDetailPage_app_group_waiting_room_0_Template, 1, 3, \"app-group-waiting-room\", 0)(1, GroupDetailPage_div_1_Template, 34, 13, \"div\", 1)(2, GroupDetailPage_div_2_Template, 4, 4, \"div\", 2);\n      i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"span\", 5);\n      i0.ɵɵlistener(\"click\", function GroupDetailPage_Template_span_click_5_listener() {\n        return ctx.closeAddQuestModal();\n      });\n      i0.ɵɵtext(6, \"\\u00D7\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(7, \"h2\");\n      i0.ɵɵtext(8, \"Add New Group Quest\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(9, \"form\", 6);\n      i0.ɵɵlistener(\"ngSubmit\", function GroupDetailPage_Template_form_ngSubmit_9_listener() {\n        return ctx.createGroupQuest();\n      });\n      i0.ɵɵelementStart(10, \"div\", 7)(11, \"div\", 8)(12, \"input\", 9);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function GroupDetailPage_Template_input_ngModelChange_12_listener($event) {\n        i0.ɵɵtwoWayBindingSet(ctx.newQuest.emoji, $event) || (ctx.newQuest.emoji = $event);\n        return $event;\n      });\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(13, \"div\", 8)(14, \"input\", 10);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function GroupDetailPage_Template_input_ngModelChange_14_listener($event) {\n        i0.ɵɵtwoWayBindingSet(ctx.newQuest.name, $event) || (ctx.newQuest.name = $event);\n        return $event;\n      });\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(15, \"div\", 8)(16, \"label\", 11);\n      i0.ɵɵtext(17, \"Description\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(18, \"textarea\", 12);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function GroupDetailPage_Template_textarea_ngModelChange_18_listener($event) {\n        i0.ɵɵtwoWayBindingSet(ctx.newQuest.description, $event) || (ctx.newQuest.description = $event);\n        return $event;\n      });\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(19, \"div\", 8)(20, \"label\", 13);\n      i0.ɵɵtext(21, \"Quest Type\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(22, \"select\", 14);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function GroupDetailPage_Template_select_ngModelChange_22_listener($event) {\n        i0.ɵɵtwoWayBindingSet(ctx.newQuest.quest_type, $event) || (ctx.newQuest.quest_type = $event);\n        return $event;\n      });\n      i0.ɵɵelementStart(23, \"option\", 15);\n      i0.ɵɵtext(24, \"Build Habit\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(25, \"option\", 16);\n      i0.ɵɵtext(26, \"Quit Habit\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(27, \"div\", 8)(28, \"label\", 17);\n      i0.ɵɵtext(29, \"Category\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(30, \"select\", 18);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function GroupDetailPage_Template_select_ngModelChange_30_listener($event) {\n        i0.ɵɵtwoWayBindingSet(ctx.newQuest.category, $event) || (ctx.newQuest.category = $event);\n        return $event;\n      });\n      i0.ɵɵelementStart(31, \"option\", 19);\n      i0.ɵɵtext(32, \"Select a category\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(33, \"option\", 20);\n      i0.ɵɵtext(34, \"Strength\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(35, \"option\", 21);\n      i0.ɵɵtext(36, \"Money\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(37, \"option\", 22);\n      i0.ɵɵtext(38, \"Health\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(39, \"option\", 23);\n      i0.ɵɵtext(40, \"Knowledge\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(41, \"div\", 8)(42, \"label\", 24);\n      i0.ɵɵtext(43, \"Priority\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(44, \"select\", 25);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function GroupDetailPage_Template_select_ngModelChange_44_listener($event) {\n        i0.ɵɵtwoWayBindingSet(ctx.newQuest.priority, $event) || (ctx.newQuest.priority = $event);\n        return $event;\n      });\n      i0.ɵɵelementStart(45, \"option\", 26);\n      i0.ɵɵtext(46, \"Basic\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(47, \"option\", 27);\n      i0.ɵɵtext(48, \"High Priority\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(49, \"div\", 28)(50, \"label\");\n      i0.ɵɵtext(51, \"Goal\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(52, \"div\", 29)(53, \"input\", 30);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function GroupDetailPage_Template_input_ngModelChange_53_listener($event) {\n        i0.ɵɵtwoWayBindingSet(ctx.newQuest.goal_value, $event) || (ctx.newQuest.goal_value = $event);\n        return $event;\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(54, \"select\", 31);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function GroupDetailPage_Template_select_ngModelChange_54_listener($event) {\n        i0.ɵɵtwoWayBindingSet(ctx.newQuest.goal_unit, $event) || (ctx.newQuest.goal_unit = $event);\n        return $event;\n      });\n      i0.ɵɵelementStart(55, \"option\", 32);\n      i0.ɵɵtext(56, \"count\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(57, \"option\", 33);\n      i0.ɵɵtext(58, \"steps\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(59, \"option\", 34);\n      i0.ɵɵtext(60, \"meters\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(61, \"option\", 35);\n      i0.ɵɵtext(62, \"kilometers\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(63, \"option\", 36);\n      i0.ɵɵtext(64, \"seconds\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(65, \"option\", 37);\n      i0.ɵɵtext(66, \"minutes\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(67, \"option\", 38);\n      i0.ɵɵtext(68, \"hours\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(69, \"option\", 39);\n      i0.ɵɵtext(70, \"calories\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(71, \"option\", 40);\n      i0.ɵɵtext(72, \"grams\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(73, \"option\", 41);\n      i0.ɵɵtext(74, \"milligrams\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(75, \"option\", 42);\n      i0.ɵɵtext(76, \"drinks\");\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(77, \"div\", 8)(78, \"label\", 43);\n      i0.ɵɵtext(79, \"Frequency\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(80, \"select\", 44);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function GroupDetailPage_Template_select_ngModelChange_80_listener($event) {\n        i0.ɵɵtwoWayBindingSet(ctx.newQuest.goal_period, $event) || (ctx.newQuest.goal_period = $event);\n        return $event;\n      });\n      i0.ɵɵlistener(\"change\", function GroupDetailPage_Template_select_change_80_listener() {\n        return ctx.onGoalPeriodChange();\n      });\n      i0.ɵɵelementStart(81, \"option\", 45);\n      i0.ɵɵtext(82, \"Every Day\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(83, \"option\", 46);\n      i0.ɵɵtext(84, \"Specific days of the week\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(85, \"option\", 47);\n      i0.ɵɵtext(86, \"Specific days of the month\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(87, \"div\", 48)(88, \"label\");\n      i0.ɵɵtext(89, \"Select Days of Week\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(90, \"div\", 49);\n      i0.ɵɵtemplate(91, GroupDetailPage_div_91_Template, 4, 5, \"div\", 50);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(92, \"div\", 51)(93, \"label\");\n      i0.ɵɵtext(94, \"Select Days of Month\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(95, \"div\", 52);\n      i0.ɵɵtemplate(96, GroupDetailPage_div_96_Template, 4, 5, \"div\", 50);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(97, \"button\", 53);\n      i0.ɵɵtext(98, \"Create Quest\");\n      i0.ɵɵelementEnd()()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.joinedToday);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.joinedToday);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.joinedToday || ctx.isAdmin);\n      i0.ɵɵadvance();\n      i0.ɵɵstyleProp(\"display\", ctx.showAddQuestModal ? \"block\" : \"none\");\n      i0.ɵɵadvance(9);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newQuest.emoji);\n      i0.ɵɵadvance(2);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newQuest.name);\n      i0.ɵɵadvance(4);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newQuest.description);\n      i0.ɵɵadvance(4);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newQuest.quest_type);\n      i0.ɵɵadvance(8);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newQuest.category);\n      i0.ɵɵadvance(14);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newQuest.priority);\n      i0.ɵɵadvance(9);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newQuest.goal_value);\n      i0.ɵɵadvance();\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newQuest.goal_unit);\n      i0.ɵɵadvance(26);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newQuest.goal_period);\n      i0.ɵɵadvance(7);\n      i0.ɵɵstyleProp(\"display\", ctx.newQuest.goal_period === \"week\" ? \"block\" : \"none\");\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngForOf\", ctx.daysOfWeek);\n      i0.ɵɵadvance();\n      i0.ɵɵstyleProp(\"display\", ctx.newQuest.goal_period === \"month\" ? \"block\" : \"none\");\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngForOf\", ctx.daysOfMonth);\n    }\n  },\n  dependencies: [IonicModule, i1.IonRange, i1.NumericValueAccessor, i1.RouterLinkWithHrefDelegate, CommonModule, i2.NgForOf, i2.NgIf, FormsModule, i3.ɵNgNoValidate, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.DefaultValueAccessor, i3.NumberValueAccessor, i3.CheckboxControlValueAccessor, i3.SelectControlValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.MinValidator, i3.NgModel, i3.NgForm, RouterModule, i4.RouterLink, EmojiInputDirective, ComponentsModule, i5.GroupSideQuestComponent, GroupWaitingRoomComponent],\n  styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%];\\n\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\tvar __webpack_modules__ = ({\\n\\n\\n 666:\\n\\n\\n\\n\\n\\n (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/sass-loader/dist/cjs.js):\\\\nexpected \\\\\\\"{\\\\\\\".\\\\n    \\u2577\\\\n330 \\u2502  Special handling for selected days */\\\\r\\\\n    \\u2502                                       ^\\\\n    \\u2575\\\\n  src\\\\\\\\app\\\\\\\\pages\\\\\\\\groups\\\\\\\\group-detail\\\\\\\\group-detail.page.scss 330:39  root stylesheet\\\");\\n\\n\\n })\\n\\n\\n \\t});\\n\\n\\n\\n \\t\\n\\n \\t// startup\\n\\n \\t// Load entry module and return exports\\n\\n \\t// This entry module doesn't tell about it's top-level declarations so it can't be inlined\\n\\n \\tvar __webpack_exports__ = {};\\n\\n \\t__webpack_modules__[666]();\\n\\n \\tresource = __webpack_exports__;\\n\\n \\t\\n\\n })()\\n;\"]\n});", "map": {"version": 3, "names": ["inject", "CommonModule", "FormsModule", "IonicModule", "ToastController", "ActivatedRoute", "Router", "RouterModule", "NavigationEnd", "take", "combineLatest", "switchMap", "map", "of", "filter", "SupabaseService", "GroupService", "UserService", "GroupMember", "EmojiInputDirective", "ComponentsModule", "GroupWaitingRoomComponent", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r0", "groupId", "group", "name", "isAdmin", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "day_r3", "ɵɵclassProp", "date_r5", "completionPercentage", "ɵɵlistener", "GroupDetailPage_div_1_div_20_Template_div_click_0_listener", "ɵɵrestoreView", "_r4", "$implicit", "ɵɵnextContext", "ɵɵresetView", "selectDateWithUrl", "ɵɵtemplate", "GroupDetailPage_div_1_div_20__svg_svg_1_Template", "isToday", "isSelected", "isFuture", "isBeforeJoin", "day", "ɵɵelementContainerStart", "headerText", "ɵɵtwoWayListener", "GroupDetailPage_div_1_ng_container_32_div_1_div_9_Template_ion_range_ngModelChange_1_listener", "$event", "_r6", "quest_r7", "ɵɵtwoWayBindingSet", "value_achieved", "GroupDetailPage_div_1_ng_container_32_div_1_div_9_Template_ion_range_ionChange_1_listener", "updateQuestProgress", "ɵɵstyleMapInterpolate1", "goal_value", "ɵɵtwoWayProperty", "ɵɵtextInterpolate4", "goal_unit", "GroupDetailPage_div_1_ng_container_32_div_1_div_10_Template_ion_range_ngModelChange_1_listener", "_r8", "GroupDetailPage_div_1_ng_container_32_div_1_div_10_Template_ion_range_ionChange_1_listener", "ɵɵtextInterpolate2", "completed_members", "total_members", "ɵɵtextInterpolate1", "streak", "GroupDetailPage_div_1_ng_container_32_div_1_div_9_Template", "GroupDetailPage_div_1_ng_container_32_div_1_div_10_Template", "GroupDetailPage_div_1_ng_container_32_div_1_div_11_Template", "GroupDetailPage_div_1_ng_container_32_div_1_div_12_Template", "completed", "emoji", "description", "GroupDetailPage_div_1_ng_container_32_div_1_Template", "is_side_quest", "userId", "joinedDate", "enable_sidequests", "selectedDate", "GroupDetailPage_div_1_Template_button_click_13_listener", "_r2", "navigateWeekWithUrl", "GroupDetailPage_div_1_div_16_Template", "GroupDetailPage_div_1_Template_button_click_17_listener", "GroupDetailPage_div_1_div_20_Template", "GroupDetailPage_div_1_div_21_Template", "GroupDetailPage_div_1_ng_container_27_Template", "GroupDetailPage_div_1_ng_container_28_Template", "GroupDetailPage_div_1_ng_container_29_Template", "GroupDetailPage_div_1_ng_container_30_Template", "GroupDetailPage_div_1_ng_container_32_Template", "GroupDetailPage_div_1_app_group_sidequest_33_Template", "ɵɵpureFunction0", "_c0", "dayNames", "weekDates", "hasFutureDates", "isYesterday", "isTomorrow", "quests", "GroupDetailPage_div_2_a_3_Template_a_click_0_listener", "_r9", "openAddQuestModal", "GroupDetailPage_div_2_a_3_Template", "ɵɵpureFunction1", "_c1", "GroupDetailPage_div_91_Template_input_ngModelChange_1_listener", "day_r11", "_r10", "selected", "value", "toLowerCase", "label", "GroupDetailPage_div_96_Template_input_ngModelChange_1_listener", "day_r13", "_r12", "GroupDetailPage", "constructor", "members", "requiredXp", "weekOffset", "isBeforeJoinDate", "joinedToday", "dailyQuest", "memberStatus", "id", "showAddQuestModal", "newQuest", "quest_type", "category", "priority", "goal_period", "daysOfWeek", "daysOfMonth", "Array", "from", "length", "_", "i", "subscriptions", "supabaseService", "groupService", "userService", "route", "router", "toastController", "ngOnInit", "push", "currentUser$", "subscribe", "user", "paramMap", "pipe", "params", "get", "loadGroup", "loadMembers", "queryParams", "initializeCalendar", "navigate", "events", "event", "refreshGroupData", "loadQuestsForDate", "loadDailySideQuest", "ionViewWillEnter", "ionViewDidEnter", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "getGroup", "calculateRequiredXp", "getGroupMembers", "currentUserMember", "find", "m", "user_id", "is_admin", "joined_date", "Date", "toISOString", "split", "setHours", "today", "getTime", "membersWithProfiles", "membersCopy", "member", "group_id", "nickname", "getUserProfile", "profile", "getUserStats", "stats", "profile_picture", "undefined", "username", "total_xp", "completed_quests", "max_streak", "level", "getWeekOffset", "direction", "parseInt", "error", "isNaN", "formatDate", "startDate", "setDate", "getDate", "generateWeekDates", "updateHeaderText", "date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "currentDay", "getDay", "mondayOffset", "monday", "dates", "dateStr", "currentMember", "joinDate", "totalQuests", "completedQuests", "updateWeekDateProgress", "navigateWeek", "currentWeekOffset", "newWeekOffset", "firstDate", "relativeTo", "week_offset", "selectedDateExists", "some", "validDate", "selectDate", "_this", "_asyncToGenerator", "toast", "create", "message", "duration", "position", "color", "present", "queryParamsHandling", "d", "console", "log", "selectedDateObj", "todayObj", "selectedTime", "todayTime", "yesterday", "yesterdayObj", "yesterdayTime", "tomorrow", "tomorrowObj", "tomorrowTime", "toLocaleDateString", "getGroupQuests", "filteredQuests", "quest", "createdDate", "created", "questObservables", "getGroupQuestProgress", "progress", "getGroupQuestProgressForDate", "allProgress", "eligibleMembers", "completedMembers", "p", "obs", "processedQuests", "_this$group", "getGroupSideQuest", "sideQuest", "getGroupSideQuestPoolItem", "current_quest_id", "poolItem", "getGroupSideQuestMemberStatus", "status", "getGroupSideQuestMemberStatuses", "allStatuses", "eligibleMemberIds", "todayStr", "s", "includes", "member_id", "last_updated", "totalMembers", "current_quest", "_this2", "newValue", "detail", "target", "result", "toggleGroupQuestCompletion", "success", "preventDefault", "closeAddQuestModal", "goToSettings", "window", "open", "_this3", "onGoalPeriodChange", "createGroupQuest", "alert", "selectedDays", "task_days_of_week", "task_days_of_month", "join", "questData", "then", "questId", "catch", "toggleSideQuest", "updateGroupSideQuestMemberStatus", "createGroupSideQuestMemberStatus", "statusId", "getCategoryIcon", "getCategoryColor", "getTotalXP", "strength_xp", "money_xp", "health_xp", "knowledge_xp", "getCompletedQuests", "reduce", "total", "getProgressPercentage", "xp", "Math", "min", "round", "updateSliderBackground", "slider", "sliderElement", "slider<PERSON><PERSON><PERSON>", "minValue", "maxValue", "sliderQuestId", "HTMLInputElement", "getAttribute", "max", "HTMLElement", "tagName", "valueAttr", "minAttr", "maxAttr", "percentage", "style", "setProperty", "background", "selectors", "decls", "vars", "consts", "template", "GroupDetailPage_Template", "rf", "ctx", "GroupDetailPage_app_group_waiting_room_0_Template", "GroupDetailPage_div_1_Template", "GroupDetailPage_div_2_Template", "GroupDetailPage_Template_span_click_5_listener", "GroupDetailPage_Template_form_ngSubmit_9_listener", "GroupDetailPage_Template_input_ngModelChange_12_listener", "GroupDetailPage_Template_input_ngModelChange_14_listener", "GroupDetailPage_Template_textarea_ngModelChange_18_listener", "GroupDetailPage_Template_select_ngModelChange_22_listener", "GroupDetailPage_Template_select_ngModelChange_30_listener", "GroupDetailPage_Template_select_ngModelChange_44_listener", "GroupDetailPage_Template_input_ngModelChange_53_listener", "GroupDetailPage_Template_select_ngModelChange_54_listener", "GroupDetailPage_Template_select_ngModelChange_80_listener", "GroupDetailPage_Template_select_change_80_listener", "GroupDetailPage_div_91_Template", "GroupDetailPage_div_96_Template", "ɵɵstyleProp", "i1", "IonRange", "NumericValueAccessor", "RouterLinkWithHrefDelegate", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "ɵNgNoValidate", "NgSelectOption", "ɵNgSelectMultipleOption", "DefaultValueAccessor", "NumberValueAccessor", "CheckboxControlValueAccessor", "SelectControlValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "MinValidator", "NgModel", "NgForm", "i4", "RouterLink", "i5", "GroupSideQuestComponent", "styles"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\groups\\group-detail\\group-detail.page.ts", "C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\groups\\group-detail\\group-detail.page.html"], "sourcesContent": ["import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy, inject } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IonicModule, ViewWillEnter, ViewDidEnter, ToastController } from '@ionic/angular';\r\nimport { ActivatedRoute, Router, RouterModule, NavigationEnd } from '@angular/router';\r\nimport { Subscription, take, firstValueFrom, combineLatest, switchMap, map, of, Observable, filter } from 'rxjs';\r\n\r\nimport { SupabaseService } from '../../../services/supabase.service';\r\nimport { GroupService } from '../../../services/group.service';\r\nimport { UserService } from '../../../services/user.service';\r\nimport { XpService, EntityType } from '../../../services/xp.service';\r\nimport { Group, GroupMember } from '../../../models/supabase.models';\r\nimport { EmojiInputDirective } from '../../../directives/emoji-input.directive';\r\nimport { ComponentsModule } from '../../../components/components.module';\r\nimport { GroupWaitingRoomComponent } from '../../../components/group-waiting-room/group-waiting-room.component';\r\nimport { QuestType, QuestPeriod, QuestPriority, QuestCategory, QuestGoalUnit } from '../../../models/quest.model';\r\n\r\ninterface GroupWithOptionalId extends Omit<Group, 'id'> {\r\n  id?: string;\r\n}\r\n\r\ninterface WeekDate {\r\n  date: string;\r\n  day: number;\r\n  isToday: boolean;\r\n  isSelected: boolean;\r\n  isFuture: boolean;\r\n  isBeforeJoin: boolean;\r\n  completionPercentage?: number;\r\n  totalQuests?: number;\r\n  completedQuests?: number;\r\n}\r\n\r\ninterface Quest {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  emoji: string;\r\n  category: string;\r\n  quest_type: string;\r\n  goal_value: number;\r\n  goal_unit: string;\r\n  value_achieved: number;\r\n  completed: boolean;\r\n  streak: number;\r\n  is_side_quest: boolean;\r\n  completed_members: number;\r\n  total_members: number;\r\n  message?: string;\r\n}\r\n\r\ninterface DailyQuest {\r\n  id: string;\r\n  streak: number;\r\n  completed_members: number;\r\n  total_members: number;\r\n  current_quest: {\r\n    id: string;\r\n    name: string;\r\n    description: string;\r\n    emoji: string;\r\n    goal_value: number;\r\n    goal_unit: string;\r\n  };\r\n}\r\n\r\ninterface MemberStatus {\r\n  id: string;\r\n  completed: boolean;\r\n  value_achieved: number;\r\n}\r\n\r\ninterface MemberWithProfile extends GroupMember {\r\n  profile_picture?: string;\r\n  username?: string;\r\n  total_xp?: number;\r\n  completed_quests?: number;\r\n  max_streak?: number;\r\n}\r\n\r\ninterface GroupActivity {\r\n  id: string;\r\n  user_id: string;\r\n  user_nickname: string;\r\n  message: string;\r\n  icon: string;\r\n  color: string;\r\n  created_at: Date;\r\n}\r\n\r\ninterface SideQuest {\r\n  id: string;\r\n  title: string;\r\n  description: string;\r\n  category: string;\r\n  xp_reward: number;\r\n  completed: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-group-detail',\r\n  templateUrl: './group-detail.page.html',\r\n  styleUrls: ['./group-detail.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule, FormsModule, RouterModule, EmojiInputDirective, ComponentsModule, GroupWaitingRoomComponent]\r\n})\r\nexport class GroupDetailPage implements OnInit, OnDestroy, ViewWillEnter, ViewDidEnter {\r\n  userId: string | null = null;\r\n\r\n  groupId: string | null = null;\r\n  group: GroupWithOptionalId | null = null;\r\n  members: MemberWithProfile[] = [];\r\n  isAdmin = false;\r\n  requiredXp = 0;\r\n  joinedDate: string = '';\r\n\r\n  dayNames: string[] = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];\r\n  weekOffset: number = 0;\r\n  weekDates: WeekDate[] = [];\r\n  selectedDate: string = '';\r\n  headerText: string = '';\r\n  isBeforeJoinDate: boolean = false;\r\n  joinedToday: boolean = false;\r\n\r\n  quests: Quest[] = [];\r\n  dailyQuest: DailyQuest | null = null;\r\n  memberStatus: MemberStatus = { id: '', completed: false, value_achieved: 0 };\r\n\r\n  showAddQuestModal: boolean = false;\r\n  newQuest = {\r\n    emoji: '🎯',\r\n    name: '',\r\n    description: '',\r\n    quest_type: 'build' as QuestType,\r\n    category: '' as QuestCategory,\r\n    priority: 'basic' as QuestPriority,\r\n    goal_value: 1,\r\n    goal_unit: 'count' as QuestGoalUnit,\r\n    goal_period: 'day' as QuestPeriod\r\n  };\r\n\r\n  daysOfWeek = [\r\n    { value: 'Monday', label: 'M', selected: false },\r\n    { value: 'Tuesday', label: 'T', selected: false },\r\n    { value: 'Wednesday', label: 'W', selected: false },\r\n    { value: 'Thursday', label: 'T', selected: false },\r\n    { value: 'Friday', label: 'F', selected: false },\r\n    { value: 'Saturday', label: 'S', selected: false },\r\n    { value: 'Sunday', label: 'S', selected: false }\r\n  ];\r\n\r\n  daysOfMonth = Array.from({ length: 31 }, (_, i) => ({ value: i + 1, selected: false }));\r\n\r\n  private subscriptions: Subscription[] = [];\r\n\r\n  private supabaseService = inject(SupabaseService);\r\n  private groupService = inject(GroupService);\r\n  private userService = inject(UserService);\r\n  private route = inject(ActivatedRoute);\r\n  private router = inject(Router);\r\n  private toastController = inject(ToastController);\r\n\r\n  constructor() {}\r\n\r\n  ngOnInit() {\r\n    this.subscriptions.push(\r\n      this.supabaseService.currentUser$.subscribe(user => {\r\n        if (user) {\r\n          this.userId = user.id;\r\n\r\n          this.route.paramMap.pipe(take(1)).subscribe(params => {\r\n            this.groupId = params.get('id');\r\n            if (this.groupId) {\r\n              this.loadGroup();\r\n              this.loadMembers();\r\n\r\n              this.route.queryParams.pipe(take(1)).subscribe(queryParams => {\r\n                this.initializeCalendar();\r\n              });\r\n            }\r\n          });\r\n        } else {\r\n          this.router.navigate(['/login']);\r\n        }\r\n      })\r\n    );\r\n\r\n    this.subscriptions.push(\r\n      this.router.events.pipe(\r\n        filter(event => event instanceof NavigationEnd),\r\n        filter(() => !!this.groupId) \n      ).subscribe(() => {\r\n        this.refreshGroupData();\r\n      })\r\n    );\r\n  }\r\n\r\n  refreshGroupData() {\r\n    if (!this.groupId) return;\r\n\r\n    this.loadGroup();\r\n    this.loadMembers();\r\n\r\n    if (this.selectedDate) {\r\n      this.loadQuestsForDate(this.selectedDate);\r\n      this.loadDailySideQuest(this.selectedDate);\r\n    }\r\n  }\r\n\r\n  ionViewWillEnter() {\r\n    this.refreshGroupData();\r\n  }\r\n\r\n  ionViewDidEnter() {\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subscriptions.forEach(sub => sub.unsubscribe());\r\n  }\r\n\r\n  loadGroup() {\r\n    if (!this.groupId) return;\r\n\r\n    this.subscriptions.push(\r\n      this.groupService.getGroup(this.groupId).subscribe(group => {\r\n        if (group) {\r\n          this.group = group;\r\n          this.calculateRequiredXp();\r\n        } else {\r\n          this.router.navigate(['/groups']);\r\n        }\r\n      })\r\n    );\r\n  }\r\n\r\n  loadMembers() {\r\n    if (!this.groupId || !this.userId) return;\r\n\r\n    this.subscriptions.push(\r\n      this.groupService.getGroupMembers(this.groupId).subscribe(members => {\r\n        const currentUserMember = members.find(m => m.user_id === this.userId);\r\n        this.isAdmin = currentUserMember?.is_admin || false;\r\n\r\n        if (currentUserMember && currentUserMember.joined_date) {\r\n          this.joinedDate = new Date(currentUserMember.joined_date).toISOString().split('T')[0];\r\n\r\n          const joinedDate = new Date(currentUserMember.joined_date);\r\n          joinedDate.setHours(0, 0, 0, 0);\r\n\r\n          const today = new Date();\r\n          today.setHours(0, 0, 0, 0);\r\n\r\n          this.joinedToday = joinedDate.getTime() === today.getTime();\r\n        }\r\n\r\n        const membersWithProfiles: MemberWithProfile[] = [];\r\n\r\n        const membersCopy = members.map(member => ({\r\n          ...member,\r\n          id: member.id || '',\r\n          group_id: member.group_id || '',\r\n          user_id: member.user_id || '',\r\n          nickname: member.nickname || '',\r\n          is_admin: member.is_admin || false,\r\n          joined_date: member.joined_date || new Date()\r\n        }));\r\n\r\n        for (const member of membersCopy) {\r\n          this.userService.getUserProfile(member.user_id).pipe(take(1)).subscribe(profile => {\r\n            this.userService.getUserStats(member.user_id).pipe(take(1)).subscribe(stats => {\r\n              membersWithProfiles.push({\r\n                ...member,\r\n                profile_picture: profile?.profile_picture || undefined,\r\n                username: profile?.username,\r\n                total_xp: stats?.total_xp || 0,\r\n                completed_quests: stats?.completed_quests || 0,\r\n                max_streak: stats?.max_streak || 0\r\n              });\r\n\r\n              this.members = [...membersWithProfiles];\r\n            });\r\n          });\r\n        }\r\n      })\r\n    );\r\n  }\r\n\r\n  calculateRequiredXp() {\r\n    if (!this.group) return;\r\n\r\n    this.requiredXp = this.group.level * 1000;\r\n  }\r\n\r\n  getWeekOffset(direction: number): number {\r\n    return this.weekOffset + direction;\r\n  }\r\n\r\n  initializeCalendar() {\r\n\r\n    const today = new Date();\r\n\r\n    this.route.queryParams.subscribe(params => {\r\n\r\n      if (params['week_offset']) {\r\n        try {\r\n          this.weekOffset = parseInt(params['week_offset']);\r\n        } catch (error) {\r\n          this.weekOffset = 0;\r\n        }\r\n      } else {\r\n        this.weekOffset = 0;\r\n      }\r\n\r\n      let selectedDate = today;\r\n      if (params['date']) {\r\n        try {\r\n          selectedDate = new Date(params['date']);\r\n\r\n          if (isNaN(selectedDate.getTime())) {\r\n            selectedDate = today;\r\n          }\r\n        } catch (error) {\r\n          selectedDate = today;\r\n        }\r\n      }\r\n\r\n      this.selectedDate = this.formatDate(selectedDate);\r\n\r\n      const startDate = new Date(today);\r\n      startDate.setDate(today.getDate() + (this.weekOffset * 7));\r\n\r\n      this.generateWeekDates(startDate);\r\n\r\n      this.updateHeaderText();\r\n\r\n      this.loadQuestsForDate(this.selectedDate);\r\n      this.loadDailySideQuest(this.selectedDate);\r\n    });\r\n  }\r\n\r\n  formatDate(date: Date): string {\r\n    const year = date.getFullYear();\r\n    const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n    return `${year}-${month}-${day}`;\r\n  }\r\n\r\n  generateWeekDates(startDate: Date) {\r\n    const today = new Date();\r\n    today.setHours(0, 0, 0, 0);\r\n\r\n    const currentDay = startDate.getDay(); \n    const mondayOffset = currentDay === 0 ? -6 : 1; \n    const monday = new Date(startDate);\r\n    monday.setDate(startDate.getDate() - currentDay + mondayOffset);\r\n\r\n\r\n    const dates: WeekDate[] = [];\r\n\r\n    for (let i = 0; i < 7; i++) {\r\n      const date = new Date(monday);\r\n      date.setDate(monday.getDate() + i);\r\n\r\n      const dateStr = this.formatDate(date);\r\n      date.setHours(0, 0, 0, 0);\r\n      const isToday = date.getTime() === today.getTime();\r\n\r\n      const isFuture = date.getTime() > today.getTime();\r\n\r\n      let isBeforeJoin = false;\r\n      if (this.members.length > 0 && this.userId) {\r\n        const currentMember = this.members.find(m => m.user_id === this.userId);\r\n        if (currentMember && currentMember.joined_date) {\r\n          const joinDate = new Date(currentMember.joined_date);\r\n          joinDate.setHours(0, 0, 0, 0);\r\n          isBeforeJoin = date < joinDate;\r\n        }\r\n      }\r\n\r\n      dates.push({\r\n        date: dateStr,\r\n        day: date.getDate(),\r\n        isToday,\r\n        isSelected: dateStr === this.selectedDate,\r\n        isFuture,\r\n        isBeforeJoin,\r\n        completionPercentage: 0,\r\n        totalQuests: 0,\r\n        completedQuests: 0\r\n      });\r\n    }\r\n\r\n    this.weekDates = dates;\r\n\r\n    this.updateWeekDateProgress();\r\n  }\r\n\r\n  navigateWeek(direction: number) {\r\n\r\n    this.route.queryParams.pipe(take(1)).subscribe(params => {\r\n      let currentWeekOffset = 0;\r\n      if (params['week_offset']) {\r\n        try {\r\n          currentWeekOffset = parseInt(params['week_offset']);\r\n        } catch (error) {\r\n        }\r\n      }\r\n\r\n      const newWeekOffset = currentWeekOffset + direction;\r\n\r\n      const firstDate = new Date(this.weekDates[0].date);\r\n\r\n      firstDate.setDate(firstDate.getDate() + (direction * 7));\r\n\r\n      this.router.navigate([], {\r\n        relativeTo: this.route,\r\n        queryParams: {\r\n          week_offset: newWeekOffset,\r\n          date: this.selectedDate \n        }\r\n      });\r\n\r\n      this.generateWeekDates(firstDate);\r\n\r\n      const selectedDateExists = this.weekDates.some(date => date.date === this.selectedDate);\r\n      if (!selectedDateExists) {\r\n        const validDate = this.weekDates.find(date => !date.isFuture && !date.isBeforeJoin);\r\n        if (validDate) {\r\n          this.selectDate(validDate);\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  async selectDate(date: WeekDate) {\r\n\r\n    if (date.isFuture) {\r\n\r\n      const toast = await this.toastController.create({\r\n        message: 'Cannot select future dates',\r\n        duration: 2000,\r\n        position: 'bottom',\r\n        color: 'warning'\r\n      });\r\n      await toast.present();\r\n\r\n      return;\r\n    }\r\n\r\n    if (date.isBeforeJoin) {\r\n      this.isBeforeJoinDate = true;\r\n\r\n      const toast = await this.toastController.create({\r\n        message: 'You cannot access dates before you joined the group',\r\n        duration: 2000,\r\n        position: 'bottom',\r\n        color: 'warning'\r\n      });\r\n      await toast.present();\r\n\r\n      return;\r\n    } else {\r\n      this.isBeforeJoinDate = false;\r\n    }\r\n\r\n    this.router.navigate([], {\r\n      relativeTo: this.route,\r\n      queryParams: { date: date.date },\r\n      queryParamsHandling: 'merge' \n    });\r\n\r\n    this.weekDates.forEach(d => d.isSelected = d.date === date.date);\r\n    this.selectedDate = date.date;\r\n\r\n\r\n    this.updateHeaderText();\r\n\r\n    this.loadQuestsForDate(date.date);\r\n    this.loadDailySideQuest(date.date);\r\n  }\r\n\r\n  updateHeaderText() {\r\n    if (!this.selectedDate) {\r\n      this.headerText = 'Today';\r\n      return;\r\n    }\r\n\r\n    const date = new Date(this.selectedDate);\r\n    const today = new Date();\r\n\r\n    date.setHours(0, 0, 0, 0);\r\n    today.setHours(0, 0, 0, 0);\r\n\r\n    console.log('GroupDetailPage: Comparing dates:', {\r\n      selectedDate: this.selectedDate,\r\n      selectedDateObj: date.toISOString(),\r\n      todayObj: today.toISOString(),\r\n      selectedTime: date.getTime(),\r\n      todayTime: today.getTime(),\r\n      isToday: date.getTime() === today.getTime()\r\n    });\r\n\r\n    if (date.getTime() === today.getTime()) {\r\n      this.headerText = 'Today';\r\n    } else {\r\n      const yesterday = new Date(today);\r\n      yesterday.setDate(yesterday.getDate() - 1);\r\n\r\n      console.log('GroupDetailPage: Checking if yesterday:', {\r\n        yesterdayObj: yesterday.toISOString(),\r\n        yesterdayTime: yesterday.getTime(),\r\n        isYesterday: date.getTime() === yesterday.getTime()\r\n      });\r\n\r\n      if (date.getTime() === yesterday.getTime()) {\r\n        this.headerText = 'Yesterday';\r\n      } else {\r\n        const tomorrow = new Date(today);\r\n        tomorrow.setDate(tomorrow.getDate() + 1);\r\n\r\n        console.log('GroupDetailPage: Checking if tomorrow:', {\r\n          tomorrowObj: tomorrow.toISOString(),\r\n          tomorrowTime: tomorrow.getTime(),\r\n          isTomorrow: date.getTime() === tomorrow.getTime()\r\n        });\r\n\r\n        if (date.getTime() === tomorrow.getTime()) {\r\n          this.headerText = 'Tomorrow';\r\n        } else {\r\n          this.headerText = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });\r\n        }\r\n      }\r\n    }\r\n\r\n  }\r\n\r\n  hasFutureDates(): boolean {\r\n    return this.weekDates.some(date => date.isFuture);\r\n  }\r\n\r\n  isToday(): boolean {\r\n    if (!this.selectedDate) return false;\r\n\r\n    const date = new Date(this.selectedDate);\r\n    const today = new Date();\r\n\r\n    date.setHours(0, 0, 0, 0);\r\n    today.setHours(0, 0, 0, 0);\r\n\r\n    return date.getTime() === today.getTime();\r\n  }\r\n\r\n  isYesterday(): boolean {\r\n    if (!this.selectedDate) return false;\r\n\r\n    const date = new Date(this.selectedDate);\r\n    const today = new Date();\r\n\r\n    date.setHours(0, 0, 0, 0);\r\n    today.setHours(0, 0, 0, 0);\r\n\r\n    const yesterday = new Date(today);\r\n    yesterday.setDate(yesterday.getDate() - 1);\r\n\r\n    return date.getTime() === yesterday.getTime();\r\n  }\r\n\r\n  isTomorrow(): boolean {\r\n    if (!this.selectedDate) return false;\r\n\r\n    const date = new Date(this.selectedDate);\r\n    const today = new Date();\r\n\r\n    date.setHours(0, 0, 0, 0);\r\n    today.setHours(0, 0, 0, 0);\r\n\r\n    const tomorrow = new Date(today);\r\n    tomorrow.setDate(tomorrow.getDate() + 1);\r\n\r\n    return date.getTime() === tomorrow.getTime();\r\n  }\r\n\r\n  updateWeekDateProgress() {\r\n    this.weekDates.forEach(date => {\r\n      if (date.isFuture || date.isBeforeJoin) {\r\n        date.completionPercentage = 0;\r\n        date.totalQuests = 0;\r\n        date.completedQuests = 0;\r\n        return;\r\n      }\r\n\r\n      date.totalQuests = 0;\r\n      date.completedQuests = 0;\r\n      date.completionPercentage = 0;\r\n\r\n    });\r\n  }\r\n\r\n  loadQuestsForDate(dateStr: string) {\r\n    if (!this.groupId || !this.userId) return;\r\n\r\n\r\n    this.subscriptions.push(\r\n      this.groupService.getGroupQuests(this.groupId).subscribe(\r\n        quests => {\r\n          if (quests && quests.length > 0) {\r\n\r\n            const selectedDate = new Date(dateStr);\r\n            const filteredQuests = quests.filter(quest => {\r\n              const createdDate = new Date(quest.created);\r\n              createdDate.setHours(0, 0, 0, 0);\r\n              selectedDate.setHours(0, 0, 0, 0);\r\n              return createdDate <= selectedDate;\r\n            });\r\n\r\n\r\n            const questObservables = filteredQuests.map(quest => {\r\n              if (!quest.id || !this.userId) {\r\n                return of(null);\r\n              }\r\n\r\n              return this.groupService.getGroupQuestProgress(quest.id, this.userId, new Date(dateStr)).pipe(\r\n                switchMap(progress => {\r\n                  if (!quest.id) {\r\n                    return of({} as Quest);\r\n                  }\r\n                  return this.groupService.getGroupQuestProgressForDate(quest.id, new Date(dateStr)).pipe(\r\n                    map(allProgress => {\r\n                      const selectedDate = new Date(dateStr);\r\n                      selectedDate.setHours(0, 0, 0, 0); \n\r\n                      const eligibleMembers = this.members.filter(member => {\r\n                        if (!member.joined_date) return false;\r\n                        const joinDate = new Date(member.joined_date);\r\n                        joinDate.setHours(0, 0, 0, 0);\r\n                        return joinDate < selectedDate; \n                      });\r\n\r\n                      const completedMembers = allProgress.filter(p =>\r\n                        p.completed &&\r\n                        eligibleMembers.some(m => m.user_id === p.user_id)\r\n                      ).length;\r\n\r\n\r\n                      let message = '';\r\n\r\n                      return {\r\n                        id: quest.id,\r\n                        name: quest.name,\r\n                        description: quest.description || '',\r\n                        emoji: quest.emoji,\r\n                        category: quest.category,\r\n                        quest_type: quest.quest_type,\r\n                        goal_value: quest.goal_value,\r\n                        goal_unit: quest.goal_unit,\r\n                        value_achieved: progress ? progress.value_achieved : 0,\r\n                        completed: progress ? progress.completed : false,\r\n                        streak: quest.streak,\r\n                        is_side_quest: false,\r\n                        completed_members: completedMembers,\r\n                        total_members: eligibleMembers.length,\r\n                        message: message\r\n                      } as Quest;\r\n                    })\r\n                  );\r\n                })\r\n              );\r\n            }).filter(obs => obs !== null) as Observable<Quest>[];\r\n\r\n            if (questObservables.length > 0) {\r\n              combineLatest(questObservables).subscribe(\r\n                processedQuests => {\r\n                  this.quests = processedQuests;\r\n                },\r\n                error => {\r\n                  this.quests = [];\r\n                }\r\n              );\r\n            } else {\r\n              this.quests = [];\r\n            }\r\n          } else {\r\n            this.quests = [];\r\n          }\r\n        },\r\n        error => {\r\n          this.quests = [];\r\n        }\r\n      )\r\n    );\r\n  }\r\n\r\n  loadDailySideQuest(dateStr: string) {\r\n    if (!this.groupId || !this.userId) return;\r\n\r\n\r\n    if (!this.group?.enable_sidequests) {\r\n      this.dailyQuest = null;\r\n      return;\r\n    }\r\n\r\n    const today = new Date();\r\n    today.setHours(0, 0, 0, 0);\r\n    const selectedDate = new Date(dateStr);\r\n    selectedDate.setHours(0, 0, 0, 0);\r\n\r\n    if (selectedDate.getTime() !== today.getTime()) {\r\n      this.dailyQuest = null;\r\n      return;\r\n    }\r\n\r\n    this.subscriptions.push(\r\n      this.groupService.getGroupSideQuest(this.groupId).subscribe(\r\n        sideQuest => {\r\n          if (sideQuest) {\r\n\r\n            this.groupService.getGroupSideQuestPoolItem(sideQuest.current_quest_id).subscribe(\r\n              poolItem => {\r\n                if (poolItem) {\r\n\r\n                  if (!sideQuest.id || !this.userId) {\r\n                    return;\r\n                  }\r\n\r\n                  this.groupService.getGroupSideQuestMemberStatus(sideQuest.id, this.userId).subscribe(\r\n                    status => {\r\n\r\n                      if (!sideQuest.id) {\r\n                        return;\r\n                      }\r\n                      this.groupService.getGroupSideQuestMemberStatuses(sideQuest.id).subscribe(\r\n                        allStatuses => {\r\n                          const today = new Date();\r\n                          today.setHours(0, 0, 0, 0); \n\r\n                          const eligibleMembers = this.members.filter(member => {\r\n                            if (!member.joined_date) return false;\r\n                            const joinDate = new Date(member.joined_date);\r\n                            joinDate.setHours(0, 0, 0, 0);\r\n                            return joinDate < today; \n                          });\r\n\r\n                          const eligibleMemberIds = eligibleMembers.map(m => m.user_id);\r\n\r\n                          const todayStr = new Date().toISOString().split('T')[0];\r\n\r\n                          const completedMembers = allStatuses.filter(s =>\r\n                            s.completed && \n                            eligibleMemberIds.includes(s.member_id) && \n                            (typeof s.last_updated === 'string' && s.last_updated === todayStr) \n                          ).length;\r\n\r\n                          const totalMembers = eligibleMembers.length;\r\n\r\n                          if (!poolItem.id) {\r\n                            return;\r\n                          }\r\n\r\n                          this.dailyQuest = {\r\n                            id: sideQuest.id || '',\r\n                            streak: sideQuest.streak,\r\n                            completed_members: completedMembers,\r\n                            total_members: totalMembers,\r\n                            current_quest: {\r\n                              id: poolItem.id || '',\r\n                              name: poolItem.name,\r\n                              description: poolItem.description || '',\r\n                              emoji: poolItem.emoji,\r\n                              goal_value: poolItem.goal_value,\r\n                              goal_unit: poolItem.goal_unit\r\n                            }\r\n                          };\r\n\r\n                          this.memberStatus = status ? {\r\n                            id: status.id || '',\r\n                            completed: status.completed,\r\n                            value_achieved: status.value_achieved\r\n                          } : {\r\n                            id: '',\r\n                            completed: false,\r\n                            value_achieved: 0\r\n                          };\r\n                        },\r\n                        error => {\r\n                        }\r\n                      );\r\n                    },\r\n                    error => {\r\n                      this.memberStatus = {\r\n                        id: '',\r\n                        completed: false,\r\n                        value_achieved: 0\r\n                      };\r\n                    }\r\n                  );\r\n                }\r\n              },\r\n              error => {\r\n              }\r\n            );\r\n          } else {\r\n            this.dailyQuest = null;\r\n          }\r\n        },\r\n        error => {\r\n          this.dailyQuest = null;\r\n        }\r\n      )\r\n    );\r\n  }\r\n\r\n  async updateQuestProgress(quest: Quest, event: any) {\r\n    if (!this.groupId || !this.userId) return;\r\n\r\n    const newValue = event.detail ? event.detail.value : parseInt(event.target.value);\r\n\r\n    if (this.isBeforeJoinDate) {\r\n      quest.message = 'You can participate in group quests starting tomorrow after joining the group.';\r\n      quest.value_achieved = quest.value_achieved; \n      return;\r\n    }\r\n\r\n\r\n    const selectedDate = this.selectedDate ? new Date(this.selectedDate) : new Date();\r\n\r\n    try {\r\n      const result = await this.groupService.toggleGroupQuestCompletion(\r\n        quest.id,\r\n        this.userId,\r\n        selectedDate,\r\n        newValue\r\n      );\r\n\r\n      if (result.success) {\r\n\r\n        quest.value_achieved = newValue;\r\n\r\n        if (quest.quest_type === 'build') {\r\n          quest.completed = newValue >= quest.goal_value;\r\n        } else { \n          quest.completed = newValue < quest.goal_value;\r\n        }\r\n\r\n        this.loadQuestsForDate(this.selectedDate);\r\n      } else {\r\n        quest.message = result.message;\r\n\r\n        quest.value_achieved = quest.value_achieved; \n      }\r\n    } catch (error) {\r\n\r\n      quest.value_achieved = quest.value_achieved; \n\r\n      quest.message = 'An error occurred while updating quest progress.';\r\n    }\r\n  }\r\n\r\n  openAddQuestModal(event: Event) {\r\n    event.preventDefault();\r\n\r\n    this.newQuest = {\r\n      emoji: '🎯',\r\n      name: '',\r\n      description: '',\r\n      quest_type: 'build' as QuestType,\r\n      category: '' as QuestCategory,\r\n      priority: 'basic' as QuestPriority,\r\n      goal_value: 1,\r\n      goal_unit: 'count' as QuestGoalUnit,\r\n      goal_period: 'day' as QuestPeriod\r\n    };\r\n\r\n    this.daysOfWeek.forEach(day => day.selected = false);\r\n    this.daysOfMonth.forEach(day => day.selected = false);\r\n\r\n    this.showAddQuestModal = true;\r\n  }\r\n\r\n  closeAddQuestModal() {\r\n    this.showAddQuestModal = false;\r\n  }\r\n\r\n  goToSettings() {\r\n    if (this.groupId) {\r\n      window.open(`/groups/${this.groupId}/settings`, '_blank');\r\n    }\r\n  }\r\n\r\n  navigateWeekWithUrl(direction: number) {\r\n\r\n    const newWeekOffset = this.weekOffset + direction;\r\n    this.weekOffset = newWeekOffset;\r\n\r\n    this.router.navigate([], {\r\n      relativeTo: this.route,\r\n      queryParams: {\r\n        week_offset: newWeekOffset,\r\n        date: this.selectedDate \n      },\r\n      queryParamsHandling: 'merge'\r\n    });\r\n\r\n    const today = new Date();\r\n    today.setHours(0, 0, 0, 0);\r\n\r\n    const startDate = new Date(today);\r\n    startDate.setDate(today.getDate() + (this.weekOffset * 7));\r\n\r\n    this.generateWeekDates(startDate);\r\n\r\n    const selectedDateObj = this.weekDates.find(d => d.date === this.selectedDate);\r\n    if (selectedDateObj && (selectedDateObj.isFuture || selectedDateObj.isBeforeJoin)) {\r\n      const validDate = this.weekDates.find(date => !date.isFuture && !date.isBeforeJoin);\r\n      if (validDate) {\r\n        this.selectDate(validDate);\r\n      }\r\n    }\r\n  }\r\n\r\n  async selectDateWithUrl(date: WeekDate) {\r\n\r\n    if (date.isFuture) {\r\n\r\n      const toast = await this.toastController.create({\r\n        message: 'Cannot select future dates',\r\n        duration: 2000,\r\n        position: 'bottom',\r\n        color: 'warning'\r\n      });\r\n      await toast.present();\r\n\r\n      return;\r\n    }\r\n\r\n    if (date.isBeforeJoin) {\r\n\r\n      const toast = await this.toastController.create({\r\n        message: 'You cannot access dates before you joined the group',\r\n        duration: 2000,\r\n        position: 'bottom',\r\n        color: 'warning'\r\n      });\r\n      await toast.present();\r\n\r\n      return;\r\n    }\r\n\r\n    this.router.navigate([], {\r\n      relativeTo: this.route,\r\n      queryParams: { date: date.date },\r\n      queryParamsHandling: 'merge' \n    });\r\n\r\n    this.weekDates.forEach(d => d.isSelected = d.date === date.date);\r\n    this.selectedDate = date.date;\r\n\r\n\r\n    this.updateHeaderText();\r\n\r\n    this.loadQuestsForDate(date.date);\r\n    this.loadDailySideQuest(date.date);\r\n  }\r\n\r\n  onGoalPeriodChange() {\r\n    if (this.newQuest.goal_period === 'week') {\r\n      this.daysOfWeek.forEach(day => day.selected = false);\r\n    } else if (this.newQuest.goal_period === 'month') {\r\n      this.daysOfMonth.forEach(day => day.selected = false);\r\n    }\r\n  }\r\n\r\n  createGroupQuest() {\r\n    if (!this.groupId || !this.userId) return;\r\n\r\n    if (!this.newQuest.name || !this.newQuest.category || !this.newQuest.priority) {\r\n      alert('Please fill in all required fields');\r\n      return;\r\n    }\r\n\r\n    let selectedDays: any[] = [];\r\n    let task_days_of_week: string | undefined = undefined;\r\n    let task_days_of_month: string | undefined = undefined;\r\n\r\n    if (this.newQuest.goal_period === 'week') {\r\n      selectedDays = this.daysOfWeek.filter(day => day.selected).map(day => day.value);\r\n      task_days_of_week = selectedDays.join(',');\r\n    } else if (this.newQuest.goal_period === 'month') {\r\n      selectedDays = this.daysOfMonth.filter(day => day.selected).map(day => day.value);\r\n      task_days_of_month = selectedDays.join(',');\r\n    }\r\n\r\n    const questData = {\r\n      ...this.newQuest,\r\n      group_id: this.groupId as string,\r\n      task_days_of_week,\r\n      task_days_of_month\r\n    };\r\n\r\n\r\n    this.groupService.createGroupQuest(questData)\r\n      .then(questId => {\r\n        this.loadQuestsForDate(this.selectedDate);\r\n      })\r\n      .catch(error => {\r\n        alert('Failed to create quest: ' + error.message);\r\n      });\r\n\r\n    this.closeAddQuestModal();\r\n    this.newQuest = {\r\n      emoji: '🎯',\r\n      name: '',\r\n      description: '',\r\n      quest_type: 'build' as QuestType,\r\n      category: '' as QuestCategory,\r\n      priority: 'basic' as QuestPriority,\r\n      goal_value: 1,\r\n      goal_unit: 'count' as QuestGoalUnit,\r\n      goal_period: 'day' as QuestPeriod\r\n    };\r\n  }\r\n\r\n  toggleSideQuest() {\r\n    if (!this.groupId || !this.userId || !this.dailyQuest || this.isBeforeJoinDate) return;\r\n\r\n    if (this.memberStatus.completed) {\r\n      this.memberStatus.value_achieved = 0;\r\n      this.memberStatus.completed = false;\r\n    } else {\r\n      this.memberStatus.value_achieved = this.dailyQuest.current_quest.goal_value;\r\n      this.memberStatus.completed = true;\r\n    }\r\n\r\n\r\n    if (this.memberStatus.id) {\r\n      this.groupService.updateGroupSideQuestMemberStatus(\r\n        this.memberStatus.id,\r\n        this.memberStatus.completed,\r\n        this.memberStatus.value_achieved\r\n      ).then(() => {\r\n\r\n        this.loadDailySideQuest(this.selectedDate);\r\n      }).catch(error => {\r\n      });\r\n    } else {\r\n      this.groupService.createGroupSideQuestMemberStatus(\r\n        this.dailyQuest.id,\r\n        this.userId,\r\n        this.memberStatus.completed,\r\n        this.memberStatus.value_achieved\r\n      ).then((statusId) => {\r\n        this.memberStatus.id = statusId;\r\n\r\n        this.loadDailySideQuest(this.selectedDate);\r\n      }).catch(error => {\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n  getCategoryIcon(category: string): string {\r\n    switch (category.toLowerCase()) {\r\n      case 'strength': return '💪';\r\n      case 'money': return '💰';\r\n      case 'health': return '❤️';\r\n      case 'knowledge': return '🧠';\r\n      default: return '🎯';\r\n    }\r\n  }\r\n\r\n  getCategoryColor(category: string): string {\r\n    switch (category.toLowerCase()) {\r\n      case 'strength': return '#FF9500';\r\n      case 'money': return '#30D158';\r\n      case 'health': return '#FF2D55';\r\n      case 'knowledge': return '#5E5CE6';\r\n      default: return '#4D7BFF';\r\n    }\r\n  }\r\n\r\n  getTotalXP(): number {\r\n    if (!this.group) return 0;\r\n\r\n    return this.group.strength_xp +\r\n           this.group.money_xp +\r\n           this.group.health_xp +\r\n           this.group.knowledge_xp;\r\n  }\r\n\r\n  getCompletedQuests(): number {\r\n    return this.members.reduce((total, member) => total + (member.completed_quests || 0), 0);\r\n  }\r\n\r\n  getProgressPercentage(xp: number): number {\r\n    if (this.requiredXp <= 0) return 0;\r\n    return Math.min(100, Math.round((xp / this.requiredXp) * 100));\r\n  }\r\n\r\n  updateSliderBackground(slider: HTMLInputElement | EventTarget | null) {\r\n    if (!slider) {\r\n      return;\r\n    }\r\n\r\n    let sliderElement: HTMLElement;\r\n    let sliderValue = 0;\r\n    let minValue = 0;\r\n    let maxValue = 100;\r\n    let sliderQuestId = '';\r\n\r\n    if (slider instanceof HTMLInputElement) {\r\n      sliderElement = slider;\r\n      sliderQuestId = slider.getAttribute('data-quest-id') || '';\r\n      sliderValue = parseInt(slider.value);\r\n      minValue = parseInt(slider.min);\r\n      maxValue = parseInt(slider.max);\r\n    } else if (slider instanceof HTMLElement && slider.tagName === 'ION-RANGE') {\r\n      sliderElement = slider;\r\n      sliderQuestId = slider.getAttribute('data-quest-id') || '';\r\n\r\n      const valueAttr = slider.getAttribute('value') || '0';\r\n      const minAttr = slider.getAttribute('min') || '0';\r\n      const maxAttr = slider.getAttribute('max') || '100';\r\n\r\n      sliderValue = parseInt(valueAttr);\r\n      minValue = parseInt(minAttr);\r\n      maxValue = parseInt(maxAttr);\r\n    } else {\r\n      return;\r\n    }\r\n\r\n    if (!sliderQuestId) {\r\n      return;\r\n    }\r\n\r\n    const percentage = maxValue > minValue ?\r\n      ((sliderValue - minValue) / (maxValue - minValue)) * 100 : 0;\r\n\r\n    if (sliderElement.tagName === 'ION-RANGE') {\r\n      sliderElement.style.setProperty('--progress-value', `${percentage}%`);\r\n    } else {\r\n      sliderElement.style.background =\r\n        `linear-gradient(to right, #4169E1 0%, #4169E1 ${percentage}%, #2C2C2E ${percentage}%, #2C2C2E 100%)`;\r\n    }\r\n  }\r\n}\r\n", "<!-- Waiting Room for users who joined today -->\r\n<app-group-waiting-room\r\n  *ngIf=\"joinedToday\"\r\n  [groupId]=\"groupId || ''\"\r\n  [groupName]=\"group?.name || 'Group'\"\r\n  [isAdmin]=\"isAdmin\">\r\n</app-group-waiting-room>\r\n\r\n<!-- Regular Group Detail View for users who joined before today -->\r\n<div class=\"container\" *ngIf=\"!joinedToday\">\r\n  <header>\r\n    <div class=\"logo-wrap\">\r\n      <div class=\"logo\">\r\n        <img src=\"assets/images/upshift_icon_mini.svg\" alt=\"Upshift\">\r\n        <span>Upshift</span>\r\n      </div>\r\n    </div>\r\n    <h1>{{ group?.emoji }} {{ group?.name }}</h1>\r\n  </header>\r\n  <a [routerLink]=\"['/groups']\" class=\"back-link\">&larr; Back to groups</a>\r\n\r\n  <div class=\"week-calendar\">\r\n    <div class=\"calendar-nav\">\r\n      <button class=\"nav-arrow prev\" (click)=\"navigateWeekWithUrl(-1)\">←</button>\r\n      <div class=\"days\">\r\n        <div class=\"day-name\" *ngFor=\"let day of dayNames\">{{ day }}</div>\r\n      </div>\r\n      <button class=\"nav-arrow next\" (click)=\"navigateWeekWithUrl(1)\">→</button>\r\n    </div>\r\n    <div class=\"dates\">\r\n      <div *ngFor=\"let date of weekDates\"\r\n           class=\"date\"\r\n           [class.active]=\"date.isToday\"\r\n           [class.selected]=\"date.isSelected\"\r\n           [class.disabled]=\"date.isFuture || date.isBeforeJoin\"\r\n           [attr.isBeforeJoin]=\"date.isBeforeJoin\"\r\n           [title]=\"date.isBeforeJoin ? 'You cannot access dates before you joined the group' : (date.isFuture ? 'Cannot select future dates' : '')\"\r\n           (click)=\"selectDateWithUrl(date)\">\r\n        <svg class=\"date-progress\" viewBox=\"0 0 36 36\" *ngIf=\"date.completionPercentage && date.completionPercentage > 0 && !date.isFuture && !date.isBeforeJoin\">\r\n          <circle cx=\"18\" cy=\"18\" r=\"15.5\"\r\n                  [attr.stroke-dasharray]=\"(date.completionPercentage || 0) + ', 100'\"\r\n                  [attr.data-date]=\"date.date\"\r\n                  class=\"progress-circle\"\r\n                  [class.low]=\"date.completionPercentage && date.completionPercentage < 50\"></circle>\r\n        </svg>\r\n        <span class=\"date-content\">{{ date.day }}</span>\r\n      </div>\r\n    </div>\r\n    <div class=\"future-date-label\" *ngIf=\"hasFutureDates()\">Future date</div>\r\n  </div>\r\n\r\n  <section class=\"quests\">\r\n    <div style=\"display: flex; justify-content: space-between;\">\r\n      <h2>Quests</h2>\r\n      <h2>\r\n        <ng-container *ngIf=\"isToday()\">Today</ng-container>\r\n        <ng-container *ngIf=\"isYesterday()\">Yesterday</ng-container>\r\n        <ng-container *ngIf=\"isTomorrow()\">Tomorrow</ng-container>\r\n        <ng-container *ngIf=\"!isToday() && !isYesterday() && !isTomorrow()\">{{ headerText }}</ng-container>\r\n      </h2>\r\n    </div>\r\n    <div class=\"quest-list\">\r\n      <ng-container *ngFor=\"let quest of quests\">\r\n        <div *ngIf=\"!quest.is_side_quest\"\r\n             class=\"quest-item\"\r\n             [class.completed]=\"quest.completed\"\r\n             [attr.data-quest-id]=\"quest.id\"\r\n             [attr.data-group-quest]=\"true\"\r\n             [attr.data-is-admin]=\"isAdmin\">\r\n        <div class=\"quest-icon\">\r\n          {{ quest.emoji }}\r\n        </div>\r\n        <div class=\"quest-info\">\r\n          <h3>{{ quest.name }}</h3>\r\n          <p>{{ quest.description }}</p>\r\n          <div class=\"progress-container\">\r\n            <div class=\"progress-time\" *ngIf=\"quest.goal_unit === 'time' || quest.goal_unit === 'min' || quest.goal_unit === 'hr' || quest.goal_unit === 'sec'\">\r\n              <ion-range\r\n                min=\"0\"\r\n                [max]=\"quest.goal_value\"\r\n                [(ngModel)]=\"quest.value_achieved\"\r\n                class=\"progress-slider\"\r\n                [attr.data-quest-id]=\"quest.id\"\r\n                [attr.data-quest-type]=\"quest.quest_type\"\r\n                [step]=\"1\"\r\n                snaps=\"true\"\r\n                ticks=\"false\"\r\n                snaps-per-step=\"true\"\r\n                (ionChange)=\"updateQuestProgress(quest, $event)\"\r\n                style=\"--progress-value: {{quest.value_achieved / quest.goal_value * 100}}%\">\r\n              </ion-range>\r\n              <div class=\"progress-text\">\r\n                {{ quest.value_achieved }}{{ quest.goal_unit === 'min' ? 'm' : quest.goal_unit === 'hr' ? 'h' : 's' }}/{{ quest.goal_value }}{{ quest.goal_unit === 'min' ? 'm' : quest.goal_unit === 'hr' ? 'h' : 's' }}\r\n              </div>\r\n            </div>\r\n            <div class=\"progress\" *ngIf=\"quest.goal_unit !== 'time' && quest.goal_unit !== 'min' && quest.goal_unit !== 'hr' && quest.goal_unit !== 'sec'\">\r\n              <ion-range\r\n                min=\"0\"\r\n                [max]=\"quest.goal_value\"\r\n                [(ngModel)]=\"quest.value_achieved\"\r\n                class=\"progress-slider\"\r\n                [attr.data-quest-id]=\"quest.id\"\r\n                [attr.data-quest-type]=\"quest.quest_type\"\r\n                [step]=\"1\"\r\n                snaps=\"true\"\r\n                ticks=\"false\"\r\n                snaps-per-step=\"true\"\r\n                (ionChange)=\"updateQuestProgress(quest, $event)\"\r\n                style=\"--progress-value: {{quest.value_achieved / quest.goal_value * 100}}%\">\r\n              </ion-range>\r\n              <div class=\"progress-text values\">\r\n                <span>\r\n                  {{ quest.value_achieved }}/{{ quest.goal_value }}\r\n                </span>\r\n                <span class=\"members-count\">\r\n                  Members: {{ quest.completed_members }}/{{ quest.total_members }}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"quest-streak\" *ngIf=\"isToday()\">\r\n          🔥{{ quest.streak }}d\r\n        </div>\r\n        <div class=\"quest-streak\" *ngIf=\"!isToday()\"></div>\r\n      </div>\r\n      </ng-container>\r\n    </div>\r\n  </section>\r\n\r\n  <!-- New Group Sidequest Component -->\r\n  <app-group-sidequest\r\n    *ngIf=\"group?.enable_sidequests && groupId && userId\"\r\n    [groupId]=\"groupId || ''\"\r\n    [userId]=\"userId || ''\"\r\n    [joinedDate]=\"joinedDate\"\r\n    [isAdmin]=\"isAdmin\"\r\n    [enableSidequests]=\"group?.enable_sidequests || false\"\r\n    [selectedDate]=\"selectedDate\">\r\n  </app-group-sidequest>\r\n</div>\r\n\r\n<div class=\"group-footer\" *ngIf=\"!joinedToday || isAdmin\">\r\n  <a [routerLink]=\"['/groups', groupId, 'settings']\" class=\"settings-btn\" style=\"text-decoration: none; \">⚙️ Settings</a>\r\n  <a href=\"#\" id=\"add-quest-btn\" class=\"add-quest-link\" *ngIf=\"isAdmin\" (click)=\"openAddQuestModal($event)\">\r\n    <span class=\"add-quest-icon\">+</span> Add Quest\r\n  </a>\r\n</div>\r\n\r\n<!-- Add Quest Modal -->\r\n<div id=\"add-quest-modal\" class=\"modal\" [style.display]=\"showAddQuestModal ? 'block' : 'none'\">\r\n  <div class=\"modal-content\">\r\n    <span class=\"close-modal\" (click)=\"closeAddQuestModal()\">&times;</span>\r\n    <h2>Add New Group Quest</h2>\r\n    <form (ngSubmit)=\"createGroupQuest()\" id=\"add-quest-form\">\r\n      <div style=\"display: flex;gap: 10px;\">\r\n        <div class=\"form-group\">\r\n          <input type=\"text\" id=\"emoji\" name=\"emoji\" [(ngModel)]=\"newQuest.emoji\" value=\"🎯\" appEmojiInput>\r\n        </div>\r\n        <div class=\"form-group\">\r\n          <input type=\"text\" id=\"name\" name=\"name\" [(ngModel)]=\"newQuest.name\" placeholder=\"Enter quest name\" required>\r\n        </div>\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"description\">Description</label>\r\n        <textarea id=\"description\" name=\"description\" [(ngModel)]=\"newQuest.description\" placeholder=\"Enter quest description\"></textarea>\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"quest_type\">Quest Type</label>\r\n        <select id=\"quest_type\" name=\"quest_type\" [(ngModel)]=\"newQuest.quest_type\">\r\n          <option value=\"build\">Build Habit</option>\r\n          <option value=\"quit\">Quit Habit</option>\r\n        </select>\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"category\">Category</label>\r\n        <select id=\"category\" name=\"category\" [(ngModel)]=\"newQuest.category\" required>\r\n          <option value=\"\">Select a category</option>\r\n          <option value=\"strength\">Strength</option>\r\n          <option value=\"money\">Money</option>\r\n          <option value=\"health\">Health</option>\r\n          <option value=\"knowledge\">Knowledge</option>\r\n        </select>\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"priority\">Priority</label>\r\n        <select id=\"priority\" name=\"priority\" [(ngModel)]=\"newQuest.priority\" required>\r\n          <option value=\"basic\">Basic</option>\r\n          <option value=\"high\">High Priority</option>\r\n        </select>\r\n      </div>\r\n      <div class=\"form-group goal-settings\">\r\n        <label>Goal</label>\r\n        <div class=\"goal-inputs\">\r\n          <input type=\"number\" id=\"goal_value\" name=\"goal_value\" [(ngModel)]=\"newQuest.goal_value\" value=\"1\" min=\"1\">\r\n          <select id=\"goal_unit\" name=\"goal_unit\" [(ngModel)]=\"newQuest.goal_unit\">\r\n            <option value=\"count\">count</option>\r\n            <option value=\"steps\">steps</option>\r\n            <option value=\"m\">meters</option>\r\n            <option value=\"km\">kilometers</option>\r\n            <option value=\"sec\">seconds</option>\r\n            <option value=\"min\">minutes</option>\r\n            <option value=\"hr\">hours</option>\r\n            <option value=\"Cal\">calories</option>\r\n            <option value=\"g\">grams</option>\r\n            <option value=\"mg\">milligrams</option>\r\n            <option value=\"drink\">drinks</option>\r\n          </select>\r\n        </div>\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"goal_period\">Frequency</label>\r\n        <select id=\"goal_period\" name=\"goal_period\" [(ngModel)]=\"newQuest.goal_period\" (change)=\"onGoalPeriodChange()\">\r\n          <option value=\"day\">Every Day</option>\r\n          <option value=\"week\">Specific days of the week</option>\r\n          <option value=\"month\">Specific days of the month</option>\r\n        </select>\r\n      </div>\r\n      <div id=\"days-of-week-container\" class=\"form-group schedule-container\" [style.display]=\"newQuest.goal_period === 'week' ? 'block' : 'none'\">\r\n        <label>Select Days of Week</label>\r\n        <div class=\"days-selector\">\r\n          <div class=\"day-checkbox\" *ngFor=\"let day of daysOfWeek\">\r\n            <input type=\"checkbox\" [id]=\"'day-' + day.value.toLowerCase()\" name=\"days_of_week\" [value]=\"day.value\"\r\n                   [(ngModel)]=\"day.selected\">\r\n            <label [for]=\"'day-' + day.value.toLowerCase()\">{{ day.label }}</label>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div id=\"days-of-month-container\" class=\"form-group schedule-container\" [style.display]=\"newQuest.goal_period === 'month' ? 'block' : 'none'\">\r\n        <label>Select Days of Month</label>\r\n        <div class=\"month-days-selector\">\r\n          <div class=\"day-checkbox\" *ngFor=\"let day of daysOfMonth\">\r\n            <input type=\"checkbox\" [id]=\"'month-day-' + day.value\" name=\"days_of_month\" [value]=\"day.value\"\r\n                   [(ngModel)]=\"day.selected\">\r\n            <label [for]=\"'month-day-' + day.value\">{{ day.value }}</label>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <button type=\"submit\" class=\"submit-btn\">Create Quest</button>\r\n    </form>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;AAAA,SAAuCA,MAAM,QAAQ,eAAe;AACpE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,EAA+BC,eAAe,QAAQ,gBAAgB;AAC1F,SAASC,cAAc,EAAEC,MAAM,EAAEC,YAAY,EAAEC,aAAa,QAAQ,iBAAiB;AACrF,SAAuBC,IAAI,EAAkBC,aAAa,EAAEC,SAAS,EAAEC,GAAG,EAAEC,EAAE,EAAcC,MAAM,QAAQ,MAAM;AAEhH,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,YAAY,QAAQ,iCAAiC;AAC9D,SAASC,WAAW,QAAQ,gCAAgC;AAE5D,SAAgBC,WAAW,QAAQ,iCAAiC;AACpE,SAASC,mBAAmB,QAAQ,2CAA2C;AAC/E,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,yBAAyB,QAAQ,qEAAqE;;;;;;;;;;;ICb/GC,EAAA,CAAAC,SAAA,iCAKyB;;;;IADvBD,EAFA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,OAAA,OAAyB,eAAAD,MAAA,CAAAE,KAAA,kBAAAF,MAAA,CAAAE,KAAA,CAAAC,IAAA,aACW,YAAAH,MAAA,CAAAI,OAAA,CACjB;;;;;IAoBbP,EAAA,CAAAQ,cAAA,cAAmD;IAAAR,EAAA,CAAAS,MAAA,GAAS;IAAAT,EAAA,CAAAU,YAAA,EAAM;;;;IAAfV,EAAA,CAAAW,SAAA,EAAS;IAATX,EAAA,CAAAY,iBAAA,CAAAC,MAAA,CAAS;;;;;;IAa5Db,EAAA,CAAAQ,cAAA,cAA0J;IACxJR,EAAA,CAAAC,SAAA,iBAI2F;IAC7FD,EAAA,CAAAU,YAAA,EAAM;;;;IADIV,EAAA,CAAAW,SAAA,EAAyE;IAAzEX,EAAA,CAAAc,WAAA,QAAAC,OAAA,CAAAC,oBAAA,IAAAD,OAAA,CAAAC,oBAAA,MAAyE;;;;;;;IAbrFhB,EAAA,CAAAQ,cAAA,cAOuC;IAAlCR,EAAA,CAAAiB,UAAA,mBAAAC,2DAAA;MAAA,MAAAH,OAAA,GAAAf,EAAA,CAAAmB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAlB,MAAA,GAAAH,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASpB,MAAA,CAAAqB,iBAAA,CAAAT,OAAA,CAAuB;IAAA,EAAC;IACpCf,EAAA,CAAAyB,UAAA,IAAAC,gDAAA,kBAA0J;IAO1J1B,EAAA,CAAAQ,cAAA,eAA2B;IAAAR,EAAA,CAAAS,MAAA,GAAc;IAC3CT,EAD2C,CAAAU,YAAA,EAAO,EAC5C;;;;IAZDV,EAFA,CAAAc,WAAA,WAAAC,OAAA,CAAAY,OAAA,CAA6B,aAAAZ,OAAA,CAAAa,UAAA,CACK,aAAAb,OAAA,CAAAc,QAAA,IAAAd,OAAA,CAAAe,YAAA,CACmB;IAErD9B,EAAA,CAAAE,UAAA,UAAAa,OAAA,CAAAe,YAAA,2DAAAf,OAAA,CAAAc,QAAA,qCAAyI;;IAE5F7B,EAAA,CAAAW,SAAA,EAAwG;IAAxGX,EAAA,CAAAE,UAAA,SAAAa,OAAA,CAAAC,oBAAA,IAAAD,OAAA,CAAAC,oBAAA,SAAAD,OAAA,CAAAc,QAAA,KAAAd,OAAA,CAAAe,YAAA,CAAwG;IAO7H9B,EAAA,CAAAW,SAAA,GAAc;IAAdX,EAAA,CAAAY,iBAAA,CAAAG,OAAA,CAAAgB,GAAA,CAAc;;;;;IAG7C/B,EAAA,CAAAQ,cAAA,cAAwD;IAAAR,EAAA,CAAAS,MAAA,kBAAW;IAAAT,EAAA,CAAAU,YAAA,EAAM;;;;;IAOrEV,EAAA,CAAAgC,uBAAA,GAAgC;IAAAhC,EAAA,CAAAS,MAAA,YAAK;;;;;;IACrCT,EAAA,CAAAgC,uBAAA,GAAoC;IAAAhC,EAAA,CAAAS,MAAA,gBAAS;;;;;;IAC7CT,EAAA,CAAAgC,uBAAA,GAAmC;IAAAhC,EAAA,CAAAS,MAAA,eAAQ;;;;;;IAC3CT,EAAA,CAAAgC,uBAAA,GAAoE;IAAAhC,EAAA,CAAAS,MAAA,GAAgB;;;;;IAAhBT,EAAA,CAAAW,SAAA,EAAgB;IAAhBX,EAAA,CAAAY,iBAAA,CAAAT,MAAA,CAAA8B,UAAA,CAAgB;;;;;;IAmB9EjC,EADF,CAAAQ,cAAA,cAAoJ,oBAanE;IAT7ER,EAAA,CAAAkC,gBAAA,2BAAAC,8FAAAC,MAAA;MAAApC,EAAA,CAAAmB,aAAA,CAAAkB,GAAA;MAAA,MAAAC,QAAA,GAAAtC,EAAA,CAAAsB,aAAA,IAAAD,SAAA;MAAArB,EAAA,CAAAuC,kBAAA,CAAAD,QAAA,CAAAE,cAAA,EAAAJ,MAAA,MAAAE,QAAA,CAAAE,cAAA,GAAAJ,MAAA;MAAA,OAAApC,EAAA,CAAAuB,WAAA,CAAAa,MAAA;IAAA,EAAkC;IAQlCpC,EAAA,CAAAiB,UAAA,uBAAAwB,0FAAAL,MAAA;MAAApC,EAAA,CAAAmB,aAAA,CAAAkB,GAAA;MAAA,MAAAC,QAAA,GAAAtC,EAAA,CAAAsB,aAAA,IAAAD,SAAA;MAAA,MAAAlB,MAAA,GAAAH,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAapB,MAAA,CAAAuC,mBAAA,CAAAJ,QAAA,EAAAF,MAAA,CAAkC;IAAA,EAAC;IAElDpC,EAAA,CAAAU,YAAA,EAAY;IACZV,EAAA,CAAAQ,cAAA,cAA2B;IACzBR,EAAA,CAAAS,MAAA,GACF;IACFT,EADE,CAAAU,YAAA,EAAM,EACF;;;;IALFV,EAAA,CAAAW,SAAA,EAA4E;IAA5EX,EAAA,CAAA2C,sBAAA,uBAAAL,QAAA,CAAAE,cAAA,GAAAF,QAAA,CAAAM,UAAA,YAA4E;IAV5E5C,EAAA,CAAAE,UAAA,QAAAoC,QAAA,CAAAM,UAAA,CAAwB;IACxB5C,EAAA,CAAA6C,gBAAA,YAAAP,QAAA,CAAAE,cAAA,CAAkC;IAIlCxC,EAAA,CAAAE,UAAA,WAAU;;IAQVF,EAAA,CAAAW,SAAA,GACF;IADEX,EAAA,CAAA8C,kBAAA,MAAAR,QAAA,CAAAE,cAAA,MAAAF,QAAA,CAAAS,SAAA,mBAAAT,QAAA,CAAAS,SAAA,4BAAAT,QAAA,CAAAM,UAAA,MAAAN,QAAA,CAAAS,SAAA,mBAAAT,QAAA,CAAAS,SAAA,2BACF;;;;;;IAGA/C,EADF,CAAAQ,cAAA,cAA+I,oBAa9D;IAT7ER,EAAA,CAAAkC,gBAAA,2BAAAc,+FAAAZ,MAAA;MAAApC,EAAA,CAAAmB,aAAA,CAAA8B,GAAA;MAAA,MAAAX,QAAA,GAAAtC,EAAA,CAAAsB,aAAA,IAAAD,SAAA;MAAArB,EAAA,CAAAuC,kBAAA,CAAAD,QAAA,CAAAE,cAAA,EAAAJ,MAAA,MAAAE,QAAA,CAAAE,cAAA,GAAAJ,MAAA;MAAA,OAAApC,EAAA,CAAAuB,WAAA,CAAAa,MAAA;IAAA,EAAkC;IAQlCpC,EAAA,CAAAiB,UAAA,uBAAAiC,2FAAAd,MAAA;MAAApC,EAAA,CAAAmB,aAAA,CAAA8B,GAAA;MAAA,MAAAX,QAAA,GAAAtC,EAAA,CAAAsB,aAAA,IAAAD,SAAA;MAAA,MAAAlB,MAAA,GAAAH,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAapB,MAAA,CAAAuC,mBAAA,CAAAJ,QAAA,EAAAF,MAAA,CAAkC;IAAA,EAAC;IAElDpC,EAAA,CAAAU,YAAA,EAAY;IAEVV,EADF,CAAAQ,cAAA,cAAkC,WAC1B;IACJR,EAAA,CAAAS,MAAA,GACF;IAAAT,EAAA,CAAAU,YAAA,EAAO;IACPV,EAAA,CAAAQ,cAAA,eAA4B;IAC1BR,EAAA,CAAAS,MAAA,GACF;IAEJT,EAFI,CAAAU,YAAA,EAAO,EACH,EACF;;;;IAVFV,EAAA,CAAAW,SAAA,EAA4E;IAA5EX,EAAA,CAAA2C,sBAAA,uBAAAL,QAAA,CAAAE,cAAA,GAAAF,QAAA,CAAAM,UAAA,YAA4E;IAV5E5C,EAAA,CAAAE,UAAA,QAAAoC,QAAA,CAAAM,UAAA,CAAwB;IACxB5C,EAAA,CAAA6C,gBAAA,YAAAP,QAAA,CAAAE,cAAA,CAAkC;IAIlCxC,EAAA,CAAAE,UAAA,WAAU;;IASRF,EAAA,CAAAW,SAAA,GACF;IADEX,EAAA,CAAAmD,kBAAA,MAAAb,QAAA,CAAAE,cAAA,OAAAF,QAAA,CAAAM,UAAA,MACF;IAEE5C,EAAA,CAAAW,SAAA,GACF;IADEX,EAAA,CAAAmD,kBAAA,eAAAb,QAAA,CAAAc,iBAAA,OAAAd,QAAA,CAAAe,aAAA,MACF;;;;;IAKRrD,EAAA,CAAAQ,cAAA,cAA4C;IAC1CR,EAAA,CAAAS,MAAA,GACF;IAAAT,EAAA,CAAAU,YAAA,EAAM;;;;IADJV,EAAA,CAAAW,SAAA,EACF;IADEX,EAAA,CAAAsD,kBAAA,kBAAAhB,QAAA,CAAAiB,MAAA,OACF;;;;;IACAvD,EAAA,CAAAC,SAAA,cAAmD;;;;;IAvDnDD,EANA,CAAAQ,cAAA,cAKoC,cACZ;IACtBR,EAAA,CAAAS,MAAA,GACF;IAAAT,EAAA,CAAAU,YAAA,EAAM;IAEJV,EADF,CAAAQ,cAAA,cAAwB,SAClB;IAAAR,EAAA,CAAAS,MAAA,GAAgB;IAAAT,EAAA,CAAAU,YAAA,EAAK;IACzBV,EAAA,CAAAQ,cAAA,QAAG;IAAAR,EAAA,CAAAS,MAAA,GAAuB;IAAAT,EAAA,CAAAU,YAAA,EAAI;IAC9BV,EAAA,CAAAQ,cAAA,cAAgC;IAoB9BR,EAnBA,CAAAyB,UAAA,IAAA+B,0DAAA,mBAAoJ,KAAAC,2DAAA,mBAmBL;IAyBnJzD,EADE,CAAAU,YAAA,EAAM,EACF;IAINV,EAHA,CAAAyB,UAAA,KAAAiC,2DAAA,kBAA4C,KAAAC,2DAAA,kBAGC;IAC/C3D,EAAA,CAAAU,YAAA,EAAM;;;;;IA5DCV,EAAA,CAAAc,WAAA,cAAAwB,QAAA,CAAAsB,SAAA,CAAmC;;IAKtC5D,EAAA,CAAAW,SAAA,GACF;IADEX,EAAA,CAAAsD,kBAAA,MAAAhB,QAAA,CAAAuB,KAAA,MACF;IAEM7D,EAAA,CAAAW,SAAA,GAAgB;IAAhBX,EAAA,CAAAY,iBAAA,CAAA0B,QAAA,CAAAhC,IAAA,CAAgB;IACjBN,EAAA,CAAAW,SAAA,GAAuB;IAAvBX,EAAA,CAAAY,iBAAA,CAAA0B,QAAA,CAAAwB,WAAA,CAAuB;IAEI9D,EAAA,CAAAW,SAAA,GAAsH;IAAtHX,EAAA,CAAAE,UAAA,SAAAoC,QAAA,CAAAS,SAAA,eAAAT,QAAA,CAAAS,SAAA,cAAAT,QAAA,CAAAS,SAAA,aAAAT,QAAA,CAAAS,SAAA,WAAsH;IAmB3H/C,EAAA,CAAAW,SAAA,EAAsH;IAAtHX,EAAA,CAAAE,UAAA,SAAAoC,QAAA,CAAAS,SAAA,eAAAT,QAAA,CAAAS,SAAA,cAAAT,QAAA,CAAAS,SAAA,aAAAT,QAAA,CAAAS,SAAA,WAAsH;IA0BtH/C,EAAA,CAAAW,SAAA,EAAe;IAAfX,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAwB,OAAA,GAAe;IAGf3B,EAAA,CAAAW,SAAA,EAAgB;IAAhBX,EAAA,CAAAE,UAAA,UAAAC,MAAA,CAAAwB,OAAA,GAAgB;;;;;IA9D7C3B,EAAA,CAAAgC,uBAAA,GAA2C;IACzChC,EAAA,CAAAyB,UAAA,IAAAsC,oDAAA,oBAKoC;;;;;IAL9B/D,EAAA,CAAAW,SAAA,EAA0B;IAA1BX,EAAA,CAAAE,UAAA,UAAAoC,QAAA,CAAA0B,aAAA,CAA0B;;;;;IAoEtChE,EAAA,CAAAC,SAAA,8BAQsB;;;;IADpBD,EALA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,OAAA,OAAyB,WAAAD,MAAA,CAAA8D,MAAA,OACF,eAAA9D,MAAA,CAAA+D,UAAA,CACE,YAAA/D,MAAA,CAAAI,OAAA,CACN,sBAAAJ,MAAA,CAAAE,KAAA,kBAAAF,MAAA,CAAAE,KAAA,CAAA8D,iBAAA,WACmC,iBAAAhE,MAAA,CAAAiE,YAAA,CACzB;;;;;;IA9H3BpE,EAHN,CAAAQ,cAAA,cAA4C,aAClC,cACiB,cACH;IAChBR,EAAA,CAAAC,SAAA,cAA6D;IAC7DD,EAAA,CAAAQ,cAAA,WAAM;IAAAR,EAAA,CAAAS,MAAA,cAAO;IAEjBT,EAFiB,CAAAU,YAAA,EAAO,EAChB,EACF;IACNV,EAAA,CAAAQ,cAAA,SAAI;IAAAR,EAAA,CAAAS,MAAA,GAAoC;IAC1CT,EAD0C,CAAAU,YAAA,EAAK,EACtC;IACTV,EAAA,CAAAQ,cAAA,YAAgD;IAAAR,EAAA,CAAAS,MAAA,6BAAqB;IAAAT,EAAA,CAAAU,YAAA,EAAI;IAIrEV,EAFJ,CAAAQ,cAAA,eAA2B,eACC,kBACyC;IAAlCR,EAAA,CAAAiB,UAAA,mBAAAoD,wDAAA;MAAArE,EAAA,CAAAmB,aAAA,CAAAmD,GAAA;MAAA,MAAAnE,MAAA,GAAAH,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASpB,MAAA,CAAAoE,mBAAA,EAAqB,CAAC,CAAC;IAAA,EAAC;IAACvE,EAAA,CAAAS,MAAA,cAAC;IAAAT,EAAA,CAAAU,YAAA,EAAS;IAC3EV,EAAA,CAAAQ,cAAA,eAAkB;IAChBR,EAAA,CAAAyB,UAAA,KAAA+C,qCAAA,kBAAmD;IACrDxE,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAQ,cAAA,kBAAgE;IAAjCR,EAAA,CAAAiB,UAAA,mBAAAwD,wDAAA;MAAAzE,EAAA,CAAAmB,aAAA,CAAAmD,GAAA;MAAA,MAAAnE,MAAA,GAAAH,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASpB,MAAA,CAAAoE,mBAAA,CAAoB,CAAC,CAAC;IAAA,EAAC;IAACvE,EAAA,CAAAS,MAAA,cAAC;IACnET,EADmE,CAAAU,YAAA,EAAS,EACtE;IACNV,EAAA,CAAAQ,cAAA,eAAmB;IACjBR,EAAA,CAAAyB,UAAA,KAAAiD,qCAAA,mBAOuC;IAUzC1E,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAyB,UAAA,KAAAkD,qCAAA,kBAAwD;IAC1D3E,EAAA,CAAAU,YAAA,EAAM;IAIFV,EAFJ,CAAAQ,cAAA,mBAAwB,eACsC,UACtD;IAAAR,EAAA,CAAAS,MAAA,cAAM;IAAAT,EAAA,CAAAU,YAAA,EAAK;IACfV,EAAA,CAAAQ,cAAA,UAAI;IAIFR,EAHA,CAAAyB,UAAA,KAAAmD,8CAAA,2BAAgC,KAAAC,8CAAA,2BACI,KAAAC,8CAAA,2BACD,KAAAC,8CAAA,2BACiC;IAExE/E,EADE,CAAAU,YAAA,EAAK,EACD;IACNV,EAAA,CAAAQ,cAAA,eAAwB;IACtBR,EAAA,CAAAyB,UAAA,KAAAuD,8CAAA,2BAA2C;IAkE/ChF,EADE,CAAAU,YAAA,EAAM,EACE;IAGVV,EAAA,CAAAyB,UAAA,KAAAwD,qDAAA,kCAOgC;IAElCjF,EAAA,CAAAU,YAAA,EAAM;;;;IA3HEV,EAAA,CAAAW,SAAA,GAAoC;IAApCX,EAAA,CAAAmD,kBAAA,KAAAhD,MAAA,CAAAE,KAAA,kBAAAF,MAAA,CAAAE,KAAA,CAAAwD,KAAA,OAAA1D,MAAA,CAAAE,KAAA,kBAAAF,MAAA,CAAAE,KAAA,CAAAC,IAAA,KAAoC;IAEvCN,EAAA,CAAAW,SAAA,EAA0B;IAA1BX,EAAA,CAAAE,UAAA,eAAAF,EAAA,CAAAkF,eAAA,KAAAC,GAAA,EAA0B;IAMenF,EAAA,CAAAW,SAAA,GAAW;IAAXX,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAiF,QAAA,CAAW;IAK7BpF,EAAA,CAAAW,SAAA,GAAY;IAAZX,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAkF,SAAA,CAAY;IAkBJrF,EAAA,CAAAW,SAAA,EAAsB;IAAtBX,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAmF,cAAA,GAAsB;IAOnCtF,EAAA,CAAAW,SAAA,GAAe;IAAfX,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAwB,OAAA,GAAe;IACf3B,EAAA,CAAAW,SAAA,EAAmB;IAAnBX,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAoF,WAAA,GAAmB;IACnBvF,EAAA,CAAAW,SAAA,EAAkB;IAAlBX,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAqF,UAAA,GAAkB;IAClBxF,EAAA,CAAAW,SAAA,EAAmD;IAAnDX,EAAA,CAAAE,UAAA,UAAAC,MAAA,CAAAwB,OAAA,OAAAxB,MAAA,CAAAoF,WAAA,OAAApF,MAAA,CAAAqF,UAAA,GAAmD;IAIpCxF,EAAA,CAAAW,SAAA,GAAS;IAATX,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAsF,MAAA,CAAS;IAsE1CzF,EAAA,CAAAW,SAAA,EAAmD;IAAnDX,EAAA,CAAAE,UAAA,UAAAC,MAAA,CAAAE,KAAA,kBAAAF,MAAA,CAAAE,KAAA,CAAA8D,iBAAA,KAAAhE,MAAA,CAAAC,OAAA,IAAAD,MAAA,CAAA8D,MAAA,CAAmD;;;;;;IAYtDjE,EAAA,CAAAQ,cAAA,aAA0G;IAApCR,EAAA,CAAAiB,UAAA,mBAAAyE,sDAAAtD,MAAA;MAAApC,EAAA,CAAAmB,aAAA,CAAAwE,GAAA;MAAA,MAAAxF,MAAA,GAAAH,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASpB,MAAA,CAAAyF,iBAAA,CAAAxD,MAAA,CAAyB;IAAA,EAAC;IACvGpC,EAAA,CAAAQ,cAAA,gBAA6B;IAAAR,EAAA,CAAAS,MAAA,QAAC;IAAAT,EAAA,CAAAU,YAAA,EAAO;IAACV,EAAA,CAAAS,MAAA,kBACxC;IAAAT,EAAA,CAAAU,YAAA,EAAI;;;;;IAHJV,EADF,CAAAQ,cAAA,cAA0D,YACgD;IAAAR,EAAA,CAAAS,MAAA,4BAAW;IAAAT,EAAA,CAAAU,YAAA,EAAI;IACvHV,EAAA,CAAAyB,UAAA,IAAAoE,kCAAA,iBAA0G;IAG5G7F,EAAA,CAAAU,YAAA,EAAM;;;;IAJDV,EAAA,CAAAW,SAAA,EAA+C;IAA/CX,EAAA,CAAAE,UAAA,eAAAF,EAAA,CAAA8F,eAAA,IAAAC,GAAA,EAAA5F,MAAA,CAAAC,OAAA,EAA+C;IACKJ,EAAA,CAAAW,SAAA,GAAa;IAAbX,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAI,OAAA,CAAa;;;;;;IA8E1DP,EADF,CAAAQ,cAAA,eAAyD,iBAErB;IAA3BR,EAAA,CAAAkC,gBAAA,2BAAA8D,+DAAA5D,MAAA;MAAA,MAAA6D,OAAA,GAAAjG,EAAA,CAAAmB,aAAA,CAAA+E,IAAA,EAAA7E,SAAA;MAAArB,EAAA,CAAAuC,kBAAA,CAAA0D,OAAA,CAAAE,QAAA,EAAA/D,MAAA,MAAA6D,OAAA,CAAAE,QAAA,GAAA/D,MAAA;MAAA,OAAApC,EAAA,CAAAuB,WAAA,CAAAa,MAAA;IAAA,EAA0B;IADjCpC,EAAA,CAAAU,YAAA,EACkC;IAClCV,EAAA,CAAAQ,cAAA,iBAAgD;IAAAR,EAAA,CAAAS,MAAA,GAAe;IACjET,EADiE,CAAAU,YAAA,EAAQ,EACnE;;;;IAHmBV,EAAA,CAAAW,SAAA,EAAuC;IAAqBX,EAA5D,CAAAE,UAAA,gBAAA+F,OAAA,CAAAG,KAAA,CAAAC,WAAA,GAAuC,UAAAJ,OAAA,CAAAG,KAAA,CAAwC;IAC/FpG,EAAA,CAAA6C,gBAAA,YAAAoD,OAAA,CAAAE,QAAA,CAA0B;IAC1BnG,EAAA,CAAAW,SAAA,EAAwC;IAAxCX,EAAA,CAAAE,UAAA,iBAAA+F,OAAA,CAAAG,KAAA,CAAAC,WAAA,GAAwC;IAACrG,EAAA,CAAAW,SAAA,EAAe;IAAfX,EAAA,CAAAY,iBAAA,CAAAqF,OAAA,CAAAK,KAAA,CAAe;;;;;;IAQ/DtG,EADF,CAAAQ,cAAA,eAA0D,iBAEtB;IAA3BR,EAAA,CAAAkC,gBAAA,2BAAAqE,+DAAAnE,MAAA;MAAA,MAAAoE,OAAA,GAAAxG,EAAA,CAAAmB,aAAA,CAAAsF,IAAA,EAAApF,SAAA;MAAArB,EAAA,CAAAuC,kBAAA,CAAAiE,OAAA,CAAAL,QAAA,EAAA/D,MAAA,MAAAoE,OAAA,CAAAL,QAAA,GAAA/D,MAAA;MAAA,OAAApC,EAAA,CAAAuB,WAAA,CAAAa,MAAA;IAAA,EAA0B;IADjCpC,EAAA,CAAAU,YAAA,EACkC;IAClCV,EAAA,CAAAQ,cAAA,iBAAwC;IAAAR,EAAA,CAAAS,MAAA,GAAe;IACzDT,EADyD,CAAAU,YAAA,EAAQ,EAC3D;;;;IAHmBV,EAAA,CAAAW,SAAA,EAA+B;IAAsBX,EAArD,CAAAE,UAAA,sBAAAsG,OAAA,CAAAJ,KAAA,CAA+B,UAAAI,OAAA,CAAAJ,KAAA,CAAyC;IACxFpG,EAAA,CAAA6C,gBAAA,YAAA2D,OAAA,CAAAL,QAAA,CAA0B;IAC1BnG,EAAA,CAAAW,SAAA,EAAgC;IAAhCX,EAAA,CAAAE,UAAA,uBAAAsG,OAAA,CAAAJ,KAAA,CAAgC;IAACpG,EAAA,CAAAW,SAAA,EAAe;IAAfX,EAAA,CAAAY,iBAAA,CAAA4F,OAAA,CAAAJ,KAAA,CAAe;;;ADhInE,OAAM,MAAOM,eAAe;EAwD1BC,YAAA;IAvDA,KAAA1C,MAAM,GAAkB,IAAI;IAE5B,KAAA7D,OAAO,GAAkB,IAAI;IAC7B,KAAAC,KAAK,GAA+B,IAAI;IACxC,KAAAuG,OAAO,GAAwB,EAAE;IACjC,KAAArG,OAAO,GAAG,KAAK;IACf,KAAAsG,UAAU,GAAG,CAAC;IACd,KAAA3C,UAAU,GAAW,EAAE;IAEvB,KAAAkB,QAAQ,GAAa,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACtE,KAAA0B,UAAU,GAAW,CAAC;IACtB,KAAAzB,SAAS,GAAe,EAAE;IAC1B,KAAAjB,YAAY,GAAW,EAAE;IACzB,KAAAnC,UAAU,GAAW,EAAE;IACvB,KAAA8E,gBAAgB,GAAY,KAAK;IACjC,KAAAC,WAAW,GAAY,KAAK;IAE5B,KAAAvB,MAAM,GAAY,EAAE;IACpB,KAAAwB,UAAU,GAAsB,IAAI;IACpC,KAAAC,YAAY,GAAiB;MAAEC,EAAE,EAAE,EAAE;MAAEvD,SAAS,EAAE,KAAK;MAAEpB,cAAc,EAAE;IAAC,CAAE;IAE5E,KAAA4E,iBAAiB,GAAY,KAAK;IAClC,KAAAC,QAAQ,GAAG;MACTxD,KAAK,EAAE,IAAI;MACXvD,IAAI,EAAE,EAAE;MACRwD,WAAW,EAAE,EAAE;MACfwD,UAAU,EAAE,OAAoB;MAChCC,QAAQ,EAAE,EAAmB;MAC7BC,QAAQ,EAAE,OAAwB;MAClC5E,UAAU,EAAE,CAAC;MACbG,SAAS,EAAE,OAAwB;MACnC0E,WAAW,EAAE;KACd;IAED,KAAAC,UAAU,GAAG,CACX;MAAEtB,KAAK,EAAE,QAAQ;MAAEE,KAAK,EAAE,GAAG;MAAEH,QAAQ,EAAE;IAAK,CAAE,EAChD;MAAEC,KAAK,EAAE,SAAS;MAAEE,KAAK,EAAE,GAAG;MAAEH,QAAQ,EAAE;IAAK,CAAE,EACjD;MAAEC,KAAK,EAAE,WAAW;MAAEE,KAAK,EAAE,GAAG;MAAEH,QAAQ,EAAE;IAAK,CAAE,EACnD;MAAEC,KAAK,EAAE,UAAU;MAAEE,KAAK,EAAE,GAAG;MAAEH,QAAQ,EAAE;IAAK,CAAE,EAClD;MAAEC,KAAK,EAAE,QAAQ;MAAEE,KAAK,EAAE,GAAG;MAAEH,QAAQ,EAAE;IAAK,CAAE,EAChD;MAAEC,KAAK,EAAE,UAAU;MAAEE,KAAK,EAAE,GAAG;MAAEH,QAAQ,EAAE;IAAK,CAAE,EAClD;MAAEC,KAAK,EAAE,QAAQ;MAAEE,KAAK,EAAE,GAAG;MAAEH,QAAQ,EAAE;IAAK,CAAE,CACjD;IAED,KAAAwB,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAE,EAAE,CAACC,CAAC,EAAEC,CAAC,MAAM;MAAE5B,KAAK,EAAE4B,CAAC,GAAG,CAAC;MAAE7B,QAAQ,EAAE;IAAK,CAAE,CAAC,CAAC;IAE/E,KAAA8B,aAAa,GAAmB,EAAE;IAElC,KAAAC,eAAe,GAAGxJ,MAAM,CAACe,eAAe,CAAC;IACzC,KAAA0I,YAAY,GAAGzJ,MAAM,CAACgB,YAAY,CAAC;IACnC,KAAA0I,WAAW,GAAG1J,MAAM,CAACiB,WAAW,CAAC;IACjC,KAAA0I,KAAK,GAAG3J,MAAM,CAACK,cAAc,CAAC;IAC9B,KAAAuJ,MAAM,GAAG5J,MAAM,CAACM,MAAM,CAAC;IACvB,KAAAuJ,eAAe,GAAG7J,MAAM,CAACI,eAAe,CAAC;EAElC;EAEf0J,QAAQA,CAAA;IACN,IAAI,CAACP,aAAa,CAACQ,IAAI,CACrB,IAAI,CAACP,eAAe,CAACQ,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MACjD,IAAIA,IAAI,EAAE;QACR,IAAI,CAAC3E,MAAM,GAAG2E,IAAI,CAACzB,EAAE;QAErB,IAAI,CAACkB,KAAK,CAACQ,QAAQ,CAACC,IAAI,CAAC3J,IAAI,CAAC,CAAC,CAAC,CAAC,CAACwJ,SAAS,CAACI,MAAM,IAAG;UACnD,IAAI,CAAC3I,OAAO,GAAG2I,MAAM,CAACC,GAAG,CAAC,IAAI,CAAC;UAC/B,IAAI,IAAI,CAAC5I,OAAO,EAAE;YAChB,IAAI,CAAC6I,SAAS,EAAE;YAChB,IAAI,CAACC,WAAW,EAAE;YAElB,IAAI,CAACb,KAAK,CAACc,WAAW,CAACL,IAAI,CAAC3J,IAAI,CAAC,CAAC,CAAC,CAAC,CAACwJ,SAAS,CAACQ,WAAW,IAAG;cAC3D,IAAI,CAACC,kBAAkB,EAAE;YAC3B,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACd,MAAM,CAACe,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAClC;IACF,CAAC,CAAC,CACH;IAED,IAAI,CAACpB,aAAa,CAACQ,IAAI,CACrB,IAAI,CAACH,MAAM,CAACgB,MAAM,CAACR,IAAI,CACrBtJ,MAAM,CAAC+J,KAAK,IAAIA,KAAK,YAAYrK,aAAa,CAAC,EAC/CM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAACY,OAAO,CAAC,CAC7B,CAACuI,SAAS,CAAC,MAAK;MACf,IAAI,CAACa,gBAAgB,EAAE;IACzB,CAAC,CAAC,CACH;EACH;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACpJ,OAAO,EAAE;IAEnB,IAAI,CAAC6I,SAAS,EAAE;IAChB,IAAI,CAACC,WAAW,EAAE;IAElB,IAAI,IAAI,CAAC9E,YAAY,EAAE;MACrB,IAAI,CAACqF,iBAAiB,CAAC,IAAI,CAACrF,YAAY,CAAC;MACzC,IAAI,CAACsF,kBAAkB,CAAC,IAAI,CAACtF,YAAY,CAAC;IAC5C;EACF;EAEAuF,gBAAgBA,CAAA;IACd,IAAI,CAACH,gBAAgB,EAAE;EACzB;EAEAI,eAAeA,CAAA,GACf;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC5B,aAAa,CAAC6B,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;EACtD;EAEAf,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAAC7I,OAAO,EAAE;IAEnB,IAAI,CAAC6H,aAAa,CAACQ,IAAI,CACrB,IAAI,CAACN,YAAY,CAAC8B,QAAQ,CAAC,IAAI,CAAC7J,OAAO,CAAC,CAACuI,SAAS,CAACtI,KAAK,IAAG;MACzD,IAAIA,KAAK,EAAE;QACT,IAAI,CAACA,KAAK,GAAGA,KAAK;QAClB,IAAI,CAAC6J,mBAAmB,EAAE;MAC5B,CAAC,MAAM;QACL,IAAI,CAAC5B,MAAM,CAACe,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;MACnC;IACF,CAAC,CAAC,CACH;EACH;EAEAH,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAC9I,OAAO,IAAI,CAAC,IAAI,CAAC6D,MAAM,EAAE;IAEnC,IAAI,CAACgE,aAAa,CAACQ,IAAI,CACrB,IAAI,CAACN,YAAY,CAACgC,eAAe,CAAC,IAAI,CAAC/J,OAAO,CAAC,CAACuI,SAAS,CAAC/B,OAAO,IAAG;MAClE,MAAMwD,iBAAiB,GAAGxD,OAAO,CAACyD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAK,IAAI,CAACtG,MAAM,CAAC;MACtE,IAAI,CAAC1D,OAAO,GAAG,CAAA6J,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEI,QAAQ,KAAI,KAAK;MAEnD,IAAIJ,iBAAiB,IAAIA,iBAAiB,CAACK,WAAW,EAAE;QACtD,IAAI,CAACvG,UAAU,GAAG,IAAIwG,IAAI,CAACN,iBAAiB,CAACK,WAAW,CAAC,CAACE,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAErF,MAAM1G,UAAU,GAAG,IAAIwG,IAAI,CAACN,iBAAiB,CAACK,WAAW,CAAC;QAC1DvG,UAAU,CAAC2G,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAE/B,MAAMC,KAAK,GAAG,IAAIJ,IAAI,EAAE;QACxBI,KAAK,CAACD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAE1B,IAAI,CAAC7D,WAAW,GAAG9C,UAAU,CAAC6G,OAAO,EAAE,KAAKD,KAAK,CAACC,OAAO,EAAE;MAC7D;MAEA,MAAMC,mBAAmB,GAAwB,EAAE;MAEnD,MAAMC,WAAW,GAAGrE,OAAO,CAACtH,GAAG,CAAC4L,MAAM,KAAK;QACzC,GAAGA,MAAM;QACT/D,EAAE,EAAE+D,MAAM,CAAC/D,EAAE,IAAI,EAAE;QACnBgE,QAAQ,EAAED,MAAM,CAACC,QAAQ,IAAI,EAAE;QAC/BZ,OAAO,EAAEW,MAAM,CAACX,OAAO,IAAI,EAAE;QAC7Ba,QAAQ,EAAEF,MAAM,CAACE,QAAQ,IAAI,EAAE;QAC/BZ,QAAQ,EAAEU,MAAM,CAACV,QAAQ,IAAI,KAAK;QAClCC,WAAW,EAAES,MAAM,CAACT,WAAW,IAAI,IAAIC,IAAI;OAC5C,CAAC,CAAC;MAEH,KAAK,MAAMQ,MAAM,IAAID,WAAW,EAAE;QAChC,IAAI,CAAC7C,WAAW,CAACiD,cAAc,CAACH,MAAM,CAACX,OAAO,CAAC,CAACzB,IAAI,CAAC3J,IAAI,CAAC,CAAC,CAAC,CAAC,CAACwJ,SAAS,CAAC2C,OAAO,IAAG;UAChF,IAAI,CAAClD,WAAW,CAACmD,YAAY,CAACL,MAAM,CAACX,OAAO,CAAC,CAACzB,IAAI,CAAC3J,IAAI,CAAC,CAAC,CAAC,CAAC,CAACwJ,SAAS,CAAC6C,KAAK,IAAG;YAC5ER,mBAAmB,CAACvC,IAAI,CAAC;cACvB,GAAGyC,MAAM;cACTO,eAAe,EAAE,CAAAH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,eAAe,KAAIC,SAAS;cACtDC,QAAQ,EAAEL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEK,QAAQ;cAC3BC,QAAQ,EAAE,CAAAJ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEI,QAAQ,KAAI,CAAC;cAC9BC,gBAAgB,EAAE,CAAAL,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEK,gBAAgB,KAAI,CAAC;cAC9CC,UAAU,EAAE,CAAAN,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEM,UAAU,KAAI;aAClC,CAAC;YAEF,IAAI,CAAClF,OAAO,GAAG,CAAC,GAAGoE,mBAAmB,CAAC;UACzC,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,CACH;EACH;EAEAd,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAAC7J,KAAK,EAAE;IAEjB,IAAI,CAACwG,UAAU,GAAG,IAAI,CAACxG,KAAK,CAAC0L,KAAK,GAAG,IAAI;EAC3C;EAEAC,aAAaA,CAACC,SAAiB;IAC7B,OAAO,IAAI,CAACnF,UAAU,GAAGmF,SAAS;EACpC;EAEA7C,kBAAkBA,CAAA;IAEhB,MAAM0B,KAAK,GAAG,IAAIJ,IAAI,EAAE;IAExB,IAAI,CAACrC,KAAK,CAACc,WAAW,CAACR,SAAS,CAACI,MAAM,IAAG;MAExC,IAAIA,MAAM,CAAC,aAAa,CAAC,EAAE;QACzB,IAAI;UACF,IAAI,CAACjC,UAAU,GAAGoF,QAAQ,CAACnD,MAAM,CAAC,aAAa,CAAC,CAAC;QACnD,CAAC,CAAC,OAAOoD,KAAK,EAAE;UACd,IAAI,CAACrF,UAAU,GAAG,CAAC;QACrB;MACF,CAAC,MAAM;QACL,IAAI,CAACA,UAAU,GAAG,CAAC;MACrB;MAEA,IAAI1C,YAAY,GAAG0G,KAAK;MACxB,IAAI/B,MAAM,CAAC,MAAM,CAAC,EAAE;QAClB,IAAI;UACF3E,YAAY,GAAG,IAAIsG,IAAI,CAAC3B,MAAM,CAAC,MAAM,CAAC,CAAC;UAEvC,IAAIqD,KAAK,CAAChI,YAAY,CAAC2G,OAAO,EAAE,CAAC,EAAE;YACjC3G,YAAY,GAAG0G,KAAK;UACtB;QACF,CAAC,CAAC,OAAOqB,KAAK,EAAE;UACd/H,YAAY,GAAG0G,KAAK;QACtB;MACF;MAEA,IAAI,CAAC1G,YAAY,GAAG,IAAI,CAACiI,UAAU,CAACjI,YAAY,CAAC;MAEjD,MAAMkI,SAAS,GAAG,IAAI5B,IAAI,CAACI,KAAK,CAAC;MACjCwB,SAAS,CAACC,OAAO,CAACzB,KAAK,CAAC0B,OAAO,EAAE,GAAI,IAAI,CAAC1F,UAAU,GAAG,CAAE,CAAC;MAE1D,IAAI,CAAC2F,iBAAiB,CAACH,SAAS,CAAC;MAEjC,IAAI,CAACI,gBAAgB,EAAE;MAEvB,IAAI,CAACjD,iBAAiB,CAAC,IAAI,CAACrF,YAAY,CAAC;MACzC,IAAI,CAACsF,kBAAkB,CAAC,IAAI,CAACtF,YAAY,CAAC;IAC5C,CAAC,CAAC;EACJ;EAEAiI,UAAUA,CAACM,IAAU;IACnB,MAAMC,IAAI,GAAGD,IAAI,CAACE,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACJ,IAAI,CAACK,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMlL,GAAG,GAAGgL,MAAM,CAACJ,IAAI,CAACH,OAAO,EAAE,CAAC,CAACS,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAI/K,GAAG,EAAE;EAClC;EAEA0K,iBAAiBA,CAACH,SAAe;IAC/B,MAAMxB,KAAK,GAAG,IAAIJ,IAAI,EAAE;IACxBI,KAAK,CAACD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAE1B,MAAMqC,UAAU,GAAGZ,SAAS,CAACa,MAAM,EAAE;IACrC,MAAMC,YAAY,GAAGF,UAAU,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAC9C,MAAMG,MAAM,GAAG,IAAI3C,IAAI,CAAC4B,SAAS,CAAC;IAClCe,MAAM,CAACd,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAGU,UAAU,GAAGE,YAAY,CAAC;IAG/D,MAAME,KAAK,GAAe,EAAE;IAE5B,KAAK,IAAItF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B,MAAM2E,IAAI,GAAG,IAAIjC,IAAI,CAAC2C,MAAM,CAAC;MAC7BV,IAAI,CAACJ,OAAO,CAACc,MAAM,CAACb,OAAO,EAAE,GAAGxE,CAAC,CAAC;MAElC,MAAMuF,OAAO,GAAG,IAAI,CAAClB,UAAU,CAACM,IAAI,CAAC;MACrCA,IAAI,CAAC9B,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACzB,MAAMlJ,OAAO,GAAGgL,IAAI,CAAC5B,OAAO,EAAE,KAAKD,KAAK,CAACC,OAAO,EAAE;MAElD,MAAMlJ,QAAQ,GAAG8K,IAAI,CAAC5B,OAAO,EAAE,GAAGD,KAAK,CAACC,OAAO,EAAE;MAEjD,IAAIjJ,YAAY,GAAG,KAAK;MACxB,IAAI,IAAI,CAAC8E,OAAO,CAACkB,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC7D,MAAM,EAAE;QAC1C,MAAMuJ,aAAa,GAAG,IAAI,CAAC5G,OAAO,CAACyD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAK,IAAI,CAACtG,MAAM,CAAC;QACvE,IAAIuJ,aAAa,IAAIA,aAAa,CAAC/C,WAAW,EAAE;UAC9C,MAAMgD,QAAQ,GAAG,IAAI/C,IAAI,CAAC8C,aAAa,CAAC/C,WAAW,CAAC;UACpDgD,QAAQ,CAAC5C,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC7B/I,YAAY,GAAG6K,IAAI,GAAGc,QAAQ;QAChC;MACF;MAEAH,KAAK,CAAC7E,IAAI,CAAC;QACTkE,IAAI,EAAEY,OAAO;QACbxL,GAAG,EAAE4K,IAAI,CAACH,OAAO,EAAE;QACnB7K,OAAO;QACPC,UAAU,EAAE2L,OAAO,KAAK,IAAI,CAACnJ,YAAY;QACzCvC,QAAQ;QACRC,YAAY;QACZd,oBAAoB,EAAE,CAAC;QACvB0M,WAAW,EAAE,CAAC;QACdC,eAAe,EAAE;OAClB,CAAC;IACJ;IAEA,IAAI,CAACtI,SAAS,GAAGiI,KAAK;IAEtB,IAAI,CAACM,sBAAsB,EAAE;EAC/B;EAEAC,YAAYA,CAAC5B,SAAiB;IAE5B,IAAI,CAAC5D,KAAK,CAACc,WAAW,CAACL,IAAI,CAAC3J,IAAI,CAAC,CAAC,CAAC,CAAC,CAACwJ,SAAS,CAACI,MAAM,IAAG;MACtD,IAAI+E,iBAAiB,GAAG,CAAC;MACzB,IAAI/E,MAAM,CAAC,aAAa,CAAC,EAAE;QACzB,IAAI;UACF+E,iBAAiB,GAAG5B,QAAQ,CAACnD,MAAM,CAAC,aAAa,CAAC,CAAC;QACrD,CAAC,CAAC,OAAOoD,KAAK,EAAE,CAChB;MACF;MAEA,MAAM4B,aAAa,GAAGD,iBAAiB,GAAG7B,SAAS;MAEnD,MAAM+B,SAAS,GAAG,IAAItD,IAAI,CAAC,IAAI,CAACrF,SAAS,CAAC,CAAC,CAAC,CAACsH,IAAI,CAAC;MAElDqB,SAAS,CAACzB,OAAO,CAACyB,SAAS,CAACxB,OAAO,EAAE,GAAIP,SAAS,GAAG,CAAE,CAAC;MAExD,IAAI,CAAC3D,MAAM,CAACe,QAAQ,CAAC,EAAE,EAAE;QACvB4E,UAAU,EAAE,IAAI,CAAC5F,KAAK;QACtBc,WAAW,EAAE;UACX+E,WAAW,EAAEH,aAAa;UAC1BpB,IAAI,EAAE,IAAI,CAACvI;;OAEd,CAAC;MAEF,IAAI,CAACqI,iBAAiB,CAACuB,SAAS,CAAC;MAEjC,MAAMG,kBAAkB,GAAG,IAAI,CAAC9I,SAAS,CAAC+I,IAAI,CAACzB,IAAI,IAAIA,IAAI,CAACA,IAAI,KAAK,IAAI,CAACvI,YAAY,CAAC;MACvF,IAAI,CAAC+J,kBAAkB,EAAE;QACvB,MAAME,SAAS,GAAG,IAAI,CAAChJ,SAAS,CAACgF,IAAI,CAACsC,IAAI,IAAI,CAACA,IAAI,CAAC9K,QAAQ,IAAI,CAAC8K,IAAI,CAAC7K,YAAY,CAAC;QACnF,IAAIuM,SAAS,EAAE;UACb,IAAI,CAACC,UAAU,CAACD,SAAS,CAAC;QAC5B;MACF;IACF,CAAC,CAAC;EACJ;EAEMC,UAAUA,CAAC3B,IAAc;IAAA,IAAA4B,KAAA;IAAA,OAAAC,iBAAA;MAE7B,IAAI7B,IAAI,CAAC9K,QAAQ,EAAE;QAEjB,MAAM4M,KAAK,SAASF,KAAI,CAAChG,eAAe,CAACmG,MAAM,CAAC;UAC9CC,OAAO,EAAE,4BAA4B;UACrCC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE;SACR,CAAC;QACF,MAAML,KAAK,CAACM,OAAO,EAAE;QAErB;MACF;MAEA,IAAIpC,IAAI,CAAC7K,YAAY,EAAE;QACrByM,KAAI,CAACxH,gBAAgB,GAAG,IAAI;QAE5B,MAAM0H,KAAK,SAASF,KAAI,CAAChG,eAAe,CAACmG,MAAM,CAAC;UAC9CC,OAAO,EAAE,qDAAqD;UAC9DC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE;SACR,CAAC;QACF,MAAML,KAAK,CAACM,OAAO,EAAE;QAErB;MACF,CAAC,MAAM;QACLR,KAAI,CAACxH,gBAAgB,GAAG,KAAK;MAC/B;MAEAwH,KAAI,CAACjG,MAAM,CAACe,QAAQ,CAAC,EAAE,EAAE;QACvB4E,UAAU,EAAEM,KAAI,CAAClG,KAAK;QACtBc,WAAW,EAAE;UAAEwD,IAAI,EAAEA,IAAI,CAACA;QAAI,CAAE;QAChCqC,mBAAmB,EAAE;OACtB,CAAC;MAEFT,KAAI,CAAClJ,SAAS,CAACyE,OAAO,CAACmF,CAAC,IAAIA,CAAC,CAACrN,UAAU,GAAGqN,CAAC,CAACtC,IAAI,KAAKA,IAAI,CAACA,IAAI,CAAC;MAChE4B,KAAI,CAACnK,YAAY,GAAGuI,IAAI,CAACA,IAAI;MAG7B4B,KAAI,CAAC7B,gBAAgB,EAAE;MAEvB6B,KAAI,CAAC9E,iBAAiB,CAACkD,IAAI,CAACA,IAAI,CAAC;MACjC4B,KAAI,CAAC7E,kBAAkB,CAACiD,IAAI,CAACA,IAAI,CAAC;IAAC;EACrC;EAEAD,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACtI,YAAY,EAAE;MACtB,IAAI,CAACnC,UAAU,GAAG,OAAO;MACzB;IACF;IAEA,MAAM0K,IAAI,GAAG,IAAIjC,IAAI,CAAC,IAAI,CAACtG,YAAY,CAAC;IACxC,MAAM0G,KAAK,GAAG,IAAIJ,IAAI,EAAE;IAExBiC,IAAI,CAAC9B,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACzBC,KAAK,CAACD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAE1BqE,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE;MAC/C/K,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BgL,eAAe,EAAEzC,IAAI,CAAChC,WAAW,EAAE;MACnC0E,QAAQ,EAAEvE,KAAK,CAACH,WAAW,EAAE;MAC7B2E,YAAY,EAAE3C,IAAI,CAAC5B,OAAO,EAAE;MAC5BwE,SAAS,EAAEzE,KAAK,CAACC,OAAO,EAAE;MAC1BpJ,OAAO,EAAEgL,IAAI,CAAC5B,OAAO,EAAE,KAAKD,KAAK,CAACC,OAAO;KAC1C,CAAC;IAEF,IAAI4B,IAAI,CAAC5B,OAAO,EAAE,KAAKD,KAAK,CAACC,OAAO,EAAE,EAAE;MACtC,IAAI,CAAC9I,UAAU,GAAG,OAAO;IAC3B,CAAC,MAAM;MACL,MAAMuN,SAAS,GAAG,IAAI9E,IAAI,CAACI,KAAK,CAAC;MACjC0E,SAAS,CAACjD,OAAO,CAACiD,SAAS,CAAChD,OAAO,EAAE,GAAG,CAAC,CAAC;MAE1C0C,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE;QACrDM,YAAY,EAAED,SAAS,CAAC7E,WAAW,EAAE;QACrC+E,aAAa,EAAEF,SAAS,CAACzE,OAAO,EAAE;QAClCxF,WAAW,EAAEoH,IAAI,CAAC5B,OAAO,EAAE,KAAKyE,SAAS,CAACzE,OAAO;OAClD,CAAC;MAEF,IAAI4B,IAAI,CAAC5B,OAAO,EAAE,KAAKyE,SAAS,CAACzE,OAAO,EAAE,EAAE;QAC1C,IAAI,CAAC9I,UAAU,GAAG,WAAW;MAC/B,CAAC,MAAM;QACL,MAAM0N,QAAQ,GAAG,IAAIjF,IAAI,CAACI,KAAK,CAAC;QAChC6E,QAAQ,CAACpD,OAAO,CAACoD,QAAQ,CAACnD,OAAO,EAAE,GAAG,CAAC,CAAC;QAExC0C,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;UACpDS,WAAW,EAAED,QAAQ,CAAChF,WAAW,EAAE;UACnCkF,YAAY,EAAEF,QAAQ,CAAC5E,OAAO,EAAE;UAChCvF,UAAU,EAAEmH,IAAI,CAAC5B,OAAO,EAAE,KAAK4E,QAAQ,CAAC5E,OAAO;SAChD,CAAC;QAEF,IAAI4B,IAAI,CAAC5B,OAAO,EAAE,KAAK4E,QAAQ,CAAC5E,OAAO,EAAE,EAAE;UACzC,IAAI,CAAC9I,UAAU,GAAG,UAAU;QAC9B,CAAC,MAAM;UACL,IAAI,CAACA,UAAU,GAAG0K,IAAI,CAACmD,kBAAkB,CAAC,OAAO,EAAE;YAAEhD,KAAK,EAAE,OAAO;YAAE/K,GAAG,EAAE;UAAS,CAAE,CAAC;QACxF;MACF;IACF;EAEF;EAEAuD,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACD,SAAS,CAAC+I,IAAI,CAACzB,IAAI,IAAIA,IAAI,CAAC9K,QAAQ,CAAC;EACnD;EAEAF,OAAOA,CAAA;IACL,IAAI,CAAC,IAAI,CAACyC,YAAY,EAAE,OAAO,KAAK;IAEpC,MAAMuI,IAAI,GAAG,IAAIjC,IAAI,CAAC,IAAI,CAACtG,YAAY,CAAC;IACxC,MAAM0G,KAAK,GAAG,IAAIJ,IAAI,EAAE;IAExBiC,IAAI,CAAC9B,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACzBC,KAAK,CAACD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAE1B,OAAO8B,IAAI,CAAC5B,OAAO,EAAE,KAAKD,KAAK,CAACC,OAAO,EAAE;EAC3C;EAEAxF,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACnB,YAAY,EAAE,OAAO,KAAK;IAEpC,MAAMuI,IAAI,GAAG,IAAIjC,IAAI,CAAC,IAAI,CAACtG,YAAY,CAAC;IACxC,MAAM0G,KAAK,GAAG,IAAIJ,IAAI,EAAE;IAExBiC,IAAI,CAAC9B,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACzBC,KAAK,CAACD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAE1B,MAAM2E,SAAS,GAAG,IAAI9E,IAAI,CAACI,KAAK,CAAC;IACjC0E,SAAS,CAACjD,OAAO,CAACiD,SAAS,CAAChD,OAAO,EAAE,GAAG,CAAC,CAAC;IAE1C,OAAOG,IAAI,CAAC5B,OAAO,EAAE,KAAKyE,SAAS,CAACzE,OAAO,EAAE;EAC/C;EAEAvF,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACpB,YAAY,EAAE,OAAO,KAAK;IAEpC,MAAMuI,IAAI,GAAG,IAAIjC,IAAI,CAAC,IAAI,CAACtG,YAAY,CAAC;IACxC,MAAM0G,KAAK,GAAG,IAAIJ,IAAI,EAAE;IAExBiC,IAAI,CAAC9B,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACzBC,KAAK,CAACD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAE1B,MAAM8E,QAAQ,GAAG,IAAIjF,IAAI,CAACI,KAAK,CAAC;IAChC6E,QAAQ,CAACpD,OAAO,CAACoD,QAAQ,CAACnD,OAAO,EAAE,GAAG,CAAC,CAAC;IAExC,OAAOG,IAAI,CAAC5B,OAAO,EAAE,KAAK4E,QAAQ,CAAC5E,OAAO,EAAE;EAC9C;EAEA6C,sBAAsBA,CAAA;IACpB,IAAI,CAACvI,SAAS,CAACyE,OAAO,CAAC6C,IAAI,IAAG;MAC5B,IAAIA,IAAI,CAAC9K,QAAQ,IAAI8K,IAAI,CAAC7K,YAAY,EAAE;QACtC6K,IAAI,CAAC3L,oBAAoB,GAAG,CAAC;QAC7B2L,IAAI,CAACe,WAAW,GAAG,CAAC;QACpBf,IAAI,CAACgB,eAAe,GAAG,CAAC;QACxB;MACF;MAEAhB,IAAI,CAACe,WAAW,GAAG,CAAC;MACpBf,IAAI,CAACgB,eAAe,GAAG,CAAC;MACxBhB,IAAI,CAAC3L,oBAAoB,GAAG,CAAC;IAE/B,CAAC,CAAC;EACJ;EAEAyI,iBAAiBA,CAAC8D,OAAe;IAC/B,IAAI,CAAC,IAAI,CAACnN,OAAO,IAAI,CAAC,IAAI,CAAC6D,MAAM,EAAE;IAGnC,IAAI,CAACgE,aAAa,CAACQ,IAAI,CACrB,IAAI,CAACN,YAAY,CAAC4H,cAAc,CAAC,IAAI,CAAC3P,OAAO,CAAC,CAACuI,SAAS,CACtDlD,MAAM,IAAG;MACP,IAAIA,MAAM,IAAIA,MAAM,CAACqC,MAAM,GAAG,CAAC,EAAE;QAE/B,MAAM1D,YAAY,GAAG,IAAIsG,IAAI,CAAC6C,OAAO,CAAC;QACtC,MAAMyC,cAAc,GAAGvK,MAAM,CAACjG,MAAM,CAACyQ,KAAK,IAAG;UAC3C,MAAMC,WAAW,GAAG,IAAIxF,IAAI,CAACuF,KAAK,CAACE,OAAO,CAAC;UAC3CD,WAAW,CAACrF,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAChCzG,YAAY,CAACyG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UACjC,OAAOqF,WAAW,IAAI9L,YAAY;QACpC,CAAC,CAAC;QAGF,MAAMgM,gBAAgB,GAAGJ,cAAc,CAAC1Q,GAAG,CAAC2Q,KAAK,IAAG;UAClD,IAAI,CAACA,KAAK,CAAC9I,EAAE,IAAI,CAAC,IAAI,CAAClD,MAAM,EAAE;YAC7B,OAAO1E,EAAE,CAAC,IAAI,CAAC;UACjB;UAEA,OAAO,IAAI,CAAC4I,YAAY,CAACkI,qBAAqB,CAACJ,KAAK,CAAC9I,EAAE,EAAE,IAAI,CAAClD,MAAM,EAAE,IAAIyG,IAAI,CAAC6C,OAAO,CAAC,CAAC,CAACzE,IAAI,CAC3FzJ,SAAS,CAACiR,QAAQ,IAAG;YACnB,IAAI,CAACL,KAAK,CAAC9I,EAAE,EAAE;cACb,OAAO5H,EAAE,CAAC,EAAW,CAAC;YACxB;YACA,OAAO,IAAI,CAAC4I,YAAY,CAACoI,4BAA4B,CAACN,KAAK,CAAC9I,EAAE,EAAE,IAAIuD,IAAI,CAAC6C,OAAO,CAAC,CAAC,CAACzE,IAAI,CACrFxJ,GAAG,CAACkR,WAAW,IAAG;cAChB,MAAMpM,YAAY,GAAG,IAAIsG,IAAI,CAAC6C,OAAO,CAAC;cACtCnJ,YAAY,CAACyG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;cAEjC,MAAM4F,eAAe,GAAG,IAAI,CAAC7J,OAAO,CAACpH,MAAM,CAAC0L,MAAM,IAAG;gBACnD,IAAI,CAACA,MAAM,CAACT,WAAW,EAAE,OAAO,KAAK;gBACrC,MAAMgD,QAAQ,GAAG,IAAI/C,IAAI,CAACQ,MAAM,CAACT,WAAW,CAAC;gBAC7CgD,QAAQ,CAAC5C,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAC7B,OAAO4C,QAAQ,GAAGrJ,YAAY;cAChC,CAAC,CAAC;cAEF,MAAMsM,gBAAgB,GAAGF,WAAW,CAAChR,MAAM,CAACmR,CAAC,IAC3CA,CAAC,CAAC/M,SAAS,IACX6M,eAAe,CAACrC,IAAI,CAAC9D,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKoG,CAAC,CAACpG,OAAO,CAAC,CACnD,CAACzC,MAAM;cAGR,IAAI6G,OAAO,GAAG,EAAE;cAEhB,OAAO;gBACLxH,EAAE,EAAE8I,KAAK,CAAC9I,EAAE;gBACZ7G,IAAI,EAAE2P,KAAK,CAAC3P,IAAI;gBAChBwD,WAAW,EAAEmM,KAAK,CAACnM,WAAW,IAAI,EAAE;gBACpCD,KAAK,EAAEoM,KAAK,CAACpM,KAAK;gBAClB0D,QAAQ,EAAE0I,KAAK,CAAC1I,QAAQ;gBACxBD,UAAU,EAAE2I,KAAK,CAAC3I,UAAU;gBAC5B1E,UAAU,EAAEqN,KAAK,CAACrN,UAAU;gBAC5BG,SAAS,EAAEkN,KAAK,CAAClN,SAAS;gBAC1BP,cAAc,EAAE8N,QAAQ,GAAGA,QAAQ,CAAC9N,cAAc,GAAG,CAAC;gBACtDoB,SAAS,EAAE0M,QAAQ,GAAGA,QAAQ,CAAC1M,SAAS,GAAG,KAAK;gBAChDL,MAAM,EAAE0M,KAAK,CAAC1M,MAAM;gBACpBS,aAAa,EAAE,KAAK;gBACpBZ,iBAAiB,EAAEsN,gBAAgB;gBACnCrN,aAAa,EAAEoN,eAAe,CAAC3I,MAAM;gBACrC6G,OAAO,EAAEA;eACD;YACZ,CAAC,CAAC,CACH;UACH,CAAC,CAAC,CACH;QACH,CAAC,CAAC,CAACnP,MAAM,CAACoR,GAAG,IAAIA,GAAG,KAAK,IAAI,CAAwB;QAErD,IAAIR,gBAAgB,CAACtI,MAAM,GAAG,CAAC,EAAE;UAC/B1I,aAAa,CAACgR,gBAAgB,CAAC,CAACzH,SAAS,CACvCkI,eAAe,IAAG;YAChB,IAAI,CAACpL,MAAM,GAAGoL,eAAe;UAC/B,CAAC,EACD1E,KAAK,IAAG;YACN,IAAI,CAAC1G,MAAM,GAAG,EAAE;UAClB,CAAC,CACF;QACH,CAAC,MAAM;UACL,IAAI,CAACA,MAAM,GAAG,EAAE;QAClB;MACF,CAAC,MAAM;QACL,IAAI,CAACA,MAAM,GAAG,EAAE;MAClB;IACF,CAAC,EACD0G,KAAK,IAAG;MACN,IAAI,CAAC1G,MAAM,GAAG,EAAE;IAClB,CAAC,CACF,CACF;EACH;EAEAiE,kBAAkBA,CAAC6D,OAAe;IAAA,IAAAuD,WAAA;IAChC,IAAI,CAAC,IAAI,CAAC1Q,OAAO,IAAI,CAAC,IAAI,CAAC6D,MAAM,EAAE;IAGnC,IAAI,GAAA6M,WAAA,GAAC,IAAI,CAACzQ,KAAK,cAAAyQ,WAAA,eAAVA,WAAA,CAAY3M,iBAAiB,GAAE;MAClC,IAAI,CAAC8C,UAAU,GAAG,IAAI;MACtB;IACF;IAEA,MAAM6D,KAAK,GAAG,IAAIJ,IAAI,EAAE;IACxBI,KAAK,CAACD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1B,MAAMzG,YAAY,GAAG,IAAIsG,IAAI,CAAC6C,OAAO,CAAC;IACtCnJ,YAAY,CAACyG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAEjC,IAAIzG,YAAY,CAAC2G,OAAO,EAAE,KAAKD,KAAK,CAACC,OAAO,EAAE,EAAE;MAC9C,IAAI,CAAC9D,UAAU,GAAG,IAAI;MACtB;IACF;IAEA,IAAI,CAACgB,aAAa,CAACQ,IAAI,CACrB,IAAI,CAACN,YAAY,CAAC4I,iBAAiB,CAAC,IAAI,CAAC3Q,OAAO,CAAC,CAACuI,SAAS,CACzDqI,SAAS,IAAG;MACV,IAAIA,SAAS,EAAE;QAEb,IAAI,CAAC7I,YAAY,CAAC8I,yBAAyB,CAACD,SAAS,CAACE,gBAAgB,CAAC,CAACvI,SAAS,CAC/EwI,QAAQ,IAAG;UACT,IAAIA,QAAQ,EAAE;YAEZ,IAAI,CAACH,SAAS,CAAC7J,EAAE,IAAI,CAAC,IAAI,CAAClD,MAAM,EAAE;cACjC;YACF;YAEA,IAAI,CAACkE,YAAY,CAACiJ,6BAA6B,CAACJ,SAAS,CAAC7J,EAAE,EAAE,IAAI,CAAClD,MAAM,CAAC,CAAC0E,SAAS,CAClF0I,MAAM,IAAG;cAEP,IAAI,CAACL,SAAS,CAAC7J,EAAE,EAAE;gBACjB;cACF;cACA,IAAI,CAACgB,YAAY,CAACmJ,+BAA+B,CAACN,SAAS,CAAC7J,EAAE,CAAC,CAACwB,SAAS,CACvE4I,WAAW,IAAG;gBACZ,MAAMzG,KAAK,GAAG,IAAIJ,IAAI,EAAE;gBACxBI,KAAK,CAACD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAE1B,MAAM4F,eAAe,GAAG,IAAI,CAAC7J,OAAO,CAACpH,MAAM,CAAC0L,MAAM,IAAG;kBACnD,IAAI,CAACA,MAAM,CAACT,WAAW,EAAE,OAAO,KAAK;kBACrC,MAAMgD,QAAQ,GAAG,IAAI/C,IAAI,CAACQ,MAAM,CAACT,WAAW,CAAC;kBAC7CgD,QAAQ,CAAC5C,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;kBAC7B,OAAO4C,QAAQ,GAAG3C,KAAK;gBACzB,CAAC,CAAC;gBAEF,MAAM0G,iBAAiB,GAAGf,eAAe,CAACnR,GAAG,CAACgL,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC;gBAE7D,MAAMkH,QAAQ,GAAG,IAAI/G,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAEvD,MAAM8F,gBAAgB,GAAGa,WAAW,CAAC/R,MAAM,CAACkS,CAAC,IAC3CA,CAAC,CAAC9N,SAAS,IACX4N,iBAAiB,CAACG,QAAQ,CAACD,CAAC,CAACE,SAAS,CAAC,IACtC,OAAOF,CAAC,CAACG,YAAY,KAAK,QAAQ,IAAIH,CAAC,CAACG,YAAY,KAAKJ,QAAS,CACpE,CAAC3J,MAAM;gBAER,MAAMgK,YAAY,GAAGrB,eAAe,CAAC3I,MAAM;gBAE3C,IAAI,CAACqJ,QAAQ,CAAChK,EAAE,EAAE;kBAChB;gBACF;gBAEA,IAAI,CAACF,UAAU,GAAG;kBAChBE,EAAE,EAAE6J,SAAS,CAAC7J,EAAE,IAAI,EAAE;kBACtB5D,MAAM,EAAEyN,SAAS,CAACzN,MAAM;kBACxBH,iBAAiB,EAAEsN,gBAAgB;kBACnCrN,aAAa,EAAEyO,YAAY;kBAC3BC,aAAa,EAAE;oBACb5K,EAAE,EAAEgK,QAAQ,CAAChK,EAAE,IAAI,EAAE;oBACrB7G,IAAI,EAAE6Q,QAAQ,CAAC7Q,IAAI;oBACnBwD,WAAW,EAAEqN,QAAQ,CAACrN,WAAW,IAAI,EAAE;oBACvCD,KAAK,EAAEsN,QAAQ,CAACtN,KAAK;oBACrBjB,UAAU,EAAEuO,QAAQ,CAACvO,UAAU;oBAC/BG,SAAS,EAAEoO,QAAQ,CAACpO;;iBAEvB;gBAED,IAAI,CAACmE,YAAY,GAAGmK,MAAM,GAAG;kBAC3BlK,EAAE,EAAEkK,MAAM,CAAClK,EAAE,IAAI,EAAE;kBACnBvD,SAAS,EAAEyN,MAAM,CAACzN,SAAS;kBAC3BpB,cAAc,EAAE6O,MAAM,CAAC7O;iBACxB,GAAG;kBACF2E,EAAE,EAAE,EAAE;kBACNvD,SAAS,EAAE,KAAK;kBAChBpB,cAAc,EAAE;iBACjB;cACH,CAAC,EACD2J,KAAK,IAAG,CACR,CAAC,CACF;YACH,CAAC,EACDA,KAAK,IAAG;cACN,IAAI,CAACjF,YAAY,GAAG;gBAClBC,EAAE,EAAE,EAAE;gBACNvD,SAAS,EAAE,KAAK;gBAChBpB,cAAc,EAAE;eACjB;YACH,CAAC,CACF;UACH;QACF,CAAC,EACD2J,KAAK,IAAG,CACR,CAAC,CACF;MACH,CAAC,MAAM;QACL,IAAI,CAAClF,UAAU,GAAG,IAAI;MACxB;IACF,CAAC,EACDkF,KAAK,IAAG;MACN,IAAI,CAAClF,UAAU,GAAG,IAAI;IACxB,CAAC,CACF,CACF;EACH;EAEMvE,mBAAmBA,CAACuN,KAAY,EAAE1G,KAAU;IAAA,IAAAyI,MAAA;IAAA,OAAAxD,iBAAA;MAChD,IAAI,CAACwD,MAAI,CAAC5R,OAAO,IAAI,CAAC4R,MAAI,CAAC/N,MAAM,EAAE;MAEnC,MAAMgO,QAAQ,GAAG1I,KAAK,CAAC2I,MAAM,GAAG3I,KAAK,CAAC2I,MAAM,CAAC9L,KAAK,GAAG8F,QAAQ,CAAC3C,KAAK,CAAC4I,MAAM,CAAC/L,KAAK,CAAC;MAEjF,IAAI4L,MAAI,CAACjL,gBAAgB,EAAE;QACzBkJ,KAAK,CAACtB,OAAO,GAAG,gFAAgF;QAChGsB,KAAK,CAACzN,cAAc,GAAGyN,KAAK,CAACzN,cAAc;QAC3C;MACF;MAGA,MAAM4B,YAAY,GAAG4N,MAAI,CAAC5N,YAAY,GAAG,IAAIsG,IAAI,CAACsH,MAAI,CAAC5N,YAAY,CAAC,GAAG,IAAIsG,IAAI,EAAE;MAEjF,IAAI;QACF,MAAM0H,MAAM,SAASJ,MAAI,CAAC7J,YAAY,CAACkK,0BAA0B,CAC/DpC,KAAK,CAAC9I,EAAE,EACR6K,MAAI,CAAC/N,MAAM,EACXG,YAAY,EACZ6N,QAAQ,CACT;QAED,IAAIG,MAAM,CAACE,OAAO,EAAE;UAElBrC,KAAK,CAACzN,cAAc,GAAGyP,QAAQ;UAE/B,IAAIhC,KAAK,CAAC3I,UAAU,KAAK,OAAO,EAAE;YAChC2I,KAAK,CAACrM,SAAS,GAAGqO,QAAQ,IAAIhC,KAAK,CAACrN,UAAU;UAChD,CAAC,MAAM;YACLqN,KAAK,CAACrM,SAAS,GAAGqO,QAAQ,GAAGhC,KAAK,CAACrN,UAAU;UAC/C;UAEAoP,MAAI,CAACvI,iBAAiB,CAACuI,MAAI,CAAC5N,YAAY,CAAC;QAC3C,CAAC,MAAM;UACL6L,KAAK,CAACtB,OAAO,GAAGyD,MAAM,CAACzD,OAAO;UAE9BsB,KAAK,CAACzN,cAAc,GAAGyN,KAAK,CAACzN,cAAc;QAC7C;MACF,CAAC,CAAC,OAAO2J,KAAK,EAAE;QAEd8D,KAAK,CAACzN,cAAc,GAAGyN,KAAK,CAACzN,cAAc;QAE3CyN,KAAK,CAACtB,OAAO,GAAG,kDAAkD;MACpE;IAAC;EACH;EAEA/I,iBAAiBA,CAAC2D,KAAY;IAC5BA,KAAK,CAACgJ,cAAc,EAAE;IAEtB,IAAI,CAAClL,QAAQ,GAAG;MACdxD,KAAK,EAAE,IAAI;MACXvD,IAAI,EAAE,EAAE;MACRwD,WAAW,EAAE,EAAE;MACfwD,UAAU,EAAE,OAAoB;MAChCC,QAAQ,EAAE,EAAmB;MAC7BC,QAAQ,EAAE,OAAwB;MAClC5E,UAAU,EAAE,CAAC;MACbG,SAAS,EAAE,OAAwB;MACnC0E,WAAW,EAAE;KACd;IAED,IAAI,CAACC,UAAU,CAACoC,OAAO,CAAC/H,GAAG,IAAIA,GAAG,CAACoE,QAAQ,GAAG,KAAK,CAAC;IACpD,IAAI,CAACwB,WAAW,CAACmC,OAAO,CAAC/H,GAAG,IAAIA,GAAG,CAACoE,QAAQ,GAAG,KAAK,CAAC;IAErD,IAAI,CAACiB,iBAAiB,GAAG,IAAI;EAC/B;EAEAoL,kBAAkBA,CAAA;IAChB,IAAI,CAACpL,iBAAiB,GAAG,KAAK;EAChC;EAEAqL,YAAYA,CAAA;IACV,IAAI,IAAI,CAACrS,OAAO,EAAE;MAChBsS,MAAM,CAACC,IAAI,CAAC,WAAW,IAAI,CAACvS,OAAO,WAAW,EAAE,QAAQ,CAAC;IAC3D;EACF;EAEAmE,mBAAmBA,CAAC0H,SAAiB;IAEnC,MAAM8B,aAAa,GAAG,IAAI,CAACjH,UAAU,GAAGmF,SAAS;IACjD,IAAI,CAACnF,UAAU,GAAGiH,aAAa;IAE/B,IAAI,CAACzF,MAAM,CAACe,QAAQ,CAAC,EAAE,EAAE;MACvB4E,UAAU,EAAE,IAAI,CAAC5F,KAAK;MACtBc,WAAW,EAAE;QACX+E,WAAW,EAAEH,aAAa;QAC1BpB,IAAI,EAAE,IAAI,CAACvI;OACZ;MACD4K,mBAAmB,EAAE;KACtB,CAAC;IAEF,MAAMlE,KAAK,GAAG,IAAIJ,IAAI,EAAE;IACxBI,KAAK,CAACD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAE1B,MAAMyB,SAAS,GAAG,IAAI5B,IAAI,CAACI,KAAK,CAAC;IACjCwB,SAAS,CAACC,OAAO,CAACzB,KAAK,CAAC0B,OAAO,EAAE,GAAI,IAAI,CAAC1F,UAAU,GAAG,CAAE,CAAC;IAE1D,IAAI,CAAC2F,iBAAiB,CAACH,SAAS,CAAC;IAEjC,MAAM8C,eAAe,GAAG,IAAI,CAAC/J,SAAS,CAACgF,IAAI,CAAC4E,CAAC,IAAIA,CAAC,CAACtC,IAAI,KAAK,IAAI,CAACvI,YAAY,CAAC;IAC9E,IAAIgL,eAAe,KAAKA,eAAe,CAACvN,QAAQ,IAAIuN,eAAe,CAACtN,YAAY,CAAC,EAAE;MACjF,MAAMuM,SAAS,GAAG,IAAI,CAAChJ,SAAS,CAACgF,IAAI,CAACsC,IAAI,IAAI,CAACA,IAAI,CAAC9K,QAAQ,IAAI,CAAC8K,IAAI,CAAC7K,YAAY,CAAC;MACnF,IAAIuM,SAAS,EAAE;QACb,IAAI,CAACC,UAAU,CAACD,SAAS,CAAC;MAC5B;IACF;EACF;EAEM7M,iBAAiBA,CAACmL,IAAc;IAAA,IAAAiG,MAAA;IAAA,OAAApE,iBAAA;MAEpC,IAAI7B,IAAI,CAAC9K,QAAQ,EAAE;QAEjB,MAAM4M,KAAK,SAASmE,MAAI,CAACrK,eAAe,CAACmG,MAAM,CAAC;UAC9CC,OAAO,EAAE,4BAA4B;UACrCC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE;SACR,CAAC;QACF,MAAML,KAAK,CAACM,OAAO,EAAE;QAErB;MACF;MAEA,IAAIpC,IAAI,CAAC7K,YAAY,EAAE;QAErB,MAAM2M,KAAK,SAASmE,MAAI,CAACrK,eAAe,CAACmG,MAAM,CAAC;UAC9CC,OAAO,EAAE,qDAAqD;UAC9DC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE;SACR,CAAC;QACF,MAAML,KAAK,CAACM,OAAO,EAAE;QAErB;MACF;MAEA6D,MAAI,CAACtK,MAAM,CAACe,QAAQ,CAAC,EAAE,EAAE;QACvB4E,UAAU,EAAE2E,MAAI,CAACvK,KAAK;QACtBc,WAAW,EAAE;UAAEwD,IAAI,EAAEA,IAAI,CAACA;QAAI,CAAE;QAChCqC,mBAAmB,EAAE;OACtB,CAAC;MAEF4D,MAAI,CAACvN,SAAS,CAACyE,OAAO,CAACmF,CAAC,IAAIA,CAAC,CAACrN,UAAU,GAAGqN,CAAC,CAACtC,IAAI,KAAKA,IAAI,CAACA,IAAI,CAAC;MAChEiG,MAAI,CAACxO,YAAY,GAAGuI,IAAI,CAACA,IAAI;MAG7BiG,MAAI,CAAClG,gBAAgB,EAAE;MAEvBkG,MAAI,CAACnJ,iBAAiB,CAACkD,IAAI,CAACA,IAAI,CAAC;MACjCiG,MAAI,CAAClJ,kBAAkB,CAACiD,IAAI,CAACA,IAAI,CAAC;IAAC;EACrC;EAEAkG,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACxL,QAAQ,CAACI,WAAW,KAAK,MAAM,EAAE;MACxC,IAAI,CAACC,UAAU,CAACoC,OAAO,CAAC/H,GAAG,IAAIA,GAAG,CAACoE,QAAQ,GAAG,KAAK,CAAC;IACtD,CAAC,MAAM,IAAI,IAAI,CAACkB,QAAQ,CAACI,WAAW,KAAK,OAAO,EAAE;MAChD,IAAI,CAACE,WAAW,CAACmC,OAAO,CAAC/H,GAAG,IAAIA,GAAG,CAACoE,QAAQ,GAAG,KAAK,CAAC;IACvD;EACF;EAEA2M,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAAC1S,OAAO,IAAI,CAAC,IAAI,CAAC6D,MAAM,EAAE;IAEnC,IAAI,CAAC,IAAI,CAACoD,QAAQ,CAAC/G,IAAI,IAAI,CAAC,IAAI,CAAC+G,QAAQ,CAACE,QAAQ,IAAI,CAAC,IAAI,CAACF,QAAQ,CAACG,QAAQ,EAAE;MAC7EuL,KAAK,CAAC,oCAAoC,CAAC;MAC3C;IACF;IAEA,IAAIC,YAAY,GAAU,EAAE;IAC5B,IAAIC,iBAAiB,GAAuBvH,SAAS;IACrD,IAAIwH,kBAAkB,GAAuBxH,SAAS;IAEtD,IAAI,IAAI,CAACrE,QAAQ,CAACI,WAAW,KAAK,MAAM,EAAE;MACxCuL,YAAY,GAAG,IAAI,CAACtL,UAAU,CAAClI,MAAM,CAACuC,GAAG,IAAIA,GAAG,CAACoE,QAAQ,CAAC,CAAC7G,GAAG,CAACyC,GAAG,IAAIA,GAAG,CAACqE,KAAK,CAAC;MAChF6M,iBAAiB,GAAGD,YAAY,CAACG,IAAI,CAAC,GAAG,CAAC;IAC5C,CAAC,MAAM,IAAI,IAAI,CAAC9L,QAAQ,CAACI,WAAW,KAAK,OAAO,EAAE;MAChDuL,YAAY,GAAG,IAAI,CAACrL,WAAW,CAACnI,MAAM,CAACuC,GAAG,IAAIA,GAAG,CAACoE,QAAQ,CAAC,CAAC7G,GAAG,CAACyC,GAAG,IAAIA,GAAG,CAACqE,KAAK,CAAC;MACjF8M,kBAAkB,GAAGF,YAAY,CAACG,IAAI,CAAC,GAAG,CAAC;IAC7C;IAEA,MAAMC,SAAS,GAAG;MAChB,GAAG,IAAI,CAAC/L,QAAQ;MAChB8D,QAAQ,EAAE,IAAI,CAAC/K,OAAiB;MAChC6S,iBAAiB;MACjBC;KACD;IAGD,IAAI,CAAC/K,YAAY,CAAC2K,gBAAgB,CAACM,SAAS,CAAC,CAC1CC,IAAI,CAACC,OAAO,IAAG;MACd,IAAI,CAAC7J,iBAAiB,CAAC,IAAI,CAACrF,YAAY,CAAC;IAC3C,CAAC,CAAC,CACDmP,KAAK,CAACpH,KAAK,IAAG;MACb4G,KAAK,CAAC,0BAA0B,GAAG5G,KAAK,CAACwC,OAAO,CAAC;IACnD,CAAC,CAAC;IAEJ,IAAI,CAAC6D,kBAAkB,EAAE;IACzB,IAAI,CAACnL,QAAQ,GAAG;MACdxD,KAAK,EAAE,IAAI;MACXvD,IAAI,EAAE,EAAE;MACRwD,WAAW,EAAE,EAAE;MACfwD,UAAU,EAAE,OAAoB;MAChCC,QAAQ,EAAE,EAAmB;MAC7BC,QAAQ,EAAE,OAAwB;MAClC5E,UAAU,EAAE,CAAC;MACbG,SAAS,EAAE,OAAwB;MACnC0E,WAAW,EAAE;KACd;EACH;EAEA+L,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACpT,OAAO,IAAI,CAAC,IAAI,CAAC6D,MAAM,IAAI,CAAC,IAAI,CAACgD,UAAU,IAAI,IAAI,CAACF,gBAAgB,EAAE;IAEhF,IAAI,IAAI,CAACG,YAAY,CAACtD,SAAS,EAAE;MAC/B,IAAI,CAACsD,YAAY,CAAC1E,cAAc,GAAG,CAAC;MACpC,IAAI,CAAC0E,YAAY,CAACtD,SAAS,GAAG,KAAK;IACrC,CAAC,MAAM;MACL,IAAI,CAACsD,YAAY,CAAC1E,cAAc,GAAG,IAAI,CAACyE,UAAU,CAAC8K,aAAa,CAACnP,UAAU;MAC3E,IAAI,CAACsE,YAAY,CAACtD,SAAS,GAAG,IAAI;IACpC;IAGA,IAAI,IAAI,CAACsD,YAAY,CAACC,EAAE,EAAE;MACxB,IAAI,CAACgB,YAAY,CAACsL,gCAAgC,CAChD,IAAI,CAACvM,YAAY,CAACC,EAAE,EACpB,IAAI,CAACD,YAAY,CAACtD,SAAS,EAC3B,IAAI,CAACsD,YAAY,CAAC1E,cAAc,CACjC,CAAC6Q,IAAI,CAAC,MAAK;QAEV,IAAI,CAAC3J,kBAAkB,CAAC,IAAI,CAACtF,YAAY,CAAC;MAC5C,CAAC,CAAC,CAACmP,KAAK,CAACpH,KAAK,IAAG,CACjB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAAChE,YAAY,CAACuL,gCAAgC,CAChD,IAAI,CAACzM,UAAU,CAACE,EAAE,EAClB,IAAI,CAAClD,MAAM,EACX,IAAI,CAACiD,YAAY,CAACtD,SAAS,EAC3B,IAAI,CAACsD,YAAY,CAAC1E,cAAc,CACjC,CAAC6Q,IAAI,CAAEM,QAAQ,IAAI;QAClB,IAAI,CAACzM,YAAY,CAACC,EAAE,GAAGwM,QAAQ;QAE/B,IAAI,CAACjK,kBAAkB,CAAC,IAAI,CAACtF,YAAY,CAAC;MAC5C,CAAC,CAAC,CAACmP,KAAK,CAACpH,KAAK,IAAG,CACjB,CAAC,CAAC;IACJ;EACF;EAGAyH,eAAeA,CAACrM,QAAgB;IAC9B,QAAQA,QAAQ,CAAClB,WAAW,EAAE;MAC5B,KAAK,UAAU;QAAE,OAAO,IAAI;MAC5B,KAAK,OAAO;QAAE,OAAO,IAAI;MACzB,KAAK,QAAQ;QAAE,OAAO,IAAI;MAC1B,KAAK,WAAW;QAAE,OAAO,IAAI;MAC7B;QAAS,OAAO,IAAI;IACtB;EACF;EAEAwN,gBAAgBA,CAACtM,QAAgB;IAC/B,QAAQA,QAAQ,CAAClB,WAAW,EAAE;MAC5B,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC,KAAK,OAAO;QAAE,OAAO,SAAS;MAC9B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC;QAAS,OAAO,SAAS;IAC3B;EACF;EAEAyN,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACzT,KAAK,EAAE,OAAO,CAAC;IAEzB,OAAO,IAAI,CAACA,KAAK,CAAC0T,WAAW,GACtB,IAAI,CAAC1T,KAAK,CAAC2T,QAAQ,GACnB,IAAI,CAAC3T,KAAK,CAAC4T,SAAS,GACpB,IAAI,CAAC5T,KAAK,CAAC6T,YAAY;EAChC;EAEAC,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACvN,OAAO,CAACwN,MAAM,CAAC,CAACC,KAAK,EAAEnJ,MAAM,KAAKmJ,KAAK,IAAInJ,MAAM,CAACW,gBAAgB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAC1F;EAEAyI,qBAAqBA,CAACC,EAAU;IAC9B,IAAI,IAAI,CAAC1N,UAAU,IAAI,CAAC,EAAE,OAAO,CAAC;IAClC,OAAO2N,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,KAAK,CAAEH,EAAE,GAAG,IAAI,CAAC1N,UAAU,GAAI,GAAG,CAAC,CAAC;EAChE;EAEA8N,sBAAsBA,CAACC,MAA6C;IAClE,IAAI,CAACA,MAAM,EAAE;MACX;IACF;IAEA,IAAIC,aAA0B;IAC9B,IAAIC,WAAW,GAAG,CAAC;IACnB,IAAIC,QAAQ,GAAG,CAAC;IAChB,IAAIC,QAAQ,GAAG,GAAG;IAClB,IAAIC,aAAa,GAAG,EAAE;IAEtB,IAAIL,MAAM,YAAYM,gBAAgB,EAAE;MACtCL,aAAa,GAAGD,MAAM;MACtBK,aAAa,GAAGL,MAAM,CAACO,YAAY,CAAC,eAAe,CAAC,IAAI,EAAE;MAC1DL,WAAW,GAAG5I,QAAQ,CAAC0I,MAAM,CAACxO,KAAK,CAAC;MACpC2O,QAAQ,GAAG7I,QAAQ,CAAC0I,MAAM,CAACH,GAAG,CAAC;MAC/BO,QAAQ,GAAG9I,QAAQ,CAAC0I,MAAM,CAACQ,GAAG,CAAC;IACjC,CAAC,MAAM,IAAIR,MAAM,YAAYS,WAAW,IAAIT,MAAM,CAACU,OAAO,KAAK,WAAW,EAAE;MAC1ET,aAAa,GAAGD,MAAM;MACtBK,aAAa,GAAGL,MAAM,CAACO,YAAY,CAAC,eAAe,CAAC,IAAI,EAAE;MAE1D,MAAMI,SAAS,GAAGX,MAAM,CAACO,YAAY,CAAC,OAAO,CAAC,IAAI,GAAG;MACrD,MAAMK,OAAO,GAAGZ,MAAM,CAACO,YAAY,CAAC,KAAK,CAAC,IAAI,GAAG;MACjD,MAAMM,OAAO,GAAGb,MAAM,CAACO,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK;MAEnDL,WAAW,GAAG5I,QAAQ,CAACqJ,SAAS,CAAC;MACjCR,QAAQ,GAAG7I,QAAQ,CAACsJ,OAAO,CAAC;MAC5BR,QAAQ,GAAG9I,QAAQ,CAACuJ,OAAO,CAAC;IAC9B,CAAC,MAAM;MACL;IACF;IAEA,IAAI,CAACR,aAAa,EAAE;MAClB;IACF;IAEA,MAAMS,UAAU,GAAGV,QAAQ,GAAGD,QAAQ,GACnC,CAACD,WAAW,GAAGC,QAAQ,KAAKC,QAAQ,GAAGD,QAAQ,CAAC,GAAI,GAAG,GAAG,CAAC;IAE9D,IAAIF,aAAa,CAACS,OAAO,KAAK,WAAW,EAAE;MACzCT,aAAa,CAACc,KAAK,CAACC,WAAW,CAAC,kBAAkB,EAAE,GAAGF,UAAU,GAAG,CAAC;IACvE,CAAC,MAAM;MACLb,aAAa,CAACc,KAAK,CAACE,UAAU,GAC5B,iDAAiDH,UAAU,cAAcA,UAAU,kBAAkB;IACzG;EACF;;mBA5gCWhP,eAAe;;mCAAfA,gBAAe;AAAA;;QAAfA,gBAAe;EAAAoP,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCoC5BpW,EA7IA,CAAAyB,UAAA,IAAA6U,iDAAA,oCAIsB,IAAAC,8BAAA,mBAIsB,IAAAC,8BAAA,iBAqIc;MAUtDxW,EAFJ,CAAAQ,cAAA,aAA+F,aAClE,cACgC;MAA/BR,EAAA,CAAAiB,UAAA,mBAAAwV,+CAAA;QAAA,OAASJ,GAAA,CAAA7D,kBAAA,EAAoB;MAAA,EAAC;MAACxS,EAAA,CAAAS,MAAA,aAAO;MAAAT,EAAA,CAAAU,YAAA,EAAO;MACvEV,EAAA,CAAAQ,cAAA,SAAI;MAAAR,EAAA,CAAAS,MAAA,0BAAmB;MAAAT,EAAA,CAAAU,YAAA,EAAK;MAC5BV,EAAA,CAAAQ,cAAA,cAA0D;MAApDR,EAAA,CAAAiB,UAAA,sBAAAyV,kDAAA;QAAA,OAAYL,GAAA,CAAAvD,gBAAA,EAAkB;MAAA,EAAC;MAG/B9S,EAFJ,CAAAQ,cAAA,cAAsC,cACZ,gBAC2E;MAAtDR,EAAA,CAAAkC,gBAAA,2BAAAyU,yDAAAvU,MAAA;QAAApC,EAAA,CAAAuC,kBAAA,CAAA8T,GAAA,CAAAhP,QAAA,CAAAxD,KAAA,EAAAzB,MAAA,MAAAiU,GAAA,CAAAhP,QAAA,CAAAxD,KAAA,GAAAzB,MAAA;QAAA,OAAAA,MAAA;MAAA,EAA4B;MACzEpC,EADE,CAAAU,YAAA,EAAiG,EAC7F;MAEJV,EADF,CAAAQ,cAAA,cAAwB,iBACuF;MAApER,EAAA,CAAAkC,gBAAA,2BAAA0U,yDAAAxU,MAAA;QAAApC,EAAA,CAAAuC,kBAAA,CAAA8T,GAAA,CAAAhP,QAAA,CAAA/G,IAAA,EAAA8B,MAAA,MAAAiU,GAAA,CAAAhP,QAAA,CAAA/G,IAAA,GAAA8B,MAAA;QAAA,OAAAA,MAAA;MAAA,EAA2B;MAExEpC,EAFI,CAAAU,YAAA,EAA6G,EACzG,EACF;MAEJV,EADF,CAAAQ,cAAA,cAAwB,iBACG;MAAAR,EAAA,CAAAS,MAAA,mBAAW;MAAAT,EAAA,CAAAU,YAAA,EAAQ;MAC5CV,EAAA,CAAAQ,cAAA,oBAAuH;MAAzER,EAAA,CAAAkC,gBAAA,2BAAA2U,4DAAAzU,MAAA;QAAApC,EAAA,CAAAuC,kBAAA,CAAA8T,GAAA,CAAAhP,QAAA,CAAAvD,WAAA,EAAA1B,MAAA,MAAAiU,GAAA,CAAAhP,QAAA,CAAAvD,WAAA,GAAA1B,MAAA;QAAA,OAAAA,MAAA;MAAA,EAAkC;MAClFpC,EADyH,CAAAU,YAAA,EAAW,EAC9H;MAEJV,EADF,CAAAQ,cAAA,cAAwB,iBACE;MAAAR,EAAA,CAAAS,MAAA,kBAAU;MAAAT,EAAA,CAAAU,YAAA,EAAQ;MAC1CV,EAAA,CAAAQ,cAAA,kBAA4E;MAAlCR,EAAA,CAAAkC,gBAAA,2BAAA4U,0DAAA1U,MAAA;QAAApC,EAAA,CAAAuC,kBAAA,CAAA8T,GAAA,CAAAhP,QAAA,CAAAC,UAAA,EAAAlF,MAAA,MAAAiU,GAAA,CAAAhP,QAAA,CAAAC,UAAA,GAAAlF,MAAA;QAAA,OAAAA,MAAA;MAAA,EAAiC;MACzEpC,EAAA,CAAAQ,cAAA,kBAAsB;MAAAR,EAAA,CAAAS,MAAA,mBAAW;MAAAT,EAAA,CAAAU,YAAA,EAAS;MAC1CV,EAAA,CAAAQ,cAAA,kBAAqB;MAAAR,EAAA,CAAAS,MAAA,kBAAU;MAEnCT,EAFmC,CAAAU,YAAA,EAAS,EACjC,EACL;MAEJV,EADF,CAAAQ,cAAA,cAAwB,iBACA;MAAAR,EAAA,CAAAS,MAAA,gBAAQ;MAAAT,EAAA,CAAAU,YAAA,EAAQ;MACtCV,EAAA,CAAAQ,cAAA,kBAA+E;MAAzCR,EAAA,CAAAkC,gBAAA,2BAAA6U,0DAAA3U,MAAA;QAAApC,EAAA,CAAAuC,kBAAA,CAAA8T,GAAA,CAAAhP,QAAA,CAAAE,QAAA,EAAAnF,MAAA,MAAAiU,GAAA,CAAAhP,QAAA,CAAAE,QAAA,GAAAnF,MAAA;QAAA,OAAAA,MAAA;MAAA,EAA+B;MACnEpC,EAAA,CAAAQ,cAAA,kBAAiB;MAAAR,EAAA,CAAAS,MAAA,yBAAiB;MAAAT,EAAA,CAAAU,YAAA,EAAS;MAC3CV,EAAA,CAAAQ,cAAA,kBAAyB;MAAAR,EAAA,CAAAS,MAAA,gBAAQ;MAAAT,EAAA,CAAAU,YAAA,EAAS;MAC1CV,EAAA,CAAAQ,cAAA,kBAAsB;MAAAR,EAAA,CAAAS,MAAA,aAAK;MAAAT,EAAA,CAAAU,YAAA,EAAS;MACpCV,EAAA,CAAAQ,cAAA,kBAAuB;MAAAR,EAAA,CAAAS,MAAA,cAAM;MAAAT,EAAA,CAAAU,YAAA,EAAS;MACtCV,EAAA,CAAAQ,cAAA,kBAA0B;MAAAR,EAAA,CAAAS,MAAA,iBAAS;MAEvCT,EAFuC,CAAAU,YAAA,EAAS,EACrC,EACL;MAEJV,EADF,CAAAQ,cAAA,cAAwB,iBACA;MAAAR,EAAA,CAAAS,MAAA,gBAAQ;MAAAT,EAAA,CAAAU,YAAA,EAAQ;MACtCV,EAAA,CAAAQ,cAAA,kBAA+E;MAAzCR,EAAA,CAAAkC,gBAAA,2BAAA8U,0DAAA5U,MAAA;QAAApC,EAAA,CAAAuC,kBAAA,CAAA8T,GAAA,CAAAhP,QAAA,CAAAG,QAAA,EAAApF,MAAA,MAAAiU,GAAA,CAAAhP,QAAA,CAAAG,QAAA,GAAApF,MAAA;QAAA,OAAAA,MAAA;MAAA,EAA+B;MACnEpC,EAAA,CAAAQ,cAAA,kBAAsB;MAAAR,EAAA,CAAAS,MAAA,aAAK;MAAAT,EAAA,CAAAU,YAAA,EAAS;MACpCV,EAAA,CAAAQ,cAAA,kBAAqB;MAAAR,EAAA,CAAAS,MAAA,qBAAa;MAEtCT,EAFsC,CAAAU,YAAA,EAAS,EACpC,EACL;MAEJV,EADF,CAAAQ,cAAA,eAAsC,aAC7B;MAAAR,EAAA,CAAAS,MAAA,YAAI;MAAAT,EAAA,CAAAU,YAAA,EAAQ;MAEjBV,EADF,CAAAQ,cAAA,eAAyB,iBACoF;MAApDR,EAAA,CAAAkC,gBAAA,2BAAA+U,yDAAA7U,MAAA;QAAApC,EAAA,CAAAuC,kBAAA,CAAA8T,GAAA,CAAAhP,QAAA,CAAAzE,UAAA,EAAAR,MAAA,MAAAiU,GAAA,CAAAhP,QAAA,CAAAzE,UAAA,GAAAR,MAAA;QAAA,OAAAA,MAAA;MAAA,EAAiC;MAAxFpC,EAAA,CAAAU,YAAA,EAA2G;MAC3GV,EAAA,CAAAQ,cAAA,kBAAyE;MAAjCR,EAAA,CAAAkC,gBAAA,2BAAAgV,0DAAA9U,MAAA;QAAApC,EAAA,CAAAuC,kBAAA,CAAA8T,GAAA,CAAAhP,QAAA,CAAAtE,SAAA,EAAAX,MAAA,MAAAiU,GAAA,CAAAhP,QAAA,CAAAtE,SAAA,GAAAX,MAAA;QAAA,OAAAA,MAAA;MAAA,EAAgC;MACtEpC,EAAA,CAAAQ,cAAA,kBAAsB;MAAAR,EAAA,CAAAS,MAAA,aAAK;MAAAT,EAAA,CAAAU,YAAA,EAAS;MACpCV,EAAA,CAAAQ,cAAA,kBAAsB;MAAAR,EAAA,CAAAS,MAAA,aAAK;MAAAT,EAAA,CAAAU,YAAA,EAAS;MACpCV,EAAA,CAAAQ,cAAA,kBAAkB;MAAAR,EAAA,CAAAS,MAAA,cAAM;MAAAT,EAAA,CAAAU,YAAA,EAAS;MACjCV,EAAA,CAAAQ,cAAA,kBAAmB;MAAAR,EAAA,CAAAS,MAAA,kBAAU;MAAAT,EAAA,CAAAU,YAAA,EAAS;MACtCV,EAAA,CAAAQ,cAAA,kBAAoB;MAAAR,EAAA,CAAAS,MAAA,eAAO;MAAAT,EAAA,CAAAU,YAAA,EAAS;MACpCV,EAAA,CAAAQ,cAAA,kBAAoB;MAAAR,EAAA,CAAAS,MAAA,eAAO;MAAAT,EAAA,CAAAU,YAAA,EAAS;MACpCV,EAAA,CAAAQ,cAAA,kBAAmB;MAAAR,EAAA,CAAAS,MAAA,aAAK;MAAAT,EAAA,CAAAU,YAAA,EAAS;MACjCV,EAAA,CAAAQ,cAAA,kBAAoB;MAAAR,EAAA,CAAAS,MAAA,gBAAQ;MAAAT,EAAA,CAAAU,YAAA,EAAS;MACrCV,EAAA,CAAAQ,cAAA,kBAAkB;MAAAR,EAAA,CAAAS,MAAA,aAAK;MAAAT,EAAA,CAAAU,YAAA,EAAS;MAChCV,EAAA,CAAAQ,cAAA,kBAAmB;MAAAR,EAAA,CAAAS,MAAA,kBAAU;MAAAT,EAAA,CAAAU,YAAA,EAAS;MACtCV,EAAA,CAAAQ,cAAA,kBAAsB;MAAAR,EAAA,CAAAS,MAAA,cAAM;MAGlCT,EAHkC,CAAAU,YAAA,EAAS,EAC9B,EACL,EACF;MAEJV,EADF,CAAAQ,cAAA,cAAwB,iBACG;MAAAR,EAAA,CAAAS,MAAA,iBAAS;MAAAT,EAAA,CAAAU,YAAA,EAAQ;MAC1CV,EAAA,CAAAQ,cAAA,kBAA+G;MAAnER,EAAA,CAAAkC,gBAAA,2BAAAiV,0DAAA/U,MAAA;QAAApC,EAAA,CAAAuC,kBAAA,CAAA8T,GAAA,CAAAhP,QAAA,CAAAI,WAAA,EAAArF,MAAA,MAAAiU,GAAA,CAAAhP,QAAA,CAAAI,WAAA,GAAArF,MAAA;QAAA,OAAAA,MAAA;MAAA,EAAkC;MAACpC,EAAA,CAAAiB,UAAA,oBAAAmW,mDAAA;QAAA,OAAUf,GAAA,CAAAxD,kBAAA,EAAoB;MAAA,EAAC;MAC5G7S,EAAA,CAAAQ,cAAA,kBAAoB;MAAAR,EAAA,CAAAS,MAAA,iBAAS;MAAAT,EAAA,CAAAU,YAAA,EAAS;MACtCV,EAAA,CAAAQ,cAAA,kBAAqB;MAAAR,EAAA,CAAAS,MAAA,iCAAyB;MAAAT,EAAA,CAAAU,YAAA,EAAS;MACvDV,EAAA,CAAAQ,cAAA,kBAAsB;MAAAR,EAAA,CAAAS,MAAA,kCAA0B;MAEpDT,EAFoD,CAAAU,YAAA,EAAS,EAClD,EACL;MAEJV,EADF,CAAAQ,cAAA,eAA4I,aACnI;MAAAR,EAAA,CAAAS,MAAA,2BAAmB;MAAAT,EAAA,CAAAU,YAAA,EAAQ;MAClCV,EAAA,CAAAQ,cAAA,eAA2B;MACzBR,EAAA,CAAAyB,UAAA,KAAA4V,+BAAA,kBAAyD;MAM7DrX,EADE,CAAAU,YAAA,EAAM,EACF;MAEJV,EADF,CAAAQ,cAAA,eAA8I,aACrI;MAAAR,EAAA,CAAAS,MAAA,4BAAoB;MAAAT,EAAA,CAAAU,YAAA,EAAQ;MACnCV,EAAA,CAAAQ,cAAA,eAAiC;MAC/BR,EAAA,CAAAyB,UAAA,KAAA6V,+BAAA,kBAA0D;MAM9DtX,EADE,CAAAU,YAAA,EAAM,EACF;MACNV,EAAA,CAAAQ,cAAA,kBAAyC;MAAAR,EAAA,CAAAS,MAAA,oBAAY;MAG3DT,EAH2D,CAAAU,YAAA,EAAS,EACzD,EACH,EACF;;;MA/OHV,EAAA,CAAAE,UAAA,SAAAmW,GAAA,CAAArP,WAAA,CAAiB;MAOIhH,EAAA,CAAAW,SAAA,EAAkB;MAAlBX,EAAA,CAAAE,UAAA,UAAAmW,GAAA,CAAArP,WAAA,CAAkB;MAqIfhH,EAAA,CAAAW,SAAA,EAA6B;MAA7BX,EAAA,CAAAE,UAAA,UAAAmW,GAAA,CAAArP,WAAA,IAAAqP,GAAA,CAAA9V,OAAA,CAA6B;MAQhBP,EAAA,CAAAW,SAAA,EAAsD;MAAtDX,EAAA,CAAAuX,WAAA,YAAAlB,GAAA,CAAAjP,iBAAA,oBAAsD;MAOzCpH,EAAA,CAAAW,SAAA,GAA4B;MAA5BX,EAAA,CAAA6C,gBAAA,YAAAwT,GAAA,CAAAhP,QAAA,CAAAxD,KAAA,CAA4B;MAG9B7D,EAAA,CAAAW,SAAA,GAA2B;MAA3BX,EAAA,CAAA6C,gBAAA,YAAAwT,GAAA,CAAAhP,QAAA,CAAA/G,IAAA,CAA2B;MAKxBN,EAAA,CAAAW,SAAA,GAAkC;MAAlCX,EAAA,CAAA6C,gBAAA,YAAAwT,GAAA,CAAAhP,QAAA,CAAAvD,WAAA,CAAkC;MAItC9D,EAAA,CAAAW,SAAA,GAAiC;MAAjCX,EAAA,CAAA6C,gBAAA,YAAAwT,GAAA,CAAAhP,QAAA,CAAAC,UAAA,CAAiC;MAOrCtH,EAAA,CAAAW,SAAA,GAA+B;MAA/BX,EAAA,CAAA6C,gBAAA,YAAAwT,GAAA,CAAAhP,QAAA,CAAAE,QAAA,CAA+B;MAU/BvH,EAAA,CAAAW,SAAA,IAA+B;MAA/BX,EAAA,CAAA6C,gBAAA,YAAAwT,GAAA,CAAAhP,QAAA,CAAAG,QAAA,CAA+B;MAQZxH,EAAA,CAAAW,SAAA,GAAiC;MAAjCX,EAAA,CAAA6C,gBAAA,YAAAwT,GAAA,CAAAhP,QAAA,CAAAzE,UAAA,CAAiC;MAChD5C,EAAA,CAAAW,SAAA,EAAgC;MAAhCX,EAAA,CAAA6C,gBAAA,YAAAwT,GAAA,CAAAhP,QAAA,CAAAtE,SAAA,CAAgC;MAiB9B/C,EAAA,CAAAW,SAAA,IAAkC;MAAlCX,EAAA,CAAA6C,gBAAA,YAAAwT,GAAA,CAAAhP,QAAA,CAAAI,WAAA,CAAkC;MAMTzH,EAAA,CAAAW,SAAA,GAAoE;MAApEX,EAAA,CAAAuX,WAAA,YAAAlB,GAAA,CAAAhP,QAAA,CAAAI,WAAA,+BAAoE;MAG7FzH,EAAA,CAAAW,SAAA,GAAa;MAAbX,EAAA,CAAAE,UAAA,YAAAmW,GAAA,CAAA3O,UAAA,CAAa;MAOa1H,EAAA,CAAAW,SAAA,EAAqE;MAArEX,EAAA,CAAAuX,WAAA,YAAAlB,GAAA,CAAAhP,QAAA,CAAAI,WAAA,gCAAqE;MAG/FzH,EAAA,CAAAW,SAAA,GAAc;MAAdX,EAAA,CAAAE,UAAA,YAAAmW,GAAA,CAAA1O,WAAA,CAAc;;;iBD/HtD9I,WAAW,EAAA2Y,EAAA,CAAAC,QAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,0BAAA,EAAEhZ,YAAY,EAAAiZ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAElZ,WAAW,EAAAmZ,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,uBAAA,EAAAH,EAAA,CAAAI,oBAAA,EAAAJ,EAAA,CAAAK,mBAAA,EAAAL,EAAA,CAAAM,4BAAA,EAAAN,EAAA,CAAAO,0BAAA,EAAAP,EAAA,CAAAQ,eAAA,EAAAR,EAAA,CAAAS,oBAAA,EAAAT,EAAA,CAAAU,iBAAA,EAAAV,EAAA,CAAAW,YAAA,EAAAX,EAAA,CAAAY,OAAA,EAAAZ,EAAA,CAAAa,MAAA,EAAE3Z,YAAY,EAAA4Z,EAAA,CAAAC,UAAA,EAAEjZ,mBAAmB,EAAEC,gBAAgB,EAAAiZ,EAAA,CAAAC,uBAAA,EAAEjZ,yBAAyB;EAAAkZ,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}