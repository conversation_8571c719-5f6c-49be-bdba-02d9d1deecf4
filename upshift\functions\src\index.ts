﻿import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import Stripe from "stripe";

admin.initializeApp();

const stripe = new Stripe(
  "sk_test_51LlETOEB7UFpNfHGFTFA8OLyCMuwT9TBOjUF0Tcw9zIbdFQCV7IenK3rfXlSvXE3CNJebHJ9tLwHxEHtF4XfAJfL00tLB9aiHN",
  {
    apiVersion: "2025-03-31.basil",
  }
);

export const createCheckoutSession = functions.https.onCall(
  async (request: functions.https.CallableRequest<any>) => {
    const { priceId, planType, uid } = request.data;

    if (!uid || !priceId || !planType) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Missing required parameters."
      );
    }

    const sessionData: Stripe.Checkout.SessionCreateParams = {
      mode: "subscription",
      payment_method_types: ["card"],
      line_items: [{ price: priceId, quantity: 1 }],
      success_url: "http://localhost:8100/today",
      cancel_url: "http://localhost:8100/",
      metadata: { firebaseUID: uid },
    };

    

    const session = await stripe.checkout.sessions.create(sessionData);
    return { sessionId: session.id };
  }
);
