﻿import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { RouterModule, ActivatedRoute } from '@angular/router';
import { BadgeService } from '../../services/badge.service';
import { UserService } from '../../services/user.service';
import { UserBadges } from '../../models/friend.model';
import { Subscription, of, switchMap } from 'rxjs';
import { SupabaseService } from '../../services/supabase.service';

interface Badge {
  name: string;
  description: string;
  unlocked: boolean;
  emoji?: string;
}

@Component({
  selector: 'app-user-badges',
  templateUrl: './user-badges.page.html',
  styleUrls: ['./user-badges.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, RouterModule]
})
export class UserBadgesPage implements OnInit {
  userBadges: UserBadges | null = null;
  badgesSubscription: Subscription | null = null;
  userId: string | null = null;
  username: string | null = null;
  isCurrentUser: boolean = false;

  titleBadges: Badge[] = [];
  streakBadges: Badge[] = [];
  sidequestBadges: Badge[] = [];
  friendBadges: Badge[] = [];
  categoryBadges: Badge[] = [];

  private supabaseService = inject(SupabaseService);
  private badgeService = inject(BadgeService);
  private userService = inject(UserService);
  private route = inject(ActivatedRoute);

  constructor() {}

  ngOnInit() {
    this.route.paramMap.pipe(
      switchMap(params => {
        const paramId = params.get('id');
        if (paramId) {
          this.userId = paramId;
          return this.userService.getUser(this.userId);
        } else {
          return this.supabaseService.currentUser$.pipe(
            switchMap(authUser => {
              if (!authUser) {
                return of(null);
              }
              this.userId = authUser.id;
              this.isCurrentUser = true;
              return this.userService.getUser(this.userId);
            })
          );
        }
      }),
      switchMap(user => {
        if (!user || !this.userId) {
          return of(null);
        }

        this.username = user.username;

        this.supabaseService.currentUser$.subscribe(authUser => {
          if (authUser) {
            this.isCurrentUser = authUser.id === this.userId;
          }
        });

        return this.badgeService.getUserBadges(this.userId);
      })
    ).subscribe({
      next: (badges) => {
        if (badges) {
          this.userBadges = badges;
          this.initializeBadges(badges);
        } else if (this.isCurrentUser) {
          this.createDefaultBadges();
        } else {
          this.userBadges = {
            user_id: this.userId || '',
            badge_newbie: false,
            badge_warrior: false,
            badge_hardcore: false,
            badge_peak_performer: false,
            badge_indestructible: false,
            badge_professional: false,
            badge_streak_7_days: false,
            badge_streak_30_days: false,
            badge_streak_100_days: false,
            badge_streak_365_days: false,
            badge_sidequest_streak_7_days: false,
            badge_sidequest_streak_30_days: false,
            badge_sidequest_streak_100_days: false,
            badge_sidequest_streak_365_days: false,
            badge_friends_5: false,
            badge_friends_10: false,
            badge_strength_master: false,
            badge_money_master: false,
            badge_health_master: false,
            badge_knowledge_master: false,
            created_at: new Date(),
            updated_at: new Date()
          };
          this.initializeBadges(this.userBadges);
        }
      },
      error: (error) => {
        alert(`Error loading badges: ${error.message}. This might be due to missing Supabase permissions.`);
      }
    });
  }

  ngOnDestroy() {
    if (this.badgesSubscription) {
      this.badgesSubscription.unsubscribe();
    }
  }

  async createDefaultBadges() {
    if (!this.userId) {
      return;
    }

    try {
      const badgeId = await this.badgeService.createUserBadges(this.userId);

      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error: any) {

      alert(`Error creating badges: ${error.message}. This might be due to missing Supabase permissions.`);
    }
  }

  goBack() {
    window.history.back();
  }

  initializeBadges(badges: UserBadges | null) {
    if (!badges) {
      return;
    }
    this.titleBadges = [
      {
        name: 'Newbie',
        description: 'Reach level 5',
        unlocked: badges.badge_newbie,
        emoji: '🥚'
      },
      {
        name: 'Warrior',
        description: 'Reach level 10',
        unlocked: badges.badge_warrior,
        emoji: '⚔️'
      },
      {
        name: 'Hardcore',
        description: 'Reach level 25',
        unlocked: badges.badge_hardcore,
        emoji: '🔥'
      },
      {
        name: 'Peak Performer',
        description: 'Reach level 50',
        unlocked: badges.badge_peak_performer,
        emoji: '🏔️'
      },
      {
        name: 'Indestructible',
        description: 'Reach level 75',
        unlocked: badges.badge_indestructible,
        emoji: '🛡️'
      },
      {
        name: 'Professional Upshifter',
        description: 'Reach level 100',
        unlocked: badges.badge_professional,
        emoji: '👑'
      }
    ];

    this.streakBadges = [
      {
        name: 'Week Warrior',
        description: 'Maintain a 7-day streak',
        unlocked: badges.badge_streak_7_days
      },
      {
        name: 'Monthly Master',
        description: 'Maintain a 30-day streak',
        unlocked: badges.badge_streak_30_days
      },
      {
        name: 'Century Club',
        description: 'Maintain a 100-day streak',
        unlocked: badges.badge_streak_100_days
      },
      {
        name: 'Year of Excellence',
        description: 'Maintain a 365-day streak',
        unlocked: badges.badge_streak_365_days
      }
    ];

    this.sidequestBadges = [
      {
        name: 'Side Quest Starter',
        description: 'Maintain a 7-day side quest streak',
        unlocked: badges.badge_sidequest_streak_7_days
      },
      {
        name: 'Side Quest Enthusiast',
        description: 'Maintain a 30-day side quest streak',
        unlocked: badges.badge_sidequest_streak_30_days
      },
      {
        name: 'Side Quest Expert',
        description: 'Maintain a 100-day side quest streak',
        unlocked: badges.badge_sidequest_streak_100_days
      },
      {
        name: 'Side Quest Legend',
        description: 'Maintain a 365-day side quest streak',
        unlocked: badges.badge_sidequest_streak_365_days
      }
    ];

    this.friendBadges = [
      {
        name: 'Social Circle',
        description: 'Add 5 friends',
        unlocked: badges.badge_friends_5
      },
      {
        name: 'Community Builder',
        description: 'Add 10 friends',
        unlocked: badges.badge_friends_10
      }
    ];

    this.categoryBadges = [
      {
        name: 'First Rep',
        description: 'Completed a quest in Strength category',
        unlocked: badges.badge_strength_master
      },
      {
        name: 'Cha-Ching!',
        description: 'Completed a quest in Money category',
        unlocked: badges.badge_money_master
      },
      {
        name: 'Vital Spark',
        description: 'Completed a quest in Health category',
        unlocked: badges.badge_health_master
      },
      {
        name: 'Smart Start',
        description: 'Completed a quest in Knowledge category',
        unlocked: badges.badge_knowledge_master
      }
    ];
  }
}
