﻿import { QuestCategory, QuestGoalUnit } from './quest.model';

export interface GroupSideQuestPool {
  id: string;                      
  name: string;                    
  description?: string;            
  goal_value: number;              
  category: QuestCategory;         
  goal_unit: QuestGoalUnit;        
  active: boolean;                 
  emoji: string;                   
  created_at?: string;             
}

export interface GroupSideQuest {
  id: string;                      
  group_id: string;                
  current_quest_id: string;        
  streak: number;                  
  last_completed_date?: string | null; 
  date_assigned: string;           
  completed: boolean;              
  value_achieved: number;          
  category: QuestCategory;         
  created_at?: string;             
  
  current_quest?: GroupSideQuestPool; 
}

export interface GroupSideQuestMemberStatus {
  id: string;                      
  group_quest_id: string;          
  member_id: string;               
  completed: boolean;              
  value_achieved: number;          
  last_updated: string;            
  created_at?: string;             
  
  member?: any;                    
  group_quest?: GroupSideQuest;    
}

export interface GroupDailyQuest {
  id: string;                      
  group_id: string;                
  streak: number;                  
  completed: boolean;              
  value_achieved: number;          
  date_assigned: string;           
  last_completed_date?: string | null; 
  category: QuestCategory;         
  
  current_quest: {
    id: string;                    
    name: string;                  
    description?: string;          
    goal_value: number;            
    goal_unit: QuestGoalUnit;      
    emoji: string;                 
  };
  
  member_status?: {
    id: string;                    
    completed: boolean;            
    value_achieved: number;        
    last_updated: string;          
  };
  
  eligible_members_count?: number; 
  completed_members_count?: number; 
}
