﻿
.quest-message {
    color: #FF9500;
    font-weight: 500;
    font-size: small;
}

 Add Quest Button */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.9);
}

.modal-content {
    background-color: transparent;
    margin: 5% auto;
    padding: 20px;
    width: 90%;
    max-width: 500px;
    position: relative;
    color: white;
     max-height: 80vh; */
.schedule-container {
    margin-top: 15px;
}

.days-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
}

.day-checkbox {
    display: flex;
    align-items: center;
    gap: 4px;
}

.day-checkbox label {
    font-size: 13px;
    cursor: pointer;
    margin-bottom: 0;
}

.month-days-selector {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
    margin-top: 8px;
}

.month-days-selector .day-checkbox {
    justify-content: center;
}

.month-days-selector .day-checkbox input[type="checkbox"] {
    margin-right: 2px;
}

.month-days-selector .day-checkbox label {
    min-width: 20px;
    text-align: center;
}

.quests, .side-quests {
    margin-bottom: 32px;
}


.quest-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}


.quest-item {
    background-color: var(--quest-bg);
    border: 1px solid var(--quest-border);
    border-radius: 8px;
    padding: 12px;
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}


.quest-item:active {
    transform: scale(0.98);
}


.quest-item.completed {
    border-color: var(--accent-color);
}


.quest-item.completed .quest-info h3 {
    color: var(--accent-color);
}


.quest-icon {
    font-size: 20px;
    min-width: 24px;
    text-align: center;
}


.quest-info {
    flex-grow: 1;
}


.quest-info h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 2px;
}


.quest-info p {
    color: var(--secondary-text);
    font-size: 12px;
    margin-bottom: 4px;
}


.progress, .progress-time {
    color: var(--secondary-text);
    font-size: 12px;
}


.quest-streak {
    font-size: 12px;
    white-space: nowrap;
}


 Add a purple line between main quests and side quests */
.progress-slider {
    background: linear-gradient(to right, var(--accent-color) 0%, var(--accent-color) 50%, var(--bg-tertiary) 50%, var(--bg-tertiary) 100%);
}


.progress-text {
    font-size: 12px;
    color: var(--secondary-text);
    margin-top: 2px;
}
:host {
    --background-color: #0C0C0F;
    --text-color: #FFFFFF;
    --secondary-text: #8E8E93;
    --accent-color: #4169E1;
    --quest-bg: #1C1C1E;
    --quest-border: #2C2C2E;
    --active-date: #4169E1;
    --inactive-date: #2C2C2E;
}


* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}


body.dark-theme {
    background-color: var(--background-color);
    color: var(--text-color);
    min-height: 100vh;
}


.container {
    width: 480px;
    margin: 0 auto;
    padding: 20px;
    scrollbar-width: none;
    overflow-y: auto;
}
.container::-webkit-scrollbar {
    display: none;  Chrome/Safari */
.date-progress .progress-circle {
    stroke: var(--accent-color);
    stroke-opacity: 0.9;
}

.date-progress .progress-circle.low {
    stroke: var(--accent-color);
}

.date-progress circle {
    stroke-width: 3;
}

 Special handling for selected days */
     6da5fe */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    opacity: 0.3;
}

.profile-container {
    padding: 20px;
    margin-bottom: 80px;
}

.profile-header {
    display: flex;
    align-items: center;

}

.profile-picture {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: var(--card-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    margin-right: 20px;
    overflow: hidden;
}

.profile-picture img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-info {
    flex: 1;
}

.profile-name {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
}

.profile-username {
    font-size: 16px;
    color: var(--secondary-text);
    margin-bottom: 5px;
}

.profile-bio {
    font-size: 14px;
    color: var(--text-secondary, #a0a3b1);
    margin-bottom: 10px;
    font-style: italic;
    max-width: 300px;
    cursor: pointer;
}

.profile-bio-container {
    max-width: 400px;
    padding-left: 100px;
}

.edit-bio-btn {
    background: transparent;
    color: var(--accent-color, #4169e1);
    font-size: 12px;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.edit-bio-btn:hover {
    background-color: rgba(65, 105, 225, 0.1);
}

 Hide edit button when bio is empty */
