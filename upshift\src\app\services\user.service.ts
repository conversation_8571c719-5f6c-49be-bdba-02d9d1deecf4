﻿
import { Injectable, EnvironmentInjector, inject, runInInjectionContext } from '@angular/core';
import { Observable, from, of, map, switchMap, catchError, BehaviorSubject, firstValueFrom } from 'rxjs';
import { User } from '../models/user.model';
import { UserProfile } from '../models/supabase.models';
import { getTitleByLevel } from '../utils/user.utils';
import { SupabaseService, supabase } from './supabase.service'; 
import { XpService, EntityType } from './xp.service';

@Injectable({ providedIn: 'root' })
export class UserService {
  private injector = inject(EnvironmentInjector);
  private xpService = inject(XpService);
  private _currentUserProfile = new BehaviorSubject<UserProfile | null>(null);
  currentUserProfile$ = this._currentUserProfile.asObservable();

  private supabaseClient = supabase;
  private supabaseService = inject(SupabaseService);

  constructor() {
    setTimeout(() => {
      this.supabaseService.currentUser$.subscribe(authUser => {
        if (authUser) {
          this.getUserById(authUser.id).subscribe(profile => {
            if (profile) {
              this._currentUserProfile.next(profile as unknown as UserProfile);
            }
          });
        } else {
          this._currentUserProfile.next(null);
        }
      });
    }, 100);
  }

  getUser(userId: string): Observable<User | null> {
    return runInInjectionContext(this.injector, () => {
      if (userId.includes('@')) {
        return this.getUserByEmail(userId);
      }

      return from(
        this.supabaseClient
          .from('profiles')
          .select('*')
          .eq('id', userId)
          .single()
      ).pipe(
        map(response => {
          if (response.error) {
            return null;
          }
          if (!response.data) {
            return null;
          }
          const userData = response.data as User;
          return userData;
        }),
        catchError(error => {
          return of(null);
        })
      );
    });
  }

  getUserByEmail(email: string): Observable<User | null> {
    return runInInjectionContext(this.injector, () => {
      return from(
        this.supabaseClient
          .from('profiles')
          .select('*')
          .eq('email', email)
          .single()
      ).pipe(
        map(response => {
          if (response.error) {
            return null;
          }
          return response.data as User;
        }),
        catchError(error => {
          return of(null);
        })
      );
    });
  }

  getUserById(userId: string): Observable<User | null> {
    return from(
      this.supabaseClient
        .from('profiles')
        .select('*')
        .eq('id', userId)
    ).pipe(
      map(response => {
        if (response.error) {
          return null;
        }

        if (!response.data || response.data.length === 0) {
          return null;
        }
        return response.data[0] as User;
      }),
      catchError(error => {
        return of(null);
      })
    );
  }

  ensureUserExists(authUser: any): Observable<User | null> {
    if (!authUser || !authUser.id) {
      return of(null);
    }

    return this.getUserById(authUser.id).pipe(
      switchMap(userById => {
        if (userById) {
          return of(userById);
        }

        if (authUser.email) {
          return this.getUserByEmail(authUser.email).pipe(
            switchMap(userByEmail => {
              if (userByEmail) {
                return of(userByEmail);
              }
              const newUser: User = {
                id: authUser.id,
                email: authUser.email || '',
                username: authUser.email ? authUser.email.split('@')[0] : 'user_' + Date.now(),
                name: authUser.user_metadata?.['name'] || '',
                registration_date: new Date(),
                last_login: new Date(),
                active: true,
                level: 1,
                title: '🥚 Beginner',
                strength_xp: 0,
                money_xp: 0,
                health_xp: 0,
                knowledge_xp: 0,
                bio: '',
                affiliate_code_used: '',
                timezone: 'UTC',
                auto_renew: true,
                sidequests_switch: true,
                show_celebration: true,
                celebration_name: 'Another Day, Another W',
                celebration_description: "You've completed all your quests for today. Keep up the great work!",
                celebration_emoji: '🦅',
                subscription_status: 'email marketing'
              };

              return from(
                this.supabaseClient
                  .from('profiles')
                  .insert(newUser)
                  .select()
              ).pipe(
                map(response => {
                  if (response.error) {
                    throw new Error(response.error.message);
                  }
                  return newUser;
                }),
                catchError(error => {
                  return of(null);
                })
              );
            })
          );
        } else {
          const newUser: User = {
            id: authUser.id,
            email: '',
            username: 'user_' + Date.now(),
            name: authUser.user_metadata?.['name'] || '',
            registration_date: new Date(),
            last_login: new Date(),
            active: true,
            level: 1,
            title: '🥚 Beginner',
            strength_xp: 0,
            money_xp: 0,
            health_xp: 0,
            knowledge_xp: 0,
            bio: '',
            affiliate_code_used: '',
            timezone: 'UTC',
            auto_renew: true,
            sidequests_switch: true,
            show_celebration: true,
            celebration_name: 'Another Day, Another W',
            celebration_description: "You've completed all your quests for today. Keep up the great work!",
            celebration_emoji: '🦅',
            subscription_status: 'email marketing'
          };

          return from(
            this.supabaseClient
              .from('profiles')
              .insert(newUser)
              .select()
          ).pipe(
            map(response => {
              if (response.error) {
                throw new Error(response.error.message);
              }
              return newUser;
            }),
            catchError(error => {
              return of(null);
            })
          );
        }
      })
    );
  }

  async createUser(user: Omit<User, 'registration_date' | 'last_login' | 'title'>) {
    const newUser: User = {
      ...user,
      registration_date: new Date(),
      last_login: new Date(),
      title: getTitleByLevel(user.level),
      strength_xp: 0,
      money_xp: 0,
      health_xp: 0,
      knowledge_xp: 0,
      bio: '',
      timezone: 'UTC',
      sidequests_switch: true,
      show_celebration: true,
      celebration_name: 'Another Day, Another W',
      celebration_description: "You've completed all your quests for today. Keep up the great work!",
      celebration_emoji: ''
    };

    const { error } = await this.supabaseClient
      .from('profiles')
      .insert(newUser);

    if (error) {
      throw new Error(error.message);
    }
  }

  async updateUserLevel(userId: string, newLevel: number) {
    const newTitle = getTitleByLevel(newLevel);

    const { error } = await this.supabaseClient
      .from('profiles')
      .update({
        level: newLevel,
        title: newTitle,
      })
      .eq('id', userId);

    if (error) {
      throw new Error(error.message);
    }
  }

  async updateUserBio(userId: string, bio: string) {
    const trimmedBio = bio.trim();
    const bioValue = trimmedBio === '' ? '' : trimmedBio;

    const { error } = await this.supabaseClient
      .from('profiles')
      .update({ bio: bioValue })
      .eq('id', userId);

    if (error) {
      throw new Error(error.message);
    }
  }

  async updateUserProfile(userId: string, data: Partial<User>) {
    const updatedData = { ...data };

    if (updatedData.bio !== undefined && updatedData.bio !== null) {
      const trimmedBio = updatedData.bio.trim();
      updatedData.bio = trimmedBio === '' ? '' : trimmedBio;
    }

    const { error } = await this.supabaseClient
      .from('profiles')
      .update(updatedData)
      .eq('id', userId);

    if (error) {
      throw new Error(error.message);
    }
  }

  async updateUser(userId: string, data: Partial<User>): Promise<void> {
    const { data: updatedUser, error } = await this.supabaseClient
      .from('profiles')
      .update(data)
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    const currentProfile = this._currentUserProfile.getValue();
    if (currentProfile && currentProfile.id === userId) {
      this._currentUserProfile.next({
        ...currentProfile,
        ...updatedUser
      } as unknown as UserProfile);
    }
  }

  async refreshCurrentUserProfile(): Promise<void> {
    const currentProfile = this._currentUserProfile.getValue();
    if (currentProfile) {
      const profile = await firstValueFrom(this.getUserById(currentProfile.id));
      if (profile) {
        this._currentUserProfile.next(profile as unknown as UserProfile);
      }
    }
  }

  getTopUsers(count: number = 10): Observable<User[]> {
    return runInInjectionContext(this.injector, () => {
      return from(
        this.supabaseClient
          .from('profiles')
          .select('*')
          .order('level', { ascending: false })
          .limit(count * 2)
      ).pipe(
        map(response => {
          if (response.error) {
            return [];
          }
          const sortedUsers = response.data.sort((a, b) => {
            if (b.level !== a.level) {
              return b.level - a.level;
            }
            const aTotalXp = (a.strength_xp || 0) + (a.money_xp || 0) + (a.health_xp || 0) + (a.knowledge_xp || 0);
            const bTotalXp = (b.strength_xp || 0) + (b.money_xp || 0) + (b.health_xp || 0) + (b.knowledge_xp || 0);
            return bTotalXp - aTotalXp;
          });
          const limitedUsers = sortedUsers.slice(0, count);
          return limitedUsers as User[];
        }),
        catchError(error => {
          return of([]);
        })
      );
    });
  }

  getUserProfile(userId: string): Observable<any> {
    return from(
      this.supabaseClient
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()
    ).pipe(
      map(response => {
        if (response.error) {
          return null;
        }
        return response.data;
      }),
      catchError(error => {
        return of(null);
      })
    );
  }

  getUserStats(userId: string): Observable<any> {
    return of({
      user_id: userId,
      total_xp: 0,
      completed_quests: 0,
      max_streak: 0
    });

     Original implementation - commented out until user_stats table is created
  }

  async checkAndLevelUp(userId: string): Promise<boolean> {
    return runInInjectionContext(this.injector, async () => {
      const { data: user, error } = await this.supabaseClient
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error || !user) {
        return false;
      }
      if (user.level >= 100) {
        return false;
      }
      const requiredXP = await firstValueFrom(this.xpService.getRequiredXpForNextLevel(user.level, EntityType.USER));

      const categories = ['strength', 'money', 'health', 'knowledge'];
      const hasEnoughXP = categories.every(category => {
        const fieldName = `${category}_xp`;
        const xpValue = user[fieldName];
        const hasEnough = typeof xpValue === 'number' && xpValue >= requiredXP;
        `);
        return hasEnough;
      });

      if (hasEnoughXP) {
        const updates: any = {
          level: user.level + 1
        };

        categories.forEach(category => {
          const fieldName = `${category}_xp`;
          const currentXP = user[fieldName];
          updates[fieldName] = currentXP - requiredXP;
        });
        updates.title = getTitleByLevel(user.level + 1);
        const { error: updateError } = await this.supabaseClient
          .from('profiles')
          .update(updates)
          .eq('id', userId);

        if (updateError) {
          return false;
        }

        await this.updateBadgesBasedOnLevel(userId, user.level + 1);

        return true;
      }
      return false;
    });
  }

  private async updateBadgesBasedOnLevel(userId: string, newLevel: number): Promise<void> {
    return runInInjectionContext(this.injector, async () => {

      const { data: badges, error } = await this.supabaseClient
        .from('user_badges')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        return;
      }

      if (!badges) {

        const newBadges = {
          user_id: userId,
          created_at: new Date(),
          updated_at: new Date()
        };

        const { error: insertError } = await this.supabaseClient
          .from('user_badges')
          .insert(newBadges);

        if (insertError) {
          return;
        }
      }

      const updates: any = {
        updated_at: new Date()
      };

      if (newLevel >= 5) {
        updates.badge_newbie = true;
      }
      if (newLevel >= 10) {
        updates.badge_warrior = true;
      }
      if (newLevel >= 15) {
        updates.badge_monk = true;
      }
      if (newLevel >= 20) {
        updates.badge_nonchalant = true;
      }
      if (newLevel >= 25) {
        updates.badge_hardcore = true;
      }
      if (newLevel >= 30) {
        updates.badge_disciplined_machine = true;
      }
      if (newLevel >= 35) {
        updates.badge_high_performer = true;
      }
      if (newLevel >= 40) {
        updates.badge_master_of_consistency = true;
      }
      if (newLevel >= 50) {
        updates.badge_peak_performer = true;
      }
      if (newLevel >= 60) {
        updates.badge_elite_operator = true;
      }
      if (newLevel >= 75) {
        updates.badge_indestructible = true;
      }
      if (newLevel >= 90) {
        updates.badge_ultra_human = true;
      }
      if (newLevel >= 100) {
        updates.badge_professional = true;
      }

      if (Object.keys(updates).length > 1) {
        const { error: updateError } = await this.supabaseClient
          .from('user_badges')
          .update(updates)
          .eq('user_id', userId);

        if (updateError) {
          return;
        }
      }
    });
  }
}
