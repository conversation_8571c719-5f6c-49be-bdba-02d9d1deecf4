import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { Router } from '@angular/router';
import { Preferences } from '@capacitor/preferences';
import { SupabaseService } from '../services/supabase.service';
import { AuthGuard } from '../guards/auth.guard';

function isPublicPath(url: string) {
  const publicPaths = ['/signup', '/pricing', '/onboarding'];
  return publicPaths.some(path => url.startsWith(path));
}

@Component({
  selector: 'app-main-redirect',
  standalone: true,
  imports: [CommonModule, IonicModule],
  template: `
    <div class="loading-container">
      <ion-spinner name="crescent"></ion-spinner>
      <p>Upshift Yourself</p>
    </div>
  `,
  styles: [`
    .loading-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100%;
      width: 100%;
    }
  `]
})
export class MainRedirectComponent implements OnInit {
  private router = inject(Router);
  private supabaseService = inject(SupabaseService);

  constructor() {}

  ngOnInit() {
    this.ionViewWillEnter();
  }

  async ionViewWillEnter() {
    const { value: onboarding } = await Preferences.get({ key: 'onboarding_complete' });
    const user = this.supabaseService._currentUser.value;
    const isAuthenticated = !!user;

    if (isAuthenticated) {
      const user = this.supabaseService._currentUser.value;

      if (!user) {
        this.router.navigateByUrl('/signup');
        return;
      }

      if (!onboarding) {
        this.router.navigateByUrl('/onboarding');
        return;
      }

      try {
        await this.supabaseService.ensureProfileExists(user);

        const { data: userData, error } = await this.supabaseService.getClient()
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (error || !userData) {
          this.router.navigateByUrl('/signup');
          return;
        }

        const endDate = userData.end_of_current_plan ? new Date(userData.end_of_current_plan) : null;
        const username = userData.username;

        if (endDate && endDate >= new Date()) {
          if (username) {

            const url = localStorage.getItem('lastAttemptedUrl');
            localStorage.removeItem('lastAttemptedUrl');
            if (url && !isPublicPath(url)) {
              setTimeout(() => {
                this.router.navigateByUrl(url);
              }, 1500);
            } else {
              this.router.navigateByUrl('/tabs/home');
            }
          } else {
            this.router.navigateByUrl('/signup-step3');
          }
        } else {
          this.router.navigateByUrl('/pricing');
        }
      } catch (error) {
        this.router.navigateByUrl('/signup');
      }
    } else {
      this.router.navigateByUrl('/signup');
    }
  }
}
