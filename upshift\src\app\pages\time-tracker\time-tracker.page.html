﻿<!-- Exact HTML from Django template with Angular syntax -->
<div class="container">
    <header>
        <div class="logo">
            <img src="assets/images/upshift_icon_mini.svg" alt="Upshift">
            <span>Upshift</span>
        </div>
        <h1>Time Tracker</h1>
    </header>

    <div class="week-calendar">
        <div class="calendar-nav">
            <button class="nav-arrow prev" (click)="changeWeek(-1)">←</button>
            <div class="days">
                <div class="day-name" *ngFor="let dayName of dayNames">{{ dayName }}</div>
            </div>
            <button class="nav-arrow next" (click)="changeWeek(1)">→</button>
        </div>
        <div class="dates">
            <div *ngFor="let date of weekDates"
                 class="date"
                 [class.active]="date.is_today"
                 [class.selected]="date.is_selected"
                 [class.disabled]="date.is_future"
                 (click)="!date.is_future && selectDate(date.date)">
                {{ date.day }}
            </div>
        </div>
    </div>

    <section class="time-tracking">
        <h2>Activities</h2>

        <div class="activity-input">
            <select id="activitySelect" (change)="handleActivitySelection($event)">
                <option value="">Select Activity</option>
                <option *ngFor="let type of activityTypes" [value]="type.id" [attr.data-emoji]="type.emoji" [attr.data-name]="type.name">
                    {{ type.emoji }} {{ type.name }}
                </option>
                <option value="custom">➕ Custom Activity</option>
            </select>

            <!-- Standard time input -->
            <div class="time-input-container" [style.display]="showStandardInput ? 'flex' : 'none'">
                <div class="time-inputs">
                    <input type="number" id="hoursInput" min="0" max="23" [(ngModel)]="hoursInput">
                    <span>h</span>
                    <input type="number" id="minutesInput" min="0" max="59" [(ngModel)]="minutesInput">
                    <span>m</span>
                </div>
                <button (click)="addActivity()">Add</button>
            </div>

            <!-- Custom activity input -->
            <div id="customActivityForm" class="custom-inline-form" [style.display]="showCustomForm ? 'flex' : 'none'">
                <div class="custom-row">
                    <input type="text" name="emoji" id="emoji" [(ngModel)]="customEmoji" appEmojiInput>
                    <input type="text" id="customActivityName" placeholder="Activity name" [(ngModel)]="customActivityName">
                </div>
                <div class="time-inputs">
                    <input type="number" id="customHoursInput" min="0" max="23" [(ngModel)]="customHoursInput">
                    <span>h</span>
                    <input type="number" id="customMinutesInput" min="0" max="59" [(ngModel)]="customMinutesInput">
                    <span>m</span>
                </div>
                <button (click)="addCustomActivity()">Add</button>
            </div>
        </div>

        <div class="time-visualization">
            <canvas id="timeChart"></canvas>
            <div class="time-summary">
                <div class="total-time">
                    Total tracked: <span id="totalTracked">{{ totalTrackedHours }}h</span>
                </div>
                <div class="remaining-time">
                    Remaining: <span id="remainingTime">{{ remainingHours }}h</span>
                </div>
            </div>
        </div>

        <div class="activity-list">
            <div *ngFor="let activity of activities" class="activity-item" [attr.data-id]="activity.id" >
                <div class="activity-name-emoji" >
                    <div class="activity-icon">{{ activity.emoji }}</div>
                    <h3 >{{ activity.name }}</h3>
                </div>
                <div class="activity-info">

                    <div class="time-input">
                        <input type="number" min="0" max="23" [(ngModel)]="activity.hours"
                               class="hours" [attr.data-activity-id]="activity.id" (change)="updateActivity(activity)">
                        <span>h</span>
                        <input type="number" min="0" max="59" [(ngModel)]="activity.minutes"
                               class="minutes" [attr.data-activity-id]="activity.id" (change)="updateActivity(activity)">
                        <span>m</span>
                    </div>
                    <button class="delete-activity" (click)="deleteActivity(activity.id)" >×</button>
                </div>

            </div>
        </div>
    </section>
</div>
