{"ast": null, "code": "var _LeaderboardPage;\nimport { inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { IonicModule } from '@ionic/angular';\nimport { RouterModule, Router, ActivatedRoute } from '@angular/router';\nimport { UserService } from '../../services/user.service';\nimport { GroupService } from '../../services/group.service';\nimport { FriendService } from '../../services/friend.service';\nimport { take, map } from 'rxjs';\nimport { SupabaseService } from '../../services/supabase.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction LeaderboardPage_div_17_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 15);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 16)(6, \"div\", 17);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 18);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 19)(11, \"span\", 20);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 20);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 20);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 20);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const group_r2 = ctx.$implicit;\n    const i_r3 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"top-1\", i_r3 === 0)(\"top-2\", i_r3 === 1)(\"top-3\", i_r3 === 2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i_r3 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(group_r2.emoji);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(group_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Level \", group_r2.level, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\uD83D\\uDCAA \", group_r2.strength_xp, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\uD83D\\uDCB0 \", group_r2.money_xp, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u2764\\uFE0F \", group_r2.health_xp, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\uD83D\\uDCDA \", group_r2.knowledge_xp, \"\");\n  }\n}\nfunction LeaderboardPage_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, LeaderboardPage_div_17_div_1_Template, 19, 14, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.topGroups);\n  }\n}\nfunction LeaderboardPage_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"p\");\n    i0.ɵɵtext(2, \"No groups found. Create a group to see it on the leaderboard!\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction LeaderboardPage_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵlistener(\"click\", function LeaderboardPage_div_21_div_1_Template_div_click_0_listener() {\n      const user_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.viewUserProfile(user_r6.id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 14);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 16)(4, \"div\", 17);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 18);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 19)(9, \"span\", 20);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 20);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 20);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 20);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const user_r6 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"clickable-user\", user_r6.id !== ctx_r3.currentUserId)(\"current-user\", user_r6.id === ctx_r3.currentUserId);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"top-1\", i_r7 === 0)(\"top-2\", i_r7 === 1)(\"top-3\", i_r7 === 2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i_r7 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", user_r6.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"Level \", user_r6.level, \" - \", user_r6.title, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\uD83D\\uDCAA \", user_r6.strength_xp, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\uD83D\\uDCB0 \", user_r6.money_xp, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u2764\\uFE0F \", user_r6.health_xp, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\uD83D\\uDCDA \", user_r6.knowledge_xp, \"\");\n  }\n}\nfunction LeaderboardPage_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, LeaderboardPage_div_21_div_1_Template, 17, 18, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.topUsers);\n  }\n}\nfunction LeaderboardPage_ng_template_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"p\");\n    i0.ɵɵtext(2, \"No users found. Be the first to climb the leaderboard!\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class LeaderboardPage {\n  constructor() {\n    this.activeTab = 'groups';\n    this.topGroups = [];\n    this.topUsers = [];\n    this.currentUserId = null;\n    this.groupsSubscription = null;\n    this.usersSubscription = null;\n    this.supabaseService = inject(SupabaseService);\n    this.userService = inject(UserService);\n    this.groupService = inject(GroupService);\n    this.friendService = inject(FriendService);\n    this.router = inject(Router);\n    this.route = inject(ActivatedRoute);\n  }\n  ngOnInit() {\n    this.supabaseService.currentUser$.subscribe(user => {\n      if (user) {\n        this.currentUserId = user.id;\n      }\n    });\n    const url = this.router.url;\n    if (url.includes('/leaderboard/users')) {\n      this.activeTab = 'users';\n    } else {\n      this.activeTab = 'groups';\n    }\n    this.loadTopGroups();\n    this.loadTopUsers();\n  }\n  ngOnDestroy() {\n    if (this.groupsSubscription) {\n      this.groupsSubscription.unsubscribe();\n    }\n    if (this.usersSubscription) {\n      this.usersSubscription.unsubscribe();\n    }\n  }\n  setActiveTab(tab) {\n    this.activeTab = tab;\n    window.location.href = `/leaderboard/${tab}`;\n  }\n  loadTopGroups() {\n    this.groupsSubscription = this.groupService.getTopGroups(10).subscribe({\n      next: groups => {\n        this.topGroups = groups;\n      },\n      error: error => {},\n      complete: () => {}\n    });\n  }\n  loadTopUsers() {\n    this.usersSubscription = this.userService.getTopUsers(10).subscribe({\n      next: users => {\n        this.topUsers = users;\n      },\n      error: error => {},\n      complete: () => {}\n    });\n  }\n  viewUserProfile(userId) {\n    if (!userId) return;\n    if (userId === this.currentUserId) {\n      return;\n    }\n    this.friendService.getFriends(this.currentUserId || '').pipe(take(1), map(friends => {\n      const isFriend = friends.some(friend => friend.user_id === this.currentUserId && friend.friend_id === userId || friend.user_id === userId && friend.friend_id === this.currentUserId);\n      if (isFriend) {\n        this.router.navigate(['/friends', userId]);\n      } else {\n        this.router.navigate(['/user-profile', userId]);\n      }\n    })).subscribe();\n  }\n  goBackToGroups() {\n    window.location.href = '/groups';\n  }\n}\n_LeaderboardPage = LeaderboardPage;\n_LeaderboardPage.ɵfac = function LeaderboardPage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _LeaderboardPage)();\n};\n_LeaderboardPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _LeaderboardPage,\n  selectors: [[\"app-leaderboard\"]],\n  decls: 24,\n  vars: 12,\n  consts: [[\"noGroups\", \"\"], [\"noUsers\", \"\"], [1, \"container\"], [1, \"logo\"], [\"src\", \"assets/images/upshift_icon_mini.svg\", \"alt\", \"Upshift\"], [1, \"leaderboard-container\"], [\"href\", \"javascript:void(0)\", 1, \"back-link\", 3, \"click\"], [1, \"leaderboard-tabs\"], [1, \"leaderboard-tab\", 3, \"click\"], [\"id\", \"groups-tab\", 1, \"leaderboard-content\"], [4, \"ngIf\", \"ngIfElse\"], [\"id\", \"users-tab\", 1, \"leaderboard-content\"], [\"class\", \"leaderboard-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"leaderboard-item\"], [1, \"rank\"], [1, \"entity-icon\"], [1, \"entity-info\"], [1, \"entity-name\"], [1, \"entity-level\"], [1, \"entity-xp\"], [1, \"xp-category\"], [1, \"no-results\"], [\"class\", \"leaderboard-item\", 3, \"clickable-user\", \"current-user\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"leaderboard-item\", 3, \"click\"]],\n  template: function LeaderboardPage_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r1 = i0.ɵɵgetCurrentView();\n      i0.ɵɵelementStart(0, \"div\", 2)(1, \"header\")(2, \"div\", 3);\n      i0.ɵɵelement(3, \"img\", 4);\n      i0.ɵɵelementStart(4, \"span\");\n      i0.ɵɵtext(5, \"Upshift\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(6, \"h1\");\n      i0.ɵɵtext(7, \"Leaderboard\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(8, \"div\", 5)(9, \"a\", 6);\n      i0.ɵɵlistener(\"click\", function LeaderboardPage_Template_a_click_9_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.goBackToGroups());\n      });\n      i0.ɵɵtext(10, \"\\u2190 Back to Groups\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(11, \"div\", 7)(12, \"button\", 8);\n      i0.ɵɵlistener(\"click\", function LeaderboardPage_Template_button_click_12_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.setActiveTab(\"groups\"));\n      });\n      i0.ɵɵtext(13, \"Top Groups\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(14, \"button\", 8);\n      i0.ɵɵlistener(\"click\", function LeaderboardPage_Template_button_click_14_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.setActiveTab(\"users\"));\n      });\n      i0.ɵɵtext(15, \"Top Users\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(16, \"div\", 9);\n      i0.ɵɵtemplate(17, LeaderboardPage_div_17_Template, 2, 1, \"div\", 10)(18, LeaderboardPage_ng_template_18_Template, 3, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(20, \"div\", 11);\n      i0.ɵɵtemplate(21, LeaderboardPage_div_21_Template, 2, 1, \"div\", 10)(22, LeaderboardPage_ng_template_22_Template, 3, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd()()();\n    }\n    if (rf & 2) {\n      const noGroups_r8 = i0.ɵɵreference(19);\n      const noUsers_r9 = i0.ɵɵreference(23);\n      i0.ɵɵadvance(12);\n      i0.ɵɵclassProp(\"active\", ctx.activeTab === \"groups\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵclassProp(\"active\", ctx.activeTab === \"users\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵclassProp(\"active\", ctx.activeTab === \"groups\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.topGroups.length > 0)(\"ngIfElse\", noGroups_r8);\n      i0.ɵɵadvance(3);\n      i0.ɵɵclassProp(\"active\", ctx.activeTab === \"users\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.topUsers.length > 0)(\"ngIfElse\", noUsers_r9);\n    }\n  },\n  dependencies: [IonicModule, CommonModule, i1.NgForOf, i1.NgIf, RouterModule],\n  styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%];\\n\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\tvar __webpack_modules__ = ({\\n\\n\\n 999:\\n\\n\\n\\n\\n\\n (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/sass-loader/dist/cjs.js):\\\\nexpected \\\\\\\"{\\\\\\\".\\\\n   \\u2577\\\\n23 \\u2502   padding-bottom: 120px !important;  Account for navigation bar */\\\\r\\\\n   \\u2502                                                                   ^\\\\n   \\u2575\\\\n  src\\\\\\\\app\\\\\\\\pages\\\\\\\\leaderboard\\\\\\\\leaderboard.page.scss 23:67  root stylesheet\\\");\\n\\n\\n })\\n\\n\\n \\t});\\n\\n\\n\\n \\t\\n\\n \\t// startup\\n\\n \\t// Load entry module and return exports\\n\\n \\t// This entry module doesn't tell about it's top-level declarations so it can't be inlined\\n\\n \\tvar __webpack_exports__ = {};\\n\\n \\t__webpack_modules__[999]();\\n\\n \\tresource = __webpack_exports__;\\n\\n \\t\\n\\n })()\\n;\"]\n});", "map": {"version": 3, "names": ["inject", "CommonModule", "IonicModule", "RouterModule", "Router", "ActivatedRoute", "UserService", "GroupService", "FriendService", "take", "map", "SupabaseService", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵclassProp", "i_r3", "ɵɵtextInterpolate", "group_r2", "emoji", "name", "ɵɵtextInterpolate1", "level", "strength_xp", "money_xp", "health_xp", "knowledge_xp", "ɵɵtemplate", "LeaderboardPage_div_17_div_1_Template", "ɵɵproperty", "ctx_r3", "topGroups", "ɵɵlistener", "LeaderboardPage_div_21_div_1_Template_div_click_0_listener", "user_r6", "ɵɵrestoreView", "_r5", "$implicit", "ɵɵnextContext", "ɵɵresetView", "viewUserProfile", "id", "currentUserId", "i_r7", "username", "ɵɵtextInterpolate2", "title", "LeaderboardPage_div_21_div_1_Template", "topUsers", "LeaderboardPage", "constructor", "activeTab", "groupsSubscription", "usersSubscription", "supabaseService", "userService", "groupService", "friendService", "router", "route", "ngOnInit", "currentUser$", "subscribe", "user", "url", "includes", "loadTopGroups", "loadTopUsers", "ngOnDestroy", "unsubscribe", "setActiveTab", "tab", "window", "location", "href", "getTopGroups", "next", "groups", "error", "complete", "getTopUsers", "users", "userId", "getFriends", "pipe", "friends", "isFriend", "some", "friend", "user_id", "friend_id", "navigate", "goBackToGroups", "selectors", "decls", "vars", "consts", "template", "LeaderboardPage_Template", "rf", "ctx", "ɵɵelement", "LeaderboardPage_Template_a_click_9_listener", "_r1", "LeaderboardPage_Template_button_click_12_listener", "LeaderboardPage_Template_button_click_14_listener", "LeaderboardPage_div_17_Template", "LeaderboardPage_ng_template_18_Template", "ɵɵtemplateRefExtractor", "LeaderboardPage_div_21_Template", "LeaderboardPage_ng_template_22_Template", "length", "noGroups_r8", "noUsers_r9", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\leaderboard\\leaderboard.page.ts", "C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\leaderboard\\leaderboard.page.html"], "sourcesContent": ["import { Component, OnInit, inject } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { RouterModule, Router, ActivatedRoute } from '@angular/router';\r\nimport { UserService } from '../../services/user.service';\r\nimport { GroupService } from '../../services/group.service';\r\nimport { FriendService } from '../../services/friend.service';\r\nimport { User } from '../../models/user.model';\r\nimport { Group } from '../../models/group.model';\r\nimport { Subscription, of, switchMap, take, map } from 'rxjs';\r\nimport { SupabaseService } from '../../services/supabase.service';\r\n\r\n@Component({\r\n  selector: 'app-leaderboard',\r\n  templateUrl: './leaderboard.page.html',\r\n  styleUrls: ['./leaderboard.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule, RouterModule]\r\n})\r\nexport class LeaderboardPage implements OnInit {\r\n  activeTab: 'groups' | 'users' = 'groups';\r\n\r\n  topGroups: Group[] = [];\r\n  topUsers: User[] = [];\r\n  currentUserId: string | null = null;\r\n\r\n  groupsSubscription: Subscription | null = null;\r\n  usersSubscription: Subscription | null = null;\r\n\r\n  private supabaseService = inject(SupabaseService);\r\n  private userService = inject(UserService);\r\n  private groupService = inject(GroupService);\r\n  private friendService = inject(FriendService);\r\n  private router = inject(Router);\r\n  private route = inject(ActivatedRoute);\r\n\r\n  constructor() {}\r\n\r\n  ngOnInit() {\r\n\r\n    this.supabaseService.currentUser$.subscribe(user => {\r\n      if (user) {\r\n        this.currentUserId = user.id;\r\n      }\r\n    });\r\n\r\n    const url = this.router.url;\r\n\r\n    if (url.includes('/leaderboard/users')) {\r\n      this.activeTab = 'users';\r\n    } else {\r\n      this.activeTab = 'groups';\r\n    }\r\n\r\n\r\n    this.loadTopGroups();\r\n    this.loadTopUsers();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    if (this.groupsSubscription) {\r\n      this.groupsSubscription.unsubscribe();\r\n    }\r\n\r\n    if (this.usersSubscription) {\r\n      this.usersSubscription.unsubscribe();\r\n    }\r\n  }\r\n\r\n  setActiveTab(tab: 'groups' | 'users') {\r\n    this.activeTab = tab;\r\n\r\n    window.location.href = `/leaderboard/${tab}`;\r\n  }\r\n\r\n  loadTopGroups() {\r\n    this.groupsSubscription = this.groupService.getTopGroups(10).subscribe({\r\n      next: (groups) => {\r\n        this.topGroups = groups;\r\n      },\r\n      error: (error) => {\r\n      },\r\n      complete: () => {\r\n      }\r\n    });\r\n  }\r\n\r\n  loadTopUsers() {\r\n    this.usersSubscription = this.userService.getTopUsers(10).subscribe({\r\n      next: (users) => {\r\n        this.topUsers = users;\r\n      },\r\n      error: (error) => {\r\n      },\r\n      complete: () => {\r\n      }\r\n    });\r\n  }\r\n\r\n  viewUserProfile(userId: string | undefined) {\r\n    if (!userId) return;\r\n\r\n    if (userId === this.currentUserId) {\r\n      return;\r\n    }\r\n\r\n    this.friendService.getFriends(this.currentUserId || '').pipe(\r\n      take(1),\r\n      map(friends => {\r\n        const isFriend = friends.some(friend =>\r\n          (friend.user_id === this.currentUserId && friend.friend_id === userId) ||\r\n          (friend.user_id === userId && friend.friend_id === this.currentUserId)\r\n        );\r\n\r\n        if (isFriend) {\r\n          this.router.navigate(['/friends', userId]);\r\n        } else {\r\n          this.router.navigate(['/user-profile', userId]);\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  goBackToGroups() {\r\n    window.location.href = '/groups';\r\n  }\r\n}\r\n", "<!-- Leaderboard Page -->\r\n<div class=\"container\">\r\n  <header>\r\n    <div class=\"logo\">\r\n      <img src=\"assets/images/upshift_icon_mini.svg\" alt=\"Upshift\">\r\n      <span>Upshift</span>\r\n    </div>\r\n    <h1>Leaderboard</h1>\r\n  </header>\r\n\r\n  <div class=\"leaderboard-container\">\r\n    <a href=\"javascript:void(0)\" (click)=\"goBackToGroups()\" class=\"back-link\">← Back to Groups</a>\r\n\r\n    <div class=\"leaderboard-tabs\">\r\n      <button (click)=\"setActiveTab('groups')\" class=\"leaderboard-tab\" [class.active]=\"activeTab === 'groups'\">Top Groups</button>\r\n      <button (click)=\"setActiveTab('users')\" class=\"leaderboard-tab\" [class.active]=\"activeTab === 'users'\">Top Users</button>\r\n    </div>\r\n\r\n    <!-- Groups Tab -->\r\n    <div id=\"groups-tab\" class=\"leaderboard-content\" [class.active]=\"activeTab === 'groups'\">\r\n      <div *ngIf=\"topGroups.length > 0; else noGroups\">\r\n        <div *ngFor=\"let group of topGroups; let i = index\" class=\"leaderboard-item\">\r\n          <div class=\"rank\" [class.top-1]=\"i === 0\" [class.top-2]=\"i === 1\" [class.top-3]=\"i === 2\">{{ i + 1 }}</div>\r\n          <div class=\"entity-icon\">{{ group.emoji }}</div>\r\n          <div class=\"entity-info\">\r\n            <div class=\"entity-name\">{{ group.name }}</div>\r\n            <div class=\"entity-level\">Level {{ group.level }}</div>\r\n            <div class=\"entity-xp\">\r\n              <span class=\"xp-category\">💪 {{ group.strength_xp }}</span>\r\n              <span class=\"xp-category\">💰 {{ group.money_xp }}</span>\r\n              <span class=\"xp-category\">❤️ {{ group.health_xp }}</span>\r\n              <span class=\"xp-category\">📚 {{ group.knowledge_xp }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <ng-template #noGroups>\r\n        <div class=\"no-results\">\r\n          <p>No groups found. Create a group to see it on the leaderboard!</p>\r\n        </div>\r\n      </ng-template>\r\n    </div>\r\n\r\n    <!-- Users Tab -->\r\n    <div id=\"users-tab\" class=\"leaderboard-content\" [class.active]=\"activeTab === 'users'\">\r\n      <div *ngIf=\"topUsers.length > 0; else noUsers\">\r\n        <div *ngFor=\"let user of topUsers; let i = index\" class=\"leaderboard-item\"\r\n             [class.clickable-user]=\"user.id !== currentUserId\"\r\n             [class.current-user]=\"user.id === currentUserId\"\r\n             (click)=\"viewUserProfile(user.id)\">\r\n          <div class=\"rank\" [class.top-1]=\"i === 0\" [class.top-2]=\"i === 1\" [class.top-3]=\"i === 2\">{{ i + 1 }}</div>\r\n          <div class=\"entity-info\">\r\n            <div class=\"entity-name\">\r\n              {{ user.username }}\r\n            </div>\r\n            <div class=\"entity-level\">Level {{ user.level }} - {{ user.title }}</div>\r\n            <div class=\"entity-xp\">\r\n              <span class=\"xp-category\">💪 {{ user.strength_xp }}</span>\r\n              <span class=\"xp-category\">💰 {{ user.money_xp }}</span>\r\n              <span class=\"xp-category\">❤️ {{ user.health_xp }}</span>\r\n              <span class=\"xp-category\">📚 {{ user.knowledge_xp }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <ng-template #noUsers>\r\n        <div class=\"no-results\">\r\n          <p>No users found. Be the first to climb the leaderboard!</p>\r\n        </div>\r\n      </ng-template>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";AAAA,SAA4BA,MAAM,QAAQ,eAAe;AACzD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,EAAEC,MAAM,EAAEC,cAAc,QAAQ,iBAAiB;AACtE,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,aAAa,QAAQ,+BAA+B;AAG7D,SAAsCC,IAAI,EAAEC,GAAG,QAAQ,MAAM;AAC7D,SAASC,eAAe,QAAQ,iCAAiC;;;;;ICYvDC,EADF,CAAAC,cAAA,cAA6E,cACe;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC3GH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAE9CH,EADF,CAAAC,cAAA,cAAyB,cACE;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC/CH,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAErDH,EADF,CAAAC,cAAA,eAAuB,gBACK;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3DH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxDH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzDH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IAG3DF,EAH2D,CAAAG,YAAA,EAAO,EACxD,EACF,EACF;;;;;IAZcH,EAAA,CAAAI,SAAA,EAAuB;IAAyBJ,EAAhD,CAAAK,WAAA,UAAAC,IAAA,OAAuB,UAAAA,IAAA,OAAwB,UAAAA,IAAA,OAAwB;IAACN,EAAA,CAAAI,SAAA,EAAW;IAAXJ,EAAA,CAAAO,iBAAA,CAAAD,IAAA,KAAW;IAC5EN,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAO,iBAAA,CAAAC,QAAA,CAAAC,KAAA,CAAiB;IAEfT,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAO,iBAAA,CAAAC,QAAA,CAAAE,IAAA,CAAgB;IACfV,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAW,kBAAA,WAAAH,QAAA,CAAAI,KAAA,KAAuB;IAErBZ,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAW,kBAAA,kBAAAH,QAAA,CAAAK,WAAA,KAA0B;IAC1Bb,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAW,kBAAA,kBAAAH,QAAA,CAAAM,QAAA,KAAuB;IACvBd,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAW,kBAAA,kBAAAH,QAAA,CAAAO,SAAA,KAAwB;IACxBf,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAW,kBAAA,kBAAAH,QAAA,CAAAQ,YAAA,KAA2B;;;;;IAX7DhB,EAAA,CAAAC,cAAA,UAAiD;IAC/CD,EAAA,CAAAiB,UAAA,IAAAC,qCAAA,oBAA6E;IAc/ElB,EAAA,CAAAG,YAAA,EAAM;;;;IAdmBH,EAAA,CAAAI,SAAA,EAAc;IAAdJ,EAAA,CAAAmB,UAAA,YAAAC,MAAA,CAAAC,SAAA,CAAc;;;;;IAiBnCrB,EADF,CAAAC,cAAA,cAAwB,QACnB;IAAAD,EAAA,CAAAE,MAAA,oEAA6D;IAClEF,EADkE,CAAAG,YAAA,EAAI,EAChE;;;;;;IAONH,EAAA,CAAAC,cAAA,cAGwC;IAAnCD,EAAA,CAAAsB,UAAA,mBAAAC,2DAAA;MAAA,MAAAC,OAAA,GAAAxB,EAAA,CAAAyB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAP,MAAA,GAAApB,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAAST,MAAA,CAAAU,eAAA,CAAAN,OAAA,CAAAO,EAAA,CAAwB;IAAA,EAAC;IACrC/B,EAAA,CAAAC,cAAA,cAA0F;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEzGH,EADF,CAAAC,cAAA,cAAyB,cACE;IACvBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEvEH,EADF,CAAAC,cAAA,cAAuB,eACK;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1DH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvDH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxDH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAG1DF,EAH0D,CAAAG,YAAA,EAAO,EACvD,EACF,EACF;;;;;;IAfDH,EADA,CAAAK,WAAA,mBAAAmB,OAAA,CAAAO,EAAA,KAAAX,MAAA,CAAAY,aAAA,CAAkD,iBAAAR,OAAA,CAAAO,EAAA,KAAAX,MAAA,CAAAY,aAAA,CACF;IAEjChC,EAAA,CAAAI,SAAA,EAAuB;IAAyBJ,EAAhD,CAAAK,WAAA,UAAA4B,IAAA,OAAuB,UAAAA,IAAA,OAAwB,UAAAA,IAAA,OAAwB;IAACjC,EAAA,CAAAI,SAAA,EAAW;IAAXJ,EAAA,CAAAO,iBAAA,CAAA0B,IAAA,KAAW;IAGjGjC,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAW,kBAAA,MAAAa,OAAA,CAAAU,QAAA,MACF;IAC0BlC,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAmC,kBAAA,WAAAX,OAAA,CAAAZ,KAAA,SAAAY,OAAA,CAAAY,KAAA,KAAyC;IAEvCpC,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAW,kBAAA,kBAAAa,OAAA,CAAAX,WAAA,KAAyB;IACzBb,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAW,kBAAA,kBAAAa,OAAA,CAAAV,QAAA,KAAsB;IACtBd,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAW,kBAAA,kBAAAa,OAAA,CAAAT,SAAA,KAAuB;IACvBf,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAW,kBAAA,kBAAAa,OAAA,CAAAR,YAAA,KAA0B;;;;;IAf5DhB,EAAA,CAAAC,cAAA,UAA+C;IAC7CD,EAAA,CAAAiB,UAAA,IAAAoB,qCAAA,oBAGwC;IAe1CrC,EAAA,CAAAG,YAAA,EAAM;;;;IAlBkBH,EAAA,CAAAI,SAAA,EAAa;IAAbJ,EAAA,CAAAmB,UAAA,YAAAC,MAAA,CAAAkB,QAAA,CAAa;;;;;IAqBjCtC,EADF,CAAAC,cAAA,cAAwB,QACnB;IAAAD,EAAA,CAAAE,MAAA,6DAAsD;IAC3DF,EAD2D,CAAAG,YAAA,EAAI,EACzD;;;ADjDd,OAAM,MAAOoC,eAAe;EAiB1BC,YAAA;IAhBA,KAAAC,SAAS,GAAuB,QAAQ;IAExC,KAAApB,SAAS,GAAY,EAAE;IACvB,KAAAiB,QAAQ,GAAW,EAAE;IACrB,KAAAN,aAAa,GAAkB,IAAI;IAEnC,KAAAU,kBAAkB,GAAwB,IAAI;IAC9C,KAAAC,iBAAiB,GAAwB,IAAI;IAErC,KAAAC,eAAe,GAAGxD,MAAM,CAACW,eAAe,CAAC;IACzC,KAAA8C,WAAW,GAAGzD,MAAM,CAACM,WAAW,CAAC;IACjC,KAAAoD,YAAY,GAAG1D,MAAM,CAACO,YAAY,CAAC;IACnC,KAAAoD,aAAa,GAAG3D,MAAM,CAACQ,aAAa,CAAC;IACrC,KAAAoD,MAAM,GAAG5D,MAAM,CAACI,MAAM,CAAC;IACvB,KAAAyD,KAAK,GAAG7D,MAAM,CAACK,cAAc,CAAC;EAEvB;EAEfyD,QAAQA,CAAA;IAEN,IAAI,CAACN,eAAe,CAACO,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MACjD,IAAIA,IAAI,EAAE;QACR,IAAI,CAACrB,aAAa,GAAGqB,IAAI,CAACtB,EAAE;MAC9B;IACF,CAAC,CAAC;IAEF,MAAMuB,GAAG,GAAG,IAAI,CAACN,MAAM,CAACM,GAAG;IAE3B,IAAIA,GAAG,CAACC,QAAQ,CAAC,oBAAoB,CAAC,EAAE;MACtC,IAAI,CAACd,SAAS,GAAG,OAAO;IAC1B,CAAC,MAAM;MACL,IAAI,CAACA,SAAS,GAAG,QAAQ;IAC3B;IAGA,IAAI,CAACe,aAAa,EAAE;IACpB,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAAChB,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAACiB,WAAW,EAAE;IACvC;IAEA,IAAI,IAAI,CAAChB,iBAAiB,EAAE;MAC1B,IAAI,CAACA,iBAAiB,CAACgB,WAAW,EAAE;IACtC;EACF;EAEAC,YAAYA,CAACC,GAAuB;IAClC,IAAI,CAACpB,SAAS,GAAGoB,GAAG;IAEpBC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,gBAAgBH,GAAG,EAAE;EAC9C;EAEAL,aAAaA,CAAA;IACX,IAAI,CAACd,kBAAkB,GAAG,IAAI,CAACI,YAAY,CAACmB,YAAY,CAAC,EAAE,CAAC,CAACb,SAAS,CAAC;MACrEc,IAAI,EAAGC,MAAM,IAAI;QACf,IAAI,CAAC9C,SAAS,GAAG8C,MAAM;MACzB,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI,CACjB,CAAC;MACDC,QAAQ,EAAEA,CAAA,KAAK,CACf;KACD,CAAC;EACJ;EAEAZ,YAAYA,CAAA;IACV,IAAI,CAACd,iBAAiB,GAAG,IAAI,CAACE,WAAW,CAACyB,WAAW,CAAC,EAAE,CAAC,CAAClB,SAAS,CAAC;MAClEc,IAAI,EAAGK,KAAK,IAAI;QACd,IAAI,CAACjC,QAAQ,GAAGiC,KAAK;MACvB,CAAC;MACDH,KAAK,EAAGA,KAAK,IAAI,CACjB,CAAC;MACDC,QAAQ,EAAEA,CAAA,KAAK,CACf;KACD,CAAC;EACJ;EAEAvC,eAAeA,CAAC0C,MAA0B;IACxC,IAAI,CAACA,MAAM,EAAE;IAEb,IAAIA,MAAM,KAAK,IAAI,CAACxC,aAAa,EAAE;MACjC;IACF;IAEA,IAAI,CAACe,aAAa,CAAC0B,UAAU,CAAC,IAAI,CAACzC,aAAa,IAAI,EAAE,CAAC,CAAC0C,IAAI,CAC1D7E,IAAI,CAAC,CAAC,CAAC,EACPC,GAAG,CAAC6E,OAAO,IAAG;MACZ,MAAMC,QAAQ,GAAGD,OAAO,CAACE,IAAI,CAACC,MAAM,IACjCA,MAAM,CAACC,OAAO,KAAK,IAAI,CAAC/C,aAAa,IAAI8C,MAAM,CAACE,SAAS,KAAKR,MAAM,IACpEM,MAAM,CAACC,OAAO,KAAKP,MAAM,IAAIM,MAAM,CAACE,SAAS,KAAK,IAAI,CAAChD,aAAc,CACvE;MAED,IAAI4C,QAAQ,EAAE;QACZ,IAAI,CAAC5B,MAAM,CAACiC,QAAQ,CAAC,CAAC,UAAU,EAAET,MAAM,CAAC,CAAC;MAC5C,CAAC,MAAM;QACL,IAAI,CAACxB,MAAM,CAACiC,QAAQ,CAAC,CAAC,eAAe,EAAET,MAAM,CAAC,CAAC;MACjD;IACF,CAAC,CAAC,CACH,CAACpB,SAAS,EAAE;EACf;EAEA8B,cAAcA,CAAA;IACZpB,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,SAAS;EAClC;;mBA1GWzB,eAAe;;mCAAfA,gBAAe;AAAA;;QAAfA,gBAAe;EAAA4C,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;;MChBxBzF,EAFJ,CAAAC,cAAA,aAAuB,aACb,aACY;MAChBD,EAAA,CAAA2F,SAAA,aAA6D;MAC7D3F,EAAA,CAAAC,cAAA,WAAM;MAAAD,EAAA,CAAAE,MAAA,cAAO;MACfF,EADe,CAAAG,YAAA,EAAO,EAChB;MACNH,EAAA,CAAAC,cAAA,SAAI;MAAAD,EAAA,CAAAE,MAAA,kBAAW;MACjBF,EADiB,CAAAG,YAAA,EAAK,EACb;MAGPH,EADF,CAAAC,cAAA,aAAmC,WACyC;MAA7CD,EAAA,CAAAsB,UAAA,mBAAAsE,4CAAA;QAAA5F,EAAA,CAAAyB,aAAA,CAAAoE,GAAA;QAAA,OAAA7F,EAAA,CAAA6B,WAAA,CAAS6D,GAAA,CAAAR,cAAA,EAAgB;MAAA,EAAC;MAAmBlF,EAAA,CAAAE,MAAA,6BAAgB;MAAAF,EAAA,CAAAG,YAAA,EAAI;MAG5FH,EADF,CAAAC,cAAA,cAA8B,iBAC6E;MAAjGD,EAAA,CAAAsB,UAAA,mBAAAwE,kDAAA;QAAA9F,EAAA,CAAAyB,aAAA,CAAAoE,GAAA;QAAA,OAAA7F,EAAA,CAAA6B,WAAA,CAAS6D,GAAA,CAAA9B,YAAA,CAAa,QAAQ,CAAC;MAAA,EAAC;MAAiE5D,EAAA,CAAAE,MAAA,kBAAU;MAAAF,EAAA,CAAAG,YAAA,EAAS;MAC5HH,EAAA,CAAAC,cAAA,iBAAuG;MAA/FD,EAAA,CAAAsB,UAAA,mBAAAyE,kDAAA;QAAA/F,EAAA,CAAAyB,aAAA,CAAAoE,GAAA;QAAA,OAAA7F,EAAA,CAAA6B,WAAA,CAAS6D,GAAA,CAAA9B,YAAA,CAAa,OAAO,CAAC;MAAA,EAAC;MAAgE5D,EAAA,CAAAE,MAAA,iBAAS;MAClHF,EADkH,CAAAG,YAAA,EAAS,EACrH;MAGNH,EAAA,CAAAC,cAAA,cAAyF;MAiBvFD,EAhBA,CAAAiB,UAAA,KAAA+E,+BAAA,kBAAiD,KAAAC,uCAAA,gCAAAjG,EAAA,CAAAkG,sBAAA,CAgB1B;MAKzBlG,EAAA,CAAAG,YAAA,EAAM;MAGNH,EAAA,CAAAC,cAAA,eAAuF;MAqBrFD,EApBA,CAAAiB,UAAA,KAAAkF,+BAAA,kBAA+C,KAAAC,uCAAA,gCAAApG,EAAA,CAAAkG,sBAAA,CAoBzB;MAO5BlG,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;;MA1DiEH,EAAA,CAAAI,SAAA,IAAuC;MAAvCJ,EAAA,CAAAK,WAAA,WAAAqF,GAAA,CAAAjD,SAAA,cAAuC;MACxCzC,EAAA,CAAAI,SAAA,GAAsC;MAAtCJ,EAAA,CAAAK,WAAA,WAAAqF,GAAA,CAAAjD,SAAA,aAAsC;MAIvDzC,EAAA,CAAAI,SAAA,GAAuC;MAAvCJ,EAAA,CAAAK,WAAA,WAAAqF,GAAA,CAAAjD,SAAA,cAAuC;MAChFzC,EAAA,CAAAI,SAAA,EAA4B;MAAAJ,EAA5B,CAAAmB,UAAA,SAAAuE,GAAA,CAAArE,SAAA,CAAAgF,MAAA,KAA4B,aAAAC,WAAA,CAAa;MAwBDtG,EAAA,CAAAI,SAAA,GAAsC;MAAtCJ,EAAA,CAAAK,WAAA,WAAAqF,GAAA,CAAAjD,SAAA,aAAsC;MAC9EzC,EAAA,CAAAI,SAAA,EAA2B;MAAAJ,EAA3B,CAAAmB,UAAA,SAAAuE,GAAA,CAAApD,QAAA,CAAA+D,MAAA,KAA2B,aAAAE,UAAA,CAAY;;;iBD5BvCjH,WAAW,EAAED,YAAY,EAAAmH,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEnH,YAAY;EAAAoH,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}