﻿import { Injectable, inject } from '@angular/core';
import { Activity, ActivityType, DayTracking } from '../models/activity.model';
import { Observable, from, of, map, catchError } from 'rxjs';
import { SupabaseService } from './supabase.service';

@Injectable({
  providedIn: 'root'
})
export class ActivityService {
  private supabaseService = inject(SupabaseService);

  constructor() {}

  getActivityTypes(): Observable<ActivityType[]> {

    return from(
      this.supabaseService.getClient()
        .from('activity_types')
        .select('*')
        .order('order')
    ).pipe(
      map(response => {
        if (response.error) {
          return [];
        }

        return response.data as ActivityType[];
      }),
      catchError(error => {
        return of([]);
      })
    );
  }

  async createActivityType(activityType: Omit<ActivityType, 'id'>): Promise<string> {

    try {
      const { data, error } = await this.supabaseService.getClient()
        .from('activity_types')
        .insert(activityType)
        .select('id')
        .single();

      if (error) {
        throw error;
      }

      return data.id;
    } catch (error) {
      throw error;
    }
  }

  async updateActivityType(typeId: string, data: Partial<ActivityType>): Promise<void> {

    try {
      const { error } = await this.supabaseService.getClient()
        .from('activity_types')
        .update(data)
        .eq('id', typeId);

      if (error) {
        throw error;
      }

    } catch (error) {
      throw error;
    }
  }

  async deleteActivityType(typeId: string): Promise<void> {

    try {
      const { error } = await this.supabaseService.getClient()
        .from('activity_types')
        .delete()
        .eq('id', typeId);

      if (error) {
        throw error;
      }

    } catch (error) {
      throw error;
    }
  }

  getDayTracking(userId: string, date: Date): Observable<DayTracking | undefined> {
    const dateString = date.toISOString().split('T')[0]; 

    return from(
      this.supabaseService.getClient()
        .from('day_tracking')
        .select('*')
        .eq('user_id', userId)
        .eq('date', dateString)
    ).pipe(
      map(response => {
        if (response.error) {
          return undefined;
        }

        if (response.data && response.data.length > 0) {
          return response.data[0] as DayTracking;
        } else {
          return undefined;
        }
      }),
      catchError(error => {
        return of(undefined);
      })
    );
  }

  async createDayTracking(userId: string, date: Date): Promise<string> {
    const dateString = date.toISOString().split('T')[0]; 

    try {
      const { data: existingData, error: existingError } = await this.supabaseService.getClient()
        .from('day_tracking')
        .select('id')
        .eq('user_id', userId)
        .eq('date', dateString);

      if (existingError) {
        throw existingError;
      }

      if (existingData && existingData.length > 0) {
        return existingData[0].id;
      }

      const newTracking: Omit<DayTracking, 'id'> = {
        user_id: userId,
        date: dateString
      };


      const { data, error } = await this.supabaseService.getClient()
        .from('day_tracking')
        .insert(newTracking)
        .select('id')
        .single();

      if (error) {
        throw error;
      }

      return data.id;
    } catch (error) {
      throw error;
    }
  }

  getActivities(dayTrackingId: string): Observable<Activity[]> {

    return from(
      this.supabaseService.getClient()
        .from('activities')
        .select('*')
        .eq('day_tracking_id', dayTrackingId)
    ).pipe(
      map(response => {
        if (response.error) {
          return [];
        }

        return response.data as Activity[];
      }),
      catchError(error => {
        return of([]);
      })
    );
  }

  async createActivity(activity: Omit<Activity, 'id'>): Promise<string> {

    if (activity.hours < 0 || activity.hours >= 24) {
      throw new Error('Hours must be between 0 and 23');
    }

    if (activity.minutes < 0 || activity.minutes >= 60) {
      throw new Error('Minutes must be between 0 and 59');
    }

    try {
      const totalMinutes = activity.hours * 60 + activity.minutes;

      const { data: existingActivities, error: existingError } = await this.supabaseService.getClient()
        .from('activities')
        .select('hours, minutes')
        .eq('day_tracking_id', activity.day_tracking_id);

      if (existingError) {
        throw existingError;
      }

      if (existingActivities && existingActivities.length > 0) {
        let existingTotalMinutes = 0;
        existingActivities.forEach(act => {
          existingTotalMinutes += act.hours * 60 + act.minutes;
        });


        if (existingTotalMinutes + totalMinutes > 24 * 60) {
          throw new Error('Total activities cannot exceed 24 hours per day');
        }
      }

      const { data, error } = await this.supabaseService.getClient()
        .from('activities')
        .insert(activity)
        .select('id')
        .single();

      if (error) {
        throw error;
      }

      return data.id;
    } catch (error) {
      throw error;
    }
  }

  async updateActivity(activityId: string, data: Partial<Activity>): Promise<void> {

    try {
      const { data: activityData, error: activityError } = await this.supabaseService.getClient()
        .from('activities')
        .select('*')
        .eq('id', activityId)
        .single();

      if (activityError) {
        throw activityError;
      }

      if (!activityData) {
        throw new Error('Activity not found');
      }

      const activity = activityData as Activity;

      const newHours = data.hours !== undefined ? data.hours : activity.hours;
      const newMinutes = data.minutes !== undefined ? data.minutes : activity.minutes;

      if (newHours < 0 || newHours >= 24) {
        throw new Error('Hours must be between 0 and 23');
      }

      if (newMinutes < 0 || newMinutes >= 60) {
        throw new Error('Minutes must be between 0 and 59');
      }

      const oldTotalMinutes = activity.hours * 60 + activity.minutes;
      const newTotalMinutes = newHours * 60 + newMinutes;
      const minutesDiff = newTotalMinutes - oldTotalMinutes;


      if (minutesDiff > 0) {
        const { data: existingActivities, error: existingError } = await this.supabaseService.getClient()
          .from('activities')
          .select('hours, minutes')
          .eq('day_tracking_id', activity.day_tracking_id)
          .neq('id', activityId);

        if (existingError) {
          throw existingError;
        }

        if (existingActivities && existingActivities.length > 0) {
          let existingTotalMinutes = 0;
          existingActivities.forEach(act => {
            existingTotalMinutes += act.hours * 60 + act.minutes;
          });


          if (existingTotalMinutes + oldTotalMinutes + minutesDiff > 24 * 60) {
            throw new Error('Total activities cannot exceed 24 hours per day');
          }
        }
      }

      const { error: updateError } = await this.supabaseService.getClient()
        .from('activities')
        .update(data)
        .eq('id', activityId);

      if (updateError) {
        throw updateError;
      }

    } catch (error) {
      throw error;
    }
  }

  async deleteActivity(activityId: string): Promise<void> {

    try {
      const { error } = await this.supabaseService.getClient()
        .from('activities')
        .delete()
        .eq('id', activityId);

      if (error) {
        throw error;
      }

    } catch (error) {
      throw error;
    }
  }

  async initializeDefaultActivityTypes(): Promise<void> {

    const defaults = [
      { name: 'Sleep', emoji: '😴', is_active: true, order: 1, is_default: true },
      { name: 'Work', emoji: '💼', is_active: true, order: 2, is_default: true },
      { name: 'Exercise', emoji: '🏃', is_active: true, order: 3, is_default: true },
      { name: 'Screen Time', emoji: '📱', is_active: true, order: 4, is_default: true },
      { name: 'Study', emoji: '📚', is_active: true, order: 5, is_default: true },
      { name: 'Meditation', emoji: '🧘', is_active: true, order: 6, is_default: true },
      { name: 'Reading', emoji: '📖', is_active: true, order: 7, is_default: true },
      { name: 'Social', emoji: '👥', is_active: true, order: 8, is_default: true },
      { name: 'Hobby', emoji: '🎨', is_active: true, order: 9, is_default: true },
      { name: 'Free Time', emoji: '🎮', is_active: true, order: 10, is_default: true }
    ];

    try {
      for (const activityType of defaults) {

        const { data: existingData, error: existingError } = await this.supabaseService.getClient()
          .from('activity_types')
          .select('id')
          .eq('name', activityType.name);

        if (existingError) {
          continue;
        }

        if (!existingData || existingData.length === 0) {
          await this.createActivityType(activityType);
        } else {
        }
      }

    } catch (error) {
    }
  }
}
