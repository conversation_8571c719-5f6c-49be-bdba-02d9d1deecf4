{"ast": null, "code": "var _FocusPage;\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction FocusPage_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dayName_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(dayName_r1);\n  }\n}\nfunction FocusPage_div_17__svg_svg_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 43);\n    i0.ɵɵelement(1, \"circle\", 44);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const date_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"low\", date_r3.completion_percentage < 50);\n    i0.ɵɵattribute(\"stroke-dasharray\", date_r3.completion_percentage + \", 100\")(\"data-date\", date_r3.date);\n  }\n}\nfunction FocusPage_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵlistener(\"click\", function FocusPage_div_17_Template_div_click_0_listener() {\n      const date_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(!date_r3.is_future && ctx_r3.selectDate(date_r3.date));\n    });\n    i0.ɵɵtemplate(1, FocusPage_div_17__svg_svg_1_Template, 2, 4, \"svg\", 41);\n    i0.ɵɵelementStart(2, \"span\", 42);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const date_r3 = ctx.$implicit;\n    i0.ɵɵclassProp(\"active\", date_r3.is_today)(\"selected\", date_r3.is_selected)(\"disabled\", date_r3.is_future);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", date_r3.total_quests > 0 && !date_r3.is_future);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(date_r3.day);\n  }\n}\nexport class FocusPage {\n  constructor() {\n    this.selectedDate = new Date();\n    this.weekDates = [];\n    this.dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];\n    this.headerText = 'Focus';\n    this.focusTime = 25;\n    this.breakTime = 5;\n    this.longBreakTime = 15;\n    this.sessionsBeforeLongBreak = 4;\n    this.isRunning = false;\n    this.isPaused = false;\n    this.isBreak = false;\n    this.isLongBreak = false;\n    this.currentSession = 1;\n    this.timerMinutes = 25;\n    this.timerSeconds = 0;\n    this.timerInterval = null;\n    this.completedSessions = 0;\n    this.totalFocusTime = 0;\n    this.currentStreak = 0;\n  }\n  ngOnInit() {\n    this.loadSettings();\n    this.loadStats();\n    this.resetTimer();\n    this.generateWeekDates();\n  }\n  ngOnDestroy() {\n    if (this.timerInterval) {\n      clearInterval(this.timerInterval);\n    }\n  }\n  loadSettings() {\n    const settings = localStorage.getItem('focusSettings');\n    if (settings) {\n      const parsedSettings = JSON.parse(settings);\n      this.focusTime = parsedSettings.focusTime || 25;\n      this.breakTime = parsedSettings.breakTime || 5;\n      this.longBreakTime = parsedSettings.longBreakTime || 15;\n      this.sessionsBeforeLongBreak = parsedSettings.sessionsBeforeLongBreak || 4;\n    }\n  }\n  saveSettings() {\n    const settings = {\n      focusTime: this.focusTime,\n      breakTime: this.breakTime,\n      longBreakTime: this.longBreakTime,\n      sessionsBeforeLongBreak: this.sessionsBeforeLongBreak\n    };\n    localStorage.setItem('focusSettings', JSON.stringify(settings));\n    this.resetTimer();\n  }\n  loadStats() {\n    const stats = localStorage.getItem('focusStats');\n    if (stats) {\n      const parsedStats = JSON.parse(stats);\n      this.completedSessions = parsedStats.completedSessions || 0;\n      this.totalFocusTime = parsedStats.totalFocusTime || 0;\n      this.currentStreak = parsedStats.currentStreak || 0;\n    }\n  }\n  saveStats() {\n    const stats = {\n      completedSessions: this.completedSessions,\n      totalFocusTime: this.totalFocusTime,\n      currentStreak: this.currentStreak,\n      lastSessionDate: new Date().toISOString().split('T')[0]\n    };\n    localStorage.setItem('focusStats', JSON.stringify(stats));\n  }\n  startTimer() {\n    if (this.isRunning && !this.isPaused) return;\n    if (this.isPaused) {\n      this.isPaused = false;\n    } else {\n      this.isRunning = true;\n    }\n    this.timerInterval = setInterval(() => {\n      if (this.timerSeconds === 0) {\n        if (this.timerMinutes === 0) {\n          this.timerCompleted();\n        } else {\n          this.timerMinutes--;\n          this.timerSeconds = 59;\n        }\n      } else {\n        this.timerSeconds--;\n      }\n    }, 1000);\n  }\n  pauseTimer() {\n    if (!this.isRunning) return;\n    this.isPaused = true;\n    clearInterval(this.timerInterval);\n  }\n  resetTimer() {\n    if (this.timerInterval) {\n      clearInterval(this.timerInterval);\n    }\n    this.isRunning = false;\n    this.isPaused = false;\n    if (this.isBreak) {\n      if (this.isLongBreak) {\n        this.timerMinutes = this.longBreakTime;\n      } else {\n        this.timerMinutes = this.breakTime;\n      }\n    } else {\n      this.timerMinutes = this.focusTime;\n    }\n    this.timerSeconds = 0;\n  }\n  timerCompleted() {\n    clearInterval(this.timerInterval);\n    this.playNotificationSound();\n    if (this.isBreak) {\n      this.isBreak = false;\n      this.timerMinutes = this.focusTime;\n    } else {\n      this.completedSessions++;\n      this.totalFocusTime += this.focusTime;\n      this.updateStreak();\n      this.saveStats();\n      if (this.currentSession % this.sessionsBeforeLongBreak === 0) {\n        this.isLongBreak = true;\n        this.timerMinutes = this.longBreakTime;\n      } else {\n        this.isLongBreak = false;\n        this.timerMinutes = this.breakTime;\n      }\n      this.isBreak = true;\n      this.currentSession++;\n    }\n    this.timerSeconds = 0;\n    this.isRunning = false;\n  }\n  playNotificationSound() {\n    const audio = new Audio('assets/sounds/notification.mp3');\n    audio.play().catch(error => {});\n  }\n  updateStreak() {\n    const stats = localStorage.getItem('focusStats');\n    if (stats) {\n      const parsedStats = JSON.parse(stats);\n      const lastSessionDate = parsedStats.lastSessionDate;\n      const today = new Date().toISOString().split('T')[0];\n      const yesterday = new Date(Date.now() - 86400000).toISOString().split('T')[0];\n      if (lastSessionDate === yesterday) {\n        this.currentStreak++;\n      } else if (lastSessionDate !== today) {\n        this.currentStreak = 1;\n      }\n    } else {\n      this.currentStreak = 1;\n    }\n  }\n  generateWeekDates() {\n    const today = new Date();\n    const currentDay = today.getDay() || 7;\n    const startDate = new Date(today);\n    startDate.setDate(today.getDate() - currentDay + 1);\n    this.weekDates = [];\n    for (let i = 0; i < 7; i++) {\n      const date = new Date(startDate);\n      date.setDate(startDate.getDate() + i);\n      const dateString = this.formatDate(date);\n      const isToday = this.isSameDay(date, today);\n      const isSelected = this.isSameDay(date, this.selectedDate);\n      const isFuture = date > today;\n      this.weekDates.push({\n        date: dateString,\n        day: date.getDate(),\n        is_today: isToday,\n        is_selected: isSelected,\n        is_future: isFuture,\n        total_quests: 0,\n        completed_quests: 0,\n        completion_percentage: 0\n      });\n    }\n  }\n  selectDate(dateString) {\n    const date = new Date(dateString);\n    this.selectedDate = date;\n    this.weekDates.forEach(weekDate => {\n      weekDate.is_selected = weekDate.date === dateString;\n    });\n    this.updateHeaderText();\n  }\n  changeWeek(direction) {\n    const firstDate = new Date(this.weekDates[0].date);\n    firstDate.setDate(firstDate.getDate() + direction * 7);\n    const currentDay = firstDate.getDay() || 7;\n    const startDate = new Date(firstDate);\n    startDate.setDate(firstDate.getDate() - currentDay + 1);\n    this.weekDates = [];\n    for (let i = 0; i < 7; i++) {\n      const date = new Date(startDate);\n      date.setDate(startDate.getDate() + i);\n      const dateString = this.formatDate(date);\n      const today = new Date();\n      const isToday = this.isSameDay(date, today);\n      const isSelected = this.isSameDay(date, this.selectedDate);\n      const isFuture = date > today;\n      this.weekDates.push({\n        date: dateString,\n        day: date.getDate(),\n        is_today: isToday,\n        is_selected: isSelected,\n        is_future: isFuture,\n        total_quests: 0,\n        completed_quests: 0,\n        completion_percentage: 0\n      });\n    }\n  }\n  updateHeaderText() {\n    const today = new Date();\n    if (this.isSameDay(this.selectedDate, today)) {\n      this.headerText = 'Focus';\n    } else {\n      this.headerText = this.selectedDate.toLocaleDateString('en-US', {\n        weekday: 'short',\n        day: 'numeric',\n        month: 'short'\n      }) + ' - Focus';\n    }\n  }\n  formatDate(date) {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n  isSameDay(date1, date2) {\n    return date1.getFullYear() === date2.getFullYear() && date1.getMonth() === date2.getMonth() && date1.getDate() === date2.getDate();\n  }\n}\n_FocusPage = FocusPage;\n_FocusPage.ɵfac = function FocusPage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _FocusPage)();\n};\n_FocusPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _FocusPage,\n  selectors: [[\"app-focus\"]],\n  decls: 95,\n  vars: 3,\n  consts: [[1, \"container\"], [1, \"logo\"], [\"src\", \"assets/images/upshift_icon_mini.svg\", \"alt\", \"Upshift\"], [1, \"week-calendar\"], [1, \"calendar-nav\"], [1, \"nav-arrow\", \"prev\", 3, \"click\"], [1, \"days\"], [\"class\", \"day-name\", 4, \"ngFor\", \"ngForOf\"], [1, \"nav-arrow\", \"next\", 3, \"click\"], [1, \"dates\"], [\"class\", \"date\", 3, \"active\", \"selected\", \"disabled\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"screen-summary\"], [1, \"summary-text\"], [1, \"screen-time-box\"], [1, \"big-time\"], [1, \"time-label\"], [1, \"app-times\"], [1, \"app-time\"], [1, \"blockers\"], [1, \"blocker\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 15v2m0-6.5a2.5 2.5 0 00-2.5 2.5h5a2.5 2.5 0 00-2.5-2.5zm0 0v-3m0 10v.01\"], [\"href\", \"#\", 1, \"cancel-link\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4S8 5.79 8 8s1.79 4 4 4zm0 0c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\"], [1, \"scheduled-blocks\"], [1, \"block-card\"], [1, \"block-card-text\"], [1, \"block-card-title\"], [1, \"block-card-sub\"], [1, \"block-card-detail\"], [1, \"block-card-icon\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 6v6l4 2m-4-14a10 10 0 100 20 10 10 0 000-20z\"], [1, \"usage-text\"], [1, \"block-buttons\"], [1, \"block-btn\"], [\"viewBox\", \"0 0 24 24\"], [\"d\", \"M19 3h-1V1h-2v2H8V1H6v2H5a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2V5a2 2 0 00-2-2zm0 16H5V8h14v11zM7 10h5v5H7v-5z\", \"fill\", \"currentColor\"], [\"d\", \"M12 7v5l4 2M12 1a11 11 0 100 22 11 11 0 000-22z\", \"fill\", \"currentColor\"], [\"d\", \"M13 2.05v2.01a8.001 8.001 0 11-6.36 13.41l1.45-1.45a6.001 6.001 0 107.91-9.27V11h-2V2.05z\", \"fill\", \"currentColor\"], [1, \"day-name\"], [1, \"date\", 3, \"click\"], [\"class\", \"date-progress\", \"viewBox\", \"0 0 36 36\", 4, \"ngIf\"], [1, \"date-content\"], [\"viewBox\", \"0 0 36 36\", 1, \"date-progress\"], [\"cx\", \"18\", \"cy\", \"18\", \"r\", \"15.5\", 1, \"progress-circle\"]],\n  template: function FocusPage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\")(2, \"div\", 1);\n      i0.ɵɵelement(3, \"img\", 2);\n      i0.ɵɵelementStart(4, \"span\");\n      i0.ɵɵtext(5, \"Upshift\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(6, \"h1\");\n      i0.ɵɵtext(7);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(8, \"div\", 3)(9, \"div\", 4)(10, \"button\", 5);\n      i0.ɵɵlistener(\"click\", function FocusPage_Template_button_click_10_listener() {\n        return ctx.changeWeek(-1);\n      });\n      i0.ɵɵtext(11, \"\\u2190\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(12, \"div\", 6);\n      i0.ɵɵtemplate(13, FocusPage_div_13_Template, 2, 1, \"div\", 7);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(14, \"button\", 8);\n      i0.ɵɵlistener(\"click\", function FocusPage_Template_button_click_14_listener() {\n        return ctx.changeWeek(1);\n      });\n      i0.ɵɵtext(15, \"\\u2192\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(16, \"div\", 9);\n      i0.ɵɵtemplate(17, FocusPage_div_17_Template, 4, 8, \"div\", 10);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(18, \"section\", 11)(19, \"p\", 12);\n      i0.ɵɵtext(20, \" You've spent 6 hours today \\u2013 enough to learn a new skill! \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(21, \"div\", 13)(22, \"div\")(23, \"span\", 14);\n      i0.ɵɵtext(24, \"41m\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(25, \"span\", 15);\n      i0.ɵɵtext(26, \"SCREEN TIME\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(27, \"div\")(28, \"span\", 14);\n      i0.ɵɵtext(29, \"16\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(30, \"span\", 15);\n      i0.ɵɵtext(31, \"SESSIONS\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(32, \"div\", 16)(33, \"div\", 17);\n      i0.ɵɵtext(34, \"Classroom \");\n      i0.ɵɵelementStart(35, \"span\");\n      i0.ɵɵtext(36, \"11m\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(37, \"div\", 17);\n      i0.ɵɵtext(38, \"Disk \");\n      i0.ɵɵelementStart(39, \"span\");\n      i0.ɵɵtext(40, \"10m\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(41, \"div\", 17);\n      i0.ɵɵtext(42, \"Instagram \");\n      i0.ɵɵelementStart(43, \"span\");\n      i0.ɵɵtext(44, \"9m\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(45, \"div\", 17);\n      i0.ɵɵtext(46, \"Trading212 \");\n      i0.ɵɵelementStart(47, \"span\");\n      i0.ɵɵtext(48, \"7m\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(49, \"div\", 18)(50, \"div\", 19)(51, \"span\");\n      i0.ɵɵnamespaceSVG();\n      i0.ɵɵelementStart(52, \"svg\", 20);\n      i0.ɵɵelement(53, \"path\", 21);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtext(54, \" App Blocker \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵnamespaceHTML();\n      i0.ɵɵelementStart(55, \"span\");\n      i0.ɵɵtext(56, \"Until 9:00 PM \");\n      i0.ɵɵelementStart(57, \"a\", 22);\n      i0.ɵɵtext(58, \"Cancel\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(59, \"div\", 19)(60, \"span\");\n      i0.ɵɵnamespaceSVG();\n      i0.ɵɵelementStart(61, \"svg\", 20);\n      i0.ɵɵelement(62, \"path\", 23);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtext(63, \" Website Blocker \");\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵnamespaceHTML();\n      i0.ɵɵelementStart(64, \"section\", 24)(65, \"h2\");\n      i0.ɵɵtext(66, \"Scheduled to block later\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(67, \"div\", 25)(68, \"div\", 26)(69, \"div\", 27);\n      i0.ɵɵtext(70, \"Time Limit Session\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(71, \"div\", 28);\n      i0.ɵɵtext(72, \"1 app\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(73, \"div\", 29);\n      i0.ɵɵtext(74, \"Monitoring \\u2022 All day\");\n      i0.ɵɵelement(75, \"br\");\n      i0.ɵɵtext(76, \"30 minute limit\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(77, \"div\", 30);\n      i0.ɵɵnamespaceSVG();\n      i0.ɵɵelementStart(78, \"svg\", 20);\n      i0.ɵɵelement(79, \"path\", 31);\n      i0.ɵɵelementEnd();\n      i0.ɵɵnamespaceHTML();\n      i0.ɵɵelementStart(80, \"span\", 32);\n      i0.ɵɵtext(81, \"Usage\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(82, \"div\", 33)(83, \"button\", 34);\n      i0.ɵɵnamespaceSVG();\n      i0.ɵɵelementStart(84, \"svg\", 35);\n      i0.ɵɵelement(85, \"path\", 36);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtext(86, \" Schedule \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵnamespaceHTML();\n      i0.ɵɵelementStart(87, \"button\", 34);\n      i0.ɵɵnamespaceSVG();\n      i0.ɵɵelementStart(88, \"svg\", 35);\n      i0.ɵɵelement(89, \"path\", 37);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtext(90, \" Time Limit \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵnamespaceHTML();\n      i0.ɵɵelementStart(91, \"button\", 34);\n      i0.ɵɵnamespaceSVG();\n      i0.ɵɵelementStart(92, \"svg\", 35);\n      i0.ɵɵelement(93, \"path\", 38);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtext(94, \" Block Now \");\n      i0.ɵɵelementEnd()()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(7);\n      i0.ɵɵtextInterpolate(ctx.headerText);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"ngForOf\", ctx.dayNames);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngForOf\", ctx.weekDates);\n    }\n  },\n  dependencies: [IonicModule, CommonModule, i1.NgForOf, i1.NgIf, FormsModule, RouterModule],\n  styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%];\\n\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\tvar __webpack_modules__ = ({\\n\\n\\n 43:\\n\\n\\n\\n\\n\\n (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/sass-loader/dist/cjs.js):\\\\nexpected \\\\\\\"{\\\\\\\".\\\\n    \\u2577\\\\n232 \\u2502  --- Button row --- */\\\\r\\\\n    \\u2502                       ^\\\\n    \\u2575\\\\n  src\\\\\\\\app\\\\\\\\pages\\\\\\\\focus\\\\\\\\focus.page.scss 232:23  root stylesheet\\\");\\n\\n\\n })\\n\\n\\n \\t});\\n\\n\\n\\n \\t\\n\\n \\t// startup\\n\\n \\t// Load entry module and return exports\\n\\n \\t// This entry module doesn't tell about it's top-level declarations so it can't be inlined\\n\\n \\tvar __webpack_exports__ = {};\\n\\n \\t__webpack_modules__[43]();\\n\\n \\tresource = __webpack_exports__;\\n\\n \\t\\n\\n })()\\n;\"]\n});", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "IonicModule", "RouterModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "dayName_r1", "ɵɵelement", "ɵɵclassProp", "date_r3", "completion_percentage", "ɵɵlistener", "FocusPage_div_17_Template_div_click_0_listener", "ɵɵrestoreView", "_r2", "$implicit", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "is_future", "selectDate", "date", "ɵɵtemplate", "FocusPage_div_17__svg_svg_1_Template", "is_today", "is_selected", "ɵɵproperty", "total_quests", "day", "FocusPage", "constructor", "selectedDate", "Date", "weekDates", "dayNames", "headerText", "focusTime", "breakTime", "longBreakTime", "sessionsBeforeLongBreak", "isRunning", "isPaused", "isBreak", "isLongBreak", "currentSession", "timerMinutes", "timerSeconds", "timerInterval", "completedSessions", "totalFocusTime", "currentStreak", "ngOnInit", "loadSettings", "loadStats", "resetTimer", "generateWeekDates", "ngOnDestroy", "clearInterval", "settings", "localStorage", "getItem", "parsedSettings", "JSON", "parse", "saveSettings", "setItem", "stringify", "stats", "parsedStats", "saveStats", "lastSessionDate", "toISOString", "split", "startTimer", "setInterval", "timerCompleted", "pauseTimer", "playNotificationSound", "updateStreak", "audio", "Audio", "play", "catch", "error", "today", "yesterday", "now", "currentDay", "getDay", "startDate", "setDate", "getDate", "i", "dateString", "formatDate", "isToday", "isSameDay", "isSelected", "isFuture", "push", "completed_quests", "for<PERSON>ach", "weekDate", "updateHeaderText", "changeWeek", "direction", "firstDate", "toLocaleDateString", "weekday", "month", "year", "getFullYear", "String", "getMonth", "padStart", "date1", "date2", "selectors", "decls", "vars", "consts", "template", "FocusPage_Template", "rf", "ctx", "FocusPage_Template_button_click_10_listener", "FocusPage_div_13_Template", "FocusPage_Template_button_click_14_listener", "FocusPage_div_17_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\focus\\focus.page.ts", "C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\focus\\focus.page.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { RouterModule } from '@angular/router';\r\n\r\ninterface WeekDate {\r\n  date: string; \n  day: number;\r\n  is_today: boolean;\r\n  is_selected: boolean;\r\n  is_future: boolean;\r\n  total_quests: number;\r\n  completed_quests: number;\r\n  completion_percentage: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-focus',\r\n  templateUrl: './focus.page.html',\r\n  styleUrls: ['./focus.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule, FormsModule, RouterModule]\r\n})\r\nexport class FocusPage implements OnInit, OnDestroy {\r\n  selectedDate: Date = new Date();\r\n  weekDates: WeekDate[] = [];\r\n  dayNames: string[] = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];\r\n  headerText: string = 'Focus';\r\n\r\n  focusTime = 25;\r\n  breakTime = 5;\r\n  longBreakTime = 15;\r\n  sessionsBeforeLongBreak = 4;\r\n\r\n  isRunning = false;\r\n  isPaused = false;\r\n  isBreak = false;\r\n  isLongBreak = false;\r\n  currentSession = 1;\r\n\r\n  timerMinutes = 25;\r\n  timerSeconds = 0;\r\n  timerInterval: any = null;\r\n\r\n  completedSessions = 0;\r\n  totalFocusTime = 0;\r\n  currentStreak = 0;\r\n\r\n  constructor() {}\r\n\r\n  ngOnInit() {\r\n    this.loadSettings();\r\n\r\n    this.loadStats();\r\n\r\n    this.resetTimer();\r\n\r\n    this.generateWeekDates();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    if (this.timerInterval) {\r\n      clearInterval(this.timerInterval);\r\n    }\r\n  }\r\n\r\n  loadSettings() {\r\n    const settings = localStorage.getItem('focusSettings');\r\n    if (settings) {\r\n      const parsedSettings = JSON.parse(settings);\r\n      this.focusTime = parsedSettings.focusTime || 25;\r\n      this.breakTime = parsedSettings.breakTime || 5;\r\n      this.longBreakTime = parsedSettings.longBreakTime || 15;\r\n      this.sessionsBeforeLongBreak = parsedSettings.sessionsBeforeLongBreak || 4;\r\n    }\r\n  }\r\n\r\n  saveSettings() {\r\n    const settings = {\r\n      focusTime: this.focusTime,\r\n      breakTime: this.breakTime,\r\n      longBreakTime: this.longBreakTime,\r\n      sessionsBeforeLongBreak: this.sessionsBeforeLongBreak\r\n    };\r\n\r\n    localStorage.setItem('focusSettings', JSON.stringify(settings));\r\n\r\n    this.resetTimer();\r\n  }\r\n\r\n  loadStats() {\r\n    const stats = localStorage.getItem('focusStats');\r\n    if (stats) {\r\n      const parsedStats = JSON.parse(stats);\r\n      this.completedSessions = parsedStats.completedSessions || 0;\r\n      this.totalFocusTime = parsedStats.totalFocusTime || 0;\r\n      this.currentStreak = parsedStats.currentStreak || 0;\r\n    }\r\n  }\r\n\r\n  saveStats() {\r\n    const stats = {\r\n      completedSessions: this.completedSessions,\r\n      totalFocusTime: this.totalFocusTime,\r\n      currentStreak: this.currentStreak,\r\n      lastSessionDate: new Date().toISOString().split('T')[0]\r\n    };\r\n\r\n    localStorage.setItem('focusStats', JSON.stringify(stats));\r\n  }\r\n\r\n  startTimer() {\r\n    if (this.isRunning && !this.isPaused) return;\r\n\r\n    if (this.isPaused) {\r\n      this.isPaused = false;\r\n    } else {\r\n      this.isRunning = true;\r\n    }\r\n\r\n    this.timerInterval = setInterval(() => {\r\n      if (this.timerSeconds === 0) {\r\n        if (this.timerMinutes === 0) {\r\n          this.timerCompleted();\r\n        } else {\r\n          this.timerMinutes--;\r\n          this.timerSeconds = 59;\r\n        }\r\n      } else {\r\n        this.timerSeconds--;\r\n      }\r\n    }, 1000);\r\n  }\r\n\r\n  pauseTimer() {\r\n    if (!this.isRunning) return;\r\n\r\n    this.isPaused = true;\r\n    clearInterval(this.timerInterval);\r\n  }\r\n\r\n  resetTimer() {\r\n    if (this.timerInterval) {\r\n      clearInterval(this.timerInterval);\r\n    }\r\n\r\n    this.isRunning = false;\r\n    this.isPaused = false;\r\n\r\n    if (this.isBreak) {\r\n      if (this.isLongBreak) {\r\n        this.timerMinutes = this.longBreakTime;\r\n      } else {\r\n        this.timerMinutes = this.breakTime;\r\n      }\r\n    } else {\r\n      this.timerMinutes = this.focusTime;\r\n    }\r\n\r\n    this.timerSeconds = 0;\r\n  }\r\n\r\n  timerCompleted() {\r\n    clearInterval(this.timerInterval);\r\n\r\n    this.playNotificationSound();\r\n\r\n    if (this.isBreak) {\r\n      this.isBreak = false;\r\n      this.timerMinutes = this.focusTime;\r\n    } else {\r\n      this.completedSessions++;\r\n      this.totalFocusTime += this.focusTime;\r\n\r\n      this.updateStreak();\r\n\r\n      this.saveStats();\r\n\r\n      if (this.currentSession % this.sessionsBeforeLongBreak === 0) {\r\n        this.isLongBreak = true;\r\n        this.timerMinutes = this.longBreakTime;\r\n      } else {\r\n        this.isLongBreak = false;\r\n        this.timerMinutes = this.breakTime;\r\n      }\r\n\r\n      this.isBreak = true;\r\n      this.currentSession++;\r\n    }\r\n\r\n    this.timerSeconds = 0;\r\n    this.isRunning = false;\r\n  }\r\n\r\n  playNotificationSound() {\r\n    const audio = new Audio('assets/sounds/notification.mp3');\r\n    audio.play().catch(error => {\r\n    });\r\n  }\r\n\r\n  updateStreak() {\r\n    const stats = localStorage.getItem('focusStats');\r\n    if (stats) {\r\n      const parsedStats = JSON.parse(stats);\r\n      const lastSessionDate = parsedStats.lastSessionDate;\r\n      const today = new Date().toISOString().split('T')[0];\r\n      const yesterday = new Date(Date.now() - 86400000).toISOString().split('T')[0];\r\n\r\n      if (lastSessionDate === yesterday) {\r\n        this.currentStreak++;\r\n      } else if (lastSessionDate !== today) {\r\n        this.currentStreak = 1;\r\n      }\r\n    } else {\r\n      this.currentStreak = 1;\r\n    }\r\n  }\r\n\r\n  generateWeekDates() {\r\n    const today = new Date();\r\n    const currentDay = today.getDay() || 7; \n    const startDate = new Date(today);\r\n    startDate.setDate(today.getDate() - currentDay + 1); \n\r\n    this.weekDates = [];\r\n    for (let i = 0; i < 7; i++) {\r\n      const date = new Date(startDate);\r\n      date.setDate(startDate.getDate() + i);\r\n\r\n      const dateString = this.formatDate(date);\r\n      const isToday = this.isSameDay(date, today);\r\n      const isSelected = this.isSameDay(date, this.selectedDate);\r\n      const isFuture = date > today;\r\n\r\n      this.weekDates.push({\r\n        date: dateString,\r\n        day: date.getDate(),\r\n        is_today: isToday,\r\n        is_selected: isSelected,\r\n        is_future: isFuture,\r\n        total_quests: 0,\r\n        completed_quests: 0,\r\n        completion_percentage: 0\r\n      });\r\n    }\r\n  }\r\n\r\n  selectDate(dateString: string) {\r\n    const date = new Date(dateString);\r\n    this.selectedDate = date;\r\n\r\n    this.weekDates.forEach(weekDate => {\r\n      weekDate.is_selected = weekDate.date === dateString;\r\n    });\r\n\r\n    this.updateHeaderText();\r\n  }\r\n\r\n  changeWeek(direction: number) {\r\n    const firstDate = new Date(this.weekDates[0].date);\r\n    firstDate.setDate(firstDate.getDate() + (direction * 7));\r\n\r\n    const currentDay = firstDate.getDay() || 7; \n    const startDate = new Date(firstDate);\r\n    startDate.setDate(firstDate.getDate() - currentDay + 1); \n\r\n    this.weekDates = [];\r\n    for (let i = 0; i < 7; i++) {\r\n      const date = new Date(startDate);\r\n      date.setDate(startDate.getDate() + i);\r\n\r\n      const dateString = this.formatDate(date);\r\n      const today = new Date();\r\n      const isToday = this.isSameDay(date, today);\r\n      const isSelected = this.isSameDay(date, this.selectedDate);\r\n      const isFuture = date > today;\r\n\r\n      this.weekDates.push({\r\n        date: dateString,\r\n        day: date.getDate(),\r\n        is_today: isToday,\r\n        is_selected: isSelected,\r\n        is_future: isFuture,\r\n        total_quests: 0,\r\n        completed_quests: 0,\r\n        completion_percentage: 0\r\n      });\r\n    }\r\n  }\r\n\r\n  updateHeaderText() {\r\n    const today = new Date();\r\n    if (this.isSameDay(this.selectedDate, today)) {\r\n      this.headerText = 'Focus';\r\n    } else {\r\n      this.headerText = this.selectedDate.toLocaleDateString('en-US', {\r\n        weekday: 'short',\r\n        day: 'numeric',\r\n        month: 'short'\r\n      }) + ' - Focus';\r\n    }\r\n  }\r\n\r\n  formatDate(date: Date): string {\r\n    const year = date.getFullYear();\r\n    const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n    return `${year}-${month}-${day}`;\r\n  }\r\n\r\n  isSameDay(date1: Date, date2: Date): boolean {\r\n    return date1.getFullYear() === date2.getFullYear() &&\r\n           date1.getMonth() === date2.getMonth() &&\r\n           date1.getDate() === date2.getDate();\r\n  }\r\n}\r\n", "\r\n<div class=\"container\">\r\n    <header>\r\n        <div class=\"logo\">\r\n            <img src=\"assets/images/upshift_icon_mini.svg\" alt=\"Upshift\">\r\n            <span>Upshift</span>\r\n        </div>\r\n        <h1>{{ headerText }}</h1>\r\n    </header>\r\n\r\n    <!-- Calendar -->\r\n    <div class=\"week-calendar\">\r\n        <div class=\"calendar-nav\">\r\n            <button class=\"nav-arrow prev\" (click)=\"changeWeek(-1)\">←</button>\r\n            <div class=\"days\">\r\n                <div class=\"day-name\" *ngFor=\"let dayName of dayNames\">{{ dayName }}</div>\r\n            </div>\r\n            <button class=\"nav-arrow next\" (click)=\"changeWeek(1)\">→</button>\r\n        </div>\r\n        <div class=\"dates\">\r\n            <div *ngFor=\"let date of weekDates\"\r\n                 class=\"date\"\r\n                 [class.active]=\"date.is_today\"\r\n                 [class.selected]=\"date.is_selected\"\r\n                 [class.disabled]=\"date.is_future\"\r\n                 (click)=\"!date.is_future && selectDate(date.date)\">\r\n                <svg class=\"date-progress\" viewBox=\"0 0 36 36\" *ngIf=\"date.total_quests > 0 && !date.is_future\">\r\n                    <circle cx=\"18\" cy=\"18\" r=\"15.5\"\r\n                            [attr.stroke-dasharray]=\"date.completion_percentage + ', 100'\"\r\n                            [attr.data-date]=\"date.date\"\r\n                            class=\"progress-circle\"\r\n                            [class.low]=\"date.completion_percentage < 50\"></circle>\r\n                </svg>\r\n                <span class=\"date-content\">{{ date.day }}</span>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n        <!-- Focus Summary Block -->\r\n        <section class=\"screen-summary\">\r\n            <p class=\"summary-text\">\r\n                You've spent 6 hours today – enough to learn a new skill!\r\n            </p>\r\n\r\n            <div class=\"screen-time-box\">\r\n                <div>\r\n                    <span class=\"big-time\">41m</span>\r\n                    <span class=\"time-label\">SCREEN TIME</span>\r\n                </div>\r\n                <div>\r\n                    <span class=\"big-time\">16</span>\r\n                    <span class=\"time-label\">SESSIONS</span>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"app-times\">\r\n                <div class=\"app-time\">Classroom <span>11m</span></div>\r\n                <div class=\"app-time\">Disk <span>10m</span></div>\r\n                <div class=\"app-time\">Instagram <span>9m</span></div>\r\n                <div class=\"app-time\">Trading212 <span>7m</span></div>\r\n            </div>\r\n\r\n            <div class=\"blockers\">\r\n                <div class=\"blocker\">\r\n                    <span>\r\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                                d=\"M12 15v2m0-6.5a2.5 2.5 0 00-2.5 2.5h5a2.5 2.5 0 00-2.5-2.5zm0 0v-3m0 10v.01\" />\r\n                        </svg>\r\n                        App Blocker\r\n                    </span>\r\n                    <span>Until 9:00 PM <a href=\"#\" class=\"cancel-link\">Cancel</a></span>\r\n                </div>\r\n\r\n                <div class=\"blocker\">\r\n                    <span>\r\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                                d=\"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4S8 5.79 8 8s1.79 4 4 4zm0 0c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\" />\r\n                        </svg>\r\n                        Website Blocker\r\n                    </span>\r\n                </div>\r\n            </div>\r\n        </section>\r\n        <section class=\"scheduled-blocks\">\r\n            <h2>Scheduled to block later</h2>\r\n\r\n            <div class=\"block-card\">\r\n                <div class=\"block-card-text\">\r\n                    <div class=\"block-card-title\">Time Limit Session</div>\r\n                    <div class=\"block-card-sub\">1 app</div>\r\n                    <div class=\"block-card-detail\">Monitoring • All day<br>30 minute limit</div>\r\n                </div>\r\n                <div class=\"block-card-icon\">\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                            d=\"M12 6v6l4 2m-4-14a10 10 0 100 20 10 10 0 000-20z\" />\r\n                    </svg>\r\n                    <span class=\"usage-text\">Usage</span>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"block-buttons\">\r\n                <button class=\"block-btn\">\r\n                    <svg viewBox=\"0 0 24 24\"><path d=\"M19 3h-1V1h-2v2H8V1H6v2H5a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2V5a2 2 0 00-2-2zm0 16H5V8h14v11zM7 10h5v5H7v-5z\" fill=\"currentColor\"/></svg>\r\n                    Schedule\r\n                </button>\r\n                <button class=\"block-btn\">\r\n                    <svg viewBox=\"0 0 24 24\"><path d=\"M12 7v5l4 2M12 1a11 11 0 100 22 11 11 0 000-22z\" fill=\"currentColor\"/></svg>\r\n                    Time Limit\r\n                </button>\r\n                <button class=\"block-btn\">\r\n                    <svg viewBox=\"0 0 24 24\"><path d=\"M13 2.05v2.01a8.001 8.001 0 11-6.36 13.41l1.45-1.45a6.001 6.001 0 107.91-9.27V11h-2V2.05z\" fill=\"currentColor\"/></svg>\r\n                    Block Now\r\n                </button>\r\n            </div>\r\n        </section>\r\n\r\n\r\n    </div>\r\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,iBAAiB;;;;;ICW9BC,EAAA,CAAAC,cAAA,cAAuD;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAnBH,EAAA,CAAAI,SAAA,EAAa;IAAbJ,EAAA,CAAAK,iBAAA,CAAAC,UAAA,CAAa;;;;;;IAWpEN,EAAA,CAAAC,cAAA,cAAgG;IAC5FD,EAAA,CAAAO,SAAA,iBAI+D;IACnEP,EAAA,CAAAG,YAAA,EAAM;;;;IADMH,EAAA,CAAAI,SAAA,EAA6C;IAA7CJ,EAAA,CAAAQ,WAAA,QAAAC,OAAA,CAAAC,qBAAA,MAA6C;;;;;;;IAX7DV,EAAA,CAAAC,cAAA,cAKwD;IAAnDD,EAAA,CAAAW,UAAA,mBAAAC,+CAAA;MAAA,MAAAH,OAAA,GAAAT,EAAA,CAAAa,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,EAAAT,OAAA,CAAAU,SAAA,IAA4BH,MAAA,CAAAI,UAAA,CAAAX,OAAA,CAAAY,IAAA,CAAqB;IAAA,EAAC;IACnDrB,EAAA,CAAAsB,UAAA,IAAAC,oCAAA,kBAAgG;IAOhGvB,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAC7CF,EAD6C,CAAAG,YAAA,EAAO,EAC9C;;;;IAVDH,EAFA,CAAAQ,WAAA,WAAAC,OAAA,CAAAe,QAAA,CAA8B,aAAAf,OAAA,CAAAgB,WAAA,CACK,aAAAhB,OAAA,CAAAU,SAAA,CACF;IAEcnB,EAAA,CAAAI,SAAA,EAA8C;IAA9CJ,EAAA,CAAA0B,UAAA,SAAAjB,OAAA,CAAAkB,YAAA,SAAAlB,OAAA,CAAAU,SAAA,CAA8C;IAOnEnB,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAK,iBAAA,CAAAI,OAAA,CAAAmB,GAAA,CAAc;;;ADTzD,OAAM,MAAOC,SAAS;EAyBpBC,YAAA;IAxBA,KAAAC,YAAY,GAAS,IAAIC,IAAI,EAAE;IAC/B,KAAAC,SAAS,GAAe,EAAE;IAC1B,KAAAC,QAAQ,GAAa,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACtE,KAAAC,UAAU,GAAW,OAAO;IAE5B,KAAAC,SAAS,GAAG,EAAE;IACd,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,uBAAuB,GAAG,CAAC;IAE3B,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,cAAc,GAAG,CAAC;IAElB,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,aAAa,GAAQ,IAAI;IAEzB,KAAAC,iBAAiB,GAAG,CAAC;IACrB,KAAAC,cAAc,GAAG,CAAC;IAClB,KAAAC,aAAa,GAAG,CAAC;EAEF;EAEfC,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;IAEnB,IAAI,CAACC,SAAS,EAAE;IAEhB,IAAI,CAACC,UAAU,EAAE;IAEjB,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACT,aAAa,EAAE;MACtBU,aAAa,CAAC,IAAI,CAACV,aAAa,CAAC;IACnC;EACF;EAEAK,YAAYA,CAAA;IACV,MAAMM,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IACtD,IAAIF,QAAQ,EAAE;MACZ,MAAMG,cAAc,GAAGC,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC;MAC3C,IAAI,CAACtB,SAAS,GAAGyB,cAAc,CAACzB,SAAS,IAAI,EAAE;MAC/C,IAAI,CAACC,SAAS,GAAGwB,cAAc,CAACxB,SAAS,IAAI,CAAC;MAC9C,IAAI,CAACC,aAAa,GAAGuB,cAAc,CAACvB,aAAa,IAAI,EAAE;MACvD,IAAI,CAACC,uBAAuB,GAAGsB,cAAc,CAACtB,uBAAuB,IAAI,CAAC;IAC5E;EACF;EAEAyB,YAAYA,CAAA;IACV,MAAMN,QAAQ,GAAG;MACftB,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCC,uBAAuB,EAAE,IAAI,CAACA;KAC/B;IAEDoB,YAAY,CAACM,OAAO,CAAC,eAAe,EAAEH,IAAI,CAACI,SAAS,CAACR,QAAQ,CAAC,CAAC;IAE/D,IAAI,CAACJ,UAAU,EAAE;EACnB;EAEAD,SAASA,CAAA;IACP,MAAMc,KAAK,GAAGR,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,IAAIO,KAAK,EAAE;MACT,MAAMC,WAAW,GAAGN,IAAI,CAACC,KAAK,CAACI,KAAK,CAAC;MACrC,IAAI,CAACnB,iBAAiB,GAAGoB,WAAW,CAACpB,iBAAiB,IAAI,CAAC;MAC3D,IAAI,CAACC,cAAc,GAAGmB,WAAW,CAACnB,cAAc,IAAI,CAAC;MACrD,IAAI,CAACC,aAAa,GAAGkB,WAAW,CAAClB,aAAa,IAAI,CAAC;IACrD;EACF;EAEAmB,SAASA,CAAA;IACP,MAAMF,KAAK,GAAG;MACZnB,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;MACzCC,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCC,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCoB,eAAe,EAAE,IAAItC,IAAI,EAAE,CAACuC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;KACvD;IAEDb,YAAY,CAACM,OAAO,CAAC,YAAY,EAAEH,IAAI,CAACI,SAAS,CAACC,KAAK,CAAC,CAAC;EAC3D;EAEAM,UAAUA,CAAA;IACR,IAAI,IAAI,CAACjC,SAAS,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;IAEtC,IAAI,IAAI,CAACA,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,GAAG,KAAK;IACvB,CAAC,MAAM;MACL,IAAI,CAACD,SAAS,GAAG,IAAI;IACvB;IAEA,IAAI,CAACO,aAAa,GAAG2B,WAAW,CAAC,MAAK;MACpC,IAAI,IAAI,CAAC5B,YAAY,KAAK,CAAC,EAAE;QAC3B,IAAI,IAAI,CAACD,YAAY,KAAK,CAAC,EAAE;UAC3B,IAAI,CAAC8B,cAAc,EAAE;QACvB,CAAC,MAAM;UACL,IAAI,CAAC9B,YAAY,EAAE;UACnB,IAAI,CAACC,YAAY,GAAG,EAAE;QACxB;MACF,CAAC,MAAM;QACL,IAAI,CAACA,YAAY,EAAE;MACrB;IACF,CAAC,EAAE,IAAI,CAAC;EACV;EAEA8B,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACpC,SAAS,EAAE;IAErB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpBgB,aAAa,CAAC,IAAI,CAACV,aAAa,CAAC;EACnC;EAEAO,UAAUA,CAAA;IACR,IAAI,IAAI,CAACP,aAAa,EAAE;MACtBU,aAAa,CAAC,IAAI,CAACV,aAAa,CAAC;IACnC;IAEA,IAAI,CAACP,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;IAErB,IAAI,IAAI,CAACC,OAAO,EAAE;MAChB,IAAI,IAAI,CAACC,WAAW,EAAE;QACpB,IAAI,CAACE,YAAY,GAAG,IAAI,CAACP,aAAa;MACxC,CAAC,MAAM;QACL,IAAI,CAACO,YAAY,GAAG,IAAI,CAACR,SAAS;MACpC;IACF,CAAC,MAAM;MACL,IAAI,CAACQ,YAAY,GAAG,IAAI,CAACT,SAAS;IACpC;IAEA,IAAI,CAACU,YAAY,GAAG,CAAC;EACvB;EAEA6B,cAAcA,CAAA;IACZlB,aAAa,CAAC,IAAI,CAACV,aAAa,CAAC;IAEjC,IAAI,CAAC8B,qBAAqB,EAAE;IAE5B,IAAI,IAAI,CAACnC,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,GAAG,KAAK;MACpB,IAAI,CAACG,YAAY,GAAG,IAAI,CAACT,SAAS;IACpC,CAAC,MAAM;MACL,IAAI,CAACY,iBAAiB,EAAE;MACxB,IAAI,CAACC,cAAc,IAAI,IAAI,CAACb,SAAS;MAErC,IAAI,CAAC0C,YAAY,EAAE;MAEnB,IAAI,CAACT,SAAS,EAAE;MAEhB,IAAI,IAAI,CAACzB,cAAc,GAAG,IAAI,CAACL,uBAAuB,KAAK,CAAC,EAAE;QAC5D,IAAI,CAACI,WAAW,GAAG,IAAI;QACvB,IAAI,CAACE,YAAY,GAAG,IAAI,CAACP,aAAa;MACxC,CAAC,MAAM;QACL,IAAI,CAACK,WAAW,GAAG,KAAK;QACxB,IAAI,CAACE,YAAY,GAAG,IAAI,CAACR,SAAS;MACpC;MAEA,IAAI,CAACK,OAAO,GAAG,IAAI;MACnB,IAAI,CAACE,cAAc,EAAE;IACvB;IAEA,IAAI,CAACE,YAAY,GAAG,CAAC;IACrB,IAAI,CAACN,SAAS,GAAG,KAAK;EACxB;EAEAqC,qBAAqBA,CAAA;IACnB,MAAME,KAAK,GAAG,IAAIC,KAAK,CAAC,gCAAgC,CAAC;IACzDD,KAAK,CAACE,IAAI,EAAE,CAACC,KAAK,CAACC,KAAK,IAAG,CAC3B,CAAC,CAAC;EACJ;EAEAL,YAAYA,CAAA;IACV,MAAMX,KAAK,GAAGR,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,IAAIO,KAAK,EAAE;MACT,MAAMC,WAAW,GAAGN,IAAI,CAACC,KAAK,CAACI,KAAK,CAAC;MACrC,MAAMG,eAAe,GAAGF,WAAW,CAACE,eAAe;MACnD,MAAMc,KAAK,GAAG,IAAIpD,IAAI,EAAE,CAACuC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpD,MAAMa,SAAS,GAAG,IAAIrD,IAAI,CAACA,IAAI,CAACsD,GAAG,EAAE,GAAG,QAAQ,CAAC,CAACf,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAE7E,IAAIF,eAAe,KAAKe,SAAS,EAAE;QACjC,IAAI,CAACnC,aAAa,EAAE;MACtB,CAAC,MAAM,IAAIoB,eAAe,KAAKc,KAAK,EAAE;QACpC,IAAI,CAAClC,aAAa,GAAG,CAAC;MACxB;IACF,CAAC,MAAM;MACL,IAAI,CAACA,aAAa,GAAG,CAAC;IACxB;EACF;EAEAK,iBAAiBA,CAAA;IACf,MAAM6B,KAAK,GAAG,IAAIpD,IAAI,EAAE;IACxB,MAAMuD,UAAU,GAAGH,KAAK,CAACI,MAAM,EAAE,IAAI,CAAC;IACtC,MAAMC,SAAS,GAAG,IAAIzD,IAAI,CAACoD,KAAK,CAAC;IACjCK,SAAS,CAACC,OAAO,CAACN,KAAK,CAACO,OAAO,EAAE,GAAGJ,UAAU,GAAG,CAAC,CAAC;IAEnD,IAAI,CAACtD,SAAS,GAAG,EAAE;IACnB,KAAK,IAAI2D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B,MAAMvE,IAAI,GAAG,IAAIW,IAAI,CAACyD,SAAS,CAAC;MAChCpE,IAAI,CAACqE,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAGC,CAAC,CAAC;MAErC,MAAMC,UAAU,GAAG,IAAI,CAACC,UAAU,CAACzE,IAAI,CAAC;MACxC,MAAM0E,OAAO,GAAG,IAAI,CAACC,SAAS,CAAC3E,IAAI,EAAE+D,KAAK,CAAC;MAC3C,MAAMa,UAAU,GAAG,IAAI,CAACD,SAAS,CAAC3E,IAAI,EAAE,IAAI,CAACU,YAAY,CAAC;MAC1D,MAAMmE,QAAQ,GAAG7E,IAAI,GAAG+D,KAAK;MAE7B,IAAI,CAACnD,SAAS,CAACkE,IAAI,CAAC;QAClB9E,IAAI,EAAEwE,UAAU;QAChBjE,GAAG,EAAEP,IAAI,CAACsE,OAAO,EAAE;QACnBnE,QAAQ,EAAEuE,OAAO;QACjBtE,WAAW,EAAEwE,UAAU;QACvB9E,SAAS,EAAE+E,QAAQ;QACnBvE,YAAY,EAAE,CAAC;QACfyE,gBAAgB,EAAE,CAAC;QACnB1F,qBAAqB,EAAE;OACxB,CAAC;IACJ;EACF;EAEAU,UAAUA,CAACyE,UAAkB;IAC3B,MAAMxE,IAAI,GAAG,IAAIW,IAAI,CAAC6D,UAAU,CAAC;IACjC,IAAI,CAAC9D,YAAY,GAAGV,IAAI;IAExB,IAAI,CAACY,SAAS,CAACoE,OAAO,CAACC,QAAQ,IAAG;MAChCA,QAAQ,CAAC7E,WAAW,GAAG6E,QAAQ,CAACjF,IAAI,KAAKwE,UAAU;IACrD,CAAC,CAAC;IAEF,IAAI,CAACU,gBAAgB,EAAE;EACzB;EAEAC,UAAUA,CAACC,SAAiB;IAC1B,MAAMC,SAAS,GAAG,IAAI1E,IAAI,CAAC,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC,CAACZ,IAAI,CAAC;IAClDqF,SAAS,CAAChB,OAAO,CAACgB,SAAS,CAACf,OAAO,EAAE,GAAIc,SAAS,GAAG,CAAE,CAAC;IAExD,MAAMlB,UAAU,GAAGmB,SAAS,CAAClB,MAAM,EAAE,IAAI,CAAC;IAC1C,MAAMC,SAAS,GAAG,IAAIzD,IAAI,CAAC0E,SAAS,CAAC;IACrCjB,SAAS,CAACC,OAAO,CAACgB,SAAS,CAACf,OAAO,EAAE,GAAGJ,UAAU,GAAG,CAAC,CAAC;IAEvD,IAAI,CAACtD,SAAS,GAAG,EAAE;IACnB,KAAK,IAAI2D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B,MAAMvE,IAAI,GAAG,IAAIW,IAAI,CAACyD,SAAS,CAAC;MAChCpE,IAAI,CAACqE,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAGC,CAAC,CAAC;MAErC,MAAMC,UAAU,GAAG,IAAI,CAACC,UAAU,CAACzE,IAAI,CAAC;MACxC,MAAM+D,KAAK,GAAG,IAAIpD,IAAI,EAAE;MACxB,MAAM+D,OAAO,GAAG,IAAI,CAACC,SAAS,CAAC3E,IAAI,EAAE+D,KAAK,CAAC;MAC3C,MAAMa,UAAU,GAAG,IAAI,CAACD,SAAS,CAAC3E,IAAI,EAAE,IAAI,CAACU,YAAY,CAAC;MAC1D,MAAMmE,QAAQ,GAAG7E,IAAI,GAAG+D,KAAK;MAE7B,IAAI,CAACnD,SAAS,CAACkE,IAAI,CAAC;QAClB9E,IAAI,EAAEwE,UAAU;QAChBjE,GAAG,EAAEP,IAAI,CAACsE,OAAO,EAAE;QACnBnE,QAAQ,EAAEuE,OAAO;QACjBtE,WAAW,EAAEwE,UAAU;QACvB9E,SAAS,EAAE+E,QAAQ;QACnBvE,YAAY,EAAE,CAAC;QACfyE,gBAAgB,EAAE,CAAC;QACnB1F,qBAAqB,EAAE;OACxB,CAAC;IACJ;EACF;EAEA6F,gBAAgBA,CAAA;IACd,MAAMnB,KAAK,GAAG,IAAIpD,IAAI,EAAE;IACxB,IAAI,IAAI,CAACgE,SAAS,CAAC,IAAI,CAACjE,YAAY,EAAEqD,KAAK,CAAC,EAAE;MAC5C,IAAI,CAACjD,UAAU,GAAG,OAAO;IAC3B,CAAC,MAAM;MACL,IAAI,CAACA,UAAU,GAAG,IAAI,CAACJ,YAAY,CAAC4E,kBAAkB,CAAC,OAAO,EAAE;QAC9DC,OAAO,EAAE,OAAO;QAChBhF,GAAG,EAAE,SAAS;QACdiF,KAAK,EAAE;OACR,CAAC,GAAG,UAAU;IACjB;EACF;EAEAf,UAAUA,CAACzE,IAAU;IACnB,MAAMyF,IAAI,GAAGzF,IAAI,CAAC0F,WAAW,EAAE;IAC/B,MAAMF,KAAK,GAAGG,MAAM,CAAC3F,IAAI,CAAC4F,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMtF,GAAG,GAAGoF,MAAM,CAAC3F,IAAI,CAACsE,OAAO,EAAE,CAAC,CAACuB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,OAAO,GAAGJ,IAAI,IAAID,KAAK,IAAIjF,GAAG,EAAE;EAClC;EAEAoE,SAASA,CAACmB,KAAW,EAAEC,KAAW;IAChC,OAAOD,KAAK,CAACJ,WAAW,EAAE,KAAKK,KAAK,CAACL,WAAW,EAAE,IAC3CI,KAAK,CAACF,QAAQ,EAAE,KAAKG,KAAK,CAACH,QAAQ,EAAE,IACrCE,KAAK,CAACxB,OAAO,EAAE,KAAKyB,KAAK,CAACzB,OAAO,EAAE;EAC5C;;aAnSW9D,SAAS;;mCAATA,UAAS;AAAA;;QAATA,UAAS;EAAAwF,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,mBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCrBd3H,EAFR,CAAAC,cAAA,aAAuB,aACX,aACc;MACdD,EAAA,CAAAO,SAAA,aAA6D;MAC7DP,EAAA,CAAAC,cAAA,WAAM;MAAAD,EAAA,CAAAE,MAAA,cAAO;MACjBF,EADiB,CAAAG,YAAA,EAAO,EAClB;MACNH,EAAA,CAAAC,cAAA,SAAI;MAAAD,EAAA,CAAAE,MAAA,GAAgB;MACxBF,EADwB,CAAAG,YAAA,EAAK,EACpB;MAKDH,EAFR,CAAAC,cAAA,aAA2B,aACG,iBACkC;MAAzBD,EAAA,CAAAW,UAAA,mBAAAkH,4CAAA;QAAA,OAASD,GAAA,CAAApB,UAAA,EAAY,CAAC,CAAC;MAAA,EAAC;MAACxG,EAAA,CAAAE,MAAA,cAAC;MAAAF,EAAA,CAAAG,YAAA,EAAS;MAClEH,EAAA,CAAAC,cAAA,cAAkB;MACdD,EAAA,CAAAsB,UAAA,KAAAwG,yBAAA,iBAAuD;MAC3D9H,EAAA,CAAAG,YAAA,EAAM;MACNH,EAAA,CAAAC,cAAA,iBAAuD;MAAxBD,EAAA,CAAAW,UAAA,mBAAAoH,4CAAA;QAAA,OAASH,GAAA,CAAApB,UAAA,CAAW,CAAC,CAAC;MAAA,EAAC;MAACxG,EAAA,CAAAE,MAAA,cAAC;MAC5DF,EAD4D,CAAAG,YAAA,EAAS,EAC/D;MACNH,EAAA,CAAAC,cAAA,cAAmB;MACfD,EAAA,CAAAsB,UAAA,KAAA0G,yBAAA,kBAKwD;MAWhEhI,EADI,CAAAG,YAAA,EAAM,EACJ;MAIEH,EADJ,CAAAC,cAAA,mBAAgC,aACJ;MACpBD,EAAA,CAAAE,MAAA,wEACJ;MAAAF,EAAA,CAAAG,YAAA,EAAI;MAIIH,EAFR,CAAAC,cAAA,eAA6B,WACpB,gBACsB;MAAAD,EAAA,CAAAE,MAAA,WAAG;MAAAF,EAAA,CAAAG,YAAA,EAAO;MACjCH,EAAA,CAAAC,cAAA,gBAAyB;MAAAD,EAAA,CAAAE,MAAA,mBAAW;MACxCF,EADwC,CAAAG,YAAA,EAAO,EACzC;MAEFH,EADJ,CAAAC,cAAA,WAAK,gBACsB;MAAAD,EAAA,CAAAE,MAAA,UAAE;MAAAF,EAAA,CAAAG,YAAA,EAAO;MAChCH,EAAA,CAAAC,cAAA,gBAAyB;MAAAD,EAAA,CAAAE,MAAA,gBAAQ;MAEzCF,EAFyC,CAAAG,YAAA,EAAO,EACtC,EACJ;MAGFH,EADJ,CAAAC,cAAA,eAAuB,eACG;MAAAD,EAAA,CAAAE,MAAA,kBAAU;MAAAF,EAAA,CAAAC,cAAA,YAAM;MAAAD,EAAA,CAAAE,MAAA,WAAG;MAAOF,EAAP,CAAAG,YAAA,EAAO,EAAM;MACtDH,EAAA,CAAAC,cAAA,eAAsB;MAAAD,EAAA,CAAAE,MAAA,aAAK;MAAAF,EAAA,CAAAC,cAAA,YAAM;MAAAD,EAAA,CAAAE,MAAA,WAAG;MAAOF,EAAP,CAAAG,YAAA,EAAO,EAAM;MACjDH,EAAA,CAAAC,cAAA,eAAsB;MAAAD,EAAA,CAAAE,MAAA,kBAAU;MAAAF,EAAA,CAAAC,cAAA,YAAM;MAAAD,EAAA,CAAAE,MAAA,UAAE;MAAOF,EAAP,CAAAG,YAAA,EAAO,EAAM;MACrDH,EAAA,CAAAC,cAAA,eAAsB;MAAAD,EAAA,CAAAE,MAAA,mBAAW;MAAAF,EAAA,CAAAC,cAAA,YAAM;MAAAD,EAAA,CAAAE,MAAA,UAAE;MAC7CF,EAD6C,CAAAG,YAAA,EAAO,EAAM,EACpD;MAIEH,EAFR,CAAAC,cAAA,eAAsB,eACG,YACX;;MACFD,EAAA,CAAAC,cAAA,eAA8F;MAC1FD,EAAA,CAAAO,SAAA,gBACsF;MAC1FP,EAAA,CAAAG,YAAA,EAAM;MACNH,EAAA,CAAAE,MAAA,qBACJ;MAAAF,EAAA,CAAAG,YAAA,EAAO;;MACPH,EAAA,CAAAC,cAAA,YAAM;MAAAD,EAAA,CAAAE,MAAA,sBAAc;MAAAF,EAAA,CAAAC,cAAA,aAAgC;MAAAD,EAAA,CAAAE,MAAA,cAAM;MAC9DF,EAD8D,CAAAG,YAAA,EAAI,EAAO,EACnE;MAGFH,EADJ,CAAAC,cAAA,eAAqB,YACX;;MACFD,EAAA,CAAAC,cAAA,eAA8F;MAC1FD,EAAA,CAAAO,SAAA,gBACwH;MAC5HP,EAAA,CAAAG,YAAA,EAAM;MACNH,EAAA,CAAAE,MAAA,yBACJ;MAGZF,EAHY,CAAAG,YAAA,EAAO,EACL,EACJ,EACA;;MAENH,EADJ,CAAAC,cAAA,mBAAkC,UAC1B;MAAAD,EAAA,CAAAE,MAAA,gCAAwB;MAAAF,EAAA,CAAAG,YAAA,EAAK;MAIzBH,EAFR,CAAAC,cAAA,eAAwB,eACS,eACK;MAAAD,EAAA,CAAAE,MAAA,0BAAkB;MAAAF,EAAA,CAAAG,YAAA,EAAM;MACtDH,EAAA,CAAAC,cAAA,eAA4B;MAAAD,EAAA,CAAAE,MAAA,aAAK;MAAAF,EAAA,CAAAG,YAAA,EAAM;MACvCH,EAAA,CAAAC,cAAA,eAA+B;MAAAD,EAAA,CAAAE,MAAA,iCAAoB;MAAAF,EAAA,CAAAO,SAAA,UAAI;MAAAP,EAAA,CAAAE,MAAA,uBAAe;MAC1EF,EAD0E,CAAAG,YAAA,EAAM,EAC1E;MACNH,EAAA,CAAAC,cAAA,eAA6B;;MACzBD,EAAA,CAAAC,cAAA,eAA8F;MAC1FD,EAAA,CAAAO,SAAA,gBAC2D;MAC/DP,EAAA,CAAAG,YAAA,EAAM;;MACNH,EAAA,CAAAC,cAAA,gBAAyB;MAAAD,EAAA,CAAAE,MAAA,aAAK;MAEtCF,EAFsC,CAAAG,YAAA,EAAO,EACnC,EACJ;MAGFH,EADJ,CAAAC,cAAA,eAA2B,kBACG;;MACtBD,EAAA,CAAAC,cAAA,eAAyB;MAAAD,EAAA,CAAAO,SAAA,gBAAmJ;MAAAP,EAAA,CAAAG,YAAA,EAAM;MAClLH,EAAA,CAAAE,MAAA,kBACJ;MAAAF,EAAA,CAAAG,YAAA,EAAS;;MACTH,EAAA,CAAAC,cAAA,kBAA0B;;MACtBD,EAAA,CAAAC,cAAA,eAAyB;MAAAD,EAAA,CAAAO,SAAA,gBAA+E;MAAAP,EAAA,CAAAG,YAAA,EAAM;MAC9GH,EAAA,CAAAE,MAAA,oBACJ;MAAAF,EAAA,CAAAG,YAAA,EAAS;;MACTH,EAAA,CAAAC,cAAA,kBAA0B;;MACtBD,EAAA,CAAAC,cAAA,eAAyB;MAAAD,EAAA,CAAAO,SAAA,gBAAyH;MAAAP,EAAA,CAAAG,YAAA,EAAM;MACxJH,EAAA,CAAAE,MAAA,mBACJ;MAKZF,EALY,CAAAG,YAAA,EAAS,EACP,EACA,EAGR;;;MAjHEH,EAAA,CAAAI,SAAA,GAAgB;MAAhBJ,EAAA,CAAAK,iBAAA,CAAAuH,GAAA,CAAAzF,UAAA,CAAgB;MAQ8BnC,EAAA,CAAAI,SAAA,GAAW;MAAXJ,EAAA,CAAA0B,UAAA,YAAAkG,GAAA,CAAA1F,QAAA,CAAW;MAKnClC,EAAA,CAAAI,SAAA,GAAY;MAAZJ,EAAA,CAAA0B,UAAA,YAAAkG,GAAA,CAAA3F,SAAA,CAAY;;;iBDElCnC,WAAW,EAAEF,YAAY,EAAAqI,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEtI,WAAW,EAAEE,YAAY;EAAAqI,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}