﻿import { Injectable, inject } from '@angular/core';
import {
  Group,
  GroupJoinRequest,
  GroupMember,
  GroupQuest,
  GroupQuestProgress,
  GroupSideQuest,
  GroupSideQuestMemberStatus,
  GroupSideQuestPool
} from '../models/group.model';
import { Observable, map, of, switchMap, from, catchError, firstValueFrom, combineLatest } from 'rxjs';
import { User } from '../models/user.model';
import { SupabaseService } from './supabase.service';
import { XpService, EntityType } from './xp.service';

@Injectable({
  providedIn: 'root'
})
export class GroupService {
  private supabaseService = inject(SupabaseService);
  private xpService = inject(XpService);

  constructor() {}



  getUserGroups(userId: string): Observable<Group[]> {
    return this.getGroups(userId);
  }

  getTopGroups(count: number = 10): Observable<Group[]> {

    return from(
      this.supabaseService.getClient()
        .from('groups')
        .select('*')
        .order('level', { ascending: false })
        .limit(count * 2) 
    ).pipe(
      map(response => {
        if (response.error) {
          return [];
        }


        const sortedGroups = response.data.sort((a, b) => {
          if (b.level !== a.level) {
            return b.level - a.level;
          }

          const aTotalXp = (a.strength_xp || 0) + (a.money_xp || 0) + (a.health_xp || 0) + (a.knowledge_xp || 0);
          const bTotalXp = (b.strength_xp || 0) + (b.money_xp || 0) + (b.health_xp || 0) + (b.knowledge_xp || 0);
          return bTotalXp - aTotalXp;
        });

        const limitedGroups = sortedGroups.slice(0, count);


        return limitedGroups as Group[];
      }),
      catchError(error => {
        return of([]);
      })
    );
  }

  getGroups(userId: string): Observable<Group[]> {

    return from(
      this.supabaseService.getClient()
        .from('group_members')
        .select('group_id')
        .eq('user_id', userId)
    ).pipe(
      switchMap(memberResponse => {
        if (memberResponse.error) {
          return of([]);
        }

        if (memberResponse.data.length === 0) {
          return of([]);
        }

        const groupIds = memberResponse.data.map(membership => membership.group_id);

        return from(
          this.supabaseService.getClient()
            .from('groups')
            .select('*')
            .in('id', groupIds)
        ).pipe(
          map(groupResponse => {
            if (groupResponse.error) {
              return [];
            }

            return groupResponse.data as Group[];
          }),
          catchError(error => {
            return of([]);
          })
        );
      })
    );
  }

  getGroup(groupId: string): Observable<Group | null> {

    return from(
      this.supabaseService.getClient()
        .from('groups')
        .select('*')
        .eq('id', groupId)
        .single()
    ).pipe(
      map(response => {
        if (response.error) {
          return null;
        }

        return response.data as Group;
      }),
      catchError(error => {
        return of(null);
      })
    );
  }

  async createGroup(group: Omit<Group, 'id' | 'created'>): Promise<string> {

    const newGroup = {
      ...group,
      created: new Date().toISOString()
    };


    const { data: groupData, error: groupError } = await this.supabaseService.getClient()
      .from('groups')
      .insert(newGroup)
      .select()
      .single();

    if (groupError) {
      throw new Error(groupError.message);
    }


    let nickname: string = 'Admin'; 

    try {
      const { data, error } = await this.supabaseService.getClient()
        .from('profiles')
        .select('username')
        .eq('id', group.admin_id)
        .single();

      if (!error && data && data.username) {
        nickname = data.username;
      }
    } catch (err) {
    }

    const member = {
      group_id: groupData.id,
      user_id: group.admin_id,
      nickname: nickname,
      is_admin: true,
      joined_date: new Date().toISOString()
    };


    const { error: memberError } = await this.supabaseService.getClient()
      .from('group_members')
      .insert(member);

    if (memberError) {
      throw new Error(memberError.message);
    }

    return groupData.id;
  }

  async updateGroup(groupId: string, data: Partial<Group>): Promise<void> {

    if (data.name) {
      const isNameAvailable = await this.checkGroupNameAvailability(data.name, groupId);
      if (!isNameAvailable) {
        throw new Error('A group with this name already exists. Please choose a different name.');
      }
    }

    const { error } = await this.supabaseService.getClient()
      .from('groups')
      .update(data)
      .eq('id', groupId);

    if (error) {
      throw new Error(error.message);
    }

  }

  async checkGroupNameAvailability(name: string, excludeGroupId?: string): Promise<boolean> {

    if (!name) {
      return false;
    }

    try {
      let query = this.supabaseService.getClient()
        .from('groups')
        .select('id')
        .eq('name', name)
        .limit(1);

      if (excludeGroupId) {
        query = query.neq('id', excludeGroupId);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(error.message);
      }

      const isAvailable = !data || data.length === 0;
      return isAvailable;
    } catch (error) {
      throw error;
    }
  }

  async deleteGroup(groupId: string): Promise<void> {

    const { error } = await this.supabaseService.getClient()
      .from('groups')
      .delete()
      .eq('id', groupId);

    if (error) {
      throw new Error(error.message);
    }

  }

  getGroupMembers(groupId: string): Observable<GroupMember[]> {

    return from(
      this.supabaseService.getClient()
        .from('group_members')
        .select('*')
        .eq('group_id', groupId)
    ).pipe(
      map(response => {
        if (response.error) {
          return [];
        }

        return response.data as GroupMember[];
      }),
      catchError(error => {
        return of([]);
      })
    );
  }

  getGroupMembersWithProfiles(groupId: string): Observable<any[]> {

    return this.getGroupMembers(groupId).pipe(
      switchMap(members => {
        if (members.length === 0) {
          return of([]);
        }

        const memberIds = members.map(member => member.user_id);

        return from(
          this.supabaseService.getClient()
            .from('profiles')
            .select('*')
            .in('id', memberIds)
        ).pipe(
          map(response => {
            if (response.error) {
              return [];
            }

            const profiles = response.data as User[];

            return members.map(member => {
              const profile = profiles.find(p => p.id === member.user_id);
              return {
                ...member,
                profile: profile || null
              };
            });
          }),
          catchError(error => {
            return of([]);
          })
        );
      })
    );
  }

  async addGroupMemberWithOptionalNickname(groupId: string, userId: string, nickname?: string): Promise<void> {

    let memberNickname: string = nickname || '';

    if (!memberNickname) {
      try {
        const { data, error } = await this.supabaseService.getClient()
          .from('profiles')
          .select('username')
          .eq('id', userId)
          .single();

        if (error) {
          memberNickname = 'Member'; 
        } else if (data && data.username) {
          memberNickname = data.username;
        } else {
          memberNickname = 'Member'; 
        }
      } catch (err) {
        memberNickname = 'Member'; 
      }
    }

    const member: GroupMember = {
      group_id: groupId,
      user_id: userId,
      nickname: memberNickname,
      is_admin: false,
      joined_date: new Date()
    };

    const { error } = await this.supabaseService.getClient()
      .from('group_members')
      .insert(member);

    if (error) {
      throw new Error(error.message);
    }

  }

  async addGroupMember(groupId: string, userId: string, nickname: string): Promise<void> {

    const member: GroupMember = {
      group_id: groupId,
      user_id: userId,
      nickname: nickname,
      is_admin: false,
      joined_date: new Date()
    };

    const { error } = await this.supabaseService.getClient()
      .from('group_members')
      .insert(member);

    if (error) {
      throw new Error(error.message);
    }

  }

  async removeGroupMember(groupId: string, userId: string): Promise<void> {

    const { data, error: findError } = await this.supabaseService.getClient()
      .from('group_members')
      .select('id')
      .eq('group_id', groupId)
      .eq('user_id', userId)
      .single();

    if (findError || !data) {
      return;
    }

    const { error: deleteError } = await this.supabaseService.getClient()
      .from('group_members')
      .delete()
      .eq('id', data.id);

    if (deleteError) {
      throw new Error(deleteError.message);
    }

  }

  async updateGroupMember(memberId: string, data: Partial<GroupMember>): Promise<void> {

    const { error } = await this.supabaseService.getClient()
      .from('group_members')
      .update(data)
      .eq('id', memberId);

    if (error) {
      throw new Error(error.message);
    }

  }

  async removeGroupMemberById(groupId: string, memberId: string): Promise<void> {

    const { error } = await this.supabaseService.getClient()
      .from('group_members')
      .delete()
      .eq('id', memberId);

    if (error) {
      throw new Error(error.message);
    }

  }


  async leaveGroup(groupId: string): Promise<void> {

    const userId = this.supabaseService.getCurrentUserId();
    if (!userId) {
      throw new Error('User not authenticated');
    }

    const { data: memberData, error: memberError } = await this.supabaseService.getClient()
      .from('group_members')
      .select('id, is_admin')
      .eq('group_id', groupId)
      .eq('user_id', userId)
      .single();

    if (memberError || !memberData) {
      throw new Error('Member not found in group');
    }

    const isAdmin = memberData.is_admin;

    const { data: allMembers, error: allMembersError } = await this.supabaseService.getClient()
      .from('group_members')
      .select('id, user_id, is_admin')
      .eq('group_id', groupId);

    if (allMembersError) {
      throw new Error(allMembersError.message);
    }

    if (allMembers.length === 1) {
      await this.deleteGroup(groupId);
      return;
    }

    if (isAdmin) {
      const otherMembers = allMembers.filter(m => m.user_id !== userId);
      if (otherMembers.length > 0) {
        const newAdmin = otherMembers[Math.floor(Math.random() * otherMembers.length)];

        const { error: updateError } = await this.supabaseService.getClient()
          .from('group_members')
          .update({ is_admin: true })
          .eq('id', newAdmin.id);

        if (updateError) {
          throw new Error(updateError.message);
        }
      }
    }

    const { error: deleteError } = await this.supabaseService.getClient()
      .from('group_members')
      .delete()
      .eq('id', memberData.id);

    if (deleteError) {
      throw new Error(deleteError.message);
    }

  }

  getGroupJoinRequests(groupId: string): Observable<GroupJoinRequest[]> {

    return from(
      this.supabaseService.getClient()
        .from('group_join_requests')
        .select('*')
        .eq('group_id', groupId)
    ).pipe(
      map(response => {
        if (response.error) {
          return [];
        }

        return response.data as GroupJoinRequest[];
      }),
      catchError(error => {
        return of([]);
      })
    );
  }

  getGroupJoinRequestsForUser(username: string): Observable<GroupJoinRequest[]> {

    );
    .map((c: string) => c.charCodeAt(0)));



    return from(
      this.supabaseService.getClient()
        .from('group_join_requests')
        .select('*, groups(name, emoji)')
        .order('created', { ascending: false })
    ).pipe(
      switchMap(response => {
        if (response.error) {

          if (response.error.code === '42501' || response.error.message?.includes('permission denied')) {
          }

          return of([]);
        }


        );

        const filteredRequests = response.data.filter((req: any) => {

          if (!req.username_invited) {
            return false;
          }

          const reqUsername = req.username_invited.trim().toLowerCase();
          const targetUsername = username.trim().toLowerCase();

          return reqUsername === targetUsername;
        });


        if (filteredRequests.length === 0) {
          return of([]);
        }

        const requestsWithInviterDetails$ = filteredRequests.map((request: any) => {

          );

          if (!request.invited_by) {
            return of({
              ...request,
              inviter_username: 'Unknown'
            } as GroupJoinRequest);
          }


          return of({
            ...request,
            inviter_username: request.invited_by
          } as GroupJoinRequest);
        });

        return combineLatest(requestsWithInviterDetails$);
      }),
      catchError(error => {
        return of([]);
      })
    );
  }

  getJoinRequestsForUserId(userId: string): Observable<GroupJoinRequest[]> {

    return from(
      this.supabaseService.getClient()
        .from('profiles')
        .select('username')
        .eq('id', userId)
        .single()
    ).pipe(
      switchMap(userResponse => {
        if (userResponse.error) {
          return of([]);
        }

        const username = userResponse.data.username;

        return this.getGroupJoinRequestsForUser(username);
      }),
      catchError(error => {
        return of([]);
      })
    );
  }

  async createGroupJoinRequest(request: Omit<GroupJoinRequest, 'id' | 'created'>): Promise<string> {

    const newRequest: Omit<GroupJoinRequest, 'id'> = {
      ...request,
      created: new Date(),
      requested_at: new Date(),
      status: 'pending'
    };

    const { data, error } = await this.supabaseService.getClient()
      .from('group_join_requests')
      .insert(newRequest)
      .select('id')
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data.id;
  }

  async acceptGroupJoinRequest(requestId: string, adminUserId: string): Promise<void> {

    const { data: request, error: getError } = await this.supabaseService.getClient()
      .from('group_join_requests')
      .select('*')
      .eq('id', requestId)
      .single();

    if (getError || !request) {
      throw new Error('Join request not found');
    }


    const username = request.username_invited;

    if (!username) {
      throw new Error('Username not found in request');
    }

    const { data: userData, error: userError } = await this.supabaseService.getClient()
      .from('profiles')
      .select('id')
      .eq('username', username)
      .single();

    if (userError || !userData) {
      throw new Error('User not found');
    }

    const userId = userData.id;

    const nickname: string = username;

    const member = {
      group_id: request.group_id,
      user_id: userId,
      nickname: nickname, 
      is_admin: false,
      joined_date: new Date()
    } as GroupMember;

    const { error: insertError } = await this.supabaseService.getClient()
      .from('group_members')
      .insert(member);

    if (insertError) {
      throw new Error(insertError.message);
    }


    const { error: deleteError } = await this.supabaseService.getClient()
      .from('group_join_requests')
      .delete()
      .eq('id', requestId);

    if (deleteError) {
    }

  }

  async rejectGroupJoinRequest(requestId: string): Promise<void> {

    const { error } = await this.supabaseService.getClient()
      .from('group_join_requests')
      .delete()
      .eq('id', requestId);

    if (error) {
      throw new Error(error.message);
    }

  }

  async generateGroupInvitationCode(groupId: string): Promise<string> {

    const code = Math.random().toString(36).substring(2, 10).toUpperCase();

    const expiry = new Date();
    expiry.setDate(expiry.getDate() + 1);

    const { error } = await this.supabaseService.getClient()
      .from('groups')
      .update({
        invitation_code: code,
        code_expiry: expiry
      })
      .eq('id', groupId);

    if (error) {
      throw new Error(error.message);
    }

    return code;
  }

  async joinGroupByCode(userId: string, code: string, nickname?: string): Promise<boolean> {

    const { data: groups, error: findError } = await this.supabaseService.getClient()
      .from('groups')
      .select('*')
      .eq('invitation_code', code);

    if (findError || !groups || groups.length === 0) {
      return false; 
    }

    const group = groups[0] as Group;
    const groupId = group.id;


    const expiryDate = group.code_expiry ? new Date(group.code_expiry) : null;
    if (!expiryDate || expiryDate < new Date()) {
      return false; 
    }

    const { data: members, error: memberError } = await this.supabaseService.getClient()
      .from('group_members')
      .select('id')
      .eq('group_id', groupId)
      .eq('user_id', userId);

    if (memberError) {
      return false;
    }

    if (members && members.length > 0) {
      return false; 
    }

    let memberNickname: string = nickname || '';

    if (!memberNickname) {
      try {
        const { data, error } = await this.supabaseService.getClient()
          .from('profiles')
          .select('username')
          .eq('id', userId)
          .single();

        if (error) {
          memberNickname = 'Member'; 
        } else if (data && data.username) {
          memberNickname = data.username;
        } else {
          memberNickname = 'Member'; 
        }
      } catch (err) {
        memberNickname = 'Member'; 
      }
    }

    const member = {
      group_id: groupId,
      user_id: userId,
      nickname: memberNickname,
      is_admin: false,
      joined_date: new Date()
    } as GroupMember;

    const { error: insertError } = await this.supabaseService.getClient()
      .from('group_members')
      .insert(member);

    if (insertError) {
      return false;
    }


    const { error: updateError } = await this.supabaseService.getClient()
      .from('groups')
      .update({
        invitation_code: null,
        code_expiry: null
      })
      .eq('id', groupId);

    if (updateError) {
    }

    return true;
  }

  getGroupQuests(groupId: string): Observable<GroupQuest[]> {

    return from(
      this.supabaseService.getClient()
        .from('group_quests')
        .select('*')
        .eq('group_id', groupId)
    ).pipe(
      map(response => {
        if (response.error) {
          return [];
        }

        return response.data as GroupQuest[];
      }),
      catchError(error => {
        return of([]);
      })
    );
  }

  getGroupQuest(questId: string): Observable<GroupQuest> {

    return from(
      this.supabaseService.getClient()
        .from('group_quests')
        .select('*')
        .eq('id', questId)
        .single()
    ).pipe(
      map(response => {
        if (response.error) {
          throw new Error(response.error.message);
        }

        return response.data as GroupQuest;
      }),
      catchError(error => {
        throw error;
      })
    );
  }

  async createGroupQuest(quest: Omit<GroupQuest, 'id' | 'created' | 'streak'>): Promise<string> {
    );

    if (!quest.priority) {
      quest.priority = 'basic';
    }

    if (!quest.name || !quest.category || !quest.quest_type || !quest.goal_unit || !quest.goal_period) {
      throw new Error('Missing required fields for quest creation');
    }

    if (!quest.emoji) {
      quest.emoji = '🎯';
    }

    const newQuest: Omit<GroupQuest, 'id'> = {
      ...quest,
      created: new Date(),
      streak: 0
    };

    );

    try {
      const { data, error } = await this.supabaseService.getClient()
        .from('group_quests')
        .insert(newQuest)
        .select('id')
        .single();

      if (error) {
        throw new Error(error.message);
      }

      if (!data || !data.id) {
        throw new Error('Failed to create quest: No data returned');
      }

      return data.id;
    } catch (error) {
      throw error;
    }
  }

  async updateGroupQuest(questId: string, data: Partial<GroupQuest>): Promise<void> {

    const { error } = await this.supabaseService.getClient()
      .from('group_quests')
      .update(data)
      .eq('id', questId);

    if (error) {
      throw new Error(error.message);
    }

  }

  async deleteGroupQuest(questId: string): Promise<void> {

    try {
      const { data: quest, error: questError } = await this.supabaseService.getClient()
        .from('group_quests')
        .select('*')
        .eq('id', questId)
        .single();

      if (questError) {
        throw new Error('Failed to get quest details for deletion');
      }

      if (quest && quest.streak > 0) {
        const groupId = quest.group_id;
        const category = quest.category;
        const priority = quest.priority;

        const { data: members, error: membersError } = await this.supabaseService.getClient()
          .from('group_members')
          .select('user_id, joined_date')
          .eq('group_id', groupId);

        if (!membersError && members && members.length > 0) {
          const today = new Date();
          const eligibleMembers = members.filter(member => {
            const joinDate = new Date(member.joined_date);
            joinDate.setHours(0, 0, 0, 0);
            const todayDate = new Date(today);
            todayDate.setHours(0, 0, 0, 0);
            return joinDate < todayDate;
          });

          if (eligibleMembers.length > 0) {
            const xpValue = priority === 'high' ? 2 : 1;
            const totalXP = xpValue * eligibleMembers.length;
            const xpField = `${category}_xp`;

            const { data: group, error: groupError } = await this.supabaseService.getClient()
              .from('groups')
              .select('*')
              .eq('id', groupId)
              .single();

            if (!groupError && group) {
              let currentXP = group[xpField as keyof typeof group] as number || 0;

              currentXP = Math.max(0, currentXP - (totalXP * quest.streak));

              const { error: xpUpdateError } = await this.supabaseService.getClient()
                .from('groups')
                .update({ [xpField]: currentXP })
                .eq('id', groupId);

              if (xpUpdateError) {
              } else {
              }
            }
          }
        }
      }

      const { error } = await this.supabaseService.getClient()
        .from('group_quests')
        .delete()
        .eq('id', questId);

      if (error) {
        throw new Error(error.message);
      }

    } catch (error) {
      throw error;
    }
  }

  getGroupQuestProgress(questId: string, userId: string, date: Date): Observable<GroupQuestProgress | undefined> {
    const dateString = date.toISOString().split('T')[0]; 

    return from(
      this.supabaseService.getClient()
        .from('group_quest_progress')
        .select('*')
        .eq('quest_id', questId)
        .eq('user_id', userId)
        .eq('date', dateString)
    ).pipe(
      map(response => {
        if (response.error) {
          return undefined;
        }

        if (response.data && response.data.length > 0) {
          return response.data[0] as GroupQuestProgress;
        } else {
          return undefined;
        }
      }),
      catchError(error => {
        return of(undefined);
      })
    );
  }

  getGroupQuestProgressForDate(questId: string, date: Date): Observable<GroupQuestProgress[]> {
    const dateString = date.toISOString().split('T')[0]; 

    return from(
      this.supabaseService.getClient()
        .from('group_quest_progress')
        .select('*')
        .eq('quest_id', questId)
        .eq('date', dateString)
    ).pipe(
      map(response => {
        if (response.error) {
          return [];
        }

        return response.data as GroupQuestProgress[];
      }),
      catchError(error => {
        return of([]);
      })
    );
  }

  async canUserParticipateInGroupQuests(userId: string, groupId: string, date: Date): Promise<boolean> {
    .split('T')[0]);

    try {
      const { data, error } = await this.supabaseService.getClient()
        .from('group_members')
        .select('joined_date')
        .eq('user_id', userId)
        .eq('group_id', groupId)
        .single();

      if (error) {
        return false;
      }

      if (!data || !data.joined_date) {
        return false;
      }

      const joinDate = new Date(data.joined_date);
      joinDate.setHours(0, 0, 0, 0);

      const targetDate = new Date(date);
      targetDate.setHours(0, 0, 0, 0);

      const canParticipate = targetDate > joinDate;
      .split('T')[0]}, target date is ${targetDate.toISOString().split('T')[0]}, can participate: ${canParticipate}`);

      return canParticipate;
    } catch (error) {
      return false;
    }
  }

  async toggleGroupQuestCompletion(
    questId: string,
    userId: string,
    date: Date,
    valueAchieved: number
  ): Promise<{ success: boolean; message?: string }> {
    const dateString = date.toISOString().split('T')[0]; 

    try {
      const { data: quest, error: questError } = await this.supabaseService.getClient()
        .from('group_quests')
        .select('*')
        .eq('id', questId)
        .single();

      if (questError || !quest) {
        return { success: false, message: 'Quest not found' };
      }

      const canParticipate = await this.canUserParticipateInGroupQuests(userId, quest.group_id, date);
      if (!canParticipate) {
        ');
        return {
          success: false,
          message: 'You can participate in group quests starting tomorrow after joining the group.'
        };
      }

      const { data: progressData, error: findError } = await this.supabaseService.getClient()
        .from('group_quest_progress')
        .select('*')
        .eq('quest_id', questId)
        .eq('user_id', userId)
        .eq('date', dateString);

      if (findError) {
        return { success: false, message: findError.message };
      }

      let progressId: string;
      let wasCompleted = false;

      if (!progressData || progressData.length === 0) {
        const newProgress = {
          quest_id: questId,
          user_id: userId,
          date: dateString,
          completed: false,
          value_achieved: 0
        };

        const { data: insertData, error: insertError } = await this.supabaseService.getClient()
          .from('group_quest_progress')
          .insert(newProgress)
          .select('id')
          .single();

        if (insertError || !insertData) {
          return { success: false, message: 'Failed to create progress document' };
        }

        progressId = insertData.id;
      } else {
        progressId = progressData[0].id;
        wasCompleted = progressData[0].completed;
      }


      let isCompleted: boolean;
      if (quest.quest_type === 'build') {
        isCompleted = valueAchieved >= quest.goal_value;
      } else { 
        isCompleted = valueAchieved < quest.goal_value;
      }

      const { error: updateError } = await this.supabaseService.getClient()
        .from('group_quest_progress')
        .update({
          value_achieved: valueAchieved,
          completed: isCompleted
        })
        .eq('id', progressId);

      if (updateError) {
        return { success: false, message: updateError.message };
      }


      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const selectedDate = new Date(date);
      selectedDate.setHours(0, 0, 0, 0);
      const isPreviousDay = selectedDate < today;

      if (isPreviousDay) {

        const { data: members, error: membersError } = await this.supabaseService.getClient()
          .from('group_members')
          .select('user_id, joined_date')
          .eq('group_id', quest.group_id);

        if (membersError || !members || members.length === 0) {
        } else {
          const eligibleMembers = members.filter(member => {
            const joinDate = new Date(member.joined_date);
            joinDate.setHours(0, 0, 0, 0);
            return selectedDate > joinDate;
          });

          if (eligibleMembers.length > 0) {
            const eligibleMemberIds = eligibleMembers.map(member => member.user_id);

            const { data: allProgress, error: progressError } = await this.supabaseService.getClient()
              .from('group_quest_progress')
              .select('user_id, completed')
              .eq('quest_id', questId)
              .eq('date', dateString);

            if (progressError) {
            } else if (allProgress) {
              const previousProgress = [...allProgress];

              const currentUserIndex = previousProgress.findIndex(p => p.user_id === userId);
              if (currentUserIndex >= 0) {
                previousProgress[currentUserIndex].completed = wasCompleted;
              }

              const wasAllEligibleMembersCompleted = eligibleMemberIds.every(id => {
                if (id === userId) {
                  return wasCompleted;
                } else {
                  const userProgress = previousProgress.find(p => p.user_id === id);
                  return userProgress && userProgress.completed;
                }
              });

              const allEligibleMembersCompleted = eligibleMemberIds.every(id => {
                if (id === userId) {
                  return isCompleted;
                } else {
                  const userProgress = allProgress.find(p => p.user_id === id);
                  return userProgress && userProgress.completed;
                }
              });


              const xpValue = quest.priority === 'high' ? 2 : 1;
              const totalXP = xpValue * eligibleMembers.length;

              const xpField = `${quest.category}_xp`;

              const { data: group, error: groupError } = await this.supabaseService.getClient()
                .from('groups')
                .select('*')
                .eq('id', quest.group_id)
                .single();

              if (groupError || !group) {
              } else {
                let currentXP = group[xpField as keyof typeof group] as number || 0;
                let xpChanged = false;

                if (allEligibleMembersCompleted && !wasAllEligibleMembersCompleted) {
                  currentXP += totalXP;
                  xpChanged = true;
                }
                else if (!allEligibleMembersCompleted && wasAllEligibleMembersCompleted) {
                  currentXP = Math.max(0, currentXP - totalXP);
                  xpChanged = true;
                } else {
                }

                if (xpChanged) {
                  const { error: xpUpdateError } = await this.supabaseService.getClient()
                    .from('groups')
                    .update({ [xpField]: currentXP })
                    .eq('id', quest.group_id);

                  if (xpUpdateError) {
                  } else {

                    await this.checkAndLevelUpGroup(quest.group_id);
                  }
                }
              }
            }
          }
        }
      }

      await this.updateGroupQuestStreak(questId);

      return { success: true };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'An unknown error occurred'
      };
    }
  }

  private async updateGroupQuestStreak(questId: string): Promise<void> {

    try {
      const { data: quest, error: questError } = await this.supabaseService.getClient()
        .from('group_quests')
        .select('*')
        .eq('id', questId)
        .single();

      if (questError || !quest) {
        return;
      }

      const groupId = quest.group_id;
      const oldStreak = quest.streak;

      const today = new Date();
      const todayString = today.toISOString().split('T')[0];

      const { data: members, error: membersError } = await this.supabaseService.getClient()
        .from('group_members')
        .select('user_id, joined_date')
        .eq('group_id', groupId);

      if (membersError || !members || members.length === 0) {
        return;
      }

      const eligibleMembers = members.filter(member => {
        const joinDate = new Date(member.joined_date);
        joinDate.setHours(0, 0, 0, 0);

        const todayDate = new Date(today);
        todayDate.setHours(0, 0, 0, 0);

        return joinDate < todayDate;
      });


      if (eligibleMembers.length === 0) {
        return;
      }

      const eligibleMemberIds = eligibleMembers.map(member => member.user_id);

      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayString = yesterday.toISOString().split('T')[0];

      const yesterdayEligibleMembers = members.filter(member => {
        const joinDate = new Date(member.joined_date);
        joinDate.setHours(0, 0, 0, 0);

        const yesterdayDate = new Date(yesterday);
        yesterdayDate.setHours(0, 0, 0, 0);

        return joinDate < yesterdayDate;
      });

      const yesterdayEligibleMemberIds = yesterdayEligibleMembers.map(member => member.user_id);

      const { data: yesterdayProgress, error: yesterdayError } = await this.supabaseService.getClient()
        .from('group_quest_progress')
        .select('user_id, completed')
        .eq('quest_id', questId)
        .eq('date', yesterdayString);

      if (yesterdayError) {
        return;
      }

      let allCompletedYesterday = false;

      if (yesterdayEligibleMemberIds.length > 0) {
        if (!yesterdayProgress || yesterdayProgress.length === 0) {
          allCompletedYesterday = false;
        } else {
          const yesterdayCompletions = yesterdayProgress
            .filter(progress => progress.completed)
            .map(progress => progress.user_id);

          allCompletedYesterday = yesterdayEligibleMemberIds.every(id => yesterdayCompletions.includes(id));
        }
      } else {
      }

      const { data: todayProgress, error: todayError } = await this.supabaseService.getClient()
        .from('group_quest_progress')
        .select('user_id, completed')
        .eq('quest_id', questId)
        .eq('date', todayString);

      if (todayError) {
        return;
      }

      let allCompletedToday = false;

      if (eligibleMemberIds.length > 0) {
        if (!todayProgress || todayProgress.length === 0) {
          allCompletedToday = false;
        } else {
          const todayCompletions = todayProgress
            .filter(progress => progress.completed)
            .map(progress => progress.user_id);

          allCompletedToday = eligibleMemberIds.every(id => todayCompletions.includes(id));
        }
      } else {
      }

      let newStreak = 0;

      const createdDate = new Date(quest.created);
      createdDate.setHours(0, 0, 0, 0);

      let currentDate = new Date(yesterday);
      let streakCount = 0;

      while (true) {
        if (currentDate < createdDate) {
          break;
        }

        const eligibleDate = new Date(currentDate);
        eligibleDate.setDate(eligibleDate.getDate() - 1);

        const historicalEligibleMembers = members.filter(member => {
          const joinDate = new Date(member.joined_date);
          joinDate.setHours(0, 0, 0, 0);
          return joinDate <= eligibleDate;
        });

        if (historicalEligibleMembers.length === 0) {
          .split('T')[0]}, stopping streak calculation`);
          break;
        }

        const historicalEligibleIds = historicalEligibleMembers.map(member => member.user_id);
        const currentDateString = currentDate.toISOString().split('T')[0];

        const { data: dateProgress, error: dateError } = await this.supabaseService.getClient()
          .from('group_quest_progress')
          .select('user_id, completed')
          .eq('quest_id', questId)
          .eq('date', currentDateString);

        if (dateError) {
          break;
        }

        if (!dateProgress || dateProgress.length === 0) {
          break;
        }

        const completedMemberIds = dateProgress
          .filter(progress => progress.completed)
          .map(progress => progress.user_id);

        const allEligibleCompleted = historicalEligibleIds.every(id =>
          completedMemberIds.includes(id)
        );

        if (!allEligibleCompleted) {
          break;
        }

        streakCount++;

        currentDate.setDate(currentDate.getDate() - 1);
      }

      if (allCompletedToday) {
        newStreak = streakCount + 1;
      } else {
        newStreak = streakCount;
      }


      const { error: updateError } = await this.supabaseService.getClient()
        .from('group_quests')
        .update({ streak: newStreak })
        .eq('id', questId);

      if (updateError) {
        return;
      }


      if (newStreak !== oldStreak) {
        const xpValue = quest.priority === 'high' ? 2 : 1;
        const totalXP = xpValue * eligibleMembers.length;

        const xpField = `${quest.category}_xp`;

        const { data: group, error: groupError } = await this.supabaseService.getClient()
          .from('groups')
          .select('*')
          .eq('id', groupId)
          .single();

        if (groupError || !group) {
          return;
        }

        let currentXP = group[xpField as keyof typeof group] as number || 0;

        if (newStreak > oldStreak) {
          currentXP += totalXP;
          . Adding ${totalXP} XP to ${xpField}, new total: ${currentXP}`);
        } else if (newStreak < oldStreak) {
          currentXP = Math.max(0, currentXP - totalXP);
          . Removing ${totalXP} XP from ${xpField}, new total: ${currentXP}`);
        }

        const { error: xpUpdateError } = await this.supabaseService.getClient()
          .from('groups')
          .update({ [xpField]: currentXP })
          .eq('id', groupId);

        if (xpUpdateError) {
          return;
        }


        await this.checkAndLevelUpGroup(groupId);
      } else {
      }
    } catch (error) {
    }
  }

  private async updateGroupXP(
    questId: string,
    groupId: string,
    category: string,
    isCompleted: boolean,
    wasCompleted: boolean,
    priority: string
  ): Promise<void> {
    :', questId, groupId, category, isCompleted, wasCompleted, priority);

    try {
      if (isCompleted === wasCompleted) {
        return;
      }

      const today = new Date();
      const todayString = today.toISOString().split('T')[0];

      const { data: members, error: membersError } = await this.supabaseService.getClient()
        .from('group_members')
        .select('user_id, joined_date')
        .eq('group_id', groupId);

      if (membersError || !members || members.length === 0) {
        return;
      }

      const eligibleMembers = members.filter(member => {
        const joinDate = new Date(member.joined_date);
        joinDate.setHours(0, 0, 0, 0);

        const todayDate = new Date(today);
        todayDate.setHours(0, 0, 0, 0);

        return joinDate < todayDate;
      });

      if (eligibleMembers.length === 0) {
        return;
      }

      const eligibleMemberIds = eligibleMembers.map(member => member.user_id);

      const xpField = `${category}_xp`;

      const xpValue = priority === 'high' ? 2 : 1;
      const totalXP = xpValue * eligibleMembers.length;

      const { data: group, error: groupError } = await this.supabaseService.getClient()
        .from('groups')
        .select('*')
        .eq('id', groupId)
        .single();

      if (groupError || !group) {
        return;
      }

      let currentXP = group[xpField as keyof typeof group] as number || 0;

      if (isCompleted) {
        currentXP += totalXP;
      } else {
        currentXP = Math.max(0, currentXP - totalXP);
      }

      const { error: updateError } = await this.supabaseService.getClient()
        .from('groups')
        .update({ [xpField]: currentXP })
        .eq('id', groupId);

      if (updateError) {
        return;
      }


      await this.checkAndLevelUpGroup(groupId);
    } catch (error) {
    }
  }

  async checkAndLevelUpGroup(groupId: string): Promise<boolean> {

    const { data: group, error: groupError } = await this.supabaseService.getClient()
      .from('groups')
      .select('*')
      .eq('id', groupId)
      .single();

    if (groupError || !group) {
      return false;
    }


    if (group.level >= 100) {
      ');
      return false;
    }

    const requiredXP = await firstValueFrom(this.xpService.getRequiredXpForNextLevel(group.level, EntityType.GROUP));

    const categories = ['strength', 'money', 'health', 'knowledge'];
    const hasEnoughXP = categories.every(category => {
      const fieldName = `${category}_xp`;
      const xpValue = group[fieldName as keyof typeof group];
      const hasEnough = typeof xpValue === 'number' && xpValue >= requiredXP;
      return hasEnough;
    });

    if (hasEnoughXP) {

      const updates: any = {
        level: group.level + 1
      };

      categories.forEach(category => {
        const fieldName = `${category}_xp`;
        const currentXP = group[fieldName as keyof typeof group] as number;
        updates[fieldName] = currentXP - requiredXP;
      });

      const { error: updateError } = await this.supabaseService.getClient()
        .from('groups')
        .update(updates)
        .eq('id', groupId);

      if (updateError) {
        return false;
      }

      return true;
    }

    return false;
  }



  getGroupSideQuestPool(questId: string): Observable<GroupSideQuestPool | undefined> {

    return from(
      this.supabaseService.getClient()
        .from('group_sidequest_pool')
        .select('*')
        .eq('id', questId)
        .single()
    ).pipe(
      map(response => {
        if (response.error) {
          return undefined;
        }

        return response.data as GroupSideQuestPool;
      }),
      catchError(error => {
        return of(undefined);
      })
    );
  }



  async createGroupSideQuest(groupId: string): Promise<string> {

    const { data: poolQuests, error: poolError } = await this.supabaseService.getClient()
      .from('group_sidequest_pool')
      .select('*')
      .eq('active', true);

    if (poolError) {
      throw new Error('Error getting side quest pool: ' + poolError.message);
    }

    if (!poolQuests || poolQuests.length === 0) {
      throw new Error('No active side quests available');
    }


    const randomIndex = Math.floor(Math.random() * poolQuests.length);
    const randomQuest = poolQuests[randomIndex];

    const newSideQuest = {
      group_id: groupId,
      current_quest_id: randomQuest.id,
      streak: 0,
      date_assigned: new Date(),
      completed: false,
      value_achieved: 0,
      category: randomQuest.category
    };


    const { data: insertData, error: insertError } = await this.supabaseService.getClient()
      .from('group_side_quests')
      .insert(newSideQuest)
      .select('id')
      .single();

    if (insertError) {
      throw new Error('Error creating side quest: ' + insertError.message);
    }

    return insertData.id;
  }



  async toggleGroupSideQuestCompletion(statusId: string): Promise<void> {

    const { data: status, error: statusError } = await this.supabaseService.getClient()
      .from('group_sidequest_member_status')
      .select('*')
      .eq('id', statusId)
      .single();

    if (statusError || !status) {
      throw new Error('Member status not found');
    }

    const wasCompleted = status.completed;

    const isCompleted = !wasCompleted;
    const today = new Date();

    const { data: groupQuest, error: questError } = await this.supabaseService.getClient()
      .from('group_side_quests')
      .select('*')
      .eq('id', status.group_quest_id)
      .single();

    if (questError || !groupQuest) {
      throw new Error('Group side quest not found');
    }


    const { data: questPool, error: poolError } = await this.supabaseService.getClient()
      .from('group_sidequest_pool')
      .select('*')
      .eq('id', groupQuest.current_quest_id)
      .single();

    if (poolError || !questPool) {
      throw new Error('Side quest pool item not found');
    }


    const { error: updateError } = await this.supabaseService.getClient()
      .from('group_sidequest_member_status')
      .update({
        completed: isCompleted,
        value_achieved: isCompleted ? questPool.goal_value : 0,
        last_updated: today
      })
      .eq('id', statusId);

    if (updateError) {
      throw new Error('Error updating status: ' + updateError.message);
    }


    if (groupQuest.id) {
      await this.updateGroupSideQuestCompletionStatus(groupQuest.id);
    }
  }

  private async updateGroupSideQuestCompletionStatus(groupQuestId: string): Promise<void> {

    const { data: groupQuest, error: questError } = await this.supabaseService.getClient()
      .from('group_side_quests')
      .select('*')
      .eq('id', groupQuestId)
      .single();

    if (questError || !groupQuest) {
      return;
    }

    const groupId = groupQuest.group_id;

    const { data: members, error: membersError } = await this.supabaseService.getClient()
      .from('group_members')
      .select('user_id, joined_date')
      .eq('group_id', groupId);

    if (membersError || !members || members.length === 0) {
      return;
    }


    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const eligibleMembers = members.filter(member => {
      const joinDate = new Date(member.joined_date);
      joinDate.setHours(0, 0, 0, 0);
      return joinDate <= yesterday;
    });

    if (eligibleMembers.length === 0) {
      return;
    }

    const memberIds = eligibleMembers.map(member => member.user_id);

    const { data: statuses, error: statusesError } = await this.supabaseService.getClient()
      .from('group_sidequest_member_status')
      .select('*')
      .eq('group_quest_id', groupQuestId);

    if (statusesError || !statuses) {
      return;
    }


    const completedMemberIds = statuses
      .filter(status => status.completed)
      .map(status => status.member_id);


    const allCompleted = memberIds.every(id => completedMemberIds.includes(id));

    const updates: any = {
      completed: allCompleted
    };

    if (allCompleted) {
      const { data: questPool, error: poolError } = await this.supabaseService.getClient()
        .from('group_sidequest_pool')
        .select('*')
        .eq('id', groupQuest.current_quest_id)
        .single();

      if (poolError || !questPool) {
      } else {
        updates.value_achieved = questPool.goal_value;
      }

      updates.last_completed_date = new Date();

      await this.updateGroupXPForSideQuest(groupId, groupQuest.category, true);
    } else {
      if (groupQuest.completed) {
        await this.updateGroupXPForSideQuest(groupId, groupQuest.category, false);
      }
    }

    const { error: updateError } = await this.supabaseService.getClient()
      .from('group_sidequests')
      .update(updates)
      .eq('id', groupQuestId);

    if (updateError) {
      return;
    }

  }



  getGroupActivities(groupId: string): Observable<any[]> {

    return from(
      this.supabaseService.getClient()
        .from('group_activities')
        .select('*')
        .eq('group_id', groupId)
        .order('created_at', { ascending: false })
        .limit(20)
    ).pipe(
      map(response => {
        if (response.error) {
          return [];
        }

        return response.data;
      }),
      catchError(error => {
        return of([]);
      })
    );
  }

  async addGroupActivity(groupId: string, activityData: { user_id: string, message: string, icon: string, color: string }): Promise<void> {

    const { data: memberData, error: memberError } = await this.supabaseService.getClient()
      .from('group_members')
      .select('nickname')
      .eq('group_id', groupId)
      .eq('user_id', activityData.user_id)
      .single();

    if (memberError) {
      throw new Error(memberError.message);
    }

    const activity = {
      group_id: groupId,
      user_id: activityData.user_id,
      user_nickname: memberData.nickname,
      message: activityData.message,
      icon: activityData.icon,
      color: activityData.color,
      created_at: new Date().toISOString()
    };

    const { error } = await this.supabaseService.getClient()
      .from('group_activities')
      .insert(activity);

    if (error) {
      throw new Error(error.message);
    }

  }

  getGroupSideQuests(groupId: string): Observable<any[]> {

    return from(
      this.supabaseService.getClient()
        .from('group_sidequests')
        .select('*')
        .eq('group_id', groupId)
    ).pipe(
      map(response => {
        if (response.error) {
          return [];
        }

        return response.data;
      }),
      catchError(error => {
        return of([]);
      })
    );
  }

  getGroupSideQuest(groupId: string): Observable<GroupSideQuest | null> {

    return from(
      this.supabaseService.getClient()
        .from('group_sidequests')
        .select('*')
        .eq('group_id', groupId)
        .single()
    ).pipe(
      map(response => {
        if (response.error) {
          if (response.error.code === 'PGRST116') {
            return null;
          }
          return null;
        }

        return response.data as GroupSideQuest;
      }),
      catchError(error => {
        return of(null);
      })
    );
  }

  getGroupSideQuestPoolItem(questId: string): Observable<GroupSideQuestPool | null> {

    return from(
      this.supabaseService.getClient()
        .from('group_sidequest_pool')
        .select('*')
        .eq('id', questId)
        .single()
    ).pipe(
      map(response => {
        if (response.error) {
          return null;
        }

        return response.data as GroupSideQuestPool;
      }),
      catchError(error => {
        return of(null);
      })
    );
  }

  getGroupSideQuestMemberStatus(questId: string, userId: string): Observable<GroupSideQuestMemberStatus | null> {

    return from(
      this.supabaseService.getClient()
        .from('group_sidequest_member_status')
        .select('*')
        .eq('group_quest_id', questId)
        .eq('member_id', userId)
        .single()
    ).pipe(
      map(response => {
        if (response.error) {
          if (response.error.code === 'PGRST116') {
            return null;
          }
          return null;
        }

        return response.data as GroupSideQuestMemberStatus;
      }),
      catchError(error => {
        return of(null);
      })
    );
  }

  getGroupSideQuestMemberStatuses(questId: string): Observable<GroupSideQuestMemberStatus[]> {

    return from(
      this.supabaseService.getClient()
        .from('group_sidequest_member_status')
        .select('*')
        .eq('group_quest_id', questId)
    ).pipe(
      map(response => {
        if (response.error) {
          return [];
        }

        return response.data as GroupSideQuestMemberStatus[];
      }),
      catchError(error => {
        return of([]);
      })
    );
  }

  async updateGroupSideQuestMemberStatus(
    statusId: string,
    completed: boolean,
    valueAchieved: number
  ): Promise<void> {

    const { error } = await this.supabaseService.getClient()
      .from('group_sidequest_member_status')
      .update({
        completed: completed,
        value_achieved: valueAchieved,
        last_updated: new Date().toISOString().split('T')[0] 
      })
      .eq('id', statusId);

    if (error) {
      throw new Error(error.message);
    }


    await this.updateGroupSideQuestCompletion(statusId);
  }

  async createGroupSideQuestMemberStatus(
    questId: string,
    userId: string,
    completed: boolean,
    valueAchieved: number
  ): Promise<string> {

    const newStatus = {
      group_quest_id: questId,
      member_id: userId,
      completed: completed,
      value_achieved: valueAchieved,
      last_updated: new Date().toISOString().split('T')[0] 
    };

    const { data, error } = await this.supabaseService.getClient()
      .from('group_sidequest_member_status')
      .insert(newStatus)
      .select('id')
      .single();

    if (error) {
      throw new Error(error.message);
    }


    await this.updateGroupSideQuestCompletion(data.id);

    return data.id;
  }

  private async updateGroupSideQuestCompletion(statusId: string): Promise<void> {

    const { data: status, error: statusError } = await this.supabaseService.getClient()
      .from('group_sidequest_member_status')
      .select('*')
      .eq('id', statusId)
      .single();

    if (statusError || !status) {
      return;
    }

    const questId = status.group_quest_id;

    const { data: quest, error: questError } = await this.supabaseService.getClient()
      .from('group_sidequests')
      .select('*')
      .eq('id', questId)
      .single();

    if (questError || !quest) {
      return;
    }

    const groupId = quest.group_id;

    const { data: members, error: membersError } = await this.supabaseService.getClient()
      .from('group_members')
      .select('user_id, joined_date')
      .eq('group_id', groupId);

    if (membersError || !members || members.length === 0) {
      return;
    }


    const today = new Date();
    const todayString = today.toISOString().split('T')[0];

    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayString = yesterday.toISOString().split('T')[0];

    const eligibleDate = yesterday.toISOString().split('T')[0];
    const eligibleMembers = members.filter(member => {
      const joinedDate = new Date(member.joined_date);
      return joinedDate <= new Date(eligibleDate);
    });


    if (eligibleMembers.length === 0) {
      return;
    }

    const eligibleMemberIds = eligibleMembers.map(member => member.user_id);

    const { data: allStatuses, error: statusesError } = await this.supabaseService.getClient()
      .from('group_sidequest_member_status')
      .select('*')
      .eq('group_quest_id', questId);

    if (statusesError) {
      return;
    }


    if (!allStatuses || allStatuses.length === 0) {
      return;
    }

    const completedMemberIds = allStatuses
      .filter(s => s.completed)
      .map(s => s.member_id);


    const allEligibleCompleted = eligibleMemberIds.every(id => completedMemberIds.includes(id));

    if (allEligibleCompleted) {
      const { data: questPool, error: poolError } = await this.supabaseService.getClient()
        .from('group_sidequest_pool')
        .select('*')
        .eq('id', quest.current_quest_id)
        .single();

      if (poolError || !questPool) {
        return;
      }


      const { error: updateError } = await this.supabaseService.getClient()
        .from('group_sidequests')
        .update({
          completed: true,
          value_achieved: questPool.goal_value,
          last_completed_date: todayString
        })
        .eq('id', questId);

      if (updateError) {
        return;
      }


      if (quest.last_completed_date === yesterdayString) {
        const { error: streakError } = await this.supabaseService.getClient()
          .from('group_sidequests')
          .update({
            streak: quest.streak + 1
          })
          .eq('id', questId);

        if (streakError) {
          return;
        }

      } else if (quest.last_completed_date !== todayString) {
        const { error: streakError } = await this.supabaseService.getClient()
          .from('group_sidequests')
          .update({
            streak: 1
          })
          .eq('id', questId);

        if (streakError) {
          return;
        }

      }

      await this.updateGroupXPForSideQuest(groupId, quest.category, true);
    } else {
      const { error: updateError } = await this.supabaseService.getClient()
        .from('group_sidequests')
        .update({
          completed: false
        })
        .eq('id', questId);

      if (updateError) {
        return;
      }


      if (quest.completed) {
        await this.updateGroupXPForSideQuest(groupId, quest.category, false);
      }
    }
  }

  private async updateGroupXPForSideQuest(
    groupId: string,
    category: string,
    isCompleted: boolean
  ): Promise<void> {

    const { data: members, error: membersError } = await this.supabaseService.getClient()
      .from('group_members')
      .select('user_id, joined_date')
      .eq('group_id', groupId);

    if (membersError || !members || members.length === 0) {
      return;
    }

    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const eligibleMembers = members.filter(member => {
      const joinDate = new Date(member.joined_date);
      joinDate.setHours(0, 0, 0, 0);
      return joinDate <= yesterday;
    });

    if (eligibleMembers.length === 0) {
      return;
    }

    const memberCount = eligibleMembers.length;

    const xpField = `${category}_xp`;

    const totalXP = 2 * memberCount;

    const { data: group, error: groupError } = await this.supabaseService.getClient()
      .from('groups')
      .select('*')
      .eq('id', groupId)
      .single();

    if (groupError || !group) {
      return;
    }

    let currentXP = group[xpField as keyof typeof group] as number || 0;

    if (isCompleted) {
      currentXP += totalXP;
    } else {
      currentXP = Math.max(0, currentXP - totalXP);
    }

    const { error: updateError } = await this.supabaseService.getClient()
      .from('groups')
      .update({ [xpField]: currentXP })
      .eq('id', groupId);

    if (updateError) {
      return;
    }


    await this.checkAndLevelUpGroup(groupId);
  }

  async updateSideQuestStatus(groupId: string, questId: string, completed: boolean): Promise<void> {

    const userId = this.supabaseService.getCurrentUserId();
    if (!userId) {
      throw new Error('User not authenticated');
    }

    const { data, error: checkError } = await this.supabaseService.getClient()
      .from('group_side_quest_statuses')
      .select('id')
      .eq('quest_id', questId)
      .eq('user_id', userId)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { 
      throw new Error(checkError.message);
    }

    if (data) {
      const { error: updateError } = await this.supabaseService.getClient()
        .from('group_side_quest_statuses')
        .update({ completed })
        .eq('id', data.id);

      if (updateError) {
        throw new Error(updateError.message);
      }
    } else {
      const { error: insertError } = await this.supabaseService.getClient()
        .from('group_side_quest_statuses')
        .insert({
          quest_id: questId,
          user_id: userId,
          completed,
          completed_at: completed ? new Date().toISOString() : null
        });

      if (insertError) {
        throw new Error(insertError.message);
      }
    }

  }

  async inviteUserToGroup(groupId: string, username: string): Promise<void> {

    const currentUser = await this.supabaseService.getCurrentUser();
    if (!currentUser || !currentUser.username) {
      throw new Error('User not authenticated or username not available');
    }

    const inviterUsername = currentUser.username;

    const { data: userData, error: userError } = await this.supabaseService.getClient()
      .from('profiles')
      .select('id')
      .eq('username', username)
      .single();

    if (userError) {
      throw new Error('User not found');
    }

    const { data: memberData, error: memberError } = await this.supabaseService.getClient()
      .from('group_members')
      .select('id')
      .eq('group_id', groupId)
      .eq('user_id', userData.id);

    if (memberError) {
      throw new Error(memberError.message);
    }

    if (memberData && memberData.length > 0) {
      throw new Error('User is already a member of this group');
    }

    const { data: existingInvites, error: checkError } = await this.supabaseService.getClient()
      .from('group_join_requests')
      .select('id')
      .eq('group_id', groupId)
      .eq('username_invited', username);

    if (checkError) {
      throw new Error(checkError.message);
    }

    if (existingInvites && existingInvites.length > 0) {
      throw new Error('An invitation has already been sent to this user');
    }

    const { error: requestError } = await this.supabaseService.getClient()
      .from('group_join_requests')
      .insert({
        group_id: groupId,
        username_invited: username,
        invited_by: inviterUsername, 
        created: new Date().toISOString(),
        status: 'pending'
      });

    if (requestError) {
      throw new Error(requestError.message);
    }

  }

  async generateInvitationCode(groupId: string): Promise<string> {

    const code = Math.random().toString(36).substring(2, 8).toUpperCase();

    const expiry = new Date();
    expiry.setHours(expiry.getHours() + 24);

    const { error } = await this.supabaseService.getClient()
      .from('groups')
      .update({
        invitation_code: code,
        code_expiry: expiry.toISOString()
      })
      .eq('id', groupId);

    if (error) {
      throw new Error(error.message);
    }

    return code;
  }
}
