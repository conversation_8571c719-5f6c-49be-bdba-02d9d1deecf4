<!-- Exact HTML from Django template with Angular syntax -->
<div class="container">
    <header>
        <div class="logo">
            <img src="assets/images/upshift_icon_mini.svg" alt="Upshift">
            <span>Upshift</span>
        </div>
        <h1>Friend Profile</h1>
    </header>

    <a href="javascript:void(0)" (click)="goBack()"  class="back-link">&larr; Go back</a>

    <div class="profile-container" *ngIf="friend">
        <div class="profile-header">
            <div class="profile-picture">
                <img *ngIf="friend.profile_picture" [src]="friend.profile_picture" [alt]="friend.username">
                <ng-container *ngIf="!friend.profile_picture">👤</ng-container>
            </div>
            <div class="profile-info">
                <div class="profile-name">{{ friend.name }}</div>
                <div class="profile-username">&#64;{{ friend.username }}</div>

                <div class="profile-level">
                    <div class="level-badge">Level {{ friend.level }}</div>
                    <div class="profile-title">{{ friend.title }}</div>
                </div>
            </div>
        </div>
        <div class="profile-bio-container" style="padding-left: 100px;">
            <div class="profile-bio" *ngIf="friend.bio">{{ friend.bio }}</div>
            <div class="profile-bio" *ngIf="!friend.bio">No bio provided</div>
        </div>

        <div class="xp-section">
            <h2>XP Progress</h2>

            <div class="category-card" *ngFor="let category of categories">
                <div class="category-header">
                    <div class="category-icon">{{ category.icon }}</div>
                    <div class="category-name">{{ category.name }}</div>
                </div>
                <div class="progress-container">
                    <div class="progress-bar" [style.width.%]="category.progress" [style.background-color]="category.color"></div>
                </div>
                <div class="xp-text">
                    <span>{{ category.current_xp }} XP</span>
                    <span>{{ category.required_xp }} XP needed</span>
                </div>
            </div>

            <div class="next-level-info">
                <div class="next-level-text">Next Level: {{ nextLevel }}</div>
                <div class="next-level-requirements">
                    Reach required XP in all categories to level up
                </div>
            </div>
        </div>

        <div class="button-container">
            <a [routerLink]="['/badges', friend.id]" style="margin-top: 15px; display: inline-block; margin-right: 10px; padding: 8px 16px; background-color: #1c1c1e; color: white; border: 1px solid #4d7bff; border-radius: 20px; text-decoration: none; font-size: 14px; font-weight: 600; transition: all 0.3s ease;">
                <span style="margin-right: 5px;">🏆</span> View Badges
            </a>
            <a (click)="removeFriend()" class="remove-button">Remove Friend</a>
        </div>
    </div>
</div>
