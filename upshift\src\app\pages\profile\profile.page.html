
    <div class="container">
        <header>
            <div class="logo">
                <img src="assets/images/upshift_icon_mini.svg" alt="Upshift">
                <span>Upshift</span>
            </div>
            <h1>Profile</h1>
        </header>

        <div class="profile-container" *ngIf="user">
            <div class="profile-header">
                <div class="profile-picture">
                    <img *ngIf="user.profile_picture" [src]="user.profile_picture" [alt]="user.username">
                    <ng-container *ngIf="!user.profile_picture">👤</ng-container>
                </div>
                <div class="profile-info">
                    <div class="profile-name">{{ user.name }}</div>
                    <div class="profile-username">&#64;{{ user.username }}</div>

                    <div class="profile-level">
                        <div class="level-badge">Level {{ user.level }}</div>
                        <div class="profile-title">{{ user.title }}</div>
                    </div>
                </div>
            </div>
            <div class="profile-bio-container">
                <div class="profile-bio" *ngIf="user.bio && user.bio.trim() !== ''" (click)="toggleBioForm()">{{ user.bio }}</div>
                <button *ngIf="!user.bio || user.bio.trim() === ''" id="add-bio-btn" class="edit-bio-btn" (click)="toggleBioForm()">Add Bio</button>

            </div>

            <div id="bio-form" class="bio-form" [style.display]="showBioForm ? 'block' : 'none'">
                <form (ngSubmit)="updateBio()">
                    <input type="text" name="bio" maxlength="100" placeholder="Add a short bio (max 100 characters)" [(ngModel)]="editedBio">
                    <div class="bio-form-buttons">
                        <button type="button" class="cancel-btn" (click)="toggleBioForm()">Cancel</button>
                        <button type="submit" class="save-btn">Save</button>
                    </div>
                </form>
            </div>

            <div class="xp-section">
                <h2>XP Progress</h2>

                <div class="category-card" *ngFor="let category of categories">
                    <div class="category-header">
                        <div class="category-icon">{{ category.icon }}</div>
                        <div class="category-name">{{ category.name }}</div>
                    </div>
                    <div class="progress-container">
                        <div class="progress-bar" [style.width.%]="category.progress" [style.background-color]="category.color"></div>
                    </div>
                    <div class="xp-text">
                        <span>{{ category.current_xp }} XP</span>
                        <span>{{ category.required_xp }} XP needed</span>
                    </div>
                </div>

                <div class="next-level-info">
                    <div class="next-level-text">Next Level: {{ nextLevel }}</div>
                    <div class="next-level-requirements">
                        Reach required XP in all categories to level up
                    </div>
                </div>

                <div class="button-container">
                    <a [routerLink]="['/badges', user.id]" class="badges-button">
                        <span style="margin-right: 5px;">🏆</span> View Badges
                    </a>
                    <a [routerLink]="['/profile-settings']" class="settings-button">⚙️ Settings</a>
                </div>
            </div>
        </div>
    </div>
