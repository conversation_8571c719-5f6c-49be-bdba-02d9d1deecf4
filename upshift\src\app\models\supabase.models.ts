﻿
export interface UserProfile {
  id: string;                      
  username: string;                
  profile_picture?: string;        
  active: boolean;                 
  strength_xp: number;             
  money_xp: number;                
  health_xp: number;               
  knowledge_xp: number;            
  level: number;                   
  title: string;                   
  bio: string;                     
  timezone: string;                
  friend_code?: string;            
  friend_code_expiry?: Date;       
  plan?: string;                   
  start_of_current_plan?: Date;    
  end_of_current_plan?: Date;      
  auto_renew: boolean;             
  start_of_sick_days?: Date;       
  end_of_sick_days?: Date;         
  sidequests_switch: boolean;      
  show_celebration: boolean;       
  celebration_name: string;        
  celebration_description: string; 
  celebration_emoji?: string;      
  subscription_status: string;     
}

export interface UserBadge {
  id: string;                      
  user_id: string;                 
  badge_newbie?: boolean;          
  badge_warrior?: boolean;         
  badge_monk?: boolean;            
  badge_nonchalant?: boolean;      
  badge_hardcore?: boolean;        
  badge_disciplined_machine?: boolean; 
  badge_high_performer?: boolean;  
  badge_master_of_consistency?: boolean; 
  badge_peak_performer?: boolean;  
  badge_elite_operator?: boolean;  
  badge_indestructible?: boolean;  
  badge_ultra_human?: boolean;     
  badge_professional?: boolean;    
  created_at: Date;                
  updated_at: Date;                
}

export interface SubscriptionHistory {
  id: string;                      
  user_id: string;                 
  plan: string;                    
  start_date_of_subscription: Date;
  end_date_of_subscription?: Date; 
  created: Date;                   
  updated: Date;                   
}

export interface Quest {
  id: string;                      
  user_id: string;                 
  name: string;
  description?: string;            
  active: boolean;                 
  quest_type: string;              
  streak: number;                  
  goal_value: number;              
  goal_unit: string;               
  goal_period: string;             
  priority: string;                
  category: string;                
  task_days_of_week?: string;      
  task_days_of_month?: string;     
  custom_reminder_times?: string;  
  created_at: Date;                
  emoji: string;                   
}

export interface QuestProgress {
  id: string;                      
  user_id: string;                 
  quest_id: string;                
  date: Date;
  completed: boolean;              
  value_achieved: number;          
  historical_streak?: number;      
}

export interface DailySideQuestPool {
  id: string;                      
  name: string;
  description?: string;            
  goal_value: number;              
  category: string;                
  goal_unit: string;               
  active: boolean;                 
  emoji: string;                   
}

export interface UserDailySideQuest {
  id: string;                      
  user_id: string;                 
  current_quest_id: string;        
  streak: number;                  
  last_completed_date?: string | null;    
  date_assigned: string;           
  completed: boolean;              
  value_achieved: number;          
  category: string;                
  emoji: string;                   
}

export interface DayTracking {
  id: string;                      
  user_id: string;                 
  date: Date;
}

export interface Activity {
  id: string;                      
  day_tracking_id: string;         
  name: string;
  emoji: string;                   
  hours: number;                   
  minutes: number;                 
  is_custom: boolean;              
}

export interface ActivityType {
  id: string;                      
  name: string;                    
  emoji: string;
  is_active: boolean;              
  order: number;                   
}

export interface Goal {
  id: string;                      
  user_id: string;                 
  name: string;
  description?: string;            
  emoji: string;                   
  start_date: Date;                
  end_date?: Date;                 
  goal_value: number;              
  current_value: number;           
  goal_unit: string;
  before_photo?: string;           
  after_photo?: string;            
}

export interface MicroGoal {
  id: string;                      
  goal_id: string;                 
  title: string;
  completed: boolean;              
  completed_at?: Date;             
}

export interface GoalJournalEntry {
  id: string;                      
  goal_id: string;                 
  milestone_percentage: number;    
  content: string;
  created_at: Date;                
}

export interface Friend {
  id: string;                      
  user_id: string;                 
  friend_id: string;               
  created: Date;                   
}

export interface Group {
  id: string;                      
  name: string;
  emoji: string;                   
  admin_id: string;                
  enable_sidequests: boolean;      
  created: Date;                   
  timezone: string;                
  level: number;                   
  strength_xp: number;             
  money_xp: number;                
  health_xp: number;               
  knowledge_xp: number;            
  invitation_code?: string;        
  code_expiry?: Date;              
}

export interface GroupMember {
  id: string;                      
  group_id: string;                
  user_id: string;                 
  nickname: string;
  is_admin: boolean;               
  joined_date: Date;               
}

export interface GroupQuest {
  id: string;                      
  group_id: string;                
  name: string;
  description?: string;            
  emoji: string;                   
  category: string;
  priority: string;                
  quest_type: string;              
  goal_value: number;              
  goal_unit: string;
  goal_period: string;
  task_days_of_week?: string;      
  task_days_of_month?: string;     
  streak: number;                  
  created: Date;                   
}

export interface GroupQuestProgress {
  id: string;                      
  quest_id: string;                
  user_id: string;                 
  date: Date;
  value_achieved: number;          
  completed: boolean;              
}

export interface GroupJoinRequest {
  id: string;                      
  group_id: string;                
  username_invited: string;        
  invited_by: string;              
  created: Date;                   
}
