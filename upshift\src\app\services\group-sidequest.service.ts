﻿import { Injectable } from '@angular/core';
import { Observable, from, of, throwError } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import { SupabaseService } from './supabase.service';
import { GroupSideQuest, GroupSideQuestMemberStatus, GroupDailyQuest } from '../models/group-sidequest.model';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class GroupSideQuestService {

  constructor(
    private supabaseService: SupabaseService,
    private http: HttpClient
  ) { }

  getGroupSideQuests(groupId: string): Observable<GroupSideQuest[]> {

    return from(
      this.supabaseService.getClient()
        .from('group_sidequests')
        .select('*, group_sidequest_pool(*)')
        .eq('group_id', groupId)
        .order('date_assigned', { ascending: false })
    ).pipe(
      map(response => {
        if (response.error) {
          return [];
        }


        return response.data.map(item => {
          const sideQuest: GroupSideQuest = {
            id: item.id,
            group_id: item.group_id,
            current_quest_id: item.current_quest_id,
            streak: item.streak,
            last_completed_date: item.last_completed_date,
            date_assigned: item.date_assigned,
            completed: item.completed,
            value_achieved: item.value_achieved,
            category: item.category,
            created_at: item.created_at,
            current_quest: item.group_sidequest_pool
          };
          return sideQuest;
        });
      }),
      catchError(error => {
        return of([]);
      })
    );
  }

  getGroupSideQuest(sideQuestId: string): Observable<GroupSideQuest | null> {

    return from(
      this.supabaseService.getClient()
        .from('group_sidequests')
        .select('*, group_sidequest_pool(*)')
        .eq('id', sideQuestId)
        .single()
    ).pipe(
      map(response => {
        if (response.error) {
          return null;
        }


        const item = response.data;
        const sideQuest: GroupSideQuest = {
          id: item.id,
          group_id: item.group_id,
          current_quest_id: item.current_quest_id,
          streak: item.streak,
          last_completed_date: item.last_completed_date,
          date_assigned: item.date_assigned,
          completed: item.completed,
          value_achieved: item.value_achieved,
          category: item.category,
          created_at: item.created_at,
          current_quest: item.group_sidequest_pool
        };
        return sideQuest;
      }),
      catchError(error => {
        return of(null);
      })
    );
  }

  getLatestGroupSideQuest(groupId: string): Observable<GroupSideQuest | null> {

    return this.getGroupSideQuests(groupId).pipe(
      map(sideQuests => {
        if (!sideQuests || sideQuests.length === 0) {
          return null;
        }

        return sideQuests[0];
      }),
      catchError(error => {
        return of(null);
      })
    );
  }

  getMemberStatus(groupQuestId: string, memberId: string): Observable<GroupSideQuestMemberStatus | null> {

    return from(
      this.supabaseService.getClient()
        .from('group_sidequest_member_status')
        .select('*')
        .eq('group_quest_id', groupQuestId)
        .eq('member_id', memberId)
        .single()
    ).pipe(
      map(response => {
        if (response.error) {
          return null;
        }

        return response.data as GroupSideQuestMemberStatus;
      }),
      catchError(error => {
        return of(null);
      })
    );
  }

  getAllMemberStatuses(groupQuestId: string): Observable<GroupSideQuestMemberStatus[]> {

    return from(
      this.supabaseService.getClient()
        .from('group_sidequest_member_status')
        .select('*, profiles(id, username, profile_picture)')
        .eq('group_quest_id', groupQuestId)
    ).pipe(
      map(response => {
        if (response.error) {
          return [];
        }


        return response.data.map(item => {
          const status: GroupSideQuestMemberStatus = {
            id: item.id,
            group_quest_id: item.group_quest_id,
            member_id: item.member_id,
            completed: item.completed,
            value_achieved: item.value_achieved,
            last_updated: item.last_updated,
            created_at: item.created_at,
            member: item.profiles
          };
          return status;
        });
      }),
      catchError(error => {
        return of([]);
      })
    );
  }

  toggleMemberCompletion(statusId: string, groupId: string): Observable<GroupSideQuestMemberStatus> {

    return from(
      this.supabaseService.getClient()
        .from('group_sidequest_member_status')
        .select('*, group_quest:group_quest_id(*)')
        .eq('id', statusId)
        .single()
    ).pipe(
      switchMap(statusResponse => {
        if (statusResponse.error) {
          return throwError(() => new Error(statusResponse.error.message));
        }

        const currentStatus = statusResponse.data as any;
        const groupQuestId = currentStatus.group_quest_id;
        const currentCompleted = currentStatus.completed;
        const today = new Date().toISOString().split('T')[0];

        const newCompleted = !currentCompleted;

        return from(
          this.supabaseService.getClient()
            .from('group_sidequests')
            .select('*, group_sidequest_pool(*)')
            .eq('id', groupQuestId)
            .single()
        ).pipe(
          switchMap(questResponse => {
            if (questResponse.error) {
              return throwError(() => new Error(questResponse.error.message));
            }

            const quest = questResponse.data;
            const goalValue = quest.group_sidequest_pool?.goal_value || 60; 
            const newValueAchieved = newCompleted ? goalValue : 0;

            return from(
              this.supabaseService.getClient()
                .from('group_sidequest_member_status')
                .update({
                  completed: newCompleted,
                  value_achieved: newValueAchieved,
                  last_updated: today
                })
                .eq('id', statusId)
                .select()
                .single()
            ).pipe(
              switchMap(updateResponse => {
                if (updateResponse.error) {
                  return throwError(() => new Error(updateResponse.error.message));
                }

                const updatedStatus = updateResponse.data as GroupSideQuestMemberStatus;

                return from(
                  this.supabaseService.getClient()
                    .from('group_sidequests')
                    .select('group_id')
                    .eq('id', groupQuestId)
                    .single()
                ).pipe(
                  switchMap(groupQuestResponse => {
                    if (groupQuestResponse.error) {
                      return throwError(() => new Error(groupQuestResponse.error.message));
                    }

                    const groupId = groupQuestResponse.data.group_id;

                    return from(
                      this.supabaseService.getClient()
                        .from('group_members')
                        .select('user_id')
                        .eq('group_id', groupId)
                        .lt('joined_date', today)
                    ).pipe(
                      switchMap(eligibleMembersResponse => {
                        if (eligibleMembersResponse.error) {
                          return throwError(() => new Error(eligibleMembersResponse.error.message));
                        }

                        const eligibleMembers = eligibleMembersResponse.data;
                        const eligibleMemberIds = eligibleMembers.map(m => m.user_id);
                        const eligibleMembersCount = eligibleMembers.length;

                        if (eligibleMembersCount === 0) {
                          return of(updatedStatus);
                        }

                        return from(
                          this.supabaseService.getClient()
                            .from('group_sidequest_member_status')
                            .select('member_id')
                            .eq('group_quest_id', groupQuestId)
                            .eq('completed', true)
                        ).pipe(
                          switchMap(completedMembersResponse => {
                            if (completedMembersResponse.error) {
                              return throwError(() => new Error(completedMembersResponse.error.message));
                            }

                            const completedMembers = completedMembersResponse.data;
                            const completedMemberIds = completedMembers.map(m => m.member_id);

                            const completedEligibleCount = eligibleMemberIds.filter(id => completedMemberIds.includes(id)).length;


                            const allCompleted = completedEligibleCount === eligibleMembersCount && eligibleMembersCount > 0;

                            return from(
                              this.supabaseService.getClient()
                                .from('group_sidequests')
                                .select('*')
                                .eq('id', groupQuestId)
                                .single()
                            ).pipe(
                              switchMap(groupQuestDetailResponse => {
                                if (groupQuestDetailResponse.error) {
                                  return throwError(() => new Error(groupQuestDetailResponse.error.message));
                                }

                                const groupQuest = groupQuestDetailResponse.data;
                                const wasAllCompleted = groupQuest.completed;
                                const category = groupQuest.category;


                                if (allCompleted && !wasAllCompleted) {
                                  const xpToAdd = eligibleMembersCount * 2; 

                                  return from(
                                    this.supabaseService.getClient()
                                      .from('group_sidequests')
                                      .update({
                                        streak: groupQuest.streak + 1,
                                        completed: true,
                                        last_completed_date: today
                                      })
                                      .eq('id', groupQuestId)
                                  ).pipe(
                                    switchMap(() => {
                                      return from(
                                        this.supabaseService.getClient()
                                          .from('groups')
                                          .select('*')
                                          .eq('id', groupId)
                                          .single()
                                      ).pipe(
                                        switchMap(groupXpResponse => {
                                          if (groupXpResponse.error) {
                                            return throwError(() => new Error(groupXpResponse.error.message));
                                          }

                                          let currentXp = 0;
                                          if (category === 'strength') {
                                            currentXp = groupXpResponse.data.strength_xp || 0;
                                          } else if (category === 'money') {
                                            currentXp = groupXpResponse.data.money_xp || 0;
                                          } else if (category === 'health') {
                                            currentXp = groupXpResponse.data.health_xp || 0;
                                          } else if (category === 'knowledge') {
                                            currentXp = groupXpResponse.data.knowledge_xp || 0;
                                          }
                                          const newXp = currentXp + xpToAdd;

                                          return from(
                                            this.supabaseService.getClient()
                                              .from('groups')
                                              .update({
                                                [`${category}_xp`]: newXp
                                              })
                                              .eq('id', groupId)
                                          ).pipe(
                                            switchMap(() => {
                                              return from(
                                                this.supabaseService.getClient()
                                                  .from('groups')
                                                  .select('*')
                                                  .eq('id', groupId)
                                                  .single()
                                              ).pipe(
                                                map(verifyResponse => {
                                                  if (verifyResponse.error) {
                                                  } else {
                                                    let updatedXp = 0;
                                                    if (category === 'strength') {
                                                      updatedXp = verifyResponse.data.strength_xp || 0;
                                                    } else if (category === 'money') {
                                                      updatedXp = verifyResponse.data.money_xp || 0;
                                                    } else if (category === 'health') {
                                                      updatedXp = verifyResponse.data.health_xp || 0;
                                                    } else if (category === 'knowledge') {
                                                      updatedXp = verifyResponse.data.knowledge_xp || 0;
                                                    }
                                                  }
                                                  return updatedStatus;
                                                })
                                              );
                                            })
                                          );
                                        })
                                      );
                                    })
                                  );
                                } else if (!allCompleted && wasAllCompleted) {
                                  const xpToSubtract = -1 * eligibleMembersCount * 2; 

                                  const newStreak = Math.max(0, groupQuest.streak - 1);
                                  return from(
                                    this.supabaseService.getClient()
                                      .from('group_sidequests')
                                      .update({
                                        streak: newStreak,
                                        completed: false
                                      })
                                      .eq('id', groupQuestId)
                                  ).pipe(
                                    switchMap(() => {
                                      return from(
                                        this.supabaseService.getClient()
                                          .from('groups')
                                          .select('*')
                                          .eq('id', groupId)
                                          .single()
                                      ).pipe(
                                        switchMap(groupXpResponse => {
                                          if (groupXpResponse.error) {
                                            return throwError(() => new Error(groupXpResponse.error.message));
                                          }

                                          let currentXp = 0;
                                          if (category === 'strength') {
                                            currentXp = groupXpResponse.data.strength_xp || 0;
                                          } else if (category === 'money') {
                                            currentXp = groupXpResponse.data.money_xp || 0;
                                          } else if (category === 'health') {
                                            currentXp = groupXpResponse.data.health_xp || 0;
                                          } else if (category === 'knowledge') {
                                            currentXp = groupXpResponse.data.knowledge_xp || 0;
                                          }
                                          const newXp = Math.max(0, currentXp + xpToSubtract);

                                          return from(
                                            this.supabaseService.getClient()
                                              .from('groups')
                                              .update({
                                                [`${category}_xp`]: newXp
                                              })
                                              .eq('id', groupId)
                                          ).pipe(
                                            switchMap(() => {
                                              return from(
                                                this.supabaseService.getClient()
                                                  .from('groups')
                                                  .select('*')
                                                  .eq('id', groupId)
                                                  .single()
                                              ).pipe(
                                                map(verifyResponse => {
                                                  if (verifyResponse.error) {
                                                  } else {
                                                    let updatedXp = 0;
                                                    if (category === 'strength') {
                                                      updatedXp = verifyResponse.data.strength_xp || 0;
                                                    } else if (category === 'money') {
                                                      updatedXp = verifyResponse.data.money_xp || 0;
                                                    } else if (category === 'health') {
                                                      updatedXp = verifyResponse.data.health_xp || 0;
                                                    } else if (category === 'knowledge') {
                                                      updatedXp = verifyResponse.data.knowledge_xp || 0;
                                                    }
                                                    } XP from group ${groupId} ${category}_xp from ${currentXp} to ${newXp}, verified value: ${updatedXp}`);
                                                  }
                                                  return updatedStatus;
                                                })
                                              );
                                            })
                                          );
                                        })
                                      );
                                    })
                                  );
                                } else {
                                  return of(updatedStatus);
                                }
                              })
                            );
                          })
                        );
                      })
                    );
                  })
                );
              })
            );
          })
        );
      })
    );
  }

  ensureGroupHasDailySideQuest(groupId: string): Observable<GroupDailyQuest | null> {

    return this.getLatestGroupSideQuest(groupId).pipe(
      switchMap(existingSideQuest => {
        if (existingSideQuest) {

          const today = new Date().toISOString().split('T')[0];
          const sideQuestDate = existingSideQuest.date_assigned?.split('T')[0];

          if (sideQuestDate === today) {
            const dailyQuest: GroupDailyQuest = {
              id: existingSideQuest.id,
              group_id: existingSideQuest.group_id,
              streak: existingSideQuest.streak,
              completed: existingSideQuest.completed,
              value_achieved: existingSideQuest.value_achieved,
              date_assigned: existingSideQuest.date_assigned,
              last_completed_date: existingSideQuest.last_completed_date,
              category: existingSideQuest.category,
              current_quest: {
                id: existingSideQuest.current_quest?.id || '',
                name: existingSideQuest.current_quest?.name || '',
                description: existingSideQuest.current_quest?.description,
                goal_value: existingSideQuest.current_quest?.goal_value || 0,
                goal_unit: existingSideQuest.current_quest?.goal_unit || 'count',
                emoji: existingSideQuest.current_quest?.emoji || '🎯'
              },
              eligible_members_count: 0,
              completed_members_count: 0
            };

            return from(
              this.supabaseService.getClient()
                .from('group_members')
                .select('user_id')
                .eq('group_id', groupId)
                .lt('joined_date', today)
            ).pipe(
              switchMap(eligibleMembersResponse => {
                if (eligibleMembersResponse.error) {
                  return of(dailyQuest);
                }

                const eligibleMembers = eligibleMembersResponse.data;
                dailyQuest.eligible_members_count = eligibleMembers.length;

                return from(
                  this.supabaseService.getClient()
                    .from('group_sidequest_member_status')
                    .select('member_id')
                    .eq('group_quest_id', existingSideQuest.id)
                    .eq('completed', true)
                ).pipe(
                  map(completedMembersResponse => {
                    if (completedMembersResponse.error) {
                      return dailyQuest;
                    }

                    const completedMembers = completedMembersResponse.data;
                    dailyQuest.completed_members_count = completedMembers.length;

                    return dailyQuest;
                  })
                );
              })
            );
          } else {
            return this.createGroupSideQuest(groupId);
          }
        } else {
          return this.createGroupSideQuest(groupId);
        }
      })
    );
  }

  private createGroupSideQuest(groupId: string): Observable<GroupDailyQuest | null> {

    return from(
      this.supabaseService.getClient()
        .from('group_sidequest_pool')
        .select('*')
        .order('id', { ascending: false })
    ).pipe(
      switchMap(poolResponse => {
        if (poolResponse.error) {
          return of(null);
        }

        if (!poolResponse.data || poolResponse.data.length === 0) {
          return of(null);
        }

        const randomIndex = Math.floor(Math.random() * poolResponse.data.length);
        const selectedQuest = poolResponse.data[randomIndex];


        const today = new Date().toISOString().split('T')[0];
        const sideQuestData = {
          current_quest_id: selectedQuest.id,
          streak: 0,
          date_assigned: today,
          completed: false,
          value_achieved: 0,
          category: selectedQuest.category
        };

        return from(
          this.supabaseService.getClient()
            .from('group_sidequests')
            .select('id')
            .eq('group_id', groupId)
            .maybeSingle()
        ).pipe(
          switchMap(existingQuestResponse => {
            if (existingQuestResponse.error) {
              return of(null);
            }

            if (existingQuestResponse.data) {
              return from(
                this.supabaseService.getClient()
                  .from('group_sidequests')
                  .update(sideQuestData)
                  .eq('group_id', groupId)
                  .select()
                  .single()
              );
            } else {
              return from(
                this.supabaseService.getClient()
                  .from('group_sidequests')
                  .insert({
                    group_id: groupId,
                    ...sideQuestData
                  })
                  .select()
                  .single()
              );
            }
          })
        ).pipe(
          switchMap(createResponse => {
            if (!createResponse || createResponse.error) {
              return of(null);
            }


            return from(
              this.supabaseService.getClient()
                .from('group_members')
                .select('user_id')
                .eq('group_id', groupId)
            ).pipe(
              switchMap(membersResponse => {
                if (membersResponse.error) {
                  return of(null);
                }

                const members = membersResponse.data;

                if (members.length === 0) {
                  const createdSideQuest: GroupSideQuest = {
                    ...createResponse.data,
                    current_quest: selectedQuest
                  };

                  const dailyQuest: GroupDailyQuest = {
                    id: createdSideQuest.id,
                    group_id: createdSideQuest.group_id,
                    streak: createdSideQuest.streak,
                    completed: createdSideQuest.completed,
                    value_achieved: createdSideQuest.value_achieved,
                    date_assigned: createdSideQuest.date_assigned,
                    last_completed_date: createdSideQuest.last_completed_date,
                    category: createdSideQuest.category,
                    current_quest: {
                      id: selectedQuest.id || '',
                      name: selectedQuest.name || '',
                      description: selectedQuest.description,
                      goal_value: selectedQuest.goal_value || 0,
                      goal_unit: selectedQuest.goal_unit || 'count',
                      emoji: selectedQuest.emoji || '🎯'
                    },
                    eligible_members_count: 0,
                    completed_members_count: 0
                  };

                  return of(dailyQuest);
                }

                const memberStatusPromises = members.map(member => {
                  return this.supabaseService.getClient()
                    .from('group_sidequest_member_status')
                    .insert({
                      group_quest_id: createResponse.data.id,
                      member_id: member.user_id,
                      completed: false,
                      value_achieved: 0
                    })
                    .select();
                });

                return from(Promise.all(memberStatusPromises)).pipe(
                  map(() => {

                    const createdSideQuest: GroupSideQuest = {
                      ...createResponse.data,
                      current_quest: selectedQuest
                    };

                    const dailyQuest: GroupDailyQuest = {
                      id: createdSideQuest.id,
                      group_id: createdSideQuest.group_id,
                      streak: createdSideQuest.streak,
                      completed: createdSideQuest.completed,
                      value_achieved: createdSideQuest.value_achieved,
                      date_assigned: createdSideQuest.date_assigned,
                      last_completed_date: createdSideQuest.last_completed_date,
                      category: createdSideQuest.category,
                      current_quest: {
                        id: selectedQuest.id || '',
                        name: selectedQuest.name || '',
                        description: selectedQuest.description,
                        goal_value: selectedQuest.goal_value || 0,
                        goal_unit: selectedQuest.goal_unit || 'count',
                        emoji: selectedQuest.emoji || '🎯'
                      },
                      eligible_members_count: members.length,
                      completed_members_count: 0
                    };

                    return dailyQuest;
                  })
                );
              })
            );
          })
        );
      })
    );
  }



  updateGroupXp(groupId: string, category: string, newXpValue: number): Observable<any> {

    return from(
      this.supabaseService.getClient()
        .from('groups')
        .update({
          [`${category}_xp`]: newXpValue
        })
        .eq('id', groupId)
    ).pipe(
      map(response => {
        if (response.error) {
          throw new Error(response.error.message);
        }

        return response.data;
      }),
      catchError(error => {
        return throwError(() => error);
      })
    );
  }

  importSideQuestsFromJson(): Observable<boolean> {

    return this.http.get<any[]>('assets/data/group-sidequest-pool.json').pipe(
      switchMap(sideQuests => {

        const insertPromises = sideQuests.map(sideQuest => {
          return this.supabaseService.getClient()
            .from('group_sidequest_pool')
            .insert({
              name: sideQuest.name,
              description: sideQuest.description,
              emoji: sideQuest.emoji,
              goal_value: sideQuest.goal_value,
              category: sideQuest.category
            });
        });

        return from(Promise.all(insertPromises)).pipe(
          map(() => {
            return true;
          }),
          catchError(error => {
            return of(false);
          })
        );
      }),
      catchError(error => {
        return of(false);
      })
    );
  }
}