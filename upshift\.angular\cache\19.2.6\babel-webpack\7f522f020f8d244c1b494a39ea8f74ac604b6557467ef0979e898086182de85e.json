{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _ImportSideQuestsPage;\nimport { inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule, FormBuilder, Validators } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { HttpClient } from '@angular/common/http';\nimport { SupabaseService } from '../../services/supabase.service';\nimport { AdminService } from '../../services/admin.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nfunction ImportSideQuestsPage_div_16_div_5_div_1_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function ImportSideQuestsPage_div_16_div_5_div_1_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const quest_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.deleteSideQuest(quest_r3.id));\n    });\n    i0.ɵɵtext(1, \"Delete\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ImportSideQuestsPage_div_16_div_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 19)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 20)(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 21)(16, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function ImportSideQuestsPage_div_16_div_5_div_1_Template_button_click_16_listener() {\n      const quest_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.toggleSideQuestActive(quest_r3));\n    });\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, ImportSideQuestsPage_div_16_div_5_div_1_button_18_Template, 2, 0, \"button\", 23);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const quest_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(quest_r3.emoji);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(quest_r3.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(quest_r3.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Category: \", quest_r3.category, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"Goal: \", quest_r3.goal_value, \" \", quest_r3.goal_unit, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active-status\", quest_r3.active)(\"inactive-status\", !quest_r3.active);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" Status: \", quest_r3.active ? \"Active\" : \"Inactive\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", quest_r3.active ? \"Deactivate\" : \"Activate\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", quest_r3.id);\n  }\n}\nfunction ImportSideQuestsPage_div_16_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtemplate(1, ImportSideQuestsPage_div_16_div_5_div_1_Template, 19, 13, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.existingSideQuests);\n  }\n}\nfunction ImportSideQuestsPage_div_16_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"p\");\n    i0.ɵɵtext(2, \"No user side quests found in the database.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImportSideQuestsPage_div_16_div_13_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtext(1, \" Name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ImportSideQuestsPage_div_16_div_13_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtext(1, \" Description is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ImportSideQuestsPage_div_16_div_13_option_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", category_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(category_r7);\n  }\n}\nfunction ImportSideQuestsPage_div_16_div_13_option_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const unit_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", unit_r8);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(unit_r8);\n  }\n}\nfunction ImportSideQuestsPage_div_16_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"form\", 27);\n    i0.ɵɵlistener(\"ngSubmit\", function ImportSideQuestsPage_div_16_div_13_Template_form_ngSubmit_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.addSideQuest());\n    });\n    i0.ɵɵelementStart(2, \"div\", 28)(3, \"div\", 29)(4, \"label\", 30);\n    i0.ɵɵtext(5, \"Emoji\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"input\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 32)(8, \"label\", 33);\n    i0.ɵɵtext(9, \"Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"input\", 34);\n    i0.ɵɵtemplate(11, ImportSideQuestsPage_div_16_div_13_div_11_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 36)(13, \"label\", 37);\n    i0.ɵɵtext(14, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"textarea\", 38);\n    i0.ɵɵtemplate(16, ImportSideQuestsPage_div_16_div_13_div_16_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 28)(18, \"div\", 36)(19, \"label\", 39);\n    i0.ɵɵtext(20, \"Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"select\", 40);\n    i0.ɵɵtemplate(22, ImportSideQuestsPage_div_16_div_13_option_22_Template, 2, 2, \"option\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 42)(24, \"label\", 43);\n    i0.ɵɵtext(25, \"Goal Value\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"input\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 36)(28, \"label\", 45);\n    i0.ɵɵtext(29, \"Goal Unit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"select\", 46);\n    i0.ɵɵtemplate(31, ImportSideQuestsPage_div_16_div_13_option_31_Template, 2, 2, \"option\", 41);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(32, \"button\", 47);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    let tmp_4_0;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r3.sideQuestForm);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r3.sideQuestForm.get(\"name\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r3.sideQuestForm.get(\"name\")) == null ? null : tmp_3_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r3.sideQuestForm.get(\"description\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r3.sideQuestForm.get(\"description\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.categories);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.goalUnits);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r3.sideQuestForm.invalid || ctx_r3.isImporting);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.isImporting ? \"Adding...\" : \"Add Side Quest\", \" \");\n  }\n}\nfunction ImportSideQuestsPage_div_16_div_20_div_8_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 19)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 20)(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const quest_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(quest_r10.emoji);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(quest_r10.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(quest_r10.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Category: \", quest_r10.category, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"Goal: \", quest_r10.goal_value, \" \", quest_r10.goal_unit, \"\");\n  }\n}\nfunction ImportSideQuestsPage_div_16_div_20_div_8_p_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"...and \", ctx_r3.jsonSideQuests.length - 5, \" more\");\n  }\n}\nfunction ImportSideQuestsPage_div_16_div_20_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"h3\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ImportSideQuestsPage_div_16_div_20_div_8_div_3_Template, 13, 6, \"div\", 16)(4, ImportSideQuestsPage_div_16_div_20_div_8_p_4_Template, 2, 1, \"p\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Side Quests to Import (\", ctx_r3.jsonSideQuests.length, \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.jsonSideQuests.slice(0, 5));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.jsonSideQuests.length > 5);\n  }\n}\nfunction ImportSideQuestsPage_div_16_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\");\n    i0.ɵɵtext(2, \"Import side quests from the JSON file to Supabase.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 50)(4, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function ImportSideQuestsPage_div_16_div_20_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.importJsonSideQuests());\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function ImportSideQuestsPage_div_16_div_20_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.openSupabaseConsole());\n    });\n    i0.ɵɵtext(7, \" Open Supabase Console \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, ImportSideQuestsPage_div_16_div_20_div_8_Template, 5, 3, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.isImporting || ctx_r3.jsonSideQuests.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.isImporting ? \"Importing...\" : \"Import Side Quests\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.jsonSideQuests.length > 0);\n  }\n}\nfunction ImportSideQuestsPage_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"section\", 7)(2, \"div\", 8)(3, \"h2\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, ImportSideQuestsPage_div_16_div_5_Template, 2, 1, \"div\", 9)(6, ImportSideQuestsPage_div_16_div_6_Template, 3, 0, \"div\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"section\", 11)(8, \"div\", 8)(9, \"h2\");\n    i0.ɵɵtext(10, \"Add New User Side Quest\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ImportSideQuestsPage_div_16_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.toggleAddForm());\n    });\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, ImportSideQuestsPage_div_16_div_13_Template, 34, 7, \"div\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"section\", 14)(15, \"div\", 8)(16, \"h2\");\n    i0.ɵɵtext(17, \"Import from JSON\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ImportSideQuestsPage_div_16_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.toggleJsonImport());\n    });\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(20, ImportSideQuestsPage_div_16_div_20_Template, 9, 3, \"div\", 6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Existing User Side Quests (\", ctx_r3.existingSideQuests.length, \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.existingSideQuests.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.existingSideQuests.length === 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.showAddForm ? \"Hide Form\" : \"Show Form\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.showAddForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.showJsonImport ? \"Hide JSON Import\" : \"Show JSON Import\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.showJsonImport);\n  }\n}\nfunction ImportSideQuestsPage_div_17_div_5_div_1_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function ImportSideQuestsPage_div_17_div_5_div_1_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const quest_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.deleteGroupSideQuest(quest_r13.id));\n    });\n    i0.ɵɵtext(1, \"Delete\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ImportSideQuestsPage_div_17_div_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 19)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 20)(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 21)(16, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function ImportSideQuestsPage_div_17_div_5_div_1_Template_button_click_16_listener() {\n      const quest_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.toggleGroupSideQuestActive(quest_r13));\n    });\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, ImportSideQuestsPage_div_17_div_5_div_1_button_18_Template, 2, 0, \"button\", 23);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const quest_r13 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(quest_r13.emoji);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(quest_r13.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(quest_r13.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Category: \", quest_r13.category, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"Goal: \", quest_r13.goal_value, \" \", quest_r13.goal_unit, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active-status\", quest_r13.active)(\"inactive-status\", !quest_r13.active);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" Status: \", quest_r13.active ? \"Active\" : \"Inactive\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", quest_r13.active ? \"Deactivate\" : \"Activate\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", quest_r13.id);\n  }\n}\nfunction ImportSideQuestsPage_div_17_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtemplate(1, ImportSideQuestsPage_div_17_div_5_div_1_Template, 19, 13, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.existingGroupSideQuests);\n  }\n}\nfunction ImportSideQuestsPage_div_17_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"p\");\n    i0.ɵɵtext(2, \"No group side quests found in the database.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImportSideQuestsPage_div_17_div_13_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtext(1, \" Name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ImportSideQuestsPage_div_17_div_13_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtext(1, \" Description is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ImportSideQuestsPage_div_17_div_13_option_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", category_r16);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(category_r16);\n  }\n}\nfunction ImportSideQuestsPage_div_17_div_13_option_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const unit_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", unit_r17);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(unit_r17);\n  }\n}\nfunction ImportSideQuestsPage_div_17_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"form\", 27);\n    i0.ɵɵlistener(\"ngSubmit\", function ImportSideQuestsPage_div_17_div_13_Template_form_ngSubmit_1_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.addGroupSideQuest());\n    });\n    i0.ɵɵelementStart(2, \"div\", 28)(3, \"div\", 29)(4, \"label\", 53);\n    i0.ɵɵtext(5, \"Emoji\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"input\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 32)(8, \"label\", 55);\n    i0.ɵɵtext(9, \"Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"input\", 56);\n    i0.ɵɵtemplate(11, ImportSideQuestsPage_div_17_div_13_div_11_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 36)(13, \"label\", 57);\n    i0.ɵɵtext(14, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"textarea\", 58);\n    i0.ɵɵtemplate(16, ImportSideQuestsPage_div_17_div_13_div_16_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 28)(18, \"div\", 36)(19, \"label\", 59);\n    i0.ɵɵtext(20, \"Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"select\", 60);\n    i0.ɵɵtemplate(22, ImportSideQuestsPage_div_17_div_13_option_22_Template, 2, 2, \"option\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 42)(24, \"label\", 61);\n    i0.ɵɵtext(25, \"Goal Value\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"input\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 36)(28, \"label\", 63);\n    i0.ɵɵtext(29, \"Goal Unit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"select\", 64);\n    i0.ɵɵtemplate(31, ImportSideQuestsPage_div_17_div_13_option_31_Template, 2, 2, \"option\", 41);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(32, \"button\", 47);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    let tmp_4_0;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r3.groupSideQuestForm);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r3.groupSideQuestForm.get(\"name\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r3.groupSideQuestForm.get(\"name\")) == null ? null : tmp_3_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r3.groupSideQuestForm.get(\"description\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r3.groupSideQuestForm.get(\"description\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.categories);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.goalUnits);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r3.groupSideQuestForm.invalid || ctx_r3.isImporting);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.isImporting ? \"Adding...\" : \"Add Group Side Quest\", \" \");\n  }\n}\nfunction ImportSideQuestsPage_div_17_div_20_div_8_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 19)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 20)(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const quest_r19 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(quest_r19.emoji);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(quest_r19.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(quest_r19.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Category: \", quest_r19.category, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"Goal: \", quest_r19.goal_value, \" \", quest_r19.goal_unit, \"\");\n  }\n}\nfunction ImportSideQuestsPage_div_17_div_20_div_8_p_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"...and \", ctx_r3.jsonGroupSideQuests.length - 5, \" more\");\n  }\n}\nfunction ImportSideQuestsPage_div_17_div_20_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"h3\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ImportSideQuestsPage_div_17_div_20_div_8_div_3_Template, 13, 6, \"div\", 16)(4, ImportSideQuestsPage_div_17_div_20_div_8_p_4_Template, 2, 1, \"p\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Group Side Quests to Import (\", ctx_r3.jsonGroupSideQuests.length, \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.jsonGroupSideQuests.slice(0, 5));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.jsonGroupSideQuests.length > 5);\n  }\n}\nfunction ImportSideQuestsPage_div_17_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\");\n    i0.ɵɵtext(2, \"Import group side quests from the JSON file to Supabase.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 50)(4, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function ImportSideQuestsPage_div_17_div_20_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.importJsonGroupSideQuests());\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function ImportSideQuestsPage_div_17_div_20_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.openSupabaseConsole());\n    });\n    i0.ɵɵtext(7, \" Open Supabase Console \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, ImportSideQuestsPage_div_17_div_20_div_8_Template, 5, 3, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.isImporting || ctx_r3.jsonGroupSideQuests.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.isImporting ? \"Importing...\" : \"Import Group Side Quests\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.jsonGroupSideQuests.length > 0);\n  }\n}\nfunction ImportSideQuestsPage_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"section\", 7)(2, \"div\", 8)(3, \"h2\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, ImportSideQuestsPage_div_17_div_5_Template, 2, 1, \"div\", 9)(6, ImportSideQuestsPage_div_17_div_6_Template, 3, 0, \"div\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"section\", 11)(8, \"div\", 8)(9, \"h2\");\n    i0.ɵɵtext(10, \"Add New Group Side Quest\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ImportSideQuestsPage_div_17_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.toggleGroupAddForm());\n    });\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, ImportSideQuestsPage_div_17_div_13_Template, 34, 7, \"div\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"section\", 14)(15, \"div\", 8)(16, \"h2\");\n    i0.ɵɵtext(17, \"Import Group Side Quests\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ImportSideQuestsPage_div_17_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.toggleGroupJsonImport());\n    });\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(20, ImportSideQuestsPage_div_17_div_20_Template, 9, 3, \"div\", 6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Existing Group Side Quests (\", ctx_r3.existingGroupSideQuests.length, \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.existingGroupSideQuests.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.existingGroupSideQuests.length === 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.showGroupAddForm ? \"Hide Form\" : \"Show Form\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.showGroupAddForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.showGroupJsonImport ? \"Hide JSON Import\" : \"Show JSON Import\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.showGroupJsonImport);\n  }\n}\nexport class ImportSideQuestsPage {\n  constructor() {\n    this.importStatus = '';\n    this.isImporting = false;\n    this.isAdmin = false;\n    this.existingSideQuests = [];\n    this.jsonSideQuests = [];\n    this.showJsonImport = false;\n    this.showAddForm = false;\n    this.existingGroupSideQuests = [];\n    this.jsonGroupSideQuests = [];\n    this.showGroupJsonImport = false;\n    this.showGroupAddForm = false;\n    this.activeTab = 'user';\n    this.categories = ['money', 'health', 'strength', 'knowledge'];\n    this.goalUnits = ['count', 'steps', 'm', 'km', 'sec', 'min', 'hr', 'Cal', 'g', 'mg', 'l', 'pages', 'books', '%', '€', '$', '£'];\n    this.http = inject(HttpClient);\n    this.supabaseService = inject(SupabaseService);\n    this.adminService = inject(AdminService);\n    this.fb = inject(FormBuilder);\n    this.sideQuestForm = this.fb.group({\n      name: ['', [Validators.required]],\n      description: ['', [Validators.required]],\n      goal_value: [1, [Validators.required, Validators.min(1)]],\n      category: ['health', [Validators.required]],\n      goal_unit: ['count', [Validators.required]],\n      emoji: ['🎯', [Validators.required]]\n    });\n    this.groupSideQuestForm = this.fb.group({\n      name: ['', [Validators.required]],\n      description: ['', [Validators.required]],\n      goal_value: [1, [Validators.required, Validators.min(1)]],\n      category: ['health', [Validators.required]],\n      goal_unit: ['count', [Validators.required]],\n      emoji: ['🎯', [Validators.required]]\n    });\n  }\n  ngOnInit() {\n    this.adminService.isAdmin().subscribe(isAdmin => {\n      this.isAdmin = isAdmin;\n      if (isAdmin) {\n        this.fetchExistingSideQuests();\n        this.fetchExistingGroupSideQuests();\n      } else {\n        this.importStatus = 'You need admin privileges to manage side quests.';\n      }\n    });\n    this.resetForm();\n    this.resetGroupForm();\n  }\n  resetForm() {\n    this.sideQuestForm.reset({\n      name: '',\n      description: '',\n      goal_value: 1,\n      category: 'health',\n      goal_unit: 'count',\n      emoji: '🎯'\n    });\n  }\n  resetGroupForm() {\n    this.groupSideQuestForm.reset({\n      name: '',\n      description: '',\n      goal_value: 1,\n      category: 'health',\n      goal_unit: 'count',\n      emoji: '🎯'\n    });\n  }\n  switchTab(tab) {\n    this.activeTab = tab;\n    this.importStatus = '';\n  }\n  openSupabaseConsole() {\n    window.open('https://app.supabase.com/', '_blank');\n  }\n  fetchExistingSideQuests() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.importStatus = 'Fetching existing side quests from Supabase...';\n      try {\n        const {\n          data,\n          error\n        } = yield _this.supabaseService.getClient().from('daily_sidequest_pool').select('*').order('id', {\n          ascending: true\n        });\n        if (error) {\n          _this.importStatus = `Error fetching existing side quests: ${error.message}`;\n          return;\n        }\n        if (data && data.length > 0) {\n          _this.existingSideQuests = data;\n          _this.importStatus = `Found ${data.length} existing side quests in Supabase.`;\n        } else {\n          _this.importStatus = 'No existing side quests found in Supabase.';\n        }\n      } catch (error) {\n        _this.importStatus = `Error fetching existing side quests: ${error.message}`;\n      }\n    })();\n  }\n  toggleJsonImport() {\n    this.showJsonImport = !this.showJsonImport;\n    if (this.showJsonImport) {\n      this.fetchJsonSideQuests();\n    }\n  }\n  toggleAddForm() {\n    this.showAddForm = !this.showAddForm;\n  }\n  fetchJsonSideQuests() {\n    this.importStatus = 'Fetching side quests from JSON...';\n    this.http.get('assets/data/sidequest-pool.json').subscribe({\n      next: data => {\n        if (!data || data.length === 0) {\n          this.importStatus = 'No side quests found in JSON file';\n          return;\n        }\n        this.jsonSideQuests = data;\n        this.importStatus = `Found ${data.length} side quests in JSON file. Ready to import.`;\n      },\n      error: error => {\n        this.importStatus = `Error fetching side quests JSON: ${error.message}`;\n      }\n    });\n  }\n  importJsonSideQuests() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.jsonSideQuests.length === 0) {\n        _this2.importStatus = 'No side quests to import from JSON';\n        return;\n      }\n      _this2.isImporting = true;\n      _this2.importStatus = 'Importing side quests from JSON...';\n      try {\n        const sideQuestsToImport = _this2.jsonSideQuests.map(quest => {\n          const {\n            id,\n            ...questWithoutId\n          } = quest;\n          return {\n            ...questWithoutId,\n            active: true\n          };\n        });\n        const {\n          error\n        } = yield _this2.supabaseService.getClient().from('daily_sidequest_pool').insert(sideQuestsToImport);\n        if (error) {\n          _this2.importStatus = `Error importing side quests: ${error.message}`;\n          _this2.isImporting = false;\n          return;\n        }\n        _this2.importStatus = 'Side quests imported successfully';\n        _this2.isImporting = false;\n        _this2.fetchExistingSideQuests();\n      } catch (error) {\n        _this2.importStatus = `Error importing side quests: ${error.message}`;\n        _this2.isImporting = false;\n      }\n    })();\n  }\n  addSideQuest() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.sideQuestForm.valid) {\n        _this3.importStatus = 'Please fill out all required fields correctly';\n        return;\n      }\n      _this3.isImporting = true;\n      _this3.importStatus = 'Adding new side quest...';\n      try {\n        const newSideQuest = {\n          ..._this3.sideQuestForm.value,\n          active: true\n        };\n        const {\n          error\n        } = yield _this3.supabaseService.getClient().from('daily_sidequest_pool').insert([newSideQuest]);\n        if (error) {\n          _this3.importStatus = `Error adding side quest: ${error.message}`;\n          _this3.isImporting = false;\n          return;\n        }\n        _this3.importStatus = 'Side quest added successfully';\n        _this3.resetForm();\n        _this3.fetchExistingSideQuests();\n      } catch (error) {\n        _this3.importStatus = `Error adding side quest: ${error.message}`;\n      } finally {\n        _this3.isImporting = false;\n      }\n    })();\n  }\n  toggleSideQuestActive(quest) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          error\n        } = yield _this4.supabaseService.getClient().from('daily_sidequest_pool').update({\n          active: !quest.active\n        }).eq('id', quest.id);\n        if (error) {\n          _this4.importStatus = `Error updating side quest: ${error.message}`;\n          return;\n        }\n        _this4.importStatus = 'Side quest updated successfully';\n        quest.active = !quest.active;\n      } catch (error) {\n        _this4.importStatus = `Error updating side quest: ${error.message}`;\n      }\n    })();\n  }\n  deleteSideQuest(questId) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      if (!questId) {\n        _this5.importStatus = 'Cannot delete quest: Invalid quest ID';\n        return;\n      }\n      if (!confirm('Are you sure you want to delete this side quest?')) {\n        return;\n      }\n      try {\n        const {\n          error\n        } = yield _this5.supabaseService.getClient().from('daily_sidequest_pool').delete().eq('id', questId);\n        if (error) {\n          _this5.importStatus = `Error deleting side quest: ${error.message}`;\n          return;\n        }\n        _this5.importStatus = 'Side quest deleted successfully';\n        _this5.fetchExistingSideQuests();\n      } catch (error) {\n        _this5.importStatus = `Error deleting side quest: ${error.message}`;\n      }\n    })();\n  }\n  fetchExistingGroupSideQuests() {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      _this6.importStatus = 'Fetching existing group side quests from Supabase...';\n      try {\n        const {\n          data,\n          error\n        } = yield _this6.supabaseService.getClient().from('group_sidequest_pool').select('*').order('id', {\n          ascending: true\n        });\n        if (error) {\n          _this6.importStatus = `Error fetching existing group side quests: ${error.message}`;\n          return;\n        }\n        if (data && data.length > 0) {\n          _this6.existingGroupSideQuests = data;\n          _this6.importStatus = `Found ${data.length} existing group side quests in Supabase.`;\n        } else {\n          _this6.importStatus = 'No existing group side quests found in Supabase.';\n        }\n      } catch (error) {\n        _this6.importStatus = `Error fetching existing group side quests: ${error.message}`;\n      }\n    })();\n  }\n  toggleGroupJsonImport() {\n    this.showGroupJsonImport = !this.showGroupJsonImport;\n    if (this.showGroupJsonImport) {\n      this.fetchJsonGroupSideQuests();\n    }\n  }\n  toggleGroupAddForm() {\n    this.showGroupAddForm = !this.showGroupAddForm;\n  }\n  fetchJsonGroupSideQuests() {\n    this.importStatus = 'Fetching group side quests from JSON...';\n    this.http.get('assets/data/group-sidequest-pool.json').subscribe({\n      next: data => {\n        if (!data || data.length === 0) {\n          this.importStatus = 'No group side quests found in JSON file';\n          return;\n        }\n        this.jsonGroupSideQuests = data;\n        this.importStatus = `Found ${data.length} group side quests in JSON file. Ready to import.`;\n      },\n      error: error => {\n        this.importStatus = `Error fetching group side quests JSON: ${error.message}`;\n      }\n    });\n  }\n  importJsonGroupSideQuests() {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      if (_this7.jsonGroupSideQuests.length === 0) {\n        _this7.importStatus = 'No group side quests to import from JSON';\n        return;\n      }\n      _this7.isImporting = true;\n      _this7.importStatus = 'Importing group side quests from JSON...';\n      try {\n        const groupSideQuestsToImport = _this7.jsonGroupSideQuests.map(quest => {\n          const {\n            id,\n            ...questWithoutId\n          } = quest;\n          return {\n            ...questWithoutId,\n            active: true\n          };\n        });\n        const {\n          error\n        } = yield _this7.supabaseService.getClient().from('group_sidequest_pool').insert(groupSideQuestsToImport);\n        if (error) {\n          _this7.importStatus = `Error importing group side quests: ${error.message}`;\n          _this7.isImporting = false;\n          return;\n        }\n        _this7.importStatus = 'Group side quests imported successfully';\n        _this7.isImporting = false;\n        _this7.fetchExistingGroupSideQuests();\n      } catch (error) {\n        _this7.importStatus = `Error importing group side quests: ${error.message}`;\n        _this7.isImporting = false;\n      }\n    })();\n  }\n  addGroupSideQuest() {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this8.groupSideQuestForm.valid) {\n        _this8.importStatus = 'Please fill out all required fields correctly';\n        return;\n      }\n      _this8.isImporting = true;\n      _this8.importStatus = 'Adding new group side quest...';\n      try {\n        const newGroupSideQuest = {\n          ..._this8.groupSideQuestForm.value,\n          active: true\n        };\n        const {\n          error\n        } = yield _this8.supabaseService.getClient().from('group_sidequest_pool').insert([newGroupSideQuest]);\n        if (error) {\n          _this8.importStatus = `Error adding group side quest: ${error.message}`;\n          _this8.isImporting = false;\n          return;\n        }\n        _this8.importStatus = 'Group side quest added successfully';\n        _this8.resetGroupForm();\n        _this8.fetchExistingGroupSideQuests();\n      } catch (error) {\n        _this8.importStatus = `Error adding group side quest: ${error.message}`;\n      } finally {\n        _this8.isImporting = false;\n      }\n    })();\n  }\n  toggleGroupSideQuestActive(quest) {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          error\n        } = yield _this9.supabaseService.getClient().from('group_sidequest_pool').update({\n          active: !quest.active\n        }).eq('id', quest.id);\n        if (error) {\n          _this9.importStatus = `Error updating group side quest: ${error.message}`;\n          return;\n        }\n        _this9.importStatus = 'Group side quest updated successfully';\n        quest.active = !quest.active;\n      } catch (error) {\n        _this9.importStatus = `Error updating group side quest: ${error.message}`;\n      }\n    })();\n  }\n  deleteGroupSideQuest(questId) {\n    var _this10 = this;\n    return _asyncToGenerator(function* () {\n      if (!questId) {\n        _this10.importStatus = 'Cannot delete quest: Invalid quest ID';\n        return;\n      }\n      if (!confirm('Are you sure you want to delete this group side quest?')) {\n        return;\n      }\n      try {\n        const {\n          error\n        } = yield _this10.supabaseService.getClient().from('group_sidequest_pool').delete().eq('id', questId);\n        if (error) {\n          _this10.importStatus = `Error deleting group side quest: ${error.message}`;\n          return;\n        }\n        _this10.importStatus = 'Group side quest deleted successfully';\n        _this10.fetchExistingGroupSideQuests();\n      } catch (error) {\n        _this10.importStatus = `Error deleting group side quest: ${error.message}`;\n      }\n    })();\n  }\n}\n_ImportSideQuestsPage = ImportSideQuestsPage;\n_ImportSideQuestsPage.ɵfac = function ImportSideQuestsPage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _ImportSideQuestsPage)();\n};\n_ImportSideQuestsPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _ImportSideQuestsPage,\n  selectors: [[\"app-import-sidequests\"]],\n  decls: 18,\n  vars: 7,\n  consts: [[1, \"container\"], [1, \"logo\"], [\"src\", \"assets/images/upshift_icon_mini.svg\", \"alt\", \"Upshift\"], [1, \"status-section\"], [1, \"tab-navigation\"], [3, \"click\"], [4, \"ngIf\"], [1, \"existing-quests-section\"], [1, \"section-header\"], [\"class\", \"side-quests-list\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"add-quest-section\"], [1, \"toggle-btn\", 3, \"click\"], [\"class\", \"add-form\", 4, \"ngIf\"], [1, \"import-section\"], [1, \"side-quests-list\"], [\"class\", \"side-quest-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"side-quest-item\"], [1, \"quest-emoji\"], [1, \"quest-details\"], [1, \"quest-meta\"], [1, \"quest-actions\"], [1, \"action-btn\", \"toggle-btn\", 3, \"click\"], [\"class\", \"action-btn delete-btn\", 3, \"click\", 4, \"ngIf\"], [1, \"action-btn\", \"delete-btn\", 3, \"click\"], [1, \"empty-state\"], [1, \"add-form\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-row\"], [1, \"form-group\", \"emoji-input\"], [\"for\", \"emoji\"], [\"type\", \"text\", \"id\", \"emoji\", \"formControlName\", \"emoji\", \"maxlength\", \"5\"], [1, \"form-group\", \"name-input\"], [\"for\", \"name\"], [\"type\", \"text\", \"id\", \"name\", \"formControlName\", \"name\", \"placeholder\", \"Enter quest name\"], [\"class\", \"error-message\", 4, \"ngIf\"], [1, \"form-group\"], [\"for\", \"description\"], [\"id\", \"description\", \"formControlName\", \"description\", \"placeholder\", \"Enter quest description\"], [\"for\", \"category\"], [\"id\", \"category\", \"formControlName\", \"category\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-group\", \"goal-input\"], [\"for\", \"goal_value\"], [\"type\", \"number\", \"id\", \"goal_value\", \"formControlName\", \"goal_value\", \"min\", \"1\"], [\"for\", \"goal_unit\"], [\"id\", \"goal_unit\", \"formControlName\", \"goal_unit\"], [\"type\", \"submit\", 1, \"submit-btn\", 3, \"disabled\"], [1, \"error-message\"], [3, \"value\"], [1, \"button-group\"], [1, \"import-btn\", 3, \"click\", \"disabled\"], [1, \"supabase-btn\", 3, \"click\"], [\"for\", \"group-emoji\"], [\"type\", \"text\", \"id\", \"group-emoji\", \"formControlName\", \"emoji\", \"maxlength\", \"5\"], [\"for\", \"group-name\"], [\"type\", \"text\", \"id\", \"group-name\", \"formControlName\", \"name\", \"placeholder\", \"Enter quest name\"], [\"for\", \"group-description\"], [\"id\", \"group-description\", \"formControlName\", \"description\", \"placeholder\", \"Enter quest description\"], [\"for\", \"group-category\"], [\"id\", \"group-category\", \"formControlName\", \"category\"], [\"for\", \"group-goal_value\"], [\"type\", \"number\", \"id\", \"group-goal_value\", \"formControlName\", \"goal_value\", \"min\", \"1\"], [\"for\", \"group-goal_unit\"], [\"id\", \"group-goal_unit\", \"formControlName\", \"goal_unit\"]],\n  template: function ImportSideQuestsPage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\")(2, \"div\", 1);\n      i0.ɵɵelement(3, \"img\", 2);\n      i0.ɵɵelementStart(4, \"span\");\n      i0.ɵɵtext(5, \"Upshift\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(6, \"h1\");\n      i0.ɵɵtext(7, \"Side Quests Management\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(8, \"section\", 3)(9, \"p\");\n      i0.ɵɵtext(10);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(11, \"div\", 4)(12, \"button\", 5);\n      i0.ɵɵlistener(\"click\", function ImportSideQuestsPage_Template_button_click_12_listener() {\n        return ctx.switchTab(\"user\");\n      });\n      i0.ɵɵtext(13, \"User Side Quests\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(14, \"button\", 5);\n      i0.ɵɵlistener(\"click\", function ImportSideQuestsPage_Template_button_click_14_listener() {\n        return ctx.switchTab(\"group\");\n      });\n      i0.ɵɵtext(15, \"Group Side Quests\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(16, ImportSideQuestsPage_div_16_Template, 21, 7, \"div\", 6)(17, ImportSideQuestsPage_div_17_Template, 21, 7, \"div\", 6);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(10);\n      i0.ɵɵtextInterpolate1(\"Status: \", ctx.importStatus || \"Ready\", \"\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵclassProp(\"active\", ctx.activeTab === \"user\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵclassProp(\"active\", ctx.activeTab === \"group\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"user\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"group\");\n    }\n  },\n  dependencies: [IonicModule, CommonModule, i1.NgForOf, i1.NgIf, FormsModule, i2.ɵNgNoValidate, i2.NgSelectOption, i2.ɵNgSelectMultipleOption, i2.DefaultValueAccessor, i2.NumberValueAccessor, i2.SelectControlValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.MaxLengthValidator, i2.MinValidator, ReactiveFormsModule, i2.FormGroupDirective, i2.FormControlName],\n  styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%];\\n\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\tvar __webpack_modules__ = ({\\n\\n\\n 987:\\n\\n\\n\\n\\n\\n (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/sass-loader/dist/cjs.js):\\\\nexpected \\\\\\\"}\\\\\\\".\\\\n    \\u2577\\\\n151 \\u2502 }\\\\r\\\\n    \\u2502  ^\\\\n    \\u2575\\\\n  src\\\\\\\\app\\\\\\\\pages\\\\\\\\import-sidequests\\\\\\\\import-sidequests.page.scss 151:2  root stylesheet\\\");\\n\\n\\n })\\n\\n\\n \\t})[_ngcontent-%COMP%];\\n\\n\\n\\n \\t\\n\\n \\t//[_ngcontent-%COMP%]   startup\\n\\n[_ngcontent-%COMP%]   //[_ngcontent-%COMP%]   Load[_ngcontent-%COMP%]   entry[_ngcontent-%COMP%]   module[_ngcontent-%COMP%]   and[_ngcontent-%COMP%]   return[_ngcontent-%COMP%]   exports\\n\\n[_ngcontent-%COMP%]   //[_ngcontent-%COMP%]   This[_ngcontent-%COMP%]   entry[_ngcontent-%COMP%]   module[_ngcontent-%COMP%]   doesn't[_ngcontent-%COMP%]   tell[_ngcontent-%COMP%]   about[_ngcontent-%COMP%]   it's[_ngcontent-%COMP%]   top-level[_ngcontent-%COMP%]   declarations[_ngcontent-%COMP%]   so[_ngcontent-%COMP%]   it[_ngcontent-%COMP%]   can't[_ngcontent-%COMP%]   be[_ngcontent-%COMP%]   inlined\\n\\n[_ngcontent-%COMP%]   var[_ngcontent-%COMP%]   __webpack_exports__[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] {};\\n\\n \\t__webpack_modules__[987]();\\n\\n \\tresource = __webpack_exports__;\\n\\n \\t\\n\\n })()\\n;\"]\n});", "map": {"version": 3, "names": ["inject", "CommonModule", "FormsModule", "ReactiveFormsModule", "FormBuilder", "Validators", "IonicModule", "HttpClient", "SupabaseService", "AdminService", "i0", "ɵɵelementStart", "ɵɵlistener", "ImportSideQuestsPage_div_16_div_5_div_1_button_18_Template_button_click_0_listener", "ɵɵrestoreView", "_r5", "quest_r3", "ɵɵnextContext", "$implicit", "ctx_r3", "ɵɵresetView", "deleteSideQuest", "id", "ɵɵtext", "ɵɵelementEnd", "ImportSideQuestsPage_div_16_div_5_div_1_Template_button_click_16_listener", "_r2", "toggleSideQuestActive", "ɵɵtemplate", "ImportSideQuestsPage_div_16_div_5_div_1_button_18_Template", "ɵɵadvance", "ɵɵtextInterpolate", "emoji", "name", "description", "ɵɵtextInterpolate1", "category", "ɵɵtextInterpolate2", "goal_value", "goal_unit", "ɵɵclassProp", "active", "ɵɵproperty", "ImportSideQuestsPage_div_16_div_5_div_1_Template", "existingSideQuests", "category_r7", "unit_r8", "ImportSideQuestsPage_div_16_div_13_Template_form_ngSubmit_1_listener", "_r6", "addSideQuest", "ɵɵelement", "ImportSideQuestsPage_div_16_div_13_div_11_Template", "ImportSideQuestsPage_div_16_div_13_div_16_Template", "ImportSideQuestsPage_div_16_div_13_option_22_Template", "ImportSideQuestsPage_div_16_div_13_option_31_Template", "sideQuestForm", "tmp_3_0", "get", "invalid", "touched", "tmp_4_0", "categories", "goalUnits", "isImporting", "quest_r10", "jsonSideQuests", "length", "ImportSideQuestsPage_div_16_div_20_div_8_div_3_Template", "ImportSideQuestsPage_div_16_div_20_div_8_p_4_Template", "slice", "ImportSideQuestsPage_div_16_div_20_Template_button_click_4_listener", "_r9", "importJsonSideQuests", "ImportSideQuestsPage_div_16_div_20_Template_button_click_6_listener", "openSupabaseConsole", "ImportSideQuestsPage_div_16_div_20_div_8_Template", "ImportSideQuestsPage_div_16_div_5_Template", "ImportSideQuestsPage_div_16_div_6_Template", "ImportSideQuestsPage_div_16_Template_button_click_11_listener", "_r1", "toggleAddForm", "ImportSideQuestsPage_div_16_div_13_Template", "ImportSideQuestsPage_div_16_Template_button_click_18_listener", "toggleJsonImport", "ImportSideQuestsPage_div_16_div_20_Template", "showAddForm", "showJsonImport", "ImportSideQuestsPage_div_17_div_5_div_1_button_18_Template_button_click_0_listener", "_r14", "quest_r13", "deleteGroupSideQuest", "ImportSideQuestsPage_div_17_div_5_div_1_Template_button_click_16_listener", "_r12", "toggleGroupSideQuestActive", "ImportSideQuestsPage_div_17_div_5_div_1_button_18_Template", "ImportSideQuestsPage_div_17_div_5_div_1_Template", "existingGroupSideQuests", "category_r16", "unit_r17", "ImportSideQuestsPage_div_17_div_13_Template_form_ngSubmit_1_listener", "_r15", "addGroupSideQuest", "ImportSideQuestsPage_div_17_div_13_div_11_Template", "ImportSideQuestsPage_div_17_div_13_div_16_Template", "ImportSideQuestsPage_div_17_div_13_option_22_Template", "ImportSideQuestsPage_div_17_div_13_option_31_Template", "groupSideQuestForm", "quest_r19", "jsonGroupSideQuests", "ImportSideQuestsPage_div_17_div_20_div_8_div_3_Template", "ImportSideQuestsPage_div_17_div_20_div_8_p_4_Template", "ImportSideQuestsPage_div_17_div_20_Template_button_click_4_listener", "_r18", "importJsonGroupSideQuests", "ImportSideQuestsPage_div_17_div_20_Template_button_click_6_listener", "ImportSideQuestsPage_div_17_div_20_div_8_Template", "ImportSideQuestsPage_div_17_div_5_Template", "ImportSideQuestsPage_div_17_div_6_Template", "ImportSideQuestsPage_div_17_Template_button_click_11_listener", "_r11", "toggleGroupAddForm", "ImportSideQuestsPage_div_17_div_13_Template", "ImportSideQuestsPage_div_17_Template_button_click_18_listener", "toggleGroupJsonImport", "ImportSideQuestsPage_div_17_div_20_Template", "showGroupAddForm", "showGroupJsonImport", "ImportSideQuestsPage", "constructor", "importStatus", "isAdmin", "activeTab", "http", "supabaseService", "adminService", "fb", "group", "required", "min", "ngOnInit", "subscribe", "fetchExistingSideQuests", "fetchExistingGroupSideQuests", "resetForm", "resetGroupForm", "reset", "switchTab", "tab", "window", "open", "_this", "_asyncToGenerator", "data", "error", "getClient", "from", "select", "order", "ascending", "message", "fetchJsonSideQuests", "next", "_this2", "sideQuestsToImport", "map", "quest", "questWithoutId", "insert", "_this3", "valid", "newSideQuest", "value", "_this4", "update", "eq", "questId", "_this5", "confirm", "delete", "_this6", "fetchJsonGroupSideQuests", "_this7", "groupSideQuestsToImport", "_this8", "newGroupSideQuest", "_this9", "_this10", "selectors", "decls", "vars", "consts", "template", "ImportSideQuestsPage_Template", "rf", "ctx", "ImportSideQuestsPage_Template_button_click_12_listener", "ImportSideQuestsPage_Template_button_click_14_listener", "ImportSideQuestsPage_div_16_Template", "ImportSideQuestsPage_div_17_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "ɵNgNoValidate", "NgSelectOption", "ɵNgSelectMultipleOption", "DefaultValueAccessor", "NumberValueAccessor", "SelectControlValueAccessor", "NgControlStatus", "NgControlStatusGroup", "MaxLengthValidator", "MinValidator", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\import-sidequests\\import-sidequests.page.ts", "C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\import-sidequests\\import-sidequests.page.html"], "sourcesContent": ["import { Component, OnInit, inject } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule, FormGroup, FormBuilder, Validators } from '@angular/forms';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { DailySideQuestPool } from '../../models/sidequest.model';\r\nimport { GroupSideQuestPool } from '../../models/group-sidequest.model';\r\nimport { SupabaseService } from '../../services/supabase.service';\r\nimport { AdminService } from '../../services/admin.service';\r\n\r\n\r\n@Component({\r\n  selector: 'app-import-sidequests',\r\n  templateUrl: './import-sidequests.page.html',\r\n  styleUrls: ['./import-sidequests.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule, FormsModule, ReactiveFormsModule]\r\n})\r\nexport class ImportSideQuestsPage implements OnInit {\r\n  importStatus: string = '';\r\n  isImporting: boolean = false;\r\n  isAdmin: boolean = false;\r\n\r\n  existingSideQuests: DailySideQuestPool[] = [];\r\n  jsonSideQuests: DailySideQuestPool[] = [];\r\n  showJsonImport: boolean = false;\r\n  showAddForm: boolean = false;\r\n\r\n  existingGroupSideQuests: GroupSideQuestPool[] = [];\r\n  jsonGroupSideQuests: GroupSideQuestPool[] = [];\r\n  showGroupJsonImport: boolean = false;\r\n  showGroupAddForm: boolean = false;\r\n  groupSideQuestForm: FormGroup;\r\n\r\n  activeTab: string = 'user'; \n\r\n  sideQuestForm: FormGroup;\r\n\r\n  categories: string[] = ['money', 'health', 'strength', 'knowledge'];\r\n  goalUnits: string[] = ['count', 'steps', 'm', 'km', 'sec', 'min', 'hr', 'Cal', 'g', 'mg', 'l', 'pages', 'books', '%', '€', '$', '£'];\r\n\r\n  private http = inject(HttpClient);\r\n  private supabaseService = inject(SupabaseService);\r\n  private adminService = inject(AdminService);\r\n  private fb = inject(FormBuilder);\r\n\r\n  constructor() {\r\n    this.sideQuestForm = this.fb.group({\r\n      name: ['', [Validators.required]],\r\n      description: ['', [Validators.required]],\r\n      goal_value: [1, [Validators.required, Validators.min(1)]],\r\n      category: ['health', [Validators.required]],\r\n      goal_unit: ['count', [Validators.required]],\r\n      emoji: ['🎯', [Validators.required]]\r\n    });\r\n\r\n    this.groupSideQuestForm = this.fb.group({\r\n      name: ['', [Validators.required]],\r\n      description: ['', [Validators.required]],\r\n      goal_value: [1, [Validators.required, Validators.min(1)]],\r\n      category: ['health', [Validators.required]],\r\n      goal_unit: ['count', [Validators.required]],\r\n      emoji: ['🎯', [Validators.required]]\r\n    });\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.adminService.isAdmin().subscribe(isAdmin => {\r\n      this.isAdmin = isAdmin;\r\n      if (isAdmin) {\r\n        this.fetchExistingSideQuests();\r\n        this.fetchExistingGroupSideQuests();\r\n      } else {\r\n        this.importStatus = 'You need admin privileges to manage side quests.';\r\n      }\r\n    });\r\n\r\n    this.resetForm();\r\n    this.resetGroupForm();\r\n  }\r\n\r\n  resetForm() {\r\n    this.sideQuestForm.reset({\r\n      name: '',\r\n      description: '',\r\n      goal_value: 1,\r\n      category: 'health',\r\n      goal_unit: 'count',\r\n      emoji: '🎯'\r\n    });\r\n  }\r\n\r\n  resetGroupForm() {\r\n    this.groupSideQuestForm.reset({\r\n      name: '',\r\n      description: '',\r\n      goal_value: 1,\r\n      category: 'health',\r\n      goal_unit: 'count',\r\n      emoji: '🎯'\r\n    });\r\n  }\r\n\r\n  switchTab(tab: string) {\r\n    this.activeTab = tab;\r\n    this.importStatus = '';\r\n  }\r\n\r\n  openSupabaseConsole() {\r\n    window.open('https://app.supabase.com/', '_blank');\r\n  }\r\n\r\n  async fetchExistingSideQuests() {\r\n    this.importStatus = 'Fetching existing side quests from Supabase...';\r\n\r\n    try {\r\n      const { data, error } = await this.supabaseService.getClient()\r\n        .from('daily_sidequest_pool')\r\n        .select('*')\r\n        .order('id', { ascending: true });\r\n\r\n      if (error) {\r\n        this.importStatus = `Error fetching existing side quests: ${error.message}`;\r\n        return;\r\n      }\r\n\r\n      if (data && data.length > 0) {\r\n        this.existingSideQuests = data;\r\n        this.importStatus = `Found ${data.length} existing side quests in Supabase.`;\r\n      } else {\r\n        this.importStatus = 'No existing side quests found in Supabase.';\r\n      }\r\n    } catch (error: any) {\r\n      this.importStatus = `Error fetching existing side quests: ${error.message}`;\r\n    }\r\n  }\r\n\r\n  toggleJsonImport() {\r\n    this.showJsonImport = !this.showJsonImport;\r\n    if (this.showJsonImport) {\r\n      this.fetchJsonSideQuests();\r\n    }\r\n  }\r\n\r\n  toggleAddForm() {\r\n    this.showAddForm = !this.showAddForm;\r\n  }\r\n\r\n  fetchJsonSideQuests() {\r\n    this.importStatus = 'Fetching side quests from JSON...';\r\n\r\n    this.http.get<DailySideQuestPool[]>('assets/data/sidequest-pool.json').subscribe({\r\n      next: (data) => {\r\n        if (!data || data.length === 0) {\r\n          this.importStatus = 'No side quests found in JSON file';\r\n          return;\r\n        }\r\n\r\n        this.jsonSideQuests = data;\r\n        this.importStatus = `Found ${data.length} side quests in JSON file. Ready to import.`;\r\n      },\r\n      error: (error) => {\r\n        this.importStatus = `Error fetching side quests JSON: ${error.message}`;\r\n      }\r\n    });\r\n  }\r\n\r\n  async importJsonSideQuests() {\r\n    if (this.jsonSideQuests.length === 0) {\r\n      this.importStatus = 'No side quests to import from JSON';\r\n      return;\r\n    }\r\n\r\n    this.isImporting = true;\r\n    this.importStatus = 'Importing side quests from JSON...';\r\n\r\n    try {\r\n      const sideQuestsToImport = this.jsonSideQuests.map(quest => {\r\n        const { id, ...questWithoutId } = quest;\r\n        return {\r\n          ...questWithoutId,\r\n          active: true\r\n        };\r\n      });\r\n\r\n\r\n      const { error } = await this.supabaseService.getClient()\r\n        .from('daily_sidequest_pool')\r\n        .insert(sideQuestsToImport);\r\n\r\n      if (error) {\r\n        this.importStatus = `Error importing side quests: ${error.message}`;\r\n        this.isImporting = false;\r\n        return;\r\n      }\r\n\r\n      this.importStatus = 'Side quests imported successfully';\r\n      this.isImporting = false;\r\n\r\n      this.fetchExistingSideQuests();\r\n    } catch (error: any) {\r\n      this.importStatus = `Error importing side quests: ${error.message}`;\r\n      this.isImporting = false;\r\n    }\r\n  }\r\n\r\n  async addSideQuest() {\r\n    if (!this.sideQuestForm.valid) {\r\n      this.importStatus = 'Please fill out all required fields correctly';\r\n      return;\r\n    }\r\n\r\n    this.isImporting = true;\r\n    this.importStatus = 'Adding new side quest...';\r\n\r\n    try {\r\n      const newSideQuest = {\r\n        ...this.sideQuestForm.value,\r\n        active: true\r\n      };\r\n\r\n\r\n      const { error } = await this.supabaseService.getClient()\r\n        .from('daily_sidequest_pool')\r\n        .insert([newSideQuest]);\r\n\r\n      if (error) {\r\n        this.importStatus = `Error adding side quest: ${error.message}`;\r\n        this.isImporting = false;\r\n        return;\r\n      }\r\n\r\n      this.importStatus = 'Side quest added successfully';\r\n\r\n      this.resetForm();\r\n\r\n      this.fetchExistingSideQuests();\r\n    } catch (error: any) {\r\n      this.importStatus = `Error adding side quest: ${error.message}`;\r\n    } finally {\r\n      this.isImporting = false;\r\n    }\r\n  }\r\n\r\n  async toggleSideQuestActive(quest: DailySideQuestPool) {\r\n    try {\r\n      const { error } = await this.supabaseService.getClient()\r\n        .from('daily_sidequest_pool')\r\n        .update({ active: !quest.active })\r\n        .eq('id', quest.id);\r\n\r\n      if (error) {\r\n        this.importStatus = `Error updating side quest: ${error.message}`;\r\n        return;\r\n      }\r\n\r\n      this.importStatus = 'Side quest updated successfully';\r\n\r\n      quest.active = !quest.active;\r\n    } catch (error: any) {\r\n      this.importStatus = `Error updating side quest: ${error.message}`;\r\n    }\r\n  }\r\n\r\n  async deleteSideQuest(questId: string | undefined) {\r\n    if (!questId) {\r\n      this.importStatus = 'Cannot delete quest: Invalid quest ID';\r\n      return;\r\n    }\r\n\r\n    if (!confirm('Are you sure you want to delete this side quest?')) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const { error } = await this.supabaseService.getClient()\r\n        .from('daily_sidequest_pool')\r\n        .delete()\r\n        .eq('id', questId);\r\n\r\n      if (error) {\r\n        this.importStatus = `Error deleting side quest: ${error.message}`;\r\n        return;\r\n      }\r\n\r\n      this.importStatus = 'Side quest deleted successfully';\r\n\r\n      this.fetchExistingSideQuests();\r\n    } catch (error: any) {\r\n      this.importStatus = `Error deleting side quest: ${error.message}`;\r\n    }\r\n  }\r\n\r\n\r\n  async fetchExistingGroupSideQuests() {\r\n    this.importStatus = 'Fetching existing group side quests from Supabase...';\r\n\r\n    try {\r\n      const { data, error } = await this.supabaseService.getClient()\r\n        .from('group_sidequest_pool')\r\n        .select('*')\r\n        .order('id', { ascending: true });\r\n\r\n      if (error) {\r\n        this.importStatus = `Error fetching existing group side quests: ${error.message}`;\r\n        return;\r\n      }\r\n\r\n      if (data && data.length > 0) {\r\n        this.existingGroupSideQuests = data;\r\n        this.importStatus = `Found ${data.length} existing group side quests in Supabase.`;\r\n      } else {\r\n        this.importStatus = 'No existing group side quests found in Supabase.';\r\n      }\r\n    } catch (error: any) {\r\n      this.importStatus = `Error fetching existing group side quests: ${error.message}`;\r\n    }\r\n  }\r\n\r\n  toggleGroupJsonImport() {\r\n    this.showGroupJsonImport = !this.showGroupJsonImport;\r\n    if (this.showGroupJsonImport) {\r\n      this.fetchJsonGroupSideQuests();\r\n    }\r\n  }\r\n\r\n  toggleGroupAddForm() {\r\n    this.showGroupAddForm = !this.showGroupAddForm;\r\n  }\r\n\r\n  fetchJsonGroupSideQuests() {\r\n    this.importStatus = 'Fetching group side quests from JSON...';\r\n\r\n    this.http.get<GroupSideQuestPool[]>('assets/data/group-sidequest-pool.json').subscribe({\r\n      next: (data) => {\r\n        if (!data || data.length === 0) {\r\n          this.importStatus = 'No group side quests found in JSON file';\r\n          return;\r\n        }\r\n\r\n        this.jsonGroupSideQuests = data;\r\n        this.importStatus = `Found ${data.length} group side quests in JSON file. Ready to import.`;\r\n      },\r\n      error: (error) => {\r\n        this.importStatus = `Error fetching group side quests JSON: ${error.message}`;\r\n      }\r\n    });\r\n  }\r\n\r\n  async importJsonGroupSideQuests() {\r\n    if (this.jsonGroupSideQuests.length === 0) {\r\n      this.importStatus = 'No group side quests to import from JSON';\r\n      return;\r\n    }\r\n\r\n    this.isImporting = true;\r\n    this.importStatus = 'Importing group side quests from JSON...';\r\n\r\n    try {\r\n      const groupSideQuestsToImport = this.jsonGroupSideQuests.map(quest => {\r\n        const { id, ...questWithoutId } = quest;\r\n        return {\r\n          ...questWithoutId,\r\n          active: true\r\n        };\r\n      });\r\n\r\n\r\n      const { error } = await this.supabaseService.getClient()\r\n        .from('group_sidequest_pool')\r\n        .insert(groupSideQuestsToImport);\r\n\r\n      if (error) {\r\n        this.importStatus = `Error importing group side quests: ${error.message}`;\r\n        this.isImporting = false;\r\n        return;\r\n      }\r\n\r\n      this.importStatus = 'Group side quests imported successfully';\r\n      this.isImporting = false;\r\n\r\n      this.fetchExistingGroupSideQuests();\r\n    } catch (error: any) {\r\n      this.importStatus = `Error importing group side quests: ${error.message}`;\r\n      this.isImporting = false;\r\n    }\r\n  }\r\n\r\n  async addGroupSideQuest() {\r\n    if (!this.groupSideQuestForm.valid) {\r\n      this.importStatus = 'Please fill out all required fields correctly';\r\n      return;\r\n    }\r\n\r\n    this.isImporting = true;\r\n    this.importStatus = 'Adding new group side quest...';\r\n\r\n    try {\r\n      const newGroupSideQuest = {\r\n        ...this.groupSideQuestForm.value,\r\n        active: true\r\n      };\r\n\r\n\r\n      const { error } = await this.supabaseService.getClient()\r\n        .from('group_sidequest_pool')\r\n        .insert([newGroupSideQuest]);\r\n\r\n      if (error) {\r\n        this.importStatus = `Error adding group side quest: ${error.message}`;\r\n        this.isImporting = false;\r\n        return;\r\n      }\r\n\r\n      this.importStatus = 'Group side quest added successfully';\r\n\r\n      this.resetGroupForm();\r\n\r\n      this.fetchExistingGroupSideQuests();\r\n    } catch (error: any) {\r\n      this.importStatus = `Error adding group side quest: ${error.message}`;\r\n    } finally {\r\n      this.isImporting = false;\r\n    }\r\n  }\r\n\r\n  async toggleGroupSideQuestActive(quest: GroupSideQuestPool) {\r\n    try {\r\n      const { error } = await this.supabaseService.getClient()\r\n        .from('group_sidequest_pool')\r\n        .update({ active: !quest.active })\r\n        .eq('id', quest.id);\r\n\r\n      if (error) {\r\n        this.importStatus = `Error updating group side quest: ${error.message}`;\r\n        return;\r\n      }\r\n\r\n      this.importStatus = 'Group side quest updated successfully';\r\n\r\n      quest.active = !quest.active;\r\n    } catch (error: any) {\r\n      this.importStatus = `Error updating group side quest: ${error.message}`;\r\n    }\r\n  }\r\n\r\n  async deleteGroupSideQuest(questId: string) {\r\n    if (!questId) {\r\n      this.importStatus = 'Cannot delete quest: Invalid quest ID';\r\n      return;\r\n    }\r\n\r\n    if (!confirm('Are you sure you want to delete this group side quest?')) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const { error } = await this.supabaseService.getClient()\r\n        .from('group_sidequest_pool')\r\n        .delete()\r\n        .eq('id', questId);\r\n\r\n      if (error) {\r\n        this.importStatus = `Error deleting group side quest: ${error.message}`;\r\n        return;\r\n      }\r\n\r\n      this.importStatus = 'Group side quest deleted successfully';\r\n\r\n      this.fetchExistingGroupSideQuests();\r\n    } catch (error: any) {\r\n      this.importStatus = `Error deleting group side quest: ${error.message}`;\r\n    }\r\n  }\r\n}\r\n", "<div class=\"container\">\r\n  <header>\r\n    <div class=\"logo\">\r\n      <img src=\"assets/images/upshift_icon_mini.svg\" alt=\"Upshift\">\r\n      <span>Upshift</span>\r\n    </div>\r\n    <h1>Side Quests Management</h1>\r\n  </header>\r\n\r\n  <section class=\"status-section\">\r\n    <p>Status: {{ importStatus || 'Ready' }}</p>\r\n  </section>\r\n\r\n  <!-- Tab Navigation -->\r\n  <div class=\"tab-navigation\">\r\n    <button [class.active]=\"activeTab === 'user'\" (click)=\"switchTab('user')\">User Side Quests</button>\r\n    <button [class.active]=\"activeTab === 'group'\" (click)=\"switchTab('group')\">Group Side Quests</button>\r\n  </div>\r\n\r\n  <!-- User Side Quests Tab -->\r\n  <div *ngIf=\"activeTab === 'user'\">\r\n    <!-- Existing Side Quests Section -->\r\n    <section class=\"existing-quests-section\">\r\n      <div class=\"section-header\">\r\n        <h2>Existing User Side Quests ({{ existingSideQuests.length }})</h2>\r\n      </div>\r\n\r\n      <div class=\"side-quests-list\" *ngIf=\"existingSideQuests.length > 0\">\r\n        <div class=\"side-quest-item\" *ngFor=\"let quest of existingSideQuests\">\r\n          <div class=\"quest-emoji\">{{ quest.emoji }}</div>\r\n          <div class=\"quest-details\">\r\n            <h4>{{ quest.name }}</h4>\r\n            <p>{{ quest.description }}</p>\r\n            <div class=\"quest-meta\">\r\n              <span>Category: {{ quest.category }}</span>\r\n              <span>Goal: {{ quest.goal_value }} {{ quest.goal_unit }}</span>\r\n              <span [class.active-status]=\"quest.active\" [class.inactive-status]=\"!quest.active\">\r\n                Status: {{ quest.active ? 'Active' : 'Inactive' }}\r\n              </span>\r\n            </div>\r\n          </div>\r\n          <div class=\"quest-actions\">\r\n            <button class=\"action-btn toggle-btn\" (click)=\"toggleSideQuestActive(quest)\">\r\n              {{ quest.active ? 'Deactivate' : 'Activate' }}\r\n            </button>\r\n            <button class=\"action-btn delete-btn\" (click)=\"deleteSideQuest(quest.id!)\" *ngIf=\"quest.id\">Delete</button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div *ngIf=\"existingSideQuests.length === 0\" class=\"empty-state\">\r\n        <p>No user side quests found in the database.</p>\r\n      </div>\r\n    </section>\r\n\r\n    <!-- Add New Side Quest Section -->\r\n    <section class=\"add-quest-section\">\r\n      <div class=\"section-header\">\r\n        <h2>Add New User Side Quest</h2>\r\n        <button class=\"toggle-btn\" (click)=\"toggleAddForm()\">\r\n          {{ showAddForm ? 'Hide Form' : 'Show Form' }}\r\n        </button>\r\n      </div>\r\n\r\n      <div class=\"add-form\" *ngIf=\"showAddForm\">\r\n        <form [formGroup]=\"sideQuestForm\" (ngSubmit)=\"addSideQuest()\">\r\n          <div class=\"form-row\">\r\n            <div class=\"form-group emoji-input\">\r\n              <label for=\"emoji\">Emoji</label>\r\n              <input type=\"text\" id=\"emoji\" formControlName=\"emoji\" maxlength=\"5\">\r\n            </div>\r\n            <div class=\"form-group name-input\">\r\n              <label for=\"name\">Name</label>\r\n              <input type=\"text\" id=\"name\" formControlName=\"name\" placeholder=\"Enter quest name\">\r\n              <div class=\"error-message\" *ngIf=\"sideQuestForm.get('name')?.invalid && sideQuestForm.get('name')?.touched\">\r\n                Name is required\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"form-group\">\r\n            <label for=\"description\">Description</label>\r\n            <textarea id=\"description\" formControlName=\"description\" placeholder=\"Enter quest description\"></textarea>\r\n            <div class=\"error-message\" *ngIf=\"sideQuestForm.get('description')?.invalid && sideQuestForm.get('description')?.touched\">\r\n              Description is required\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"form-row\">\r\n            <div class=\"form-group\">\r\n              <label for=\"category\">Category</label>\r\n              <select id=\"category\" formControlName=\"category\">\r\n                <option *ngFor=\"let category of categories\" [value]=\"category\">{{ category }}</option>\r\n              </select>\r\n            </div>\r\n\r\n            <div class=\"form-group goal-input\">\r\n              <label for=\"goal_value\">Goal Value</label>\r\n              <input type=\"number\" id=\"goal_value\" formControlName=\"goal_value\" min=\"1\">\r\n            </div>\r\n\r\n            <div class=\"form-group\">\r\n              <label for=\"goal_unit\">Goal Unit</label>\r\n              <select id=\"goal_unit\" formControlName=\"goal_unit\">\r\n                <option *ngFor=\"let unit of goalUnits\" [value]=\"unit\">{{ unit }}</option>\r\n              </select>\r\n            </div>\r\n          </div>\r\n\r\n          <button type=\"submit\" class=\"submit-btn\" [disabled]=\"sideQuestForm.invalid || isImporting\">\r\n            {{ isImporting ? 'Adding...' : 'Add Side Quest' }}\r\n          </button>\r\n        </form>\r\n      </div>\r\n    </section>\r\n\r\n    <!-- Import from JSON Section -->\r\n    <section class=\"import-section\">\r\n      <div class=\"section-header\">\r\n        <h2>Import from JSON</h2>\r\n        <button class=\"toggle-btn\" (click)=\"toggleJsonImport()\">\r\n          {{ showJsonImport ? 'Hide JSON Import' : 'Show JSON Import' }}\r\n        </button>\r\n      </div>\r\n\r\n      <div *ngIf=\"showJsonImport\">\r\n        <p>Import side quests from the JSON file to Supabase.</p>\r\n\r\n        <div class=\"button-group\">\r\n          <button class=\"import-btn\" (click)=\"importJsonSideQuests()\" [disabled]=\"isImporting || jsonSideQuests.length === 0\">\r\n            {{ isImporting ? 'Importing...' : 'Import Side Quests' }}\r\n          </button>\r\n          <button class=\"supabase-btn\" (click)=\"openSupabaseConsole()\">\r\n            Open Supabase Console\r\n          </button>\r\n        </div>\r\n\r\n        <div class=\"side-quests-list\" *ngIf=\"jsonSideQuests.length > 0\">\r\n          <h3>Side Quests to Import ({{ jsonSideQuests.length }})</h3>\r\n          <div class=\"side-quest-item\" *ngFor=\"let quest of jsonSideQuests.slice(0, 5)\">\r\n            <div class=\"quest-emoji\">{{ quest.emoji }}</div>\r\n            <div class=\"quest-details\">\r\n              <h4>{{ quest.name }}</h4>\r\n              <p>{{ quest.description }}</p>\r\n              <div class=\"quest-meta\">\r\n                <span>Category: {{ quest.category }}</span>\r\n                <span>Goal: {{ quest.goal_value }} {{ quest.goal_unit }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <p *ngIf=\"jsonSideQuests.length > 5\">...and {{ jsonSideQuests.length - 5 }} more</p>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </div>\r\n\r\n  <!-- Group Side Quests Tab -->\r\n  <div *ngIf=\"activeTab === 'group'\">\r\n    <!-- Existing Group Side Quests Section -->\r\n    <section class=\"existing-quests-section\">\r\n      <div class=\"section-header\">\r\n        <h2>Existing Group Side Quests ({{ existingGroupSideQuests.length }})</h2>\r\n      </div>\r\n\r\n      <div class=\"side-quests-list\" *ngIf=\"existingGroupSideQuests.length > 0\">\r\n        <div class=\"side-quest-item\" *ngFor=\"let quest of existingGroupSideQuests\">\r\n          <div class=\"quest-emoji\">{{ quest.emoji }}</div>\r\n          <div class=\"quest-details\">\r\n            <h4>{{ quest.name }}</h4>\r\n            <p>{{ quest.description }}</p>\r\n            <div class=\"quest-meta\">\r\n              <span>Category: {{ quest.category }}</span>\r\n              <span>Goal: {{ quest.goal_value }} {{ quest.goal_unit }}</span>\r\n              <span [class.active-status]=\"quest.active\" [class.inactive-status]=\"!quest.active\">\r\n                Status: {{ quest.active ? 'Active' : 'Inactive' }}\r\n              </span>\r\n            </div>\r\n          </div>\r\n          <div class=\"quest-actions\">\r\n            <button class=\"action-btn toggle-btn\" (click)=\"toggleGroupSideQuestActive(quest)\">\r\n              {{ quest.active ? 'Deactivate' : 'Activate' }}\r\n            </button>\r\n            <button class=\"action-btn delete-btn\" (click)=\"deleteGroupSideQuest(quest.id)\" *ngIf=\"quest.id\">Delete</button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div *ngIf=\"existingGroupSideQuests.length === 0\" class=\"empty-state\">\r\n        <p>No group side quests found in the database.</p>\r\n      </div>\r\n    </section>\r\n\r\n    <!-- Add New Group Side Quest Section -->\r\n    <section class=\"add-quest-section\">\r\n      <div class=\"section-header\">\r\n        <h2>Add New Group Side Quest</h2>\r\n        <button class=\"toggle-btn\" (click)=\"toggleGroupAddForm()\">\r\n          {{ showGroupAddForm ? 'Hide Form' : 'Show Form' }}\r\n        </button>\r\n      </div>\r\n\r\n      <div class=\"add-form\" *ngIf=\"showGroupAddForm\">\r\n        <form [formGroup]=\"groupSideQuestForm\" (ngSubmit)=\"addGroupSideQuest()\">\r\n          <div class=\"form-row\">\r\n            <div class=\"form-group emoji-input\">\r\n              <label for=\"group-emoji\">Emoji</label>\r\n              <input type=\"text\" id=\"group-emoji\" formControlName=\"emoji\" maxlength=\"5\">\r\n            </div>\r\n            <div class=\"form-group name-input\">\r\n              <label for=\"group-name\">Name</label>\r\n              <input type=\"text\" id=\"group-name\" formControlName=\"name\" placeholder=\"Enter quest name\">\r\n              <div class=\"error-message\" *ngIf=\"groupSideQuestForm.get('name')?.invalid && groupSideQuestForm.get('name')?.touched\">\r\n                Name is required\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"form-group\">\r\n            <label for=\"group-description\">Description</label>\r\n            <textarea id=\"group-description\" formControlName=\"description\" placeholder=\"Enter quest description\"></textarea>\r\n            <div class=\"error-message\" *ngIf=\"groupSideQuestForm.get('description')?.invalid && groupSideQuestForm.get('description')?.touched\">\r\n              Description is required\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"form-row\">\r\n            <div class=\"form-group\">\r\n              <label for=\"group-category\">Category</label>\r\n              <select id=\"group-category\" formControlName=\"category\">\r\n                <option *ngFor=\"let category of categories\" [value]=\"category\">{{ category }}</option>\r\n              </select>\r\n            </div>\r\n\r\n            <div class=\"form-group goal-input\">\r\n              <label for=\"group-goal_value\">Goal Value</label>\r\n              <input type=\"number\" id=\"group-goal_value\" formControlName=\"goal_value\" min=\"1\">\r\n            </div>\r\n\r\n            <div class=\"form-group\">\r\n              <label for=\"group-goal_unit\">Goal Unit</label>\r\n              <select id=\"group-goal_unit\" formControlName=\"goal_unit\">\r\n                <option *ngFor=\"let unit of goalUnits\" [value]=\"unit\">{{ unit }}</option>\r\n              </select>\r\n            </div>\r\n          </div>\r\n\r\n          <button type=\"submit\" class=\"submit-btn\" [disabled]=\"groupSideQuestForm.invalid || isImporting\">\r\n            {{ isImporting ? 'Adding...' : 'Add Group Side Quest' }}\r\n          </button>\r\n        </form>\r\n      </div>\r\n    </section>\r\n\r\n    <!-- Import Group Side Quests Section -->\r\n    <section class=\"import-section\">\r\n      <div class=\"section-header\">\r\n        <h2>Import Group Side Quests</h2>\r\n        <button class=\"toggle-btn\" (click)=\"toggleGroupJsonImport()\">\r\n          {{ showGroupJsonImport ? 'Hide JSON Import' : 'Show JSON Import' }}\r\n        </button>\r\n      </div>\r\n\r\n      <div *ngIf=\"showGroupJsonImport\">\r\n        <p>Import group side quests from the JSON file to Supabase.</p>\r\n\r\n        <div class=\"button-group\">\r\n          <button class=\"import-btn\" (click)=\"importJsonGroupSideQuests()\" [disabled]=\"isImporting || jsonGroupSideQuests.length === 0\">\r\n            {{ isImporting ? 'Importing...' : 'Import Group Side Quests' }}\r\n          </button>\r\n          <button class=\"supabase-btn\" (click)=\"openSupabaseConsole()\">\r\n            Open Supabase Console\r\n          </button>\r\n        </div>\r\n\r\n        <div class=\"side-quests-list\" *ngIf=\"jsonGroupSideQuests.length > 0\">\r\n          <h3>Group Side Quests to Import ({{ jsonGroupSideQuests.length }})</h3>\r\n          <div class=\"side-quest-item\" *ngFor=\"let quest of jsonGroupSideQuests.slice(0, 5)\">\r\n            <div class=\"quest-emoji\">{{ quest.emoji }}</div>\r\n            <div class=\"quest-details\">\r\n              <h4>{{ quest.name }}</h4>\r\n              <p>{{ quest.description }}</p>\r\n              <div class=\"quest-meta\">\r\n                <span>Category: {{ quest.category }}</span>\r\n                <span>Goal: {{ quest.goal_value }} {{ quest.goal_unit }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <p *ngIf=\"jsonGroupSideQuests.length > 5\">...and {{ jsonGroupSideQuests.length - 5 }} more</p>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;AAAA,SAA4BA,MAAM,QAAQ,eAAe;AACzD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,EAAaC,WAAW,EAAEC,UAAU,QAAQ,gBAAgB;AACrG,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,UAAU,QAAQ,sBAAsB;AAGjD,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,YAAY,QAAQ,8BAA8B;;;;;;;ICqC/CC,EAAA,CAAAC,cAAA,iBAA4F;IAAtDD,EAAA,CAAAE,UAAA,mBAAAC,mFAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,QAAA,GAAAN,EAAA,CAAAO,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASD,MAAA,CAAAE,eAAA,CAAAL,QAAA,CAAAM,EAAA,CAA0B;IAAA,EAAC;IAAkBZ,EAAA,CAAAa,MAAA,aAAM;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IAhB7Gd,EADF,CAAAC,cAAA,cAAsE,cAC3C;IAAAD,EAAA,CAAAa,MAAA,GAAiB;IAAAb,EAAA,CAAAc,YAAA,EAAM;IAE9Cd,EADF,CAAAC,cAAA,cAA2B,SACrB;IAAAD,EAAA,CAAAa,MAAA,GAAgB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACzBd,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAa,MAAA,GAAuB;IAAAb,EAAA,CAAAc,YAAA,EAAI;IAE5Bd,EADF,CAAAC,cAAA,cAAwB,WAChB;IAAAD,EAAA,CAAAa,MAAA,IAA8B;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAC3Cd,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,IAAkD;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAC/Dd,EAAA,CAAAC,cAAA,YAAmF;IACjFD,EAAA,CAAAa,MAAA,IACF;IAEJb,EAFI,CAAAc,YAAA,EAAO,EACH,EACF;IAEJd,EADF,CAAAC,cAAA,eAA2B,kBACoD;IAAvCD,EAAA,CAAAE,UAAA,mBAAAa,0EAAA;MAAA,MAAAT,QAAA,GAAAN,EAAA,CAAAI,aAAA,CAAAY,GAAA,EAAAR,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASD,MAAA,CAAAQ,qBAAA,CAAAX,QAAA,CAA4B;IAAA,EAAC;IAC1EN,EAAA,CAAAa,MAAA,IACF;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAkB,UAAA,KAAAC,0DAAA,qBAA4F;IAEhGnB,EADE,CAAAc,YAAA,EAAM,EACF;;;;IAlBqBd,EAAA,CAAAoB,SAAA,GAAiB;IAAjBpB,EAAA,CAAAqB,iBAAA,CAAAf,QAAA,CAAAgB,KAAA,CAAiB;IAEpCtB,EAAA,CAAAoB,SAAA,GAAgB;IAAhBpB,EAAA,CAAAqB,iBAAA,CAAAf,QAAA,CAAAiB,IAAA,CAAgB;IACjBvB,EAAA,CAAAoB,SAAA,GAAuB;IAAvBpB,EAAA,CAAAqB,iBAAA,CAAAf,QAAA,CAAAkB,WAAA,CAAuB;IAElBxB,EAAA,CAAAoB,SAAA,GAA8B;IAA9BpB,EAAA,CAAAyB,kBAAA,eAAAnB,QAAA,CAAAoB,QAAA,KAA8B;IAC9B1B,EAAA,CAAAoB,SAAA,GAAkD;IAAlDpB,EAAA,CAAA2B,kBAAA,WAAArB,QAAA,CAAAsB,UAAA,OAAAtB,QAAA,CAAAuB,SAAA,KAAkD;IAClD7B,EAAA,CAAAoB,SAAA,EAAoC;IAACpB,EAArC,CAAA8B,WAAA,kBAAAxB,QAAA,CAAAyB,MAAA,CAAoC,qBAAAzB,QAAA,CAAAyB,MAAA,CAAwC;IAChF/B,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAyB,kBAAA,cAAAnB,QAAA,CAAAyB,MAAA,8BACF;IAKA/B,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAAyB,kBAAA,MAAAnB,QAAA,CAAAyB,MAAA,kCACF;IAC4E/B,EAAA,CAAAoB,SAAA,EAAc;IAAdpB,EAAA,CAAAgC,UAAA,SAAA1B,QAAA,CAAAM,EAAA,CAAc;;;;;IAlBhGZ,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAAkB,UAAA,IAAAe,gDAAA,oBAAsE;IAoBxEjC,EAAA,CAAAc,YAAA,EAAM;;;;IApB2Cd,EAAA,CAAAoB,SAAA,EAAqB;IAArBpB,EAAA,CAAAgC,UAAA,YAAAvB,MAAA,CAAAyB,kBAAA,CAAqB;;;;;IAsBpElC,EADF,CAAAC,cAAA,cAAiE,QAC5D;IAAAD,EAAA,CAAAa,MAAA,iDAA0C;IAC/Cb,EAD+C,CAAAc,YAAA,EAAI,EAC7C;;;;;IAsBEd,EAAA,CAAAC,cAAA,cAA4G;IAC1GD,EAAA,CAAAa,MAAA,yBACF;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;;IAORd,EAAA,CAAAC,cAAA,cAA0H;IACxHD,EAAA,CAAAa,MAAA,gCACF;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;;IAOFd,EAAA,CAAAC,cAAA,iBAA+D;IAAAD,EAAA,CAAAa,MAAA,GAAc;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;IAA1Cd,EAAA,CAAAgC,UAAA,UAAAG,WAAA,CAAkB;IAACnC,EAAA,CAAAoB,SAAA,EAAc;IAAdpB,EAAA,CAAAqB,iBAAA,CAAAc,WAAA,CAAc;;;;;IAY7EnC,EAAA,CAAAC,cAAA,iBAAsD;IAAAD,EAAA,CAAAa,MAAA,GAAU;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;IAAlCd,EAAA,CAAAgC,UAAA,UAAAI,OAAA,CAAc;IAACpC,EAAA,CAAAoB,SAAA,EAAU;IAAVpB,EAAA,CAAAqB,iBAAA,CAAAe,OAAA,CAAU;;;;;;IAvCxEpC,EADF,CAAAC,cAAA,cAA0C,eACsB;IAA5BD,EAAA,CAAAE,UAAA,sBAAAmC,qEAAA;MAAArC,EAAA,CAAAI,aAAA,CAAAkC,GAAA;MAAA,MAAA7B,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAYD,MAAA,CAAA8B,YAAA,EAAc;IAAA,EAAC;IAGvDvC,EAFJ,CAAAC,cAAA,cAAsB,cACgB,gBACf;IAAAD,EAAA,CAAAa,MAAA,YAAK;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAChCd,EAAA,CAAAwC,SAAA,gBAAoE;IACtExC,EAAA,CAAAc,YAAA,EAAM;IAEJd,EADF,CAAAC,cAAA,cAAmC,gBACf;IAAAD,EAAA,CAAAa,MAAA,WAAI;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAC9Bd,EAAA,CAAAwC,SAAA,iBAAmF;IACnFxC,EAAA,CAAAkB,UAAA,KAAAuB,kDAAA,kBAA4G;IAIhHzC,EADE,CAAAc,YAAA,EAAM,EACF;IAGJd,EADF,CAAAC,cAAA,eAAwB,iBACG;IAAAD,EAAA,CAAAa,MAAA,mBAAW;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAC5Cd,EAAA,CAAAwC,SAAA,oBAA0G;IAC1GxC,EAAA,CAAAkB,UAAA,KAAAwB,kDAAA,kBAA0H;IAG5H1C,EAAA,CAAAc,YAAA,EAAM;IAIFd,EAFJ,CAAAC,cAAA,eAAsB,eACI,iBACA;IAAAD,EAAA,CAAAa,MAAA,gBAAQ;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACtCd,EAAA,CAAAC,cAAA,kBAAiD;IAC/CD,EAAA,CAAAkB,UAAA,KAAAyB,qDAAA,qBAA+D;IAEnE3C,EADE,CAAAc,YAAA,EAAS,EACL;IAGJd,EADF,CAAAC,cAAA,eAAmC,iBACT;IAAAD,EAAA,CAAAa,MAAA,kBAAU;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAC1Cd,EAAA,CAAAwC,SAAA,iBAA0E;IAC5ExC,EAAA,CAAAc,YAAA,EAAM;IAGJd,EADF,CAAAC,cAAA,eAAwB,iBACC;IAAAD,EAAA,CAAAa,MAAA,iBAAS;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACxCd,EAAA,CAAAC,cAAA,kBAAmD;IACjDD,EAAA,CAAAkB,UAAA,KAAA0B,qDAAA,qBAAsD;IAG5D5C,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;IAENd,EAAA,CAAAC,cAAA,kBAA2F;IACzFD,EAAA,CAAAa,MAAA,IACF;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACJ,EACH;;;;;;IAhDEd,EAAA,CAAAoB,SAAA,EAA2B;IAA3BpB,EAAA,CAAAgC,UAAA,cAAAvB,MAAA,CAAAoC,aAAA,CAA2B;IASC7C,EAAA,CAAAoB,SAAA,IAA8E;IAA9EpB,EAAA,CAAAgC,UAAA,WAAAc,OAAA,GAAArC,MAAA,CAAAoC,aAAA,CAAAE,GAAA,2BAAAD,OAAA,CAAAE,OAAA,OAAAF,OAAA,GAAArC,MAAA,CAAAoC,aAAA,CAAAE,GAAA,2BAAAD,OAAA,CAAAG,OAAA,EAA8E;IAShFjD,EAAA,CAAAoB,SAAA,GAA4F;IAA5FpB,EAAA,CAAAgC,UAAA,WAAAkB,OAAA,GAAAzC,MAAA,CAAAoC,aAAA,CAAAE,GAAA,kCAAAG,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAAzC,MAAA,CAAAoC,aAAA,CAAAE,GAAA,kCAAAG,OAAA,CAAAD,OAAA,EAA4F;IASvFjD,EAAA,CAAAoB,SAAA,GAAa;IAAbpB,EAAA,CAAAgC,UAAA,YAAAvB,MAAA,CAAA0C,UAAA,CAAa;IAYjBnD,EAAA,CAAAoB,SAAA,GAAY;IAAZpB,EAAA,CAAAgC,UAAA,YAAAvB,MAAA,CAAA2C,SAAA,CAAY;IAKFpD,EAAA,CAAAoB,SAAA,EAAiD;IAAjDpB,EAAA,CAAAgC,UAAA,aAAAvB,MAAA,CAAAoC,aAAA,CAAAG,OAAA,IAAAvC,MAAA,CAAA4C,WAAA,CAAiD;IACxFrD,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAyB,kBAAA,MAAAhB,MAAA,CAAA4C,WAAA,uCACF;;;;;IA6BErD,EADF,CAAAC,cAAA,cAA8E,cACnD;IAAAD,EAAA,CAAAa,MAAA,GAAiB;IAAAb,EAAA,CAAAc,YAAA,EAAM;IAE9Cd,EADF,CAAAC,cAAA,cAA2B,SACrB;IAAAD,EAAA,CAAAa,MAAA,GAAgB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACzBd,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAa,MAAA,GAAuB;IAAAb,EAAA,CAAAc,YAAA,EAAI;IAE5Bd,EADF,CAAAC,cAAA,cAAwB,WAChB;IAAAD,EAAA,CAAAa,MAAA,IAA8B;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAC3Cd,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,IAAkD;IAG9Db,EAH8D,CAAAc,YAAA,EAAO,EAC3D,EACF,EACF;;;;IATqBd,EAAA,CAAAoB,SAAA,GAAiB;IAAjBpB,EAAA,CAAAqB,iBAAA,CAAAiC,SAAA,CAAAhC,KAAA,CAAiB;IAEpCtB,EAAA,CAAAoB,SAAA,GAAgB;IAAhBpB,EAAA,CAAAqB,iBAAA,CAAAiC,SAAA,CAAA/B,IAAA,CAAgB;IACjBvB,EAAA,CAAAoB,SAAA,GAAuB;IAAvBpB,EAAA,CAAAqB,iBAAA,CAAAiC,SAAA,CAAA9B,WAAA,CAAuB;IAElBxB,EAAA,CAAAoB,SAAA,GAA8B;IAA9BpB,EAAA,CAAAyB,kBAAA,eAAA6B,SAAA,CAAA5B,QAAA,KAA8B;IAC9B1B,EAAA,CAAAoB,SAAA,GAAkD;IAAlDpB,EAAA,CAAA2B,kBAAA,WAAA2B,SAAA,CAAA1B,UAAA,OAAA0B,SAAA,CAAAzB,SAAA,KAAkD;;;;;IAI9D7B,EAAA,CAAAC,cAAA,QAAqC;IAAAD,EAAA,CAAAa,MAAA,GAA2C;IAAAb,EAAA,CAAAc,YAAA,EAAI;;;;IAA/Cd,EAAA,CAAAoB,SAAA,EAA2C;IAA3CpB,EAAA,CAAAyB,kBAAA,YAAAhB,MAAA,CAAA8C,cAAA,CAAAC,MAAA,cAA2C;;;;;IAZhFxD,EADF,CAAAC,cAAA,cAAgE,SAC1D;IAAAD,EAAA,CAAAa,MAAA,GAAmD;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAY5Dd,EAXA,CAAAkB,UAAA,IAAAuC,uDAAA,mBAA8E,IAAAC,qDAAA,eAWzC;IACvC1D,EAAA,CAAAc,YAAA,EAAM;;;;IAbAd,EAAA,CAAAoB,SAAA,GAAmD;IAAnDpB,EAAA,CAAAyB,kBAAA,4BAAAhB,MAAA,CAAA8C,cAAA,CAAAC,MAAA,MAAmD;IACRxD,EAAA,CAAAoB,SAAA,EAA6B;IAA7BpB,EAAA,CAAAgC,UAAA,YAAAvB,MAAA,CAAA8C,cAAA,CAAAI,KAAA,OAA6B;IAWxE3D,EAAA,CAAAoB,SAAA,EAA+B;IAA/BpB,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAA8C,cAAA,CAAAC,MAAA,KAA+B;;;;;;IAxBrCxD,EADF,CAAAC,cAAA,UAA4B,QACvB;IAAAD,EAAA,CAAAa,MAAA,yDAAkD;IAAAb,EAAA,CAAAc,YAAA,EAAI;IAGvDd,EADF,CAAAC,cAAA,cAA0B,iBAC4F;IAAzFD,EAAA,CAAAE,UAAA,mBAAA0D,oEAAA;MAAA5D,EAAA,CAAAI,aAAA,CAAAyD,GAAA;MAAA,MAAApD,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASD,MAAA,CAAAqD,oBAAA,EAAsB;IAAA,EAAC;IACzD9D,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,iBAA6D;IAAhCD,EAAA,CAAAE,UAAA,mBAAA6D,oEAAA;MAAA/D,EAAA,CAAAI,aAAA,CAAAyD,GAAA;MAAA,MAAApD,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASD,MAAA,CAAAuD,mBAAA,EAAqB;IAAA,EAAC;IAC1DhE,EAAA,CAAAa,MAAA,8BACF;IACFb,EADE,CAAAc,YAAA,EAAS,EACL;IAENd,EAAA,CAAAkB,UAAA,IAAA+C,iDAAA,iBAAgE;IAelEjE,EAAA,CAAAc,YAAA,EAAM;;;;IAvB0Dd,EAAA,CAAAoB,SAAA,GAAuD;IAAvDpB,EAAA,CAAAgC,UAAA,aAAAvB,MAAA,CAAA4C,WAAA,IAAA5C,MAAA,CAAA8C,cAAA,CAAAC,MAAA,OAAuD;IACjHxD,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAyB,kBAAA,MAAAhB,MAAA,CAAA4C,WAAA,8CACF;IAM6BrD,EAAA,CAAAoB,SAAA,GAA+B;IAA/BpB,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAA8C,cAAA,CAAAC,MAAA,KAA+B;;;;;;IAhH9DxD,EAJN,CAAAC,cAAA,UAAkC,iBAES,aACX,SACtB;IAAAD,EAAA,CAAAa,MAAA,GAA2D;IACjEb,EADiE,CAAAc,YAAA,EAAK,EAChE;IAwBNd,EAtBA,CAAAkB,UAAA,IAAAgD,0CAAA,iBAAoE,IAAAC,0CAAA,kBAsBH;IAGnEnE,EAAA,CAAAc,YAAA,EAAU;IAKNd,EAFJ,CAAAC,cAAA,kBAAmC,aACL,SACtB;IAAAD,EAAA,CAAAa,MAAA,+BAAuB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAChCd,EAAA,CAAAC,cAAA,kBAAqD;IAA1BD,EAAA,CAAAE,UAAA,mBAAAkE,8DAAA;MAAApE,EAAA,CAAAI,aAAA,CAAAiE,GAAA;MAAA,MAAA5D,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASD,MAAA,CAAA6D,aAAA,EAAe;IAAA,EAAC;IAClDtE,EAAA,CAAAa,MAAA,IACF;IACFb,EADE,CAAAc,YAAA,EAAS,EACL;IAENd,EAAA,CAAAkB,UAAA,KAAAqD,2CAAA,mBAA0C;IAkD5CvE,EAAA,CAAAc,YAAA,EAAU;IAKNd,EAFJ,CAAAC,cAAA,mBAAgC,cACF,UACtB;IAAAD,EAAA,CAAAa,MAAA,wBAAgB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACzBd,EAAA,CAAAC,cAAA,kBAAwD;IAA7BD,EAAA,CAAAE,UAAA,mBAAAsE,8DAAA;MAAAxE,EAAA,CAAAI,aAAA,CAAAiE,GAAA;MAAA,MAAA5D,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASD,MAAA,CAAAgE,gBAAA,EAAkB;IAAA,EAAC;IACrDzE,EAAA,CAAAa,MAAA,IACF;IACFb,EADE,CAAAc,YAAA,EAAS,EACL;IAENd,EAAA,CAAAkB,UAAA,KAAAwD,2CAAA,iBAA4B;IA6BhC1E,EADE,CAAAc,YAAA,EAAU,EACN;;;;IAjIId,EAAA,CAAAoB,SAAA,GAA2D;IAA3DpB,EAAA,CAAAyB,kBAAA,gCAAAhB,MAAA,CAAAyB,kBAAA,CAAAsB,MAAA,MAA2D;IAGlCxD,EAAA,CAAAoB,SAAA,EAAmC;IAAnCpB,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAAyB,kBAAA,CAAAsB,MAAA,KAAmC;IAsB5DxD,EAAA,CAAAoB,SAAA,EAAqC;IAArCpB,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAAyB,kBAAA,CAAAsB,MAAA,OAAqC;IAUvCxD,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAAyB,kBAAA,MAAAhB,MAAA,CAAAkE,WAAA,kCACF;IAGqB3E,EAAA,CAAAoB,SAAA,EAAiB;IAAjBpB,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAAkE,WAAA,CAAiB;IAyDpC3E,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAAyB,kBAAA,MAAAhB,MAAA,CAAAmE,cAAA,gDACF;IAGI5E,EAAA,CAAAoB,SAAA,EAAoB;IAApBpB,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAAmE,cAAA,CAAoB;;;;;;IAyDpB5E,EAAA,CAAAC,cAAA,iBAAgG;IAA1DD,EAAA,CAAAE,UAAA,mBAAA2E,mFAAA;MAAA7E,EAAA,CAAAI,aAAA,CAAA0E,IAAA;MAAA,MAAAC,SAAA,GAAA/E,EAAA,CAAAO,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASD,MAAA,CAAAuE,oBAAA,CAAAD,SAAA,CAAAnE,EAAA,CAA8B;IAAA,EAAC;IAAkBZ,EAAA,CAAAa,MAAA,aAAM;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IAhBjHd,EADF,CAAAC,cAAA,cAA2E,cAChD;IAAAD,EAAA,CAAAa,MAAA,GAAiB;IAAAb,EAAA,CAAAc,YAAA,EAAM;IAE9Cd,EADF,CAAAC,cAAA,cAA2B,SACrB;IAAAD,EAAA,CAAAa,MAAA,GAAgB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACzBd,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAa,MAAA,GAAuB;IAAAb,EAAA,CAAAc,YAAA,EAAI;IAE5Bd,EADF,CAAAC,cAAA,cAAwB,WAChB;IAAAD,EAAA,CAAAa,MAAA,IAA8B;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAC3Cd,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,IAAkD;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAC/Dd,EAAA,CAAAC,cAAA,YAAmF;IACjFD,EAAA,CAAAa,MAAA,IACF;IAEJb,EAFI,CAAAc,YAAA,EAAO,EACH,EACF;IAEJd,EADF,CAAAC,cAAA,eAA2B,kBACyD;IAA5CD,EAAA,CAAAE,UAAA,mBAAA+E,0EAAA;MAAA,MAAAF,SAAA,GAAA/E,EAAA,CAAAI,aAAA,CAAA8E,IAAA,EAAA1E,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASD,MAAA,CAAA0E,0BAAA,CAAAJ,SAAA,CAAiC;IAAA,EAAC;IAC/E/E,EAAA,CAAAa,MAAA,IACF;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAkB,UAAA,KAAAkE,0DAAA,qBAAgG;IAEpGpF,EADE,CAAAc,YAAA,EAAM,EACF;;;;IAlBqBd,EAAA,CAAAoB,SAAA,GAAiB;IAAjBpB,EAAA,CAAAqB,iBAAA,CAAA0D,SAAA,CAAAzD,KAAA,CAAiB;IAEpCtB,EAAA,CAAAoB,SAAA,GAAgB;IAAhBpB,EAAA,CAAAqB,iBAAA,CAAA0D,SAAA,CAAAxD,IAAA,CAAgB;IACjBvB,EAAA,CAAAoB,SAAA,GAAuB;IAAvBpB,EAAA,CAAAqB,iBAAA,CAAA0D,SAAA,CAAAvD,WAAA,CAAuB;IAElBxB,EAAA,CAAAoB,SAAA,GAA8B;IAA9BpB,EAAA,CAAAyB,kBAAA,eAAAsD,SAAA,CAAArD,QAAA,KAA8B;IAC9B1B,EAAA,CAAAoB,SAAA,GAAkD;IAAlDpB,EAAA,CAAA2B,kBAAA,WAAAoD,SAAA,CAAAnD,UAAA,OAAAmD,SAAA,CAAAlD,SAAA,KAAkD;IAClD7B,EAAA,CAAAoB,SAAA,EAAoC;IAACpB,EAArC,CAAA8B,WAAA,kBAAAiD,SAAA,CAAAhD,MAAA,CAAoC,qBAAAgD,SAAA,CAAAhD,MAAA,CAAwC;IAChF/B,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAyB,kBAAA,cAAAsD,SAAA,CAAAhD,MAAA,8BACF;IAKA/B,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAAyB,kBAAA,MAAAsD,SAAA,CAAAhD,MAAA,kCACF;IACgF/B,EAAA,CAAAoB,SAAA,EAAc;IAAdpB,EAAA,CAAAgC,UAAA,SAAA+C,SAAA,CAAAnE,EAAA,CAAc;;;;;IAlBpGZ,EAAA,CAAAC,cAAA,cAAyE;IACvED,EAAA,CAAAkB,UAAA,IAAAmE,gDAAA,oBAA2E;IAoB7ErF,EAAA,CAAAc,YAAA,EAAM;;;;IApB2Cd,EAAA,CAAAoB,SAAA,EAA0B;IAA1BpB,EAAA,CAAAgC,UAAA,YAAAvB,MAAA,CAAA6E,uBAAA,CAA0B;;;;;IAsBzEtF,EADF,CAAAC,cAAA,cAAsE,QACjE;IAAAD,EAAA,CAAAa,MAAA,kDAA2C;IAChDb,EADgD,CAAAc,YAAA,EAAI,EAC9C;;;;;IAsBEd,EAAA,CAAAC,cAAA,cAAsH;IACpHD,EAAA,CAAAa,MAAA,yBACF;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;;IAORd,EAAA,CAAAC,cAAA,cAAoI;IAClID,EAAA,CAAAa,MAAA,gCACF;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;;IAOFd,EAAA,CAAAC,cAAA,iBAA+D;IAAAD,EAAA,CAAAa,MAAA,GAAc;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;IAA1Cd,EAAA,CAAAgC,UAAA,UAAAuD,YAAA,CAAkB;IAACvF,EAAA,CAAAoB,SAAA,EAAc;IAAdpB,EAAA,CAAAqB,iBAAA,CAAAkE,YAAA,CAAc;;;;;IAY7EvF,EAAA,CAAAC,cAAA,iBAAsD;IAAAD,EAAA,CAAAa,MAAA,GAAU;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;IAAlCd,EAAA,CAAAgC,UAAA,UAAAwD,QAAA,CAAc;IAACxF,EAAA,CAAAoB,SAAA,EAAU;IAAVpB,EAAA,CAAAqB,iBAAA,CAAAmE,QAAA,CAAU;;;;;;IAvCxExF,EADF,CAAAC,cAAA,cAA+C,eAC2B;IAAjCD,EAAA,CAAAE,UAAA,sBAAAuF,qEAAA;MAAAzF,EAAA,CAAAI,aAAA,CAAAsF,IAAA;MAAA,MAAAjF,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAYD,MAAA,CAAAkF,iBAAA,EAAmB;IAAA,EAAC;IAGjE3F,EAFJ,CAAAC,cAAA,cAAsB,cACgB,gBACT;IAAAD,EAAA,CAAAa,MAAA,YAAK;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACtCd,EAAA,CAAAwC,SAAA,gBAA0E;IAC5ExC,EAAA,CAAAc,YAAA,EAAM;IAEJd,EADF,CAAAC,cAAA,cAAmC,gBACT;IAAAD,EAAA,CAAAa,MAAA,WAAI;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACpCd,EAAA,CAAAwC,SAAA,iBAAyF;IACzFxC,EAAA,CAAAkB,UAAA,KAAA0E,kDAAA,kBAAsH;IAI1H5F,EADE,CAAAc,YAAA,EAAM,EACF;IAGJd,EADF,CAAAC,cAAA,eAAwB,iBACS;IAAAD,EAAA,CAAAa,MAAA,mBAAW;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAClDd,EAAA,CAAAwC,SAAA,oBAAgH;IAChHxC,EAAA,CAAAkB,UAAA,KAAA2E,kDAAA,kBAAoI;IAGtI7F,EAAA,CAAAc,YAAA,EAAM;IAIFd,EAFJ,CAAAC,cAAA,eAAsB,eACI,iBACM;IAAAD,EAAA,CAAAa,MAAA,gBAAQ;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAC5Cd,EAAA,CAAAC,cAAA,kBAAuD;IACrDD,EAAA,CAAAkB,UAAA,KAAA4E,qDAAA,qBAA+D;IAEnE9F,EADE,CAAAc,YAAA,EAAS,EACL;IAGJd,EADF,CAAAC,cAAA,eAAmC,iBACH;IAAAD,EAAA,CAAAa,MAAA,kBAAU;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAChDd,EAAA,CAAAwC,SAAA,iBAAgF;IAClFxC,EAAA,CAAAc,YAAA,EAAM;IAGJd,EADF,CAAAC,cAAA,eAAwB,iBACO;IAAAD,EAAA,CAAAa,MAAA,iBAAS;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAC9Cd,EAAA,CAAAC,cAAA,kBAAyD;IACvDD,EAAA,CAAAkB,UAAA,KAAA6E,qDAAA,qBAAsD;IAG5D/F,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;IAENd,EAAA,CAAAC,cAAA,kBAAgG;IAC9FD,EAAA,CAAAa,MAAA,IACF;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACJ,EACH;;;;;;IAhDEd,EAAA,CAAAoB,SAAA,EAAgC;IAAhCpB,EAAA,CAAAgC,UAAA,cAAAvB,MAAA,CAAAuF,kBAAA,CAAgC;IASJhG,EAAA,CAAAoB,SAAA,IAAwF;IAAxFpB,EAAA,CAAAgC,UAAA,WAAAc,OAAA,GAAArC,MAAA,CAAAuF,kBAAA,CAAAjD,GAAA,2BAAAD,OAAA,CAAAE,OAAA,OAAAF,OAAA,GAAArC,MAAA,CAAAuF,kBAAA,CAAAjD,GAAA,2BAAAD,OAAA,CAAAG,OAAA,EAAwF;IAS1FjD,EAAA,CAAAoB,SAAA,GAAsG;IAAtGpB,EAAA,CAAAgC,UAAA,WAAAkB,OAAA,GAAAzC,MAAA,CAAAuF,kBAAA,CAAAjD,GAAA,kCAAAG,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAAzC,MAAA,CAAAuF,kBAAA,CAAAjD,GAAA,kCAAAG,OAAA,CAAAD,OAAA,EAAsG;IASjGjD,EAAA,CAAAoB,SAAA,GAAa;IAAbpB,EAAA,CAAAgC,UAAA,YAAAvB,MAAA,CAAA0C,UAAA,CAAa;IAYjBnD,EAAA,CAAAoB,SAAA,GAAY;IAAZpB,EAAA,CAAAgC,UAAA,YAAAvB,MAAA,CAAA2C,SAAA,CAAY;IAKFpD,EAAA,CAAAoB,SAAA,EAAsD;IAAtDpB,EAAA,CAAAgC,UAAA,aAAAvB,MAAA,CAAAuF,kBAAA,CAAAhD,OAAA,IAAAvC,MAAA,CAAA4C,WAAA,CAAsD;IAC7FrD,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAyB,kBAAA,MAAAhB,MAAA,CAAA4C,WAAA,6CACF;;;;;IA6BErD,EADF,CAAAC,cAAA,cAAmF,cACxD;IAAAD,EAAA,CAAAa,MAAA,GAAiB;IAAAb,EAAA,CAAAc,YAAA,EAAM;IAE9Cd,EADF,CAAAC,cAAA,cAA2B,SACrB;IAAAD,EAAA,CAAAa,MAAA,GAAgB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACzBd,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAa,MAAA,GAAuB;IAAAb,EAAA,CAAAc,YAAA,EAAI;IAE5Bd,EADF,CAAAC,cAAA,cAAwB,WAChB;IAAAD,EAAA,CAAAa,MAAA,IAA8B;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAC3Cd,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,IAAkD;IAG9Db,EAH8D,CAAAc,YAAA,EAAO,EAC3D,EACF,EACF;;;;IATqBd,EAAA,CAAAoB,SAAA,GAAiB;IAAjBpB,EAAA,CAAAqB,iBAAA,CAAA4E,SAAA,CAAA3E,KAAA,CAAiB;IAEpCtB,EAAA,CAAAoB,SAAA,GAAgB;IAAhBpB,EAAA,CAAAqB,iBAAA,CAAA4E,SAAA,CAAA1E,IAAA,CAAgB;IACjBvB,EAAA,CAAAoB,SAAA,GAAuB;IAAvBpB,EAAA,CAAAqB,iBAAA,CAAA4E,SAAA,CAAAzE,WAAA,CAAuB;IAElBxB,EAAA,CAAAoB,SAAA,GAA8B;IAA9BpB,EAAA,CAAAyB,kBAAA,eAAAwE,SAAA,CAAAvE,QAAA,KAA8B;IAC9B1B,EAAA,CAAAoB,SAAA,GAAkD;IAAlDpB,EAAA,CAAA2B,kBAAA,WAAAsE,SAAA,CAAArE,UAAA,OAAAqE,SAAA,CAAApE,SAAA,KAAkD;;;;;IAI9D7B,EAAA,CAAAC,cAAA,QAA0C;IAAAD,EAAA,CAAAa,MAAA,GAAgD;IAAAb,EAAA,CAAAc,YAAA,EAAI;;;;IAApDd,EAAA,CAAAoB,SAAA,EAAgD;IAAhDpB,EAAA,CAAAyB,kBAAA,YAAAhB,MAAA,CAAAyF,mBAAA,CAAA1C,MAAA,cAAgD;;;;;IAZ1FxD,EADF,CAAAC,cAAA,cAAqE,SAC/D;IAAAD,EAAA,CAAAa,MAAA,GAA8D;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAYvEd,EAXA,CAAAkB,UAAA,IAAAiF,uDAAA,mBAAmF,IAAAC,qDAAA,eAWzC;IAC5CpG,EAAA,CAAAc,YAAA,EAAM;;;;IAbAd,EAAA,CAAAoB,SAAA,GAA8D;IAA9DpB,EAAA,CAAAyB,kBAAA,kCAAAhB,MAAA,CAAAyF,mBAAA,CAAA1C,MAAA,MAA8D;IACnBxD,EAAA,CAAAoB,SAAA,EAAkC;IAAlCpB,EAAA,CAAAgC,UAAA,YAAAvB,MAAA,CAAAyF,mBAAA,CAAAvC,KAAA,OAAkC;IAW7E3D,EAAA,CAAAoB,SAAA,EAAoC;IAApCpB,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAAyF,mBAAA,CAAA1C,MAAA,KAAoC;;;;;;IAxB1CxD,EADF,CAAAC,cAAA,UAAiC,QAC5B;IAAAD,EAAA,CAAAa,MAAA,+DAAwD;IAAAb,EAAA,CAAAc,YAAA,EAAI;IAG7Dd,EADF,CAAAC,cAAA,cAA0B,iBACsG;IAAnGD,EAAA,CAAAE,UAAA,mBAAAmG,oEAAA;MAAArG,EAAA,CAAAI,aAAA,CAAAkG,IAAA;MAAA,MAAA7F,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASD,MAAA,CAAA8F,yBAAA,EAA2B;IAAA,EAAC;IAC9DvG,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,iBAA6D;IAAhCD,EAAA,CAAAE,UAAA,mBAAAsG,oEAAA;MAAAxG,EAAA,CAAAI,aAAA,CAAAkG,IAAA;MAAA,MAAA7F,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASD,MAAA,CAAAuD,mBAAA,EAAqB;IAAA,EAAC;IAC1DhE,EAAA,CAAAa,MAAA,8BACF;IACFb,EADE,CAAAc,YAAA,EAAS,EACL;IAENd,EAAA,CAAAkB,UAAA,IAAAuF,iDAAA,iBAAqE;IAevEzG,EAAA,CAAAc,YAAA,EAAM;;;;IAvB+Dd,EAAA,CAAAoB,SAAA,GAA4D;IAA5DpB,EAAA,CAAAgC,UAAA,aAAAvB,MAAA,CAAA4C,WAAA,IAAA5C,MAAA,CAAAyF,mBAAA,CAAA1C,MAAA,OAA4D;IAC3HxD,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAyB,kBAAA,MAAAhB,MAAA,CAAA4C,WAAA,oDACF;IAM6BrD,EAAA,CAAAoB,SAAA,GAAoC;IAApCpB,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAAyF,mBAAA,CAAA1C,MAAA,KAAoC;;;;;;IAhHnExD,EAJN,CAAAC,cAAA,UAAmC,iBAEQ,aACX,SACtB;IAAAD,EAAA,CAAAa,MAAA,GAAiE;IACvEb,EADuE,CAAAc,YAAA,EAAK,EACtE;IAwBNd,EAtBA,CAAAkB,UAAA,IAAAwF,0CAAA,iBAAyE,IAAAC,0CAAA,kBAsBH;IAGxE3G,EAAA,CAAAc,YAAA,EAAU;IAKNd,EAFJ,CAAAC,cAAA,kBAAmC,aACL,SACtB;IAAAD,EAAA,CAAAa,MAAA,gCAAwB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACjCd,EAAA,CAAAC,cAAA,kBAA0D;IAA/BD,EAAA,CAAAE,UAAA,mBAAA0G,8DAAA;MAAA5G,EAAA,CAAAI,aAAA,CAAAyG,IAAA;MAAA,MAAApG,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASD,MAAA,CAAAqG,kBAAA,EAAoB;IAAA,EAAC;IACvD9G,EAAA,CAAAa,MAAA,IACF;IACFb,EADE,CAAAc,YAAA,EAAS,EACL;IAENd,EAAA,CAAAkB,UAAA,KAAA6F,2CAAA,mBAA+C;IAkDjD/G,EAAA,CAAAc,YAAA,EAAU;IAKNd,EAFJ,CAAAC,cAAA,mBAAgC,cACF,UACtB;IAAAD,EAAA,CAAAa,MAAA,gCAAwB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACjCd,EAAA,CAAAC,cAAA,kBAA6D;IAAlCD,EAAA,CAAAE,UAAA,mBAAA8G,8DAAA;MAAAhH,EAAA,CAAAI,aAAA,CAAAyG,IAAA;MAAA,MAAApG,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASD,MAAA,CAAAwG,qBAAA,EAAuB;IAAA,EAAC;IAC1DjH,EAAA,CAAAa,MAAA,IACF;IACFb,EADE,CAAAc,YAAA,EAAS,EACL;IAENd,EAAA,CAAAkB,UAAA,KAAAgG,2CAAA,iBAAiC;IA6BrClH,EADE,CAAAc,YAAA,EAAU,EACN;;;;IAjIId,EAAA,CAAAoB,SAAA,GAAiE;IAAjEpB,EAAA,CAAAyB,kBAAA,iCAAAhB,MAAA,CAAA6E,uBAAA,CAAA9B,MAAA,MAAiE;IAGxCxD,EAAA,CAAAoB,SAAA,EAAwC;IAAxCpB,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAA6E,uBAAA,CAAA9B,MAAA,KAAwC;IAsBjExD,EAAA,CAAAoB,SAAA,EAA0C;IAA1CpB,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAA6E,uBAAA,CAAA9B,MAAA,OAA0C;IAU5CxD,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAAyB,kBAAA,MAAAhB,MAAA,CAAA0G,gBAAA,kCACF;IAGqBnH,EAAA,CAAAoB,SAAA,EAAsB;IAAtBpB,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAA0G,gBAAA,CAAsB;IAyDzCnH,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAAyB,kBAAA,MAAAhB,MAAA,CAAA2G,mBAAA,gDACF;IAGIpH,EAAA,CAAAoB,SAAA,EAAyB;IAAzBpB,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAA2G,mBAAA,CAAyB;;;ADlPrC,OAAM,MAAOC,oBAAoB;EA4B/BC,YAAA;IA3BA,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAlE,WAAW,GAAY,KAAK;IAC5B,KAAAmE,OAAO,GAAY,KAAK;IAExB,KAAAtF,kBAAkB,GAAyB,EAAE;IAC7C,KAAAqB,cAAc,GAAyB,EAAE;IACzC,KAAAqB,cAAc,GAAY,KAAK;IAC/B,KAAAD,WAAW,GAAY,KAAK;IAE5B,KAAAW,uBAAuB,GAAyB,EAAE;IAClD,KAAAY,mBAAmB,GAAyB,EAAE;IAC9C,KAAAkB,mBAAmB,GAAY,KAAK;IACpC,KAAAD,gBAAgB,GAAY,KAAK;IAGjC,KAAAM,SAAS,GAAW,MAAM;IAI1B,KAAAtE,UAAU,GAAa,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,CAAC;IACnE,KAAAC,SAAS,GAAa,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAE5H,KAAAsE,IAAI,GAAGpI,MAAM,CAACO,UAAU,CAAC;IACzB,KAAA8H,eAAe,GAAGrI,MAAM,CAACQ,eAAe,CAAC;IACzC,KAAA8H,YAAY,GAAGtI,MAAM,CAACS,YAAY,CAAC;IACnC,KAAA8H,EAAE,GAAGvI,MAAM,CAACI,WAAW,CAAC;IAG9B,IAAI,CAACmD,aAAa,GAAG,IAAI,CAACgF,EAAE,CAACC,KAAK,CAAC;MACjCvG,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC5B,UAAU,CAACoI,QAAQ,CAAC,CAAC;MACjCvG,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC7B,UAAU,CAACoI,QAAQ,CAAC,CAAC;MACxCnG,UAAU,EAAE,CAAC,CAAC,EAAE,CAACjC,UAAU,CAACoI,QAAQ,EAAEpI,UAAU,CAACqI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACzDtG,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC/B,UAAU,CAACoI,QAAQ,CAAC,CAAC;MAC3ClG,SAAS,EAAE,CAAC,OAAO,EAAE,CAAClC,UAAU,CAACoI,QAAQ,CAAC,CAAC;MAC3CzG,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC3B,UAAU,CAACoI,QAAQ,CAAC;KACpC,CAAC;IAEF,IAAI,CAAC/B,kBAAkB,GAAG,IAAI,CAAC6B,EAAE,CAACC,KAAK,CAAC;MACtCvG,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC5B,UAAU,CAACoI,QAAQ,CAAC,CAAC;MACjCvG,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC7B,UAAU,CAACoI,QAAQ,CAAC,CAAC;MACxCnG,UAAU,EAAE,CAAC,CAAC,EAAE,CAACjC,UAAU,CAACoI,QAAQ,EAAEpI,UAAU,CAACqI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACzDtG,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC/B,UAAU,CAACoI,QAAQ,CAAC,CAAC;MAC3ClG,SAAS,EAAE,CAAC,OAAO,EAAE,CAAClC,UAAU,CAACoI,QAAQ,CAAC,CAAC;MAC3CzG,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC3B,UAAU,CAACoI,QAAQ,CAAC;KACpC,CAAC;EACJ;EAEAE,QAAQA,CAAA;IACN,IAAI,CAACL,YAAY,CAACJ,OAAO,EAAE,CAACU,SAAS,CAACV,OAAO,IAAG;MAC9C,IAAI,CAACA,OAAO,GAAGA,OAAO;MACtB,IAAIA,OAAO,EAAE;QACX,IAAI,CAACW,uBAAuB,EAAE;QAC9B,IAAI,CAACC,4BAA4B,EAAE;MACrC,CAAC,MAAM;QACL,IAAI,CAACb,YAAY,GAAG,kDAAkD;MACxE;IACF,CAAC,CAAC;IAEF,IAAI,CAACc,SAAS,EAAE;IAChB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAD,SAASA,CAAA;IACP,IAAI,CAACxF,aAAa,CAAC0F,KAAK,CAAC;MACvBhH,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfI,UAAU,EAAE,CAAC;MACbF,QAAQ,EAAE,QAAQ;MAClBG,SAAS,EAAE,OAAO;MAClBP,KAAK,EAAE;KACR,CAAC;EACJ;EAEAgH,cAAcA,CAAA;IACZ,IAAI,CAACtC,kBAAkB,CAACuC,KAAK,CAAC;MAC5BhH,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfI,UAAU,EAAE,CAAC;MACbF,QAAQ,EAAE,QAAQ;MAClBG,SAAS,EAAE,OAAO;MAClBP,KAAK,EAAE;KACR,CAAC;EACJ;EAEAkH,SAASA,CAACC,GAAW;IACnB,IAAI,CAAChB,SAAS,GAAGgB,GAAG;IACpB,IAAI,CAAClB,YAAY,GAAG,EAAE;EACxB;EAEAvD,mBAAmBA,CAAA;IACjB0E,MAAM,CAACC,IAAI,CAAC,2BAA2B,EAAE,QAAQ,CAAC;EACpD;EAEMR,uBAAuBA,CAAA;IAAA,IAAAS,KAAA;IAAA,OAAAC,iBAAA;MAC3BD,KAAI,CAACrB,YAAY,GAAG,gDAAgD;MAEpE,IAAI;QACF,MAAM;UAAEuB,IAAI;UAAEC;QAAK,CAAE,SAASH,KAAI,CAACjB,eAAe,CAACqB,SAAS,EAAE,CAC3DC,IAAI,CAAC,sBAAsB,CAAC,CAC5BC,MAAM,CAAC,GAAG,CAAC,CACXC,KAAK,CAAC,IAAI,EAAE;UAAEC,SAAS,EAAE;QAAI,CAAE,CAAC;QAEnC,IAAIL,KAAK,EAAE;UACTH,KAAI,CAACrB,YAAY,GAAG,wCAAwCwB,KAAK,CAACM,OAAO,EAAE;UAC3E;QACF;QAEA,IAAIP,IAAI,IAAIA,IAAI,CAACtF,MAAM,GAAG,CAAC,EAAE;UAC3BoF,KAAI,CAAC1G,kBAAkB,GAAG4G,IAAI;UAC9BF,KAAI,CAACrB,YAAY,GAAG,SAASuB,IAAI,CAACtF,MAAM,oCAAoC;QAC9E,CAAC,MAAM;UACLoF,KAAI,CAACrB,YAAY,GAAG,4CAA4C;QAClE;MACF,CAAC,CAAC,OAAOwB,KAAU,EAAE;QACnBH,KAAI,CAACrB,YAAY,GAAG,wCAAwCwB,KAAK,CAACM,OAAO,EAAE;MAC7E;IAAC;EACH;EAEA5E,gBAAgBA,CAAA;IACd,IAAI,CAACG,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,IAAI,IAAI,CAACA,cAAc,EAAE;MACvB,IAAI,CAAC0E,mBAAmB,EAAE;IAC5B;EACF;EAEAhF,aAAaA,CAAA;IACX,IAAI,CAACK,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;EACtC;EAEA2E,mBAAmBA,CAAA;IACjB,IAAI,CAAC/B,YAAY,GAAG,mCAAmC;IAEvD,IAAI,CAACG,IAAI,CAAC3E,GAAG,CAAuB,iCAAiC,CAAC,CAACmF,SAAS,CAAC;MAC/EqB,IAAI,EAAGT,IAAI,IAAI;QACb,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACtF,MAAM,KAAK,CAAC,EAAE;UAC9B,IAAI,CAAC+D,YAAY,GAAG,mCAAmC;UACvD;QACF;QAEA,IAAI,CAAChE,cAAc,GAAGuF,IAAI;QAC1B,IAAI,CAACvB,YAAY,GAAG,SAASuB,IAAI,CAACtF,MAAM,6CAA6C;MACvF,CAAC;MACDuF,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACxB,YAAY,GAAG,oCAAoCwB,KAAK,CAACM,OAAO,EAAE;MACzE;KACD,CAAC;EACJ;EAEMvF,oBAAoBA,CAAA;IAAA,IAAA0F,MAAA;IAAA,OAAAX,iBAAA;MACxB,IAAIW,MAAI,CAACjG,cAAc,CAACC,MAAM,KAAK,CAAC,EAAE;QACpCgG,MAAI,CAACjC,YAAY,GAAG,oCAAoC;QACxD;MACF;MAEAiC,MAAI,CAACnG,WAAW,GAAG,IAAI;MACvBmG,MAAI,CAACjC,YAAY,GAAG,oCAAoC;MAExD,IAAI;QACF,MAAMkC,kBAAkB,GAAGD,MAAI,CAACjG,cAAc,CAACmG,GAAG,CAACC,KAAK,IAAG;UACzD,MAAM;YAAE/I,EAAE;YAAE,GAAGgJ;UAAc,CAAE,GAAGD,KAAK;UACvC,OAAO;YACL,GAAGC,cAAc;YACjB7H,MAAM,EAAE;WACT;QACH,CAAC,CAAC;QAGF,MAAM;UAAEgH;QAAK,CAAE,SAASS,MAAI,CAAC7B,eAAe,CAACqB,SAAS,EAAE,CACrDC,IAAI,CAAC,sBAAsB,CAAC,CAC5BY,MAAM,CAACJ,kBAAkB,CAAC;QAE7B,IAAIV,KAAK,EAAE;UACTS,MAAI,CAACjC,YAAY,GAAG,gCAAgCwB,KAAK,CAACM,OAAO,EAAE;UACnEG,MAAI,CAACnG,WAAW,GAAG,KAAK;UACxB;QACF;QAEAmG,MAAI,CAACjC,YAAY,GAAG,mCAAmC;QACvDiC,MAAI,CAACnG,WAAW,GAAG,KAAK;QAExBmG,MAAI,CAACrB,uBAAuB,EAAE;MAChC,CAAC,CAAC,OAAOY,KAAU,EAAE;QACnBS,MAAI,CAACjC,YAAY,GAAG,gCAAgCwB,KAAK,CAACM,OAAO,EAAE;QACnEG,MAAI,CAACnG,WAAW,GAAG,KAAK;MAC1B;IAAC;EACH;EAEMd,YAAYA,CAAA;IAAA,IAAAuH,MAAA;IAAA,OAAAjB,iBAAA;MAChB,IAAI,CAACiB,MAAI,CAACjH,aAAa,CAACkH,KAAK,EAAE;QAC7BD,MAAI,CAACvC,YAAY,GAAG,+CAA+C;QACnE;MACF;MAEAuC,MAAI,CAACzG,WAAW,GAAG,IAAI;MACvByG,MAAI,CAACvC,YAAY,GAAG,0BAA0B;MAE9C,IAAI;QACF,MAAMyC,YAAY,GAAG;UACnB,GAAGF,MAAI,CAACjH,aAAa,CAACoH,KAAK;UAC3BlI,MAAM,EAAE;SACT;QAGD,MAAM;UAAEgH;QAAK,CAAE,SAASe,MAAI,CAACnC,eAAe,CAACqB,SAAS,EAAE,CACrDC,IAAI,CAAC,sBAAsB,CAAC,CAC5BY,MAAM,CAAC,CAACG,YAAY,CAAC,CAAC;QAEzB,IAAIjB,KAAK,EAAE;UACTe,MAAI,CAACvC,YAAY,GAAG,4BAA4BwB,KAAK,CAACM,OAAO,EAAE;UAC/DS,MAAI,CAACzG,WAAW,GAAG,KAAK;UACxB;QACF;QAEAyG,MAAI,CAACvC,YAAY,GAAG,+BAA+B;QAEnDuC,MAAI,CAACzB,SAAS,EAAE;QAEhByB,MAAI,CAAC3B,uBAAuB,EAAE;MAChC,CAAC,CAAC,OAAOY,KAAU,EAAE;QACnBe,MAAI,CAACvC,YAAY,GAAG,4BAA4BwB,KAAK,CAACM,OAAO,EAAE;MACjE,CAAC,SAAS;QACRS,MAAI,CAACzG,WAAW,GAAG,KAAK;MAC1B;IAAC;EACH;EAEMpC,qBAAqBA,CAAC0I,KAAyB;IAAA,IAAAO,MAAA;IAAA,OAAArB,iBAAA;MACnD,IAAI;QACF,MAAM;UAAEE;QAAK,CAAE,SAASmB,MAAI,CAACvC,eAAe,CAACqB,SAAS,EAAE,CACrDC,IAAI,CAAC,sBAAsB,CAAC,CAC5BkB,MAAM,CAAC;UAAEpI,MAAM,EAAE,CAAC4H,KAAK,CAAC5H;QAAM,CAAE,CAAC,CACjCqI,EAAE,CAAC,IAAI,EAAET,KAAK,CAAC/I,EAAE,CAAC;QAErB,IAAImI,KAAK,EAAE;UACTmB,MAAI,CAAC3C,YAAY,GAAG,8BAA8BwB,KAAK,CAACM,OAAO,EAAE;UACjE;QACF;QAEAa,MAAI,CAAC3C,YAAY,GAAG,iCAAiC;QAErDoC,KAAK,CAAC5H,MAAM,GAAG,CAAC4H,KAAK,CAAC5H,MAAM;MAC9B,CAAC,CAAC,OAAOgH,KAAU,EAAE;QACnBmB,MAAI,CAAC3C,YAAY,GAAG,8BAA8BwB,KAAK,CAACM,OAAO,EAAE;MACnE;IAAC;EACH;EAEM1I,eAAeA,CAAC0J,OAA2B;IAAA,IAAAC,MAAA;IAAA,OAAAzB,iBAAA;MAC/C,IAAI,CAACwB,OAAO,EAAE;QACZC,MAAI,CAAC/C,YAAY,GAAG,uCAAuC;QAC3D;MACF;MAEA,IAAI,CAACgD,OAAO,CAAC,kDAAkD,CAAC,EAAE;QAChE;MACF;MAEA,IAAI;QACF,MAAM;UAAExB;QAAK,CAAE,SAASuB,MAAI,CAAC3C,eAAe,CAACqB,SAAS,EAAE,CACrDC,IAAI,CAAC,sBAAsB,CAAC,CAC5BuB,MAAM,EAAE,CACRJ,EAAE,CAAC,IAAI,EAAEC,OAAO,CAAC;QAEpB,IAAItB,KAAK,EAAE;UACTuB,MAAI,CAAC/C,YAAY,GAAG,8BAA8BwB,KAAK,CAACM,OAAO,EAAE;UACjE;QACF;QAEAiB,MAAI,CAAC/C,YAAY,GAAG,iCAAiC;QAErD+C,MAAI,CAACnC,uBAAuB,EAAE;MAChC,CAAC,CAAC,OAAOY,KAAU,EAAE;QACnBuB,MAAI,CAAC/C,YAAY,GAAG,8BAA8BwB,KAAK,CAACM,OAAO,EAAE;MACnE;IAAC;EACH;EAGMjB,4BAA4BA,CAAA;IAAA,IAAAqC,MAAA;IAAA,OAAA5B,iBAAA;MAChC4B,MAAI,CAAClD,YAAY,GAAG,sDAAsD;MAE1E,IAAI;QACF,MAAM;UAAEuB,IAAI;UAAEC;QAAK,CAAE,SAAS0B,MAAI,CAAC9C,eAAe,CAACqB,SAAS,EAAE,CAC3DC,IAAI,CAAC,sBAAsB,CAAC,CAC5BC,MAAM,CAAC,GAAG,CAAC,CACXC,KAAK,CAAC,IAAI,EAAE;UAAEC,SAAS,EAAE;QAAI,CAAE,CAAC;QAEnC,IAAIL,KAAK,EAAE;UACT0B,MAAI,CAAClD,YAAY,GAAG,8CAA8CwB,KAAK,CAACM,OAAO,EAAE;UACjF;QACF;QAEA,IAAIP,IAAI,IAAIA,IAAI,CAACtF,MAAM,GAAG,CAAC,EAAE;UAC3BiH,MAAI,CAACnF,uBAAuB,GAAGwD,IAAI;UACnC2B,MAAI,CAAClD,YAAY,GAAG,SAASuB,IAAI,CAACtF,MAAM,0CAA0C;QACpF,CAAC,MAAM;UACLiH,MAAI,CAAClD,YAAY,GAAG,kDAAkD;QACxE;MACF,CAAC,CAAC,OAAOwB,KAAU,EAAE;QACnB0B,MAAI,CAAClD,YAAY,GAAG,8CAA8CwB,KAAK,CAACM,OAAO,EAAE;MACnF;IAAC;EACH;EAEApC,qBAAqBA,CAAA;IACnB,IAAI,CAACG,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;IACpD,IAAI,IAAI,CAACA,mBAAmB,EAAE;MAC5B,IAAI,CAACsD,wBAAwB,EAAE;IACjC;EACF;EAEA5D,kBAAkBA,CAAA;IAChB,IAAI,CAACK,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;EAChD;EAEAuD,wBAAwBA,CAAA;IACtB,IAAI,CAACnD,YAAY,GAAG,yCAAyC;IAE7D,IAAI,CAACG,IAAI,CAAC3E,GAAG,CAAuB,uCAAuC,CAAC,CAACmF,SAAS,CAAC;MACrFqB,IAAI,EAAGT,IAAI,IAAI;QACb,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACtF,MAAM,KAAK,CAAC,EAAE;UAC9B,IAAI,CAAC+D,YAAY,GAAG,yCAAyC;UAC7D;QACF;QAEA,IAAI,CAACrB,mBAAmB,GAAG4C,IAAI;QAC/B,IAAI,CAACvB,YAAY,GAAG,SAASuB,IAAI,CAACtF,MAAM,mDAAmD;MAC7F,CAAC;MACDuF,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACxB,YAAY,GAAG,0CAA0CwB,KAAK,CAACM,OAAO,EAAE;MAC/E;KACD,CAAC;EACJ;EAEM9C,yBAAyBA,CAAA;IAAA,IAAAoE,MAAA;IAAA,OAAA9B,iBAAA;MAC7B,IAAI8B,MAAI,CAACzE,mBAAmB,CAAC1C,MAAM,KAAK,CAAC,EAAE;QACzCmH,MAAI,CAACpD,YAAY,GAAG,0CAA0C;QAC9D;MACF;MAEAoD,MAAI,CAACtH,WAAW,GAAG,IAAI;MACvBsH,MAAI,CAACpD,YAAY,GAAG,0CAA0C;MAE9D,IAAI;QACF,MAAMqD,uBAAuB,GAAGD,MAAI,CAACzE,mBAAmB,CAACwD,GAAG,CAACC,KAAK,IAAG;UACnE,MAAM;YAAE/I,EAAE;YAAE,GAAGgJ;UAAc,CAAE,GAAGD,KAAK;UACvC,OAAO;YACL,GAAGC,cAAc;YACjB7H,MAAM,EAAE;WACT;QACH,CAAC,CAAC;QAGF,MAAM;UAAEgH;QAAK,CAAE,SAAS4B,MAAI,CAAChD,eAAe,CAACqB,SAAS,EAAE,CACrDC,IAAI,CAAC,sBAAsB,CAAC,CAC5BY,MAAM,CAACe,uBAAuB,CAAC;QAElC,IAAI7B,KAAK,EAAE;UACT4B,MAAI,CAACpD,YAAY,GAAG,sCAAsCwB,KAAK,CAACM,OAAO,EAAE;UACzEsB,MAAI,CAACtH,WAAW,GAAG,KAAK;UACxB;QACF;QAEAsH,MAAI,CAACpD,YAAY,GAAG,yCAAyC;QAC7DoD,MAAI,CAACtH,WAAW,GAAG,KAAK;QAExBsH,MAAI,CAACvC,4BAA4B,EAAE;MACrC,CAAC,CAAC,OAAOW,KAAU,EAAE;QACnB4B,MAAI,CAACpD,YAAY,GAAG,sCAAsCwB,KAAK,CAACM,OAAO,EAAE;QACzEsB,MAAI,CAACtH,WAAW,GAAG,KAAK;MAC1B;IAAC;EACH;EAEMsC,iBAAiBA,CAAA;IAAA,IAAAkF,MAAA;IAAA,OAAAhC,iBAAA;MACrB,IAAI,CAACgC,MAAI,CAAC7E,kBAAkB,CAAC+D,KAAK,EAAE;QAClCc,MAAI,CAACtD,YAAY,GAAG,+CAA+C;QACnE;MACF;MAEAsD,MAAI,CAACxH,WAAW,GAAG,IAAI;MACvBwH,MAAI,CAACtD,YAAY,GAAG,gCAAgC;MAEpD,IAAI;QACF,MAAMuD,iBAAiB,GAAG;UACxB,GAAGD,MAAI,CAAC7E,kBAAkB,CAACiE,KAAK;UAChClI,MAAM,EAAE;SACT;QAGD,MAAM;UAAEgH;QAAK,CAAE,SAAS8B,MAAI,CAAClD,eAAe,CAACqB,SAAS,EAAE,CACrDC,IAAI,CAAC,sBAAsB,CAAC,CAC5BY,MAAM,CAAC,CAACiB,iBAAiB,CAAC,CAAC;QAE9B,IAAI/B,KAAK,EAAE;UACT8B,MAAI,CAACtD,YAAY,GAAG,kCAAkCwB,KAAK,CAACM,OAAO,EAAE;UACrEwB,MAAI,CAACxH,WAAW,GAAG,KAAK;UACxB;QACF;QAEAwH,MAAI,CAACtD,YAAY,GAAG,qCAAqC;QAEzDsD,MAAI,CAACvC,cAAc,EAAE;QAErBuC,MAAI,CAACzC,4BAA4B,EAAE;MACrC,CAAC,CAAC,OAAOW,KAAU,EAAE;QACnB8B,MAAI,CAACtD,YAAY,GAAG,kCAAkCwB,KAAK,CAACM,OAAO,EAAE;MACvE,CAAC,SAAS;QACRwB,MAAI,CAACxH,WAAW,GAAG,KAAK;MAC1B;IAAC;EACH;EAEM8B,0BAA0BA,CAACwE,KAAyB;IAAA,IAAAoB,MAAA;IAAA,OAAAlC,iBAAA;MACxD,IAAI;QACF,MAAM;UAAEE;QAAK,CAAE,SAASgC,MAAI,CAACpD,eAAe,CAACqB,SAAS,EAAE,CACrDC,IAAI,CAAC,sBAAsB,CAAC,CAC5BkB,MAAM,CAAC;UAAEpI,MAAM,EAAE,CAAC4H,KAAK,CAAC5H;QAAM,CAAE,CAAC,CACjCqI,EAAE,CAAC,IAAI,EAAET,KAAK,CAAC/I,EAAE,CAAC;QAErB,IAAImI,KAAK,EAAE;UACTgC,MAAI,CAACxD,YAAY,GAAG,oCAAoCwB,KAAK,CAACM,OAAO,EAAE;UACvE;QACF;QAEA0B,MAAI,CAACxD,YAAY,GAAG,uCAAuC;QAE3DoC,KAAK,CAAC5H,MAAM,GAAG,CAAC4H,KAAK,CAAC5H,MAAM;MAC9B,CAAC,CAAC,OAAOgH,KAAU,EAAE;QACnBgC,MAAI,CAACxD,YAAY,GAAG,oCAAoCwB,KAAK,CAACM,OAAO,EAAE;MACzE;IAAC;EACH;EAEMrE,oBAAoBA,CAACqF,OAAe;IAAA,IAAAW,OAAA;IAAA,OAAAnC,iBAAA;MACxC,IAAI,CAACwB,OAAO,EAAE;QACZW,OAAI,CAACzD,YAAY,GAAG,uCAAuC;QAC3D;MACF;MAEA,IAAI,CAACgD,OAAO,CAAC,wDAAwD,CAAC,EAAE;QACtE;MACF;MAEA,IAAI;QACF,MAAM;UAAExB;QAAK,CAAE,SAASiC,OAAI,CAACrD,eAAe,CAACqB,SAAS,EAAE,CACrDC,IAAI,CAAC,sBAAsB,CAAC,CAC5BuB,MAAM,EAAE,CACRJ,EAAE,CAAC,IAAI,EAAEC,OAAO,CAAC;QAEpB,IAAItB,KAAK,EAAE;UACTiC,OAAI,CAACzD,YAAY,GAAG,oCAAoCwB,KAAK,CAACM,OAAO,EAAE;UACvE;QACF;QAEA2B,OAAI,CAACzD,YAAY,GAAG,uCAAuC;QAE3DyD,OAAI,CAAC5C,4BAA4B,EAAE;MACrC,CAAC,CAAC,OAAOW,KAAU,EAAE;QACnBiC,OAAI,CAACzD,YAAY,GAAG,oCAAoCwB,KAAK,CAACM,OAAO,EAAE;MACzE;IAAC;EACH;;wBAvcWhC,oBAAoB;;mCAApBA,qBAAoB;AAAA;;QAApBA,qBAAoB;EAAA4D,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MChB7BvL,EAFJ,CAAAC,cAAA,aAAuB,aACb,aACY;MAChBD,EAAA,CAAAwC,SAAA,aAA6D;MAC7DxC,EAAA,CAAAC,cAAA,WAAM;MAAAD,EAAA,CAAAa,MAAA,cAAO;MACfb,EADe,CAAAc,YAAA,EAAO,EAChB;MACNd,EAAA,CAAAC,cAAA,SAAI;MAAAD,EAAA,CAAAa,MAAA,6BAAsB;MAC5Bb,EAD4B,CAAAc,YAAA,EAAK,EACxB;MAGPd,EADF,CAAAC,cAAA,iBAAgC,QAC3B;MAAAD,EAAA,CAAAa,MAAA,IAAqC;MAC1Cb,EAD0C,CAAAc,YAAA,EAAI,EACpC;MAIRd,EADF,CAAAC,cAAA,cAA4B,iBACgD;MAA5BD,EAAA,CAAAE,UAAA,mBAAAuL,uDAAA;QAAA,OAASD,GAAA,CAAAhD,SAAA,CAAU,MAAM,CAAC;MAAA,EAAC;MAACxI,EAAA,CAAAa,MAAA,wBAAgB;MAAAb,EAAA,CAAAc,YAAA,EAAS;MACnGd,EAAA,CAAAC,cAAA,iBAA4E;MAA7BD,EAAA,CAAAE,UAAA,mBAAAwL,uDAAA;QAAA,OAASF,GAAA,CAAAhD,SAAA,CAAU,OAAO,CAAC;MAAA,EAAC;MAACxI,EAAA,CAAAa,MAAA,yBAAiB;MAC/Fb,EAD+F,CAAAc,YAAA,EAAS,EAClG;MA2INd,EAxIA,CAAAkB,UAAA,KAAAyK,oCAAA,kBAAkC,KAAAC,oCAAA,kBAwIC;MAsIrC5L,EAAA,CAAAc,YAAA,EAAM;;;MAxRCd,EAAA,CAAAoB,SAAA,IAAqC;MAArCpB,EAAA,CAAAyB,kBAAA,aAAA+J,GAAA,CAAAjE,YAAA,gBAAqC;MAKhCvH,EAAA,CAAAoB,SAAA,GAAqC;MAArCpB,EAAA,CAAA8B,WAAA,WAAA0J,GAAA,CAAA/D,SAAA,YAAqC;MACrCzH,EAAA,CAAAoB,SAAA,GAAsC;MAAtCpB,EAAA,CAAA8B,WAAA,WAAA0J,GAAA,CAAA/D,SAAA,aAAsC;MAI1CzH,EAAA,CAAAoB,SAAA,GAA0B;MAA1BpB,EAAA,CAAAgC,UAAA,SAAAwJ,GAAA,CAAA/D,SAAA,YAA0B;MAwI1BzH,EAAA,CAAAoB,SAAA,EAA2B;MAA3BpB,EAAA,CAAAgC,UAAA,SAAAwJ,GAAA,CAAA/D,SAAA,aAA2B;;;iBD5IvB7H,WAAW,EAAEL,YAAY,EAAAsM,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEvM,WAAW,EAAAwM,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,uBAAA,EAAAH,EAAA,CAAAI,oBAAA,EAAAJ,EAAA,CAAAK,mBAAA,EAAAL,EAAA,CAAAM,0BAAA,EAAAN,EAAA,CAAAO,eAAA,EAAAP,EAAA,CAAAQ,oBAAA,EAAAR,EAAA,CAAAS,kBAAA,EAAAT,EAAA,CAAAU,YAAA,EAAEjN,mBAAmB,EAAAuM,EAAA,CAAAW,kBAAA,EAAAX,EAAA,CAAAY,eAAA;EAAAC,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}