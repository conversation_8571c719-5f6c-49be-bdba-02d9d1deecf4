﻿import { Injectable, inject } from '@angular/core';
import { UserBadges } from '../models/friend.model';
import { Observable, from, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { SupabaseService } from './supabase.service';

@Injectable({
  providedIn: 'root'
})
export class BadgeService {
  private supabaseService = inject(SupabaseService);

  constructor() {}

  getUserBadges(userId: string): Observable<UserBadges | undefined> {

    return from(
      this.supabaseService.supabase
        .from('user_badges')
        .select('*')
        .eq('user_id', userId)
    ).pipe(
      map(response => {
        if (response.error) {

          if (response.error.code === '42501' ||
              response.error.message.includes('permission') ||
              response.error.message.includes('policy')) {
            throw new Error(`Permission denied: ${response.error.message}. Please check Supabase RLS policies for user_badges table.`);
          }

          return undefined;
        }

        if (response.data && response.data.length > 0) {
          return response.data[0] as UserBadges;
        } else {
          return undefined;
        }
      }),
      catchError(error => {
        if (error.message && error.message.includes('Permission denied')) {
          throw error;
        }
        return of(undefined);
      })
    );
  }

  async createUserBadges(userId: string): Promise<string> {

    try {
      const { data: existingBadges, error: checkError } = await this.supabaseService.supabase
        .from('user_badges')
        .select('id')
        .eq('user_id', userId);

      if (checkError) {

        if (checkError.code === '42501' || checkError.message.includes('permission') || checkError.message.includes('policy')) {
          throw new Error(`Permission denied: ${checkError.message}. Please check Supabase RLS policies for user_badges table.`);
        }

        throw new Error(checkError.message);
      }

      if (existingBadges && existingBadges.length > 0) {
        return existingBadges[0].id;
      }

      const newBadges: UserBadges = {
        user_id: userId,
        badge_newbie: false,
        badge_warrior: false,
        badge_hardcore: false,
        badge_peak_performer: false,
        badge_indestructible: false,
        badge_professional: false,
        badge_streak_7_days: false,
        badge_streak_30_days: false,
        badge_streak_100_days: false,
        badge_streak_365_days: false,
        badge_sidequest_streak_7_days: false,
        badge_sidequest_streak_30_days: false,
        badge_sidequest_streak_100_days: false,
        badge_sidequest_streak_365_days: false,
        badge_friends_5: false,
        badge_friends_10: false,
        badge_strength_master: false,
        badge_money_master: false,
        badge_health_master: false,
        badge_knowledge_master: false,
        created_at: new Date(),
        updated_at: new Date()
      };


      const { data, error } = await this.supabaseService.supabase
        .from('user_badges')
        .insert(newBadges)
        .select('id')
        .single();

      if (error) {

        if (error.code === '42501' || error.message.includes('permission') || error.message.includes('policy')) {
          throw new Error(`Permission denied: ${error.message}. Please check Supabase RLS policies for user_badges table.`);
        }

        throw new Error(error.message);
      }

      return data.id;
    } catch (error) {
      throw error;
    }
  }

  async updateUserBadges(badgeId: string, data: Partial<UserBadges>): Promise<void> {

    try {
      const updates = {
        ...data,
        updated_at: new Date()
      };

      const { error } = await this.supabaseService.supabase
        .from('user_badges')
        .update(updates)
        .eq('id', badgeId);

      if (error) {

        if (error.code === '42501' || error.message.includes('permission') || error.message.includes('policy')) {
          throw new Error(`Permission denied: ${error.message}. Please check Supabase RLS policies for user_badges table.`);
        }

        throw new Error(error.message);
      }

    } catch (error) {
      throw error;
    }
  }

  async updateCategoryBadges(userId: string, category: string): Promise<void> {

    try {
      const { data: badges, error: findError } = await this.supabaseService.supabase
        .from('user_badges')
        .select('id')
        .eq('user_id', userId);

      if (findError) {

        if (findError.code === '42501' || findError.message.includes('permission') || findError.message.includes('policy')) {
          throw new Error(`Permission denied: ${findError.message}. Please check Supabase RLS policies for user_badges table.`);
        }

        throw new Error(findError.message);
      }

      if (!badges || badges.length === 0) {
        await this.createUserBadges(userId);
        return this.updateCategoryBadges(userId, category); 
      }

      const badgeId = badges[0].id;
      const badgeField = `badge_${category}_master`;


      const { error: updateError } = await this.supabaseService.supabase
        .from('user_badges')
        .update({
          [badgeField]: true,
          updated_at: new Date()
        })
        .eq('id', badgeId);

      if (updateError) {

        if (updateError.code === '42501' || updateError.message.includes('permission') || updateError.message.includes('policy')) {
          throw new Error(`Permission denied: ${updateError.message}. Please check Supabase RLS policies for user_badges table.`);
        }

        throw new Error(updateError.message);
      }

    } catch (error) {
      throw error;
    }
  }
}

