﻿import { Component, OnInit, inject } from '@angular/core';
import { IonRouterOutlet } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { UserService } from '../../services/user.service';
import { User } from '../../models/user.model';
import { Subscription, of, switchMap } from 'rxjs';
import { SupabaseService } from '../../services/supabase.service';
import { XpService, EntityType } from '../../services/xp.service';

interface CategoryDisplay {
  name: string;
  icon: string;
  color: string;
  current_xp: number;
  required_xp: number;
  progress: number;
}

@Component({
  selector: 'app-profile',
  templateUrl: './profile.page.html',
  styleUrls: ['./profile.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule]
})
export class ProfilePage implements OnInit {
  user: User | null = null;
  userSubscription: Subscription | null = null;

  categories: CategoryDisplay[] = [];
  nextLevel = 0;

  showBioForm = false;
  editedBio = '';

  private supabaseService = inject(SupabaseService);
  private userService = inject(UserService);
  private xpService = inject(XpService);
  private routerOutlet = inject(IonRouterOutlet);

  constructor() {}

  ngOnInit() {
    this.userSubscription = this.userService.currentUserProfile$.subscribe(userProfile => {

      if (userProfile) {
        const user = userProfile as unknown as User;

        );

        this.user = {
          ...user,
          level: user.level ?? 1,  
          strength_xp: user.strength_xp ?? 0,
          money_xp: user.money_xp ?? 0,
          health_xp: user.health_xp ?? 0,
          knowledge_xp: user.knowledge_xp ?? 0,
          bio: user.bio || '',
          title: user.title || '🥚 Beginner'
        };

        this.editedBio = this.user.bio || '';

        console.log('Profile: User XP fields:', {
          strength_xp: this.user.strength_xp,
          money_xp: this.user.money_xp,
          health_xp: this.user.health_xp,
          knowledge_xp: this.user.knowledge_xp
        });

        this.calculateXpProgress();

        setTimeout(() => {
          const editBioBtn = document.getElementById('edit-bio-btn');
          if (editBioBtn && (!this.user?.bio || this.user.bio.trim() === '')) {
            editBioBtn.style.display = 'none';
          }
        }, 100);
      } else {

        this.supabaseService.currentUser$.pipe(
          switchMap(authUser => {
            if (!authUser) {
              return of(null);
            }


            return this.userService.ensureUserExists(authUser);
          })
        ).subscribe();
      }
    });
  }

  ngOnDestroy() {
    if (this.userSubscription) {
      this.userSubscription.unsubscribe();
    }
  }

  ionViewWillEnter() {

    if (this.routerOutlet.canGoBack()) {

      this.userService.refreshCurrentUserProfile().then(() => {
      }).catch(error => {
      });
    } else {
    }
  }

  async calculateXpProgress() {
    if (!this.user) {
      return;
    }


    if (typeof this.user.level !== 'number') {
      this.user.level = 1; 
    }

    this.xpService.calculateXpProgress(this.user, EntityType.USER).subscribe(result => {
      if (result) {
        this.categories = result.categories;
        this.nextLevel = result.next_level;
      }
    });
  }

  toggleBioForm() {
    this.showBioForm = !this.showBioForm;

    if (this.showBioForm && this.user) {
      this.editedBio = this.user.bio || '';
    }

    if (!this.showBioForm) {
      setTimeout(() => {
        const editBioBtn = document.getElementById('edit-bio-btn');
        if (editBioBtn && (!this.user?.bio || this.user?.bio.trim() === '')) {
          editBioBtn.style.display = 'none';
        }
      }, 0);
    }
  }

  updateBio() {
    if (!this.user || !this.user.id) return;

    const bio = this.editedBio.trim();
    if (bio.length > 100) {
      return;
    }

    this.userService.updateUserBio(this.user.id, bio).then(() => {
      if (this.user) {
        this.user.bio = bio === '' ? '' : bio;
      }
      this.showBioForm = false;

      setTimeout(() => {
        const editBioBtn = document.getElementById('edit-bio-btn');
        if (editBioBtn && (!this.user?.bio || this.user.bio.trim() === '')) {
          editBioBtn.style.display = 'none';
        }
      }, 0);
    });
  }
}
