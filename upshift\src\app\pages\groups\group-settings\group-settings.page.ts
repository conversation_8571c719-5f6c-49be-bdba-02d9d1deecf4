import { Compo<PERSON>, <PERSON><PERSON>ni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule, ToastController } from '@ionic/angular';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { Subscription, take, debounceTime, distinctUntilChanged, Subject, map } from 'rxjs';

import { SupabaseService } from '../../../services/supabase.service';
import { GroupService } from '../../../services/group.service';
import { UserService } from '../../../services/user.service';
import { FriendService } from '../../../services/friend.service';
import { XpService, EntityType } from '../../../services/xp.service';
import { Group, GroupMember } from '../../../models/supabase.models';
import { EmojiInputDirective } from '../../../directives/emoji-input.directive';

// Ensure Group interface has all required properties
interface GroupWithOptionalId extends Omit<Group, 'id'> {
  id?: string;
}

interface MemberWithProfile extends GroupMember {
  profile_picture?: string;
  username?: string;
}

@Component({
  selector: 'app-group-settings',
  templateUrl: './group-settings.page.html',
  styleUrls: ['./group-settings.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule, EmojiInputDirective]
})
export class GroupSettingsPage implements OnInit, OnDestroy {
  // User data
  userId: string | null = null;

  // Group data
  groupId: string | null = null;
  group: GroupWithOptionalId | null = null;
  members: MemberWithProfile[] = [];
  isAdmin = false;
  requiredXp = 0;

  // Group info form
  groupInfo = {
    name: '',
    emoji: '👥'
  };
  nameError = '';
  nameChecking = false;
  nameAvailable = false;
  private nameCheckSubject = new Subject<string>();
  private nameCheckSubscription: Subscription | null = null;

  // Invite
  activeTab = 'friends';
  inviteUsername = '';
  usernameSuggestions: string[] = [];
  invitationCode: string | null = null;
  codeIsValid = false;
  invitationSentMessage = '';
  isUsernameValid = false; // Track if the username is valid
  alreadyInvitedUsernames: string[] = []; // Track usernames that have already been invited
  validFriendUsernames: string[] = []; // Track valid friend usernames

  // Edit member
  showEditNicknameModal = false;
  editingMember: MemberWithProfile = {
    id: '',
    group_id: '',
    user_id: '',
    nickname: '',
    is_admin: false,
    joined_date: new Date()
  };

  // Messages
  successMessage = '';
  errorMessage = '';

  // Subscriptions
  private subscriptions: Subscription[] = [];

  // Services
  private supabaseService = inject(SupabaseService);
  private groupService = inject(GroupService);
  private userService = inject(UserService);
  private friendService = inject(FriendService);
  private xpService = inject(XpService);
  private route = inject(ActivatedRoute);
  private router = inject(Router);

  constructor() {}

  ngOnInit() {
    console.log('GroupSettingsPage: ngOnInit called');

    // Set up the debounced group name check
    this.nameCheckSubscription = this.nameCheckSubject.pipe(
      debounceTime(500), // Wait 500ms after the last event before emitting
      distinctUntilChanged() // Only emit if the value has changed
    ).subscribe(name => {
      this.checkGroupNameExists(name);
    });

    // Add the subscription to the list
    if (this.nameCheckSubscription) {
      this.subscriptions.push(this.nameCheckSubscription);
    }

    // Get the current user
    this.subscriptions.push(
      this.supabaseService.currentUser$.subscribe(user => {
        console.log('GroupSettingsPage: Current user:', user);
        if (user) {
          this.userId = user.id;

          // Get the group ID from the route
          this.route.paramMap.pipe(take(1)).subscribe(params => {
            console.log('GroupSettingsPage: Route params:', params);
            this.groupId = params.get('id');
            console.log('GroupSettingsPage: Group ID from route:', this.groupId);

            if (this.groupId) {
              this.loadGroup();
              this.loadMembers();
              this.checkInvitationCode();
              this.loadAlreadyInvitedUsernames();
              this.loadValidFriendUsernames();
            } else {
              console.error('GroupSettingsPage: No group ID found in route params');
            }
          });
        } else {
          console.log('GroupSettingsPage: No user found, redirecting to login');
          this.router.navigate(['/login']);
        }
      })
    );
  }

  ngOnDestroy() {
    // Unsubscribe from all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  loadGroup() {
    console.log('GroupSettingsPage: loadGroup called with groupId:', this.groupId);
    if (!this.groupId) {
      console.error('GroupSettingsPage: Cannot load group - groupId is null or undefined');
      return;
    }

    this.subscriptions.push(
      this.groupService.getGroup(this.groupId).subscribe(group => {
        console.log('GroupSettingsPage: Group data received:', group);
        if (group) {
          this.group = group;
          this.groupInfo.name = group.name;
          this.groupInfo.emoji = group.emoji;
          this.calculateRequiredXp();
        } else {
          console.error('GroupSettingsPage: Group not found, redirecting to groups list');
          this.router.navigate(['/groups']);
        }
      })
    );
  }

  loadMembers() {
    console.log('GroupSettingsPage: loadMembers called with groupId:', this.groupId, 'userId:', this.userId);
    if (!this.groupId || !this.userId) {
      console.error('GroupSettingsPage: Cannot load members - groupId or userId is missing');
      return;
    }

    this.subscriptions.push(
      this.groupService.getGroupMembers(this.groupId).subscribe(members => {
        console.log('GroupSettingsPage: Group members received:', members);

        // Check if current user is admin
        const currentUserMember = members.find(m => m.user_id === this.userId);
        this.isAdmin = currentUserMember?.is_admin || false;
        console.log('GroupSettingsPage: Current user is admin:', this.isAdmin);

        // Load profile data for each member
        const membersWithProfiles: MemberWithProfile[] = [];

        // Create a copy of the members array with all required properties
        const membersCopy = members.map(member => ({
          ...member,
          id: member.id || '',
          group_id: member.group_id || '',
          user_id: member.user_id || '',
          nickname: member.nickname || '',
          is_admin: member.is_admin || false,
          joined_date: member.joined_date || new Date()
        }));

        console.log('GroupSettingsPage: Processing members with profiles');
        for (const member of membersCopy) {
          this.userService.getUserProfile(member.user_id).pipe(take(1)).subscribe(profile => {
            console.log('GroupSettingsPage: Profile received for member:', member.user_id, profile);
            membersWithProfiles.push({
              ...member,
              profile_picture: profile?.profile_picture || undefined,
              username: profile?.username
            });

            // Update the members array after each member is processed
            this.members = [...membersWithProfiles];
            console.log('GroupSettingsPage: Updated members array:', this.members);
          });
        }
      })
    );
  }

  // Check if a user is a friend of the current user
  isFriend(userId: string): boolean {
    if (!this.userId || !userId) return false;

    // This is a placeholder - we'll need to implement this properly
    // For now, we'll just return false to use the user-profile route
    return false;
  }

  // Navigate to the appropriate profile page
  navigateToProfile(userId: string) {
    if (!userId) return;

    // Don't navigate if it's the current user
    if (userId === this.userId) return;

    // Check if this user is a friend
    this.friendService.getFriends(this.userId || '').pipe(
      take(1),
      map((friends: any[]) => {
        // Check if the user is in the friends list
        const isFriend = friends.some((friend: any) =>
          (friend.user_id === this.userId && friend.friend_id === userId) ||
          (friend.user_id === userId && friend.friend_id === this.userId)
        );

        if (isFriend) {
          // Navigate to friend profile if they are a friend
          this.router.navigate(['/friends', userId]);
        } else {
          // Navigate to user profile if they are not a friend
          this.router.navigate(['/user-profile', userId]);
        }
      })
    ).subscribe();
  }

  calculateRequiredXp() {
    if (!this.group) return;

    // Get required XP for next level from the JSON configuration
    this.xpService.getRequiredXpForNextLevel(this.group.level, EntityType.GROUP)
      .pipe(take(1))
      .subscribe(requiredXp => {
        console.log('GroupSettingsPage: Required XP for next level:', requiredXp);
        this.requiredXp = requiredXp;
      });
  }

  getProgressPercentage(xp: number): number {
    if (this.requiredXp <= 0) return 0;
    return Math.min(100, Math.round((xp / this.requiredXp) * 100));
  }

  // Check if group name is available as user types
  checkGroupName() {
    const name = this.groupInfo.name?.trim();

    // Reset states
    this.nameAvailable = false;

    if (!name) {
      this.nameError = '';
      this.nameChecking = false;
      return;
    }

    // Show checking indicator
    this.nameChecking = true;

    // Send the name to the debounced subject
    this.nameCheckSubject.next(name);
  }

  // Check if group name exists in database
  async checkGroupNameExists(name: string) {
    if (!name || !this.groupId) {
      this.nameError = '';
      this.nameChecking = false;
      return;
    }

    try {
      const isAvailable = await this.groupService.checkGroupNameAvailability(name, this.groupId);

      this.nameChecking = false;

      if (!isAvailable) {
        this.nameError = 'This group name is already taken';
        this.nameAvailable = false;
      } else {
        this.nameError = '';
        this.nameAvailable = true;
      }
    } catch (error) {
      console.error('Error checking group name:', error);
      this.nameChecking = false;
      this.nameError = 'Error checking group name availability';
    }
  }

  updateGroupInfo() {
    if (!this.groupId || !this.group || !this.isAdmin) return;

    if (!this.groupInfo.name.trim()) {
      this.errorMessage = 'Group name cannot be empty.';
      setTimeout(() => this.errorMessage = '', 3000);
      return;
    }

    // Check if the name is available
    if (this.nameError) {
      this.errorMessage = 'Please choose a different group name.';
      setTimeout(() => this.errorMessage = '', 3000);
      return;
    }

    this.groupService.updateGroup(this.groupId, {
      name: this.groupInfo.name.trim(),
      emoji: this.groupInfo.emoji
    }).then(() => {
      this.successMessage = 'Group information updated successfully!';
      setTimeout(() => this.successMessage = '', 3000);

      // Update local group object
      if (this.group) {
        this.group.name = this.groupInfo.name.trim();
        this.group.emoji = this.groupInfo.emoji;
      }
    }).catch(error => {
      this.errorMessage = error.message || 'Error updating group information.';
      console.error('Error updating group:', error);
      setTimeout(() => this.errorMessage = '', 3000);
    });
  }

  // Emoji validation is now handled by the EmojiInputDirective

  setActiveTab(tab: string) {
    this.activeTab = tab;
    // Clear invitation message when changing tabs
    this.invitationSentMessage = '';
  }

  inviteFriend() {
    if (!this.groupId || !this.isAdmin || !this.inviteUsername.trim()) return;

    const usernameToInvite = this.inviteUsername.trim();

    // Validate the username format before sending the invitation
    if (!this.friendService.validateUsernameFormat(usernameToInvite)) {
      this.errorMessage = 'Invalid username format. Username should only contain letters, numbers, and special characters.';
      setTimeout(() => this.errorMessage = '', 3000);
      return;
    }

    // Check if the user is already invited
    if (this.alreadyInvitedUsernames.includes(usernameToInvite)) {
      this.errorMessage = 'An invitation has already been sent to this user.';
      setTimeout(() => this.errorMessage = '', 3000);
      return;
    }

    // Check if the username belongs to a friend
    if (!this.validFriendUsernames.includes(usernameToInvite)) {
      this.errorMessage = 'You can only invite users from your friends list.';
      setTimeout(() => this.errorMessage = '', 3000);
      return;
    }

    this.groupService.inviteUserToGroup(this.groupId, usernameToInvite)
      .then(() => {
        // Add the username to the already invited list
        this.alreadyInvitedUsernames.push(usernameToInvite);

        // Set both messages for better visibility
        this.successMessage = `Invitation sent to ${usernameToInvite}!`;
        this.invitationSentMessage = `Invitation sent to ${usernameToInvite}!`;
        this.inviteUsername = '';

        // Clear the global success message after 3 seconds
        setTimeout(() => this.successMessage = '', 3000);

        // Clear the invitation-specific message after 5 seconds
        setTimeout(() => this.invitationSentMessage = '', 5000);
      })
      .catch(error => {
        console.error('Error inviting user:', error);

        // Check for specific error messages
        if (error.message && error.message.includes('already been sent')) {
          this.errorMessage = 'An invitation has already been sent to this user.';
        } else if (error.message && error.message.includes('already a member')) {
          this.errorMessage = 'This user is already a member of the group.';
        } else if (error.message && error.message.includes('User not found')) {
          this.errorMessage = 'User not found. Please check the username.';
        } else {
          this.errorMessage = 'Error sending invitation. Make sure the username is correct and the user is in your friends list.';
        }

        setTimeout(() => this.errorMessage = '', 3000);
      });
  }

  generateInvitationCode() {
    if (!this.groupId || !this.isAdmin) return;

    this.groupService.generateInvitationCode(this.groupId)
      .then(code => {
        this.invitationCode = code;
        this.codeIsValid = true;
      })
      .catch(error => {
        this.errorMessage = 'Error generating invitation code.';
        console.error('Error generating code:', error);
        setTimeout(() => this.errorMessage = '', 3000);
      });
  }

  checkInvitationCode() {
    if (!this.groupId) return;

    this.groupService.getGroup(this.groupId).pipe(take(1)).subscribe(group => {
      if (group && group.invitation_code && group.code_expiry) {
        const expiryDate = new Date(group.code_expiry);
        if (expiryDate > new Date()) {
          this.invitationCode = group.invitation_code;
          this.codeIsValid = true;
        }
      }
    });
  }

  loadAlreadyInvitedUsernames() {
    if (!this.groupId) return;

    // Get all pending invitations for this group
    this.supabaseService.supabase
      .from('group_join_requests')
      .select('username_invited')
      .eq('group_id', this.groupId)
      .then(response => {
        if (response.error) {
          console.error('Error loading invited usernames:', response.error);
          return;
        }

        // Store the list of already invited usernames
        this.alreadyInvitedUsernames = response.data.map(invite => invite.username_invited);
        console.log('Already invited usernames:', this.alreadyInvitedUsernames);
      });
  }

  loadValidFriendUsernames() {
    if (!this.userId) return;

    // Get all friends of the current user
    this.friendService.getFriendsWithProfiles(this.userId)
      .pipe(take(1))
      .subscribe(friends => {
        if (!friends || friends.length === 0) {
          console.log('No friends found');
          return;
        }

        // Extract usernames from friend profiles
        this.validFriendUsernames = friends
          .filter(friend => friend.profile && friend.profile.username)
          .map(friend => friend.profile.username);

        console.log('Valid friend usernames:', this.validFriendUsernames);
      });
  }

  updateSideQuestSettings() {
    if (!this.groupId || !this.group || !this.isAdmin) return;

    console.log('GroupSettingsPage: Updating side quest settings - enable_sidequests:', this.group.enable_sidequests);

    this.groupService.updateGroup(this.groupId, {
      enable_sidequests: this.group.enable_sidequests
    }).then(() => {
      console.log('GroupSettingsPage: Side quest settings updated successfully');
      this.successMessage = 'Side quest settings updated successfully!';
      setTimeout(() => this.successMessage = '', 3000);

      // Reload the group to ensure we have the latest data
      this.loadGroup();
    }).catch(error => {
      this.errorMessage = 'Error updating side quest settings.';
      console.error('GroupSettingsPage: Error updating settings:', error);
      setTimeout(() => this.errorMessage = '', 3000);
    });
  }

  editMemberNickname(member: MemberWithProfile) {
    this.editingMember = { ...member };
    this.showEditNicknameModal = true;
  }

  updateMemberNickname() {
    if (!this.groupId || !this.isAdmin || !this.editingMember.id) return;

    this.groupService.updateGroupMember(this.editingMember.id, {
      nickname: this.editingMember.nickname.trim()
    }).then(() => {
      this.successMessage = 'Member nickname updated successfully!';
      this.showEditNicknameModal = false;

      // Update local members array
      const index = this.members.findIndex(m => m.id === this.editingMember.id);
      if (index !== -1) {
        this.members[index].nickname = this.editingMember.nickname.trim();
      }

      setTimeout(() => this.successMessage = '', 3000);
    }).catch(error => {
      this.errorMessage = 'Error updating member nickname.';
      console.error('Error updating nickname:', error);
      setTimeout(() => this.errorMessage = '', 3000);
    });
  }

  removeMember(member: MemberWithProfile) {
    if (!this.groupId || !this.isAdmin || !member.id) return;

    if (confirm(`Are you sure you want to remove ${member.nickname} from the group?`)) {
      this.groupService.removeGroupMemberById(this.groupId, member.id)
        .then(() => {
          this.successMessage = `${member.nickname} has been removed from the group.`;

          // Update local members array
          this.members = this.members.filter(m => m.id !== member.id);

          setTimeout(() => this.successMessage = '', 3000);
        })
        .catch(error => {
          this.errorMessage = 'Error removing member from group.';
          console.error('Error removing member:', error);
          setTimeout(() => this.errorMessage = '', 3000);
        });
    }
  }

  leaveGroup() {
    if (!this.groupId || !this.userId) return;

    if (confirm('Are you sure you want to leave this group? You will need to be invited again to rejoin.')) {
      this.groupService.leaveGroup(this.groupId)
        .then(() => {
          this.router.navigate(['/groups']);
        })
        .catch(error => {
          this.errorMessage = 'Error leaving group.';
          console.error('Error leaving group:', error);
          setTimeout(() => this.errorMessage = '', 3000);
        });
    }
  }

  confirmDeleteGroup() {
    if (!this.groupId || !this.isAdmin) return;

    if (confirm('Are you sure you want to delete this group? This action cannot be undone and all group data will be permanently deleted.')) {
      this.groupService.deleteGroup(this.groupId)
        .then(() => {
          this.router.navigate(['/groups']);
        })
        .catch(error => {
          this.errorMessage = 'Error deleting group.';
          console.error('Error deleting group:', error);
          setTimeout(() => this.errorMessage = '', 3000);
        });
    }
  }

  selectUsername(username: string) {
    this.inviteUsername = username;
    this.usernameSuggestions = [];

    const trimmedUsername = username.trim();

    // Validate the username format
    const isFormatValid = trimmedUsername !== '' &&
                         this.friendService.validateUsernameFormat(trimmedUsername);

    // Check if the username is already invited
    const isAlreadyInvited = this.alreadyInvitedUsernames.includes(trimmedUsername);

    // Check if the username belongs to a friend
    const isFriend = this.validFriendUsernames.includes(trimmedUsername);

    // Username is valid only if the format is valid, it's not already invited, and it belongs to a friend
    this.isUsernameValid = isFormatValid && !isAlreadyInvited && isFriend;

    // Show error message if already invited
    if (isAlreadyInvited) {
      this.errorMessage = 'An invitation has already been sent to this user.';
      setTimeout(() => this.errorMessage = '', 3000);
    }
    // Show error message if not a friend
    else if (!isFriend && isFormatValid) {
      this.errorMessage = 'You can only invite users from your friends list.';
      setTimeout(() => this.errorMessage = '', 3000);
    }
  }

  onUsernameInput() {
    console.log('GroupSettingsPage: Username input changed:', this.inviteUsername);

    // Clear any previous invitation message when user starts typing
    this.invitationSentMessage = '';

    const trimmedUsername = this.inviteUsername.trim();

    // Validate the username format
    const isFormatValid = trimmedUsername !== '' &&
                         this.friendService.validateUsernameFormat(trimmedUsername);

    // Check if the username is already invited
    const isAlreadyInvited = this.alreadyInvitedUsernames.includes(trimmedUsername);

    // Check if the username belongs to a friend
    const isFriend = this.validFriendUsernames.includes(trimmedUsername);

    // Username is valid only if the format is valid, it's not already invited, and it belongs to a friend
    this.isUsernameValid = isFormatValid && !isAlreadyInvited && isFriend;

    if (!this.userId || !this.groupId || !this.inviteUsername || this.inviteUsername.length < 2) {
      this.usernameSuggestions = [];
      return;
    }

    // Search for friends by username
    this.friendService.searchFriendsByUsername(this.userId, this.inviteUsername, this.groupId)
      .pipe(take(1))
      .subscribe(suggestions => {
        console.log('GroupSettingsPage: Username suggestions:', suggestions);
        this.usernameSuggestions = suggestions;
      });
  }
}
