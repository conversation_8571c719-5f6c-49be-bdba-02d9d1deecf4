﻿import { Compo<PERSON>, <PERSON><PERSON>ni<PERSON>, On<PERSON><PERSON>roy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule, ToastController } from '@ionic/angular';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { Subscription, take, debounceTime, distinctUntilChanged, Subject, map } from 'rxjs';

import { SupabaseService } from '../../../services/supabase.service';
import { GroupService } from '../../../services/group.service';
import { UserService } from '../../../services/user.service';
import { FriendService } from '../../../services/friend.service';
import { XpService, EntityType } from '../../../services/xp.service';
import { Group, GroupMember } from '../../../models/supabase.models';
import { EmojiInputDirective } from '../../../directives/emoji-input.directive';

interface GroupWithOptionalId extends Omit<Group, 'id'> {
  id?: string;
}

interface MemberWithProfile extends GroupMember {
  profile_picture?: string;
  username?: string;
}

@Component({
  selector: 'app-group-settings',
  templateUrl: './group-settings.page.html',
  styleUrls: ['./group-settings.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule, EmojiInputDirective]
})
export class GroupSettingsPage implements OnInit, OnDestroy {
  userId: string | null = null;

  groupId: string | null = null;
  group: GroupWithOptionalId | null = null;
  members: MemberWithProfile[] = [];
  isAdmin = false;
  requiredXp = 0;

  groupInfo = {
    name: '',
    emoji: '👥'
  };
  nameError = '';
  nameChecking = false;
  nameAvailable = false;
  private nameCheckSubject = new Subject<string>();
  private nameCheckSubscription: Subscription | null = null;

  activeTab = 'friends';
  inviteUsername = '';
  usernameSuggestions: string[] = [];
  invitationCode: string | null = null;
  codeIsValid = false;
  invitationSentMessage = '';
  isUsernameValid = false; 
  alreadyInvitedUsernames: string[] = []; 
  validFriendUsernames: string[] = []; 

  showEditNicknameModal = false;
  editingMember: MemberWithProfile = {
    id: '',
    group_id: '',
    user_id: '',
    nickname: '',
    is_admin: false,
    joined_date: new Date()
  };

  successMessage = '';
  errorMessage = '';

  private subscriptions: Subscription[] = [];

  private supabaseService = inject(SupabaseService);
  private groupService = inject(GroupService);
  private userService = inject(UserService);
  private friendService = inject(FriendService);
  private xpService = inject(XpService);
  private route = inject(ActivatedRoute);
  private router = inject(Router);

  constructor() {}

  ngOnInit() {

    this.nameCheckSubscription = this.nameCheckSubject.pipe(
      debounceTime(500), 
      distinctUntilChanged() 
    ).subscribe(name => {
      this.checkGroupNameExists(name);
    });

    if (this.nameCheckSubscription) {
      this.subscriptions.push(this.nameCheckSubscription);
    }

    this.subscriptions.push(
      this.supabaseService.currentUser$.subscribe(user => {
        if (user) {
          this.userId = user.id;

          this.route.paramMap.pipe(take(1)).subscribe(params => {
            this.groupId = params.get('id');

            if (this.groupId) {
              this.loadGroup();
              this.loadMembers();
              this.checkInvitationCode();
              this.loadAlreadyInvitedUsernames();
              this.loadValidFriendUsernames();
            } else {
            }
          });
        } else {
          this.router.navigate(['/login']);
        }
      })
    );
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  loadGroup() {
    if (!this.groupId) {
      return;
    }

    this.subscriptions.push(
      this.groupService.getGroup(this.groupId).subscribe(group => {
        if (group) {
          this.group = group;
          this.groupInfo.name = group.name;
          this.groupInfo.emoji = group.emoji;
          this.calculateRequiredXp();
        } else {
          this.router.navigate(['/groups']);
        }
      })
    );
  }

  loadMembers() {
    if (!this.groupId || !this.userId) {
      return;
    }

    this.subscriptions.push(
      this.groupService.getGroupMembers(this.groupId).subscribe(members => {

        const currentUserMember = members.find(m => m.user_id === this.userId);
        this.isAdmin = currentUserMember?.is_admin || false;

        const membersWithProfiles: MemberWithProfile[] = [];

        const membersCopy = members.map(member => ({
          ...member,
          id: member.id || '',
          group_id: member.group_id || '',
          user_id: member.user_id || '',
          nickname: member.nickname || '',
          is_admin: member.is_admin || false,
          joined_date: member.joined_date || new Date()
        }));

        for (const member of membersCopy) {
          this.userService.getUserProfile(member.user_id).pipe(take(1)).subscribe(profile => {
            membersWithProfiles.push({
              ...member,
              profile_picture: profile?.profile_picture || undefined,
              username: profile?.username
            });

            this.members = [...membersWithProfiles];
          });
        }
      })
    );
  }

  isFriend(userId: string): boolean {
    if (!this.userId || !userId) return false;

    return false;
  }

  navigateToProfile(userId: string) {
    if (!userId) return;

    if (userId === this.userId) return;

    this.friendService.getFriends(this.userId || '').pipe(
      take(1),
      map((friends: any[]) => {
        const isFriend = friends.some((friend: any) =>
          (friend.user_id === this.userId && friend.friend_id === userId) ||
          (friend.user_id === userId && friend.friend_id === this.userId)
        );

        if (isFriend) {
          this.router.navigate(['/friends', userId]);
        } else {
          this.router.navigate(['/user-profile', userId]);
        }
      })
    ).subscribe();
  }

  calculateRequiredXp() {
    if (!this.group) return;

    this.xpService.getRequiredXpForNextLevel(this.group.level, EntityType.GROUP)
      .pipe(take(1))
      .subscribe(requiredXp => {
        this.requiredXp = requiredXp;
      });
  }

  getProgressPercentage(xp: number): number {
    if (this.requiredXp <= 0) return 0;
    return Math.min(100, Math.round((xp / this.requiredXp) * 100));
  }

  checkGroupName() {
    const name = this.groupInfo.name?.trim();

    this.nameAvailable = false;

    if (!name) {
      this.nameError = '';
      this.nameChecking = false;
      return;
    }

    this.nameChecking = true;

    this.nameCheckSubject.next(name);
  }

  async checkGroupNameExists(name: string) {
    if (!name || !this.groupId) {
      this.nameError = '';
      this.nameChecking = false;
      return;
    }

    try {
      const isAvailable = await this.groupService.checkGroupNameAvailability(name, this.groupId);

      this.nameChecking = false;

      if (!isAvailable) {
        this.nameError = 'This group name is already taken';
        this.nameAvailable = false;
      } else {
        this.nameError = '';
        this.nameAvailable = true;
      }
    } catch (error) {
      this.nameChecking = false;
      this.nameError = 'Error checking group name availability';
    }
  }

  updateGroupInfo() {
    if (!this.groupId || !this.group || !this.isAdmin) return;

    if (!this.groupInfo.name.trim()) {
      this.errorMessage = 'Group name cannot be empty.';
      setTimeout(() => this.errorMessage = '', 3000);
      return;
    }

    if (this.nameError) {
      this.errorMessage = 'Please choose a different group name.';
      setTimeout(() => this.errorMessage = '', 3000);
      return;
    }

    this.groupService.updateGroup(this.groupId, {
      name: this.groupInfo.name.trim(),
      emoji: this.groupInfo.emoji
    }).then(() => {
      this.successMessage = 'Group information updated successfully!';
      setTimeout(() => this.successMessage = '', 3000);

      if (this.group) {
        this.group.name = this.groupInfo.name.trim();
        this.group.emoji = this.groupInfo.emoji;
      }
    }).catch(error => {
      this.errorMessage = error.message || 'Error updating group information.';
      setTimeout(() => this.errorMessage = '', 3000);
    });
  }


  setActiveTab(tab: string) {
    this.activeTab = tab;
    this.invitationSentMessage = '';
  }

  inviteFriend() {
    if (!this.groupId || !this.isAdmin || !this.inviteUsername.trim()) return;

    const usernameToInvite = this.inviteUsername.trim();

    if (!this.friendService.validateUsernameFormat(usernameToInvite)) {
      this.errorMessage = 'Invalid username format. Username should only contain letters, numbers, and special characters.';
      setTimeout(() => this.errorMessage = '', 3000);
      return;
    }

    if (this.alreadyInvitedUsernames.includes(usernameToInvite)) {
      this.errorMessage = 'An invitation has already been sent to this user.';
      setTimeout(() => this.errorMessage = '', 3000);
      return;
    }

    if (!this.validFriendUsernames.includes(usernameToInvite)) {
      this.errorMessage = 'You can only invite users from your friends list.';
      setTimeout(() => this.errorMessage = '', 3000);
      return;
    }

    this.groupService.inviteUserToGroup(this.groupId, usernameToInvite)
      .then(() => {
        this.alreadyInvitedUsernames.push(usernameToInvite);

        this.successMessage = `Invitation sent to ${usernameToInvite}!`;
        this.invitationSentMessage = `Invitation sent to ${usernameToInvite}!`;
        this.inviteUsername = '';

        setTimeout(() => this.successMessage = '', 3000);

        setTimeout(() => this.invitationSentMessage = '', 5000);
      })
      .catch(error => {

        if (error.message && error.message.includes('already been sent')) {
          this.errorMessage = 'An invitation has already been sent to this user.';
        } else if (error.message && error.message.includes('already a member')) {
          this.errorMessage = 'This user is already a member of the group.';
        } else if (error.message && error.message.includes('User not found')) {
          this.errorMessage = 'User not found. Please check the username.';
        } else {
          this.errorMessage = 'Error sending invitation. Make sure the username is correct and the user is in your friends list.';
        }

        setTimeout(() => this.errorMessage = '', 3000);
      });
  }

  generateInvitationCode() {
    if (!this.groupId || !this.isAdmin) return;

    this.groupService.generateInvitationCode(this.groupId)
      .then(code => {
        this.invitationCode = code;
        this.codeIsValid = true;
      })
      .catch(error => {
        this.errorMessage = 'Error generating invitation code.';
        setTimeout(() => this.errorMessage = '', 3000);
      });
  }

  checkInvitationCode() {
    if (!this.groupId) return;

    this.groupService.getGroup(this.groupId).pipe(take(1)).subscribe(group => {
      if (group && group.invitation_code && group.code_expiry) {
        const expiryDate = new Date(group.code_expiry);
        if (expiryDate > new Date()) {
          this.invitationCode = group.invitation_code;
          this.codeIsValid = true;
        }
      }
    });
  }

  loadAlreadyInvitedUsernames() {
    if (!this.groupId) return;

    this.supabaseService.supabase
      .from('group_join_requests')
      .select('username_invited')
      .eq('group_id', this.groupId)
      .then(response => {
        if (response.error) {
          return;
        }

        this.alreadyInvitedUsernames = response.data.map(invite => invite.username_invited);
      });
  }

  loadValidFriendUsernames() {
    if (!this.userId) return;

    this.friendService.getFriendsWithProfiles(this.userId)
      .pipe(take(1))
      .subscribe(friends => {
        if (!friends || friends.length === 0) {
          return;
        }

        this.validFriendUsernames = friends
          .filter(friend => friend.profile && friend.profile.username)
          .map(friend => friend.profile.username);

      });
  }

  updateSideQuestSettings() {
    if (!this.groupId || !this.group || !this.isAdmin) return;


    this.groupService.updateGroup(this.groupId, {
      enable_sidequests: this.group.enable_sidequests
    }).then(() => {
      this.successMessage = 'Side quest settings updated successfully!';
      setTimeout(() => this.successMessage = '', 3000);

      this.loadGroup();
    }).catch(error => {
      this.errorMessage = 'Error updating side quest settings.';
      setTimeout(() => this.errorMessage = '', 3000);
    });
  }

  editMemberNickname(member: MemberWithProfile) {
    this.editingMember = { ...member };
    this.showEditNicknameModal = true;
  }

  updateMemberNickname() {
    if (!this.groupId || !this.isAdmin || !this.editingMember.id) return;

    this.groupService.updateGroupMember(this.editingMember.id, {
      nickname: this.editingMember.nickname.trim()
    }).then(() => {
      this.successMessage = 'Member nickname updated successfully!';
      this.showEditNicknameModal = false;

      const index = this.members.findIndex(m => m.id === this.editingMember.id);
      if (index !== -1) {
        this.members[index].nickname = this.editingMember.nickname.trim();
      }

      setTimeout(() => this.successMessage = '', 3000);
    }).catch(error => {
      this.errorMessage = 'Error updating member nickname.';
      setTimeout(() => this.errorMessage = '', 3000);
    });
  }

  removeMember(member: MemberWithProfile) {
    if (!this.groupId || !this.isAdmin || !member.id) return;

    if (confirm(`Are you sure you want to remove ${member.nickname} from the group?`)) {
      this.groupService.removeGroupMemberById(this.groupId, member.id)
        .then(() => {
          this.successMessage = `${member.nickname} has been removed from the group.`;

          this.members = this.members.filter(m => m.id !== member.id);

          setTimeout(() => this.successMessage = '', 3000);
        })
        .catch(error => {
          this.errorMessage = 'Error removing member from group.';
          setTimeout(() => this.errorMessage = '', 3000);
        });
    }
  }

  leaveGroup() {
    if (!this.groupId || !this.userId) return;

    if (confirm('Are you sure you want to leave this group? You will need to be invited again to rejoin.')) {
      this.groupService.leaveGroup(this.groupId)
        .then(() => {
          this.router.navigate(['/groups']);
        })
        .catch(error => {
          this.errorMessage = 'Error leaving group.';
          setTimeout(() => this.errorMessage = '', 3000);
        });
    }
  }

  confirmDeleteGroup() {
    if (!this.groupId || !this.isAdmin) return;

    if (confirm('Are you sure you want to delete this group? This action cannot be undone and all group data will be permanently deleted.')) {
      this.groupService.deleteGroup(this.groupId)
        .then(() => {
          this.router.navigate(['/groups']);
        })
        .catch(error => {
          this.errorMessage = 'Error deleting group.';
          setTimeout(() => this.errorMessage = '', 3000);
        });
    }
  }

  selectUsername(username: string) {
    this.inviteUsername = username;
    this.usernameSuggestions = [];

    const trimmedUsername = username.trim();

    const isFormatValid = trimmedUsername !== '' &&
                         this.friendService.validateUsernameFormat(trimmedUsername);

    const isAlreadyInvited = this.alreadyInvitedUsernames.includes(trimmedUsername);

    const isFriend = this.validFriendUsernames.includes(trimmedUsername);

    this.isUsernameValid = isFormatValid && !isAlreadyInvited && isFriend;

    if (isAlreadyInvited) {
      this.errorMessage = 'An invitation has already been sent to this user.';
      setTimeout(() => this.errorMessage = '', 3000);
    }
    else if (!isFriend && isFormatValid) {
      this.errorMessage = 'You can only invite users from your friends list.';
      setTimeout(() => this.errorMessage = '', 3000);
    }
  }

  onUsernameInput() {

    this.invitationSentMessage = '';

    const trimmedUsername = this.inviteUsername.trim();

    const isFormatValid = trimmedUsername !== '' &&
                         this.friendService.validateUsernameFormat(trimmedUsername);

    const isAlreadyInvited = this.alreadyInvitedUsernames.includes(trimmedUsername);

    const isFriend = this.validFriendUsernames.includes(trimmedUsername);

    this.isUsernameValid = isFormatValid && !isAlreadyInvited && isFriend;

    if (!this.userId || !this.groupId || !this.inviteUsername || this.inviteUsername.length < 2) {
      this.usernameSuggestions = [];
      return;
    }

    this.friendService.searchFriendsByUsername(this.userId, this.inviteUsername, this.groupId)
      .pipe(take(1))
      .subscribe(suggestions => {
        this.usernameSuggestions = suggestions;
      });
  }
}
