﻿import { Injectable } from '@angular/core';
import { Preferences as CapacitorPreferences } from '@capacitor/preferences';

@Injectable({
  providedIn: 'root'
})
export class PreferencesService {
  private useLocalStorage = false;

  constructor() {
    this.checkCapacitorAvailability();
  }

  private async checkCapacitorAvailability(): Promise<void> {
    try {
      await CapacitorPreferences.get({ key: 'test_key' });
      this.useLocalStorage = false;
    } catch (error) {
      this.useLocalStorage = true;
    }
  }

  async get(key: string): Promise<{ value: string | null }> {
    try {
      if (this.useLocalStorage) {
        const value = localStorage.getItem(key);
        return { value };
      } else {
        return await CapacitorPreferences.get({ key });
      }
    } catch (error) {
      const value = localStorage.getItem(key);
      return { value };
    }
  }

  async set(key: string, value: string): Promise<void> {
    try {
      if (this.useLocalStorage) {
        localStorage.setItem(key, value);
      } else {
        await CapacitorPreferences.set({ key, value });
      }
    } catch (error) {
      localStorage.setItem(key, value);
    }
  }

  async remove(key: string): Promise<void> {
    try {
      if (this.useLocalStorage) {
        localStorage.removeItem(key);
      } else {
        await CapacitorPreferences.remove({ key });
      }
    } catch (error) {
      localStorage.removeItem(key);
    }
  }

  async clear(): Promise<void> {
    try {
      if (this.useLocalStorage) {
        localStorage.clear();
      } else {
        await CapacitorPreferences.clear();
      }
    } catch (error) {
      localStorage.clear();
    }
  }
}
