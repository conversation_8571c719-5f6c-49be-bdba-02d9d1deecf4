﻿.container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  color: var(--text-color);
  overflow-y: auto;
  padding-bottom: 80px;  Space for navigation */
.tab-navigation {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--quest-border);
}

.tab-navigation button {
  padding: 10px 20px;
  background-color: transparent;
  border: none;
  border-bottom: 3px solid transparent;
  cursor: pointer;
  font-weight: 500;
  color: var(--secondary-text);
  transition: all 0.3s ease;
}

.tab-navigation button.active {
  color: var(--accent-color);
  border-bottom-color: var(--accent-color);
}

 Common section styles */
.button-group {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.import-btn,
.supabase-btn,
.toggle-btn,
.submit-btn,
.action-btn {
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.import-btn {
  background-color: var(--accent-color);
  color: white;
}

.supabase-btn {
  background-color: #3ecf8e;
  color: white;
}

.toggle-btn {
  background-color: #4169E1;
  color: white;
}

.submit-btn {
  background-color: var(--accent-color);
  color: white;
  padding: 10px 20px;
  font-size: 16px;
  width: 100%;
  margin-top: 20px;
}

.action-btn {
  padding: 6px 12px;
  font-size: 12px;
}

.toggle-btn.action-btn {
  background-color: #4169E1;
}

.delete-btn {
  background-color: #e74c3c;
}

button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

 Side quests list */
.add-form {
  margin-top: 20px;
}

.form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.form-group {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.emoji-input {
  flex: 0 0 80px;
}

.name-input {
  flex: 1;
}

.goal-input {
  flex: 0 0 100px;
}

label {
  margin-bottom: 5px;
  font-size: 14px;
  color: var(--text-color);
}

input, select, textarea {
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid var(--quest-border);
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--text-color);
  font-size: 14px;
}

textarea {
  min-height: 80px;
  resize: vertical;
}

.error-message {
  color: #e74c3c;
  font-size: 12px;
  margin-top: 5px;
}

.empty-state {
  text-align: center;
  padding: 20px;
  color: var(--secondary-text);
}
