﻿import { Component, OnInit } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import {SupabaseService} from "./services/supabase.service";

@Component({
  selector: 'app-root',
  standalone: true,
  templateUrl: './app.component.html',
  imports: [IonicModule],
})
export class AppComponent implements OnInit {
  private initialized = false;
  constructor(private supabase: SupabaseService) {
  }

  async ngOnInit() {
    await this.supabase.loadSession();
    this.initialized = true;
  }
}
