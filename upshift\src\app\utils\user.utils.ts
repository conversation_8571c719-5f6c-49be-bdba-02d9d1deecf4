﻿export function getTitleByLevel(level: number): string {
    const TITLE_CHOICES: [number, string][] = [
      [100, '🧬 Professional Upshifter'],
      [90, '👁 Ultra Human'],
      [75, '💎 Indestructible'],
      [60, '👑 Elite Operator'],
      [50, '🚀 Peak Performer'],
      [40, '🧙‍♂️ Master of Consistency'],
      [35, '🌟 High Performer'],
      [30, '🦾 Disciplined Machine'],
      [25, '🔥 Hardcore'],
      [20, '🧠 Nonchalant'],
      [15, '🧘 Monk'],
      [10, '⚔️ Warrior'],
      [5, '💪 Newbie'],
      [1, '🥚 Beginner'],
    ];
    for (const [lvl, title] of TITLE_CHOICES) {
      if (level >= lvl) return title;
    }
    return '🥚 Beginner';
  }
