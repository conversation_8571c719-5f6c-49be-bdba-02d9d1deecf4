import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule, ToastController } from '@ionic/angular';
import { SupabaseService } from '../../services/supabase.service';
import { Subscription } from 'rxjs';


interface AffiliateOffer {
  id: string;
  name: string;
  description: string;
  number_of_invites_required: number;
  image_cover: string;
  price_value: number;
}

interface AffiliateReward {
  id: string;
  created_at: string;
  user_id: string;
  affiliate_offer: string;
  completed: boolean;
  subscription_status: string;
  affiliate_code_used: string;
  number_of_affiliates: number;
}

@Component({
  selector: 'app-affiliates',
  templateUrl: './affiliates.page.html',
  styleUrls: ['./affiliates.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule]
})
export class AffiliatesPage implements OnInit, OnDestroy {
  userId: string | null = null;
  affiliateOffers: AffiliateOffer[] = [];
  userAffiliateCount: number = 0;
  selectedOffer: AffiliateOffer | null = null;
  showOfferDetails: boolean = false;
  userSubscription: Subscription = new Subscription();
  isLoading: boolean = true;
  userRewards: AffiliateReward[] = [];

  constructor(
    private supabaseService: SupabaseService,
    private toastController: ToastController
  ) { }

  ngOnInit() {
    this.isLoading = true;

    // Get current user
    const userDataSubscription = this.supabaseService.currentUser$.subscribe(user => {
      if (user) {
        this.userId = user.id;
        this.loadUserData();
        this.loadAffiliateOffers();
        this.loadUserRewards();
      } else {
        this.userId = null;
        this.isLoading = false;
      }
    });

    this.userSubscription.add(userDataSubscription);
  }

  ngOnDestroy() {
    this.userSubscription.unsubscribe();
  }

  async loadUserData() {
    if (!this.userId) return;

    try {
      // Get user data from profiles table directly
      const { data, error } = await this.supabaseService.getClient()
        .from('profiles')
        .select('*')
        .eq('id', this.userId)
        .single();

      if (data && !error) {
        // Get number_of_affiliates from profiles table
        this.userAffiliateCount = data.number_of_affiliates || 0;
      }
    } catch (error) {
      console.error('Error loading user data:', error);
    }
  }

  async loadAffiliateOffers() {
    if (!this.userId) return;

    try {
      const { data, error } = await this.supabaseService.getClient()
        .from('affiliate_offers')
        .select('*')
        .order('number_of_invites_required', { ascending: true });

      if (error) {
        throw error;
      }

      this.affiliateOffers = data || [];

      // Process image URLs to use the correct bucket
      this.affiliateOffers = this.affiliateOffers.map(offer => {
        if (offer.image_cover && !offer.image_cover.includes('://')) {
          try {
            // Get the public URL for the image
            const { data: urlData } = this.supabaseService.getClient()
              .storage
              .from('affiliate-offer')
              .getPublicUrl(offer.image_cover);

            console.log(`Generated public URL for ${offer.image_cover}:`, urlData);

            return {
              ...offer,
              image_cover: urlData.publicUrl
            };
          } catch (err) {
            const error = err as any;
            console.error(`Error getting public URL for ${offer.image_cover}:`, error);
            return offer;
          }
        }
        return offer;
      });

      this.isLoading = false;
    } catch (error) {
      console.error('Error loading affiliate offers:', error);
      this.isLoading = false;
    }
  }

  async loadUserRewards() {
    if (!this.userId) return;

    try {
      const { data, error } = await this.supabaseService.getClient()
        .from('affiliate_rewards_history')
        .select('*')
        .eq('user_id', this.userId);

      if (error) {
        throw error;
      }

      this.userRewards = data || [];
    } catch (error) {
      console.error('Error loading user rewards:', error);
    }
  }

  viewOfferDetails(offer: AffiliateOffer) {
    console.log('Opening offer details for:', offer);
    this.selectedOffer = offer;
    this.showOfferDetails = true;
  }

  closeOfferDetails() {
    console.log('Closing offer details');
    this.showOfferDetails = false;
    this.selectedOffer = null;
  }

  isOfferClaimed(offerId: string): boolean {
    return this.userRewards.some(reward => reward.affiliate_offer === offerId);
  }

  canClaimOffer(offer: AffiliateOffer): boolean {
    return this.userAffiliateCount >= offer.number_of_invites_required && !this.isOfferClaimed(offer.id);
  }

  async claimOffer(offer: AffiliateOffer) {
    if (!this.userId || !this.canClaimOffer(offer)) return;

    try {
      const { error } = await this.supabaseService.getClient()
        .from('affiliate_rewards_history')
        .insert([
          {
            user_id: this.userId,
            affiliate_offer: offer.id,
            completed: true,
            subscription_status: 'claimed',
            affiliate_code_used: '',
            number_of_affiliates: this.userAffiliateCount
          }
        ]);

      if (error) {
        throw error;
      }

      // Refresh rewards list
      this.loadUserRewards();

      // Show success message with toast
      const toast = await this.toastController.create({
        message: `Successfully claimed ${offer.name}! You saved ${this.formatPrice(offer.price_value)}.`,
        duration: 3000,
        position: 'bottom',
        color: 'success',
        buttons: [
          {
            text: 'OK',
            role: 'cancel'
          }
        ]
      });
      await toast.present();

      // Close the modal
      this.closeOfferDetails();
    } catch (error) {
      console.error('Error claiming offer:', error);

      // Show error message with toast
      const toast = await this.toastController.create({
        message: 'Failed to claim offer. Please try again.',
        duration: 3000,
        position: 'bottom',
        color: 'danger',
        buttons: [
          {
            text: 'OK',
            role: 'cancel'
          }
        ]
      });
      await toast.present();
    }
  }

  /**
   * Format price with the user's currency
   */
  formatPrice(value: number, showCode = false): string {
    if (value === undefined || value === null) return '';

    try {
      // Simple formatting with $ symbol
      return showCode ? `${value.toFixed(2)} USD` : `$${value.toFixed(2)}`;
    } catch (error) {
      console.error('Error formatting price:', error);
      return `$${value}`;
    }
  }
}
