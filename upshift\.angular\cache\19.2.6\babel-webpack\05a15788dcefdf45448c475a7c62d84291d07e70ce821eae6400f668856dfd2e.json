{"ast": null, "code": "var _GroupWaitingRoomComponent;\nimport { IonicModule } from '@ionic/angular';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@ionic/angular\";\nexport class GroupWaitingRoomComponent {\n  constructor(router) {\n    this.router = router;\n    this.groupId = '';\n    this.groupName = 'Group';\n    this.isAdmin = false;\n  }\n  goToSettings() {\n    if (this.groupId) {\n      this.router.navigate([`/groups/${this.groupId}/settings`]);\n    }\n  }\n  goBack() {\n    this.router.navigate(['/groups']);\n  }\n}\n_GroupWaitingRoomComponent = GroupWaitingRoomComponent;\n_GroupWaitingRoomComponent.ɵfac = function GroupWaitingRoomComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _GroupWaitingRoomComponent)(i0.ɵɵdirectiveInject(i1.Router));\n};\n_GroupWaitingRoomComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _GroupWaitingRoomComponent,\n  selectors: [[\"app-group-waiting-room\"]],\n  inputs: {\n    groupId: \"groupId\",\n    groupName: \"groupName\",\n    isAdmin: \"isAdmin\"\n  },\n  decls: 17,\n  vars: 1,\n  consts: [[1, \"waiting-room-container\"], [1, \"waiting-room-content\"], [1, \"waiting-room-icon\"], [1, \"waiting-message\"], [1, \"sub-message\"], [1, \"action-buttons\"], [1, \"settings-button\", 3, \"click\"], [\"name\", \"settings-outline\"], [1, \"back-button\", 3, \"click\"], [\"name\", \"arrow-back-outline\"]],\n  template: function GroupWaitingRoomComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n      i0.ɵɵtext(3, \"\\u23F3\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"h2\");\n      i0.ɵɵtext(5);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(6, \"p\", 3);\n      i0.ɵɵtext(7, \"You'll be able to participate in group quests starting tomorrow.\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(8, \"p\", 4);\n      i0.ɵɵtext(9, \"This gives everyone a fair chance to prepare for the challenges ahead.\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(10, \"div\", 5)(11, \"button\", 6);\n      i0.ɵɵlistener(\"click\", function GroupWaitingRoomComponent_Template_button_click_11_listener() {\n        return ctx.goToSettings();\n      });\n      i0.ɵɵelement(12, \"ion-icon\", 7);\n      i0.ɵɵtext(13, \" Settings \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(14, \"button\", 8);\n      i0.ɵɵlistener(\"click\", function GroupWaitingRoomComponent_Template_button_click_14_listener() {\n        return ctx.goBack();\n      });\n      i0.ɵɵelement(15, \"ion-icon\", 9);\n      i0.ɵɵtext(16, \" Back to Groups \");\n      i0.ɵɵelementEnd()()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(5);\n      i0.ɵɵtextInterpolate1(\"Welcome to \", ctx.groupName, \"!\");\n    }\n  },\n  dependencies: [IonicModule, i2.IonIcon, CommonModule],\n  styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%];\\n\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\tvar __webpack_modules__ = ({\\n\\n\\n 971:\\n\\n\\n\\n\\n\\n (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/sass-loader/dist/cjs.js):\\\\nexpected \\\\\\\"{\\\\\\\".\\\\n  \\u2577\\\\n6 \\u2502   padding-bottom: 70px;  Space for bottom navigation */\\\\r\\\\n  \\u2502                                                        ^\\\\n  \\u2575\\\\n  src\\\\\\\\app\\\\\\\\components\\\\\\\\group-waiting-room\\\\\\\\group-waiting-room.component.scss 6:56  root stylesheet\\\");\\n\\n\\n })\\n\\n\\n \\t});\\n\\n\\n\\n \\t\\n\\n \\t// startup\\n\\n \\t// Load entry module and return exports\\n\\n \\t// This entry module doesn't tell about it's top-level declarations so it can't be inlined\\n\\n \\tvar __webpack_exports__ = {};\\n\\n \\t__webpack_modules__[971]();\\n\\n \\tresource = __webpack_exports__;\\n\\n \\t\\n\\n })()\\n;\"]\n});", "map": {"version": 3, "names": ["IonicModule", "CommonModule", "GroupWaitingRoomComponent", "constructor", "router", "groupId", "groupName", "isAdmin", "goToSettings", "navigate", "goBack", "i0", "ɵɵdirectiveInject", "i1", "Router", "selectors", "inputs", "decls", "vars", "consts", "template", "GroupWaitingRoomComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "GroupWaitingRoomComponent_Template_button_click_11_listener", "ɵɵelement", "GroupWaitingRoomComponent_Template_button_click_14_listener", "ɵɵadvance", "ɵɵtextInterpolate1", "i2", "IonIcon", "styles"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\components\\group-waiting-room\\group-waiting-room.component.ts", "C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\components\\group-waiting-room\\group-waiting-room.component.html"], "sourcesContent": ["import { Component, Input } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { CommonModule } from '@angular/common';\r\n\r\n@Component({\r\n  selector: 'app-group-waiting-room',\r\n  templateUrl: './group-waiting-room.component.html',\r\n  styleUrls: ['./group-waiting-room.component.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule]\r\n})\r\nexport class GroupWaitingRoomComponent {\r\n  @Input() groupId: string = '';\r\n  @Input() groupName: string = 'Group';\r\n  @Input() isAdmin: boolean = false;\r\n\r\n  constructor(private router: Router) {}\r\n\r\n  goToSettings() {\r\n    if (this.groupId) {\r\n      this.router.navigate([`/groups/${this.groupId}/settings`]);\r\n    }\r\n  }\r\n\r\n  goBack() {\r\n    this.router.navigate(['/groups']);\r\n  }\r\n}\r\n", "<div class=\"waiting-room-container\">\r\n  <div class=\"waiting-room-content\">\r\n    <div class=\"waiting-room-icon\">⏳</div>\r\n    <h2>Welcome to {{ groupName }}!</h2>\r\n    <p class=\"waiting-message\">You'll be able to participate in group quests starting tomorrow.</p>\r\n    <p class=\"sub-message\">This gives everyone a fair chance to prepare for the challenges ahead.</p>\r\n\r\n    <div class=\"action-buttons\">\r\n      <button class=\"settings-button\" (click)=\"goToSettings()\">\r\n        <ion-icon name=\"settings-outline\"></ion-icon>\r\n        Settings\r\n      </button>\r\n      <button class=\"back-button\" (click)=\"goBack()\">\r\n        <ion-icon name=\"arrow-back-outline\"></ion-icon>\r\n        Back to Groups\r\n      </button>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";AAEA,SAASA,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,iBAAiB;;;;AAS9C,OAAM,MAAOC,yBAAyB;EAKpCC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAJjB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,SAAS,GAAW,OAAO;IAC3B,KAAAC,OAAO,GAAY,KAAK;EAEI;EAErCC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACH,OAAO,EAAE;MAChB,IAAI,CAACD,MAAM,CAACK,QAAQ,CAAC,CAAC,WAAW,IAAI,CAACJ,OAAO,WAAW,CAAC,CAAC;IAC5D;EACF;EAEAK,MAAMA,CAAA;IACJ,IAAI,CAACN,MAAM,CAACK,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnC;;6BAfWP,yBAAyB;;mCAAzBA,0BAAyB,EAAAS,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA;AAAA;;QAAzBZ,0BAAyB;EAAAa,SAAA;EAAAC,MAAA;IAAAX,OAAA;IAAAC,SAAA;IAAAC,OAAA;EAAA;EAAAU,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCVlCX,EAFJ,CAAAa,cAAA,aAAoC,aACA,aACD;MAAAb,EAAA,CAAAc,MAAA,aAAC;MAAAd,EAAA,CAAAe,YAAA,EAAM;MACtCf,EAAA,CAAAa,cAAA,SAAI;MAAAb,EAAA,CAAAc,MAAA,GAA2B;MAAAd,EAAA,CAAAe,YAAA,EAAK;MACpCf,EAAA,CAAAa,cAAA,WAA2B;MAAAb,EAAA,CAAAc,MAAA,uEAAgE;MAAAd,EAAA,CAAAe,YAAA,EAAI;MAC/Ff,EAAA,CAAAa,cAAA,WAAuB;MAAAb,EAAA,CAAAc,MAAA,6EAAsE;MAAAd,EAAA,CAAAe,YAAA,EAAI;MAG/Ff,EADF,CAAAa,cAAA,cAA4B,iBAC+B;MAAzBb,EAAA,CAAAgB,UAAA,mBAAAC,4DAAA;QAAA,OAASL,GAAA,CAAAf,YAAA,EAAc;MAAA,EAAC;MACtDG,EAAA,CAAAkB,SAAA,mBAA6C;MAC7ClB,EAAA,CAAAc,MAAA,kBACF;MAAAd,EAAA,CAAAe,YAAA,EAAS;MACTf,EAAA,CAAAa,cAAA,iBAA+C;MAAnBb,EAAA,CAAAgB,UAAA,mBAAAG,4DAAA;QAAA,OAASP,GAAA,CAAAb,MAAA,EAAQ;MAAA,EAAC;MAC5CC,EAAA,CAAAkB,SAAA,mBAA+C;MAC/ClB,EAAA,CAAAc,MAAA,wBACF;MAGNd,EAHM,CAAAe,YAAA,EAAS,EACL,EACF,EACF;;;MAfEf,EAAA,CAAAoB,SAAA,GAA2B;MAA3BpB,EAAA,CAAAqB,kBAAA,gBAAAT,GAAA,CAAAjB,SAAA,MAA2B;;;iBDOvBN,WAAW,EAAAiC,EAAA,CAAAC,OAAA,EAAEjC,YAAY;EAAAkC,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}