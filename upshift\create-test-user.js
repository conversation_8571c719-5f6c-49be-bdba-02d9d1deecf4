﻿
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://tobifepmbrrrvshpvrqa.supabase.co';
const supabaseServiceRoleKey = 'YOUR_SUPABASE_SERVICE_ROLE_KEY'; 

const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

async function createTestUser() {
  try {
    const email = '<EMAIL>';
    const password = 'password123';
    
    
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true 
    });
    
    if (authError) {
      return;
    }
    
    
    const now = new Date();
    const oneMonthLater = new Date();
    oneMonthLater.setMonth(now.getMonth() + 1);
    
    const userData = {
      id: authData.user.id,
      email: email,
      username: 'testuser',
      name: 'Test User',
      profile_picture: null,
      registration_date: now,
      last_login: now,
      active: true,
      level: 1,
      strength_xp: 0,
      money_xp: 0,
      health_xp: 0,
      knowledge_xp: 0,
      title: '🥚 Beginner',
      bio: '',
      plan: 'monthly',
      start_of_current_plan: now,
      end_of_current_plan: oneMonthLater,
      auto_renew: true,
      sidequests_switch: true,
      show_celebration: true,
      celebration_name: 'Another Day, Another W',
      celebration_description: "You've completed all your quests for today. Keep up the great work!",
      celebration_emoji: '🎉',
      subscription_status: 'email marketing'
    };
    
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .insert(userData)
      .select();
    
    if (profileError) {
      return;
    }
    
    
  } catch (error) {
  }
}

createTestUser();
