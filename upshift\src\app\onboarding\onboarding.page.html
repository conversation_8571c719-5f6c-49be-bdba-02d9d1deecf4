﻿

<ion-content [fullscreen]="true" class="ion-padding">
  <ion-header >
    <ion-toolbar>
      <ion-row class="head">
        <ion-icon name="arrow-back-outline" (click)="goBack()"></ion-icon>
        <ion-progress-bar *ngIf="!quizEnded" [value]="progress" color="success"></ion-progress-bar>
        <ion-badge *ngIf="!quizEnded">EN</ion-badge>
      </ion-row>
    </ion-toolbar>
  </ion-header>

  <ion-grid *ngIf="currentQuestion">
    <ion-row class="questions" *ngIf="!quizEnded">
      <h1 class="gradient-text upshift-title">Question #{{ currentQuestion.id }}</h1>
      <ion-text class="ion-margin-top ion-margin-bottom dark-text question">
        {{ currentQuestion.question }}
      </ion-text>
      <ion-list lines="none">
        <ion-button
          *ngFor="let answerItem of currentQuestion.answers"
          class="ion-margin-top answer-btn"
          size="large"
          (click)="clickedAnswer(answerItem.answer); animateButton($event)"
        >
          <span>
            <ion-badge class="ion-align-items-center ion-justify-content-center">
              <ion-text>{{ answerItem.id + 1 }}</ion-text>
            </ion-badge> {{ answerItem.answer }}
          </span>
        </ion-button>
      </ion-list>
    </ion-row>
    <ion-row *ngIf="quizEnded" class="referral-section">
      <div class="referral-content">
        <h1 class="upshift-title">Do you have a referral code?</h1>
        <ion-text class="dark-text can-skip">
          You can skip this step.
        </ion-text>
        <form (ngSubmit)="applyReferral($event)" class="referral-form">
          <div class="input-wrapper">
            <div class="input-with-button">
              <input type="text" placeholder="Referral code"
                     [(ngModel)]="referralCode"
                     (ngModelChange)="onReferralCodeChange($event)"
                     (input)="onInputChange($event)"
                     [disabled]="isCheckingUsername"
                     name="referralCode"
                     class="referral-input">
              <button type="submit" class="apply-button" [disabled]="isCheckingUsername || !referralCode.trim()">
                <ion-icon name="checkmark-outline"></ion-icon>
              </button>
            </div>
          </div>
        </form>

        <!-- Error message -->
        <div class="error-message" *ngIf="usernameError">
          {{ usernameError }}
        </div>

        <!-- Loading indicator -->
        <div class="loading-indicator" *ngIf="isCheckingUsername">
          Checking referral code...
        </div>

        <!-- Display applied referral code -->
        <div class="applied-code" *ngIf="appliedUsername">
          <div class="code-badge">
            <span>{{ appliedUsername }}</span>
            <button class="remove-code" (click)="removeReferral($event)">×</button>
          </div>
        </div>
      </div>
    </ion-row>
  </ion-grid>
</ion-content>

<ion-footer *ngIf="quizEnded" class="ion-no-border">
  <div class="button-group">
    <ion-button class="skip-btn answer-btn" (click)="skipReferral($event)">
      Skip
    </ion-button>
    <ion-button class="submit-btn answer-btn" (click)="continueToPrice($event)" *ngIf="appliedUsername">
      Continue
    </ion-button>
  </div>
</ion-footer>
