﻿import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, forkJoin } from 'rxjs';
import { map, catchError, shareReplay } from 'rxjs/operators';

export interface LevelData {
  Level: number;
  XP_per_category: number;
}

export enum EntityType {
  USER = 'user',
  GROUP = 'group'
}

@Injectable({
  providedIn: 'root'
})
export class XpService {
  private userLevelDataCache$: Observable<LevelData[]> | null = null;
  private groupLevelDataCache$: Observable<LevelData[]> | null = null;

  constructor(private http: HttpClient) { }

  getLevelData(entityType: EntityType = EntityType.USER): Observable<LevelData[]> {
    if (entityType === EntityType.USER) {
      if (!this.userLevelDataCache$) {
        this.userLevelDataCache$ = this.http.get<LevelData[]>('assets/data/levelsAndXp.json').pipe(
          catchError(error => {
            return of([]);
          }),
          shareReplay(1)
        );
      }
      return this.userLevelDataCache$;
    } else {
      if (!this.groupLevelDataCache$) {
        this.groupLevelDataCache$ = this.http.get<LevelData[]>('assets/data/groupLevelsAndXp.json').pipe(
          catchError(error => {
            return of([]);
          }),
          shareReplay(1)
        );
      }
      return this.groupLevelDataCache$;
    }
  }

  getRequiredXpForNextLevel(currentLevel: number, entityType: EntityType = EntityType.USER): Observable<number> {
    return this.getLevelData(entityType).pipe(
      map(levelData => {
        if (currentLevel >= 100) {
          return 0; 
        }

        const nextLevelData = levelData.find(item => item.Level === currentLevel + 1);
        return nextLevelData ? nextLevelData.XP_per_category : 0;
      })
    );
  }

  calculateXpProgress(entity: any, entityType: EntityType = EntityType.USER): Observable<any> {
    if (!entity) {
      return of(null);
    }

    return this.getRequiredXpForNextLevel(entity.level, entityType).pipe(
      map(requiredXp => {
        const categories = [
          { name: 'Strength', field: 'strength_xp', icon: '💪', color: '#FF5733' },
          { name: 'Money', field: 'money_xp', icon: '💰', color: '#33FF57' },
          { name: 'Health', field: 'health_xp', icon: '❤️', color: '#3357FF' },
          { name: 'Knowledge', field: 'knowledge_xp', icon: '🧠', color: '#F033FF' }
        ];

        for (const category of categories) {
          const currentXp = entity[category.field] || 0;
          category['current_xp'] = currentXp;
          category['required_xp'] = requiredXp;

          if (requiredXp > 0) {
            category['progress'] = Math.min(100, Math.round((currentXp / requiredXp) * 100));
          } else {
            category['progress'] = 100;
          }
        }

        return {
          categories,
          next_level: entity.level < 100 ? entity.level + 1 : 'Max'
        };
      })
    );
  }

  getUserTitle(level: number): string {
    if (level >= 100) return 'Professional Upshifter';
    if (level >= 90) return 'Ultra Human';
    if (level >= 75) return 'Indestructible';
    if (level >= 60) return 'Elite Operator';
    if (level >= 50) return 'Peak Performer';
    if (level >= 40) return 'Master of Consistency';
    if (level >= 35) return 'High Performer';
    if (level >= 30) return 'Disciplined Machine';
    if (level >= 25) return 'Hardcore';
    if (level >= 20) return 'Nonchalant';
    if (level >= 15) return 'Monk';
    if (level >= 10) return 'Warrior';
    if (level >= 5) return 'Newbie';
    return 'Beginner';
  }
}
