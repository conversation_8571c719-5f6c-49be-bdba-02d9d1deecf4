{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _CreateGroupPage;\nimport { inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { RouterModule, Router } from '@angular/router';\nimport { GroupService } from '../../../services/group.service';\nimport { SupabaseService } from '../../../services/supabase.service';\nimport { HttpClient } from '@angular/common/http';\nimport { debounceTime, distinctUntilChanged, Subject } from 'rxjs';\nimport { Geolocation } from '@capacitor/geolocation';\nimport { EmojiInputDirective } from '../../../directives/emoji-input.directive';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/router\";\nconst _c0 = () => [\"/groups\"];\nfunction CreateGroupPage_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.errorMessage);\n  }\n}\nfunction CreateGroupPage_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nameError);\n  }\n}\nfunction CreateGroupPage_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtext(1, \"Checking availability...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateGroupPage_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1, \"Group name is available!\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateGroupPage_option_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tz_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", tz_r2.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tz_r2.label);\n  }\n}\nfunction CreateGroupPage_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelement(1, \"ion-spinner\", 32);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateGroupPage_span_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Detecting your timezone...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateGroupPage_span_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Select the timezone for this group. Cannot be changed later.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateGroupPage_option_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const country_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", country_r3.code);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(country_r3.name);\n  }\n}\nfunction CreateGroupPage_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelement(1, \"ion-spinner\", 32);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateGroupPage_span_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Detecting your location...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateGroupPage_span_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Select the country for this group.\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class CreateGroupPage {\n  constructor() {\n    this.userId = null;\n    this.group = {\n      name: '',\n      emoji: '👥',\n      timezone: 'UTC',\n      country: '',\n      level: 0,\n      strength_xp: 0,\n      money_xp: 0,\n      health_xp: 0,\n      knowledge_xp: 0,\n      enable_sidequests: true\n    };\n    this.timezones = [{\n      value: 'UTC',\n      label: '(UTC) Coordinated Universal Time'\n    }, {\n      value: 'America/Anchorage',\n      label: '(UTC-8) Anchorage'\n    }, {\n      value: 'America/Los_Angeles',\n      label: '(UTC-7) Los Angeles'\n    }, {\n      value: 'America/Phoenix',\n      label: '(UTC-7) Phoenix'\n    }, {\n      value: 'America/Vancouver',\n      label: '(UTC-7) Vancouver'\n    }, {\n      value: 'America/Denver',\n      label: '(UTC-6) Denver'\n    }, {\n      value: 'America/Mexico_City',\n      label: '(UTC-6) Mexico City'\n    }, {\n      value: 'America/San_Salvador',\n      label: '(UTC-6) San Salvador'\n    }, {\n      value: 'America/Managua',\n      label: '(UTC-6) Managua'\n    }, {\n      value: 'America/Tegucigalpa',\n      label: '(UTC-6) Tegucigalpa'\n    }, {\n      value: 'America/Bogota',\n      label: '(UTC-5) Bogota'\n    }, {\n      value: 'America/Chicago',\n      label: '(UTC-5) Chicago'\n    }, {\n      value: 'America/Lima',\n      label: '(UTC-5) Lima'\n    }, {\n      value: 'America/Panama',\n      label: '(UTC-5) Panama City'\n    }, {\n      value: 'America/Caracas',\n      label: '(UTC-4) Caracas'\n    }, {\n      value: 'America/New_York',\n      label: '(UTC-4) New York'\n    }, {\n      value: 'America/Santiago',\n      label: '(UTC-4) Santiago'\n    }, {\n      value: 'America/Toronto',\n      label: '(UTC-4) Toronto'\n    }, {\n      value: 'America/Santo_Domingo',\n      label: '(UTC-4) Santo Domingo'\n    }, {\n      value: 'America/Port_of_Spain',\n      label: '(UTC-4) Port of Spain'\n    }, {\n      value: 'America/Argentina/Buenos_Aires',\n      label: '(UTC-3) Buenos Aires'\n    }, {\n      value: 'America/Sao_Paulo',\n      label: '(UTC-3) Sao Paulo'\n    }, {\n      value: 'America/Montevideo',\n      label: '(UTC-3) Montevideo'\n    }, {\n      value: 'America/Asuncion',\n      label: '(UTC-3) Asuncion'\n    }, {\n      value: 'Atlantic/Azores',\n      label: '(UTC) Azores'\n    }, {\n      value: 'Atlantic/Reykjavik',\n      label: '(UTC) Reykjavik'\n    }, {\n      value: 'Africa/Casablanca',\n      label: '(UTC+1) Casablanca'\n    }, {\n      value: 'Europe/Dublin',\n      label: '(UTC+1) Dublin'\n    }, {\n      value: 'Africa/Lagos',\n      label: '(UTC+1) Lagos'\n    }, {\n      value: 'Europe/Lisbon',\n      label: '(UTC+1) Lisbon'\n    }, {\n      value: 'Europe/London',\n      label: '(UTC+1) London'\n    }, {\n      value: 'Africa/Tunis',\n      label: '(UTC+1) Tunis'\n    }, {\n      value: 'Africa/Algiers',\n      label: '(UTC+1) Algiers'\n    }, {\n      value: 'Europe/Amsterdam',\n      label: '(UTC+2) Amsterdam'\n    }, {\n      value: 'Europe/Belgrade',\n      label: '(UTC+2) Belgrade'\n    }, {\n      value: 'Europe/Berlin',\n      label: '(UTC+2) Berlin'\n    }, {\n      value: 'Europe/Bratislava',\n      label: '(UTC+2) Bratislava'\n    }, {\n      value: 'Europe/Brussels',\n      label: '(UTC+2) Brussels'\n    }, {\n      value: 'Europe/Budapest',\n      label: '(UTC+2) Budapest'\n    }, {\n      value: 'Europe/Copenhagen',\n      label: '(UTC+2) Copenhagen'\n    }, {\n      value: 'Africa/Cairo',\n      label: '(UTC+2) Cairo'\n    }, {\n      value: 'Europe/Madrid',\n      label: '(UTC+2) Madrid'\n    }, {\n      value: 'Europe/Oslo',\n      label: '(UTC+2) Oslo'\n    }, {\n      value: 'Europe/Paris',\n      label: '(UTC+2) Paris'\n    }, {\n      value: 'Europe/Prague',\n      label: '(UTC+2) Prague'\n    }, {\n      value: 'Europe/Rome',\n      label: '(UTC+2) Rome'\n    }, {\n      value: 'Europe/Stockholm',\n      label: '(UTC+2) Stockholm'\n    }, {\n      value: 'Europe/Vienna',\n      label: '(UTC+2) Vienna'\n    }, {\n      value: 'Europe/Warsaw',\n      label: '(UTC+2) Warsaw'\n    }, {\n      value: 'Europe/Zurich',\n      label: '(UTC+2) Zurich'\n    }, {\n      value: 'Africa/Nairobi',\n      label: '(UTC+3) Nairobi'\n    }, {\n      value: 'Europe/Athens',\n      label: '(UTC+3) Athens'\n    }, {\n      value: 'Asia/Baghdad',\n      label: '(UTC+3) Baghdad'\n    }, {\n      value: 'Europe/Helsinki',\n      label: '(UTC+3) Helsinki'\n    }, {\n      value: 'Europe/Istanbul',\n      label: '(UTC+3) Istanbul'\n    }, {\n      value: 'Asia/Jerusalem',\n      label: '(UTC+3) Jerusalem'\n    }, {\n      value: 'Europe/Kiev',\n      label: '(UTC+3) Kiev'\n    }, {\n      value: 'Asia/Kuwait',\n      label: '(UTC+3) Kuwait City'\n    }, {\n      value: 'Europe/Moscow',\n      label: '(UTC+3) Moscow'\n    }, {\n      value: 'Asia/Riyadh',\n      label: '(UTC+3) Riyadh'\n    }, {\n      value: 'Asia/Qatar',\n      label: '(UTC+3) Doha'\n    }, {\n      value: 'Asia/Tehran',\n      label: '(UTC+3:30) Tehran'\n    }, {\n      value: 'Asia/Dubai',\n      label: '(UTC+4) Dubai'\n    }, {\n      value: 'Asia/Muscat',\n      label: '(UTC+4) Muscat'\n    }, {\n      value: 'Asia/Baku',\n      label: '(UTC+4) Baku'\n    }, {\n      value: 'Asia/Yerevan',\n      label: '(UTC+4) Yerevan'\n    }, {\n      value: 'Asia/Kabul',\n      label: '(UTC+4:30) Kabul'\n    }, {\n      value: 'Asia/Karachi',\n      label: '(UTC+5) Karachi'\n    }, {\n      value: 'Asia/Tashkent',\n      label: '(UTC+5) Tashkent'\n    }, {\n      value: 'Asia/Kolkata',\n      label: '(UTC+5:30) New Delhi'\n    }, {\n      value: 'Asia/Colombo',\n      label: '(UTC+5:30) Colombo'\n    }, {\n      value: 'Asia/Kathmandu',\n      label: '(UTC+5:45) Kathmandu'\n    }, {\n      value: 'Asia/Dhaka',\n      label: '(UTC+6) Dhaka'\n    }, {\n      value: 'Asia/Almaty',\n      label: '(UTC+6) Almaty'\n    }, {\n      value: 'Asia/Yangon',\n      label: '(UTC+6:30) Yangon'\n    }, {\n      value: 'Asia/Bangkok',\n      label: '(UTC+7) Bangkok'\n    }, {\n      value: 'Asia/Jakarta',\n      label: '(UTC+7) Jakarta'\n    }, {\n      value: 'Asia/Hanoi',\n      label: '(UTC+7) Hanoi'\n    }, {\n      value: 'Asia/Phnom_Penh',\n      label: '(UTC+7) Phnom Penh'\n    }, {\n      value: 'Asia/Vientiane',\n      label: '(UTC+7) Vientiane'\n    }, {\n      value: 'Asia/Hong_Kong',\n      label: '(UTC+8) Hong Kong'\n    }, {\n      value: 'Asia/Beijing',\n      label: '(UTC+8) Beijing'\n    }, {\n      value: 'Asia/Shanghai',\n      label: '(UTC+8) Shanghai'\n    }, {\n      value: 'Asia/Singapore',\n      label: '(UTC+8) Singapore'\n    }, {\n      value: 'Asia/Taipei',\n      label: '(UTC+8) Taipei'\n    }, {\n      value: 'Asia/Kuala_Lumpur',\n      label: '(UTC+8) Kuala Lumpur'\n    }, {\n      value: 'Asia/Manila',\n      label: '(UTC+8) Manila'\n    }, {\n      value: 'Asia/Ulaanbaatar',\n      label: '(UTC+8) Ulaanbaatar'\n    }, {\n      value: 'Asia/Seoul',\n      label: '(UTC+9) Seoul'\n    }, {\n      value: 'Asia/Tokyo',\n      label: '(UTC+9) Tokyo'\n    }, {\n      value: 'Asia/Pyongyang',\n      label: '(UTC+9) Pyongyang'\n    }, {\n      value: 'Australia/Darwin',\n      label: '(UTC+9:30) Darwin'\n    }, {\n      value: 'Australia/Adelaide',\n      label: '(UTC+9:30) Adelaide'\n    }, {\n      value: 'Australia/Brisbane',\n      label: '(UTC+10) Brisbane'\n    }, {\n      value: 'Australia/Canberra',\n      label: '(UTC+10) Canberra'\n    }, {\n      value: 'Australia/Melbourne',\n      label: '(UTC+10) Melbourne'\n    }, {\n      value: 'Australia/Sydney',\n      label: '(UTC+10) Sydney'\n    }, {\n      value: 'Pacific/Guam',\n      label: '(UTC+10) Guam'\n    }, {\n      value: 'Pacific/Port_Moresby',\n      label: '(UTC+10) Port Moresby'\n    }, {\n      value: 'Pacific/Noumea',\n      label: '(UTC+11) Noumea'\n    }, {\n      value: 'Pacific/Auckland',\n      label: '(UTC+12) Auckland'\n    }, {\n      value: 'Pacific/Fiji',\n      label: '(UTC+12) Suva'\n    }];\n    this.countries = [{\n      name: 'Afghanistan',\n      code: 'AF'\n    }, {\n      name: 'Åland Islands',\n      code: 'AX'\n    }, {\n      name: 'Albania',\n      code: 'AL'\n    }, {\n      name: 'Algeria',\n      code: 'DZ'\n    }, {\n      name: 'American Samoa',\n      code: 'AS'\n    }, {\n      name: 'Andorra',\n      code: 'AD'\n    }, {\n      name: 'Angola',\n      code: 'AO'\n    }, {\n      name: 'Anguilla',\n      code: 'AI'\n    }, {\n      name: 'Antarctica',\n      code: 'AQ'\n    }, {\n      name: 'Antigua and Barbuda',\n      code: 'AG'\n    }, {\n      name: 'Argentina',\n      code: 'AR'\n    }, {\n      name: 'Armenia',\n      code: 'AM'\n    }, {\n      name: 'Aruba',\n      code: 'AW'\n    }, {\n      name: 'Australia',\n      code: 'AU'\n    }, {\n      name: 'Austria',\n      code: 'AT'\n    }, {\n      name: 'Azerbaijan',\n      code: 'AZ'\n    }, {\n      name: 'Bahamas',\n      code: 'BS'\n    }, {\n      name: 'Bahrain',\n      code: 'BH'\n    }, {\n      name: 'Bangladesh',\n      code: 'BD'\n    }, {\n      name: 'Barbados',\n      code: 'BB'\n    }, {\n      name: 'Belarus',\n      code: 'BY'\n    }, {\n      name: 'Belgium',\n      code: 'BE'\n    }, {\n      name: 'Belize',\n      code: 'BZ'\n    }, {\n      name: 'Benin',\n      code: 'BJ'\n    }, {\n      name: 'Bermuda',\n      code: 'BM'\n    }, {\n      name: 'Bhutan',\n      code: 'BT'\n    }, {\n      name: 'Bolivia',\n      code: 'BO'\n    }, {\n      name: 'Bosnia and Herzegovina',\n      code: 'BA'\n    }, {\n      name: 'Botswana',\n      code: 'BW'\n    }, {\n      name: 'Bouvet Island',\n      code: 'BV'\n    }, {\n      name: 'Brazil',\n      code: 'BR'\n    }, {\n      name: 'British Indian Ocean Territory',\n      code: 'IO'\n    }, {\n      name: 'Brunei Darussalam',\n      code: 'BN'\n    }, {\n      name: 'Bulgaria',\n      code: 'BG'\n    }, {\n      name: 'Burkina Faso',\n      code: 'BF'\n    }, {\n      name: 'Burundi',\n      code: 'BI'\n    }, {\n      name: 'Cambodia',\n      code: 'KH'\n    }, {\n      name: 'Cameroon',\n      code: 'CM'\n    }, {\n      name: 'Canada',\n      code: 'CA'\n    }, {\n      name: 'Cape Verde',\n      code: 'CV'\n    }, {\n      name: 'Cayman Islands',\n      code: 'KY'\n    }, {\n      name: 'Central African Republic',\n      code: 'CF'\n    }, {\n      name: 'Chad',\n      code: 'TD'\n    }, {\n      name: 'Chile',\n      code: 'CL'\n    }, {\n      name: 'China',\n      code: 'CN'\n    }, {\n      name: 'Christmas Island',\n      code: 'CX'\n    }, {\n      name: 'Cocos (Keeling) Islands',\n      code: 'CC'\n    }, {\n      name: 'Colombia',\n      code: 'CO'\n    }, {\n      name: 'Comoros',\n      code: 'KM'\n    }, {\n      name: 'Congo',\n      code: 'CG'\n    }, {\n      name: 'Congo, The Democratic Republic of the',\n      code: 'CD'\n    }, {\n      name: 'Cook Islands',\n      code: 'CK'\n    }, {\n      name: 'Costa Rica',\n      code: 'CR'\n    }, {\n      name: 'Cote D\\'Ivoire',\n      code: 'CI'\n    }, {\n      name: 'Croatia',\n      code: 'HR'\n    }, {\n      name: 'Cuba',\n      code: 'CU'\n    }, {\n      name: 'Cyprus',\n      code: 'CY'\n    }, {\n      name: 'Czech Republic',\n      code: 'CZ'\n    }, {\n      name: 'Denmark',\n      code: 'DK'\n    }, {\n      name: 'Djibouti',\n      code: 'DJ'\n    }, {\n      name: 'Dominica',\n      code: 'DM'\n    }, {\n      name: 'Dominican Republic',\n      code: 'DO'\n    }, {\n      name: 'Ecuador',\n      code: 'EC'\n    }, {\n      name: 'Egypt',\n      code: 'EG'\n    }, {\n      name: 'El Salvador',\n      code: 'SV'\n    }, {\n      name: 'Equatorial Guinea',\n      code: 'GQ'\n    }, {\n      name: 'Eritrea',\n      code: 'ER'\n    }, {\n      name: 'Estonia',\n      code: 'EE'\n    }, {\n      name: 'Ethiopia',\n      code: 'ET'\n    }, {\n      name: 'Falkland Islands (Malvinas)',\n      code: 'FK'\n    }, {\n      name: 'Faroe Islands',\n      code: 'FO'\n    }, {\n      name: 'Fiji',\n      code: 'FJ'\n    }, {\n      name: 'Finland',\n      code: 'FI'\n    }, {\n      name: 'France',\n      code: 'FR'\n    }, {\n      name: 'French Guiana',\n      code: 'GF'\n    }, {\n      name: 'French Polynesia',\n      code: 'PF'\n    }, {\n      name: 'French Southern Territories',\n      code: 'TF'\n    }, {\n      name: 'Gabon',\n      code: 'GA'\n    }, {\n      name: 'Gambia',\n      code: 'GM'\n    }, {\n      name: 'Georgia',\n      code: 'GE'\n    }, {\n      name: 'Germany',\n      code: 'DE'\n    }, {\n      name: 'Ghana',\n      code: 'GH'\n    }, {\n      name: 'Gibraltar',\n      code: 'GI'\n    }, {\n      name: 'Greece',\n      code: 'GR'\n    }, {\n      name: 'Greenland',\n      code: 'GL'\n    }, {\n      name: 'Grenada',\n      code: 'GD'\n    }, {\n      name: 'Guadeloupe',\n      code: 'GP'\n    }, {\n      name: 'Guam',\n      code: 'GU'\n    }, {\n      name: 'Guatemala',\n      code: 'GT'\n    }, {\n      name: 'Guernsey',\n      code: 'GG'\n    }, {\n      name: 'Guinea',\n      code: 'GN'\n    }, {\n      name: 'Guinea-Bissau',\n      code: 'GW'\n    }, {\n      name: 'Guyana',\n      code: 'GY'\n    }, {\n      name: 'Haiti',\n      code: 'HT'\n    }, {\n      name: 'Heard Island and Mcdonald Islands',\n      code: 'HM'\n    }, {\n      name: 'Holy See (Vatican City State)',\n      code: 'VA'\n    }, {\n      name: 'Honduras',\n      code: 'HN'\n    }, {\n      name: 'Hong Kong',\n      code: 'HK'\n    }, {\n      name: 'Hungary',\n      code: 'HU'\n    }, {\n      name: 'Iceland',\n      code: 'IS'\n    }, {\n      name: 'India',\n      code: 'IN'\n    }, {\n      name: 'Indonesia',\n      code: 'ID'\n    }, {\n      name: 'Iran, Islamic Republic Of',\n      code: 'IR'\n    }, {\n      name: 'Iraq',\n      code: 'IQ'\n    }, {\n      name: 'Ireland',\n      code: 'IE'\n    }, {\n      name: 'Isle of Man',\n      code: 'IM'\n    }, {\n      name: 'Israel',\n      code: 'IL'\n    }, {\n      name: 'Italy',\n      code: 'IT'\n    }, {\n      name: 'Jamaica',\n      code: 'JM'\n    }, {\n      name: 'Japan',\n      code: 'JP'\n    }, {\n      name: 'Jersey',\n      code: 'JE'\n    }, {\n      name: 'Jordan',\n      code: 'JO'\n    }, {\n      name: 'Kazakhstan',\n      code: 'KZ'\n    }, {\n      name: 'Kenya',\n      code: 'KE'\n    }, {\n      name: 'Kiribati',\n      code: 'KI'\n    }, {\n      name: 'Korea, Democratic People\\'s Republic of',\n      code: 'KP'\n    }, {\n      name: 'Korea, Republic of',\n      code: 'KR'\n    }, {\n      name: 'Kuwait',\n      code: 'KW'\n    }, {\n      name: 'Kyrgyzstan',\n      code: 'KG'\n    }, {\n      name: 'Lao People\\'s Democratic Republic',\n      code: 'LA'\n    }, {\n      name: 'Latvia',\n      code: 'LV'\n    }, {\n      name: 'Lebanon',\n      code: 'LB'\n    }, {\n      name: 'Lesotho',\n      code: 'LS'\n    }, {\n      name: 'Liberia',\n      code: 'LR'\n    }, {\n      name: 'Libyan Arab Jamahiriya',\n      code: 'LY'\n    }, {\n      name: 'Liechtenstein',\n      code: 'LI'\n    }, {\n      name: 'Lithuania',\n      code: 'LT'\n    }, {\n      name: 'Luxembourg',\n      code: 'LU'\n    }, {\n      name: 'Macao',\n      code: 'MO'\n    }, {\n      name: 'Macedonia, The Former Yugoslav Republic of',\n      code: 'MK'\n    }, {\n      name: 'Madagascar',\n      code: 'MG'\n    }, {\n      name: 'Malawi',\n      code: 'MW'\n    }, {\n      name: 'Malaysia',\n      code: 'MY'\n    }, {\n      name: 'Maldives',\n      code: 'MV'\n    }, {\n      name: 'Mali',\n      code: 'ML'\n    }, {\n      name: 'Malta',\n      code: 'MT'\n    }, {\n      name: 'Marshall Islands',\n      code: 'MH'\n    }, {\n      name: 'Martinique',\n      code: 'MQ'\n    }, {\n      name: 'Mauritania',\n      code: 'MR'\n    }, {\n      name: 'Mauritius',\n      code: 'MU'\n    }, {\n      name: 'Mayotte',\n      code: 'YT'\n    }, {\n      name: 'Mexico',\n      code: 'MX'\n    }, {\n      name: 'Micronesia, Federated States of',\n      code: 'FM'\n    }, {\n      name: 'Moldova, Republic of',\n      code: 'MD'\n    }, {\n      name: 'Monaco',\n      code: 'MC'\n    }, {\n      name: 'Mongolia',\n      code: 'MN'\n    }, {\n      name: 'Montenegro',\n      code: 'ME'\n    }, {\n      name: 'Montserrat',\n      code: 'MS'\n    }, {\n      name: 'Morocco',\n      code: 'MA'\n    }, {\n      name: 'Mozambique',\n      code: 'MZ'\n    }, {\n      name: 'Myanmar',\n      code: 'MM'\n    }, {\n      name: 'Namibia',\n      code: 'NA'\n    }, {\n      name: 'Nauru',\n      code: 'NR'\n    }, {\n      name: 'Nepal',\n      code: 'NP'\n    }, {\n      name: 'Netherlands',\n      code: 'NL'\n    }, {\n      name: 'Netherlands Antilles',\n      code: 'AN'\n    }, {\n      name: 'New Caledonia',\n      code: 'NC'\n    }, {\n      name: 'New Zealand',\n      code: 'NZ'\n    }, {\n      name: 'Nicaragua',\n      code: 'NI'\n    }, {\n      name: 'Niger',\n      code: 'NE'\n    }, {\n      name: 'Nigeria',\n      code: 'NG'\n    }, {\n      name: 'Niue',\n      code: 'NU'\n    }, {\n      name: 'Norfolk Island',\n      code: 'NF'\n    }, {\n      name: 'Northern Mariana Islands',\n      code: 'MP'\n    }, {\n      name: 'Norway',\n      code: 'NO'\n    }, {\n      name: 'Oman',\n      code: 'OM'\n    }, {\n      name: 'Pakistan',\n      code: 'PK'\n    }, {\n      name: 'Palau',\n      code: 'PW'\n    }, {\n      name: 'Palestinian Territory, Occupied',\n      code: 'PS'\n    }, {\n      name: 'Panama',\n      code: 'PA'\n    }, {\n      name: 'Papua New Guinea',\n      code: 'PG'\n    }, {\n      name: 'Paraguay',\n      code: 'PY'\n    }, {\n      name: 'Peru',\n      code: 'PE'\n    }, {\n      name: 'Philippines',\n      code: 'PH'\n    }, {\n      name: 'Pitcairn',\n      code: 'PN'\n    }, {\n      name: 'Poland',\n      code: 'PL'\n    }, {\n      name: 'Portugal',\n      code: 'PT'\n    }, {\n      name: 'Puerto Rico',\n      code: 'PR'\n    }, {\n      name: 'Qatar',\n      code: 'QA'\n    }, {\n      name: 'Reunion',\n      code: 'RE'\n    }, {\n      name: 'Romania',\n      code: 'RO'\n    }, {\n      name: 'Russian Federation',\n      code: 'RU'\n    }, {\n      name: 'Rwanda',\n      code: 'RW'\n    }, {\n      name: 'Saint Helena',\n      code: 'SH'\n    }, {\n      name: 'Saint Kitts and Nevis',\n      code: 'KN'\n    }, {\n      name: 'Saint Lucia',\n      code: 'LC'\n    }, {\n      name: 'Saint Pierre and Miquelon',\n      code: 'PM'\n    }, {\n      name: 'Saint Vincent and the Grenadines',\n      code: 'VC'\n    }, {\n      name: 'Samoa',\n      code: 'WS'\n    }, {\n      name: 'San Marino',\n      code: 'SM'\n    }, {\n      name: 'Sao Tome and Principe',\n      code: 'ST'\n    }, {\n      name: 'Saudi Arabia',\n      code: 'SA'\n    }, {\n      name: 'Senegal',\n      code: 'SN'\n    }, {\n      name: 'Serbia',\n      code: 'RS'\n    }, {\n      name: 'Seychelles',\n      code: 'SC'\n    }, {\n      name: 'Sierra Leone',\n      code: 'SL'\n    }, {\n      name: 'Singapore',\n      code: 'SG'\n    }, {\n      name: 'Slovakia',\n      code: 'SK'\n    }, {\n      name: 'Slovenia',\n      code: 'SI'\n    }, {\n      name: 'Solomon Islands',\n      code: 'SB'\n    }, {\n      name: 'Somalia',\n      code: 'SO'\n    }, {\n      name: 'South Africa',\n      code: 'ZA'\n    }, {\n      name: 'South Georgia and the South Sandwich Islands',\n      code: 'GS'\n    }, {\n      name: 'Spain',\n      code: 'ES'\n    }, {\n      name: 'Sri Lanka',\n      code: 'LK'\n    }, {\n      name: 'Sudan',\n      code: 'SD'\n    }, {\n      name: 'Suriname',\n      code: 'SR'\n    }, {\n      name: 'Svalbard and Jan Mayen',\n      code: 'SJ'\n    }, {\n      name: 'Swaziland',\n      code: 'SZ'\n    }, {\n      name: 'Sweden',\n      code: 'SE'\n    }, {\n      name: 'Switzerland',\n      code: 'CH'\n    }, {\n      name: 'Syrian Arab Republic',\n      code: 'SY'\n    }, {\n      name: 'Taiwan',\n      code: 'TW'\n    }, {\n      name: 'Tajikistan',\n      code: 'TJ'\n    }, {\n      name: 'Tanzania, United Republic of',\n      code: 'TZ'\n    }, {\n      name: 'Thailand',\n      code: 'TH'\n    }, {\n      name: 'Timor-Leste',\n      code: 'TL'\n    }, {\n      name: 'Togo',\n      code: 'TG'\n    }, {\n      name: 'Tokelau',\n      code: 'TK'\n    }, {\n      name: 'Tonga',\n      code: 'TO'\n    }, {\n      name: 'Trinidad and Tobago',\n      code: 'TT'\n    }, {\n      name: 'Tunisia',\n      code: 'TN'\n    }, {\n      name: 'Turkey',\n      code: 'TR'\n    }, {\n      name: 'Turkmenistan',\n      code: 'TM'\n    }, {\n      name: 'Turks and Caicos Islands',\n      code: 'TC'\n    }, {\n      name: 'Tuvalu',\n      code: 'TV'\n    }, {\n      name: 'Uganda',\n      code: 'UG'\n    }, {\n      name: 'Ukraine',\n      code: 'UA'\n    }, {\n      name: 'United Arab Emirates',\n      code: 'AE'\n    }, {\n      name: 'United Kingdom',\n      code: 'GB'\n    }, {\n      name: 'United States',\n      code: 'US'\n    }, {\n      name: 'United States Minor Outlying Islands',\n      code: 'UM'\n    }, {\n      name: 'Uruguay',\n      code: 'UY'\n    }, {\n      name: 'Uzbekistan',\n      code: 'UZ'\n    }, {\n      name: 'Vanuatu',\n      code: 'VU'\n    }, {\n      name: 'Venezuela',\n      code: 'VE'\n    }, {\n      name: 'Vietnam',\n      code: 'VN'\n    }, {\n      name: 'Virgin Islands, British',\n      code: 'VG'\n    }, {\n      name: 'Virgin Islands, U.S.',\n      code: 'VI'\n    }, {\n      name: 'Wallis and Futuna',\n      code: 'WF'\n    }, {\n      name: 'Western Sahara',\n      code: 'EH'\n    }, {\n      name: 'Yemen',\n      code: 'YE'\n    }, {\n      name: 'Zambia',\n      code: 'ZM'\n    }, {\n      name: 'Zimbabwe',\n      code: 'ZW'\n    }];\n    this.isSubmitting = false;\n    this.errorMessage = '';\n    this.nameError = '';\n    this.nameChecking = false;\n    this.nameAvailable = false;\n    this.isLoadingCountry = true;\n    this.isLoadingTimezone = true;\n    this.nameCheckSubject = new Subject();\n    this.nameCheckSubscription = null;\n    this.supabaseService = inject(SupabaseService);\n    this.groupService = inject(GroupService);\n    this.router = inject(Router);\n    this.http = inject(HttpClient);\n    this.userCoordinates = null;\n    this.cityCoordinates = {\n      'Anchorage': {\n        lat: 61.2181,\n        lng: -149.9003\n      },\n      'Los Angeles': {\n        lat: 34.0522,\n        lng: -118.2437\n      },\n      'Phoenix': {\n        lat: 33.4484,\n        lng: -112.0740\n      },\n      'Vancouver': {\n        lat: 49.2827,\n        lng: -123.1207\n      },\n      'Denver': {\n        lat: 39.7392,\n        lng: -104.9903\n      },\n      'Mexico City': {\n        lat: 19.4326,\n        lng: -99.1332\n      },\n      'San Salvador': {\n        lat: 13.6929,\n        lng: -89.2182\n      },\n      'Managua': {\n        lat: 12.1149,\n        lng: -86.2362\n      },\n      'Tegucigalpa': {\n        lat: 14.0723,\n        lng: -87.1921\n      },\n      'Bogota': {\n        lat: 4.7110,\n        lng: -74.0721\n      },\n      'Chicago': {\n        lat: 41.8781,\n        lng: -87.6298\n      },\n      'Lima': {\n        lat: 12.0464,\n        lng: -77.0428\n      },\n      'Panama City': {\n        lat: 8.9824,\n        lng: -79.5199\n      },\n      'Caracas': {\n        lat: 10.4806,\n        lng: -66.9036\n      },\n      'New York': {\n        lat: 40.7128,\n        lng: -74.0060\n      },\n      'Santiago': {\n        lat: -33.4489,\n        lng: -70.6693\n      },\n      'Toronto': {\n        lat: 43.6532,\n        lng: -79.3832\n      },\n      'Santo Domingo': {\n        lat: 18.4861,\n        lng: -69.9312\n      },\n      'Port of Spain': {\n        lat: 10.6550,\n        lng: -61.5020\n      },\n      'Buenos Aires': {\n        lat: -34.6037,\n        lng: -58.3816\n      },\n      'Sao Paulo': {\n        lat: -23.5505,\n        lng: -46.6333\n      },\n      'Montevideo': {\n        lat: -34.9011,\n        lng: -56.1915\n      },\n      'Asuncion': {\n        lat: -25.2637,\n        lng: -57.5759\n      },\n      'Azores': {\n        lat: 37.7412,\n        lng: -25.6756\n      },\n      'Reykjavik': {\n        lat: 64.1466,\n        lng: -21.9426\n      },\n      'Casablanca': {\n        lat: 33.5731,\n        lng: -7.5898\n      },\n      'Dublin': {\n        lat: 53.3498,\n        lng: -6.2603\n      },\n      'Lagos': {\n        lat: 6.5244,\n        lng: 3.3792\n      },\n      'Lisbon': {\n        lat: 38.7223,\n        lng: -9.1393\n      },\n      'London': {\n        lat: 51.5074,\n        lng: -0.1278\n      },\n      'Tunis': {\n        lat: 36.8065,\n        lng: 10.1815\n      },\n      'Algiers': {\n        lat: 36.7538,\n        lng: 3.0588\n      },\n      'Amsterdam': {\n        lat: 52.3676,\n        lng: 4.9041\n      },\n      'Belgrade': {\n        lat: 44.7866,\n        lng: 20.4489\n      },\n      'Berlin': {\n        lat: 52.5200,\n        lng: 13.4050\n      },\n      'Bratislava': {\n        lat: 48.1486,\n        lng: 17.1077\n      },\n      'Brussels': {\n        lat: 50.8503,\n        lng: 4.3517\n      },\n      'Budapest': {\n        lat: 47.4979,\n        lng: 19.0402\n      },\n      'Copenhagen': {\n        lat: 55.6761,\n        lng: 12.5683\n      },\n      'Cairo': {\n        lat: 30.0444,\n        lng: 31.2357\n      },\n      'Madrid': {\n        lat: 40.4168,\n        lng: -3.7038\n      },\n      'Oslo': {\n        lat: 59.9139,\n        lng: 10.7522\n      },\n      'Paris': {\n        lat: 48.8566,\n        lng: 2.3522\n      },\n      'Prague': {\n        lat: 50.0755,\n        lng: 14.4378\n      },\n      'Rome': {\n        lat: 41.9028,\n        lng: 12.4964\n      },\n      'Stockholm': {\n        lat: 59.3293,\n        lng: 18.0686\n      },\n      'Vienna': {\n        lat: 48.2082,\n        lng: 16.3738\n      },\n      'Warsaw': {\n        lat: 52.2297,\n        lng: 21.0122\n      },\n      'Zurich': {\n        lat: 47.3769,\n        lng: 8.5417\n      },\n      'Nairobi': {\n        lat: -1.2921,\n        lng: 36.8219\n      },\n      'Athens': {\n        lat: 37.9838,\n        lng: 23.7275\n      },\n      'Baghdad': {\n        lat: 33.3152,\n        lng: 44.3661\n      },\n      'Helsinki': {\n        lat: 60.1699,\n        lng: 24.9384\n      },\n      'Istanbul': {\n        lat: 41.0082,\n        lng: 28.9784\n      },\n      'Jerusalem': {\n        lat: 31.7683,\n        lng: 35.2137\n      },\n      'Kiev': {\n        lat: 50.4501,\n        lng: 30.5234\n      },\n      'Kuwait City': {\n        lat: 29.3759,\n        lng: 47.9774\n      },\n      'Moscow': {\n        lat: 55.7558,\n        lng: 37.6173\n      },\n      'Riyadh': {\n        lat: 24.7136,\n        lng: 46.6753\n      },\n      'Doha': {\n        lat: 25.2854,\n        lng: 51.5310\n      },\n      'Tehran': {\n        lat: 35.6892,\n        lng: 51.3890\n      },\n      'Dubai': {\n        lat: 25.2048,\n        lng: 55.2708\n      },\n      'Muscat': {\n        lat: 23.5880,\n        lng: 58.3829\n      },\n      'Baku': {\n        lat: 40.4093,\n        lng: 49.8671\n      },\n      'Yerevan': {\n        lat: 40.1792,\n        lng: 44.4991\n      },\n      'Kabul': {\n        lat: 34.5553,\n        lng: 69.2075\n      },\n      'Karachi': {\n        lat: 24.8607,\n        lng: 67.0011\n      },\n      'Tashkent': {\n        lat: 41.2995,\n        lng: 69.2401\n      },\n      'New Delhi': {\n        lat: 28.6139,\n        lng: 77.2090\n      },\n      'Kolkata': {\n        lat: 22.5726,\n        lng: 88.3639\n      },\n      'Colombo': {\n        lat: 6.9271,\n        lng: 79.8612\n      },\n      'Kathmandu': {\n        lat: 27.7172,\n        lng: 85.3240\n      },\n      'Dhaka': {\n        lat: 23.8103,\n        lng: 90.4125\n      },\n      'Almaty': {\n        lat: 43.2220,\n        lng: 76.8512\n      },\n      'Yangon': {\n        lat: 16.8661,\n        lng: 96.1951\n      },\n      'Bangkok': {\n        lat: 13.7563,\n        lng: 100.5018\n      },\n      'Jakarta': {\n        lat: 6.2088,\n        lng: 106.8456\n      },\n      'Hanoi': {\n        lat: 21.0278,\n        lng: 105.8342\n      },\n      'Ho Chi Minh': {\n        lat: 10.8231,\n        lng: 106.6297\n      },\n      'Phnom Penh': {\n        lat: 11.5564,\n        lng: 104.9282\n      },\n      'Vientiane': {\n        lat: 17.9757,\n        lng: 102.6331\n      },\n      'Hong Kong': {\n        lat: 22.3193,\n        lng: 114.1694\n      },\n      'Beijing': {\n        lat: 39.9042,\n        lng: 116.4074\n      },\n      'Shanghai': {\n        lat: 31.2304,\n        lng: 121.4737\n      },\n      'Singapore': {\n        lat: 1.3521,\n        lng: 103.8198\n      },\n      'Taipei': {\n        lat: 25.0330,\n        lng: 121.5654\n      },\n      'Kuala Lumpur': {\n        lat: 3.1390,\n        lng: 101.6869\n      },\n      'Manila': {\n        lat: 14.5995,\n        lng: 120.9842\n      },\n      'Ulaanbaatar': {\n        lat: 47.8864,\n        lng: 106.9057\n      },\n      'Seoul': {\n        lat: 37.5665,\n        lng: 126.9780\n      },\n      'Tokyo': {\n        lat: 35.6762,\n        lng: 139.6503\n      },\n      'Pyongyang': {\n        lat: 39.0392,\n        lng: 125.7625\n      },\n      'Darwin': {\n        lat: -12.4634,\n        lng: 130.8456\n      },\n      'Adelaide': {\n        lat: -34.9285,\n        lng: 138.6007\n      },\n      'Brisbane': {\n        lat: -27.4698,\n        lng: 153.0251\n      },\n      'Canberra': {\n        lat: -35.2809,\n        lng: 149.1300\n      },\n      'Melbourne': {\n        lat: -37.8136,\n        lng: 144.9631\n      },\n      'Sydney': {\n        lat: -33.8688,\n        lng: 151.2093\n      },\n      'Perth': {\n        lat: -31.9505,\n        lng: 115.8605\n      },\n      'Guam': {\n        lat: 13.4443,\n        lng: 144.7937\n      },\n      'Port Moresby': {\n        lat: -9.4438,\n        lng: 147.1803\n      },\n      'Noumea': {\n        lat: -22.2711,\n        lng: 166.4416\n      },\n      'Auckland': {\n        lat: -36.8509,\n        lng: 174.7645\n      },\n      'Suva': {\n        lat: -18.1416,\n        lng: 178.4419\n      }\n    };\n  }\n  ngOnInit() {\n    this.supabaseService.currentUser$.subscribe(user => {\n      if (user) {\n        this.userId = user.id;\n      } else {\n        this.router.navigate(['/login']);\n      }\n    });\n    this.nameCheckSubscription = this.nameCheckSubject.pipe(debounceTime(500), distinctUntilChanged()).subscribe(name => {\n      this.checkGroupNameExists(name);\n    });\n    this.countries.sort((a, b) => a.name.localeCompare(b.name));\n    this.detectUserCountry();\n  }\n  ngOnDestroy() {\n    if (this.nameCheckSubscription) {\n      this.nameCheckSubscription.unsubscribe();\n    }\n  }\n  detectUserCountry() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.isLoadingCountry = true;\n      try {\n        const position = yield Geolocation.getCurrentPosition({\n          enableHighAccuracy: true,\n          timeout: 10000\n        });\n        if (position && position.coords) {\n          const {\n            latitude,\n            longitude\n          } = position.coords;\n          _this.userCoordinates = {\n            latitude,\n            longitude\n          };\n          _this.http.get(`https:\n          .subscribe({\n            next: (data) => {\n              if (data && data.countryCode) {\n                this.group.country = data.countryCode;\n\n                const foundCountry = this.countries.find(c => c.code === data.countryCode);\n                if (!foundCountry) {\n                  this.group.country = 'US';\n                }\n\n                this.detectTimezone(latitude, longitude);\n              } else {\n                this.fallbackToIpGeolocation();\n              }\n              this.isLoadingCountry = false;\n            },\n            error: (error) => {\n              this.fallbackToIpGeolocation();\n            }\n          });\n      } else {\n        this.fallbackToIpGeolocation();\n      }\n    } catch (error) {\n      this.fallbackToIpGeolocation();\n    }\n  }\n\n  private detectTimezone(latitude: number, longitude: number) {\n    this.http.get<any>(`, https, subscribe({\n            next: data => {\n              if (data && data.status === 'OK') {\n                const gmtOffset = data.gmtOffset / 3600;\n                if (data.zoneName) {\n                  const foundTimezone = _this.timezones.find(tz => tz.value === data.zoneName);\n                  if (foundTimezone) {\n                    _this.group.timezone = foundTimezone.value;\n                    return;\n                  }\n                }\n                if (data.zoneName) {\n                  const similarTimezone = _this.findSimilarTimezone(data.zoneName);\n                  if (similarTimezone) {\n                    _this.group.timezone = similarTimezone;\n                    return;\n                  }\n                }\n                _this.findTimezoneByOffset(gmtOffset);\n              }\n            },\n            error: error => {\n              _this.detectBrowserTimezone();\n            }\n          }));\n        }\n      } finally {}\n    })();\n  }\n  findTimezoneByOffset(offsetHours) {\n    const offsetSign = offsetHours >= 0 ? '+' : '-';\n    const offsetAbs = Math.abs(offsetHours);\n    const offsetInt = Math.floor(offsetAbs);\n    const offsetFraction = offsetAbs - offsetInt;\n    let offsetStr;\n    if (offsetFraction === 0) {\n      if (offsetHours === 0) {\n        offsetStr = 'UTC';\n      } else {\n        offsetStr = `UTC${offsetSign}${offsetInt}`;\n      }\n    } else if (offsetFraction === 0.5) {\n      offsetStr = `UTC${offsetSign}${offsetInt}:30`;\n    } else if (offsetFraction === 0.75) {\n      offsetStr = `UTC${offsetSign}${offsetInt}:45`;\n    } else if (offsetFraction === 0.25) {\n      offsetStr = `UTC${offsetSign}${offsetInt}:15`;\n    } else {\n      offsetStr = `UTC${offsetSign}${Math.round(offsetAbs)}`;\n    }\n    const timezonesByOffset = this.timezones.filter(tz => tz.label.includes(`(${offsetStr})`) || tz.label.includes(`(${offsetStr.replace('+', ' +')})`));\n    if (timezonesByOffset.length > 0) {\n      if (this.userCoordinates) {\n        const nearestCity = this.findNearestCityInTimezones(this.userCoordinates.latitude, this.userCoordinates.longitude, timezonesByOffset);\n        if (nearestCity) {\n          this.group.timezone = nearestCity.value;\n          return;\n        }\n      }\n      const userCountry = this.group.country;\n      if (userCountry) {\n        const countryCityPatterns = {\n          'US': ['New York', 'Los Angeles', 'Chicago', 'Denver', 'Phoenix', 'Anchorage'],\n          'CA': ['Toronto', 'Vancouver'],\n          'MX': ['Mexico City'],\n          'SV': ['San Salvador'],\n          'NI': ['Managua'],\n          'HN': ['Tegucigalpa'],\n          'CO': ['Bogota'],\n          'PE': ['Lima'],\n          'PA': ['Panama City'],\n          'VE': ['Caracas'],\n          'CL': ['Santiago'],\n          'DO': ['Santo Domingo'],\n          'TT': ['Port of Spain'],\n          'AR': ['Buenos Aires'],\n          'BR': ['Sao Paulo'],\n          'UY': ['Montevideo'],\n          'PY': ['Asuncion'],\n          'PT': ['Lisbon', 'Azores'],\n          'IS': ['Reykjavik'],\n          'MA': ['Casablanca'],\n          'IE': ['Dublin'],\n          'NG': ['Lagos'],\n          'GB': ['London'],\n          'TN': ['Tunis'],\n          'DZ': ['Algiers'],\n          'NL': ['Amsterdam'],\n          'RS': ['Belgrade'],\n          'DE': ['Berlin'],\n          'SK': ['Bratislava'],\n          'BE': ['Brussels'],\n          'HU': ['Budapest'],\n          'DK': ['Copenhagen'],\n          'ES': ['Madrid'],\n          'NO': ['Oslo'],\n          'FR': ['Paris'],\n          'CZ': ['Prague'],\n          'IT': ['Rome'],\n          'SE': ['Stockholm'],\n          'AT': ['Vienna'],\n          'PL': ['Warsaw'],\n          'CH': ['Zurich'],\n          'KE': ['Nairobi'],\n          'GR': ['Athens'],\n          'IQ': ['Baghdad'],\n          'FI': ['Helsinki'],\n          'TR': ['Istanbul'],\n          'IL': ['Jerusalem'],\n          'UA': ['Kiev'],\n          'KW': ['Kuwait City'],\n          'RU': ['Moscow'],\n          'SA': ['Riyadh'],\n          'QA': ['Doha'],\n          'IR': ['Tehran'],\n          'AE': ['Dubai'],\n          'OM': ['Muscat'],\n          'AZ': ['Baku'],\n          'AM': ['Yerevan'],\n          'AF': ['Kabul'],\n          'PK': ['Karachi'],\n          'UZ': ['Tashkent'],\n          'IN': ['New Delhi', 'Kolkata'],\n          'LK': ['Colombo'],\n          'NP': ['Kathmandu'],\n          'BD': ['Dhaka'],\n          'KZ': ['Almaty'],\n          'MM': ['Yangon'],\n          'TH': ['Bangkok'],\n          'ID': ['Jakarta'],\n          'VN': ['Hanoi', 'Ho Chi Minh'],\n          'KH': ['Phnom Penh'],\n          'LA': ['Vientiane'],\n          'HK': ['Hong Kong'],\n          'CN': ['Beijing', 'Shanghai'],\n          'SG': ['Singapore'],\n          'TW': ['Taipei'],\n          'MY': ['Kuala Lumpur'],\n          'PH': ['Manila'],\n          'MN': ['Ulaanbaatar'],\n          'KR': ['Seoul'],\n          'JP': ['Tokyo'],\n          'KP': ['Pyongyang'],\n          'AU': ['Sydney', 'Melbourne', 'Brisbane', 'Perth', 'Adelaide', 'Darwin', 'Canberra'],\n          'GU': ['Guam'],\n          'PG': ['Port Moresby'],\n          'NC': ['Noumea'],\n          'NZ': ['Auckland'],\n          'FJ': ['Suva'],\n          'EG': ['Cairo'],\n          'ZA': ['Johannesburg']\n        };\n        if (countryCityPatterns[userCountry]) {\n          for (const cityPattern of countryCityPatterns[userCountry]) {\n            const countryTimezone = timezonesByOffset.find(tz => tz.label.toLowerCase().includes(cityPattern.toLowerCase()));\n            if (countryTimezone) {\n              this.group.timezone = countryTimezone.value;\n              return;\n            }\n          }\n        }\n      }\n      const majorCities = ['London', 'Berlin', 'Paris', 'New York', 'Tokyo', 'Sydney', 'Moscow', 'Dubai', 'Singapore', 'Hong Kong', 'Los Angeles', 'Chicago', 'Toronto'];\n      for (const city of majorCities) {\n        const cityTimezone = timezonesByOffset.find(tz => tz.label.toLowerCase().includes(city.toLowerCase()));\n        if (cityTimezone) {\n          this.group.timezone = cityTimezone.value;\n          return;\n        }\n      }\n      this.group.timezone = timezonesByOffset[0].value;\n    } else {\n      this.group.timezone = 'UTC';\n    }\n  }\n  findNearestCityInTimezones(lat, lng, timezones) {\n    if (!lat || !lng || timezones.length === 0) return null;\n    let nearestCity = null;\n    let shortestDistance = Number.MAX_VALUE;\n    for (const timezone of timezones) {\n      const cityMatch = timezone.label.match(/\\)\\s+(.+)$/);\n      if (!cityMatch) continue;\n      const cityName = cityMatch[1];\n      let cityCoords = null;\n      if (this.cityCoordinates[cityName]) {\n        cityCoords = this.cityCoordinates[cityName];\n      } else {\n        for (const knownCity in this.cityCoordinates) {\n          if (cityName.includes(knownCity) || knownCity.includes(cityName)) {\n            cityCoords = this.cityCoordinates[knownCity];\n            break;\n          }\n        }\n      }\n      if (cityCoords) {\n        const distance = this.calculateDistance(lat, lng, cityCoords.lat, cityCoords.lng);\n        if (distance < shortestDistance) {\n          shortestDistance = distance;\n          nearestCity = timezone;\n        }\n      }\n    }\n    return nearestCity;\n  }\n  calculateDistance(lat1, lon1, lat2, lon2) {\n    const R = 6371;\n    const dLat = this.deg2rad(lat2 - lat1);\n    const dLon = this.deg2rad(lon2 - lon1);\n    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n    const distance = R * c;\n    return distance;\n  }\n  deg2rad(deg) {\n    return deg * (Math.PI / 180);\n  }\n  detectBrowserTimezone() {\n    try {\n      const offsetMinutes = new Date().getTimezoneOffset();\n      const offsetHours = -offsetMinutes / 60;\n      this.findTimezoneByOffset(offsetHours);\n    } catch (error) {\n      this.group.timezone = 'UTC';\n    }\n  }\n  findSimilarTimezone(zoneName) {\n    const parts = zoneName.split('/');\n    if (parts.length >= 1) {\n      const continent = parts[0];\n      const similarTimezone = this.timezones.find(tz => tz.value.startsWith(continent + '/'));\n      if (similarTimezone) {\n        return similarTimezone.value;\n      }\n    }\n    return null;\n  }\n  fallbackToIpGeolocation() {\n    this.http.get('https://ipapi.co/json/').subscribe({\n      next: data => {\n        if (data && data.country_code) {\n          this.group.country = data.country_code;\n          const foundCountry = this.countries.find(c => c.code === data.country_code);\n          if (!foundCountry) {\n            this.group.country = 'US';\n          }\n          if (data.timezone) {\n            const foundTimezone = this.timezones.find(tz => tz.value === data.timezone);\n            if (foundTimezone) {\n              this.group.timezone = foundTimezone.value;\n            } else {\n              const similarTimezone = this.findSimilarTimezone(data.timezone);\n              if (similarTimezone) {\n                this.group.timezone = similarTimezone;\n              } else if (data.utc_offset) {\n                const offsetHours = parseInt(data.utc_offset.substring(0, 3)) + parseInt(data.utc_offset.substring(3, 5)) / 60;\n                this.findTimezoneByOffset(offsetHours);\n              } else {\n                this.detectBrowserTimezone();\n              }\n            }\n          } else {\n            this.detectBrowserTimezone();\n          }\n        } else {\n          this.group.country = 'US';\n          this.detectBrowserTimezone();\n        }\n        this.isLoadingCountry = false;\n      },\n      error: error => {\n        this.group.country = 'US';\n        this.detectBrowserTimezone();\n        this.isLoadingCountry = false;\n      }\n    });\n  }\n  checkGroupName() {\n    var _this$group$name;\n    const name = (_this$group$name = this.group.name) === null || _this$group$name === void 0 ? void 0 : _this$group$name.trim();\n    this.nameAvailable = false;\n    if (!name) {\n      this.nameError = '';\n      this.nameChecking = false;\n      return;\n    }\n    this.nameChecking = true;\n    this.nameCheckSubject.next(name);\n  }\n  checkGroupNameExists(name) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!name) {\n        _this2.nameError = '';\n        _this2.nameChecking = false;\n        return;\n      }\n      try {\n        const {\n          data,\n          error\n        } = yield _this2.supabaseService.getClient().from('groups').select('id').eq('name', name).limit(1);\n        _this2.nameChecking = false;\n        if (error) {\n          _this2.nameError = 'Error checking group name availability';\n          return;\n        }\n        if (data && data.length > 0) {\n          _this2.nameError = 'This group name is already taken';\n          _this2.nameAvailable = false;\n        } else {\n          _this2.nameError = '';\n          _this2.nameAvailable = true;\n        }\n      } catch (error) {\n        _this2.nameChecking = false;\n        _this2.nameError = 'Error checking group name availability';\n      }\n    })();\n  }\n  createGroup() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.userId) {\n        _this3.errorMessage = 'You must be logged in to create a group';\n        return;\n      }\n      if (!_this3.group.name || !_this3.group.emoji || !_this3.group.timezone || !_this3.group.country) {\n        _this3.errorMessage = 'Please fill in all required fields';\n        return;\n      }\n      if (_this3.nameError) {\n        _this3.errorMessage = 'Please choose a different group name';\n        return;\n      }\n      _this3.isSubmitting = true;\n      _this3.errorMessage = '';\n      try {\n        const newGroup = {\n          name: _this3.group.name,\n          emoji: _this3.group.emoji,\n          timezone: _this3.group.timezone,\n          country: _this3.group.country,\n          admin_id: _this3.userId,\n          level: 0,\n          strength_xp: 0,\n          money_xp: 0,\n          health_xp: 0,\n          knowledge_xp: 0,\n          enable_sidequests: true\n        };\n        const groupId = yield _this3.groupService.createGroup(newGroup);\n        _this3.router.navigate(['/groups', groupId]);\n      } catch (error) {\n        _this3.errorMessage = error.message || 'Failed to create group. Please try again.';\n      } finally {\n        _this3.isSubmitting = false;\n      }\n    })();\n  }\n}\n_CreateGroupPage = CreateGroupPage;\n_CreateGroupPage.ɵfac = function CreateGroupPage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CreateGroupPage)();\n};\n_CreateGroupPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _CreateGroupPage,\n  selectors: [[\"app-create-group\"]],\n  decls: 44,\n  vars: 22,\n  consts: [[1, \"container\"], [1, \"logo\"], [\"src\", \"assets/images/upshift_icon_mini.svg\", \"alt\", \"Upshift\"], [1, \"back-link\", 3, \"routerLink\"], [\"class\", \"messages\", 4, \"ngIf\"], [1, \"group-form\", 3, \"ngSubmit\"], [1, \"form-group\"], [\"for\", \"name\"], [1, \"input-group\"], [1, \"emoji\"], [\"type\", \"text\", \"name\", \"emoji\", \"id\", \"emoji\", \"value\", \"\\uD83D\\uDC65\", \"appEmojiInput\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"name\", \"name\", \"id\", \"name\", \"placeholder\", \"Enter group name\", \"required\", \"\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"class\", \"checking-message\", 4, \"ngIf\"], [\"class\", \"success-message\", 4, \"ngIf\"], [\"for\", \"timezone\"], [1, \"select-wrapper\"], [\"name\", \"timezone\", \"id\", \"timezone\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"loading-indicator\", 4, \"ngIf\"], [1, \"help-text\"], [4, \"ngIf\"], [\"for\", \"country\"], [\"name\", \"country\", \"id\", \"country\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"type\", \"submit\", 1, \"btn\", \"primary\", \"full-width\", 3, \"disabled\"], [1, \"messages\"], [1, \"message\", \"error\"], [1, \"error-message\"], [1, \"checking-message\"], [1, \"success-message\"], [3, \"value\"], [1, \"loading-indicator\"], [\"name\", \"dots\", \"color\", \"medium\"]],\n  template: function CreateGroupPage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\")(2, \"div\", 1);\n      i0.ɵɵelement(3, \"img\", 2);\n      i0.ɵɵelementStart(4, \"span\");\n      i0.ɵɵtext(5, \"Upshift\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(6, \"h1\");\n      i0.ɵɵtext(7, \"Create New Group\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(8, \"a\", 3);\n      i0.ɵɵtext(9, \"\\u2190 Back to groups\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(10, CreateGroupPage_div_10_Template, 3, 1, \"div\", 4);\n      i0.ɵɵelementStart(11, \"form\", 5);\n      i0.ɵɵlistener(\"ngSubmit\", function CreateGroupPage_Template_form_ngSubmit_11_listener() {\n        return ctx.createGroup();\n      });\n      i0.ɵɵelementStart(12, \"div\", 6)(13, \"label\", 7);\n      i0.ɵɵtext(14, \"Group Name\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(15, \"div\", 8)(16, \"div\", 9)(17, \"input\", 10);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function CreateGroupPage_Template_input_ngModelChange_17_listener($event) {\n        i0.ɵɵtwoWayBindingSet(ctx.group.emoji, $event) || (ctx.group.emoji = $event);\n        return $event;\n      });\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(18, \"input\", 11);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function CreateGroupPage_Template_input_ngModelChange_18_listener($event) {\n        i0.ɵɵtwoWayBindingSet(ctx.group.name, $event) || (ctx.group.name = $event);\n        return $event;\n      });\n      i0.ɵɵlistener(\"input\", function CreateGroupPage_Template_input_input_18_listener() {\n        return ctx.checkGroupName();\n      });\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(19, CreateGroupPage_div_19_Template, 2, 1, \"div\", 12)(20, CreateGroupPage_div_20_Template, 2, 0, \"div\", 13)(21, CreateGroupPage_div_21_Template, 2, 0, \"div\", 14);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(22, \"div\", 6)(23, \"label\", 15);\n      i0.ɵɵtext(24, \"Timezone\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(25, \"div\", 16)(26, \"select\", 17);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function CreateGroupPage_Template_select_ngModelChange_26_listener($event) {\n        i0.ɵɵtwoWayBindingSet(ctx.group.timezone, $event) || (ctx.group.timezone = $event);\n        return $event;\n      });\n      i0.ɵɵtemplate(27, CreateGroupPage_option_27_Template, 2, 2, \"option\", 18);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(28, CreateGroupPage_div_28_Template, 2, 0, \"div\", 19);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(29, \"div\", 20);\n      i0.ɵɵtemplate(30, CreateGroupPage_span_30_Template, 2, 0, \"span\", 21)(31, CreateGroupPage_span_31_Template, 2, 0, \"span\", 21);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(32, \"div\", 6)(33, \"label\", 22);\n      i0.ɵɵtext(34, \"Country\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(35, \"div\", 16)(36, \"select\", 23);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function CreateGroupPage_Template_select_ngModelChange_36_listener($event) {\n        i0.ɵɵtwoWayBindingSet(ctx.group.country, $event) || (ctx.group.country = $event);\n        return $event;\n      });\n      i0.ɵɵtemplate(37, CreateGroupPage_option_37_Template, 2, 2, \"option\", 18);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(38, CreateGroupPage_div_38_Template, 2, 0, \"div\", 19);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(39, \"div\", 20);\n      i0.ɵɵtemplate(40, CreateGroupPage_span_40_Template, 2, 0, \"span\", 21)(41, CreateGroupPage_span_41_Template, 2, 0, \"span\", 21);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(42, \"button\", 24);\n      i0.ɵɵtext(43);\n      i0.ɵɵelementEnd()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(21, _c0));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n      i0.ɵɵadvance(7);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.group.emoji);\n      i0.ɵɵadvance();\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.group.name);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.nameError);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.nameChecking);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.nameAvailable);\n      i0.ɵɵadvance(5);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.group.timezone);\n      i0.ɵɵproperty(\"disabled\", ctx.isLoadingCountry);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngForOf\", ctx.timezones);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.isLoadingCountry);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.isLoadingCountry);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingCountry);\n      i0.ɵɵadvance(5);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.group.country);\n      i0.ɵɵproperty(\"disabled\", ctx.isLoadingCountry);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngForOf\", ctx.countries);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.isLoadingCountry);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.isLoadingCountry);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingCountry);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"disabled\", ctx.isSubmitting || ctx.nameError);\n      i0.ɵɵadvance();\n      i0.ɵɵtextInterpolate1(\" \", ctx.isSubmitting ? \"Creating...\" : \"Create Group\", \" \");\n    }\n  },\n  dependencies: [IonicModule, i1.IonSpinner, i1.RouterLinkWithHrefDelegate, CommonModule, i2.NgForOf, i2.NgIf, FormsModule, i3.ɵNgNoValidate, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.DefaultValueAccessor, i3.SelectControlValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.NgModel, i3.NgForm, RouterModule, i4.RouterLink, EmojiInputDirective],\n  styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%];\\n\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\tvar __webpack_modules__ = ({\\n\\n\\n 472:\\n\\n\\n\\n\\n\\n (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/sass-loader/dist/cjs.js):\\\\nexpected \\\\\\\"{\\\\\\\".\\\\n  \\u2577\\\\n3 \\u2502   appearance: auto;  Pou\\u017Eije nat\\u00EDvny vzh\\u013Ead select boxu */\\\\r\\\\n  \\u2502                                                           ^\\\\n  \\u2575\\\\n  src\\\\\\\\app\\\\\\\\pages\\\\\\\\groups\\\\\\\\create-group\\\\\\\\create-group.page.scss 3:59  root stylesheet\\\");\\n\\n\\n })\\n\\n\\n \\t});\\n\\n\\n\\n \\t\\n\\n \\t// startup\\n\\n \\t// Load entry module and return exports\\n\\n \\t// This entry module doesn't tell about it's top-level declarations so it can't be inlined\\n\\n \\tvar __webpack_exports__ = {};\\n\\n \\t__webpack_modules__[472]();\\n\\n \\tresource = __webpack_exports__;\\n\\n \\t\\n\\n })()\\n;\"]\n});", "map": {"version": 3, "names": ["inject", "CommonModule", "FormsModule", "IonicModule", "RouterModule", "Router", "GroupService", "SupabaseService", "HttpClient", "debounceTime", "distinctUntilChanged", "Subject", "Geolocation", "EmojiInputDirective", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "errorMessage", "nameError", "ɵɵproperty", "tz_r2", "value", "label", "ɵɵelement", "country_r3", "code", "name", "CreateGroupPage", "constructor", "userId", "group", "emoji", "timezone", "country", "level", "strength_xp", "money_xp", "health_xp", "knowledge_xp", "enable_sidequests", "timezones", "countries", "isSubmitting", "nameChecking", "nameAvailable", "isLoadingCountry", "isLoadingTimezone", "nameCheckSubject", "nameCheckSubscription", "supabaseService", "groupService", "router", "http", "userCoordinates", "cityCoordinates", "lat", "lng", "ngOnInit", "currentUser$", "subscribe", "user", "id", "navigate", "pipe", "checkGroupNameExists", "sort", "a", "b", "localeCompare", "detectUserCountry", "ngOnDestroy", "unsubscribe", "_this", "_asyncToGenerator", "position", "getCurrentPosition", "enableHighAccuracy", "timeout", "coords", "latitude", "longitude", "get", "https", "next", "data", "status", "gmtOffset", "zoneName", "foundTimezone", "find", "tz", "similarTimezone", "findSimilarTimezone", "findTimezoneByOffset", "error", "detectBrowserTimezone", "offsetHours", "offsetSign", "offsetAbs", "Math", "abs", "offsetInt", "floor", "offsetFraction", "offsetStr", "round", "timezonesByOffset", "filter", "includes", "replace", "length", "nearestCity", "findNearestCityInTimezones", "userCountry", "countryCityPatterns", "cityPattern", "countryTimezone", "toLowerCase", "majorCities", "city", "cityTimezone", "shortestDistance", "Number", "MAX_VALUE", "cityMatch", "match", "cityName", "cityCoords", "knownCity", "distance", "calculateDistance", "lat1", "lon1", "lat2", "lon2", "R", "dLat", "deg2rad", "dLon", "sin", "cos", "c", "atan2", "sqrt", "deg", "PI", "offsetMinutes", "Date", "getTimezoneOffset", "parts", "split", "continent", "startsWith", "fallbackToIpGeolocation", "country_code", "foundCountry", "utc_offset", "parseInt", "substring", "checkGroupName", "_this$group$name", "trim", "_this2", "getClient", "from", "select", "eq", "limit", "createGroup", "_this3", "newGroup", "admin_id", "groupId", "message", "selectors", "decls", "vars", "consts", "template", "CreateGroupPage_Template", "rf", "ctx", "ɵɵtemplate", "CreateGroupPage_div_10_Template", "ɵɵlistener", "CreateGroupPage_Template_form_ngSubmit_11_listener", "ɵɵtwoWayListener", "CreateGroupPage_Template_input_ngModelChange_17_listener", "$event", "ɵɵtwoWayBindingSet", "CreateGroupPage_Template_input_ngModelChange_18_listener", "CreateGroupPage_Template_input_input_18_listener", "CreateGroupPage_div_19_Template", "CreateGroupPage_div_20_Template", "CreateGroupPage_div_21_Template", "CreateGroupPage_Template_select_ngModelChange_26_listener", "CreateGroupPage_option_27_Template", "CreateGroupPage_div_28_Template", "CreateGroupPage_span_30_Template", "CreateGroupPage_span_31_Template", "CreateGroupPage_Template_select_ngModelChange_36_listener", "CreateGroupPage_option_37_Template", "CreateGroupPage_div_38_Template", "CreateGroupPage_span_40_Template", "CreateGroupPage_span_41_Template", "ɵɵpureFunction0", "_c0", "ɵɵtwoWayProperty", "ɵɵtextInterpolate1", "i1", "Ion<PERSON><PERSON><PERSON>", "RouterLinkWithHrefDelegate", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "ɵNgNoValidate", "NgSelectOption", "ɵNgSelectMultipleOption", "DefaultValueAccessor", "SelectControlValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "NgModel", "NgForm", "i4", "RouterLink", "styles"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\groups\\create-group\\create-group.page.ts", "C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\groups\\create-group\\create-group.page.html"], "sourcesContent": ["import { Component, OnInit, inject } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { RouterModule, Router } from '@angular/router';\r\nimport { GroupService } from '../../../services/group.service';\r\nimport { Group } from '../../../models/group.model';\r\nimport { SupabaseService } from '../../../services/supabase.service';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { debounceTime, distinctUntilChanged, Subject, Subscription } from 'rxjs';\r\nimport { Geolocation } from '@capacitor/geolocation';\r\nimport { EmojiInputDirective } from '../../../directives/emoji-input.directive';\r\n\r\n@Component({\r\n  selector: 'app-create-group',\r\n  templateUrl: './create-group.page.html',\r\n  styleUrls: ['./create-group.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule, FormsModule, RouterModule, EmojiInputDirective]\r\n})\r\nexport class CreateGroupPage implements OnInit {\r\n  userId: string | null = null;\r\n\r\n  group: Partial<Group> = {\r\n    name: '',\r\n    emoji: '👥',\r\n    timezone: 'UTC', \n    country: '',\r\n    level: 0,\r\n    strength_xp: 0,\r\n    money_xp: 0,\r\n    health_xp: 0,\r\n    knowledge_xp: 0,\r\n    enable_sidequests: true\r\n  };\r\n\r\n  timezones: { value: string, label: string }[] = [\r\n    { value: 'UTC', label: '(UTC) Coordinated Universal Time' },\r\n\r\n    { value: 'America/Anchorage', label: '(UTC-8) Anchorage' },\r\n    { value: 'America/Los_Angeles', label: '(UTC-7) Los Angeles' },\r\n    { value: 'America/Phoenix', label: '(UTC-7) Phoenix' },\r\n    { value: 'America/Vancouver', label: '(UTC-7) Vancouver' },\r\n    { value: 'America/Denver', label: '(UTC-6) Denver' },\r\n    { value: 'America/Mexico_City', label: '(UTC-6) Mexico City' },\r\n    { value: 'America/San_Salvador', label: '(UTC-6) San Salvador' },\r\n    { value: 'America/Managua', label: '(UTC-6) Managua' },\r\n    { value: 'America/Tegucigalpa', label: '(UTC-6) Tegucigalpa' },\r\n    { value: 'America/Bogota', label: '(UTC-5) Bogota' },\r\n    { value: 'America/Chicago', label: '(UTC-5) Chicago' },\r\n    { value: 'America/Lima', label: '(UTC-5) Lima' },\r\n    { value: 'America/Panama', label: '(UTC-5) Panama City' },\r\n    { value: 'America/Caracas', label: '(UTC-4) Caracas' },\r\n    { value: 'America/New_York', label: '(UTC-4) New York' },\r\n    { value: 'America/Santiago', label: '(UTC-4) Santiago' },\r\n    { value: 'America/Toronto', label: '(UTC-4) Toronto' },\r\n    { value: 'America/Santo_Domingo', label: '(UTC-4) Santo Domingo' },\r\n    { value: 'America/Port_of_Spain', label: '(UTC-4) Port of Spain' },\r\n    { value: 'America/Argentina/Buenos_Aires', label: '(UTC-3) Buenos Aires' },\r\n    { value: 'America/Sao_Paulo', label: '(UTC-3) Sao Paulo' },\r\n    { value: 'America/Montevideo', label: '(UTC-3) Montevideo' },\r\n    { value: 'America/Asuncion', label: '(UTC-3) Asuncion' },\r\n\r\n    { value: 'Atlantic/Azores', label: '(UTC) Azores' },\r\n    { value: 'Atlantic/Reykjavik', label: '(UTC) Reykjavik' },\r\n    { value: 'Africa/Casablanca', label: '(UTC+1) Casablanca' },\r\n    { value: 'Europe/Dublin', label: '(UTC+1) Dublin' },\r\n    { value: 'Africa/Lagos', label: '(UTC+1) Lagos' },\r\n    { value: 'Europe/Lisbon', label: '(UTC+1) Lisbon' },\r\n    { value: 'Europe/London', label: '(UTC+1) London' },\r\n    { value: 'Africa/Tunis', label: '(UTC+1) Tunis' },\r\n    { value: 'Africa/Algiers', label: '(UTC+1) Algiers' },\r\n    { value: 'Europe/Amsterdam', label: '(UTC+2) Amsterdam' },\r\n    { value: 'Europe/Belgrade', label: '(UTC+2) Belgrade' },\r\n    { value: 'Europe/Berlin', label: '(UTC+2) Berlin' },\r\n    { value: 'Europe/Bratislava', label: '(UTC+2) Bratislava' },\r\n    { value: 'Europe/Brussels', label: '(UTC+2) Brussels' },\r\n    { value: 'Europe/Budapest', label: '(UTC+2) Budapest' },\r\n    { value: 'Europe/Copenhagen', label: '(UTC+2) Copenhagen' },\r\n    { value: 'Africa/Cairo', label: '(UTC+2) Cairo' },\r\n    { value: 'Europe/Madrid', label: '(UTC+2) Madrid' },\r\n    { value: 'Europe/Oslo', label: '(UTC+2) Oslo' },\r\n    { value: 'Europe/Paris', label: '(UTC+2) Paris' },\r\n    { value: 'Europe/Prague', label: '(UTC+2) Prague' },\r\n    { value: 'Europe/Rome', label: '(UTC+2) Rome' },\r\n    { value: 'Europe/Stockholm', label: '(UTC+2) Stockholm' },\r\n    { value: 'Europe/Vienna', label: '(UTC+2) Vienna' },\r\n    { value: 'Europe/Warsaw', label: '(UTC+2) Warsaw' },\r\n    { value: 'Europe/Zurich', label: '(UTC+2) Zurich' },\r\n    { value: 'Africa/Nairobi', label: '(UTC+3) Nairobi' },\r\n    { value: 'Europe/Athens', label: '(UTC+3) Athens' },\r\n    { value: 'Asia/Baghdad', label: '(UTC+3) Baghdad' },\r\n    { value: 'Europe/Helsinki', label: '(UTC+3) Helsinki' },\r\n    { value: 'Europe/Istanbul', label: '(UTC+3) Istanbul' },\r\n    { value: 'Asia/Jerusalem', label: '(UTC+3) Jerusalem' },\r\n    { value: 'Europe/Kiev', label: '(UTC+3) Kiev' },\r\n    { value: 'Asia/Kuwait', label: '(UTC+3) Kuwait City' },\r\n    { value: 'Europe/Moscow', label: '(UTC+3) Moscow' },\r\n    { value: 'Asia/Riyadh', label: '(UTC+3) Riyadh' },\r\n    { value: 'Asia/Qatar', label: '(UTC+3) Doha' },\r\n    { value: 'Asia/Tehran', label: '(UTC+3:30) Tehran' },\r\n    { value: 'Asia/Dubai', label: '(UTC+4) Dubai' },\r\n    { value: 'Asia/Muscat', label: '(UTC+4) Muscat' },\r\n    { value: 'Asia/Baku', label: '(UTC+4) Baku' },\r\n    { value: 'Asia/Yerevan', label: '(UTC+4) Yerevan' },\r\n    { value: 'Asia/Kabul', label: '(UTC+4:30) Kabul' },\r\n    { value: 'Asia/Karachi', label: '(UTC+5) Karachi' },\r\n    { value: 'Asia/Tashkent', label: '(UTC+5) Tashkent' },\r\n    { value: 'Asia/Kolkata', label: '(UTC+5:30) New Delhi' },\r\n    { value: 'Asia/Colombo', label: '(UTC+5:30) Colombo' },\r\n    { value: 'Asia/Kathmandu', label: '(UTC+5:45) Kathmandu' },\r\n    { value: 'Asia/Dhaka', label: '(UTC+6) Dhaka' },\r\n    { value: 'Asia/Almaty', label: '(UTC+6) Almaty' },\r\n    { value: 'Asia/Yangon', label: '(UTC+6:30) Yangon' },\r\n    { value: 'Asia/Bangkok', label: '(UTC+7) Bangkok' },\r\n    { value: 'Asia/Jakarta', label: '(UTC+7) Jakarta' },\r\n    { value: 'Asia/Hanoi', label: '(UTC+7) Hanoi' },\r\n    { value: 'Asia/Phnom_Penh', label: '(UTC+7) Phnom Penh' },\r\n    { value: 'Asia/Vientiane', label: '(UTC+7) Vientiane' },\r\n    { value: 'Asia/Hong_Kong', label: '(UTC+8) Hong Kong' },\r\n    { value: 'Asia/Beijing', label: '(UTC+8) Beijing' },\r\n    { value: 'Asia/Shanghai', label: '(UTC+8) Shanghai' },\r\n    { value: 'Asia/Singapore', label: '(UTC+8) Singapore' },\r\n    { value: 'Asia/Taipei', label: '(UTC+8) Taipei' },\r\n    { value: 'Asia/Kuala_Lumpur', label: '(UTC+8) Kuala Lumpur' },\r\n    { value: 'Asia/Manila', label: '(UTC+8) Manila' },\r\n    { value: 'Asia/Ulaanbaatar', label: '(UTC+8) Ulaanbaatar' },\r\n    { value: 'Asia/Seoul', label: '(UTC+9) Seoul' },\r\n    { value: 'Asia/Tokyo', label: '(UTC+9) Tokyo' },\r\n    { value: 'Asia/Pyongyang', label: '(UTC+9) Pyongyang' },\r\n    { value: 'Australia/Darwin', label: '(UTC+9:30) Darwin' },\r\n    { value: 'Australia/Adelaide', label: '(UTC+9:30) Adelaide' },\r\n    { value: 'Australia/Brisbane', label: '(UTC+10) Brisbane' },\r\n    { value: 'Australia/Canberra', label: '(UTC+10) Canberra' },\r\n    { value: 'Australia/Melbourne', label: '(UTC+10) Melbourne' },\r\n    { value: 'Australia/Sydney', label: '(UTC+10) Sydney' },\r\n    { value: 'Pacific/Guam', label: '(UTC+10) Guam' },\r\n    { value: 'Pacific/Port_Moresby', label: '(UTC+10) Port Moresby' },\r\n    { value: 'Pacific/Noumea', label: '(UTC+11) Noumea' },\r\n    { value: 'Pacific/Auckland', label: '(UTC+12) Auckland' },\r\n    { value: 'Pacific/Fiji', label: '(UTC+12) Suva' }\r\n  ];\r\n\r\n  countries: { name: string, code: string }[] = [\r\n    { name: 'Afghanistan', code: 'AF' },\r\n    { name: 'Åland Islands', code: 'AX' },\r\n    { name: 'Albania', code: 'AL' },\r\n    { name: 'Algeria', code: 'DZ' },\r\n    { name: 'American Samoa', code: 'AS' },\r\n    { name: 'Andorra', code: 'AD' },\r\n    { name: 'Angola', code: 'AO' },\r\n    { name: 'Anguilla', code: 'AI' },\r\n    { name: 'Antarctica', code: 'AQ' },\r\n    { name: 'Antigua and Barbuda', code: 'AG' },\r\n    { name: 'Argentina', code: 'AR' },\r\n    { name: 'Armenia', code: 'AM' },\r\n    { name: 'Aruba', code: 'AW' },\r\n    { name: 'Australia', code: 'AU' },\r\n    { name: 'Austria', code: 'AT' },\r\n    { name: 'Azerbaijan', code: 'AZ' },\r\n    { name: 'Bahamas', code: 'BS' },\r\n    { name: 'Bahrain', code: 'BH' },\r\n    { name: 'Bangladesh', code: 'BD' },\r\n    { name: 'Barbados', code: 'BB' },\r\n    { name: 'Belarus', code: 'BY' },\r\n    { name: 'Belgium', code: 'BE' },\r\n    { name: 'Belize', code: 'BZ' },\r\n    { name: 'Benin', code: 'BJ' },\r\n    { name: 'Bermuda', code: 'BM' },\r\n    { name: 'Bhutan', code: 'BT' },\r\n    { name: 'Bolivia', code: 'BO' },\r\n    { name: 'Bosnia and Herzegovina', code: 'BA' },\r\n    { name: 'Botswana', code: 'BW' },\r\n    { name: 'Bouvet Island', code: 'BV' },\r\n    { name: 'Brazil', code: 'BR' },\r\n    { name: 'British Indian Ocean Territory', code: 'IO' },\r\n    { name: 'Brunei Darussalam', code: 'BN' },\r\n    { name: 'Bulgaria', code: 'BG' },\r\n    { name: 'Burkina Faso', code: 'BF' },\r\n    { name: 'Burundi', code: 'BI' },\r\n    { name: 'Cambodia', code: 'KH' },\r\n    { name: 'Cameroon', code: 'CM' },\r\n    { name: 'Canada', code: 'CA' },\r\n    { name: 'Cape Verde', code: 'CV' },\r\n    { name: 'Cayman Islands', code: 'KY' },\r\n    { name: 'Central African Republic', code: 'CF' },\r\n    { name: 'Chad', code: 'TD' },\r\n    { name: 'Chile', code: 'CL' },\r\n    { name: 'China', code: 'CN' },\r\n    { name: 'Christmas Island', code: 'CX' },\r\n    { name: 'Cocos (Keeling) Islands', code: 'CC' },\r\n    { name: 'Colombia', code: 'CO' },\r\n    { name: 'Comoros', code: 'KM' },\r\n    { name: 'Congo', code: 'CG' },\r\n    { name: 'Congo, The Democratic Republic of the', code: 'CD' },\r\n    { name: 'Cook Islands', code: 'CK' },\r\n    { name: 'Costa Rica', code: 'CR' },\r\n    { name: 'Cote D\\'Ivoire', code: 'CI' },\r\n    { name: 'Croatia', code: 'HR' },\r\n    { name: 'Cuba', code: 'CU' },\r\n    { name: 'Cyprus', code: 'CY' },\r\n    { name: 'Czech Republic', code: 'CZ' },\r\n    { name: 'Denmark', code: 'DK' },\r\n    { name: 'Djibouti', code: 'DJ' },\r\n    { name: 'Dominica', code: 'DM' },\r\n    { name: 'Dominican Republic', code: 'DO' },\r\n    { name: 'Ecuador', code: 'EC' },\r\n    { name: 'Egypt', code: 'EG' },\r\n    { name: 'El Salvador', code: 'SV' },\r\n    { name: 'Equatorial Guinea', code: 'GQ' },\r\n    { name: 'Eritrea', code: 'ER' },\r\n    { name: 'Estonia', code: 'EE' },\r\n    { name: 'Ethiopia', code: 'ET' },\r\n    { name: 'Falkland Islands (Malvinas)', code: 'FK' },\r\n    { name: 'Faroe Islands', code: 'FO' },\r\n    { name: 'Fiji', code: 'FJ' },\r\n    { name: 'Finland', code: 'FI' },\r\n    { name: 'France', code: 'FR' },\r\n    { name: 'French Guiana', code: 'GF' },\r\n    { name: 'French Polynesia', code: 'PF' },\r\n    { name: 'French Southern Territories', code: 'TF' },\r\n    { name: 'Gabon', code: 'GA' },\r\n    { name: 'Gambia', code: 'GM' },\r\n    { name: 'Georgia', code: 'GE' },\r\n    { name: 'Germany', code: 'DE' },\r\n    { name: 'Ghana', code: 'GH' },\r\n    { name: 'Gibraltar', code: 'GI' },\r\n    { name: 'Greece', code: 'GR' },\r\n    { name: 'Greenland', code: 'GL' },\r\n    { name: 'Grenada', code: 'GD' },\r\n    { name: 'Guadeloupe', code: 'GP' },\r\n    { name: 'Guam', code: 'GU' },\r\n    { name: 'Guatemala', code: 'GT' },\r\n    { name: 'Guernsey', code: 'GG' },\r\n    { name: 'Guinea', code: 'GN' },\r\n    { name: 'Guinea-Bissau', code: 'GW' },\r\n    { name: 'Guyana', code: 'GY' },\r\n    { name: 'Haiti', code: 'HT' },\r\n    { name: 'Heard Island and Mcdonald Islands', code: 'HM' },\r\n    { name: 'Holy See (Vatican City State)', code: 'VA' },\r\n    { name: 'Honduras', code: 'HN' },\r\n    { name: 'Hong Kong', code: 'HK' },\r\n    { name: 'Hungary', code: 'HU' },\r\n    { name: 'Iceland', code: 'IS' },\r\n    { name: 'India', code: 'IN' },\r\n    { name: 'Indonesia', code: 'ID' },\r\n    { name: 'Iran, Islamic Republic Of', code: 'IR' },\r\n    { name: 'Iraq', code: 'IQ' },\r\n    { name: 'Ireland', code: 'IE' },\r\n    { name: 'Isle of Man', code: 'IM' },\r\n    { name: 'Israel', code: 'IL' },\r\n    { name: 'Italy', code: 'IT' },\r\n    { name: 'Jamaica', code: 'JM' },\r\n    { name: 'Japan', code: 'JP' },\r\n    { name: 'Jersey', code: 'JE' },\r\n    { name: 'Jordan', code: 'JO' },\r\n    { name: 'Kazakhstan', code: 'KZ' },\r\n    { name: 'Kenya', code: 'KE' },\r\n    { name: 'Kiribati', code: 'KI' },\r\n    { name: 'Korea, Democratic People\\'s Republic of', code: 'KP' },\r\n    { name: 'Korea, Republic of', code: 'KR' },\r\n    { name: 'Kuwait', code: 'KW' },\r\n    { name: 'Kyrgyzstan', code: 'KG' },\r\n    { name: 'Lao People\\'s Democratic Republic', code: 'LA' },\r\n    { name: 'Latvia', code: 'LV' },\r\n    { name: 'Lebanon', code: 'LB' },\r\n    { name: 'Lesotho', code: 'LS' },\r\n    { name: 'Liberia', code: 'LR' },\r\n    { name: 'Libyan Arab Jamahiriya', code: 'LY' },\r\n    { name: 'Liechtenstein', code: 'LI' },\r\n    { name: 'Lithuania', code: 'LT' },\r\n    { name: 'Luxembourg', code: 'LU' },\r\n    { name: 'Macao', code: 'MO' },\r\n    { name: 'Macedonia, The Former Yugoslav Republic of', code: 'MK' },\r\n    { name: 'Madagascar', code: 'MG' },\r\n    { name: 'Malawi', code: 'MW' },\r\n    { name: 'Malaysia', code: 'MY' },\r\n    { name: 'Maldives', code: 'MV' },\r\n    { name: 'Mali', code: 'ML' },\r\n    { name: 'Malta', code: 'MT' },\r\n    { name: 'Marshall Islands', code: 'MH' },\r\n    { name: 'Martinique', code: 'MQ' },\r\n    { name: 'Mauritania', code: 'MR' },\r\n    { name: 'Mauritius', code: 'MU' },\r\n    { name: 'Mayotte', code: 'YT' },\r\n    { name: 'Mexico', code: 'MX' },\r\n    { name: 'Micronesia, Federated States of', code: 'FM' },\r\n    { name: 'Moldova, Republic of', code: 'MD' },\r\n    { name: 'Monaco', code: 'MC' },\r\n    { name: 'Mongolia', code: 'MN' },\r\n    { name: 'Montenegro', code: 'ME' },\r\n    { name: 'Montserrat', code: 'MS' },\r\n    { name: 'Morocco', code: 'MA' },\r\n    { name: 'Mozambique', code: 'MZ' },\r\n    { name: 'Myanmar', code: 'MM' },\r\n    { name: 'Namibia', code: 'NA' },\r\n    { name: 'Nauru', code: 'NR' },\r\n    { name: 'Nepal', code: 'NP' },\r\n    { name: 'Netherlands', code: 'NL' },\r\n    { name: 'Netherlands Antilles', code: 'AN' },\r\n    { name: 'New Caledonia', code: 'NC' },\r\n    { name: 'New Zealand', code: 'NZ' },\r\n    { name: 'Nicaragua', code: 'NI' },\r\n    { name: 'Niger', code: 'NE' },\r\n    { name: 'Nigeria', code: 'NG' },\r\n    { name: 'Niue', code: 'NU' },\r\n    { name: 'Norfolk Island', code: 'NF' },\r\n    { name: 'Northern Mariana Islands', code: 'MP' },\r\n    { name: 'Norway', code: 'NO' },\r\n    { name: 'Oman', code: 'OM' },\r\n    { name: 'Pakistan', code: 'PK' },\r\n    { name: 'Palau', code: 'PW' },\r\n    { name: 'Palestinian Territory, Occupied', code: 'PS' },\r\n    { name: 'Panama', code: 'PA' },\r\n    { name: 'Papua New Guinea', code: 'PG' },\r\n    { name: 'Paraguay', code: 'PY' },\r\n    { name: 'Peru', code: 'PE' },\r\n    { name: 'Philippines', code: 'PH' },\r\n    { name: 'Pitcairn', code: 'PN' },\r\n    { name: 'Poland', code: 'PL' },\r\n    { name: 'Portugal', code: 'PT' },\r\n    { name: 'Puerto Rico', code: 'PR' },\r\n    { name: 'Qatar', code: 'QA' },\r\n    { name: 'Reunion', code: 'RE' },\r\n    { name: 'Romania', code: 'RO' },\r\n    { name: 'Russian Federation', code: 'RU' },\r\n    { name: 'Rwanda', code: 'RW' },\r\n    { name: 'Saint Helena', code: 'SH' },\r\n    { name: 'Saint Kitts and Nevis', code: 'KN' },\r\n    { name: 'Saint Lucia', code: 'LC' },\r\n    { name: 'Saint Pierre and Miquelon', code: 'PM' },\r\n    { name: 'Saint Vincent and the Grenadines', code: 'VC' },\r\n    { name: 'Samoa', code: 'WS' },\r\n    { name: 'San Marino', code: 'SM' },\r\n    { name: 'Sao Tome and Principe', code: 'ST' },\r\n    { name: 'Saudi Arabia', code: 'SA' },\r\n    { name: 'Senegal', code: 'SN' },\r\n    { name: 'Serbia', code: 'RS' },\r\n    { name: 'Seychelles', code: 'SC' },\r\n    { name: 'Sierra Leone', code: 'SL' },\r\n    { name: 'Singapore', code: 'SG' },\r\n    { name: 'Slovakia', code: 'SK' },\r\n    { name: 'Slovenia', code: 'SI' },\r\n    { name: 'Solomon Islands', code: 'SB' },\r\n    { name: 'Somalia', code: 'SO' },\r\n    { name: 'South Africa', code: 'ZA' },\r\n    { name: 'South Georgia and the South Sandwich Islands', code: 'GS' },\r\n    { name: 'Spain', code: 'ES' },\r\n    { name: 'Sri Lanka', code: 'LK' },\r\n    { name: 'Sudan', code: 'SD' },\r\n    { name: 'Suriname', code: 'SR' },\r\n    { name: 'Svalbard and Jan Mayen', code: 'SJ' },\r\n    { name: 'Swaziland', code: 'SZ' },\r\n    { name: 'Sweden', code: 'SE' },\r\n    { name: 'Switzerland', code: 'CH' },\r\n    { name: 'Syrian Arab Republic', code: 'SY' },\r\n    { name: 'Taiwan', code: 'TW' },\r\n    { name: 'Tajikistan', code: 'TJ' },\r\n    { name: 'Tanzania, United Republic of', code: 'TZ' },\r\n    { name: 'Thailand', code: 'TH' },\r\n    { name: 'Timor-Leste', code: 'TL' },\r\n    { name: 'Togo', code: 'TG' },\r\n    { name: 'Tokelau', code: 'TK' },\r\n    { name: 'Tonga', code: 'TO' },\r\n    { name: 'Trinidad and Tobago', code: 'TT' },\r\n    { name: 'Tunisia', code: 'TN' },\r\n    { name: 'Turkey', code: 'TR' },\r\n    { name: 'Turkmenistan', code: 'TM' },\r\n    { name: 'Turks and Caicos Islands', code: 'TC' },\r\n    { name: 'Tuvalu', code: 'TV' },\r\n    { name: 'Uganda', code: 'UG' },\r\n    { name: 'Ukraine', code: 'UA' },\r\n    { name: 'United Arab Emirates', code: 'AE' },\r\n    { name: 'United Kingdom', code: 'GB' },\r\n    { name: 'United States', code: 'US' },\r\n    { name: 'United States Minor Outlying Islands', code: 'UM' },\r\n    { name: 'Uruguay', code: 'UY' },\r\n    { name: 'Uzbekistan', code: 'UZ' },\r\n    { name: 'Vanuatu', code: 'VU' },\r\n    { name: 'Venezuela', code: 'VE' },\r\n    { name: 'Vietnam', code: 'VN' },\r\n    { name: 'Virgin Islands, British', code: 'VG' },\r\n    { name: 'Virgin Islands, U.S.', code: 'VI' },\r\n    { name: 'Wallis and Futuna', code: 'WF' },\r\n    { name: 'Western Sahara', code: 'EH' },\r\n    { name: 'Yemen', code: 'YE' },\r\n    { name: 'Zambia', code: 'ZM' },\r\n    { name: 'Zimbabwe', code: 'ZW' }\r\n  ];\r\n\r\n  isSubmitting = false;\r\n  errorMessage = '';\r\n  nameError = '';\r\n  nameChecking = false;\r\n  nameAvailable = false;\r\n  isLoadingCountry = true;\r\n  isLoadingTimezone = true;\r\n  private nameCheckSubject = new Subject<string>();\r\n  private nameCheckSubscription: Subscription | null = null;\r\n\r\n  private supabaseService = inject(SupabaseService);\r\n  private groupService = inject(GroupService);\r\n  private router = inject(Router);\r\n  private http = inject(HttpClient);\r\n\r\n  constructor() {}\r\n\r\n  ngOnInit() {\r\n    this.supabaseService.currentUser$.subscribe(user => {\r\n      if (user) {\r\n        this.userId = user.id;\r\n      } else {\r\n        this.router.navigate(['/login']);\r\n      }\r\n    });\r\n\r\n    this.nameCheckSubscription = this.nameCheckSubject.pipe(\r\n      debounceTime(500), \n      distinctUntilChanged() \n    ).subscribe(name => {\r\n      this.checkGroupNameExists(name);\r\n    });\r\n\r\n    this.countries.sort((a, b) => a.name.localeCompare(b.name));\r\n\r\n    this.detectUserCountry();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    if (this.nameCheckSubscription) {\r\n      this.nameCheckSubscription.unsubscribe();\r\n    }\r\n  }\r\n\r\n  async detectUserCountry() {\r\n    this.isLoadingCountry = true;\r\n\r\n    try {\r\n      const position = await Geolocation.getCurrentPosition({\r\n        enableHighAccuracy: true,\r\n        timeout: 10000\r\n      });\r\n\r\n\r\n      if (position && position.coords) {\r\n        const { latitude, longitude } = position.coords;\r\n\r\n        this.userCoordinates = { latitude, longitude };\r\n\r\n        this.http.get<any>(`https:\n          .subscribe({\r\n            next: (data) => {\r\n              if (data && data.countryCode) {\r\n                this.group.country = data.countryCode;\r\n\r\n                const foundCountry = this.countries.find(c => c.code === data.countryCode);\r\n                if (!foundCountry) {\r\n                  this.group.country = 'US';\r\n                }\r\n\r\n                this.detectTimezone(latitude, longitude);\r\n              } else {\r\n                this.fallbackToIpGeolocation();\r\n              }\r\n              this.isLoadingCountry = false;\r\n            },\r\n            error: (error) => {\r\n              this.fallbackToIpGeolocation();\r\n            }\r\n          });\r\n      } else {\r\n        this.fallbackToIpGeolocation();\r\n      }\r\n    } catch (error) {\r\n      this.fallbackToIpGeolocation();\r\n    }\r\n  }\r\n\r\n  private detectTimezone(latitude: number, longitude: number) {\r\n    this.http.get<any>(`https:\n      .subscribe({\r\n        next: (data) => {\r\n          if (data && data.status === 'OK') {\r\n\r\n            const gmtOffset = data.gmtOffset / 3600; \n\r\n            if (data.zoneName) {\r\n\r\n              const foundTimezone = this.timezones.find(tz => tz.value === data.zoneName);\r\n              if (foundTimezone) {\r\n                this.group.timezone = foundTimezone.value;\r\n                return;\r\n              }\r\n            }\r\n\r\n            if (data.zoneName) {\r\n              const similarTimezone = this.findSimilarTimezone(data.zoneName);\r\n              if (similarTimezone) {\r\n                this.group.timezone = similarTimezone;\r\n                return;\r\n              }\r\n            }\r\n\r\n            this.findTimezoneByOffset(gmtOffset);\r\n          }\r\n        },\r\n        error: (error) => {\r\n          this.detectBrowserTimezone();\r\n        }\r\n      });\r\n  }\r\n\r\n  private findTimezoneByOffset(offsetHours: number): void {\r\n\r\n    const offsetSign = offsetHours >= 0 ? '+' : '-';\r\n    const offsetAbs = Math.abs(offsetHours);\r\n    const offsetInt = Math.floor(offsetAbs);\r\n    const offsetFraction = offsetAbs - offsetInt;\r\n\r\n    let offsetStr: string;\r\n\r\n    if (offsetFraction === 0) {\r\n      if (offsetHours === 0) {\r\n        offsetStr = 'UTC';\r\n      } else {\r\n        offsetStr = `UTC${offsetSign}${offsetInt}`;\r\n      }\r\n    } else if (offsetFraction === 0.5) {\r\n      offsetStr = `UTC${offsetSign}${offsetInt}:30`;\r\n    } else if (offsetFraction === 0.75) {\r\n      offsetStr = `UTC${offsetSign}${offsetInt}:45`;\r\n    } else if (offsetFraction === 0.25) {\r\n      offsetStr = `UTC${offsetSign}${offsetInt}:15`;\r\n    } else {\r\n      offsetStr = `UTC${offsetSign}${Math.round(offsetAbs)}`;\r\n    }\r\n\r\n\r\n    const timezonesByOffset = this.timezones.filter(tz =>\r\n      tz.label.includes(`(${offsetStr})`) ||\r\n      tz.label.includes(`(${offsetStr.replace('+', ' +')})`)\r\n    );\r\n\r\n    if (timezonesByOffset.length > 0) {\r\n      if (this.userCoordinates) {\r\n        const nearestCity = this.findNearestCityInTimezones(this.userCoordinates.latitude, this.userCoordinates.longitude, timezonesByOffset);\r\n        if (nearestCity) {\r\n          this.group.timezone = nearestCity.value;\r\n          return;\r\n        }\r\n      }\r\n\r\n      const userCountry = this.group.country;\r\n\r\n      if (userCountry) {\r\n        const countryCityPatterns: { [key: string]: string[] } = {\r\n          'US': ['New York', 'Los Angeles', 'Chicago', 'Denver', 'Phoenix', 'Anchorage'],\r\n          'CA': ['Toronto', 'Vancouver'],\r\n          'MX': ['Mexico City'],\r\n          'SV': ['San Salvador'],\r\n          'NI': ['Managua'],\r\n          'HN': ['Tegucigalpa'],\r\n          'CO': ['Bogota'],\r\n          'PE': ['Lima'],\r\n          'PA': ['Panama City'],\r\n          'VE': ['Caracas'],\r\n          'CL': ['Santiago'],\r\n          'DO': ['Santo Domingo'],\r\n          'TT': ['Port of Spain'],\r\n          'AR': ['Buenos Aires'],\r\n          'BR': ['Sao Paulo'],\r\n          'UY': ['Montevideo'],\r\n          'PY': ['Asuncion'],\r\n\r\n          'PT': ['Lisbon', 'Azores'],\r\n          'IS': ['Reykjavik'],\r\n          'MA': ['Casablanca'],\r\n          'IE': ['Dublin'],\r\n          'NG': ['Lagos'],\r\n          'GB': ['London'],\r\n          'TN': ['Tunis'],\r\n          'DZ': ['Algiers'],\r\n          'NL': ['Amsterdam'],\r\n          'RS': ['Belgrade'],\r\n          'DE': ['Berlin'],\r\n          'SK': ['Bratislava'],\r\n          'BE': ['Brussels'],\r\n          'HU': ['Budapest'],\r\n          'DK': ['Copenhagen'],\r\n          'ES': ['Madrid'],\r\n          'NO': ['Oslo'],\r\n          'FR': ['Paris'],\r\n          'CZ': ['Prague'],\r\n          'IT': ['Rome'],\r\n          'SE': ['Stockholm'],\r\n          'AT': ['Vienna'],\r\n          'PL': ['Warsaw'],\r\n          'CH': ['Zurich'],\r\n          'KE': ['Nairobi'],\r\n          'GR': ['Athens'],\r\n          'IQ': ['Baghdad'],\r\n          'FI': ['Helsinki'],\r\n          'TR': ['Istanbul'],\r\n          'IL': ['Jerusalem'],\r\n          'UA': ['Kiev'],\r\n          'KW': ['Kuwait City'],\r\n          'RU': ['Moscow'],\r\n          'SA': ['Riyadh'],\r\n          'QA': ['Doha'],\r\n\r\n          'IR': ['Tehran'],\r\n          'AE': ['Dubai'],\r\n          'OM': ['Muscat'],\r\n          'AZ': ['Baku'],\r\n          'AM': ['Yerevan'],\r\n          'AF': ['Kabul'],\r\n          'PK': ['Karachi'],\r\n          'UZ': ['Tashkent'],\r\n          'IN': ['New Delhi', 'Kolkata'],\r\n          'LK': ['Colombo'],\r\n          'NP': ['Kathmandu'],\r\n          'BD': ['Dhaka'],\r\n          'KZ': ['Almaty'],\r\n          'MM': ['Yangon'],\r\n          'TH': ['Bangkok'],\r\n          'ID': ['Jakarta'],\r\n          'VN': ['Hanoi', 'Ho Chi Minh'],\r\n          'KH': ['Phnom Penh'],\r\n          'LA': ['Vientiane'],\r\n          'HK': ['Hong Kong'],\r\n          'CN': ['Beijing', 'Shanghai'],\r\n          'SG': ['Singapore'],\r\n          'TW': ['Taipei'],\r\n          'MY': ['Kuala Lumpur'],\r\n          'PH': ['Manila'],\r\n          'MN': ['Ulaanbaatar'],\r\n          'KR': ['Seoul'],\r\n          'JP': ['Tokyo'],\r\n          'KP': ['Pyongyang'],\r\n\r\n          'AU': ['Sydney', 'Melbourne', 'Brisbane', 'Perth', 'Adelaide', 'Darwin', 'Canberra'],\r\n          'GU': ['Guam'],\r\n          'PG': ['Port Moresby'],\r\n          'NC': ['Noumea'],\r\n          'NZ': ['Auckland'],\r\n          'FJ': ['Suva'],\r\n\r\n          'EG': ['Cairo'],\r\n          'ZA': ['Johannesburg']\r\n        };\r\n\r\n        if (countryCityPatterns[userCountry]) {\r\n          for (const cityPattern of countryCityPatterns[userCountry]) {\r\n            const countryTimezone = timezonesByOffset.find(tz =>\r\n              tz.label.toLowerCase().includes(cityPattern.toLowerCase())\r\n            );\r\n\r\n            if (countryTimezone) {\r\n              this.group.timezone = countryTimezone.value;\r\n              return;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      const majorCities = ['London', 'Berlin', 'Paris', 'New York', 'Tokyo', 'Sydney', 'Moscow',\r\n                          'Dubai', 'Singapore', 'Hong Kong', 'Los Angeles', 'Chicago', 'Toronto'];\r\n\r\n      for (const city of majorCities) {\r\n        const cityTimezone = timezonesByOffset.find(tz =>\r\n          tz.label.toLowerCase().includes(city.toLowerCase())\r\n        );\r\n        if (cityTimezone) {\r\n          this.group.timezone = cityTimezone.value;\r\n          return;\r\n        }\r\n      }\r\n\r\n      this.group.timezone = timezonesByOffset[0].value;\r\n    } else {\r\n      this.group.timezone = 'UTC';\r\n    }\r\n  }\r\n\r\n  private userCoordinates: { latitude: number, longitude: number } | null = null;\r\n\r\n  private cityCoordinates: { [key: string]: { lat: number, lng: number } } = {\r\n    'Anchorage': { lat: 61.2181, lng: -149.9003 },\r\n    'Los Angeles': { lat: 34.0522, lng: -118.2437 },\r\n    'Phoenix': { lat: 33.4484, lng: -112.0740 },\r\n    'Vancouver': { lat: 49.2827, lng: -123.1207 },\r\n    'Denver': { lat: 39.7392, lng: -104.9903 },\r\n    'Mexico City': { lat: 19.4326, lng: -99.1332 },\r\n    'San Salvador': { lat: 13.6929, lng: -89.2182 },\r\n    'Managua': { lat: 12.1149, lng: -86.2362 },\r\n    'Tegucigalpa': { lat: 14.0723, lng: -87.1921 },\r\n    'Bogota': { lat: 4.7110, lng: -74.0721 },\r\n    'Chicago': { lat: 41.8781, lng: -87.6298 },\r\n    'Lima': { lat: 12.0464, lng: -77.0428 },\r\n    'Panama City': { lat: 8.9824, lng: -79.5199 },\r\n    'Caracas': { lat: 10.4806, lng: -66.9036 },\r\n    'New York': { lat: 40.7128, lng: -74.0060 },\r\n    'Santiago': { lat: -33.4489, lng: -70.6693 },\r\n    'Toronto': { lat: 43.6532, lng: -79.3832 },\r\n    'Santo Domingo': { lat: 18.4861, lng: -69.9312 },\r\n    'Port of Spain': { lat: 10.6550, lng: -61.5020 },\r\n    'Buenos Aires': { lat: -34.6037, lng: -58.3816 },\r\n    'Sao Paulo': { lat: -23.5505, lng: -46.6333 },\r\n    'Montevideo': { lat: -34.9011, lng: -56.1915 },\r\n    'Asuncion': { lat: -25.2637, lng: -57.5759 },\r\n\r\n    'Azores': { lat: 37.7412, lng: -25.6756 },\r\n    'Reykjavik': { lat: 64.1466, lng: -21.9426 },\r\n    'Casablanca': { lat: 33.5731, lng: -7.5898 },\r\n    'Dublin': { lat: 53.3498, lng: -6.2603 },\r\n    'Lagos': { lat: 6.5244, lng: 3.3792 },\r\n    'Lisbon': { lat: 38.7223, lng: -9.1393 },\r\n    'London': { lat: 51.5074, lng: -0.1278 },\r\n    'Tunis': { lat: 36.8065, lng: 10.1815 },\r\n    'Algiers': { lat: 36.7538, lng: 3.0588 },\r\n    'Amsterdam': { lat: 52.3676, lng: 4.9041 },\r\n    'Belgrade': { lat: 44.7866, lng: 20.4489 },\r\n    'Berlin': { lat: 52.5200, lng: 13.4050 },\r\n    'Bratislava': { lat: 48.1486, lng: 17.1077 },\r\n    'Brussels': { lat: 50.8503, lng: 4.3517 },\r\n    'Budapest': { lat: 47.4979, lng: 19.0402 },\r\n    'Copenhagen': { lat: 55.6761, lng: 12.5683 },\r\n    'Cairo': { lat: 30.0444, lng: 31.2357 },\r\n    'Madrid': { lat: 40.4168, lng: -3.7038 },\r\n    'Oslo': { lat: 59.9139, lng: 10.7522 },\r\n    'Paris': { lat: 48.8566, lng: 2.3522 },\r\n    'Prague': { lat: 50.0755, lng: 14.4378 },\r\n    'Rome': { lat: 41.9028, lng: 12.4964 },\r\n    'Stockholm': { lat: 59.3293, lng: 18.0686 },\r\n    'Vienna': { lat: 48.2082, lng: 16.3738 },\r\n    'Warsaw': { lat: 52.2297, lng: 21.0122 },\r\n    'Zurich': { lat: 47.3769, lng: 8.5417 },\r\n    'Nairobi': { lat: -1.2921, lng: 36.8219 },\r\n    'Athens': { lat: 37.9838, lng: 23.7275 },\r\n    'Baghdad': { lat: 33.3152, lng: 44.3661 },\r\n    'Helsinki': { lat: 60.1699, lng: 24.9384 },\r\n    'Istanbul': { lat: 41.0082, lng: 28.9784 },\r\n    'Jerusalem': { lat: 31.7683, lng: 35.2137 },\r\n    'Kiev': { lat: 50.4501, lng: 30.5234 },\r\n    'Kuwait City': { lat: 29.3759, lng: 47.9774 },\r\n    'Moscow': { lat: 55.7558, lng: 37.6173 },\r\n    'Riyadh': { lat: 24.7136, lng: 46.6753 },\r\n    'Doha': { lat: 25.2854, lng: 51.5310 },\r\n\r\n    'Tehran': { lat: 35.6892, lng: 51.3890 },\r\n    'Dubai': { lat: 25.2048, lng: 55.2708 },\r\n    'Muscat': { lat: 23.5880, lng: 58.3829 },\r\n    'Baku': { lat: 40.4093, lng: 49.8671 },\r\n    'Yerevan': { lat: 40.1792, lng: 44.4991 },\r\n    'Kabul': { lat: 34.5553, lng: 69.2075 },\r\n    'Karachi': { lat: 24.8607, lng: 67.0011 },\r\n    'Tashkent': { lat: 41.2995, lng: 69.2401 },\r\n    'New Delhi': { lat: 28.6139, lng: 77.2090 },\r\n    'Kolkata': { lat: 22.5726, lng: 88.3639 },\r\n    'Colombo': { lat: 6.9271, lng: 79.8612 },\r\n    'Kathmandu': { lat: 27.7172, lng: 85.3240 },\r\n    'Dhaka': { lat: 23.8103, lng: 90.4125 },\r\n    'Almaty': { lat: 43.2220, lng: 76.8512 },\r\n    'Yangon': { lat: 16.8661, lng: 96.1951 },\r\n    'Bangkok': { lat: 13.7563, lng: 100.5018 },\r\n    'Jakarta': { lat: 6.2088, lng: 106.8456 },\r\n    'Hanoi': { lat: 21.0278, lng: 105.8342 },\r\n    'Ho Chi Minh': { lat: 10.8231, lng: 106.6297 },\r\n    'Phnom Penh': { lat: 11.5564, lng: 104.9282 },\r\n    'Vientiane': { lat: 17.9757, lng: 102.6331 },\r\n    'Hong Kong': { lat: 22.3193, lng: 114.1694 },\r\n    'Beijing': { lat: 39.9042, lng: 116.4074 },\r\n    'Shanghai': { lat: 31.2304, lng: 121.4737 },\r\n    'Singapore': { lat: 1.3521, lng: 103.8198 },\r\n    'Taipei': { lat: 25.0330, lng: 121.5654 },\r\n    'Kuala Lumpur': { lat: 3.1390, lng: 101.6869 },\r\n    'Manila': { lat: 14.5995, lng: 120.9842 },\r\n    'Ulaanbaatar': { lat: 47.8864, lng: 106.9057 },\r\n    'Seoul': { lat: 37.5665, lng: 126.9780 },\r\n    'Tokyo': { lat: 35.6762, lng: 139.6503 },\r\n    'Pyongyang': { lat: 39.0392, lng: 125.7625 },\r\n\r\n    'Darwin': { lat: -12.4634, lng: 130.8456 },\r\n    'Adelaide': { lat: -34.9285, lng: 138.6007 },\r\n    'Brisbane': { lat: -27.4698, lng: 153.0251 },\r\n    'Canberra': { lat: -35.2809, lng: 149.1300 },\r\n    'Melbourne': { lat: -37.8136, lng: 144.9631 },\r\n    'Sydney': { lat: -33.8688, lng: 151.2093 },\r\n    'Perth': { lat: -31.9505, lng: 115.8605 },\r\n    'Guam': { lat: 13.4443, lng: 144.7937 },\r\n    'Port Moresby': { lat: -9.4438, lng: 147.1803 },\r\n    'Noumea': { lat: -22.2711, lng: 166.4416 },\r\n    'Auckland': { lat: -36.8509, lng: 174.7645 },\r\n    'Suva': { lat: -18.1416, lng: 178.4419 }\r\n  };\r\n\r\n  private findNearestCityInTimezones(lat: number, lng: number, timezones: { value: string, label: string }[]): { value: string, label: string } | null {\r\n    if (!lat || !lng || timezones.length === 0) return null;\r\n\r\n    let nearestCity: { value: string, label: string } | null = null;\r\n    let shortestDistance = Number.MAX_VALUE;\r\n\r\n    for (const timezone of timezones) {\r\n      const cityMatch = timezone.label.match(/\\)\\s+(.+)$/);\r\n      if (!cityMatch) continue;\r\n\r\n      const cityName = cityMatch[1];\r\n\r\n      let cityCoords: { lat: number, lng: number } | null = null;\r\n\r\n      if (this.cityCoordinates[cityName]) {\r\n        cityCoords = this.cityCoordinates[cityName];\r\n      } else {\r\n        for (const knownCity in this.cityCoordinates) {\r\n          if (cityName.includes(knownCity) || knownCity.includes(cityName)) {\r\n            cityCoords = this.cityCoordinates[knownCity];\r\n            break;\r\n          }\r\n        }\r\n      }\r\n\r\n      if (cityCoords) {\r\n        const distance = this.calculateDistance(lat, lng, cityCoords.lat, cityCoords.lng);\r\n\r\n        if (distance < shortestDistance) {\r\n          shortestDistance = distance;\r\n          nearestCity = timezone;\r\n        }\r\n      }\r\n    }\r\n\r\n    return nearestCity;\r\n  }\r\n\r\n  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {\r\n    const R = 6371; \n    const dLat = this.deg2rad(lat2 - lat1);\r\n    const dLon = this.deg2rad(lon2 - lon1);\r\n    const a =\r\n      Math.sin(dLat/2) * Math.sin(dLat/2) +\r\n      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *\r\n      Math.sin(dLon/2) * Math.sin(dLon/2);\r\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));\r\n    const distance = R * c; \n    return distance;\r\n  }\r\n\r\n  private deg2rad(deg: number): number {\r\n    return deg * (Math.PI/180);\r\n  }\r\n\r\n  private detectBrowserTimezone(): void {\r\n    try {\r\n      const offsetMinutes = new Date().getTimezoneOffset();\r\n      const offsetHours = -offsetMinutes / 60;\r\n\r\n\r\n      this.findTimezoneByOffset(offsetHours);\r\n    } catch (error) {\r\n      this.group.timezone = 'UTC';\r\n    }\r\n  }\r\n\r\n  private findSimilarTimezone(zoneName: string): string | null {\r\n    const parts = zoneName.split('/');\r\n    if (parts.length >= 1) {\r\n      const continent = parts[0];\r\n\r\n      const similarTimezone = this.timezones.find(tz => tz.value.startsWith(continent + '/'));\r\n      if (similarTimezone) {\r\n        return similarTimezone.value;\r\n      }\r\n    }\r\n\r\n    return null;\r\n  }\r\n\r\n  private fallbackToIpGeolocation() {\r\n\r\n    this.http.get<any>('https://ipapi.co/json/').subscribe({\r\n      next: (data) => {\r\n        if (data && data.country_code) {\r\n          this.group.country = data.country_code;\r\n\r\n          const foundCountry = this.countries.find(c => c.code === data.country_code);\r\n          if (!foundCountry) {\r\n            this.group.country = 'US';\r\n          }\r\n\r\n          if (data.timezone) {\r\n\r\n            const foundTimezone = this.timezones.find(tz => tz.value === data.timezone);\r\n            if (foundTimezone) {\r\n              this.group.timezone = foundTimezone.value;\r\n            } else {\r\n              const similarTimezone = this.findSimilarTimezone(data.timezone);\r\n              if (similarTimezone) {\r\n                this.group.timezone = similarTimezone;\r\n              } else if (data.utc_offset) {\r\n                const offsetHours = parseInt(data.utc_offset.substring(0, 3)) +\r\n                                   (parseInt(data.utc_offset.substring(3, 5)) / 60);\r\n                this.findTimezoneByOffset(offsetHours);\r\n              } else {\r\n                this.detectBrowserTimezone();\r\n              }\r\n            }\r\n          } else {\r\n            this.detectBrowserTimezone();\r\n          }\r\n        } else {\r\n          this.group.country = 'US';\r\n          this.detectBrowserTimezone();\r\n        }\r\n        this.isLoadingCountry = false;\r\n      },\r\n      error: (error) => {\r\n        this.group.country = 'US';\r\n        this.detectBrowserTimezone();\r\n        this.isLoadingCountry = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  checkGroupName() {\r\n    const name = this.group.name?.trim();\r\n\r\n    this.nameAvailable = false;\r\n\r\n    if (!name) {\r\n      this.nameError = '';\r\n      this.nameChecking = false;\r\n      return;\r\n    }\r\n\r\n    this.nameChecking = true;\r\n\r\n    this.nameCheckSubject.next(name);\r\n  }\r\n\r\n  async checkGroupNameExists(name: string) {\r\n    if (!name) {\r\n      this.nameError = '';\r\n      this.nameChecking = false;\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const { data, error } = await this.supabaseService.getClient()\r\n        .from('groups')\r\n        .select('id')\r\n        .eq('name', name)\r\n        .limit(1);\r\n\r\n      this.nameChecking = false;\r\n\r\n      if (error) {\r\n        this.nameError = 'Error checking group name availability';\r\n        return;\r\n      }\r\n\r\n      if (data && data.length > 0) {\r\n        this.nameError = 'This group name is already taken';\r\n        this.nameAvailable = false;\r\n      } else {\r\n        this.nameError = '';\r\n        this.nameAvailable = true;\r\n      }\r\n    } catch (error) {\r\n      this.nameChecking = false;\r\n      this.nameError = 'Error checking group name availability';\r\n    }\r\n  }\r\n\r\n\r\n  async createGroup() {\r\n    if (!this.userId) {\r\n      this.errorMessage = 'You must be logged in to create a group';\r\n      return;\r\n    }\r\n\r\n    if (!this.group.name || !this.group.emoji || !this.group.timezone || !this.group.country) {\r\n      this.errorMessage = 'Please fill in all required fields';\r\n      return;\r\n    }\r\n\r\n    if (this.nameError) {\r\n      this.errorMessage = 'Please choose a different group name';\r\n      return;\r\n    }\r\n\r\n    this.isSubmitting = true;\r\n    this.errorMessage = '';\r\n\r\n    try {\r\n      const newGroup: Omit<Group, 'id' | 'created'> = {\r\n        name: this.group.name,\r\n        emoji: this.group.emoji,\r\n        timezone: this.group.timezone,\r\n        country: this.group.country,\r\n        admin_id: this.userId,\r\n        level: 0,\r\n        strength_xp: 0,\r\n        money_xp: 0,\r\n        health_xp: 0,\r\n        knowledge_xp: 0,\r\n        enable_sidequests: true\r\n      };\r\n\r\n      const groupId = await this.groupService.createGroup(newGroup);\r\n\r\n      this.router.navigate(['/groups', groupId]);\r\n    } catch (error: any) {\r\n      this.errorMessage = error.message || 'Failed to create group. Please try again.';\r\n    } finally {\r\n      this.isSubmitting = false;\r\n    }\r\n  }\r\n}\r\n", "<!-- Exact HTML from Django template with Angular syntax -->\r\n<div class=\"container\">\r\n  <header>\r\n    <div class=\"logo\">\r\n      <img src=\"assets/images/upshift_icon_mini.svg\" alt=\"Upshift\">\r\n      <span>Upshift</span>\r\n    </div>\r\n    <h1>Create New Group</h1>\r\n  </header>\r\n  <a [routerLink]=\"['/groups']\" class=\"back-link\">&larr; Back to groups</a>\r\n\r\n  <div *ngIf=\"errorMessage\" class=\"messages\">\r\n    <div class=\"message error\">{{ errorMessage }}</div>\r\n  </div>\r\n\r\n  <form (ngSubmit)=\"createGroup()\" class=\"group-form\">\r\n    <div class=\"form-group\">\r\n      <label for=\"name\">Group Name</label>\r\n      <div class=\"input-group\">\r\n        <div class=\"emoji\"><input type=\"text\" [(ngModel)]=\"group.emoji\" name=\"emoji\" id=\"emoji\" value=\"👥\" appEmojiInput></div>\r\n        <input type=\"text\" [(ngModel)]=\"group.name\" name=\"name\" id=\"name\" placeholder=\"Enter group name\" required (input)=\"checkGroupName()\">\r\n      </div>\r\n      <div *ngIf=\"nameError\" class=\"error-message\">{{ nameError }}</div>\r\n      <div *ngIf=\"nameChecking\" class=\"checking-message\">Checking availability...</div>\r\n      <div *ngIf=\"nameAvailable\" class=\"success-message\">Group name is available!</div>\r\n    </div>\r\n\r\n    <div class=\"form-group\">\r\n      <label for=\"timezone\">Timezone</label>\r\n      <div class=\"select-wrapper\">\r\n        <select [(ngModel)]=\"group.timezone\" name=\"timezone\" id=\"timezone\" required [disabled]=\"isLoadingCountry\">\r\n          <option *ngFor=\"let tz of timezones\" [value]=\"tz.value\">{{ tz.label }}</option>\r\n        </select>\r\n        <div *ngIf=\"isLoadingCountry\" class=\"loading-indicator\">\r\n          <ion-spinner name=\"dots\" color=\"medium\"></ion-spinner>\r\n        </div>\r\n      </div>\r\n      <div class=\"help-text\">\r\n        <span *ngIf=\"isLoadingCountry\">Detecting your timezone...</span>\r\n        <span *ngIf=\"!isLoadingCountry\">Select the timezone for this group. Cannot be changed later.</span>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"form-group\">\r\n      <label for=\"country\">Country</label>\r\n      <div class=\"select-wrapper\">\r\n        <select [(ngModel)]=\"group.country\" name=\"country\" id=\"country\" required [disabled]=\"isLoadingCountry\">\r\n          <option *ngFor=\"let country of countries\" [value]=\"country.code\">{{ country.name }}</option>\r\n        </select>\r\n        <div *ngIf=\"isLoadingCountry\" class=\"loading-indicator\">\r\n          <ion-spinner name=\"dots\" color=\"medium\"></ion-spinner>\r\n        </div>\r\n      </div>\r\n      <div class=\"help-text\">\r\n        <span *ngIf=\"isLoadingCountry\">Detecting your location...</span>\r\n        <span *ngIf=\"!isLoadingCountry\">Select the country for this group.</span>\r\n      </div>\r\n    </div>\r\n\r\n    <button type=\"submit\" class=\"btn primary full-width\" [disabled]=\"isSubmitting || nameError\">\r\n      {{ isSubmitting ? 'Creating...' : 'Create Group' }}\r\n    </button>\r\n  </form>\r\n</div>\r\n"], "mappings": ";;AAAA,SAA4BA,MAAM,QAAQ,eAAe;AACzD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,EAAEC,MAAM,QAAQ,iBAAiB;AACtD,SAASC,YAAY,QAAQ,iCAAiC;AAE9D,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,OAAO,QAAsB,MAAM;AAChF,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,mBAAmB,QAAQ,2CAA2C;;;;;;;;;ICC3EC,EADF,CAAAC,cAAA,cAA2C,cACd;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAC/CF,EAD+C,CAAAG,YAAA,EAAM,EAC/C;;;;IADuBH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,YAAA,CAAkB;;;;;IAU3CP,EAAA,CAAAC,cAAA,cAA6C;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAArBH,EAAA,CAAAI,SAAA,EAAe;IAAfJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAE,SAAA,CAAe;;;;;IAC5DR,EAAA,CAAAC,cAAA,cAAmD;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACjFH,EAAA,CAAAC,cAAA,cAAmD;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAO7EH,EAAA,CAAAC,cAAA,iBAAwD;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAA1CH,EAAA,CAAAS,UAAA,UAAAC,KAAA,CAAAC,KAAA,CAAkB;IAACX,EAAA,CAAAI,SAAA,EAAc;IAAdJ,EAAA,CAAAK,iBAAA,CAAAK,KAAA,CAAAE,KAAA,CAAc;;;;;IAExEZ,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAa,SAAA,sBAAsD;IACxDb,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAC,cAAA,WAA+B;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAChEH,EAAA,CAAAC,cAAA,WAAgC;IAAAD,EAAA,CAAAE,MAAA,mEAA4D;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAQjGH,EAAA,CAAAC,cAAA,iBAAiE;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAlDH,EAAA,CAAAS,UAAA,UAAAK,UAAA,CAAAC,IAAA,CAAsB;IAACf,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAS,UAAA,CAAAE,IAAA,CAAkB;;;;;IAErFhB,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAa,SAAA,sBAAsD;IACxDb,EAAA,CAAAG,YAAA,EAAM;;;;;IAGNH,EAAA,CAAAC,cAAA,WAA+B;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAChEH,EAAA,CAAAC,cAAA,WAAgC;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;ADnCjF,OAAM,MAAOc,eAAe;EAiY1BC,YAAA;IAhYA,KAAAC,MAAM,GAAkB,IAAI;IAE5B,KAAAC,KAAK,GAAmB;MACtBJ,IAAI,EAAE,EAAE;MACRK,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE,KAAK;MACfC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE,CAAC;MACRC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE,CAAC;MACZC,YAAY,EAAE,CAAC;MACfC,iBAAiB,EAAE;KACpB;IAED,KAAAC,SAAS,GAAuC,CAC9C;MAAEnB,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAkC,CAAE,EAE3D;MAAED,KAAK,EAAE,mBAAmB;MAAEC,KAAK,EAAE;IAAmB,CAAE,EAC1D;MAAED,KAAK,EAAE,qBAAqB;MAAEC,KAAK,EAAE;IAAqB,CAAE,EAC9D;MAAED,KAAK,EAAE,iBAAiB;MAAEC,KAAK,EAAE;IAAiB,CAAE,EACtD;MAAED,KAAK,EAAE,mBAAmB;MAAEC,KAAK,EAAE;IAAmB,CAAE,EAC1D;MAAED,KAAK,EAAE,gBAAgB;MAAEC,KAAK,EAAE;IAAgB,CAAE,EACpD;MAAED,KAAK,EAAE,qBAAqB;MAAEC,KAAK,EAAE;IAAqB,CAAE,EAC9D;MAAED,KAAK,EAAE,sBAAsB;MAAEC,KAAK,EAAE;IAAsB,CAAE,EAChE;MAAED,KAAK,EAAE,iBAAiB;MAAEC,KAAK,EAAE;IAAiB,CAAE,EACtD;MAAED,KAAK,EAAE,qBAAqB;MAAEC,KAAK,EAAE;IAAqB,CAAE,EAC9D;MAAED,KAAK,EAAE,gBAAgB;MAAEC,KAAK,EAAE;IAAgB,CAAE,EACpD;MAAED,KAAK,EAAE,iBAAiB;MAAEC,KAAK,EAAE;IAAiB,CAAE,EACtD;MAAED,KAAK,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAc,CAAE,EAChD;MAAED,KAAK,EAAE,gBAAgB;MAAEC,KAAK,EAAE;IAAqB,CAAE,EACzD;MAAED,KAAK,EAAE,iBAAiB;MAAEC,KAAK,EAAE;IAAiB,CAAE,EACtD;MAAED,KAAK,EAAE,kBAAkB;MAAEC,KAAK,EAAE;IAAkB,CAAE,EACxD;MAAED,KAAK,EAAE,kBAAkB;MAAEC,KAAK,EAAE;IAAkB,CAAE,EACxD;MAAED,KAAK,EAAE,iBAAiB;MAAEC,KAAK,EAAE;IAAiB,CAAE,EACtD;MAAED,KAAK,EAAE,uBAAuB;MAAEC,KAAK,EAAE;IAAuB,CAAE,EAClE;MAAED,KAAK,EAAE,uBAAuB;MAAEC,KAAK,EAAE;IAAuB,CAAE,EAClE;MAAED,KAAK,EAAE,gCAAgC;MAAEC,KAAK,EAAE;IAAsB,CAAE,EAC1E;MAAED,KAAK,EAAE,mBAAmB;MAAEC,KAAK,EAAE;IAAmB,CAAE,EAC1D;MAAED,KAAK,EAAE,oBAAoB;MAAEC,KAAK,EAAE;IAAoB,CAAE,EAC5D;MAAED,KAAK,EAAE,kBAAkB;MAAEC,KAAK,EAAE;IAAkB,CAAE,EAExD;MAAED,KAAK,EAAE,iBAAiB;MAAEC,KAAK,EAAE;IAAc,CAAE,EACnD;MAAED,KAAK,EAAE,oBAAoB;MAAEC,KAAK,EAAE;IAAiB,CAAE,EACzD;MAAED,KAAK,EAAE,mBAAmB;MAAEC,KAAK,EAAE;IAAoB,CAAE,EAC3D;MAAED,KAAK,EAAE,eAAe;MAAEC,KAAK,EAAE;IAAgB,CAAE,EACnD;MAAED,KAAK,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAe,CAAE,EACjD;MAAED,KAAK,EAAE,eAAe;MAAEC,KAAK,EAAE;IAAgB,CAAE,EACnD;MAAED,KAAK,EAAE,eAAe;MAAEC,KAAK,EAAE;IAAgB,CAAE,EACnD;MAAED,KAAK,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAe,CAAE,EACjD;MAAED,KAAK,EAAE,gBAAgB;MAAEC,KAAK,EAAE;IAAiB,CAAE,EACrD;MAAED,KAAK,EAAE,kBAAkB;MAAEC,KAAK,EAAE;IAAmB,CAAE,EACzD;MAAED,KAAK,EAAE,iBAAiB;MAAEC,KAAK,EAAE;IAAkB,CAAE,EACvD;MAAED,KAAK,EAAE,eAAe;MAAEC,KAAK,EAAE;IAAgB,CAAE,EACnD;MAAED,KAAK,EAAE,mBAAmB;MAAEC,KAAK,EAAE;IAAoB,CAAE,EAC3D;MAAED,KAAK,EAAE,iBAAiB;MAAEC,KAAK,EAAE;IAAkB,CAAE,EACvD;MAAED,KAAK,EAAE,iBAAiB;MAAEC,KAAK,EAAE;IAAkB,CAAE,EACvD;MAAED,KAAK,EAAE,mBAAmB;MAAEC,KAAK,EAAE;IAAoB,CAAE,EAC3D;MAAED,KAAK,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAe,CAAE,EACjD;MAAED,KAAK,EAAE,eAAe;MAAEC,KAAK,EAAE;IAAgB,CAAE,EACnD;MAAED,KAAK,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAc,CAAE,EAC/C;MAAED,KAAK,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAe,CAAE,EACjD;MAAED,KAAK,EAAE,eAAe;MAAEC,KAAK,EAAE;IAAgB,CAAE,EACnD;MAAED,KAAK,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAc,CAAE,EAC/C;MAAED,KAAK,EAAE,kBAAkB;MAAEC,KAAK,EAAE;IAAmB,CAAE,EACzD;MAAED,KAAK,EAAE,eAAe;MAAEC,KAAK,EAAE;IAAgB,CAAE,EACnD;MAAED,KAAK,EAAE,eAAe;MAAEC,KAAK,EAAE;IAAgB,CAAE,EACnD;MAAED,KAAK,EAAE,eAAe;MAAEC,KAAK,EAAE;IAAgB,CAAE,EACnD;MAAED,KAAK,EAAE,gBAAgB;MAAEC,KAAK,EAAE;IAAiB,CAAE,EACrD;MAAED,KAAK,EAAE,eAAe;MAAEC,KAAK,EAAE;IAAgB,CAAE,EACnD;MAAED,KAAK,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAiB,CAAE,EACnD;MAAED,KAAK,EAAE,iBAAiB;MAAEC,KAAK,EAAE;IAAkB,CAAE,EACvD;MAAED,KAAK,EAAE,iBAAiB;MAAEC,KAAK,EAAE;IAAkB,CAAE,EACvD;MAAED,KAAK,EAAE,gBAAgB;MAAEC,KAAK,EAAE;IAAmB,CAAE,EACvD;MAAED,KAAK,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAc,CAAE,EAC/C;MAAED,KAAK,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAqB,CAAE,EACtD;MAAED,KAAK,EAAE,eAAe;MAAEC,KAAK,EAAE;IAAgB,CAAE,EACnD;MAAED,KAAK,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAgB,CAAE,EACjD;MAAED,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAc,CAAE,EAC9C;MAAED,KAAK,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAmB,CAAE,EACpD;MAAED,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAe,CAAE,EAC/C;MAAED,KAAK,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAgB,CAAE,EACjD;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAc,CAAE,EAC7C;MAAED,KAAK,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAiB,CAAE,EACnD;MAAED,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAkB,CAAE,EAClD;MAAED,KAAK,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAiB,CAAE,EACnD;MAAED,KAAK,EAAE,eAAe;MAAEC,KAAK,EAAE;IAAkB,CAAE,EACrD;MAAED,KAAK,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAsB,CAAE,EACxD;MAAED,KAAK,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAoB,CAAE,EACtD;MAAED,KAAK,EAAE,gBAAgB;MAAEC,KAAK,EAAE;IAAsB,CAAE,EAC1D;MAAED,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAe,CAAE,EAC/C;MAAED,KAAK,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAgB,CAAE,EACjD;MAAED,KAAK,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAmB,CAAE,EACpD;MAAED,KAAK,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAiB,CAAE,EACnD;MAAED,KAAK,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAiB,CAAE,EACnD;MAAED,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAe,CAAE,EAC/C;MAAED,KAAK,EAAE,iBAAiB;MAAEC,KAAK,EAAE;IAAoB,CAAE,EACzD;MAAED,KAAK,EAAE,gBAAgB;MAAEC,KAAK,EAAE;IAAmB,CAAE,EACvD;MAAED,KAAK,EAAE,gBAAgB;MAAEC,KAAK,EAAE;IAAmB,CAAE,EACvD;MAAED,KAAK,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAiB,CAAE,EACnD;MAAED,KAAK,EAAE,eAAe;MAAEC,KAAK,EAAE;IAAkB,CAAE,EACrD;MAAED,KAAK,EAAE,gBAAgB;MAAEC,KAAK,EAAE;IAAmB,CAAE,EACvD;MAAED,KAAK,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAgB,CAAE,EACjD;MAAED,KAAK,EAAE,mBAAmB;MAAEC,KAAK,EAAE;IAAsB,CAAE,EAC7D;MAAED,KAAK,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAgB,CAAE,EACjD;MAAED,KAAK,EAAE,kBAAkB;MAAEC,KAAK,EAAE;IAAqB,CAAE,EAC3D;MAAED,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAe,CAAE,EAC/C;MAAED,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAe,CAAE,EAC/C;MAAED,KAAK,EAAE,gBAAgB;MAAEC,KAAK,EAAE;IAAmB,CAAE,EACvD;MAAED,KAAK,EAAE,kBAAkB;MAAEC,KAAK,EAAE;IAAmB,CAAE,EACzD;MAAED,KAAK,EAAE,oBAAoB;MAAEC,KAAK,EAAE;IAAqB,CAAE,EAC7D;MAAED,KAAK,EAAE,oBAAoB;MAAEC,KAAK,EAAE;IAAmB,CAAE,EAC3D;MAAED,KAAK,EAAE,oBAAoB;MAAEC,KAAK,EAAE;IAAmB,CAAE,EAC3D;MAAED,KAAK,EAAE,qBAAqB;MAAEC,KAAK,EAAE;IAAoB,CAAE,EAC7D;MAAED,KAAK,EAAE,kBAAkB;MAAEC,KAAK,EAAE;IAAiB,CAAE,EACvD;MAAED,KAAK,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAe,CAAE,EACjD;MAAED,KAAK,EAAE,sBAAsB;MAAEC,KAAK,EAAE;IAAuB,CAAE,EACjE;MAAED,KAAK,EAAE,gBAAgB;MAAEC,KAAK,EAAE;IAAiB,CAAE,EACrD;MAAED,KAAK,EAAE,kBAAkB;MAAEC,KAAK,EAAE;IAAmB,CAAE,EACzD;MAAED,KAAK,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAe,CAAE,CAClD;IAED,KAAAmB,SAAS,GAAqC,CAC5C;MAAEf,IAAI,EAAE,aAAa;MAAED,IAAI,EAAE;IAAI,CAAE,EACnC;MAAEC,IAAI,EAAE,eAAe;MAAED,IAAI,EAAE;IAAI,CAAE,EACrC;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,gBAAgB;MAAED,IAAI,EAAE;IAAI,CAAE,EACtC;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,UAAU;MAAED,IAAI,EAAE;IAAI,CAAE,EAChC;MAAEC,IAAI,EAAE,YAAY;MAAED,IAAI,EAAE;IAAI,CAAE,EAClC;MAAEC,IAAI,EAAE,qBAAqB;MAAED,IAAI,EAAE;IAAI,CAAE,EAC3C;MAAEC,IAAI,EAAE,WAAW;MAAED,IAAI,EAAE;IAAI,CAAE,EACjC;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAEC,IAAI,EAAE,WAAW;MAAED,IAAI,EAAE;IAAI,CAAE,EACjC;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,YAAY;MAAED,IAAI,EAAE;IAAI,CAAE,EAClC;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,YAAY;MAAED,IAAI,EAAE;IAAI,CAAE,EAClC;MAAEC,IAAI,EAAE,UAAU;MAAED,IAAI,EAAE;IAAI,CAAE,EAChC;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,wBAAwB;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9C;MAAEC,IAAI,EAAE,UAAU;MAAED,IAAI,EAAE;IAAI,CAAE,EAChC;MAAEC,IAAI,EAAE,eAAe;MAAED,IAAI,EAAE;IAAI,CAAE,EACrC;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,gCAAgC;MAAED,IAAI,EAAE;IAAI,CAAE,EACtD;MAAEC,IAAI,EAAE,mBAAmB;MAAED,IAAI,EAAE;IAAI,CAAE,EACzC;MAAEC,IAAI,EAAE,UAAU;MAAED,IAAI,EAAE;IAAI,CAAE,EAChC;MAAEC,IAAI,EAAE,cAAc;MAAED,IAAI,EAAE;IAAI,CAAE,EACpC;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,UAAU;MAAED,IAAI,EAAE;IAAI,CAAE,EAChC;MAAEC,IAAI,EAAE,UAAU;MAAED,IAAI,EAAE;IAAI,CAAE,EAChC;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,YAAY;MAAED,IAAI,EAAE;IAAI,CAAE,EAClC;MAAEC,IAAI,EAAE,gBAAgB;MAAED,IAAI,EAAE;IAAI,CAAE,EACtC;MAAEC,IAAI,EAAE,0BAA0B;MAAED,IAAI,EAAE;IAAI,CAAE,EAChD;MAAEC,IAAI,EAAE,MAAM;MAAED,IAAI,EAAE;IAAI,CAAE,EAC5B;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAEC,IAAI,EAAE,kBAAkB;MAAED,IAAI,EAAE;IAAI,CAAE,EACxC;MAAEC,IAAI,EAAE,yBAAyB;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/C;MAAEC,IAAI,EAAE,UAAU;MAAED,IAAI,EAAE;IAAI,CAAE,EAChC;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAEC,IAAI,EAAE,uCAAuC;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7D;MAAEC,IAAI,EAAE,cAAc;MAAED,IAAI,EAAE;IAAI,CAAE,EACpC;MAAEC,IAAI,EAAE,YAAY;MAAED,IAAI,EAAE;IAAI,CAAE,EAClC;MAAEC,IAAI,EAAE,gBAAgB;MAAED,IAAI,EAAE;IAAI,CAAE,EACtC;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,MAAM;MAAED,IAAI,EAAE;IAAI,CAAE,EAC5B;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,gBAAgB;MAAED,IAAI,EAAE;IAAI,CAAE,EACtC;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,UAAU;MAAED,IAAI,EAAE;IAAI,CAAE,EAChC;MAAEC,IAAI,EAAE,UAAU;MAAED,IAAI,EAAE;IAAI,CAAE,EAChC;MAAEC,IAAI,EAAE,oBAAoB;MAAED,IAAI,EAAE;IAAI,CAAE,EAC1C;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAEC,IAAI,EAAE,aAAa;MAAED,IAAI,EAAE;IAAI,CAAE,EACnC;MAAEC,IAAI,EAAE,mBAAmB;MAAED,IAAI,EAAE;IAAI,CAAE,EACzC;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,UAAU;MAAED,IAAI,EAAE;IAAI,CAAE,EAChC;MAAEC,IAAI,EAAE,6BAA6B;MAAED,IAAI,EAAE;IAAI,CAAE,EACnD;MAAEC,IAAI,EAAE,eAAe;MAAED,IAAI,EAAE;IAAI,CAAE,EACrC;MAAEC,IAAI,EAAE,MAAM;MAAED,IAAI,EAAE;IAAI,CAAE,EAC5B;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,eAAe;MAAED,IAAI,EAAE;IAAI,CAAE,EACrC;MAAEC,IAAI,EAAE,kBAAkB;MAAED,IAAI,EAAE;IAAI,CAAE,EACxC;MAAEC,IAAI,EAAE,6BAA6B;MAAED,IAAI,EAAE;IAAI,CAAE,EACnD;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAEC,IAAI,EAAE,WAAW;MAAED,IAAI,EAAE;IAAI,CAAE,EACjC;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,WAAW;MAAED,IAAI,EAAE;IAAI,CAAE,EACjC;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,YAAY;MAAED,IAAI,EAAE;IAAI,CAAE,EAClC;MAAEC,IAAI,EAAE,MAAM;MAAED,IAAI,EAAE;IAAI,CAAE,EAC5B;MAAEC,IAAI,EAAE,WAAW;MAAED,IAAI,EAAE;IAAI,CAAE,EACjC;MAAEC,IAAI,EAAE,UAAU;MAAED,IAAI,EAAE;IAAI,CAAE,EAChC;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,eAAe;MAAED,IAAI,EAAE;IAAI,CAAE,EACrC;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAEC,IAAI,EAAE,mCAAmC;MAAED,IAAI,EAAE;IAAI,CAAE,EACzD;MAAEC,IAAI,EAAE,+BAA+B;MAAED,IAAI,EAAE;IAAI,CAAE,EACrD;MAAEC,IAAI,EAAE,UAAU;MAAED,IAAI,EAAE;IAAI,CAAE,EAChC;MAAEC,IAAI,EAAE,WAAW;MAAED,IAAI,EAAE;IAAI,CAAE,EACjC;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAEC,IAAI,EAAE,WAAW;MAAED,IAAI,EAAE;IAAI,CAAE,EACjC;MAAEC,IAAI,EAAE,2BAA2B;MAAED,IAAI,EAAE;IAAI,CAAE,EACjD;MAAEC,IAAI,EAAE,MAAM;MAAED,IAAI,EAAE;IAAI,CAAE,EAC5B;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,aAAa;MAAED,IAAI,EAAE;IAAI,CAAE,EACnC;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,YAAY;MAAED,IAAI,EAAE;IAAI,CAAE,EAClC;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAEC,IAAI,EAAE,UAAU;MAAED,IAAI,EAAE;IAAI,CAAE,EAChC;MAAEC,IAAI,EAAE,yCAAyC;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/D;MAAEC,IAAI,EAAE,oBAAoB;MAAED,IAAI,EAAE;IAAI,CAAE,EAC1C;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,YAAY;MAAED,IAAI,EAAE;IAAI,CAAE,EAClC;MAAEC,IAAI,EAAE,mCAAmC;MAAED,IAAI,EAAE;IAAI,CAAE,EACzD;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,wBAAwB;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9C;MAAEC,IAAI,EAAE,eAAe;MAAED,IAAI,EAAE;IAAI,CAAE,EACrC;MAAEC,IAAI,EAAE,WAAW;MAAED,IAAI,EAAE;IAAI,CAAE,EACjC;MAAEC,IAAI,EAAE,YAAY;MAAED,IAAI,EAAE;IAAI,CAAE,EAClC;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAEC,IAAI,EAAE,4CAA4C;MAAED,IAAI,EAAE;IAAI,CAAE,EAClE;MAAEC,IAAI,EAAE,YAAY;MAAED,IAAI,EAAE;IAAI,CAAE,EAClC;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,UAAU;MAAED,IAAI,EAAE;IAAI,CAAE,EAChC;MAAEC,IAAI,EAAE,UAAU;MAAED,IAAI,EAAE;IAAI,CAAE,EAChC;MAAEC,IAAI,EAAE,MAAM;MAAED,IAAI,EAAE;IAAI,CAAE,EAC5B;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAEC,IAAI,EAAE,kBAAkB;MAAED,IAAI,EAAE;IAAI,CAAE,EACxC;MAAEC,IAAI,EAAE,YAAY;MAAED,IAAI,EAAE;IAAI,CAAE,EAClC;MAAEC,IAAI,EAAE,YAAY;MAAED,IAAI,EAAE;IAAI,CAAE,EAClC;MAAEC,IAAI,EAAE,WAAW;MAAED,IAAI,EAAE;IAAI,CAAE,EACjC;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,iCAAiC;MAAED,IAAI,EAAE;IAAI,CAAE,EACvD;MAAEC,IAAI,EAAE,sBAAsB;MAAED,IAAI,EAAE;IAAI,CAAE,EAC5C;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,UAAU;MAAED,IAAI,EAAE;IAAI,CAAE,EAChC;MAAEC,IAAI,EAAE,YAAY;MAAED,IAAI,EAAE;IAAI,CAAE,EAClC;MAAEC,IAAI,EAAE,YAAY;MAAED,IAAI,EAAE;IAAI,CAAE,EAClC;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,YAAY;MAAED,IAAI,EAAE;IAAI,CAAE,EAClC;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAEC,IAAI,EAAE,aAAa;MAAED,IAAI,EAAE;IAAI,CAAE,EACnC;MAAEC,IAAI,EAAE,sBAAsB;MAAED,IAAI,EAAE;IAAI,CAAE,EAC5C;MAAEC,IAAI,EAAE,eAAe;MAAED,IAAI,EAAE;IAAI,CAAE,EACrC;MAAEC,IAAI,EAAE,aAAa;MAAED,IAAI,EAAE;IAAI,CAAE,EACnC;MAAEC,IAAI,EAAE,WAAW;MAAED,IAAI,EAAE;IAAI,CAAE,EACjC;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,MAAM;MAAED,IAAI,EAAE;IAAI,CAAE,EAC5B;MAAEC,IAAI,EAAE,gBAAgB;MAAED,IAAI,EAAE;IAAI,CAAE,EACtC;MAAEC,IAAI,EAAE,0BAA0B;MAAED,IAAI,EAAE;IAAI,CAAE,EAChD;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,MAAM;MAAED,IAAI,EAAE;IAAI,CAAE,EAC5B;MAAEC,IAAI,EAAE,UAAU;MAAED,IAAI,EAAE;IAAI,CAAE,EAChC;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAEC,IAAI,EAAE,iCAAiC;MAAED,IAAI,EAAE;IAAI,CAAE,EACvD;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,kBAAkB;MAAED,IAAI,EAAE;IAAI,CAAE,EACxC;MAAEC,IAAI,EAAE,UAAU;MAAED,IAAI,EAAE;IAAI,CAAE,EAChC;MAAEC,IAAI,EAAE,MAAM;MAAED,IAAI,EAAE;IAAI,CAAE,EAC5B;MAAEC,IAAI,EAAE,aAAa;MAAED,IAAI,EAAE;IAAI,CAAE,EACnC;MAAEC,IAAI,EAAE,UAAU;MAAED,IAAI,EAAE;IAAI,CAAE,EAChC;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,UAAU;MAAED,IAAI,EAAE;IAAI,CAAE,EAChC;MAAEC,IAAI,EAAE,aAAa;MAAED,IAAI,EAAE;IAAI,CAAE,EACnC;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,oBAAoB;MAAED,IAAI,EAAE;IAAI,CAAE,EAC1C;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,cAAc;MAAED,IAAI,EAAE;IAAI,CAAE,EACpC;MAAEC,IAAI,EAAE,uBAAuB;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7C;MAAEC,IAAI,EAAE,aAAa;MAAED,IAAI,EAAE;IAAI,CAAE,EACnC;MAAEC,IAAI,EAAE,2BAA2B;MAAED,IAAI,EAAE;IAAI,CAAE,EACjD;MAAEC,IAAI,EAAE,kCAAkC;MAAED,IAAI,EAAE;IAAI,CAAE,EACxD;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAEC,IAAI,EAAE,YAAY;MAAED,IAAI,EAAE;IAAI,CAAE,EAClC;MAAEC,IAAI,EAAE,uBAAuB;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7C;MAAEC,IAAI,EAAE,cAAc;MAAED,IAAI,EAAE;IAAI,CAAE,EACpC;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,YAAY;MAAED,IAAI,EAAE;IAAI,CAAE,EAClC;MAAEC,IAAI,EAAE,cAAc;MAAED,IAAI,EAAE;IAAI,CAAE,EACpC;MAAEC,IAAI,EAAE,WAAW;MAAED,IAAI,EAAE;IAAI,CAAE,EACjC;MAAEC,IAAI,EAAE,UAAU;MAAED,IAAI,EAAE;IAAI,CAAE,EAChC;MAAEC,IAAI,EAAE,UAAU;MAAED,IAAI,EAAE;IAAI,CAAE,EAChC;MAAEC,IAAI,EAAE,iBAAiB;MAAED,IAAI,EAAE;IAAI,CAAE,EACvC;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,cAAc;MAAED,IAAI,EAAE;IAAI,CAAE,EACpC;MAAEC,IAAI,EAAE,8CAA8C;MAAED,IAAI,EAAE;IAAI,CAAE,EACpE;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAEC,IAAI,EAAE,WAAW;MAAED,IAAI,EAAE;IAAI,CAAE,EACjC;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAEC,IAAI,EAAE,UAAU;MAAED,IAAI,EAAE;IAAI,CAAE,EAChC;MAAEC,IAAI,EAAE,wBAAwB;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9C;MAAEC,IAAI,EAAE,WAAW;MAAED,IAAI,EAAE;IAAI,CAAE,EACjC;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,aAAa;MAAED,IAAI,EAAE;IAAI,CAAE,EACnC;MAAEC,IAAI,EAAE,sBAAsB;MAAED,IAAI,EAAE;IAAI,CAAE,EAC5C;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,YAAY;MAAED,IAAI,EAAE;IAAI,CAAE,EAClC;MAAEC,IAAI,EAAE,8BAA8B;MAAED,IAAI,EAAE;IAAI,CAAE,EACpD;MAAEC,IAAI,EAAE,UAAU;MAAED,IAAI,EAAE;IAAI,CAAE,EAChC;MAAEC,IAAI,EAAE,aAAa;MAAED,IAAI,EAAE;IAAI,CAAE,EACnC;MAAEC,IAAI,EAAE,MAAM;MAAED,IAAI,EAAE;IAAI,CAAE,EAC5B;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAEC,IAAI,EAAE,qBAAqB;MAAED,IAAI,EAAE;IAAI,CAAE,EAC3C;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,cAAc;MAAED,IAAI,EAAE;IAAI,CAAE,EACpC;MAAEC,IAAI,EAAE,0BAA0B;MAAED,IAAI,EAAE;IAAI,CAAE,EAChD;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,sBAAsB;MAAED,IAAI,EAAE;IAAI,CAAE,EAC5C;MAAEC,IAAI,EAAE,gBAAgB;MAAED,IAAI,EAAE;IAAI,CAAE,EACtC;MAAEC,IAAI,EAAE,eAAe;MAAED,IAAI,EAAE;IAAI,CAAE,EACrC;MAAEC,IAAI,EAAE,sCAAsC;MAAED,IAAI,EAAE;IAAI,CAAE,EAC5D;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,YAAY;MAAED,IAAI,EAAE;IAAI,CAAE,EAClC;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,WAAW;MAAED,IAAI,EAAE;IAAI,CAAE,EACjC;MAAEC,IAAI,EAAE,SAAS;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAEC,IAAI,EAAE,yBAAyB;MAAED,IAAI,EAAE;IAAI,CAAE,EAC/C;MAAEC,IAAI,EAAE,sBAAsB;MAAED,IAAI,EAAE;IAAI,CAAE,EAC5C;MAAEC,IAAI,EAAE,mBAAmB;MAAED,IAAI,EAAE;IAAI,CAAE,EACzC;MAAEC,IAAI,EAAE,gBAAgB;MAAED,IAAI,EAAE;IAAI,CAAE,EACtC;MAAEC,IAAI,EAAE,OAAO;MAAED,IAAI,EAAE;IAAI,CAAE,EAC7B;MAAEC,IAAI,EAAE,QAAQ;MAAED,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAEC,IAAI,EAAE,UAAU;MAAED,IAAI,EAAE;IAAI,CAAE,CACjC;IAED,KAAAiB,YAAY,GAAG,KAAK;IACpB,KAAAzB,YAAY,GAAG,EAAE;IACjB,KAAAC,SAAS,GAAG,EAAE;IACd,KAAAyB,YAAY,GAAG,KAAK;IACpB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,gBAAgB,GAAG,IAAI;IACvB,KAAAC,iBAAiB,GAAG,IAAI;IAChB,KAAAC,gBAAgB,GAAG,IAAIxC,OAAO,EAAU;IACxC,KAAAyC,qBAAqB,GAAwB,IAAI;IAEjD,KAAAC,eAAe,GAAGrD,MAAM,CAACO,eAAe,CAAC;IACzC,KAAA+C,YAAY,GAAGtD,MAAM,CAACM,YAAY,CAAC;IACnC,KAAAiD,MAAM,GAAGvD,MAAM,CAACK,MAAM,CAAC;IACvB,KAAAmD,IAAI,GAAGxD,MAAM,CAACQ,UAAU,CAAC;IAyRzB,KAAAiD,eAAe,GAAmD,IAAI;IAEtE,KAAAC,eAAe,GAAoD;MACzE,WAAW,EAAE;QAAEC,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAQ,CAAE;MAC7C,aAAa,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAQ,CAAE;MAC/C,SAAS,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAQ,CAAE;MAC3C,WAAW,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAQ,CAAE;MAC7C,QAAQ,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAQ,CAAE;MAC1C,aAAa,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAO,CAAE;MAC9C,cAAc,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAO,CAAE;MAC/C,SAAS,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAO,CAAE;MAC1C,aAAa,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAO,CAAE;MAC9C,QAAQ,EAAE;QAAED,GAAG,EAAE,MAAM;QAAEC,GAAG,EAAE,CAAC;MAAO,CAAE;MACxC,SAAS,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAO,CAAE;MAC1C,MAAM,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAO,CAAE;MACvC,aAAa,EAAE;QAAED,GAAG,EAAE,MAAM;QAAEC,GAAG,EAAE,CAAC;MAAO,CAAE;MAC7C,SAAS,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAO,CAAE;MAC1C,UAAU,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAO,CAAE;MAC3C,UAAU,EAAE;QAAED,GAAG,EAAE,CAAC,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAO,CAAE;MAC5C,SAAS,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAO,CAAE;MAC1C,eAAe,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAO,CAAE;MAChD,eAAe,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAO,CAAE;MAChD,cAAc,EAAE;QAAED,GAAG,EAAE,CAAC,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAO,CAAE;MAChD,WAAW,EAAE;QAAED,GAAG,EAAE,CAAC,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAO,CAAE;MAC7C,YAAY,EAAE;QAAED,GAAG,EAAE,CAAC,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAO,CAAE;MAC9C,UAAU,EAAE;QAAED,GAAG,EAAE,CAAC,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAO,CAAE;MAE5C,QAAQ,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAO,CAAE;MACzC,WAAW,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAO,CAAE;MAC5C,YAAY,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAM,CAAE;MAC5C,QAAQ,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAM,CAAE;MACxC,OAAO,EAAE;QAAED,GAAG,EAAE,MAAM;QAAEC,GAAG,EAAE;MAAM,CAAE;MACrC,QAAQ,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAM,CAAE;MACxC,QAAQ,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAM,CAAE;MACxC,OAAO,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MACvC,SAAS,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAM,CAAE;MACxC,WAAW,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAM,CAAE;MAC1C,UAAU,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MAC1C,QAAQ,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MACxC,YAAY,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MAC5C,UAAU,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAM,CAAE;MACzC,UAAU,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MAC1C,YAAY,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MAC5C,OAAO,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MACvC,QAAQ,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE,CAAC;MAAM,CAAE;MACxC,MAAM,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MACtC,OAAO,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAM,CAAE;MACtC,QAAQ,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MACxC,MAAM,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MACtC,WAAW,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MAC3C,QAAQ,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MACxC,QAAQ,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MACxC,QAAQ,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAM,CAAE;MACvC,SAAS,EAAE;QAAED,GAAG,EAAE,CAAC,MAAM;QAAEC,GAAG,EAAE;MAAO,CAAE;MACzC,QAAQ,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MACxC,SAAS,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MACzC,UAAU,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MAC1C,UAAU,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MAC1C,WAAW,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MAC3C,MAAM,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MACtC,aAAa,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MAC7C,QAAQ,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MACxC,QAAQ,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MACxC,MAAM,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MAEtC,QAAQ,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MACxC,OAAO,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MACvC,QAAQ,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MACxC,MAAM,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MACtC,SAAS,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MACzC,OAAO,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MACvC,SAAS,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MACzC,UAAU,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MAC1C,WAAW,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MAC3C,SAAS,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MACzC,SAAS,EAAE;QAAED,GAAG,EAAE,MAAM;QAAEC,GAAG,EAAE;MAAO,CAAE;MACxC,WAAW,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MAC3C,OAAO,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MACvC,QAAQ,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MACxC,QAAQ,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAO,CAAE;MACxC,SAAS,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAQ,CAAE;MAC1C,SAAS,EAAE;QAAED,GAAG,EAAE,MAAM;QAAEC,GAAG,EAAE;MAAQ,CAAE;MACzC,OAAO,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAQ,CAAE;MACxC,aAAa,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAQ,CAAE;MAC9C,YAAY,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAQ,CAAE;MAC7C,WAAW,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAQ,CAAE;MAC5C,WAAW,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAQ,CAAE;MAC5C,SAAS,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAQ,CAAE;MAC1C,UAAU,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAQ,CAAE;MAC3C,WAAW,EAAE;QAAED,GAAG,EAAE,MAAM;QAAEC,GAAG,EAAE;MAAQ,CAAE;MAC3C,QAAQ,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAQ,CAAE;MACzC,cAAc,EAAE;QAAED,GAAG,EAAE,MAAM;QAAEC,GAAG,EAAE;MAAQ,CAAE;MAC9C,QAAQ,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAQ,CAAE;MACzC,aAAa,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAQ,CAAE;MAC9C,OAAO,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAQ,CAAE;MACxC,OAAO,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAQ,CAAE;MACxC,WAAW,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAQ,CAAE;MAE5C,QAAQ,EAAE;QAAED,GAAG,EAAE,CAAC,OAAO;QAAEC,GAAG,EAAE;MAAQ,CAAE;MAC1C,UAAU,EAAE;QAAED,GAAG,EAAE,CAAC,OAAO;QAAEC,GAAG,EAAE;MAAQ,CAAE;MAC5C,UAAU,EAAE;QAAED,GAAG,EAAE,CAAC,OAAO;QAAEC,GAAG,EAAE;MAAQ,CAAE;MAC5C,UAAU,EAAE;QAAED,GAAG,EAAE,CAAC,OAAO;QAAEC,GAAG,EAAE;MAAQ,CAAE;MAC5C,WAAW,EAAE;QAAED,GAAG,EAAE,CAAC,OAAO;QAAEC,GAAG,EAAE;MAAQ,CAAE;MAC7C,QAAQ,EAAE;QAAED,GAAG,EAAE,CAAC,OAAO;QAAEC,GAAG,EAAE;MAAQ,CAAE;MAC1C,OAAO,EAAE;QAAED,GAAG,EAAE,CAAC,OAAO;QAAEC,GAAG,EAAE;MAAQ,CAAE;MACzC,MAAM,EAAE;QAAED,GAAG,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAQ,CAAE;MACvC,cAAc,EAAE;QAAED,GAAG,EAAE,CAAC,MAAM;QAAEC,GAAG,EAAE;MAAQ,CAAE;MAC/C,QAAQ,EAAE;QAAED,GAAG,EAAE,CAAC,OAAO;QAAEC,GAAG,EAAE;MAAQ,CAAE;MAC1C,UAAU,EAAE;QAAED,GAAG,EAAE,CAAC,OAAO;QAAEC,GAAG,EAAE;MAAQ,CAAE;MAC5C,MAAM,EAAE;QAAED,GAAG,EAAE,CAAC,OAAO;QAAEC,GAAG,EAAE;MAAQ;KACvC;EArYc;EAEfC,QAAQA,CAAA;IACN,IAAI,CAACR,eAAe,CAACS,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MACjD,IAAIA,IAAI,EAAE;QACR,IAAI,CAAC/B,MAAM,GAAG+B,IAAI,CAACC,EAAE;MACvB,CAAC,MAAM;QACL,IAAI,CAACV,MAAM,CAACW,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAClC;IACF,CAAC,CAAC;IAEF,IAAI,CAACd,qBAAqB,GAAG,IAAI,CAACD,gBAAgB,CAACgB,IAAI,CACrD1D,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,CACvB,CAACqD,SAAS,CAACjC,IAAI,IAAG;MACjB,IAAI,CAACsC,oBAAoB,CAACtC,IAAI,CAAC;IACjC,CAAC,CAAC;IAEF,IAAI,CAACe,SAAS,CAACwB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACxC,IAAI,CAAC0C,aAAa,CAACD,CAAC,CAACzC,IAAI,CAAC,CAAC;IAE3D,IAAI,CAAC2C,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACtB,qBAAqB,EAAE;MAC9B,IAAI,CAACA,qBAAqB,CAACuB,WAAW,EAAE;IAC1C;EACF;EAEMF,iBAAiBA,CAAA;IAAA,IAAAG,KAAA;IAAA,OAAAC,iBAAA;MACrBD,KAAI,CAAC3B,gBAAgB,GAAG,IAAI;MAE5B,IAAI;QACF,MAAM6B,QAAQ,SAASlE,WAAW,CAACmE,kBAAkB,CAAC;UACpDC,kBAAkB,EAAE,IAAI;UACxBC,OAAO,EAAE;SACV,CAAC;QAGF,IAAIH,QAAQ,IAAIA,QAAQ,CAACI,MAAM,EAAE;UAC/B,MAAM;YAAEC,QAAQ;YAAEC;UAAS,CAAE,GAAGN,QAAQ,CAACI,MAAM;UAE/CN,KAAI,CAACnB,eAAe,GAAG;YAAE0B,QAAQ;YAAEC;UAAS,CAAE;UAE9CR,KAAI,CAACpB,IAAI,CAAC6B,GAAG,CAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBA8BH,EAAAC,KAAK,EACtBvB,SAAS,CAAC;YACTwB,IAAI,EAAGC,IAAI,IAAI;cACb,IAAIA,IAAI,IAAIA,IAAI,CAACC,MAAM,KAAK,IAAI,EAAE;gBAEhC,MAAMC,SAAS,GAAGF,IAAI,CAACE,SAAS,GAAG,IAAI;gBAEvC,IAAIF,IAAI,CAACG,QAAQ,EAAE;kBAEjB,MAAMC,aAAa,GAAGhB,KAAI,CAAChC,SAAS,CAACiD,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACrE,KAAK,KAAK+D,IAAI,CAACG,QAAQ,CAAC;kBAC3E,IAAIC,aAAa,EAAE;oBACjBhB,KAAI,CAAC1C,KAAK,CAACE,QAAQ,GAAGwD,aAAa,CAACnE,KAAK;oBACzC;kBACF;gBACF;gBAEA,IAAI+D,IAAI,CAACG,QAAQ,EAAE;kBACjB,MAAMI,eAAe,GAAGnB,KAAI,CAACoB,mBAAmB,CAACR,IAAI,CAACG,QAAQ,CAAC;kBAC/D,IAAII,eAAe,EAAE;oBACnBnB,KAAI,CAAC1C,KAAK,CAACE,QAAQ,GAAG2D,eAAe;oBACrC;kBACF;gBACF;gBAEAnB,KAAI,CAACqB,oBAAoB,CAACP,SAAS,CAAC;cACtC;YACF,CAAC;YACDQ,KAAK,EAAGA,KAAK,IAAI;cACftB,KAAI,CAACuB,qBAAqB,EAAE;YAC9B;WACD,CAAC;QACN;MAEA,CAAC,SAAD;IAAC;EAAD;EAAQF,oBAAoBA,CAACG,WAAmB;IAE9C,MAAMC,UAAU,GAAGD,WAAW,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;IAC/C,MAAME,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACJ,WAAW,CAAC;IACvC,MAAMK,SAAS,GAAGF,IAAI,CAACG,KAAK,CAACJ,SAAS,CAAC;IACvC,MAAMK,cAAc,GAAGL,SAAS,GAAGG,SAAS;IAE5C,IAAIG,SAAiB;IAErB,IAAID,cAAc,KAAK,CAAC,EAAE;MACxB,IAAIP,WAAW,KAAK,CAAC,EAAE;QACrBQ,SAAS,GAAG,KAAK;MACnB,CAAC,MAAM;QACLA,SAAS,GAAG,MAAMP,UAAU,GAAGI,SAAS,EAAE;MAC5C;IACF,CAAC,MAAM,IAAIE,cAAc,KAAK,GAAG,EAAE;MACjCC,SAAS,GAAG,MAAMP,UAAU,GAAGI,SAAS,KAAK;IAC/C,CAAC,MAAM,IAAIE,cAAc,KAAK,IAAI,EAAE;MAClCC,SAAS,GAAG,MAAMP,UAAU,GAAGI,SAAS,KAAK;IAC/C,CAAC,MAAM,IAAIE,cAAc,KAAK,IAAI,EAAE;MAClCC,SAAS,GAAG,MAAMP,UAAU,GAAGI,SAAS,KAAK;IAC/C,CAAC,MAAM;MACLG,SAAS,GAAG,MAAMP,UAAU,GAAGE,IAAI,CAACM,KAAK,CAACP,SAAS,CAAC,EAAE;IACxD;IAGA,MAAMQ,iBAAiB,GAAG,IAAI,CAAClE,SAAS,CAACmE,MAAM,CAACjB,EAAE,IAChDA,EAAE,CAACpE,KAAK,CAACsF,QAAQ,CAAC,IAAIJ,SAAS,GAAG,CAAC,IACnCd,EAAE,CAACpE,KAAK,CAACsF,QAAQ,CAAC,IAAIJ,SAAS,CAACK,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CACvD;IAED,IAAIH,iBAAiB,CAACI,MAAM,GAAG,CAAC,EAAE;MAChC,IAAI,IAAI,CAACzD,eAAe,EAAE;QACxB,MAAM0D,WAAW,GAAG,IAAI,CAACC,0BAA0B,CAAC,IAAI,CAAC3D,eAAe,CAAC0B,QAAQ,EAAE,IAAI,CAAC1B,eAAe,CAAC2B,SAAS,EAAE0B,iBAAiB,CAAC;QACrI,IAAIK,WAAW,EAAE;UACf,IAAI,CAACjF,KAAK,CAACE,QAAQ,GAAG+E,WAAW,CAAC1F,KAAK;UACvC;QACF;MACF;MAEA,MAAM4F,WAAW,GAAG,IAAI,CAACnF,KAAK,CAACG,OAAO;MAEtC,IAAIgF,WAAW,EAAE;QACf,MAAMC,mBAAmB,GAAgC;UACvD,IAAI,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,CAAC;UAC9E,IAAI,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC;UAC9B,IAAI,EAAE,CAAC,aAAa,CAAC;UACrB,IAAI,EAAE,CAAC,cAAc,CAAC;UACtB,IAAI,EAAE,CAAC,SAAS,CAAC;UACjB,IAAI,EAAE,CAAC,aAAa,CAAC;UACrB,IAAI,EAAE,CAAC,QAAQ,CAAC;UAChB,IAAI,EAAE,CAAC,MAAM,CAAC;UACd,IAAI,EAAE,CAAC,aAAa,CAAC;UACrB,IAAI,EAAE,CAAC,SAAS,CAAC;UACjB,IAAI,EAAE,CAAC,UAAU,CAAC;UAClB,IAAI,EAAE,CAAC,eAAe,CAAC;UACvB,IAAI,EAAE,CAAC,eAAe,CAAC;UACvB,IAAI,EAAE,CAAC,cAAc,CAAC;UACtB,IAAI,EAAE,CAAC,WAAW,CAAC;UACnB,IAAI,EAAE,CAAC,YAAY,CAAC;UACpB,IAAI,EAAE,CAAC,UAAU,CAAC;UAElB,IAAI,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;UAC1B,IAAI,EAAE,CAAC,WAAW,CAAC;UACnB,IAAI,EAAE,CAAC,YAAY,CAAC;UACpB,IAAI,EAAE,CAAC,QAAQ,CAAC;UAChB,IAAI,EAAE,CAAC,OAAO,CAAC;UACf,IAAI,EAAE,CAAC,QAAQ,CAAC;UAChB,IAAI,EAAE,CAAC,OAAO,CAAC;UACf,IAAI,EAAE,CAAC,SAAS,CAAC;UACjB,IAAI,EAAE,CAAC,WAAW,CAAC;UACnB,IAAI,EAAE,CAAC,UAAU,CAAC;UAClB,IAAI,EAAE,CAAC,QAAQ,CAAC;UAChB,IAAI,EAAE,CAAC,YAAY,CAAC;UACpB,IAAI,EAAE,CAAC,UAAU,CAAC;UAClB,IAAI,EAAE,CAAC,UAAU,CAAC;UAClB,IAAI,EAAE,CAAC,YAAY,CAAC;UACpB,IAAI,EAAE,CAAC,QAAQ,CAAC;UAChB,IAAI,EAAE,CAAC,MAAM,CAAC;UACd,IAAI,EAAE,CAAC,OAAO,CAAC;UACf,IAAI,EAAE,CAAC,QAAQ,CAAC;UAChB,IAAI,EAAE,CAAC,MAAM,CAAC;UACd,IAAI,EAAE,CAAC,WAAW,CAAC;UACnB,IAAI,EAAE,CAAC,QAAQ,CAAC;UAChB,IAAI,EAAE,CAAC,QAAQ,CAAC;UAChB,IAAI,EAAE,CAAC,QAAQ,CAAC;UAChB,IAAI,EAAE,CAAC,SAAS,CAAC;UACjB,IAAI,EAAE,CAAC,QAAQ,CAAC;UAChB,IAAI,EAAE,CAAC,SAAS,CAAC;UACjB,IAAI,EAAE,CAAC,UAAU,CAAC;UAClB,IAAI,EAAE,CAAC,UAAU,CAAC;UAClB,IAAI,EAAE,CAAC,WAAW,CAAC;UACnB,IAAI,EAAE,CAAC,MAAM,CAAC;UACd,IAAI,EAAE,CAAC,aAAa,CAAC;UACrB,IAAI,EAAE,CAAC,QAAQ,CAAC;UAChB,IAAI,EAAE,CAAC,QAAQ,CAAC;UAChB,IAAI,EAAE,CAAC,MAAM,CAAC;UAEd,IAAI,EAAE,CAAC,QAAQ,CAAC;UAChB,IAAI,EAAE,CAAC,OAAO,CAAC;UACf,IAAI,EAAE,CAAC,QAAQ,CAAC;UAChB,IAAI,EAAE,CAAC,MAAM,CAAC;UACd,IAAI,EAAE,CAAC,SAAS,CAAC;UACjB,IAAI,EAAE,CAAC,OAAO,CAAC;UACf,IAAI,EAAE,CAAC,SAAS,CAAC;UACjB,IAAI,EAAE,CAAC,UAAU,CAAC;UAClB,IAAI,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;UAC9B,IAAI,EAAE,CAAC,SAAS,CAAC;UACjB,IAAI,EAAE,CAAC,WAAW,CAAC;UACnB,IAAI,EAAE,CAAC,OAAO,CAAC;UACf,IAAI,EAAE,CAAC,QAAQ,CAAC;UAChB,IAAI,EAAE,CAAC,QAAQ,CAAC;UAChB,IAAI,EAAE,CAAC,SAAS,CAAC;UACjB,IAAI,EAAE,CAAC,SAAS,CAAC;UACjB,IAAI,EAAE,CAAC,OAAO,EAAE,aAAa,CAAC;UAC9B,IAAI,EAAE,CAAC,YAAY,CAAC;UACpB,IAAI,EAAE,CAAC,WAAW,CAAC;UACnB,IAAI,EAAE,CAAC,WAAW,CAAC;UACnB,IAAI,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC;UAC7B,IAAI,EAAE,CAAC,WAAW,CAAC;UACnB,IAAI,EAAE,CAAC,QAAQ,CAAC;UAChB,IAAI,EAAE,CAAC,cAAc,CAAC;UACtB,IAAI,EAAE,CAAC,QAAQ,CAAC;UAChB,IAAI,EAAE,CAAC,aAAa,CAAC;UACrB,IAAI,EAAE,CAAC,OAAO,CAAC;UACf,IAAI,EAAE,CAAC,OAAO,CAAC;UACf,IAAI,EAAE,CAAC,WAAW,CAAC;UAEnB,IAAI,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC;UACpF,IAAI,EAAE,CAAC,MAAM,CAAC;UACd,IAAI,EAAE,CAAC,cAAc,CAAC;UACtB,IAAI,EAAE,CAAC,QAAQ,CAAC;UAChB,IAAI,EAAE,CAAC,UAAU,CAAC;UAClB,IAAI,EAAE,CAAC,MAAM,CAAC;UAEd,IAAI,EAAE,CAAC,OAAO,CAAC;UACf,IAAI,EAAE,CAAC,cAAc;SACtB;QAED,IAAIA,mBAAmB,CAACD,WAAW,CAAC,EAAE;UACpC,KAAK,MAAME,WAAW,IAAID,mBAAmB,CAACD,WAAW,CAAC,EAAE;YAC1D,MAAMG,eAAe,GAAGV,iBAAiB,CAACjB,IAAI,CAACC,EAAE,IAC/CA,EAAE,CAACpE,KAAK,CAAC+F,WAAW,EAAE,CAACT,QAAQ,CAACO,WAAW,CAACE,WAAW,EAAE,CAAC,CAC3D;YAED,IAAID,eAAe,EAAE;cACnB,IAAI,CAACtF,KAAK,CAACE,QAAQ,GAAGoF,eAAe,CAAC/F,KAAK;cAC3C;YACF;UACF;QACF;MACF;MAEA,MAAMiG,WAAW,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EACrE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,aAAa,EAAE,SAAS,EAAE,SAAS,CAAC;MAE3F,KAAK,MAAMC,IAAI,IAAID,WAAW,EAAE;QAC9B,MAAME,YAAY,GAAGd,iBAAiB,CAACjB,IAAI,CAACC,EAAE,IAC5CA,EAAE,CAACpE,KAAK,CAAC+F,WAAW,EAAE,CAACT,QAAQ,CAACW,IAAI,CAACF,WAAW,EAAE,CAAC,CACpD;QACD,IAAIG,YAAY,EAAE;UAChB,IAAI,CAAC1F,KAAK,CAACE,QAAQ,GAAGwF,YAAY,CAACnG,KAAK;UACxC;QACF;MACF;MAEA,IAAI,CAACS,KAAK,CAACE,QAAQ,GAAG0E,iBAAiB,CAAC,CAAC,CAAC,CAACrF,KAAK;IAClD,CAAC,MAAM;MACL,IAAI,CAACS,KAAK,CAACE,QAAQ,GAAG,KAAK;IAC7B;EACF;EAkHQgF,0BAA0BA,CAACzD,GAAW,EAAEC,GAAW,EAAEhB,SAA6C;IACxG,IAAI,CAACe,GAAG,IAAI,CAACC,GAAG,IAAIhB,SAAS,CAACsE,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAEvD,IAAIC,WAAW,GAA4C,IAAI;IAC/D,IAAIU,gBAAgB,GAAGC,MAAM,CAACC,SAAS;IAEvC,KAAK,MAAM3F,QAAQ,IAAIQ,SAAS,EAAE;MAChC,MAAMoF,SAAS,GAAG5F,QAAQ,CAACV,KAAK,CAACuG,KAAK,CAAC,YAAY,CAAC;MACpD,IAAI,CAACD,SAAS,EAAE;MAEhB,MAAME,QAAQ,GAAGF,SAAS,CAAC,CAAC,CAAC;MAE7B,IAAIG,UAAU,GAAwC,IAAI;MAE1D,IAAI,IAAI,CAACzE,eAAe,CAACwE,QAAQ,CAAC,EAAE;QAClCC,UAAU,GAAG,IAAI,CAACzE,eAAe,CAACwE,QAAQ,CAAC;MAC7C,CAAC,MAAM;QACL,KAAK,MAAME,SAAS,IAAI,IAAI,CAAC1E,eAAe,EAAE;UAC5C,IAAIwE,QAAQ,CAAClB,QAAQ,CAACoB,SAAS,CAAC,IAAIA,SAAS,CAACpB,QAAQ,CAACkB,QAAQ,CAAC,EAAE;YAChEC,UAAU,GAAG,IAAI,CAACzE,eAAe,CAAC0E,SAAS,CAAC;YAC5C;UACF;QACF;MACF;MAEA,IAAID,UAAU,EAAE;QACd,MAAME,QAAQ,GAAG,IAAI,CAACC,iBAAiB,CAAC3E,GAAG,EAAEC,GAAG,EAAEuE,UAAU,CAACxE,GAAG,EAAEwE,UAAU,CAACvE,GAAG,CAAC;QAEjF,IAAIyE,QAAQ,GAAGR,gBAAgB,EAAE;UAC/BA,gBAAgB,GAAGQ,QAAQ;UAC3BlB,WAAW,GAAG/E,QAAQ;QACxB;MACF;IACF;IAEA,OAAO+E,WAAW;EACpB;EAEQmB,iBAAiBA,CAACC,IAAY,EAAEC,IAAY,EAAEC,IAAY,EAAEC,IAAY;IAC9E,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,IAAI,GAAG,IAAI,CAACC,OAAO,CAACJ,IAAI,GAAGF,IAAI,CAAC;IACtC,MAAMO,IAAI,GAAG,IAAI,CAACD,OAAO,CAACH,IAAI,GAAGF,IAAI,CAAC;IACtC,MAAMlE,CAAC,GACLiC,IAAI,CAACwC,GAAG,CAACH,IAAI,GAAC,CAAC,CAAC,GAAGrC,IAAI,CAACwC,GAAG,CAACH,IAAI,GAAC,CAAC,CAAC,GACnCrC,IAAI,CAACyC,GAAG,CAAC,IAAI,CAACH,OAAO,CAACN,IAAI,CAAC,CAAC,GAAGhC,IAAI,CAACyC,GAAG,CAAC,IAAI,CAACH,OAAO,CAACJ,IAAI,CAAC,CAAC,GAC3DlC,IAAI,CAACwC,GAAG,CAACD,IAAI,GAAC,CAAC,CAAC,GAAGvC,IAAI,CAACwC,GAAG,CAACD,IAAI,GAAC,CAAC,CAAC;IACrC,MAAMG,CAAC,GAAG,CAAC,GAAG1C,IAAI,CAAC2C,KAAK,CAAC3C,IAAI,CAAC4C,IAAI,CAAC7E,CAAC,CAAC,EAAEiC,IAAI,CAAC4C,IAAI,CAAC,CAAC,GAAC7E,CAAC,CAAC,CAAC;IACtD,MAAM+D,QAAQ,GAAGM,CAAC,GAAGM,CAAC;IACtB,OAAOZ,QAAQ;EACjB;EAEQQ,OAAOA,CAACO,GAAW;IACzB,OAAOA,GAAG,IAAI7C,IAAI,CAAC8C,EAAE,GAAC,GAAG,CAAC;EAC5B;EAEQlD,qBAAqBA,CAAA;IAC3B,IAAI;MACF,MAAMmD,aAAa,GAAG,IAAIC,IAAI,EAAE,CAACC,iBAAiB,EAAE;MACpD,MAAMpD,WAAW,GAAG,CAACkD,aAAa,GAAG,EAAE;MAGvC,IAAI,CAACrD,oBAAoB,CAACG,WAAW,CAAC;IACxC,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,IAAI,CAAChE,KAAK,CAACE,QAAQ,GAAG,KAAK;IAC7B;EACF;EAEQ4D,mBAAmBA,CAACL,QAAgB;IAC1C,MAAM8D,KAAK,GAAG9D,QAAQ,CAAC+D,KAAK,CAAC,GAAG,CAAC;IACjC,IAAID,KAAK,CAACvC,MAAM,IAAI,CAAC,EAAE;MACrB,MAAMyC,SAAS,GAAGF,KAAK,CAAC,CAAC,CAAC;MAE1B,MAAM1D,eAAe,GAAG,IAAI,CAACnD,SAAS,CAACiD,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACrE,KAAK,CAACmI,UAAU,CAACD,SAAS,GAAG,GAAG,CAAC,CAAC;MACvF,IAAI5D,eAAe,EAAE;QACnB,OAAOA,eAAe,CAACtE,KAAK;MAC9B;IACF;IAEA,OAAO,IAAI;EACb;EAEQoI,uBAAuBA,CAAA;IAE7B,IAAI,CAACrG,IAAI,CAAC6B,GAAG,CAAM,wBAAwB,CAAC,CAACtB,SAAS,CAAC;MACrDwB,IAAI,EAAGC,IAAI,IAAI;QACb,IAAIA,IAAI,IAAIA,IAAI,CAACsE,YAAY,EAAE;UAC7B,IAAI,CAAC5H,KAAK,CAACG,OAAO,GAAGmD,IAAI,CAACsE,YAAY;UAEtC,MAAMC,YAAY,GAAG,IAAI,CAAClH,SAAS,CAACgD,IAAI,CAACoD,CAAC,IAAIA,CAAC,CAACpH,IAAI,KAAK2D,IAAI,CAACsE,YAAY,CAAC;UAC3E,IAAI,CAACC,YAAY,EAAE;YACjB,IAAI,CAAC7H,KAAK,CAACG,OAAO,GAAG,IAAI;UAC3B;UAEA,IAAImD,IAAI,CAACpD,QAAQ,EAAE;YAEjB,MAAMwD,aAAa,GAAG,IAAI,CAAChD,SAAS,CAACiD,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACrE,KAAK,KAAK+D,IAAI,CAACpD,QAAQ,CAAC;YAC3E,IAAIwD,aAAa,EAAE;cACjB,IAAI,CAAC1D,KAAK,CAACE,QAAQ,GAAGwD,aAAa,CAACnE,KAAK;YAC3C,CAAC,MAAM;cACL,MAAMsE,eAAe,GAAG,IAAI,CAACC,mBAAmB,CAACR,IAAI,CAACpD,QAAQ,CAAC;cAC/D,IAAI2D,eAAe,EAAE;gBACnB,IAAI,CAAC7D,KAAK,CAACE,QAAQ,GAAG2D,eAAe;cACvC,CAAC,MAAM,IAAIP,IAAI,CAACwE,UAAU,EAAE;gBAC1B,MAAM5D,WAAW,GAAG6D,QAAQ,CAACzE,IAAI,CAACwE,UAAU,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GACzCD,QAAQ,CAACzE,IAAI,CAACwE,UAAU,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAG;gBACnE,IAAI,CAACjE,oBAAoB,CAACG,WAAW,CAAC;cACxC,CAAC,MAAM;gBACL,IAAI,CAACD,qBAAqB,EAAE;cAC9B;YACF;UACF,CAAC,MAAM;YACL,IAAI,CAACA,qBAAqB,EAAE;UAC9B;QACF,CAAC,MAAM;UACL,IAAI,CAACjE,KAAK,CAACG,OAAO,GAAG,IAAI;UACzB,IAAI,CAAC8D,qBAAqB,EAAE;QAC9B;QACA,IAAI,CAAClD,gBAAgB,GAAG,KAAK;MAC/B,CAAC;MACDiD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChE,KAAK,CAACG,OAAO,GAAG,IAAI;QACzB,IAAI,CAAC8D,qBAAqB,EAAE;QAC5B,IAAI,CAAClD,gBAAgB,GAAG,KAAK;MAC/B;KACD,CAAC;EACJ;EAEAkH,cAAcA,CAAA;IAAA,IAAAC,gBAAA;IACZ,MAAMtI,IAAI,IAAAsI,gBAAA,GAAG,IAAI,CAAClI,KAAK,CAACJ,IAAI,cAAAsI,gBAAA,uBAAfA,gBAAA,CAAiBC,IAAI,EAAE;IAEpC,IAAI,CAACrH,aAAa,GAAG,KAAK;IAE1B,IAAI,CAAClB,IAAI,EAAE;MACT,IAAI,CAACR,SAAS,GAAG,EAAE;MACnB,IAAI,CAACyB,YAAY,GAAG,KAAK;MACzB;IACF;IAEA,IAAI,CAACA,YAAY,GAAG,IAAI;IAExB,IAAI,CAACI,gBAAgB,CAACoC,IAAI,CAACzD,IAAI,CAAC;EAClC;EAEMsC,oBAAoBA,CAACtC,IAAY;IAAA,IAAAwI,MAAA;IAAA,OAAAzF,iBAAA;MACrC,IAAI,CAAC/C,IAAI,EAAE;QACTwI,MAAI,CAAChJ,SAAS,GAAG,EAAE;QACnBgJ,MAAI,CAACvH,YAAY,GAAG,KAAK;QACzB;MACF;MAEA,IAAI;QACF,MAAM;UAAEyC,IAAI;UAAEU;QAAK,CAAE,SAASoE,MAAI,CAACjH,eAAe,CAACkH,SAAS,EAAE,CAC3DC,IAAI,CAAC,QAAQ,CAAC,CACdC,MAAM,CAAC,IAAI,CAAC,CACZC,EAAE,CAAC,MAAM,EAAE5I,IAAI,CAAC,CAChB6I,KAAK,CAAC,CAAC,CAAC;QAEXL,MAAI,CAACvH,YAAY,GAAG,KAAK;QAEzB,IAAImD,KAAK,EAAE;UACToE,MAAI,CAAChJ,SAAS,GAAG,wCAAwC;UACzD;QACF;QAEA,IAAIkE,IAAI,IAAIA,IAAI,CAAC0B,MAAM,GAAG,CAAC,EAAE;UAC3BoD,MAAI,CAAChJ,SAAS,GAAG,kCAAkC;UACnDgJ,MAAI,CAACtH,aAAa,GAAG,KAAK;QAC5B,CAAC,MAAM;UACLsH,MAAI,CAAChJ,SAAS,GAAG,EAAE;UACnBgJ,MAAI,CAACtH,aAAa,GAAG,IAAI;QAC3B;MACF,CAAC,CAAC,OAAOkD,KAAK,EAAE;QACdoE,MAAI,CAACvH,YAAY,GAAG,KAAK;QACzBuH,MAAI,CAAChJ,SAAS,GAAG,wCAAwC;MAC3D;IAAC;EACH;EAGMsJ,WAAWA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAhG,iBAAA;MACf,IAAI,CAACgG,MAAI,CAAC5I,MAAM,EAAE;QAChB4I,MAAI,CAACxJ,YAAY,GAAG,yCAAyC;QAC7D;MACF;MAEA,IAAI,CAACwJ,MAAI,CAAC3I,KAAK,CAACJ,IAAI,IAAI,CAAC+I,MAAI,CAAC3I,KAAK,CAACC,KAAK,IAAI,CAAC0I,MAAI,CAAC3I,KAAK,CAACE,QAAQ,IAAI,CAACyI,MAAI,CAAC3I,KAAK,CAACG,OAAO,EAAE;QACxFwI,MAAI,CAACxJ,YAAY,GAAG,oCAAoC;QACxD;MACF;MAEA,IAAIwJ,MAAI,CAACvJ,SAAS,EAAE;QAClBuJ,MAAI,CAACxJ,YAAY,GAAG,sCAAsC;QAC1D;MACF;MAEAwJ,MAAI,CAAC/H,YAAY,GAAG,IAAI;MACxB+H,MAAI,CAACxJ,YAAY,GAAG,EAAE;MAEtB,IAAI;QACF,MAAMyJ,QAAQ,GAAkC;UAC9ChJ,IAAI,EAAE+I,MAAI,CAAC3I,KAAK,CAACJ,IAAI;UACrBK,KAAK,EAAE0I,MAAI,CAAC3I,KAAK,CAACC,KAAK;UACvBC,QAAQ,EAAEyI,MAAI,CAAC3I,KAAK,CAACE,QAAQ;UAC7BC,OAAO,EAAEwI,MAAI,CAAC3I,KAAK,CAACG,OAAO;UAC3B0I,QAAQ,EAAEF,MAAI,CAAC5I,MAAM;UACrBK,KAAK,EAAE,CAAC;UACRC,WAAW,EAAE,CAAC;UACdC,QAAQ,EAAE,CAAC;UACXC,SAAS,EAAE,CAAC;UACZC,YAAY,EAAE,CAAC;UACfC,iBAAiB,EAAE;SACpB;QAED,MAAMqI,OAAO,SAASH,MAAI,CAACvH,YAAY,CAACsH,WAAW,CAACE,QAAQ,CAAC;QAE7DD,MAAI,CAACtH,MAAM,CAACW,QAAQ,CAAC,CAAC,SAAS,EAAE8G,OAAO,CAAC,CAAC;MAC5C,CAAC,CAAC,OAAO9E,KAAU,EAAE;QACnB2E,MAAI,CAACxJ,YAAY,GAAG6E,KAAK,CAAC+E,OAAO,IAAI,2CAA2C;MAClF,CAAC,SAAS;QACRJ,MAAI,CAAC/H,YAAY,GAAG,KAAK;MAC3B;IAAC;EACH;;mBAp+BWf,eAAe;;mCAAfA,gBAAe;AAAA;;QAAfA,gBAAe;EAAAmJ,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCjBxB1K,EAFJ,CAAAC,cAAA,aAAuB,aACb,aACY;MAChBD,EAAA,CAAAa,SAAA,aAA6D;MAC7Db,EAAA,CAAAC,cAAA,WAAM;MAAAD,EAAA,CAAAE,MAAA,cAAO;MACfF,EADe,CAAAG,YAAA,EAAO,EAChB;MACNH,EAAA,CAAAC,cAAA,SAAI;MAAAD,EAAA,CAAAE,MAAA,uBAAgB;MACtBF,EADsB,CAAAG,YAAA,EAAK,EAClB;MACTH,EAAA,CAAAC,cAAA,WAAgD;MAAAD,EAAA,CAAAE,MAAA,4BAAqB;MAAAF,EAAA,CAAAG,YAAA,EAAI;MAEzEH,EAAA,CAAA4K,UAAA,KAAAC,+BAAA,iBAA2C;MAI3C7K,EAAA,CAAAC,cAAA,eAAoD;MAA9CD,EAAA,CAAA8K,UAAA,sBAAAC,mDAAA;QAAA,OAAYJ,GAAA,CAAAb,WAAA,EAAa;MAAA,EAAC;MAE5B9J,EADF,CAAAC,cAAA,cAAwB,gBACJ;MAAAD,EAAA,CAAAE,MAAA,kBAAU;MAAAF,EAAA,CAAAG,YAAA,EAAQ;MAEfH,EADrB,CAAAC,cAAA,cAAyB,cACJ,iBAA8F;MAA3ED,EAAA,CAAAgL,gBAAA,2BAAAC,yDAAAC,MAAA;QAAAlL,EAAA,CAAAmL,kBAAA,CAAAR,GAAA,CAAAvJ,KAAA,CAAAC,KAAA,EAAA6J,MAAA,MAAAP,GAAA,CAAAvJ,KAAA,CAAAC,KAAA,GAAA6J,MAAA;QAAA,OAAAA,MAAA;MAAA,EAAyB;MAAkDlL,EAA9F,CAAAG,YAAA,EAA8F,EAAM;MACvHH,EAAA,CAAAC,cAAA,iBAAqI;MAAlHD,EAAA,CAAAgL,gBAAA,2BAAAI,yDAAAF,MAAA;QAAAlL,EAAA,CAAAmL,kBAAA,CAAAR,GAAA,CAAAvJ,KAAA,CAAAJ,IAAA,EAAAkK,MAAA,MAAAP,GAAA,CAAAvJ,KAAA,CAAAJ,IAAA,GAAAkK,MAAA;QAAA,OAAAA,MAAA;MAAA,EAAwB;MAA+DlL,EAAA,CAAA8K,UAAA,mBAAAO,iDAAA;QAAA,OAASV,GAAA,CAAAtB,cAAA,EAAgB;MAAA,EAAC;MACtIrJ,EADE,CAAAG,YAAA,EAAqI,EACjI;MAGNH,EAFA,CAAA4K,UAAA,KAAAU,+BAAA,kBAA6C,KAAAC,+BAAA,kBACM,KAAAC,+BAAA,kBACA;MACrDxL,EAAA,CAAAG,YAAA,EAAM;MAGJH,EADF,CAAAC,cAAA,cAAwB,iBACA;MAAAD,EAAA,CAAAE,MAAA,gBAAQ;MAAAF,EAAA,CAAAG,YAAA,EAAQ;MAEpCH,EADF,CAAAC,cAAA,eAA4B,kBACgF;MAAlGD,EAAA,CAAAgL,gBAAA,2BAAAS,0DAAAP,MAAA;QAAAlL,EAAA,CAAAmL,kBAAA,CAAAR,GAAA,CAAAvJ,KAAA,CAAAE,QAAA,EAAA4J,MAAA,MAAAP,GAAA,CAAAvJ,KAAA,CAAAE,QAAA,GAAA4J,MAAA;QAAA,OAAAA,MAAA;MAAA,EAA4B;MAClClL,EAAA,CAAA4K,UAAA,KAAAc,kCAAA,qBAAwD;MAC1D1L,EAAA,CAAAG,YAAA,EAAS;MACTH,EAAA,CAAA4K,UAAA,KAAAe,+BAAA,kBAAwD;MAG1D3L,EAAA,CAAAG,YAAA,EAAM;MACNH,EAAA,CAAAC,cAAA,eAAuB;MAErBD,EADA,CAAA4K,UAAA,KAAAgB,gCAAA,mBAA+B,KAAAC,gCAAA,mBACC;MAEpC7L,EADE,CAAAG,YAAA,EAAM,EACF;MAGJH,EADF,CAAAC,cAAA,cAAwB,iBACD;MAAAD,EAAA,CAAAE,MAAA,eAAO;MAAAF,EAAA,CAAAG,YAAA,EAAQ;MAElCH,EADF,CAAAC,cAAA,eAA4B,kBAC6E;MAA/FD,EAAA,CAAAgL,gBAAA,2BAAAc,0DAAAZ,MAAA;QAAAlL,EAAA,CAAAmL,kBAAA,CAAAR,GAAA,CAAAvJ,KAAA,CAAAG,OAAA,EAAA2J,MAAA,MAAAP,GAAA,CAAAvJ,KAAA,CAAAG,OAAA,GAAA2J,MAAA;QAAA,OAAAA,MAAA;MAAA,EAA2B;MACjClL,EAAA,CAAA4K,UAAA,KAAAmB,kCAAA,qBAAiE;MACnE/L,EAAA,CAAAG,YAAA,EAAS;MACTH,EAAA,CAAA4K,UAAA,KAAAoB,+BAAA,kBAAwD;MAG1DhM,EAAA,CAAAG,YAAA,EAAM;MACNH,EAAA,CAAAC,cAAA,eAAuB;MAErBD,EADA,CAAA4K,UAAA,KAAAqB,gCAAA,mBAA+B,KAAAC,gCAAA,mBACC;MAEpClM,EADE,CAAAG,YAAA,EAAM,EACF;MAENH,EAAA,CAAAC,cAAA,kBAA4F;MAC1FD,EAAA,CAAAE,MAAA,IACF;MAEJF,EAFI,CAAAG,YAAA,EAAS,EACJ,EACH;;;MAtDDH,EAAA,CAAAI,SAAA,GAA0B;MAA1BJ,EAAA,CAAAS,UAAA,eAAAT,EAAA,CAAAmM,eAAA,KAAAC,GAAA,EAA0B;MAEvBpM,EAAA,CAAAI,SAAA,GAAkB;MAAlBJ,EAAA,CAAAS,UAAA,SAAAkK,GAAA,CAAApK,YAAA,CAAkB;MAQoBP,EAAA,CAAAI,SAAA,GAAyB;MAAzBJ,EAAA,CAAAqM,gBAAA,YAAA1B,GAAA,CAAAvJ,KAAA,CAAAC,KAAA,CAAyB;MAC5CrB,EAAA,CAAAI,SAAA,EAAwB;MAAxBJ,EAAA,CAAAqM,gBAAA,YAAA1B,GAAA,CAAAvJ,KAAA,CAAAJ,IAAA,CAAwB;MAEvChB,EAAA,CAAAI,SAAA,EAAe;MAAfJ,EAAA,CAAAS,UAAA,SAAAkK,GAAA,CAAAnK,SAAA,CAAe;MACfR,EAAA,CAAAI,SAAA,EAAkB;MAAlBJ,EAAA,CAAAS,UAAA,SAAAkK,GAAA,CAAA1I,YAAA,CAAkB;MAClBjC,EAAA,CAAAI,SAAA,EAAmB;MAAnBJ,EAAA,CAAAS,UAAA,SAAAkK,GAAA,CAAAzI,aAAA,CAAmB;MAMflC,EAAA,CAAAI,SAAA,GAA4B;MAA5BJ,EAAA,CAAAqM,gBAAA,YAAA1B,GAAA,CAAAvJ,KAAA,CAAAE,QAAA,CAA4B;MAAwCtB,EAAA,CAAAS,UAAA,aAAAkK,GAAA,CAAAxI,gBAAA,CAA6B;MAChFnC,EAAA,CAAAI,SAAA,EAAY;MAAZJ,EAAA,CAAAS,UAAA,YAAAkK,GAAA,CAAA7I,SAAA,CAAY;MAE/B9B,EAAA,CAAAI,SAAA,EAAsB;MAAtBJ,EAAA,CAAAS,UAAA,SAAAkK,GAAA,CAAAxI,gBAAA,CAAsB;MAKrBnC,EAAA,CAAAI,SAAA,GAAsB;MAAtBJ,EAAA,CAAAS,UAAA,SAAAkK,GAAA,CAAAxI,gBAAA,CAAsB;MACtBnC,EAAA,CAAAI,SAAA,EAAuB;MAAvBJ,EAAA,CAAAS,UAAA,UAAAkK,GAAA,CAAAxI,gBAAA,CAAuB;MAOtBnC,EAAA,CAAAI,SAAA,GAA2B;MAA3BJ,EAAA,CAAAqM,gBAAA,YAAA1B,GAAA,CAAAvJ,KAAA,CAAAG,OAAA,CAA2B;MAAsCvB,EAAA,CAAAS,UAAA,aAAAkK,GAAA,CAAAxI,gBAAA,CAA6B;MACxEnC,EAAA,CAAAI,SAAA,EAAY;MAAZJ,EAAA,CAAAS,UAAA,YAAAkK,GAAA,CAAA5I,SAAA,CAAY;MAEpC/B,EAAA,CAAAI,SAAA,EAAsB;MAAtBJ,EAAA,CAAAS,UAAA,SAAAkK,GAAA,CAAAxI,gBAAA,CAAsB;MAKrBnC,EAAA,CAAAI,SAAA,GAAsB;MAAtBJ,EAAA,CAAAS,UAAA,SAAAkK,GAAA,CAAAxI,gBAAA,CAAsB;MACtBnC,EAAA,CAAAI,SAAA,EAAuB;MAAvBJ,EAAA,CAAAS,UAAA,UAAAkK,GAAA,CAAAxI,gBAAA,CAAuB;MAImBnC,EAAA,CAAAI,SAAA,EAAsC;MAAtCJ,EAAA,CAAAS,UAAA,aAAAkK,GAAA,CAAA3I,YAAA,IAAA2I,GAAA,CAAAnK,SAAA,CAAsC;MACzFR,EAAA,CAAAI,SAAA,EACF;MADEJ,EAAA,CAAAsM,kBAAA,MAAA3B,GAAA,CAAA3I,YAAA,uCACF;;;iBD3CQ3C,WAAW,EAAAkN,EAAA,CAAAC,UAAA,EAAAD,EAAA,CAAAE,0BAAA,EAAEtN,YAAY,EAAAuN,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAExN,WAAW,EAAAyN,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,uBAAA,EAAAH,EAAA,CAAAI,oBAAA,EAAAJ,EAAA,CAAAK,0BAAA,EAAAL,EAAA,CAAAM,eAAA,EAAAN,EAAA,CAAAO,oBAAA,EAAAP,EAAA,CAAAQ,iBAAA,EAAAR,EAAA,CAAAS,OAAA,EAAAT,EAAA,CAAAU,MAAA,EAAEjO,YAAY,EAAAkO,EAAA,CAAAC,UAAA,EAAE1N,mBAAmB;EAAA2N,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}