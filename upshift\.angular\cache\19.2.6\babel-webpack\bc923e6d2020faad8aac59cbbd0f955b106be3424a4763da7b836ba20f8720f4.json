{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/work-things/vlastne/upshift_project/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _GroupListPage;\nimport { inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule, ToastController } from '@ionic/angular';\nimport { RouterModule, Router } from '@angular/router';\nimport { GroupService } from '../../../services/group.service';\nimport { take } from 'rxjs';\nimport { SupabaseService } from '../../../services/supabase.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/router\";\nconst _c0 = () => [\"/create-group\"];\nconst _c1 = () => [\"/group-requests\"];\nconst _c2 = a0 => [\"/groups\", a0];\nfunction GroupListPage_section_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 15)(1, \"div\", 16)(2, \"a\", 17)(3, \"span\", 18);\n    i0.ɵɵtext(4, \"\\uD83D\\uDC65\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h2\");\n    i0.ɵɵtext(6, \"Join Requests\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 19);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(2, _c1));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.joinRequests.length);\n  }\n}\nfunction GroupListPage_div_27_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 24)(4, \"h3\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"a\", 25);\n    i0.ɵɵtext(7, \"Show all quests\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const group_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(group_r3.emoji);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(group_r3.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(3, _c2, group_r3.id));\n  }\n}\nfunction GroupListPage_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtemplate(1, GroupListPage_div_27_div_1_Template, 8, 5, \"div\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.groups);\n  }\n}\nfunction GroupListPage_ng_template_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"No groups yet.\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class GroupListPage {\n  constructor() {\n    this.userId = null;\n    this.groups = [];\n    this.joinRequests = [];\n    this.invitationCode = '';\n    this.supabaseService = inject(SupabaseService);\n    this.groupService = inject(GroupService);\n    this.router = inject(Router);\n    this.toastController = inject(ToastController);\n  }\n  ngOnInit() {\n    this.supabaseService.currentUser$.pipe(take(1)).subscribe(authUser => {\n      if (authUser) {\n        this.userId = authUser.id;\n        this.loadGroups();\n        this.loadJoinRequests();\n      } else {}\n    });\n  }\n  loadGroups() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this.userId) return;\n      return new Promise(resolve => {\n        _this.groupService.getUserGroups(_this.userId).subscribe(groups => {\n          _this.groups = groups;\n          resolve();\n        });\n      });\n    })();\n  }\n  loadJoinRequests() {\n    if (!this.userId) {\n      return;\n    }\n    this.supabaseService.getClient().from('profiles').select('username').eq('id', this.userId).single().then(response => {\n      if (response.error) {\n        return;\n      }\n      const username = response.data.username;\n      this.supabaseService.getClient().from('group_join_requests').select('*').ilike('username_invited', username).then(directResponse => {\n        if (directResponse.error) {} else {\n          this.joinRequests = directResponse.data;\n        }\n      });\n    });\n    this.groupService.getJoinRequestsForUserId(this.userId).subscribe({\n      next: requests => {},\n      error: error => {}\n    });\n  }\n  joinGroupByCode() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.userId || !_this2.invitationCode.trim()) return;\n      try {\n        const success = yield _this2.groupService.joinGroupByCode(_this2.userId, _this2.invitationCode.trim());\n        if (success) {\n          const toast = yield _this2.toastController.create({\n            message: 'Successfully joined the group!',\n            duration: 2000,\n            position: 'bottom',\n            color: 'success'\n          });\n          yield toast.present();\n          _this2.invitationCode = '';\n          yield _this2.loadGroups();\n          window.location.reload();\n        } else {\n          const toast = yield _this2.toastController.create({\n            message: 'Invalid or expired invitation code.',\n            duration: 2000,\n            position: 'bottom',\n            color: 'danger'\n          });\n          yield toast.present();\n        }\n      } catch (error) {\n        const toast = yield _this2.toastController.create({\n          message: 'Failed to join group. Please try again.',\n          duration: 2000,\n          position: 'bottom',\n          color: 'danger'\n        });\n        yield toast.present();\n      }\n    })();\n  }\n  goToLeaderboard() {\n    window.location.href = '/leaderboard/groups';\n  }\n  refreshData() {\n    if (this.userId) {\n      this.supabaseService.getClient().from('profiles').select('username').eq('id', this.userId).single().then(response => {\n        if (response.error) {} else {\n          const username = response.data.username;\n          this.supabaseService.getClient().from('group_join_requests').select('*').ilike('username_invited', username).then(joinResponse => {\n            if (joinResponse.error) {} else {\n              this.joinRequests = joinResponse.data;\n            }\n          });\n        }\n      });\n      this.loadGroups();\n    }\n  }\n}\n_GroupListPage = GroupListPage;\n_GroupListPage.ɵfac = function GroupListPage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _GroupListPage)();\n};\n_GroupListPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _GroupListPage,\n  selectors: [[\"app-group-list\"]],\n  decls: 30,\n  vars: 6,\n  consts: [[\"noGroups\", \"\"], [1, \"container\"], [1, \"logo\"], [\"src\", \"assets/images/upshift_icon_mini.svg\", \"alt\", \"Upshift\"], [\"class\", \"join-requests\", 4, \"ngIf\"], [1, \"group-actions\"], [1, \"btn\", \"primary\", \"full-width\", 3, \"routerLink\"], [1, \"join-group-section\"], [1, \"join-group-form\", 3, \"ngSubmit\"], [\"type\", \"text\", \"name\", \"invitation_code\", \"placeholder\", \"Enter invitation code\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"submit\", 1, \"btn\", \"secondary\"], [1, \"your-groups\"], [1, \"section-header\"], [\"id\", \"leaderboard-btn\", 1, \"leaderboard-btn\", 3, \"click\"], [\"class\", \"group-list\", 4, \"ngIf\", \"ngIfElse\"], [1, \"join-requests\"], [1, \"card\"], [1, \"card-header\", \"link\", 3, \"routerLink\"], [1, \"icon\"], [1, \"badge\"], [1, \"group-list\"], [\"class\", \"group-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"group-card\"], [1, \"group-icon\"], [1, \"group-info\"], [1, \"group-link\", 3, \"routerLink\"]],\n  template: function GroupListPage_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r1 = i0.ɵɵgetCurrentView();\n      i0.ɵɵelementStart(0, \"div\", 1)(1, \"header\")(2, \"div\", 2);\n      i0.ɵɵelement(3, \"img\", 3);\n      i0.ɵɵelementStart(4, \"span\");\n      i0.ɵɵtext(5, \"Upshift\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(6, \"h1\");\n      i0.ɵɵtext(7, \"Groups\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(8, GroupListPage_section_8_Template, 9, 3, \"section\", 4);\n      i0.ɵɵelementStart(9, \"section\", 5)(10, \"a\", 6);\n      i0.ɵɵtext(11, \"+ Create Group\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(12, \"div\", 7)(13, \"h3\");\n      i0.ɵɵtext(14, \"Join a Group\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(15, \"p\");\n      i0.ɵɵtext(16, \"Enter an invitation code to join a group:\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(17, \"form\", 8);\n      i0.ɵɵlistener(\"ngSubmit\", function GroupListPage_Template_form_ngSubmit_17_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.joinGroupByCode());\n      });\n      i0.ɵɵelementStart(18, \"input\", 9);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function GroupListPage_Template_input_ngModelChange_18_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        i0.ɵɵtwoWayBindingSet(ctx.invitationCode, $event) || (ctx.invitationCode = $event);\n        return i0.ɵɵresetView($event);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(19, \"button\", 10);\n      i0.ɵɵtext(20, \"Join Group\");\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(21, \"section\", 11)(22, \"div\", 12)(23, \"h2\");\n      i0.ɵɵtext(24, \"Your Groups\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(25, \"button\", 13);\n      i0.ɵɵlistener(\"click\", function GroupListPage_Template_button_click_25_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.goToLeaderboard());\n      });\n      i0.ɵɵtext(26, \"Leaderboard\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(27, GroupListPage_div_27_Template, 2, 1, \"div\", 14)(28, GroupListPage_ng_template_28_Template, 2, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd()();\n    }\n    if (rf & 2) {\n      const noGroups_r4 = i0.ɵɵreference(29);\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"ngIf\", ctx.joinRequests && ctx.joinRequests.length > 0);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(5, _c0));\n      i0.ɵɵadvance(8);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.invitationCode);\n      i0.ɵɵadvance(9);\n      i0.ɵɵproperty(\"ngIf\", ctx.groups && ctx.groups.length > 0)(\"ngIfElse\", noGroups_r4);\n    }\n  },\n  dependencies: [IonicModule, i1.RouterLinkWithHrefDelegate, CommonModule, i2.NgForOf, i2.NgIf, FormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.NgModel, i3.NgForm, RouterModule, i4.RouterLink],\n  styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%];\\n\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\tvar __webpack_modules__ = ({\\n\\n\\n 104:\\n\\n\\n\\n\\n\\n (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/sass-loader/dist/cjs.js):\\\\nexpected \\\\\\\"{\\\\\\\".\\\\n   \\u2577\\\\n64 \\u2502  Adjust container padding to account for navigation bar */\\\\r\\\\n   \\u2502                                                           ^\\\\n   \\u2575\\\\n  src\\\\\\\\app\\\\\\\\pages\\\\\\\\groups\\\\\\\\group-list\\\\\\\\group-list.page.scss 64:59  root stylesheet\\\");\\n\\n\\n })\\n\\n\\n \\t});\\n\\n\\n\\n \\t\\n\\n \\t// startup\\n\\n \\t// Load entry module and return exports\\n\\n \\t// This entry module doesn't tell about it's top-level declarations so it can't be inlined\\n\\n \\tvar __webpack_exports__ = {};\\n\\n \\t__webpack_modules__[104]();\\n\\n \\tresource = __webpack_exports__;\\n\\n \\t\\n\\n })()\\n;\"]\n});", "map": {"version": 3, "names": ["inject", "CommonModule", "FormsModule", "IonicModule", "ToastController", "RouterModule", "Router", "GroupService", "take", "SupabaseService", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c1", "ɵɵtextInterpolate", "ctx_r1", "joinRequests", "length", "group_r3", "emoji", "name", "ɵɵpureFunction1", "_c2", "id", "ɵɵtemplate", "GroupListPage_div_27_div_1_Template", "groups", "GroupListPage", "constructor", "userId", "invitationCode", "supabaseService", "groupService", "router", "toastController", "ngOnInit", "currentUser$", "pipe", "subscribe", "authUser", "loadGroups", "loadJoinRequests", "_this", "_asyncToGenerator", "Promise", "resolve", "getUserGroups", "getClient", "from", "select", "eq", "single", "then", "response", "error", "username", "data", "ilike", "directResponse", "getJoinRequestsForUserId", "next", "requests", "joinGroupByCode", "_this2", "trim", "success", "toast", "create", "message", "duration", "position", "color", "present", "window", "location", "reload", "goToLeaderboard", "href", "refreshData", "joinResponse", "selectors", "decls", "vars", "consts", "template", "GroupListPage_Template", "rf", "ctx", "ɵɵelement", "GroupListPage_section_8_Template", "ɵɵlistener", "GroupListPage_Template_form_ngSubmit_17_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "ɵɵtwoWayListener", "GroupListPage_Template_input_ngModelChange_18_listener", "$event", "ɵɵtwoWayBindingSet", "GroupListPage_Template_button_click_25_listener", "GroupListPage_div_27_Template", "GroupListPage_ng_template_28_Template", "ɵɵtemplateRefExtractor", "_c0", "ɵɵtwoWayProperty", "noGroups_r4", "i1", "RouterLinkWithHrefDelegate", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "NgModel", "NgForm", "i4", "RouterLink", "styles"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\groups\\group-list\\group-list.page.ts", "C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\app\\pages\\groups\\group-list\\group-list.page.html"], "sourcesContent": ["import { Component, OnInit, inject } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IonicModule, ToastController } from '@ionic/angular';\r\nimport { RouterModule, Router } from '@angular/router';\r\nimport { GroupService } from '../../../services/group.service';\r\nimport { Group, GroupJoinRequest } from '../../../models/group.model';\r\nimport { take } from 'rxjs';\r\nimport { SupabaseService } from '../../../services/supabase.service';\r\n\r\n@Component({\r\n  selector: 'app-group-list',\r\n  templateUrl: './group-list.page.html',\r\n  styleUrls: ['./group-list.page.scss'],\r\n  standalone: true,\r\n  imports: [IonicModule, CommonModule, FormsModule, RouterModule]\r\n})\r\nexport class GroupListPage implements OnInit {\r\n  userId: string | null = null;\r\n\r\n  groups: Group[] = [];\r\n  joinRequests: GroupJoinRequest[] = [];\r\n\r\n  invitationCode = '';\r\n\r\n  private supabaseService = inject(SupabaseService);\r\n  private groupService = inject(GroupService);\r\n  private router = inject(Router);\r\n  private toastController = inject(ToastController);\r\n\r\n  constructor() {}\r\n\r\n  ngOnInit() {\r\n\r\n    this.supabaseService.currentUser$.pipe(\r\n      take(1)\r\n    ).subscribe(authUser => {\r\n\r\n      if (authUser) {\r\n        this.userId = authUser.id;\r\n\r\n\r\n        this.loadGroups();\r\n        this.loadJoinRequests();\r\n      } else {\r\n      }\r\n    });\r\n  }\r\n\r\n  async loadGroups() {\r\n    if (!this.userId) return;\r\n\r\n    return new Promise<void>((resolve) => {\r\n      this.groupService.getUserGroups(this.userId!).subscribe(groups => {\r\n        this.groups = groups;\r\n        resolve();\r\n      });\r\n    });\r\n  }\r\n\r\n  loadJoinRequests() {\r\n    if (!this.userId) {\r\n      return;\r\n    }\r\n\r\n\r\n    this.supabaseService.getClient()\r\n      .from('profiles')\r\n      .select('username')\r\n      .eq('id', this.userId)\r\n      .single()\r\n      .then(response => {\r\n        if (response.error) {\r\n          return;\r\n        }\r\n\r\n        const username = response.data.username;\r\n\r\n        this.supabaseService.getClient()\r\n          .from('group_join_requests')\r\n          .select('*')\r\n          .ilike('username_invited', username)\r\n          .then(directResponse => {\r\n\r\n            if (directResponse.error) {\r\n            } else {\r\n\r\n              this.joinRequests = directResponse.data;\r\n\r\n            }\r\n          });\r\n      });\r\n\r\n    this.groupService.getJoinRequestsForUserId(this.userId).subscribe({\r\n      next: (requests) => {\r\n      },\r\n      error: (error) => {\r\n      }\r\n    });\r\n  }\r\n\r\n  async joinGroupByCode() {\r\n    if (!this.userId || !this.invitationCode.trim()) return;\r\n\r\n    try {\r\n      const success = await this.groupService.joinGroupByCode(this.userId!, this.invitationCode.trim());\r\n\r\n      if (success) {\r\n        const toast = await this.toastController.create({\r\n          message: 'Successfully joined the group!',\r\n          duration: 2000,\r\n          position: 'bottom',\r\n          color: 'success'\r\n        });\r\n        await toast.present();\r\n\r\n        this.invitationCode = '';\r\n\r\n        await this.loadGroups();\r\n\r\n        window.location.reload();\r\n      } else {\r\n        const toast = await this.toastController.create({\r\n          message: 'Invalid or expired invitation code.',\r\n          duration: 2000,\r\n          position: 'bottom',\r\n          color: 'danger'\r\n        });\r\n        await toast.present();\r\n      }\r\n    } catch (error) {\r\n\r\n      const toast = await this.toastController.create({\r\n        message: 'Failed to join group. Please try again.',\r\n        duration: 2000,\r\n        position: 'bottom',\r\n        color: 'danger'\r\n      });\r\n      await toast.present();\r\n    }\r\n  }\r\n\r\n  goToLeaderboard() {\r\n    window.location.href = '/leaderboard/groups';\r\n  }\r\n\r\n  refreshData() {\r\n\r\n    if (this.userId) {\r\n      this.supabaseService.getClient()\r\n        .from('profiles')\r\n        .select('username')\r\n        .eq('id', this.userId!)  \n        .single()\r\n        .then(response => {\r\n          if (response.error) {\r\n          } else {\r\n            const username = response.data.username;\r\n\r\n            this.supabaseService.getClient()\r\n              .from('group_join_requests')\r\n              .select('*')\r\n              .ilike('username_invited', username)\r\n              .then(joinResponse => {\r\n                if (joinResponse.error) {\r\n                } else {\r\n\r\n                  this.joinRequests = joinResponse.data;\r\n                }\r\n              });\r\n          }\r\n        });\r\n\r\n      this.loadGroups();\r\n    }\r\n  }\r\n}\r\n", "<!-- Exact HTML from Django template with Angular syntax -->\r\n<div class=\"container\">\r\n    <header>\r\n        <div class=\"logo\">\r\n            <img src=\"assets/images/upshift_icon_mini.svg\" alt=\"Upshift\">\r\n            <span>Upshift</span>\r\n        </div>\r\n        <h1>Groups</h1>\r\n    </header>\r\n\r\n\r\n\r\n    <!-- Always show one of these sections -->\r\n    <section class=\"join-requests\" *ngIf=\"joinRequests && joinRequests.length > 0\">\r\n        <div class=\"card\">\r\n            <a [routerLink]=\"['/group-requests']\" class=\"card-header link\">\r\n                <span class=\"icon\">👥</span>\r\n                <h2>Join Requests</h2>\r\n                <span class=\"badge\">{{ joinRequests.length }}</span>\r\n            </a>\r\n        </div>\r\n    </section>\r\n\r\n\r\n\r\n    <section class=\"group-actions\">\r\n        <a [routerLink]=\"['/create-group']\" class=\"btn primary full-width\">+ Create Group</a>\r\n\r\n        <div class=\"join-group-section\">\r\n            <h3>Join a Group</h3>\r\n            <p>Enter an invitation code to join a group:</p>\r\n            <form (ngSubmit)=\"joinGroupByCode()\" class=\"join-group-form\">\r\n                <input type=\"text\" [(ngModel)]=\"invitationCode\" name=\"invitation_code\" placeholder=\"Enter invitation code\" required>\r\n                <button type=\"submit\" class=\"btn secondary\">Join Group</button>\r\n            </form>\r\n        </div>\r\n    </section>\r\n\r\n    <section class=\"your-groups\">\r\n        <div class=\"section-header\">\r\n            <h2>Your Groups</h2>\r\n            <button (click)=\"goToLeaderboard()\" id=\"leaderboard-btn\" class=\"leaderboard-btn\">Leaderboard</button>\r\n        </div>\r\n        <div *ngIf=\"groups && groups.length > 0; else noGroups\" class=\"group-list\">\r\n            <div *ngFor=\"let group of groups\" class=\"group-card\">\r\n                <div class=\"group-icon\">{{ group.emoji }}</div>\r\n                <div class=\"group-info\">\r\n                    <h3>{{ group.name }}</h3>\r\n                    <a [routerLink]=\"['/groups', group.id]\" class=\"group-link\">Show all quests</a>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <ng-template #noGroups>\r\n            <p>No groups yet.</p>\r\n        </ng-template>\r\n    </section>\r\n</div>\r\n"], "mappings": ";;AAAA,SAA4BA,MAAM,QAAQ,eAAe;AACzD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,EAAEC,eAAe,QAAQ,gBAAgB;AAC7D,SAASC,YAAY,EAAEC,MAAM,QAAQ,iBAAiB;AACtD,SAASC,YAAY,QAAQ,iCAAiC;AAE9D,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,eAAe,QAAQ,oCAAoC;;;;;;;;;;;ICQpDC,EAHZ,CAAAC,cAAA,kBAA+E,cACzD,YACiD,eACxC;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAGzDF,EAHyD,CAAAG,YAAA,EAAO,EACpD,EACF,EACA;;;;IANCH,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAK,UAAA,eAAAL,EAAA,CAAAM,eAAA,IAAAC,GAAA,EAAkC;IAGbP,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAQ,iBAAA,CAAAC,MAAA,CAAAC,YAAA,CAAAC,MAAA,CAAyB;;;;;IA2B7CX,EADJ,CAAAC,cAAA,cAAqD,cACzB;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAE3CH,EADJ,CAAAC,cAAA,cAAwB,SAChB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,YAA2D;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAElFF,EAFkF,CAAAG,YAAA,EAAI,EAC5E,EACJ;;;;IALsBH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAQ,iBAAA,CAAAI,QAAA,CAAAC,KAAA,CAAiB;IAEjCb,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAQ,iBAAA,CAAAI,QAAA,CAAAE,IAAA,CAAgB;IACjBd,EAAA,CAAAI,SAAA,EAAoC;IAApCJ,EAAA,CAAAK,UAAA,eAAAL,EAAA,CAAAe,eAAA,IAAAC,GAAA,EAAAJ,QAAA,CAAAK,EAAA,EAAoC;;;;;IALnDjB,EAAA,CAAAC,cAAA,cAA2E;IACvED,EAAA,CAAAkB,UAAA,IAAAC,mCAAA,kBAAqD;IAOzDnB,EAAA,CAAAG,YAAA,EAAM;;;;IAPqBH,EAAA,CAAAI,SAAA,EAAS;IAATJ,EAAA,CAAAK,UAAA,YAAAI,MAAA,CAAAW,MAAA,CAAS;;;;;IAShCpB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;ADpCjC,OAAM,MAAOkB,aAAa;EAaxBC,YAAA;IAZA,KAAAC,MAAM,GAAkB,IAAI;IAE5B,KAAAH,MAAM,GAAY,EAAE;IACpB,KAAAV,YAAY,GAAuB,EAAE;IAErC,KAAAc,cAAc,GAAG,EAAE;IAEX,KAAAC,eAAe,GAAGnC,MAAM,CAACS,eAAe,CAAC;IACzC,KAAA2B,YAAY,GAAGpC,MAAM,CAACO,YAAY,CAAC;IACnC,KAAA8B,MAAM,GAAGrC,MAAM,CAACM,MAAM,CAAC;IACvB,KAAAgC,eAAe,GAAGtC,MAAM,CAACI,eAAe,CAAC;EAElC;EAEfmC,QAAQA,CAAA;IAEN,IAAI,CAACJ,eAAe,CAACK,YAAY,CAACC,IAAI,CACpCjC,IAAI,CAAC,CAAC,CAAC,CACR,CAACkC,SAAS,CAACC,QAAQ,IAAG;MAErB,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAACV,MAAM,GAAGU,QAAQ,CAAChB,EAAE;QAGzB,IAAI,CAACiB,UAAU,EAAE;QACjB,IAAI,CAACC,gBAAgB,EAAE;MACzB,CAAC,MAAM,CACP;IACF,CAAC,CAAC;EACJ;EAEMD,UAAUA,CAAA;IAAA,IAAAE,KAAA;IAAA,OAAAC,iBAAA;MACd,IAAI,CAACD,KAAI,CAACb,MAAM,EAAE;MAElB,OAAO,IAAIe,OAAO,CAAQC,OAAO,IAAI;QACnCH,KAAI,CAACV,YAAY,CAACc,aAAa,CAACJ,KAAI,CAACb,MAAO,CAAC,CAACS,SAAS,CAACZ,MAAM,IAAG;UAC/DgB,KAAI,CAAChB,MAAM,GAAGA,MAAM;UACpBmB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,CAAC;IAAC;EACL;EAEAJ,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACZ,MAAM,EAAE;MAChB;IACF;IAGA,IAAI,CAACE,eAAe,CAACgB,SAAS,EAAE,CAC7BC,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,UAAU,CAAC,CAClBC,EAAE,CAAC,IAAI,EAAE,IAAI,CAACrB,MAAM,CAAC,CACrBsB,MAAM,EAAE,CACRC,IAAI,CAACC,QAAQ,IAAG;MACf,IAAIA,QAAQ,CAACC,KAAK,EAAE;QAClB;MACF;MAEA,MAAMC,QAAQ,GAAGF,QAAQ,CAACG,IAAI,CAACD,QAAQ;MAEvC,IAAI,CAACxB,eAAe,CAACgB,SAAS,EAAE,CAC7BC,IAAI,CAAC,qBAAqB,CAAC,CAC3BC,MAAM,CAAC,GAAG,CAAC,CACXQ,KAAK,CAAC,kBAAkB,EAAEF,QAAQ,CAAC,CACnCH,IAAI,CAACM,cAAc,IAAG;QAErB,IAAIA,cAAc,CAACJ,KAAK,EAAE,CAC1B,CAAC,MAAM;UAEL,IAAI,CAACtC,YAAY,GAAG0C,cAAc,CAACF,IAAI;QAEzC;MACF,CAAC,CAAC;IACN,CAAC,CAAC;IAEJ,IAAI,CAACxB,YAAY,CAAC2B,wBAAwB,CAAC,IAAI,CAAC9B,MAAM,CAAC,CAACS,SAAS,CAAC;MAChEsB,IAAI,EAAGC,QAAQ,IAAI,CACnB,CAAC;MACDP,KAAK,EAAGA,KAAK,IAAI,CACjB;KACD,CAAC;EACJ;EAEMQ,eAAeA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAApB,iBAAA;MACnB,IAAI,CAACoB,MAAI,CAAClC,MAAM,IAAI,CAACkC,MAAI,CAACjC,cAAc,CAACkC,IAAI,EAAE,EAAE;MAEjD,IAAI;QACF,MAAMC,OAAO,SAASF,MAAI,CAAC/B,YAAY,CAAC8B,eAAe,CAACC,MAAI,CAAClC,MAAO,EAAEkC,MAAI,CAACjC,cAAc,CAACkC,IAAI,EAAE,CAAC;QAEjG,IAAIC,OAAO,EAAE;UACX,MAAMC,KAAK,SAASH,MAAI,CAAC7B,eAAe,CAACiC,MAAM,CAAC;YAC9CC,OAAO,EAAE,gCAAgC;YACzCC,QAAQ,EAAE,IAAI;YACdC,QAAQ,EAAE,QAAQ;YAClBC,KAAK,EAAE;WACR,CAAC;UACF,MAAML,KAAK,CAACM,OAAO,EAAE;UAErBT,MAAI,CAACjC,cAAc,GAAG,EAAE;UAExB,MAAMiC,MAAI,CAACvB,UAAU,EAAE;UAEvBiC,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;QAC1B,CAAC,MAAM;UACL,MAAMT,KAAK,SAASH,MAAI,CAAC7B,eAAe,CAACiC,MAAM,CAAC;YAC9CC,OAAO,EAAE,qCAAqC;YAC9CC,QAAQ,EAAE,IAAI;YACdC,QAAQ,EAAE,QAAQ;YAClBC,KAAK,EAAE;WACR,CAAC;UACF,MAAML,KAAK,CAACM,OAAO,EAAE;QACvB;MACF,CAAC,CAAC,OAAOlB,KAAK,EAAE;QAEd,MAAMY,KAAK,SAASH,MAAI,CAAC7B,eAAe,CAACiC,MAAM,CAAC;UAC9CC,OAAO,EAAE,yCAAyC;UAClDC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE;SACR,CAAC;QACF,MAAML,KAAK,CAACM,OAAO,EAAE;MACvB;IAAC;EACH;EAEAI,eAAeA,CAAA;IACbH,MAAM,CAACC,QAAQ,CAACG,IAAI,GAAG,qBAAqB;EAC9C;EAEAC,WAAWA,CAAA;IAET,IAAI,IAAI,CAACjD,MAAM,EAAE;MACf,IAAI,CAACE,eAAe,CAACgB,SAAS,EAAE,CAC7BC,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,UAAU,CAAC,CAClBC,EAAE,CAAC,IAAI,EAAE,IAAI,CAACrB,MAAO,CAAC,CACtBsB,MAAM,EAAE,CACRC,IAAI,CAACC,QAAQ,IAAG;QACf,IAAIA,QAAQ,CAACC,KAAK,EAAE,CACpB,CAAC,MAAM;UACL,MAAMC,QAAQ,GAAGF,QAAQ,CAACG,IAAI,CAACD,QAAQ;UAEvC,IAAI,CAACxB,eAAe,CAACgB,SAAS,EAAE,CAC7BC,IAAI,CAAC,qBAAqB,CAAC,CAC3BC,MAAM,CAAC,GAAG,CAAC,CACXQ,KAAK,CAAC,kBAAkB,EAAEF,QAAQ,CAAC,CACnCH,IAAI,CAAC2B,YAAY,IAAG;YACnB,IAAIA,YAAY,CAACzB,KAAK,EAAE,CACxB,CAAC,MAAM;cAEL,IAAI,CAACtC,YAAY,GAAG+D,YAAY,CAACvB,IAAI;YACvC;UACF,CAAC,CAAC;QACN;MACF,CAAC,CAAC;MAEJ,IAAI,CAAChB,UAAU,EAAE;IACnB;EACF;;iBA9JWb,aAAa;;mCAAbA,cAAa;AAAA;;QAAbA,cAAa;EAAAqD,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;;MCdlBhF,EAFR,CAAAC,cAAA,aAAuB,aACX,aACc;MACdD,EAAA,CAAAkF,SAAA,aAA6D;MAC7DlF,EAAA,CAAAC,cAAA,WAAM;MAAAD,EAAA,CAAAE,MAAA,cAAO;MACjBF,EADiB,CAAAG,YAAA,EAAO,EAClB;MACNH,EAAA,CAAAC,cAAA,SAAI;MAAAD,EAAA,CAAAE,MAAA,aAAM;MACdF,EADc,CAAAG,YAAA,EAAK,EACV;MAKTH,EAAA,CAAAkB,UAAA,IAAAiE,gCAAA,qBAA+E;MAa3EnF,EADJ,CAAAC,cAAA,iBAA+B,YACwC;MAAAD,EAAA,CAAAE,MAAA,sBAAc;MAAAF,EAAA,CAAAG,YAAA,EAAI;MAGjFH,EADJ,CAAAC,cAAA,cAAgC,UACxB;MAAAD,EAAA,CAAAE,MAAA,oBAAY;MAAAF,EAAA,CAAAG,YAAA,EAAK;MACrBH,EAAA,CAAAC,cAAA,SAAG;MAAAD,EAAA,CAAAE,MAAA,iDAAyC;MAAAF,EAAA,CAAAG,YAAA,EAAI;MAChDH,EAAA,CAAAC,cAAA,eAA6D;MAAvDD,EAAA,CAAAoF,UAAA,sBAAAC,iDAAA;QAAArF,EAAA,CAAAsF,aAAA,CAAAC,GAAA;QAAA,OAAAvF,EAAA,CAAAwF,WAAA,CAAYP,GAAA,CAAAzB,eAAA,EAAiB;MAAA,EAAC;MAChCxD,EAAA,CAAAC,cAAA,gBAAoH;MAAjGD,EAAA,CAAAyF,gBAAA,2BAAAC,uDAAAC,MAAA;QAAA3F,EAAA,CAAAsF,aAAA,CAAAC,GAAA;QAAAvF,EAAA,CAAA4F,kBAAA,CAAAX,GAAA,CAAAzD,cAAA,EAAAmE,MAAA,MAAAV,GAAA,CAAAzD,cAAA,GAAAmE,MAAA;QAAA,OAAA3F,EAAA,CAAAwF,WAAA,CAAAG,MAAA;MAAA,EAA4B;MAA/C3F,EAAA,CAAAG,YAAA,EAAoH;MACpHH,EAAA,CAAAC,cAAA,kBAA4C;MAAAD,EAAA,CAAAE,MAAA,kBAAU;MAGlEF,EAHkE,CAAAG,YAAA,EAAS,EAC5D,EACL,EACA;MAIFH,EAFR,CAAAC,cAAA,mBAA6B,eACG,UACpB;MAAAD,EAAA,CAAAE,MAAA,mBAAW;MAAAF,EAAA,CAAAG,YAAA,EAAK;MACpBH,EAAA,CAAAC,cAAA,kBAAiF;MAAzED,EAAA,CAAAoF,UAAA,mBAAAS,gDAAA;QAAA7F,EAAA,CAAAsF,aAAA,CAAAC,GAAA;QAAA,OAAAvF,EAAA,CAAAwF,WAAA,CAASP,GAAA,CAAAX,eAAA,EAAiB;MAAA,EAAC;MAA8CtE,EAAA,CAAAE,MAAA,mBAAW;MAChGF,EADgG,CAAAG,YAAA,EAAS,EACnG;MAUNH,EATA,CAAAkB,UAAA,KAAA4E,6BAAA,kBAA2E,KAAAC,qCAAA,gCAAA/F,EAAA,CAAAgG,sBAAA,CASpD;MAI/BhG,EADI,CAAAG,YAAA,EAAU,EACR;;;;MA3C8BH,EAAA,CAAAI,SAAA,GAA6C;MAA7CJ,EAAA,CAAAK,UAAA,SAAA4E,GAAA,CAAAvE,YAAA,IAAAuE,GAAA,CAAAvE,YAAA,CAAAC,MAAA,KAA6C;MAatEX,EAAA,CAAAI,SAAA,GAAgC;MAAhCJ,EAAA,CAAAK,UAAA,eAAAL,EAAA,CAAAM,eAAA,IAAA2F,GAAA,EAAgC;MAMRjG,EAAA,CAAAI,SAAA,GAA4B;MAA5BJ,EAAA,CAAAkG,gBAAA,YAAAjB,GAAA,CAAAzD,cAAA,CAA4B;MAWjDxB,EAAA,CAAAI,SAAA,GAAmC;MAAAJ,EAAnC,CAAAK,UAAA,SAAA4E,GAAA,CAAA7D,MAAA,IAAA6D,GAAA,CAAA7D,MAAA,CAAAT,MAAA,KAAmC,aAAAwF,WAAA,CAAa;;;iBD5BlD1G,WAAW,EAAA2G,EAAA,CAAAC,0BAAA,EAAE9G,YAAY,EAAA+G,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEhH,WAAW,EAAAiH,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,oBAAA,EAAAJ,EAAA,CAAAK,iBAAA,EAAAL,EAAA,CAAAM,OAAA,EAAAN,EAAA,CAAAO,MAAA,EAAErH,YAAY,EAAAsH,EAAA,CAAAC,UAAA;EAAAC,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}