﻿import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, from, throwError } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { SupabaseService } from './supabase.service';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class EdgeFunctionsService {
  private baseUrl = `${environment.supabase.url}/functions/v1`;

  constructor(
    private http: HttpClient,
    private supabaseService: SupabaseService
  ) {}

  createCheckoutSession(priceId: string, planType: string, uid: string): Observable<{ sessionId: string }> {
    const url = `${this.baseUrl}/createCheckoutSession`;
    const body = { priceId, planType, uid };

    return from(this.supabaseService.getClient().auth.getSession()).pipe(
      switchMap(({ data }) => {
        const token = data.session?.access_token;
        if (!token) {
          return throwError(() => new Error('No authentication token available'));
        }

        const headers = new HttpHeaders({
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        });

        return this.http.post<{ sessionId: string }>(url, body, { headers });
      }),
      catchError(error => {
        return throwError(() => new Error(`Error creating checkout session: ${error.message}`));
      })
    );
  }

  createUser(email: string, password: string, userData: any): Observable<{ uid: string }> {
    const url = `${this.baseUrl}/createUser`;
    const body = { email, password, userData };

    return from(this.supabaseService.getClient().auth.getSession()).pipe(
      switchMap(({ data }) => {
        const token = data.session?.access_token;
        if (!token) {
          return throwError(() => new Error('No authentication token available'));
        }

        const headers = new HttpHeaders({
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        });

        return this.http.post<{ uid: string }>(url, body, { headers });
      }),
      catchError(error => {
        return throwError(() => new Error(`Error creating user: ${error.message}`));
      })
    );
  }
}
