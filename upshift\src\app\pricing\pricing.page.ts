﻿import { Component, inject, runInInjectionContext, EnvironmentInjector } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { RouterModule, Router } from '@angular/router';
import { PreferencesService } from '../services/preferences.service';
import { SupabaseService } from '../services/supabase.service';

type PlanKey = 'monthly' | 'yearly' | 'discountYearly';

@Component({
  selector: 'app-plan',
  standalone: true,
  imports: [IonicModule, CommonModule, RouterModule],
  templateUrl: './pricing.page.html',
  styleUrls: ['./pricing.page.scss'],
})
export class PricingPage {
  private router = inject(Router);
  private injector = inject(EnvironmentInjector);
  private preferencesService = inject(PreferencesService);
  private supabaseService = inject(SupabaseService);
  private authSubscription: any;
  private isRedirecting = false;
  selectedPlan: PlanKey | null = null;

  hasAffiliateDiscount = false;
  affiliateCode = '';
  discountPercentage = 5; 

  PRICING: Record<PlanKey, {
    label: string;
    displayPrice: string;
    originalPrice: number;
    priceId: string;
    planType: string;
    badge?: boolean;
    badgeText?: string;
  }> = {
    monthly: {
      label: 'Monthly',
      displayPrice: '€9.99/mo',
      originalPrice: 9.99,
      priceId: 'price_1RDOm4EB7UFpNfHGQIndEtcK',
      planType: 'monthly',
      badge: false,
      badgeText: 'Best Value',
    },
    yearly: {
      label: 'Yearly',
      displayPrice: '€2.99/mo',
      originalPrice: 2.99,
      priceId: 'price_1RDOmhEB7UFpNfHGa4QL9P8g',
      planType: 'yearly',
      badge: true,
      badgeText: 'Best Value',
    },
    discountYearly: {
      label: 'Yearly Discount',
      displayPrice: '€1.99/mo',
      originalPrice: 1.99,
      priceId: 'price_1RDPIVEB7UFpNfHGNplAzv3l',
      planType: 'yearly',
      badge: true,
      badgeText: 'Best Value',
    }
  };

  public pageContent = [
    {
      icon: 'lock-open',
      title: 'Today',
      text:'Unlock all app features, like group challenges, side quests and more.'
    },
    {
      icon: 'barbell',
      title: 'In 1 month',
      text:'You will notice a difference in your productivity and focus.'
    },
    {
      icon: 'diamond',
      title: 'In 365 days',
      text:'You will be a different person, with consistent routine, better focus and more energy.'
    }
  ];

  constructor() {
  }

  async ionViewWillEnter() {
    try {
      const { value: affiliateCode } = await this.preferencesService.get('affiliate_code_used');

      if (affiliateCode) {
        this.hasAffiliateDiscount = true;
        this.affiliateCode = affiliateCode;

        this.applyAffiliateDiscount();
      } else {
      }

      const { value: onboarding } = await this.preferencesService.get('onboarding_complete');

      runInInjectionContext(this.injector, () => {
        this.authSubscription = this.supabaseService.currentUser$.subscribe(async (user) => {
          if (!user) return;
          if (!onboarding) {
            this.router.navigateByUrl('/onboarding');
            return;
          }

          try {
            runInInjectionContext(this.injector, async () => {
              if (!user.id) {
                return;
              }


              const { data: userData, error } = await this.supabaseService.getClient()
                .from('profiles')
                .select('*')
                .eq('id', user.id)
                .single();

              if (error) {
                return;
              }

              if (!userData) {
                return;
              }


              let endDate = userData.end_of_current_plan ? new Date(userData.end_of_current_plan) : null;
              const username = userData.username;
              const name = userData.name;

              );

              let isValidPlan = false;
              if (endDate instanceof Date) {
                isValidPlan = endDate > new Date();
              } else {
              }

              if (isValidPlan) {
                if (this.isRedirecting) {
                  return;
                }

                this.isRedirecting = true;

                if (username) {
                  setTimeout(() => {
                    this.router.navigateByUrl('/tabs/home');
                    setTimeout(() => {
                      this.isRedirecting = false;
                    }, 2000);
                  }, 500);
                } else {
                  setTimeout(() => {
                    this.router.navigateByUrl('/signup-step3');
                    setTimeout(() => {
                      this.isRedirecting = false;
                    }, 2000);
                  }, 500);
                }
              } else {
              }
            });
          } catch (error) {
          }
        });
      });
    } catch (error) {
    }
  }

  ionViewWillLeave() {
    if (this.authSubscription) {
      this.authSubscription.unsubscribe();
    }
  }

  private applyAffiliateDiscount() {
    for (const key of Object.keys(this.PRICING) as PlanKey[]) {
      const plan = this.PRICING[key];
      const originalPrice = plan.originalPrice;
      const discountedPrice = originalPrice * (1 - this.discountPercentage / 100);

      const formattedPrice = discountedPrice.toFixed(2);

      plan.displayPrice = `€${formattedPrice}/mo`;

      if (plan.badge) {
        plan.badgeText = `${this.discountPercentage}% Off`;
      } else {
        plan.badge = true;
        plan.badgeText = `${this.discountPercentage}% Off`;
      }

    }
  }

  getPricingValue(key: string) {
    return this.PRICING[key as PlanKey];
  }

  setSelectedPlan(key: string) {
    this.selectedPlan = key as PlanKey;
  }

  processPayment() {
    this.router.navigate(['/rating']);
  }
}
