﻿ Exact CSS from Django template */
}


header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo img {
  height: 24px;
}

.logo span {
  font-size: 20px;
  font-weight: 600;
}

h1 {
  font-size: 20px;
  font-weight: 600;
}

.settings-container {
  padding: 20px;
  margin-bottom: 80px;
}

.settings-header {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input[type="text"],
.form-group textarea {
  width: 100%;
  padding: 10px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background-color: var(--card-bg);
  color: var(--text-color);
  font-size: 16px;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.checkbox-group {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  margin-top: 5px;
}

.checkbox-group input[type="checkbox"] {
  margin-right: 10px;
}

.profile-picture-preview {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background-color: var(--card-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  margin-bottom: 10px;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.profile-picture-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.file-input-container {
  margin-top: 20px;
}

.file-input-label {
  display: inline-block;
  cursor: pointer;
}

.file-button {
  background-color: var(--accent-color);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  display: inline-block;
}

.file-status {
  color: var(--secondary-text);
  font-size: 14px;
  margin-top: 8px;
}

input[type="file"] {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

.help-text {
  font-size: 12px;
  color: var(--secondary-text);
  margin-top: 10px;
}

.submit-button {
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 16px;
  cursor: pointer;
  margin-top: 20px;
  width: 100%;
}

.submit-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.back-button {
  display: inline-block;
  background-color: var(--card-bg);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 10px 15px;
  font-size: 14px;
  text-decoration: none;
  margin-top: 20px;
}

.logout-button {
  display: inline-block;
  background-color: var(--danger-color);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 15px;
  font-size: 14px;
  text-decoration: none;
  margin-top: 20px;
  margin-left: 10px;
  cursor: pointer;
}

.error-message {
  color: var(--danger-color);
  margin-top: 5px;
  font-size: 14px;
}

.success-message {
  background-color: #4CAF50;
  color: white;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 25px;
  text-align: center;
  font-weight: bold;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  animation: fadeIn 0.5s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.success-message ion-icon {
  margin-right: 8px;
  font-size: 20px;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

#celebration-settings {
  background-color: rgba(var(--accent-color-rgb), 0.1);
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
  margin-bottom: 20px;
  border: 1px solid rgba(var(--accent-color-rgb), 0.3);
}

#celebration-settings .form-group:last-child {
  margin-bottom: 0;
}

 Navigation Styles */
.container {
  padding-bottom: 120px !important;
}


.settings-container {
  padding: 20px;
  margin-bottom: 80px;
}

.settings-header {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #CCC;
    font-size: 14px;
}

.form-group input[type="text"],
.form-group textarea {
  width: 100%;
  padding: 10px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background-color: var(--card-bg);
  color: var(--text-color);
  font-size: 16px;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.checkbox-group {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  margin-top: 5px;
}

.checkbox-group input[type="checkbox"] {
  margin-right: 10px;
}

.profile-picture-preview {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background-color: var(--card-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  margin-bottom: 10px;
  overflow: hidden;
}

.profile-picture-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.file-input-container {
  margin-top: 20px;
}

.file-input-label {
  display: inline-block;
  cursor: pointer;
}

.file-button {
  background-color: var(--accent-color);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  display: inline-block;
}

.file-status {
  color: var(--secondary-text);
  font-size: 14px;
  margin-top: 8px;
}

input[type="file"] {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

.help-text {
  font-size: 12px;
  color: var(--secondary-text);
  margin-top: 10px;
}

.submit-button {
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 16px;
  cursor: pointer;
  margin-top: 20px;
}

.back-button {
  display: inline-block;
  background-color: var(--card-bg);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 10px 15px;
  font-size: 14px;
  text-decoration: none;
  margin-top: 20px;
}

.logout-button {
  display: inline-block;
  background-color: var(--danger-color);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 15px;
  font-size: 14px;
  text-decoration: none;
  margin-top: 20px;
  margin-left: 10px;
}

.error-message {
  color: var(--danger-color);
  margin-top: 5px;
  font-size: 14px;
}

.success-message {
  background-color: #4CAF50;
  color: white;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 25px;
  text-align: center;
  font-weight: bold;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  animation: fadeIn 0.5s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  position: sticky;
  top: 0;
  z-index: 100;
}

#celebration-settings {
  background-color: rgba(var(--accent-color-rgb), 0.1);
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
  margin-bottom: 20px;
  border: 1px solid rgba(var(--accent-color-rgb), 0.3);
}

#celebration-settings .form-group:last-child {
  margin-bottom: 0;
}