﻿import { Injectable, inject } from '@angular/core';
import { Observable, map, of, catchError, from, switchMap, finalize } from 'rxjs';
import { SupabaseService } from './supabase.service';

@Injectable({
  providedIn: 'root'
})
export class AdminService {
  private supabaseService = inject(SupabaseService);

  isAdmin(): Observable<boolean> {

    return this.supabaseService.currentUser$.pipe(
      switchMap(authUser => {
        if (!authUser) {
          return of(false);
        }


        return from(this.supabaseService.supabase
          .from('profiles')
          .select('username')
          .eq('id', authUser.id)
          .single()
        ).pipe(
          map(response => {
            if (response.error) {
              return false;
            }

            const isAdmin = response.data?.username === 'admin';

            if (isAdmin) {
            } else {
            }

            return isAdmin;
          }),
          catchError(error => {
            return of(false);
          })
        );
      })
    );
  }

  getCollection(collectionName: string): Observable<any[]> {

    try {
      return from(this.supabaseService.supabase
        .from(collectionName)
        .select('*')
      ).pipe(
        map(response => {
          if (response.error) {
            return [];
          }

          return response.data;
        }),
        catchError(error => {
          return of([]);
        })
      );
    } catch (error) {
      return of([]);
    }
  }

  getDocument(collectionName: string, docId: string): Observable<any> {

    try {
      return from(this.supabaseService.supabase
        .from(collectionName)
        .select('*')
        .eq('id', docId)
        .single()
      ).pipe(
        map(response => {
          if (response.error) {
            return null;
          }

          return response.data;
        }),
        catchError(error => {
          return of(null);
        })
      );
    } catch (error) {
      return of(null);
    }
  }

  createAuthUser(email: string, password: string, userData?: any): Observable<string> {

    alert(`WARNING: This will create a real Supabase Authentication user with email: ${email}`);

    let adminSession: any = null;

    return from(this.supabaseService.supabase.auth.getSession()).pipe(
      switchMap(sessionResponse => {
        adminSession = sessionResponse.data.session;

        return from(this.supabaseService.supabase.auth.signUp({
          email,
          password,
          options: {
            emailRedirectTo: window.location.origin
          }
        }));
      }),
      switchMap(response => {
        if (response.error) {
          throw response.error;
        }

        const uid = response.data.user?.id;
        if (!uid) {
          throw new Error('Failed to get user ID after signup');
        }


        const profileData = {
          id: uid,
          email,
          username: userData?.username || email.split('@')[0],
          name: userData?.name || '',
          registration_date: new Date(),
          last_login: new Date(),
          active: true,
          strength_xp: 0,
          money_xp: 0,
          health_xp: 0,
          knowledge_xp: 0,
          level: 0, 
          title: 'Beginner',
          bio: userData?.bio || '',
          affiliate_code_used: userData?.affiliate_code_used || '',
          timezone: userData?.timezone || 'UTC',
          auto_renew: userData?.auto_renew || false,
          sidequests_switch: userData?.sidequests_switch !== undefined ? userData.sidequests_switch : true,
          show_celebration: userData?.show_celebration !== undefined ? userData.show_celebration : true,
          plan: userData?.plan || 'monthly', 
          start_of_current_plan: userData?.start_of_current_plan || new Date(), 
          end_of_current_plan: userData?.end_of_current_plan || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) 
        };

        if (userData) {
          Object.keys(userData).forEach(key => {
            if (key !== '_password' && key !== 'created_at') {
              profileData[key] = userData[key];
            }
          });
        }

        if (profileData['_password']) {
          delete profileData['_password'];
        }

        return from(this.supabaseService.supabase
          .from('profiles')
          .insert(profileData)
        ).pipe(
          map(insertResponse => {
            if (insertResponse.error) {
              throw insertResponse.error;
            }

            return uid;
          }),
          finalize(() => {
            if (adminSession) {
              this.supabaseService.supabase.auth.setSession({
                access_token: adminSession.access_token,
                refresh_token: adminSession.refresh_token
              }).catch(error => {
              });
            }
          })
        );
      }),
      catchError(error => {

        if (error.message?.includes('already exists')) {
          alert(`Error: A user with email ${email} already exists. Please use a different email.`);
        } else {
          alert(`Error creating user: ${error.message}`);
        }

        if (adminSession) {
          this.supabaseService.supabase.auth.setSession({
            access_token: adminSession.access_token,
            refresh_token: adminSession.refresh_token
          }).catch(e => );
        }

        throw error;
      })
    );
  }

  addDocument(collectionName: string, data: any, documentId?: string): Observable<string> {

    try {
      const docData = {
        ...data
      };

      if (collectionName !== 'profiles') {
        docData.created_at = new Date();
      }

      if (collectionName === 'profiles' && data.email && data._password) {

        const password = data._password;
        :', password);

        delete docData._password;

        if (!docData.plan || (docData.plan !== 'monthly' && docData.plan !== 'yearly')) {
          docData.plan = 'monthly';
        }

        return this.createAuthUser(data.email, password, docData).pipe(
          map(uid => {
            if (!uid) {
              throw new Error('Failed to create Supabase Authentication user');
            }


            alert(`Success! Created user in both Supabase Authentication and Database.\n\nEmail: ${data.email}\nID: ${uid}`);

            return uid;
          }),
          catchError(error => {
            throw error;
          })
        );
      } else {

        if (collectionName === 'profiles') {
          if (!docData.plan || (docData.plan !== 'monthly' && docData.plan !== 'yearly')) {
            docData.plan = 'monthly';
          }
        }

        if (documentId) {
          docData.id = documentId;

          return from(this.supabaseService.supabase
            .from(collectionName)
            .upsert(docData)
          ).pipe(
            map(response => {
              if (response.error) {
                throw response.error;
              }

              return documentId;
            }),
            catchError(error => {
              throw error;
            })
          );
        } else {
          return from(this.supabaseService.supabase
            .from(collectionName)
            .insert(docData)
            .select()
          ).pipe(
            map(response => {
              if (response.error) {
                throw response.error;
              }

              const newId = response.data[0]?.id;
              return newId;
            }),
            catchError(error => {
              throw error;
            })
          );
        }
      }
    } catch (error) {
      return of('');
    }
  }

  updateDocument(collectionName: string, docId: string, data: any): Observable<void> {

    try {
      const updateData = {
        ...data,
        id: docId 
      };

      if (collectionName !== 'profiles') {
        updateData.updated_at = new Date();
      }

      return from(this.supabaseService.supabase
        .from(collectionName)
        .update(updateData)
        .eq('id', docId)
      ).pipe(
        map(response => {
          if (response.error) {
            throw response.error;
          }

        }),
        catchError(error => {
          throw error;
        })
      );
    } catch (error) {
      return of(undefined);
    }
  }

  deleteDocument(collectionName: string, docId: string): Observable<void> {

    try {
      return from(this.supabaseService.supabase
        .from(collectionName)
        .delete()
        .eq('id', docId)
      ).pipe(
        map(response => {
          if (response.error) {
            throw response.error;
          }

        }),
        catchError(error => {
          throw error;
        })
      );
    } catch (error) {
      return of(undefined);
    }
  }

  getCollections(): Observable<string[]> {
    const defaultCollections = [
      'profiles',
      'quests',
      'quest_progress',
      'daily_sidequest_pool',
      'user_daily_sidequests',
      'activities',
      'activity_types',
      'day_tracking',
      'friends',
      'goal_journal_entries',
      'goals',
      'group_join_requests',
      'group_members',
      'group_quest_progress',
      'group_quests',
      'groups',
      'microgoals',
      'subscription_history',
      'user_badges',
      'group_sidequest_pool',
      'group_sidequests',
      'group_sidequest_member_status'
    ];
    return of(defaultCollections);
  }

  addCollection(collectionName: string): Observable<void> {
    ');
    return of(undefined);
  }

  getModelTemplate(collectionName: string): any {

    switch (collectionName.toLowerCase()) {
      case 'profiles':
        return {
          email: '',
          _password: '', 
          username: '',
          name: '',
          registration_date: new Date(),
          last_login: new Date(),
          active: true,
          strength_xp: 0,
          money_xp: 0,
          health_xp: 0,
          knowledge_xp: 0,
          level: 0, 
          title: 'Beginner',
          bio: '',
          affiliate_code_used: '',
          timezone: 'UTC',
          auto_renew: false,
          sidequests_switch: true,
          show_celebration: true,
          celebration_name: 'Another Day, Another W',
          celebration_description: "You've completed all your quests for today. Keep up the great work!",
          celebration_emoji: '🦅',
          subscription_status: 'active',
          start_of_current_plan: new Date(), 
          end_of_current_plan: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), 
          plan: 'monthly' 
        };

      case 'quests':
        return {
          id: '',
          name: '',
          description: '',
          active: true,
          quest_type: 'build',
          user_id: '',
          streak: 0,
          goal_value: 1,
          goal_unit: 'count',
          goal_period: 'day',
          priority: 'basic',
          category: 'strength',
          created_at: new Date(),
          emoji: '🎯',
          task_days_of_week: '',
          task_days_of_month: '',
          custom_reminder_times: '',
          custom_reminder_test: ''
        };

      case 'quest_progress':
        return {
          id: '',
          user_id: '',
          quest_id: '',
          date: new Date(),
          completed: false,
          value_achieved: 0
        };

      case 'daily_sidequest_pool':
        return {
          id: '',
          name: '',
          description: '',
          emoji: '🎯',
          category: 'strength',
          active: true,
          goal_value: 1,
          goal_unit: 'count'
        };

      case 'user_daily_sidequests':
        return {
          id: '',
          user_id: '',
          date_assigned: new Date(),
          current_quest_id: '',
          completed: false,
          value_achieved: 0,
          streak: 0,
          last_completed_date: null,
          category: '',
          emoji: '🎯'
        };

      case 'activities':
        return {
          id: '',
          day_tracking_id: '',
          name: '',
          emoji: '⚡',
          hours: 0,
          minutes: 0,
          is_custom: false
        };

      case 'activity_types':
        return {
          id: '',
          name: '',
          emoji: '',
          is_active: true,
          order: 0,
          color: '#4169E1'
        };

      case 'day_tracking':
        return {
          id: '',
          user_id: '',
          date: new Date(),
          total_hours: 0,
          total_minutes: 0
        };

      case 'friends':
        return {
          id: '',
          user_id: '',
          friend_id: '',
          created: new Date()
        };

      case 'goal_journal_entries':
        return {
          id: '',
          goal_id: '',
          created_at: new Date(),
          milestone_percentage: 0,
          content: ''
        };

      case 'goals':
        return {
          id: '',
          user_id: '',
          name: '',
          description: '',
          emoji: '🎯',
          start_date: new Date(),
          end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), 
          goal_value: 0,
          current_value: 0,
          goal_unit: '',
          before_photo: '',
          after_photo: ''
        };

      case 'group_join_requests':
        return {
          id: '',
          group_id: '',
          username_invited: '',
          invited_by: '',
          created: new Date()
        };

      case 'group_members':
        return {
          id: '',
          group_id: '',
          user_id: '',
          nickname: '',
          is_admin: false,
          joined_date: new Date()
        };

      case 'group_quest_progress':
        return {
          id: '',
          quest_id: '',
          user_id: '',
          date: new Date(),
          value_achieved: 0,
          completed: false
        };

      case 'group_quests':
        return {
          id: '',
          group_id: '',
          created: new Date(),
          name: '',
          description: '',
          emoji: '🎯',
          category: '',
          priority: '',
          quest_type: '',
          goal_value: 1,
          goal_unit: '',
          goal_period: '',
          task_days_of_week: '',
          task_days_of_month: '',
          streak: 0
        };

      case 'groups':
        return {
          id: '',
          created: new Date(),
          name: '',
          emoji: '👥',
          admin_id: '',
          enable_sidequests: true,
          timezone: 'UTC',
          level: 0,
          strength_xp: 0,
          money_xp: 0,
          health_xp: 0,
          knowledge_xp: 0,
          invitation_code: '',
          code_expiry: null
        };

      case 'microgoals':
        return {
          id: '',
          goal_id: '',
          title: '',
          completed: false,
          completed_at: null
        };

      case 'group_sidequest_pool':
        return {
          id: '',
          name: '',
          description: '',
          goal_value: 1,
          category: 'health',
          goal_unit: 'count',
          active: true,
          emoji: '🎯',
          created_at: new Date()
        };

      case 'group_sidequests':
        return {
          id: '',
          group_id: '',
          current_quest_id: '',
          streak: 0,
          last_completed_date: null,
          date_assigned: new Date().toISOString().split('T')[0],
          completed: false,
          value_achieved: 0,
          category: 'health',
          created_at: new Date()
        };

      case 'group_sidequest_member_status':
        return {
          id: '',
          group_quest_id: '',
          member_id: '',
          completed: false,
          value_achieved: 0,
          last_updated: new Date().toISOString().split('T')[0],
          created_at: new Date()
        };

      case 'subscription_history':
        return {
          id: '',
          user_id: '',
          created_at: new Date(),
          start_date_of_subscription: new Date(),
          end_date_of_subscription: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), 
          plan: 'monthly' 
        };

      case 'user_badges':
        return {
          id: '',
          user_id: '',
          created_at: new Date(),
          updated_at: new Date(),
          badge_newbie: false,
          badge_warrior: false,
          badge_hardcore: false,
          badge_peak_performer: false,
          badge_indestructible: false,
          badge_professional: false,
          badge_streak_7_days: false,
          badge_streak_30_days: false,
          badge_streak_100_days: false,
          badge_streak_365_days: false,
          badge_sidequest_streak_7_days: false,
          badge_sidequest_streak_30_days: false,
          badge_sidequest_streak_100_days: false,
          badge_sidequest_streak_365_days: false,
          badge_friends_5: false,
          badge_friends_10: false,
          badge_strength_master: false,
          badge_money_master: false,
          badge_health_master: false,
          badge_knowledge_master: false
        };

      default:
        return {};
    }
  }

  isForeignKey(fieldName: string): boolean {
    return fieldName === 'user_id' ||
           fieldName === 'quest_id' ||
           fieldName === 'group_id' ||
           fieldName === 'goal_id' ||
           fieldName === 'friend_id' ||
           fieldName === 'day_tracking_id' ||
           fieldName === 'admin_id' ||
           fieldName === 'invited_by' ||
           fieldName === 'current_quest_id';
  }

  getForeignKeyTable(fieldName: string): string {
    if (fieldName === 'user_id') return 'profiles';
    if (fieldName === 'quest_id') return 'quests';
    if (fieldName === 'group_id') return 'groups';
    if (fieldName === 'goal_id') return 'goals';
    if (fieldName === 'friend_id') return 'profiles';
    if (fieldName === 'day_tracking_id') return 'day_tracking';
    if (fieldName === 'admin_id') return 'profiles';
    if (fieldName === 'invited_by') return 'profiles';
    if (fieldName === 'current_quest_id') return 'daily_sidequest_pool';

    return '';
  }

  getForeignKeyDisplayField(tableName: string): string {
    if (tableName === 'profiles') return 'username';
    if (tableName === 'quests') return 'name';
    if (tableName === 'groups') return 'name';
    if (tableName === 'goals') return 'name';
    if (tableName === 'day_tracking') return 'id';
    if (tableName === 'daily_sidequest_pool') return 'name';

    return 'id';
  }

  getForeignKeyOptions(fieldName: string): Observable<{id: string, display: string}[]> {
    const referencedTable = this.getForeignKeyTable(fieldName);
    const displayField = this.getForeignKeyDisplayField(referencedTable);

    if (!referencedTable) {
      return of([]);
    }


    return from(this.supabaseService.supabase
      .from(referencedTable)
      .select(`id, ${displayField}`)
    ).pipe(
      map(response => {
        if (response.error) {
          return [];
        }

        return response.data.map((item: any) => ({
          id: item.id,
          display: `${item[displayField]} (${item.id.substring(0, 8)}...)`
        }));
      }),
      catchError(error => {
        return of([]);
      })
    );
  }

  getFieldTypes(collectionName: string): Record<string, string> {

    let fieldTypes: Record<string, string> = {};

    switch (collectionName.toLowerCase()) {
      case 'profiles':
        fieldTypes = {
          email: 'email',
          _password: 'password', 
          username: 'text',
          name: 'text',
          registration_date: 'date',
          last_login: 'date',
          active: 'boolean',
          strength_xp: 'number',
          money_xp: 'number',
          health_xp: 'number',
          knowledge_xp: 'number',
          level: 'number',
          title: 'text',
          bio: 'textarea',
          affiliate_code_used: 'text',
          timezone: 'text',
          auto_renew: 'boolean',
          sidequests_switch: 'boolean',
          show_celebration: 'boolean',
          celebration_name: 'text',
          celebration_description: 'textarea',
          celebration_emoji: 'text',
          subscription_status: 'select',
          start_of_current_plan: 'date',
          end_of_current_plan: 'date',
          plan: 'select'
        };
        break;

      case 'quests':
        fieldTypes = {
          id: 'text',
          name: 'text',
          description: 'textarea',
          active: 'boolean',
          quest_type: 'select',
          user_id: 'text',
          streak: 'number',
          goal_value: 'number',
          goal_unit: 'select',
          goal_period: 'select',
          priority: 'select',
          category: 'select',
          created_at: 'date',
          emoji: 'text',
          task_days_of_week: 'text',
          task_days_of_month: 'text',
          custom_reminder_times: 'text',
          custom_reminder_test: 'text'
        };
        break;

      case 'quest_progress':
        fieldTypes = {
          id: 'text',
          user_id: 'text',
          quest_id: 'text',
          date: 'date',
          completed: 'boolean',
          value_achieved: 'number'
        };
        break;

      case 'daily_sidequest_pool':
        fieldTypes = {
          id: 'text',
          name: 'text',
          description: 'textarea',
          emoji: 'text',
          category: 'select',
          active: 'boolean',
          goal_value: 'number',
          goal_unit: 'select'
        };
        break;

      case 'user_daily_sidequests':
        fieldTypes = {
          id: 'text',
          user_id: 'text',
          date_assigned: 'date',
          current_quest_id: 'text',
          completed: 'boolean',
          value_achieved: 'number',
          streak: 'number',
          last_completed_date: 'date',
          category: 'select',
          emoji: 'text'
        };
        break;

      case 'activities':
        fieldTypes = {
          id: 'text',
          day_tracking_id: 'text',
          name: 'text',
          emoji: 'text',
          hours: 'number',
          minutes: 'number',
          is_custom: 'boolean'
        };
        break;

      case 'activity_types':
        fieldTypes = {
          id: 'text',
          name: 'text',
          emoji: 'text',
          is_active: 'boolean',
          order: 'number',
          color: 'text'
        };
        break;

      case 'day_tracking':
        fieldTypes = {
          id: 'text',
          user_id: 'text',
          date: 'date',
          total_hours: 'number',
          total_minutes: 'number'
        };
        break;

      case 'friends':
        fieldTypes = {
          id: 'text',
          user_id: 'text',
          friend_id: 'text',
          created: 'date'
        };
        break;

      case 'goal_journal_entries':
        fieldTypes = {
          id: 'text',
          goal_id: 'text',
          created_at: 'date',
          milestone_percentage: 'number',
          content: 'textarea'
        };
        break;

      case 'goals':
        fieldTypes = {
          id: 'text',
          user_id: 'text',
          name: 'text',
          description: 'textarea',
          emoji: 'text',
          start_date: 'date',
          end_date: 'date',
          goal_value: 'number',
          current_value: 'number',
          goal_unit: 'text',
          before_photo: 'text',
          after_photo: 'text'
        };
        break;

      case 'group_join_requests':
        fieldTypes = {
          id: 'text',
          group_id: 'text',
          username_invited: 'text',
          invited_by: 'text',
          created: 'date'
        };
        break;

      case 'group_members':
        fieldTypes = {
          id: 'text',
          group_id: 'text',
          user_id: 'text',
          nickname: 'text',
          is_admin: 'boolean',
          joined_date: 'date'
        };
        break;

      case 'group_quest_progress':
        fieldTypes = {
          id: 'text',
          quest_id: 'text',
          user_id: 'text',
          date: 'date',
          value_achieved: 'number',
          completed: 'boolean'
        };
        break;

      case 'group_quests':
        fieldTypes = {
          id: 'text',
          group_id: 'text',
          created: 'date',
          name: 'text',
          description: 'textarea',
          emoji: 'text',
          category: 'select',
          priority: 'select',
          quest_type: 'select',
          goal_value: 'number',
          goal_unit: 'select',
          goal_period: 'select',
          task_days_of_week: 'text',
          task_days_of_month: 'text',
          streak: 'number'
        };
        break;

      case 'groups':
        fieldTypes = {
          id: 'text',
          created: 'date',
          name: 'text',
          emoji: 'text',
          admin_id: 'text',
          enable_sidequests: 'boolean',
          timezone: 'text',
          level: 'number',
          strength_xp: 'number',
          money_xp: 'number',
          health_xp: 'number',
          knowledge_xp: 'number',
          invitation_code: 'text',
          code_expiry: 'date'
        };
        break;

      case 'microgoals':
        fieldTypes = {
          id: 'text',
          goal_id: 'text',
          title: 'text',
          completed: 'boolean',
          completed_at: 'date'
        };
        break;

      case 'subscription_history':
        fieldTypes = {
          id: 'text',
          user_id: 'text',
          created_at: 'date',
          start_date_of_subscription: 'date',
          end_date_of_subscription: 'date',
          plan: 'select'
        };
        break;

      case 'user_badges':
        fieldTypes = {
          id: 'text',
          user_id: 'text',
          created_at: 'date',
          updated_at: 'date',
          badge_newbie: 'boolean',
          badge_warrior: 'boolean',
          badge_hardcore: 'boolean',
          badge_peak_performer: 'boolean',
          badge_indestructible: 'boolean',
          badge_professional: 'boolean',
          badge_streak_7_days: 'boolean',
          badge_streak_30_days: 'boolean',
          badge_streak_100_days: 'boolean',
          badge_streak_365_days: 'boolean',
          badge_sidequest_streak_7_days: 'boolean',
          badge_sidequest_streak_30_days: 'boolean',
          badge_sidequest_streak_100_days: 'boolean',
          badge_sidequest_streak_365_days: 'boolean',
          badge_friends_5: 'boolean',
          badge_friends_10: 'boolean',
          badge_strength_master: 'boolean',
          badge_money_master: 'boolean',
          badge_health_master: 'boolean',
          badge_knowledge_master: 'boolean'
        };
        break;

      default:
        return {};
    }

    for (const fieldName in fieldTypes) {
      if (this.isForeignKey(fieldName)) {
        fieldTypes[fieldName] = 'foreign-key';
      }
    }

    return fieldTypes;
  }

  getSelectOptions(collectionName: string, fieldName: string): string[] {

    const categories = ['money', 'health', 'strength', 'knowledge'];
    const goalUnits = ['count', 'steps', 'm', 'km', 'sec', 'min', 'hr', 'Cal', 'g', 'mg', 'drink', 'time', 'pages', 'books', '%', '€', '$', '£'];
    const goalPeriods = ['day', 'week', 'month'];
    const priorities = ['basic', 'high'];
    const questTypes = ['build', 'quit'];

    if (collectionName === 'profiles' || collectionName === 'subscription_history') {
      if (fieldName === 'subscription_status') {
        return ['email marketing', 'active', 'cancelled', 'paused', 'expired'];
      }
      if (fieldName === 'plan') {
        return ['monthly', 'yearly'];
      }
    }

    if (collectionName === 'quests') {
      if (fieldName === 'quest_type') {
        return questTypes;
      }
      if (fieldName === 'goal_unit') {
        return goalUnits;
      }
      if (fieldName === 'goal_period') {
        return goalPeriods;
      }
      if (fieldName === 'priority') {
        return priorities;
      }
      if (fieldName === 'category') {
        return categories;
      }
    }

    if (collectionName === 'daily_sidequest_pool') {
      if (fieldName === 'category') {
        return categories;
      }
      if (fieldName === 'goal_unit') {
        return goalUnits;
      }
    }

    if (collectionName === 'user_daily_sidequests') {
      if (fieldName === 'category') {
        return categories;
      }
    }

    if (collectionName === 'group_quests') {
      if (fieldName === 'category') {
        return categories;
      }
      if (fieldName === 'priority') {
        return priorities;
      }
      if (fieldName === 'quest_type') {
        return questTypes;
      }
      if (fieldName === 'goal_unit') {
        return goalUnits;
      }
      if (fieldName === 'goal_period') {
        return goalPeriods;
      }
    }

    if (collectionName === 'subscription_history') {
      if (fieldName === 'plan') {
        return ['basic', 'premium', 'pro'];
      }
    }

    return [];
  }

  createTestAdminUser(): Observable<string> {

    const adminUser = {
      id: 'admin-test-id', 
      email: '<EMAIL>',
      username: 'admin',
      registration_date: new Date(),
      last_login: new Date(),
      active: true,
      level: 10,
      title: '🔥 Warrior',
      strength_xp: 100,
      money_xp: 100,
      health_xp: 100,
      knowledge_xp: 100,
      bio: 'Admin user',
      affiliate_code_used: '',
      timezone: 'UTC',
      auto_renew: true,
      sidequests_switch: true,
      show_celebration: true,
      celebration_name: 'Another Day, Another W',
      celebration_description: "You've completed all your quests for today. Keep up the great work!",
      celebration_emoji: '🦅',
      subscription_status: 'active',
      end_of_current_plan: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), 
      plan: 'basic'
    };

    try {
      return from(this.supabaseService.supabase
        .from('profiles')
        .upsert(adminUser)
      ).pipe(
        map(response => {
          if (response.error) {
            throw response.error;
          }

          return adminUser.id;
        }),
        catchError(error => {
          throw error;
        })
      );
    } catch (error) {
      return of('');
    }
  }

  createTestQuests(userId: string): Observable<string[]> {

    const testQuests = [
      {
        user_id: userId,
        name: 'Morning Workout',
        description: 'Do a quick morning workout to start the day',
        emoji: '💪',
        category: 'health',
        quest_type: 'build',
        goal_value: 30,
        goal_unit: 'min',
        goal_period: 'day',
        priority: 'high',
        active: true,
        created_at: new Date(),
        streak: 3
      },
      {
        user_id: userId,
        name: 'Read a Book',
        description: 'Read at least 20 pages of a book',
        emoji: '📚',
        category: 'knowledge',
        quest_type: 'build',
        goal_value: 20,
        goal_unit: 'pages',
        goal_period: 'day',
        priority: 'basic',
        active: true,
        created_at: new Date(),
        streak: 5
      },
      {
        user_id: userId,
        name: 'Save Money',
        description: 'Put aside some money for savings',
        emoji: '💰',
        category: 'money',
        quest_type: 'build',
        goal_value: 10,
        goal_unit: '$',
        goal_period: 'day',
        priority: 'basic',
        active: true,
        created_at: new Date(),
        streak: 2
      }
    ];

    try {
      return from(this.supabaseService.supabase
        .from('quests')
        .insert(testQuests)
        .select('id')
      ).pipe(
        map(response => {
          if (response.error) {
            throw response.error;
          }

          const questIds = response.data.map(quest => quest.id);
          return questIds;
        }),
        catchError(error => {
          throw error;
        })
      );
    } catch (error) {
      return of([]);
    }
  }

  createTestQuestProgress(userId: string, questIds: string[]): Observable<string[]> {

    if (!questIds || questIds.length === 0) {
      return of([]);
    }

    try {
      const today = new Date();
      const progressEntries: any[] = [];

      for (let i = 0; i < questIds.length; i++) {
        const progressData = {
          user_id: userId,
          quest_id: questIds[i],
          date: today,
          completed: i === 0, 
          value_achieved: i === 0 ? 30 : (i === 1 ? 10 : 5) 
        };

        progressEntries.push(progressData);
      }

      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      for (const questId of questIds) {
        const progressData = {
          user_id: userId,
          quest_id: questId,
          date: yesterday,
          completed: true,
          value_achieved: 30
        };

        progressEntries.push(progressData);
      }

      return from(this.supabaseService.supabase
        .from('quest_progress')
        .insert(progressEntries)
        .select('id')
      ).pipe(
        map(response => {
          if (response.error) {
            throw response.error;
          }

          const progressIds = response.data.map(progress => progress.id);
          return progressIds;
        }),
        catchError(error => {
          throw error;
        })
      );
    } catch (error) {
      return of([]);
    }
  }

  initializeTestData(): Observable<boolean> {

    const adminId = 'admin-test-id';

    return this.createTestAdminUser().pipe(
      switchMap(userId => {
        return this.createTestQuests(userId);
      }),
      switchMap(questIds => {
        return this.createTestQuestProgress(adminId, questIds);
      }),
      map(() => {
        return true;
      }),
      catchError(error => {
        return of(false);
      })
    );
  }
}




