{"ast": null, "code": "import { enableProdMode, importProvidersFrom } from '@angular/core';\nimport { bootstrapApplication } from '@angular/platform-browser';\nimport { provideHttpClient } from '@angular/common/http';\nimport { AppComponent } from './app/app.component';\nimport { provideRouter } from '@angular/router';\nimport { routes } from './app/app-routing.module';\nimport { provideIonicAngular } from '@ionic/angular/standalone';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { environment } from './environments/environment';\nimport { QuestService } from './app/services/quest.service';\nimport { SideQuestService } from './app/services/sidequest.service';\nimport { GoalService } from './app/services/goal.service';\nimport { FriendService } from './app/services/friend.service';\nimport { GroupService } from './app/services/group.service';\nimport { ActivityService } from './app/services/activity.service';\nimport { BadgeService } from './app/services/badge.service';\nimport { UserService } from './app/services/user.service';\nimport { AdminService } from './app/services/admin.service';\nimport { PreferencesService } from './app/services/preferences.service';\nimport { createAnimation } from '@ionic/angular/standalone';\nexport const fadeAnimation = (baseEl, opts) => {\n  const enteringEl = opts.enteringEl;\n  const leavingEl = opts.leavingEl;\n  const enteringAnimation = createAnimation().addElement(enteringEl).duration(300).easing('ease-in-out').fromTo('opacity', 0, 1);\n  const leavingAnimation = createAnimation().addElement(leavingEl).duration(300).easing('ease-in-out').fromTo('opacity', 1, 0);\n  return createAnimation().addAnimation(enteringAnimation).addAnimation(leavingAnimation);\n};\nif (environment.production) {\n  enableProdMode();\n}\nbootstrapApplication(AppComponent, {\n  providers: [provideRouter(routes), provideIonicAngular({\n    animated: true,\n    navAnimation: fadeAnimation\n  }), provideHttpClient(), importProvidersFrom(ReactiveFormsModule), QuestService, SideQuestService, GoalService, FriendService, GroupService, ActivityService, BadgeService, UserService, AdminService, PreferencesService]\n});", "map": {"version": 3, "names": ["enableProdMode", "importProvidersFrom", "bootstrapApplication", "provideHttpClient", "AppComponent", "provideRouter", "routes", "provideIonicAngular", "ReactiveFormsModule", "environment", "QuestService", "SideQuestService", "GoalService", "FriendService", "GroupService", "ActivityService", "BadgeService", "UserService", "AdminService", "PreferencesService", "createAnimation", "fadeAnimation", "baseEl", "opts", "enteringEl", "leavingEl", "enteringAnimation", "addElement", "duration", "easing", "fromTo", "leavingAnimation", "addAnimation", "production", "providers", "animated", "navAnimation"], "sources": ["C:\\Users\\<USER>\\work-things\\vlastne\\upshift_project\\upshift\\src\\main.ts"], "sourcesContent": ["import { enableProdMode, importProvidersFrom } from '@angular/core';\r\nimport { bootstrapApplication } from '@angular/platform-browser';\r\nimport { provideHttpClient } from '@angular/common/http';\r\n\r\nimport { AppComponent } from './app/app.component';\r\nimport { provideRouter } from '@angular/router';\r\nimport { routes } from './app/app-routing.module';\r\n\r\nimport { provideIonicAngular } from '@ionic/angular/standalone';\r\n\r\nimport { ReactiveFormsModule } from '@angular/forms';\r\n\r\nimport { environment } from './environments/environment';\r\n\r\nimport { QuestService } from './app/services/quest.service';\r\nimport { SideQuestService } from './app/services/sidequest.service';\r\nimport { GoalService } from './app/services/goal.service';\r\nimport { FriendService } from './app/services/friend.service';\r\nimport { GroupService } from './app/services/group.service';\r\nimport { ActivityService } from './app/services/activity.service';\r\nimport { BadgeService } from './app/services/badge.service';\r\nimport { UserService } from './app/services/user.service';\r\nimport { AdminService } from './app/services/admin.service';\r\nimport { PreferencesService } from './app/services/preferences.service';\r\nimport { Animation, createAnimation } from '@ionic/angular/standalone';\r\n\r\nexport const fadeAnimation = (baseEl: HTMLElement, opts?: any): Animation => {\r\n  const enteringEl = opts.enteringEl;\r\n  const leavingEl = opts.leavingEl;\r\n\r\n  const enteringAnimation = createAnimation()\r\n    .addElement(enteringEl)\r\n    .duration(300)\r\n    .easing('ease-in-out')\r\n    .fromTo('opacity', 0, 1);\r\n\r\n  const leavingAnimation = createAnimation()\r\n    .addElement(leavingEl)\r\n    .duration(300)\r\n    .easing('ease-in-out')\r\n    .fromTo('opacity', 1, 0);\r\n\r\n  return createAnimation()\r\n    .addAnimation(enteringAnimation)\r\n    .addAnimation(leavingAnimation);\r\n};\r\nif (environment.production) {\r\n  enableProdMode();\r\n}\r\n\r\nbootstrapApplication(AppComponent, {\r\n  providers: [\r\n    provideRouter(routes),\r\n    provideIonicAngular({\r\n      animated: true,\r\n      navAnimation: fadeAnimation\r\n    }),\r\n    provideHttpClient(),\r\n\r\n\r\n    importProvidersFrom(ReactiveFormsModule),\r\n\r\n    QuestService,\r\n    SideQuestService,\r\n    GoalService,\r\n    FriendService,\r\n    GroupService,\r\n    ActivityService,\r\n    BadgeService,\r\n    UserService,\r\n    AdminService,\r\n    PreferencesService\r\n  ]\r\n});\r\n"], "mappings": "AAAA,SAASA,cAAc,EAAEC,mBAAmB,QAAQ,eAAe;AACnE,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,iBAAiB,QAAQ,sBAAsB;AAExD,SAASC,YAAY,QAAQ,qBAAqB;AAClD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,MAAM,QAAQ,0BAA0B;AAEjD,SAASC,mBAAmB,QAAQ,2BAA2B;AAE/D,SAASC,mBAAmB,QAAQ,gBAAgB;AAEpD,SAASC,WAAW,QAAQ,4BAA4B;AAExD,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,gBAAgB,QAAQ,kCAAkC;AACnE,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,aAAa,QAAQ,+BAA+B;AAC7D,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,kBAAkB,QAAQ,oCAAoC;AACvE,SAAoBC,eAAe,QAAQ,2BAA2B;AAEtE,OAAO,MAAMC,aAAa,GAAGA,CAACC,MAAmB,EAAEC,IAAU,KAAe;EAC1E,MAAMC,UAAU,GAAGD,IAAI,CAACC,UAAU;EAClC,MAAMC,SAAS,GAAGF,IAAI,CAACE,SAAS;EAEhC,MAAMC,iBAAiB,GAAGN,eAAe,EAAE,CACxCO,UAAU,CAACH,UAAU,CAAC,CACtBI,QAAQ,CAAC,GAAG,CAAC,CACbC,MAAM,CAAC,aAAa,CAAC,CACrBC,MAAM,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;EAE1B,MAAMC,gBAAgB,GAAGX,eAAe,EAAE,CACvCO,UAAU,CAACF,SAAS,CAAC,CACrBG,QAAQ,CAAC,GAAG,CAAC,CACbC,MAAM,CAAC,aAAa,CAAC,CACrBC,MAAM,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;EAE1B,OAAOV,eAAe,EAAE,CACrBY,YAAY,CAACN,iBAAiB,CAAC,CAC/BM,YAAY,CAACD,gBAAgB,CAAC;AACnC,CAAC;AACD,IAAItB,WAAW,CAACwB,UAAU,EAAE;EAC1BjC,cAAc,EAAE;AAClB;AAEAE,oBAAoB,CAACE,YAAY,EAAE;EACjC8B,SAAS,EAAE,CACT7B,aAAa,CAACC,MAAM,CAAC,EACrBC,mBAAmB,CAAC;IAClB4B,QAAQ,EAAE,IAAI;IACdC,YAAY,EAAEf;GACf,CAAC,EACFlB,iBAAiB,EAAE,EAGnBF,mBAAmB,CAACO,mBAAmB,CAAC,EAExCE,YAAY,EACZC,gBAAgB,EAChBC,WAAW,EACXC,aAAa,EACbC,YAAY,EACZC,eAAe,EACfC,YAAY,EACZC,WAAW,EACXC,YAAY,EACZC,kBAAkB;CAErB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}