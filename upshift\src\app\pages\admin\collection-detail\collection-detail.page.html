﻿<div class="container">
  <header>
    <div class="logo">
      <img src="assets/images/upshift_icon_mini.svg" alt="Upshift">
      <span>Upshift</span>
    </div>
    <div class="header-content">
      <button class="back-button" (click)="goBack()">← Back</button>
      <h1>{{ collectionName }}</h1>
    </div>
  </header>

  <div class="admin-content">
    <div class="collection-actions">
      <button class="add-button" (click)="toggleAddForm()">
        {{ showAddForm ? 'Cancel' : 'Add Document' }}
      </button>
    </div>

    <!-- Add Document Form -->
    <div *ngIf="showAddForm" class="document-form">
      <h2>Add New Document</h2>

      <div class="form-actions">
        <button class="json-button" (click)="openJsonEditor()">Edit as JSON</button>
        <button class="save-button" (click)="addDocument()">Save Document</button>
      </div>

      <div class="form-fields">
        <div class="form-field" *ngFor="let key of getObjectKeys(newDocument)">
          <label>{{ key }}</label>

          <!-- Different input types based on field type -->
          <ng-container [ngSwitch]="getFieldType(key)">
            <!-- Text input -->
            <input *ngSwitchCase="'text'" type="text" [(ngModel)]="newDocument[key]">

            <!-- Email input -->
            <input *ngSwitchCase="'email'" type="email" [(ngModel)]="newDocument[key]">

            <!-- Password input -->
            <input *ngSwitchCase="'password'" type="password" [(ngModel)]="newDocument[key]">

            <!-- Number input -->
            <input *ngSwitchCase="'number'" type="number" [(ngModel)]="newDocument[key]">

            <!-- Date input -->
            <input *ngSwitchCase="'date'" type="date" [ngModel]="formatDateForInput(newDocument[key])"
                  (ngModelChange)="handleDateChange(newDocument, key, $event)">

            <!-- Textarea -->
            <textarea *ngSwitchCase="'textarea'" rows="3" [(ngModel)]="newDocument[key]"></textarea>

            <!-- Boolean/Checkbox -->
            <div *ngSwitchCase="'boolean'" class="checkbox-field">
              <input type="checkbox" [(ngModel)]="newDocument[key]" [id]="'new-' + key">
              <label [for]="'new-' + key">{{ newDocument[key] ? 'Yes' : 'No' }}</label>
            </div>

            <!-- Select/Dropdown -->
            <select *ngSwitchCase="'select'" [(ngModel)]="newDocument[key]">
              <option value="">-- Select --</option>
              <option *ngFor="let option of getSelectOptions(key)" [value]="option">{{ option }}</option>
            </select>

            <!-- Foreign Key Dropdown -->
            <select *ngSwitchCase="'foreign-key'" [(ngModel)]="newDocument[key]">
              <option value="">-- Select --</option>
              <option *ngFor="let option of getForeignKeyOptions(key)" [value]="option.id">
                {{ option.display }}
              </option>
            </select>

            <!-- Color picker -->
            <input *ngSwitchCase="'color'" type="color" [(ngModel)]="newDocument[key]">

            <!-- File input for images -->
            <div *ngSwitchCase="'file'" class="file-input-container">
              <input type="file" (change)="handleFileUpload($event, newDocument, key)" accept="image/*">
              <div *ngIf="newDocument[key]" class="file-preview">
                <img [src]="newDocument[key]" alt="Preview">
              </div>
            </div>

            <!-- Default text input -->
            <input *ngSwitchDefault type="text" [(ngModel)]="newDocument[key]">
          </ng-container>
        </div>

        <div class="add-field">
          <input type="text" #newFieldName placeholder="Field name">
          <button (click)="newDocument[newFieldName.value] = ''; newFieldName.value = ''">Add Field</button>
        </div>
      </div>
    </div>

    <!-- JSON Editor Modal -->
    <div *ngIf="showJsonEditor" class="json-editor-modal">
      <div class="json-editor-content">
        <h2>Edit as JSON</h2>
        <div class="json-error" *ngIf="jsonError">{{ jsonError }}</div>
        <textarea [(ngModel)]="jsonEditorContent" rows="20"></textarea>
        <div class="json-actions">
          <button class="cancel-button" (click)="cancelJsonEdit()">Cancel</button>
          <button class="apply-button" (click)="applyJsonChanges()">Apply Changes</button>
        </div>
      </div>
    </div>

    <!-- Documents List -->
    <div class="documents-list">
      <div *ngIf="isLoading" class="loading">
        <ion-spinner></ion-spinner>
        <p>Loading documents...</p>
      </div>

      <div *ngIf="!isLoading && documents.length === 0" class="no-data">
        <p>No documents found in this collection.</p>
      </div>

      <div *ngIf="!isLoading && documents.length > 0" class="documents-table">
        <div class="document-item" *ngFor="let document of documents">
          <!-- View Mode -->
          <div *ngIf="editingDocument?.id !== document.id" class="document-view">
            <div class="document-header">
              <h3>ID: {{ document.id }}</h3>
              <div class="document-actions">
                <button class="edit-button" (click)="editDocument(document)">Edit</button>
                <button class="delete-button" (click)="deleteDocument(document.id)">Delete</button>
              </div>
            </div>

            <div class="document-fields">
              <div class="document-field" *ngFor="let key of getObjectKeys(document)" [hidden]="key === 'id'">
                <span class="field-name">{{ key }}:</span>
                <span class="field-value" [innerHTML]="formatDocumentFieldValue(document, key)"></span>
              </div>
            </div>
          </div>

          <!-- Edit Mode -->
          <div *ngIf="editingDocument?.id === document.id" class="document-edit">
            <h3>Editing Document</h3>

            <div class="form-actions">
              <button class="json-button" (click)="openJsonEditor(document)">Edit as JSON</button>
              <button class="cancel-button" (click)="cancelEdit()">Cancel</button>
              <button class="save-button" (click)="updateDocument()">Save Changes</button>
            </div>

            <div class="form-fields">
              <div class="form-field" *ngFor="let key of getObjectKeys(editingDocument)" [hidden]="key === 'id'">
                <label>{{ key }}</label>

                <!-- Different input types based on field type -->
                <ng-container [ngSwitch]="getFieldType(key)">
                  <!-- Text input -->
                  <input *ngSwitchCase="'text'" type="text" [(ngModel)]="editingDocument[key]">

                  <!-- Email input -->
                  <input *ngSwitchCase="'email'" type="email" [(ngModel)]="editingDocument[key]">

                  <!-- Password input -->
                  <input *ngSwitchCase="'password'" type="password" [(ngModel)]="editingDocument[key]">

                  <!-- Number input -->
                  <input *ngSwitchCase="'number'" type="number" [(ngModel)]="editingDocument[key]">

                  <!-- Date input -->
                  <input *ngSwitchCase="'date'" type="date" [ngModel]="formatDateForInput(editingDocument[key])"
                        (ngModelChange)="handleDateChange(editingDocument, key, $event)">

                  <!-- Textarea -->
                  <textarea *ngSwitchCase="'textarea'" rows="3" [(ngModel)]="editingDocument[key]"></textarea>

                  <!-- Boolean/Checkbox -->
                  <div *ngSwitchCase="'boolean'" class="checkbox-field">
                    <input type="checkbox" [(ngModel)]="editingDocument[key]" [id]="'edit-' + key">
                    <label [for]="'edit-' + key">{{ editingDocument[key] ? 'Yes' : 'No' }}</label>
                  </div>

                  <!-- Select/Dropdown -->
                  <select *ngSwitchCase="'select'" [(ngModel)]="editingDocument[key]">
                    <option value="">-- Select --</option>
                    <option *ngFor="let option of getSelectOptions(key)" [value]="option">{{ option }}</option>
                  </select>

                  <!-- Foreign Key Dropdown -->
                  <select *ngSwitchCase="'foreign-key'" [(ngModel)]="editingDocument[key]">
                    <option value="">-- Select --</option>
                    <option *ngFor="let option of getForeignKeyOptions(key)" [value]="option.id">
                      {{ option.display }}
                    </option>
                  </select>

                  <!-- Color picker -->
                  <input *ngSwitchCase="'color'" type="color" [(ngModel)]="editingDocument[key]">

                  <!-- File input for images -->
                  <div *ngSwitchCase="'file'" class="file-input-container">
                    <input type="file" (change)="handleFileUpload($event, editingDocument, key)" accept="image/*">
                    <div *ngIf="editingDocument[key]" class="file-preview">
                      <img [src]="editingDocument[key]" alt="Preview">
                    </div>
                  </div>

                  <!-- Default text input -->
                  <input *ngSwitchDefault type="text" [(ngModel)]="editingDocument[key]">
                </ng-container>
              </div>

              <div class="add-field">
                <input type="text" #newEditFieldName placeholder="Field name">
                <button (click)="editingDocument[newEditFieldName.value] = ''; newEditFieldName.value = ''">Add Field</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
