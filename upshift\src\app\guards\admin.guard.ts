﻿import { Injectable, inject } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { Observable, map, switchMap, of } from 'rxjs';
import { SupabaseService } from '../services/supabase.service';
import { UserService } from '../services/user.service';

@Injectable({
  providedIn: 'root'
})
export class AdminGuard implements CanActivate {
  private supabaseService = inject(SupabaseService);
  private userService = inject(UserService);
  private router = inject(Router);

  canActivate(): Observable<boolean> {

    return this.supabaseService.currentUser$.pipe(
      switchMap(authUser => {
        if (!authUser) {
          this.router.navigate(['/register']);
          return of(false);
        }


        return this.userService.getUserById(authUser.id).pipe(
          map(userData => {
            if (!userData) {
              this.router.navigate(['/register']);
              return false;
            }

            const isAdmin = userData.username === 'admin';

            if (!isAdmin) {
              this.router.navigate(['/today']);
            } else {
            }

            return isAdmin;
          })
        );
      })
    );
  }
}


