﻿:host {
  --bg: #0a0b0f;               Main background - darker, more neutral */
  --surface-alt: #1e1f25;      Slight variation for subtle contrast */
  --accent-hover: #5277e8;     Lighter accent for hover/focus */
  --text: #ffffff;             High contrast text */
  --text-muted: #6b6d7c;       Hints, timestamps, soft elements */
  --success: #32d690;          Completed or positive status */
  --error: #ef4444;            Incomplete/failed task indicators */
}

.header-section {
  margin-bottom: var(--section-spacing);
  padding: 24px;
  background-color: var(--surface);
  border-radius: var(--card-border-radius);
  box-shadow: var(--card-shadow);
  margin: 16px;

  .header-content {
    text-align: center;
    margin-bottom: 24px;

    h1 {
      margin-bottom: 8px;
      font-size: 28px;
      font-weight: bold;
      color: var(--text);
      background: linear-gradient(90deg, var(--accent), #6A5ACD);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
    }

    .subtitle {
      margin-top: 0;
      color: var(--text-secondary);
      font-size: 16px;
    }
  }

  .affiliate-counter {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;

    .counter-value {
      font-size: 48px;
      font-weight: bold;
      color: var(--accent);
      text-shadow: 0 0 15px rgba(65, 105, 225, 0.3);
      margin-bottom: 4px;
    }

    .counter-label {
      font-size: 16px;
      color: var(--text-secondary);
      text-transform: uppercase;
      letter-spacing: 1px;
    }
  }

  .header-description {
    text-align: center;
    color: var(--text-secondary);
    font-size: 14px;
    line-height: 1.5;
    max-width: 600px;
    margin: 0 auto;
    padding: 0 16px;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;

  p {
    margin-top: 10px;
    color: var(--text-secondary);
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: var(--text-secondary);
  background-color: var(--card-bg);
  border-radius: var(--card-border-radius);
  padding: 30px;

  ion-icon {
    font-size: 64px;
    margin-bottom: 20px;
    color: var(--accent-color);
    opacity: 0.5;
  }

  p {
    font-size: 18px;
    margin-bottom: 8px;
    text-align: center;
  }

  .empty-subtitle {
    font-size: 14px;
    opacity: 0.7;
  }
}

.rewards-section {
  margin-bottom: var(--section-spacing);
  padding: 0 16px;

  .section-header {
    margin-bottom: 24px;
    padding: 0 8px;

    h2 {
      font-size: 22px;
      font-weight: bold;
      color: var(--text);
      margin-bottom: 8px;
    }

    .section-subtitle {
      color: var(--text-secondary);
      font-size: 14px;
    }
  }

  .rewards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;

    @media (max-width: 576px) {
      grid-template-columns: 1fr;
    }
  }

  .reward-card {
    position: relative;
    --background: var(--surface);
    border-radius: var(--card-border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    transition: transform 0.2s, box-shadow 0.2s;
    margin: 0;
    cursor: pointer;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    }

    &.claimed {
      border: 2px solid var(--success);

      .reward-status {
        background-color: var(--success);
      }
    }

    &.available:not(.claimed) {
      border: 2px solid var(--accent);

      .reward-status.available {
        background-color: var(--accent);
      }
    }

    .reward-status {
      position: absolute;
      top: 12px;
      right: 12px;
      display: flex;
      align-items: center;
      padding: 6px 10px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: bold;
      color: white;
      z-index: 1;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

      ion-icon {
        margin-right: 6px;
        font-size: 14px;
      }
    }

    .reward-image {
      height: 160px;
      width: 100%;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      &.placeholder {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(65, 105, 225, 0.1);

        ion-icon {
          font-size: 64px;
          color: var(--accent);
          opacity: 0.5;
        }
      }
    }

    ion-card-content {
      padding: 16px;

      .reward-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 8px;
        color: var(--text);
      }

      .reward-description {
        color: var(--text-secondary);
        font-size: 14px;
        margin-bottom: 16px;
        display: -webkit-box;
        display: box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        box-orient: vertical;
        overflow: hidden;
        line-height: 1.4;
        height: 40px;
      }

      .reward-details {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;

        .reward-value, .reward-requirement {
          display: flex;
          align-items: center;
          font-size: 14px;
          color: var(--text-secondary);

          ion-icon {
            margin-right: 6px;
            font-size: 16px;
          }
        }

        .reward-requirement.met {
          color: var(--success);
        }
      }

      .reward-progress {
        margin-bottom: 16px;

        .progress-bar {
          height: 6px;
          background-color: var(--progress-bg);
          border-radius: 3px;
          overflow: hidden;

          .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--accent), #6A5ACD);
            border-radius: 3px;
            transition: width 0.3s ease;

            &.complete {
              background: linear-gradient(90deg, var(--success), #32d690);
            }
          }
        }
      }
    }

    .reward-action {
      padding-top: 8px;

      ion-button {
        --color: white;
        font-weight: 500;
        margin: 0;

        &::part(native) {
          padding: 0;
        }
      }
    }
  }
}

.affiliate-offer-modal {
  --background: var(--bg);
  --width: 480px;
  --height: 80%;
  --border-radius: 0;

  ion-header {
    --background: var(--bg);

    ion-toolbar {
      --background: var(--bg);
      --color: var(--text);
    }
  }

  ion-content {
    --background: var(--bg);
  }
}

.modal-content {
  display: flex;
  flex-direction: column;
  padding: 0;

  .reward-detail-header {
    margin-bottom: 24px;

    .reward-detail-image {
      width: 100%;
      height: 240px;
      overflow: hidden;
      margin-bottom: 20px;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      &.placeholder {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(65, 105, 225, 0.1);

        ion-icon {
          font-size: 80px;
          color: var(--accent-color);
          opacity: 0.5;
        }
      }
    }

    .reward-detail-title {
      padding: 0 16px;

      h1 {
        font-size: 28px;
        font-weight: bold;
        margin-bottom: 12px;
        color: var(--text-primary);
      }

      .reward-detail-value {
        display: flex;
        align-items: center;
        font-size: 18px;
        font-weight: 600;
        color: var(--accent-color);

        ion-icon {
          margin-right: 8px;
          font-size: 20px;
        }
      }
    }
  }

  .reward-detail-section {
    padding: 0 16px;
    margin-bottom: 24px;

    h3 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 12px;
      color: var(--text-primary);
    }

    p {
      color: var(--text-secondary);
      line-height: 1.6;
      font-size: 16px;
    }

    .progress-container {
      margin-top: 12px;

      .progress-bar {
        height: 8px;
        background-color: var(--progress-bg);
        border-radius: 4px;
        overflow: hidden;
        margin-bottom: 8px;

        .progress-fill {
          height: 100%;
          background-color: var(--progress-fill);
          border-radius: 4px;
          transition: width 0.3s ease;

          &.complete {
            background-color: var(--progress-fill-complete);
          }
        }
      }

      .progress-text {
        font-size: 14px;
        color: var(--text-secondary);

        &.complete {
          color: var(--success-color);
          font-weight: 500;
        }
      }
    }

    .requirement-message {
      display: flex;
      align-items: center;
      margin-top: 12px;
      padding: 12px;
      background-color: rgba(255, 214, 10, 0.1);
      border-radius: 8px;

      ion-icon {
        margin-right: 8px;
        font-size: 20px;
        color: var(--warning-color);
      }

      span {
        font-size: 14px;
        color: var(--text-secondary);
      }
    }

    .claimed-badge {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 16px;
      background-color: rgba(48, 209, 88, 0.1);
      border-radius: 8px;

      ion-icon {
        margin-right: 8px;
        font-size: 24px;
        color: var(--success-color);
      }

      span {
        font-size: 16px;
        font-weight: 600;
        color: var(--success-color);
      }
    }
  }

  .reward-detail-action {
    padding: 0 16px 16px;
    margin-top: auto;

    ion-button {
      --border-radius: 12px;
      --padding-top: 16px;
      --padding-bottom: 16px;
      font-weight: 600;
      font-size: 16px;
    }
  }
}

ion-header {
  ion-toolbar {
    --background: var(--bg);
    --border-color: transparent;

    .logo {
      display: flex;
      align-items: center;
      gap: 8px;
      padding-left: 16px;

      img {
        height: 24px;
      }

      span {
        font-size: 20px;
        font-weight: 600;
        color: var(--text);
      }
    }

    ion-title {
      font-size: 20px;
      font-weight: 600;
      color: var(--text);
      text-align: end;
      padding-right: 16px;
    }
  }
}

@media screen and (max-width: 480px) {
  .page-container {
    width: auto;
  }
  .affiliate-offer-modal {
  --width: 90%;
  
}
}